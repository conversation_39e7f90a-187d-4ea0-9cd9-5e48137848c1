# generic packages and tmux
before_install:
  - sudo apt-get update
  - sudo apt-get install -y git-core expect
  - sudo apt-get install -y python-software-properties software-properties-common
  - sudo apt-get install -y libevent-dev libncurses-dev
  - git clone https://github.com/tmux/tmux.git
  - cd tmux
  - git checkout 2.5
  - sh autogen.sh
  - ./configure && make && sudo make install

install:
  - git fetch --unshallow --recurse-submodules || git fetch --recurse-submodules
  # manual `git clone` required for testing `tmux-test` plugin itself
  - git clone https://github.com/tmux-plugins/tmux-test lib/tmux-test; true
  - lib/tmux-test/setup

script: ./tests/run_tests_in_isolation
