#!/usr/bin/env bash

# Tmux key-binding script.
# Scripts intended to be used via the command line are in `bin/` directory.

CURRENT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
SCRIPTS_DIR="$CURRENT_DIR/../scripts"
HELPERS_DIR="$SCRIPTS_DIR/helpers"

source "$HELPERS_DIR/tmux_echo_functions.sh"
source "$HELPERS_DIR/tmux_utils.sh"

main() {
	reload_tmux_environment
	"$SCRIPTS_DIR/clean_plugins.sh" --tmux-echo >/dev/null 2>&1
	reload_tmux_environment
	end_message
}
main
