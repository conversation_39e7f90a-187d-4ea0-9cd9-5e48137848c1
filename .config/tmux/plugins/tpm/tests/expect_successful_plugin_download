#!/usr/bin/env expect

# disables script output
log_user 0

spawn tmux

# Waiting for tmux to attach. If this is not done, next command, `send` will
# not work properly.
sleep 1

# this is tmux prefix + I
send "I"

# cloning might take a while
set timeout 15

expect_after {
  timeout { exit 1 }
}

expect {
  "Installing \"tmux-example-plugin\""
}

expect {
  "\"tmux-example-plugin\" download success"
}

expect {
  "Done, press ENTER to continue" {
    send "
"
  }
}

sleep 1
# this is tmux prefix + I
send "I"

expect {
  "Already installed \"tmux-example-plugin\""
}

expect {
  "Done, press ENTER to continue" {
    exit 0
  }
}

exit 1
