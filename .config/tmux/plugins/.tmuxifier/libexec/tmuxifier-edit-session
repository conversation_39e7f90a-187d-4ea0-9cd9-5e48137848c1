#! /usr/bin/env bash
set -e
[ -n "$TMUXIFIER_DEBUG" ] && set -x

# Load internal utility functions.
source "$TMUXIFIER/lib/util.sh"

# Provide tmuxifier help
if calling-help "$@"; then
  echo "usage: tmuxifier edit-session <layout_name>

Aliases: edit-ses, eses, es

Open specified session layout for editing in \$EDITOR."
  exit
fi

# Provide tmuxifier completions
if calling-complete "$@"; then
  tmuxifier-list-sessions
  exit
fi

if [ -z "$1" ]; then
  echo "$(tmuxifier-help edit-session $@)" >&2
  exit 1
fi

layout_name="$1"
layout_file="$TMUXIFIER_LAYOUT_PATH/${layout_name}.session.sh"

if [ ! -f "$layout_file" ]; then
  echo "tmuxifier: session layout '$layout_name' does not exist." >&2
  echo "" >&2
  echo "You can create it with:" >&2
  echo "  tmuxifier new-session '$layout_name'" >&2
  exit 1
fi

if [ -n "$EDITOR" ]; then
  exec $EDITOR "$layout_file"
else
  echo "'\$EDITOR' is not set. Please manually open the layout for editing:"
  echo "$layout_file"
  echo
fi
