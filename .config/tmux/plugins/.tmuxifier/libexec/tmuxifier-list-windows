#! /usr/bin/env bash
set -e
[ -n "$TMUXIFIER_DEBUG" ] && set -x

# Load internal utility functions.
source "$TMUXIFIER/lib/util.sh"

# Provide tmuxifier help
if calling-help "$@"; then
  echo "usage: tmuxifier list-windows

Aliases: list-win, lwin, lw

List all window layouts."
  exit
fi

list=$(find -L "$TMUXIFIER_LAYOUT_PATH" -name "*.window.sh" | sort)
for file in $list; do
  file=${file/$TMUXIFIER_LAYOUT_PATH\//}
  echo "${file/.window.sh/}"
done
