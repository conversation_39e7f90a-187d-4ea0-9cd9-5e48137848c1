#! /usr/bin/env bash
set -e
[ -n "$TMUXIFIER_DEBUG" ] && set -x

# Load internal utility functions.
source "$TMUXIFIER/lib/util.sh"

# Provide tmuxifier help
if calling-help "$@"; then
  echo "usage: tmuxifier alias <alias>

Resolve a command alias to it's full name."
  exit
fi

case "$1" in
  "session" | "ses" | "s" )
    echo "load-session"
    ;;
  "window" | "win" | "w" )
    echo "load-window"
    ;;
  "new-ses" | "nses" | "ns" )
    echo "new-session"
    ;;
  "new-win" | "nwin" | "nw" )
    echo "new-window"
    ;;
  "edit-ses" | "eses" | "es" )
    echo "edit-session"
    ;;
  "edit-win" | "ewin" | "ew" )
    echo "edit-window"
    ;;
  "l" )
    echo "list"
    ;;
  "list-ses" | "lses" | "ls" )
    echo "list-sessions"
    ;;
  "list-win" | "lwin" | "lw" )
    echo "list-windows"
    ;;
  * )
    exit 1
esac
