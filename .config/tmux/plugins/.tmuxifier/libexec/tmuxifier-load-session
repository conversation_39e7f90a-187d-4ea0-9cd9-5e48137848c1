#! /usr/bin/env bash
set -e
[ -n "$TMUXIFIER_DEBUG" ] && set -x

# Load internal utility functions.
source "$TMUXIFIER/lib/util.sh"

# Provide tmuxifier help
if calling-help "$@"; then
  echo "usage: tmuxifier load-session <layout_name | file_path> [<iterm mode>]

Aliases: session, ses, s

Create a session using the session layout, unless the session already exists
in which case, we simply attach/switch to the existing one.

Arguments:
   <layout_name | file_path>  - Name of a session layout stored in the layouts
                                directory, or path to a session layout file.
   <iterm mode>               - When given as \"-CC\" tmux will be called with
                                the -CC argument enabling iTerm2 integration.
                                More info: https://iterm2.com"
  exit
fi

# Provide tmuxifier completions
if calling-complete "$@"; then
  tmuxifier-list-sessions
  exit
fi

if [ -z "$1" ]; then
  echo "$(tmuxifier-help load-session $@)" >&2
  exit 1
fi

# Load runtime functions.
source "$TMUXIFIER/lib/runtime.sh"

if [ "$2" == "-CC" ]; then
  export TMUXIFIER_TMUX_ITERM_ATTACH="-CC"
fi

# Load session file.
load_session "$1"
