#! /usr/bin/env bash
set -e
[ -n "$TMUXIFIER_DEBUG" ] && set -x

# Load internal utility functions.
source "$TMUXIFIER/lib/util.sh"

# Provide tmuxifier help
if calling-help "$@"; then
  echo "usage: tmuxifier new-session <layout_name>

Aliases: new-ses, nses, ns

Create a new session layout and open it for editing in \$EDITOR."
  exit
fi

# Provide tmuxifier completions
if calling-complete "$@"; then
  tmuxifier-list-sessions
  exit
fi

if [ -z "$1" ]; then
  echo "$(tmuxifier-help new-session $@)" >&2
  exit 1
fi

layout_name="$1"
layout_file="$TMUXIFIER_LAYOUT_PATH/${layout_name}.session.sh"
template="$TMUXIFIER/templates/session.sh"

if [ ! -f "$template" ]; then
  echo "tmuxifier: session layout template not found: $template" >&2
  exit 1
fi

if [ -f "$layout_file" ]; then
  echo "session layout '$layout_name' already exists." >&2
  echo "" >&2
  echo "You can edit it with:" >&2
  echo "  tmuxifier edit-session \"$layout_name\"" >&2
  exit 1
fi

content="$(cat "$template")"
echo "${content//\{\{SESSION_NAME\}\}/$layout_name}" > "$layout_file"

if [ -n "$EDITOR" ]; then
  exec $EDITOR "$layout_file"
else
  echo "Layout file has been created, but '\$EDITOR' is not set. Please "
  echo "manually open the layout for editing:"
  echo "$layout_file"
  echo
fi
