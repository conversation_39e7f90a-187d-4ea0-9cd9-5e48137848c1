#! /usr/bin/env bash
set -e
[ -n "$TMUXIFIER_DEBUG" ] && set -x

# Load internal utility functions.
source "$TMUXIFIER/lib/util.sh"

# Provide tmuxifier help
if calling-help "$@"; then
  tmuxifier-help
  exit
fi

# Provide tmuxifier completions
if calling-complete "$@"; then
  tmuxifier-commands
  exit
fi

has-help() {
  grep -i "^# Provide tmuxifier help" "$1" >/dev/null
}

if [ -z "$1" ]; then
  echo "usage: tmuxifier <command> [<args>]

Some useful tmuxifier commands are:
   <command>      <alias>
   load-session   s        Load the specified session layout.
   load-window    w        Load the specified window layout into current session.
   list           l        List all session and window layouts.
   list-sessions  ls       List session layouts.
   list-windows   lw       List window layouts.
   new-session    ns       Create new session layout and open it with \$EDITOR.
   new-window     nw       Create new window layout and open it with \$EDITOR.
   edit-session   es       Edit specified session layout with \$EDITOR.
   edit-window    ew       Edit specified window layout with \$EDITOR.
   commands                List all tmuxifier commands (including internal).
   version                 Print Tmuxifier version.
   help                    Show this message.

See 'tmuxifier help <command>' for information on a specific command."
  exit
fi

! command_path="$(tmuxifier-resolve-command-path "$1")"

if [ -z "$command_path" ]; then
  echo "tmuxifier: no such command '$1'" >&2
  exit 1
fi

if has-help "$command_path"; then
  shift
  exec "$command_path" "$@" --help
else
  command="$(basename "$command_path")"
  command="${command/tmuxifier\-/}"
  echo "Sorry, the '$command' command isn't documented yet."
  echo ""
  echo "You can view the command's source here:"
  echo "$command_path"
  echo ""
fi
