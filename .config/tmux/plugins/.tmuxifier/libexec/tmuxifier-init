#! /usr/bin/env bash
set -e
[ -n "$TMUXIFIER_DEBUG" ] && set -x

# Set shell to first argument that is not "-", "-h" or "--help".
for arg in "$@"; do
  if [ "$arg" != "-" ] && [ "$arg" != "-h" ] && [ "$arg" != "--help" ]; then
    shell="$arg"
  fi
done

if [ -z "$shell" ]; then
  shell="$(basename "$SHELL")"
fi

case "$shell" in
  bash )
    profile='~/.bash_profile'
    ;;
  zsh )
    profile='~/.zshrc'
    ;;
  ksh )
    profile='~/.profile'
    ;;
  csh )
    profile='~/.cshrc'
    ;;
  tcsh )
    profile='~/.tcshrc'
    ;;
  fish )
    profile='~/.config/fish/config.fish'
    ;;
  * )
    profile='shell init file'
    ;;
esac

# Provide tmuxifier help
if [[ " $@ " == *" --help "* ]]; then
  echo "usage: tmuxifier init -

Load Tmuxifier by adding the following to your ${profile}:
"

  case "$shell" in
    csh | tcsh )
      echo "    eval \`tmuxifier init -\`
"
      ;;
    fish )
      echo "    eval (tmuxifier init -)
"
      ;;
    * )
      echo "    eval \"\$(tmuxifier init -)\"
"
      ;;
  esac

  echo "You might also need to add Tmuxifier's bin directory to your PATH."
  exit
fi

# Print help if "-" argument is not given
if [[ " $@ " != *" - "* ]]; then
  echo "$(tmuxifier-help init $@)" >&2
  exit 1
fi

case "$shell" in
  csh | tcsh )
    echo "setenv TMUXIFIER \"$TMUXIFIER\";"
    echo "source \"\$TMUXIFIER/init.tcsh\";"
    ;;
  fish )
    echo "set -gx TMUXIFIER \"$TMUXIFIER\";"
    # fish shell 2.0.0 does not have the source alias
    if [[ $(fish --version 2>&1 | awk -F'version ' '{print $2}') = '2.0.0' ]]; then
      echo ". \"\$TMUXIFIER/init.fish\";"
    else
      echo "source \"\$TMUXIFIER/init.fish\";"
    fi  
    ;;
  * )
    echo "export TMUXIFIER=\"$TMUXIFIER\";"
    echo "source \"\$TMUXIFIER/init.sh\";"
    ;;
esac
