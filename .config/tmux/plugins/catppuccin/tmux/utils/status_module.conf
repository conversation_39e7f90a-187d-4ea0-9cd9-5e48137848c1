# vim:set ft=tmux:

# Embedded style that ensures that modules look "connected"
# when required.
set -gqF @_ctp_connect_style \
  "#{?#{==:#{@catppuccin_status_connect_separator},yes},,#[bg=default]}"

# There are four colors involved:
#
# - Icon
#   - fg: @catppuccin_status_[module]_icon_fg [default = crust]
#   - bg: @catppuccin_status_[module]_icon_bg [required]
# - Text
#   - fg: @catppuccin_status_[module]_text_fg [default = foreground]
#   - bg: @catppuccin_status_[module]_text_bg [default = @catppuccin_status_module_text_bg]

set -ogqF "@catppuccin_status_${MODULE_NAME}_icon_fg" "#{E:@thm_crust}"
set -ogqF "@catppuccin_status_${MODULE_NAME}_text_fg" "#{E:@thm_fg}"

%if "#{==:#{@catppuccin_status_${MODULE_NAME}_icon_bg},}"
  set -gqF "@catppuccin_status_${MODULE_NAME}_icon_bg" "#{@catppuccin_${MODULE_NAME}_color}"
%endif

%if "#{==:#{@catppuccin_status_${MODULE_NAME}_text_bg},}"
  set -gqF @_ctp_module_text_bg "#{E:@catppuccin_status_module_text_bg}"
%else
  set -gqF @_ctp_module_text_bg "#{@catppuccin_status_${MODULE_NAME}_text_bg}"
%endif

set -gF "@catppuccin_status_${MODULE_NAME}" \
  "#[fg=#{@catppuccin_status_${MODULE_NAME}_icon_bg}]#{@_ctp_connect_style}#{@catppuccin_status_left_separator}"

set -agF "@catppuccin_status_${MODULE_NAME}" \
  "#[fg=#{@catppuccin_status_${MODULE_NAME}_icon_fg},bg=#{@catppuccin_status_${MODULE_NAME}_icon_bg}]#{@catppuccin_${MODULE_NAME}_icon}"

set -agF "@catppuccin_status_${MODULE_NAME}" \
  "#{@catppuccin_status_middle_separator}"

set -agF "@catppuccin_status_${MODULE_NAME}" \
  "#[fg=#{@catppuccin_status_${MODULE_NAME}_text_fg},bg=#{@_ctp_module_text_bg}]"

set -ag "@catppuccin_status_${MODULE_NAME}" "#{E:@catppuccin_${MODULE_NAME}_text}"

set -agF "@catppuccin_status_${MODULE_NAME}" "#{@_ctp_connect_style}#{@catppuccin_status_right_separator}"

set -ug @_ctp_connect_style
set -ug @_ctp_module_text_bg
