# All options and their defaults.
#
# This is executed separately to the main configuration
# so that options are set before parsing the rest of the config.

set -ogq @catppuccin_flavor "mocha"

set -ogq @catppuccin_status_background "default"

# Menu styling options
set -ogq @catppuccin_menu_selected_style "fg=#{@thm_fg},bold,bg=#{@thm_overlay_0}"

# Pane styling options
set -ogq @catppuccin_pane_status_enabled "no" # set to "yes" to enable
set -ogq @catppuccin_pane_border_status "off" # set to "yes" to enable
set -ogq @catppuccin_pane_border_style "fg=#{@thm_overlay_0}"
set -ogq @catppuccin_pane_active_border_style "##{?pane_in_mode,fg=#{@thm_lavender},##{?pane_synchronized,fg=#{@thm_mauve},fg=#{@thm_lavender}}}"
set -ogq @catppuccin_pane_left_separator "█"
set -ogq @catppuccin_pane_middle_separator "█"
set -ogq @catppuccin_pane_right_separator "█"
set -ogq @catppuccin_pane_color "#{@thm_green}"
set -ogq @catppuccin_pane_background_color "#{@thm_surface_0}"
set -ogq @catppuccin_pane_default_text "##{b:pane_current_path}"
set -ogq @catppuccin_pane_default_fill "number"
set -ogq @catppuccin_pane_number_position "left" # right, left

# NOTE: Changes to make the option names more intuitive and more closely follow
# the tmux naming conventions.
# @catppuccin_window_current_background -> @catppuccin_window_current_number_color
# @catppuccin_window_current_color      -> @catppuccin_window_current_text_color
# @catppuccin_window_default_background -> @catppuccin_window_number_color
# @catppuccin_window_default_color      -> @catppuccin_window_text_color
# @catppuccin_window_status             -> @catppuccin_window_flags
#
# Removed:
# @catppuccin_window_default_fill, @catppuccin_window_current_fill
# Just set the number and text colors.

# Window options
set -ogq @catppuccin_window_status_style "basic" # basic, rounded, slanted, custom, or none
set -ogq @catppuccin_window_text_color "#{@thm_surface_0}"
set -ogq @catppuccin_window_number_color "#{@thm_overlay_2}"
set -ogq @catppuccin_window_text " #T"
set -ogq @catppuccin_window_number "#I"
set -ogq @catppuccin_window_current_text_color "#{@thm_surface_1}"
set -ogq @catppuccin_window_current_number_color "#{@thm_mauve}"
set -ogq @catppuccin_window_current_text " #T"
set -ogq @catppuccin_window_current_number "#I"
set -ogq @catppuccin_window_number_position "left"

# Window flags
set -ogq @catppuccin_window_flags "none"
set -ogq @catppuccin_window_flags_icon_last " 󰖰" # -
set -ogq @catppuccin_window_flags_icon_current " 󰖯" # *
set -ogq @catppuccin_window_flags_icon_zoom " 󰁌" # Z
set -ogq @catppuccin_window_flags_icon_mark " 󰃀" # M
set -ogq @catppuccin_window_flags_icon_silent " 󰂛" # ~
set -ogq @catppuccin_window_flags_icon_activity " 󱅫" # #
set -ogq @catppuccin_window_flags_icon_bell " 󰂞" # !
# Matches icon order when using `#F` (`#!~[*-]MZ`)
set -ogq @catppuccin_window_flags_icon_format "##{?window_activity_flag,#{E:@catppuccin_window_flags_icon_activity},}##{?window_bell_flag,#{E:@catppuccin_window_flags_icon_bell},}##{?window_silence_flag,#{E:@catppuccin_window_flags_icon_silent},}##{?window_active,#{E:@catppuccin_window_flags_icon_current},}##{?window_last_flag,#{E:@catppuccin_window_flags_icon_last},}##{?window_marked_flag,#{E:@catppuccin_window_flags_icon_mark},}##{?window_zoomed_flag,#{E:@catppuccin_window_flags_icon_zoom},}"

# Status line options
set -ogq @catppuccin_status_left_separator ""
set -ogq @catppuccin_status_middle_separator ""
set -ogq @catppuccin_status_right_separator "#[reverse]█#[noreverse]"
set -ogq @catppuccin_status_connect_separator "yes" # yes, no

# Maintain backwards compatibility. Use @catppuccin_status_module_bg_color if it is set.
set -ogq @catppuccin_status_module_text_bg "#{?@catppuccin_status_module_bg_color,#{E:@catppuccin_status_module_bg_color},#{@thm_surface_0}}"
