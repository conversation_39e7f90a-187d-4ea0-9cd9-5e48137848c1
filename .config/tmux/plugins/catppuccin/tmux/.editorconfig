# EditorConfig helps developers define and maintain consistent
# coding styles between different editors and IDEs
# EditorConfig is awesome: https://EditorConfig.org

root = true

[*]
charset = utf-8
indent_size = 2
indent_style = space
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# go
[*.go]
indent_style = tab
indent_size = 4

# python
[*.{ini,py,py.tpl,rst}]
indent_size = 4

# rust
[*.rs]
indent_size = 4

# documentation, utils
[*.{md,mdx,diff}]
trim_trailing_whitespace = false

# windows shell scripts
[*.{cmd,bat,ps1}]
end_of_line = crlf
