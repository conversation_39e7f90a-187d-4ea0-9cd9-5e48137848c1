---
name: Shellcheck
permissions:
  contents: read
on:
  pull_request:
    paths-ignore:
      - "*.md"
      - "assets/**"
  push:
    paths-ignore:
      - "*.md"
      - "assets/**"
    branches:
      - main
  workflow_dispatch:
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  shellcheck:
    name: Shellcheck
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Run ShellCheck
        uses: ludeeus/action-shellcheck@master
        with:
          additional_files: "catppuccin.tmux"
        env:
          SHELLCHECK_OPTS: "-a"
