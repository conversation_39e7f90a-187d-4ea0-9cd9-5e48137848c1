{"last-release-sha": "408c02ccf44d0a59a7a63ce2b65c5c29982c5c0e", "packages": {".": {"release-type": "simple", "changelog-path": "CHANGELOG.md", "bump-minor-pre-major": true, "bump-patch-for-minor-pre-major": false, "draft": false, "extra-files": ["README.md", "docs/tutorials/01-getting-started.md"]}}, "changelog-sections": [{"type": "feat", "section": "Added"}, {"type": "change", "section": "Changed"}, {"type": "fix", "section": "Fixed"}, {"type": "deprecate", "section": "Deprecated"}, {"type": "remove", "section": "Removed"}, {"type": "perf", "section": "Performance Improvements"}, {"type": "revert", "section": "Reverts"}, {"type": "docs", "section": "Documentation"}], "$schema": "https://raw.githubusercontent.com/googleapis/release-please/main/schemas/config.json"}