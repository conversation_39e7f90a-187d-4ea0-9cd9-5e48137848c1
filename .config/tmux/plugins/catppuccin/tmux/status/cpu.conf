# vim:set ft=tmux:
%hidden MODULE_NAME="cpu"

set -ogq @cpu_low_fg_color "#{E:@thm_fg}"
set -ogq @cpu_medium_fg_color "#{E:@thm_fg}"
set -ogq @cpu_high_fg_color "#{E:@thm_crust}"

set -ogq @cpu_low_bg_color "#{E:@catppuccin_status_module_text_bg}"
set -ogq @cpu_medium_bg_color "#{E:@catppuccin_status_module_text_bg}"
set -ogq @cpu_high_bg_color "#{E:@thm_red}"

set -ogq "@catppuccin_${MODULE_NAME}_icon" " "
set -ogqF "@catppuccin_${MODULE_NAME}_color" "#{E:@thm_yellow}"
set -ogq "@catppuccin_status_${MODULE_NAME}_text_fg" "#{l:#{cpu_fg_color}}"
set -ogq "@catppuccin_status_${MODULE_NAME}_text_bg" "#{l:#{cpu_bg_color}}"
set -ogq "@catppuccin_${MODULE_NAME}_text" " #{l:#{cpu_percentage}}"

source -F "#{d:current_file}/../utils/status_module.conf"
