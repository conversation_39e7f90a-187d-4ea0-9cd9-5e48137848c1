# vim:set ft=tmux:
%hidden MODULE_NAME="kube"

# Requires https://github.com/jonmosco/kube-tmux

set -ogq "@catppuccin_${MODULE_NAME}_icon" "󱃾 "
set -ogqF "@catppuccin_${MODULE_NAME}_color" "#{E:@thm_blue}"
set -ogqF "@catppuccin_kube_context_color" "#{E:@thm_red}"
set -ogqF "@catppuccin_kube_namespace_color" "#{E:@thm_sky}"
set -ogq "@catppuccin_${MODULE_NAME}_text" \
   " #(${TMUX_PLUGIN_MANAGER_PATH}kube-tmux/kube.tmux 250 #{@catppuccin_kube_context_color} #{@catppuccin_kube_namespace_color})"

source -F "#{d:current_file}/../utils/status_module.conf"
