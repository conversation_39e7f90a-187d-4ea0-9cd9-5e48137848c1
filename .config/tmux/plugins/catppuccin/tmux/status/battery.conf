# vim:set ft=tmux:
%hidden MODULE_NAME="battery"

set -ogq @batt_icon_charge_tier8 "󰁹"
set -ogq @batt_icon_charge_tier7 "󰂁"
set -ogq @batt_icon_charge_tier6 "󰁿"
set -ogq @batt_icon_charge_tier5 "󰁾"
set -ogq @batt_icon_charge_tier4 "󰁽"
set -ogq @batt_icon_charge_tier3 "󰁼"
set -ogq @batt_icon_charge_tier2 "󰁻"
set -ogq @batt_icon_charge_tier1 "󰁺"
set -ogq @batt_icon_status_charged "󰚥"
set -ogq @batt_icon_status_charging "󰂄"
set -ogq @batt_icon_status_discharging "󰂃"
set -ogq @batt_icon_status_unknown "󰂑"
set -ogq @batt_icon_status_attached "󱈑"

set -ogq "@catppuccin_${MODULE_NAME}_icon" "#{l:#{battery_icon}} "
set -ogqF "@catppuccin_${MODULE_NAME}_color" "#{E:@thm_lavender}"
set -ogq "@catppuccin_${MODULE_NAME}_text" " #{l:#{battery_percentage}}"

source -F "#{d:current_file}/../utils/status_module.conf"
