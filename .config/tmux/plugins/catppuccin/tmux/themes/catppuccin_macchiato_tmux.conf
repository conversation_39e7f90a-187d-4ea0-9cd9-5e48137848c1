# vim:set ft=tmux:

# --> <PERSON><PERSON><PERSON><PERSON> (Macchiato)
set -ogq @thm_bg "#24273a"
set -ogq @thm_fg "#cad3f5"

# Colors
set -ogq @thm_rosewater "#f4dbd6"
set -ogq @thm_flamingo "#f0c6c6"
set -ogq @thm_rosewater "#f4dbd6"
set -ogq @thm_pink "#f5bde6"
set -ogq @thm_mauve "#c6a0f6"
set -ogq @thm_red "#ed8796"
set -ogq @thm_maroon "#ee99a0"
set -ogq @thm_peach "#f5a97f"
set -ogq @thm_yellow "#eed49f"
set -ogq @thm_green "#a6da95"
set -ogq @thm_teal "#8bd5ca"
set -ogq @thm_sky "#91d7e3"
set -ogq @thm_sapphire "#7dc4e4"
set -ogq @thm_blue "#8aadf4"
set -ogq @thm_lavender "#b7bdf8"

# Surfaces and overlays
set -ogq @thm_subtext_1 "#a5adcb"
set -ogq @thm_subtext_0 "#b8c0e0"
set -ogq @thm_overlay_2 "#939ab7"
set -ogq @thm_overlay_1 "#8087a2"
set -ogq @thm_overlay_0 "#6e738d"
set -ogq @thm_surface_2 "#5b6078"
set -ogq @thm_surface_1 "#494d64"
set -ogq @thm_surface_0 "#363a4f"
set -ogq @thm_mantle "#1e2030"
set -ogq @thm_crust "#181926"

