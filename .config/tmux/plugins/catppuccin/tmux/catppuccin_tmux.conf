source -F "#{d:current_file}/themes/catppuccin_#{@catppuccin_flavor}_tmux.conf"

%if "#{==:#{@catppuccin_status_background},default}"
  set -gF @_ctp_status_bg "#{@thm_mantle}"
  set -gF status-style "bg=#{@_ctp_status_bg},fg=#{@thm_fg}"

  %hidden CTP_MESSAGE_BACKGROUND="#{@thm_overlay_0}"
%elif "#{==:#{@catppuccin_status_background},none}"
  set -g status-style "default"
  set -g @_ctp_status_bg "none"

  %hidden CTP_MESSAGE_BACKGROUND="default"
%else
  # Treat @catppuccin_status_background as a format string.
  set -gF status-style "bg=#{E:@catppuccin_status_background},fg=#{@thm_fg}"
  set -gF @_ctp_status_bg "#{E:@catppuccin_status_background}"

  %hidden CTP_MESSAGE_BACKGROUND="#{E:@catppuccin_status_background}"
%endif

source -F "#{d:current_file}/status/application.conf"
source -F "#{d:current_file}/status/battery.conf"
source -F "#{d:current_file}/status/clima.conf"
source -F "#{d:current_file}/status/cpu.conf"
source -F "#{d:current_file}/status/date_time.conf"
source -F "#{d:current_file}/status/directory.conf"
source -F "#{d:current_file}/status/gitmux.conf"
source -F "#{d:current_file}/status/host.conf"
source -F "#{d:current_file}/status/kube.conf"
source -F "#{d:current_file}/status/load.conf"
source -F "#{d:current_file}/status/pomodoro_plus.conf"
source -F "#{d:current_file}/status/session.conf"
source -F "#{d:current_file}/status/uptime.conf"
source -F "#{d:current_file}/status/user.conf"
source -F "#{d:current_file}/status/weather.conf"

# messages
set -gF message-style "fg=#{@thm_teal},bg=$CTP_MESSAGE_BACKGROUND,align=centre"
set -gF message-command-style "fg=#{@thm_teal},bg=$CTP_MESSAGE_BACKGROUND,align=centre"

# menu
%if "#{>=:#{version},3.4}"
  set -gF menu-selected-style "#{E:@catppuccin_menu_selected_style}"
%endif

# panes
set -wgF pane-active-border-style "#{E:@catppuccin_pane_active_border_style}"
set -wgF pane-border-style "#{E:@catppuccin_pane_border_style}"

%if "#{==:#{@catppuccin_pane_status_enabled},yes}"
  # "internal" variables are kept as @_ctp_p_.*
  # and then unset at the end.
  set -gq @_ctp_p_left "" # the content on the left, usually a separator
  set -gq @_ctp_p_middle ""
  set -gq @_ctp_p_right ""
  set -gq @_ctp_p_number ""
  set -gq @_ctp_p_text ""

  %if "#{==:#{@catppuccin_pane_default_fill},none}"
    set -g @_ctp_p_left \
      "#[fg=#{@thm_surface_0},bg=default]#{@catppuccin_pane_left_separator}"
    set -g @_ctp_p_middle \
      "#[fg=#{@thm_fg},bg=#{@thm_surface_0}]#{@catppuccin_pane_middle_separator}"
    set -g @_ctp_p_right \
      "#[fg=#{@thm_surface_0},bg=default]#{@catppuccin_pane_right_separator}"

    set -g @_ctp_p_number \
      "#[fg=#{@thm_fg},bg=#{@thm_surface_0}]##{pane_index}"
    set -g @_ctp_p_text \
      "#[fg=#{@thm_fg},bg=#{@thm_surface_0}]#{E:@catppuccin_pane_default_text}"

  %elif "#{==:#{@catppuccin_pane_default_fill},all}"

    set -g @_ctp_p_left \
      "#[fg=#{E:@catppuccin_pane_color},bg=default]#{@catppuccin_pane_left_separator}"
    set -g @_ctp_p_middle \
      "#[fg=#{E:@catppuccin_pane_color},bg=#{E:@catppuccin_pane_background_color}]#{@catppuccin_pane_middle_separator}"
    set -g @_ctp_p_right \
      "#[fg=#{E:@catppuccin_pane_color},bg=default]#{@catppuccin_pane_right_separator}"

    set -g @_ctp_p_number \
      "#[fg=#{E:@catppuccin_pane_background_color},bg=#{E:@catppuccin_pane_color}]##{pane_index}"
    set -g @_ctp_p_text \
      "#[fg=#{E:@catppuccin_pane_background_color},bg=#{E:@catppuccin_pane_color}]#{E:@catppuccin_pane_default_text}"

  %elif "#{==:#{@catppuccin_pane_default_fill},number}"

    %if "#{==:#{@catppuccin_pane_number_position},left}"
      set -g @_ctp_p_left \
        "#[fg=#{E:@catppuccin_pane_color},bg=default]#{@catppuccin_pane_left_separator}"
      set -g @_ctp_p_right \
        "#[fg=#{E:@catppuccin_pane_background_color},bg=default]#{@catppuccin_pane_right_separator}"
    %else
      set -g @_ctp_p_left \
        "#[fg=#{E:@catppuccin_pane_background_color},bg=default]#{@catppuccin_pane_left_separator}"
      set -g @_ctp_p_right \
        "#[fg=#{E:@catppuccin_pane_color},bg=default]#{@catppuccin_pane_right_separator}"
    %endif

    set -g @_ctp_p_middle \
      "#[fg=#{E:@catppuccin_pane_color},bg=#{E:@catppuccin_pane_background_color}]#{@catppuccin_pane_middle_separator}"

    set -g @_ctp_p_number \
      "#[fg=#{E:@catppuccin_pane_background_color},bg=#{E:@catppuccin_pane_color}]##{pane_index}"
    set -g @_ctp_p_text \
      "#[fg=#{E:@catppuccin_pane_color},bg=#{E:@catppuccin_pane_background_color}]#{E:@catppuccin_pane_default_text}"

  %endif

  %if "#{==:#{@catppuccin_pane_number_position},left}"
    set -wgF pane-border-format \
      "#{E:@_ctp_p_left}#{E:@_ctp_p_number}#{E:@_ctp_p_middle} #{E:@_ctp_p_text}#{E:@_ctp_p_right}"
  %else
    set -wgF pane-border-format \
      "#{E:@_ctp_p_left}#{E:@_ctp_p_text} #{E:@_ctp_p_middle}#{E:@_ctp_p_number}#{E:@_ctp_p_right}"
  %endif

  set -ug @_ctp_p_left
  set -ug @_ctp_p_middle
  set -ug @_ctp_p_right
  set -ug @_ctp_p_number
  set -ug @_ctp_p_text
%endif

# popups
%if "#{>=:#{version},3.4}"
  set -gF popup-style "bg=#{@thm_bg},fg=#{@thm_fg}"
  set -gF popup-border-style "fg=#{@thm_surface_1}"
%endif

%if "#{==:#{@catppuccin_window_status_style},basic}"

  set -gq @catppuccin_window_left_separator " "
  set -gq @catppuccin_window_middle_separator " "
  set -gq @catppuccin_window_right_separator " "

%elif "#{==:#{@catppuccin_window_status_style},rounded}"

  set -gq @catppuccin_window_left_separator "#[fg=#{@_ctp_status_bg},reverse]#[none]"
  set -gq @catppuccin_window_middle_separator " "
  set -gq @catppuccin_window_right_separator "#[fg=#{@_ctp_status_bg},reverse]#[none]"

%elif "#{==:#{@catppuccin_window_status_style},slanted}"

  set -gq @catppuccin_window_left_separator "#[fg=#{@_ctp_status_bg},reverse]#[none]"

  %if "#{==:#{@catppuccin_window_number_position},left}"
    set -gq @catppuccin_window_middle_separator "#[fg=#{@catppuccin_window_number_color},bg=#{@catppuccin_window_text_color}]"
    set -gq @catppuccin_window_current_middle_separator \
      "#[fg=#{@catppuccin_window_current_number_color},bg=#{@catppuccin_window_current_text_color}]"
  %else
    set -gq @catppuccin_window_middle_separator " #[fg=#{@catppuccin_window_number_color},bg=#{@catppuccin_window_text_color}]"
    set -gq @catppuccin_window_current_middle_separator \
      " #[fg=#{@catppuccin_window_current_number_color},bg=#{@catppuccin_window_current_text_color}]"
  %endif

  set -gq @catppuccin_window_right_separator "#[fg=#{@_ctp_status_bg},reverse]█#[none]"

%endif

set -ogqF @catppuccin_window_current_left_separator "#{@catppuccin_window_left_separator}"
set -ogqF @catppuccin_window_current_middle_separator "#{@catppuccin_window_middle_separator}"
set -ogqF @catppuccin_window_current_right_separator "#{@catppuccin_window_right_separator}"

# window status
%if "#{!=:#{@catppuccin_window_status_style},none}"
  set -gF window-status-activity-style "bg=#{@thm_lavender},fg=#{@thm_crust}"
  set -gF window-status-bell-style "bg=#{@thm_yellow},fg=#{@thm_crust}"

   %if "#{==:#{@catppuccin_window_flags},icon}"
    set -gqF @_ctp_w_flags "#{E:@catppuccin_window_flags_icon_format} "
  %elif "#{==:#{@catppuccin_window_flags},text}"
    set -gq @_ctp_w_flags "#F"
  %else
    set -gq @_ctp_w_flags ""
  %endif

  set -g @_ctp_w_number_style "#[fg=#{@thm_crust},bg=#{@catppuccin_window_number_color}]"
  set -g @_ctp_w_text_style "#[fg=#{@thm_fg},bg=#{@catppuccin_window_text_color}]"
  %if "#{==:#{@catppuccin_window_number_position},left}"
    set -gF window-status-format \
      "#{E:@_ctp_w_number_style}#{E:@catppuccin_window_left_separator}#{@catppuccin_window_number}"
    set -agF window-status-format "#{E:@catppuccin_window_middle_separator}"
    set -agF window-status-format \
      "#{E:@_ctp_w_text_style}#{@catppuccin_window_text}#{@_ctp_w_flags}#{E:@catppuccin_window_right_separator}"
  %else
    set -gF window-status-format \
      "#{E:@_ctp_w_text_style}#{E:@catppuccin_window_left_separator}#{E:@_ctp_w_text_style}#{@catppuccin_window_text}#{@_ctp_w_flags}"
    set -agF window-status-format "#{E:@catppuccin_window_middle_separator}"
    set -agF window-status-format \
      "#{E:@_ctp_w_number_style} #{@catppuccin_window_number}#{E:@catppuccin_window_right_separator}"
  %endif

  # =======================================
  # And do the same for the current window.
  # =======================================

  set -g @_ctp_w_number_style "#[fg=#{@thm_crust},bg=#{@catppuccin_window_current_number_color}]"
  set -g @_ctp_w_text_style "#[fg=#{@thm_fg},bg=#{@catppuccin_window_current_text_color}]"
  %if "#{==:#{@catppuccin_window_number_position},left}"
    set -gF window-status-current-format \
      "#{E:@_ctp_w_number_style}#{E:@catppuccin_window_left_separator}#{@catppuccin_window_current_number}"
    set -agF window-status-current-format "#{E:@catppuccin_window_current_middle_separator}"
    set -agF window-status-current-format \
      "#{E:@_ctp_w_text_style}#{@catppuccin_window_current_text}#{@_ctp_w_flags}#{E:@catppuccin_window_right_separator}"
  %else
    set -gF window-status-current-format \
      "#{E:@_ctp_w_text_style}#{E:@catppuccin_window_left_separator}#{E:@_ctp_w_text_style}#{@catppuccin_window_current_text}#{@_ctp_w_flags}"
    set -agF window-status-current-format "#{E:@catppuccin_window_current_middle_separator}"
    set -agF window-status-current-format \
      "#{E:@_ctp_w_number_style} #{@catppuccin_window_current_number}#{E:@catppuccin_window_right_separator}"
  %endif


  # Cleanup (unset) all of the internal variables.
  set -ug @_ctp_w_number_style
  set -ug @_ctp_w_text_style
  set -ug @_ctp_w_flags
%endif

# Mode style. This is used for copy mode highlighting to style the current selection.
set -gF mode-style "bg=#{@thm_surface_0},bold"
set -gF clock-mode-colour "#{@thm_blue}"
