{"id": "e3f11087-473e-4dad-9e99-d46a28acc761", "zed": "context", "version": "0.4.0", "text": "\n\n\ndata \"aws_ami\" \"rohit_testbox_ami\" {\n  most_recent = true\n  owners      = [\"amazon\"]\n  filter {\n    name   = \"architecture\"\n    values = [\"x86_64\"]\n  }\n  filter {\n    name   = \"name\"\n    values = [\"ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-20240423\"]\n  }\n}\nin above data block, I want to provision arm architecture with ubuntu 24.04\nTo provision an ARM architecture (AWS Graviton) with Ubuntu 24.04, you'll need to modify the architecture filter and the AMI name pattern. Here's the modified data block:\n\n```hcl\ndata \"aws_ami\" \"rohit_testbox_ami\" {\n  most_recent = true\n  owners      = [\"amazon\"]\n  filter {\n    name   = \"architecture\"\n    values = [\"arm64\"]  # Changed from x86_64 to arm64\n  }\n  filter {\n    name   = \"name\"\n    values = [\"ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-arm64-server-*\"]  # Changed to arm64 pattern\n  }\n}\n```\n\nKey changes made:\n1. Changed architecture from `x86_64` to `arm64`\n2. Modified the AMI name pattern to include `arm64` instead of `amd64`\n\nRemember that when using an ARM-based AMI, you'll need to use an ARM-compatible instance type (like t4g, c6g, r6g, etc.) in your EC2 instance configuration.\n\nFor example, in your EC2 instance configuration, you would use something like:\n```hcl\nresource \"aws_instance\" \"example\" {\n  ami           = data.aws_ami.rohit_testbox_ami.id\n  instance_type = \"t4g.micro\"  # ARM-compatible instance type\n  # ... other configuration ...\n}\n```\n", "messages": [{"id": {"replica_id": 0, "value": 0}, "start": 0, "metadata": {"role": "user", "status": "Done", "timestamp": {"replica_id": 0, "value": 370}}}, {"id": {"replica_id": 0, "value": 7}, "start": 276, "metadata": {"role": "user", "status": "Done", "timestamp": {"replica_id": 0, "value": 371}}}, {"id": {"replica_id": 0, "value": 36}, "start": 352, "metadata": {"role": "assistant", "status": "Done", "timestamp": {"replica_id": 0, "value": 372}}}, {"id": {"replica_id": 0, "value": 37}, "start": 1432, "metadata": {"role": "user", "status": "Done", "timestamp": {"replica_id": 0, "value": 373}}}], "summary": "Ubuntu ARM AMI Data Configuration", "slash_command_output_sections": [{"range": {"start": 0, "end": 1}, "icon": "Library", "label": "<PERSON><PERSON><PERSON>", "metadata": null}]}