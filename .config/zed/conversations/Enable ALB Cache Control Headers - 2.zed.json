{"id": "2e1d6f32-6dd6-487d-8075-fe15650eb5ec", "zed": "context", "version": "0.4.0", "text": "\n\n\n```tf ops/terraform/prod/alb.tf\nresource \"aws_lb\" \"main\" {\n  name               = var.lb_name\n  internal           = var.lb_internal\n  load_balancer_type = var.lb_type\n  security_groups    = [aws_security_group.ecs_alb_loadbalancer_sg.id]\n  subnets            = [module.main_public_subnet_1.subnet_id, module.main_public_subnet_2.subnet_id, module.main_public_subnet_3.subnet_id]\n\n  enable_deletion_protection = var.lb_deletion_protection\n\n  access_logs {\n    bucket  = aws_s3_bucket.logs.bucket\n    prefix  = \"alb/ravenclaw/access-logs\"\n    enabled = true\n  }\n\n  connection_logs {\n    bucket  = aws_s3_bucket.logs.id\n    prefix  = \"alb/ravenclaw/connection-logs\"\n    enabled = true\n  }\n\n  tags = var.alb_tags\n}\n\nresource \"aws_lb_target_group\" \"main\" {\n  name        = var.bff_lb_target_group_name\n  target_type = var.bff_lb_target_group_target_type\n  port        = var.bff_lb_target_group_port\n  protocol    = var.bff_lb_target_group_protocol\n  vpc_id      = module.vpc_main.vpc_id\n  tags        = var.target_group_bff_tags\n  # health_check {\n  #   path = var.bff_lb_target_group_health_check_path\n  # }\n}\n\nresource \"aws_lb_target_group\" \"gateway\" {\n  name        = var.gateway_lb_target_group_name\n  target_type = var.gateway_lb_target_group_target_type\n  port        = var.gateway_lb_target_group_port\n  protocol    = var.gateway_lb_target_group_protocol\n  vpc_id      = module.vpc_main.vpc_id\n  tags        = var.target_group_gateway_tags\n  # health_check {\n  #   path = var.gateway_lb_target_group_health_check_path\n  # }\n}\n\nresource \"aws_lb_target_group\" \"swagger\" {\n  name        = \"swagger-target-group\"\n  target_type = \"ip\"\n  port        = 8082\n  protocol    = \"HTTP\"\n  vpc_id      = module.vpc_main.vpc_id\n  tags = {\n    Name        = \"ravenclaw-lb-target-group-swagger\"\n    Environment = \"prod\"\n    Product     = \"ravenclaw\"\n    Team        = \"infra\"\n  }\n  health_check {\n    path = \"/swagger/index.html\"\n  }\n}\n\n\nresource \"aws_lb_listener\" \"main\" {\n  load_balancer_arn = aws_lb.main.arn\n  port              = var.lb_listener_port\n  protocol          = var.lb_listener_protocol\n  ssl_policy        = var.lb_listener_ssl_policy\n\n  certificate_arn                                              = var.certificate_arn\n  routing_http_response_server_enabled                         = false\n  routing_http_response_strict_transport_security_header_value = \"max-age=********; includeSubDomains; preload\"\n  routing_http_response_x_content_type_options_header_value    = \"nosniff\"\n\n\n  # default_action {\n  #   type             = var.lb_listener_default_action_type\n  #   target_group_arn = aws_lb_target_group.main.arn\n  # }\n\n  default_action {\n    type = \"fixed-response\"\n\n    fixed_response {\n      content_type = \"text/plain\"\n      message_body = \"Either IP or endpoint may not be valid!\"\n      status_code  = \"404\"\n    }\n  }\n\n  tags = var.alb_listener_tags\n}\n\nresource \"aws_lb_listener_rule\" \"main\" {\n  listener_arn = aws_lb_listener.main.arn\n  priority     = 100\n\n  action {\n    type             = \"forward\"\n    target_group_arn = aws_lb_target_group.main.arn\n  }\n\n  condition {\n    path_pattern {\n      values = [\"/v0/api/*\"]\n    }\n  }\n\n  tags = var.alb_listener_rule_bff_tags\n}\n\nresource \"aws_lb_listener_rule\" \"gateway\" {\n  listener_arn = aws_lb_listener.main.arn\n  priority     = 101\n\n  action {\n    type             = \"forward\"\n    target_group_arn = aws_lb_target_group.gateway.arn\n  }\n\n  condition {\n    path_pattern {\n      values = [\"/v0/hooks/*\"]\n    }\n  }\n\n  tags = var.alb_listener_rule_gateway_tags\n}\n\nresource \"aws_lb_listener_rule\" \"swagger\" {\n  listener_arn = aws_lb_listener.main.arn\n  priority     = 102\n\n  action {\n    type             = \"forward\"\n    target_group_arn = aws_lb_target_group.swagger.arn\n  }\n\n  condition {\n    path_pattern {\n      values = [\"/swagger/*\"]\n    }\n  }\n\n  condition {\n    source_ip {\n      values = [\"*************/32\", \"*************/32\"]\n    }\n  }\n\n  tags = {\n    Name        = \"ravenclaw-lb-listener-rule-swagger\"\n    Environment = \"prod\"\n    Product     = \"ravenclaw\"\n    Team        = \"infra\"\n  }\n}\n\n# resource \"aws_alb_listener\" \"alb_http\" {\n#   load_balancer_arn = aws_lb.main.arn\n#   port              = \"80\"\n#   protocol          = \"HTTP\"\n\n#   default_action {\n#     type = \"redirect\"\n#     redirect {\n#       host        = \"ravenclaw.ravenmail.io\"\n#       port        = \"443\"\n#       protocol    = \"HTTPS\"\n#       status_code = \"HTTP_301\"\n#     }\n#   }\n\n#   tags = var.keycloak_alb_listener_tags\n# }\n\n# Keycloak ALB\nresource \"aws_lb\" \"keycloak\" {\n  name               = var.keycloak_lb_name\n  internal           = var.keycloak_lb_internal\n  load_balancer_type = var.keycloak_lb_type\n  security_groups    = [aws_security_group.ecs_alb_loadbalancer_sg.id]\n  subnets            = [module.main_public_subnet_1.subnet_id, module.main_public_subnet_2.subnet_id, module.main_public_subnet_3.subnet_id]\n\n  enable_deletion_protection = var.keycloak_lb_deletion_protection\n\n  access_logs {\n    bucket  = aws_s3_bucket.logs.id\n    prefix  = \"alb/keycloak/access-logs\"\n    enabled = true\n  }\n\n  connection_logs {\n    bucket  = aws_s3_bucket.logs.id\n    prefix  = \"alb/keycloak/connection-logs\"\n    enabled = true\n  }\n\n  tags = var.keycloak_alb_tags\n}\n\nresource \"aws_lb_target_group\" \"keycloak\" {\n  name        = var.keycloak_lb_target_group_name\n  target_type = var.keycloak_lb_target_group_target_type\n  port        = var.keycloak_lb_target_group_port\n  protocol    = var.keycloak_lb_target_group_protocol\n  vpc_id      = module.vpc_main.vpc_id\n  tags        = var.target_group_keycloak_tags\n  health_check {\n    path     = var.keycloak_lb_target_group_health_check_path\n    port     = var.kc_alb_health_check_port\n    protocol = var.kc_alb_health_check_protocol\n    matcher  = var.kc_alb_health_check_matcher\n  }\n}\n\nresource \"aws_lb_listener\" \"keycloak\" {\n  load_balancer_arn = aws_lb.keycloak.arn\n  port              = var.keycloak_lb_listener_port\n  protocol          = var.keycloak_lb_listener_protocol\n  ssl_policy        = var.keycloak_lb_listener_ssl_policy\n\n  certificate_arn                                              = var.keycloak_certificate_arn\n  routing_http_response_server_enabled                         = false\n  routing_http_response_strict_transport_security_header_value = \"max-age=********; includeSubDomains; preload\"\n  routing_http_response_x_content_type_options_header_value    = \"nosniff\"\n\n  default_action {\n    type = var.kc_alb_listener_default_action_type\n\n    target_group_arn = aws_lb_target_group.keycloak.id\n  }\n\n  tags = var.keycloak_alb_listener_tags\n}\n\n# resource \"aws_alb_listener\" \"keycloak_http\" {\n#   load_balancer_arn = aws_lb.keycloak.arn\n#   port              = var.kc_alb_http_listener_port\n#   protocol          = var.kc_alb_http_listener_protocol\n\n#   default_action {\n#     type = var.kc_alb_http_listener_default_action_type\n#     redirect {\n#       host        = var.keycloak_hostname == \"\" ? aws_lb.keycloak.dns_name : var.keycloak_hostname\n#       port        = var.kc_alb_http_listener_default_action_redirect_port\n#       protocol    = var.kc_alb_http_listener_default_action_redirect_protocol\n#       status_code = var.kc_alb_http_listener_default_action_redirect_status_code\n#     }\n#   }\n\n#   tags = var.keycloak_alb_listener_tags\n# }\n\n\n\nresource \"aws_lb_listener_rule\" \"keycloak_https_redirect\" {\n  listener_arn = aws_lb_listener.keycloak.arn\n  count        = var.keycloak_hostname == \"\" ? 0 : 1\n\n  action {\n    type = var.kc_alb_https_redirect_action_type\n    redirect {\n      host        = var.keycloak_hostname\n      port        = var.kc_alb_https_redirect_action_redirect_port\n      protocol    = var.kc_alb_https_redirect_action_redirect_protocol\n      status_code = var.kb_alb_https_redirect_action_redirect_status_code\n    }\n  }\n\n  condition {\n    host_header {\n      values = [aws_lb.keycloak.dns_name]\n    }\n  }\n\n  tags = var.alb_listener_rule_keycloak_tags\n}\n\nresource \"aws_lb_listener_rule\" \"keycloak\" {\n  listener_arn = aws_lb_listener.keycloak.arn\n  priority     = 100\n\n  action {\n    type             = var.kc_alb_listener_rule_100_action_type\n    target_group_arn = aws_lb_target_group.keycloak.arn\n  }\n\n  condition {\n    path_pattern {\n      values = var.kc_alb_listener_rule_100_condition_path_pattern\n    }\n  }\n\n  tags = var.alb_listener_rule_keycloak_tags\n}\n```\n\nenable cache-control header\nTo enable cache-control headers in your ALB configuration, you can add them using the `response_headers` configuration in your ALB listener rules. Here's how you can modify your configuration:\n\nFor the main listener and Keycloak listener, add the cache-control headers like this:\n\n```hcl\n# For the main listener\nresource \"aws_lb_listener\" \"main\" {\n  # ... existing configuration ...\n\n  # Add these lines\n  response_headers {\n    cache_control = \"no-store, no-cache, must-revalidate\"\n  }\n}\n\n# For the Keycloak listener\nresource \"aws_lb_listener\" \"keycloak\" {\n  # ... existing configuration ...\n\n  # Add these lines\n  response_headers {\n    cache_control = \"no-store, no-cache, must-revalidate\"\n  }\n}\n```\n\nYou can also add cache-control headers to specific listener rules if you want different caching behavior for different paths:\n\n```hcl\nresource \"aws_lb_listener_rule\" \"main\" {\n  # ... existing configuration ...\n\n  action {\n    type             = \"forward\"\n    target_group_arn = aws_lb_target_group.main.arn\n\n    # Add these lines\n    fixed_response_headers {\n      name  = \"Cache-Control\"\n      value = \"no-store, no-cache, must-revalidate\"\n    }\n  }\n}\n\nresource \"aws_lb_listener_rule\" \"keycloak\" {\n  # ... existing configuration ...\n\n  action {\n    type             = \"forward\"\n    target_group_arn = aws_lb_target_group.keycloak.arn\n\n    # Add these lines\n    fixed_response_headers {\n      name  = \"Cache-Control\"\n      value = \"no-store, no-cache, must-revalidate\"\n    }\n  }\n}\n```\n\nThe `no-store, no-cache, must-revalidate` value is a common security-focused cache control setting that:\n- `no-store`: Prevents storing of any response\n- `no-cache`: Requires validation with the origin server before using cached content\n- `must-revalidate`: Forces checking that cached content is still valid\n\nChoose the cache control values that best match your security and performance requirements. Some other common options include:\n- `private, no-cache`: For user-specific content\n- `public, max-age=3600`: For public content that can be cached for 1 hour\n- `no-cache`: Requires revalidation but allows storing\n", "messages": [{"id": {"replica_id": 0, "value": 0}, "start": 0, "metadata": {"role": "user", "status": "Done", "timestamp": {"replica_id": 0, "value": 80}}}, {"id": {"replica_id": 0, "value": 18}, "start": 8325, "metadata": {"role": "assistant", "status": "Done", "timestamp": {"replica_id": 0, "value": 81}}}, {"id": {"replica_id": 0, "value": 19}, "start": 10431, "metadata": {"role": "user", "status": "Done", "timestamp": {"replica_id": 0, "value": 82}}}], "summary": "Enable ALB Cache Control Headers", "slash_command_output_sections": [{"range": {"start": 0, "end": 1}, "icon": "Library", "label": "<PERSON><PERSON><PERSON>", "metadata": null}, {"range": {"start": 3, "end": 8296}, "icon": "File", "label": "ops/terraform/prod/alb.tf", "metadata": {"path": "ops/terraform/prod/alb.tf"}}]}