{"id": "07b56191-dcae-4ffd-9165-dd147f3bf028", "zed": "context", "version": "0.4.0", "text": "\n\n\n```py ops/terraform/prod/lambda_function_vpc_flow_logs_payload/lambda.py\nimport csv\nimport gzip\nimport json\nfrom datetime import datetime\nfrom os import environ\nfrom queue import Full\nfrom tempfile import NamedTemporaryFile\nfrom urllib.parse import unquote_plus\n\nimport boto3\n\nLOG_GROUP_NAME = environ[\"LOG_GROUP_NAME\"]\n\n# https://docs.aws.amazon.com/elasticloadbalancing/latest/application/load-balancer-connection-logs.html\nFIELD_NAMES = [\n    \"version\",\n    \"account-id\",\n    \"interface-id\",\n    \"srcaddr\",\n    \"dstaddr\",\n    \"srcport\",\n    \"dstport\",\n    \"protocol\",\n    \"packets\",\n    \"bytes\",\n    \"start\",\n    \"end\",\n    \"action\",\n    \"log-status\",\n    \"vpc-id\",\n    \"subnet-id\",\n    \"instance-id\",\n    \"tcp-flags\",\n    \"type\",\n    \"pkt-srcaddr\",\n    \"pkt-dstaddr\",\n    \"region\",\n    \"az-id\",\n    \"sublocation-type\",\n    \"sublocation-id\",\n    \"pkt-src-aws-service\",\n    \"pkt-dst-aws-service\",\n    \"flow-direction\",\n    \"traffic-path\",\n    \"ecs-cluster-arn\",\n    \"ecs-cluster-name\",\n    \"ecs-container-instance-arn\",\n    \"ecs-container-instance-id\",\n    \"ecs-container-id\",\n    \"ecs-second-container-id\",\n    \"ecs-service-name\",\n    \"ecs-task-definition-arn\",\n    \"ecs-task-arn\",\n    \"ecs-task-id\",\n    \"reject-reason\"\n]\n\nlogs_client = boto3.client(\"logs\")\ns3_client = boto3.client(\"s3\")\n\n\nclass LogEventsBatch:\n    \"\"\"\n    Stores multiple log events to be sent in a batch to CloudWatch.\n\n    This is used to track the limits of the PutLogEvents API call:\n        * number of log events\n        * combined size of messages\n\n    \"\"\"\n\n    # https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/logs.html#CloudWatchLogs.Client.put_log_events\n    MAX_BATCH_COUNT = 10000\n    MAX_BATCH_SIZE = 1048576\n    LOG_EVENT_OVERHEAD = 26\n\n    def __init__(self):\n        self._events = []\n        self._size = 0\n\n    def read(self):\n        \"\"\"\n        Returns the log events in this batch,\n        and clears it out.\n\n        \"\"\"\n\n        try:\n            return self._events\n        finally:\n            self._events = []\n            self._size = 0\n\n    def write(self, message, timestamp):\n        \"\"\"\n        Adds a log event to the batch. If the log event cannot be added\n        due to batch limits, then a Full exception is raised instead.\n\n        \"\"\"\n\n        if len(self._events) >= self.MAX_BATCH_COUNT:\n            raise Full\n\n        event_size = len(message) + self.LOG_EVENT_OVERHEAD\n        if self._size + event_size > self.MAX_BATCH_SIZE:\n            raise Full\n\n        event = {\"message\": message, \"timestamp\": timestamp}\n\n        self._events.append(event)\n        self._size += event_size\n\n\nclass LogStream:\n    def __init__(self, name):\n        self._name = name\n        self._log_events_batch = LogEventsBatch()\n        self._sequence_token_kwarg = {}\n\n    def flush(self):\n        \"\"\"\n        Sends the current log events batch to CloudWatch\n        and then clears the batch.\n\n        \"\"\"\n\n        events = self._log_events_batch.read()\n\n        if not events:\n            return\n\n        response = logs_client.put_log_events(\n            logGroupName=LOG_GROUP_NAME,\n            logStreamName=self._name,\n            logEvents=events,\n            **self._sequence_token_kwarg,\n        )\n\n        self._sequence_token_kwarg[\"sequenceToken\"] = response[\"nextSequenceToken\"]\n\n        rejected_info = response.get(\"rejectedLogEventsInfo\")\n        if rejected_info:\n\n            # The expired log events.\n            expired_index = rejected_info.get(\"expiredLogEventEndIndex\")\n            if expired_index is not None:\n                expired_events = events[0:expired_index]\n                for event in expired_events:\n                    print(\"ERROR: CloudWatch Log event expired: {}\".format(event))\n\n            # The log events that are too old.\n            too_old_index = rejected_info.get(\"tooOldLogEventEndIndex\")\n            if too_old_index is not None:\n                too_old_events = events[0:too_old_index]\n                for event in too_old_events:\n                    print(\n                        \"ERROR: CloudWatch Log event timestamp too old: {}\".format(\n                            event\n                        )\n                    )\n\n            # The log events that are too new.\n            too_new_index = rejected_info.get(\"tooNewLogEventStartIndex\")\n            if too_new_index is not None:\n                too_new_events = events[too_new_index:-1]\n                for event in too_new_events:\n                    print(\n                        \"ERROR: CloudWatch Log event timestamp too new: {}\".format(\n                            event\n                        )\n                    )\n\n            raise Exception(rejected_info)\n\n    def truncate(self):\n        \"\"\"\n        Ensures that the log stream exists in CloudWatch.\n        Any existing logs in the log stream will be deleted.\n\n        \"\"\"\n\n        try:\n            logs_client.create_log_stream(\n                logGroupName=LOG_GROUP_NAME, logStreamName=self._name\n            )\n        except logs_client.exceptions.ResourceAlreadyExistsException:\n            logs_client.delete_log_stream(\n                logGroupName=LOG_GROUP_NAME, logStreamName=self._name\n            )\n            logs_client.create_log_stream(\n                logGroupName=LOG_GROUP_NAME, logStreamName=self._name\n            )\n\n        self._sequence_token_kwarg.clear()\n\n    def write(self, message, timestamp):\n        \"\"\"\n        Adds a log event to the batch. If the log event cannot be added\n        due to batch limits, then a Full exception is raised instead.\n\n        This only prepares the batch of log events to be sent to CloudWatch.\n        Use flush() to send the log event batch to CloudWatch.\n\n        \"\"\"\n\n        self._log_events_batch.write(message, timestamp)\n\n\ndef parse_iso8601(string):\n    \"\"\"\n    Parses a string such as 2020-01-15T12:29:59.432228Z\n    and returns a matching datetime object.\n\n    \"\"\"\n    dt = datetime.utcfromtimestamp(string)\n    iso8601_string = dt.strftime(\"%Y-%m-%dT%H:%M:%S.%fZ\")\n\n    return iso8601_string\n\n\ndef read_log_entries(bucket, key):\n    \"\"\"\n    Reads ALB log entries from an S3 object.\n    Generates a dictionary for each log line.\n\n    \"\"\"\n\n    with NamedTemporaryFile() as temp_file:\n        s3_client.download_file(bucket, key, temp_file.name)\n        with gzip.open(temp_file.name, mode=\"rt\") as extracted_file:\n            reader = csv.DictReader(\n                extracted_file, fieldnames=FIELD_NAMES, delimiter=\" \"\n            )\n            yield from reader\n\n\ndef handler(event, context):\n    for record in event[\"Records\"]:\n\n        bucket = record[\"s3\"][\"bucket\"][\"name\"]\n        key = unquote_plus(record[\"s3\"][\"object\"][\"key\"])\n\n        print(f\"Processing s3://{bucket}/{key}\")\n\n        log_stream = LogStream(f\"{bucket}/{key}\")\n        log_stream.truncate()\n\n        # Logs must be added to CloudWatch in chronological order.\n        # Entries in the S3 log file are sometimes out of order.\n        # So create a list with every log entry and sort them\n        # before pushing them to CloudWatch.\n\n        parsed_entries = []\n        for entry in read_log_entries(bucket, key):\n            message = json.dumps(entry)\n            # timestamp = int(parse_iso8601(entry[\"timestamp\"]).timestamp() * 1000)\n            if entry[\"version\"] == \"version\":\n                continue\n            # if not isinstance(entry[\"start\"], int):\n            #     continue\n            timestamp = int(entry[\"start\"])\n            parsed_entries.append((timestamp, message))\n\n        parsed_entries.sort()\n\n        for (timestamp, message) in parsed_entries:\n            try:\n                log_stream.write(message, timestamp)\n            except Full:\n                log_stream.flush()\n                log_stream.write(message, timestamp)\n\n        log_stream.flush()\n```\n\nCloudWatch Log event timestamp too old:\nThis error occurs when trying to send log events to CloudWatch Logs with timestamps that are too old. CloudWatch Logs has restrictions on the age of log events that can be ingested.\n\nHere are the key constraints for CloudWatch Logs timestamps:\n\n1. Log events must be within 14 days in the past\n2. Log events must be within 2 hours in the future \n\nTo fix this issue, you have a few options:\n\n1. Verify the timestamp calculation is correct:\n```python\n# Make sure timestamp is in milliseconds since epoch\ntimestamp = int(entry[\"start\"] * 1000) # If start is in seconds\n```\n\n2. Add validation to skip events that are too old:\n```python\nfrom datetime import datetime, timedelta\n\ndef is_timestamp_valid(timestamp_ms):\n    now = datetime.utcnow()\n    event_time = datetime.fromtimestamp(timestamp_ms/1000)\n    \n    # Check if event is within valid time window\n    too_old = event_time < now - timedelta(days=14)\n    too_new = event_time > now + timedelta(hours=2)\n    \n    return not (too_old or too_new)\n\n# Then in your code:\nfor (timestamp, message) in parsed_entries:\n    if not is_timestamp_valid(timestamp):\n        print(f\"Skipping event with invalid timestamp: {timestamp}\")\n        continue\n        \n    try:\n        log_stream.write(message, timestamp) \n    except Full:\n        log_stream.flush()\n        log_stream.write(message, timestamp)\n```\n\n3. Use current time for old events:\n```python\nfrom time import time\n\ncurrent_time = int(time() * 1000)\n\nfor (timestamp, message) in parsed_entries:\n    # Use current time if timestamp is too old\n    adjusted_timestamp = current_time if timestamp < (current_time - 14*24*60*60*1000) else timestamp\n    \n    try:\n        log_stream.write(message, adjusted_timestamp)\n    except Full:\n        log_stream.flush()\n        log_stream.write(message, adjusted_timestamp)\n```\n\n4. Add error handling to gracefully handle rejected events:\n```python\ntry:\n    log_stream.flush()\nexcept Exception as e:\n    print(f\"Failed to write logs: {e}\")\n    # Handle or report error as needed\n```\n\nChoose the approach that best fits your requirements:\n\n- Skip old events if historical data isn't needed\n- Use current timestamp if approximate timing is acceptable \n- Add validation to prevent invalid events from being sent\n- Handle errors gracefully if some events can be dropped\n\nThe key is ensuring all timestamps sent to CloudWatch Logs are within the valid time window of -14 days to +2 hours from current time.\n", "messages": [{"id": {"replica_id": 0, "value": 0}, "start": 0, "metadata": {"role": "user", "status": "Done", "timestamp": {"replica_id": 0, "value": 40}}}, {"id": {"replica_id": 0, "value": 19}, "start": 7878, "metadata": {"role": "assistant", "status": "Done", "timestamp": {"replica_id": 0, "value": 41}}}, {"id": {"replica_id": 0, "value": 20}, "start": 10319, "metadata": {"role": "user", "status": "Done", "timestamp": {"replica_id": 0, "value": 42}}}], "summary": "VPC Flow Logs Timestamp Error Handling", "slash_command_output_sections": [{"range": {"start": 0, "end": 1}, "icon": "Library", "label": "<PERSON><PERSON><PERSON>", "metadata": null}, {"range": {"start": 3, "end": 7837}, "icon": "File", "label": "ops/terraform/prod/lambda_function_vpc_flow_logs_payload/lambda.py", "metadata": {"path": "ops/terraform/prod/lambda_function_vpc_flow_logs_payload/lambda.py"}}], "thought_process_output_sections": []}