{"LuaSnip": {"branch": "master", "commit": "c1851d5c519611dfc451b6582961b2602e0af89b"}, "VectorCode": {"branch": "main", "commit": "0be48d75aff47c9d29d448766eacb946844bc718"}, "alpha-nvim": {"branch": "main", "commit": "de72250e054e5e691b9736ee30db72c65d560771"}, "catppuccin": {"branch": "main", "commit": "1bf070129c0b6f77cc23f6a2212dcdc868308c52"}, "cmp-buffer": {"branch": "main", "commit": "b74fab3656eea9de20a9b8116afa3cfc4ec09657"}, "cmp-nvim-lsp": {"branch": "main", "commit": "a8912b88ce488f411177fc8aed358b04dc246d7b"}, "cmp-nvim-lua": {"branch": "main", "commit": "f12408bdb54c39c23e67cab726264c10db33ada8"}, "cmp-path": {"branch": "main", "commit": "c6635aae33a50d6010bf1aa756ac2398a2d54c32"}, "cmp_luasnip": {"branch": "master", "commit": "98d9cb5c2c38532bd9bdb481067b20fea8f32e90"}, "codecompanion-history.nvim": {"branch": "main", "commit": "36f1a611a2bed70b57a50c877ffc50bdb74c9d23"}, "codecompanion.nvim": {"branch": "main", "commit": "d19670a44c35e9ba0674cc7a25ff3b8f22bbf062"}, "codeium.nvim": {"branch": "main", "commit": "821b570b526dbb05b57aa4ded578b709a704a38a"}, "conform.nvim": {"branch": "master", "commit": "2b2b30260203af3b93a7470ac6c8457ddd6e32d9"}, "copilot-cmp": {"branch": "master", "commit": "15fc12af3d0109fa76b60b5cffa1373697e261d1"}, "copilot.lua": {"branch": "master", "commit": "2fe34db04570f6c47db0b752ca421a49b7357c03"}, "copilot.vim": {"branch": "release", "commit": "d1e8429bef7f7709586886b0a23a46fbecc685c4"}, "diagram.nvim": {"branch": "master", "commit": "84fe677aa940605ef248f36c0ea23561690c95eb"}, "diffview.nvim": {"branch": "main", "commit": "4516612fe98ff56ae0415a259ff6361a89419b0a"}, "dressing.nvim": {"branch": "master", "commit": "2d7c2db2507fa3c4956142ee607431ddb2828639"}, "fidget.nvim": {"branch": "main", "commit": "d9ba6b7bfe29b3119a610892af67602641da778e"}, "flash.nvim": {"branch": "main", "commit": "3c942666f115e2811e959eabbdd361a025db8b63"}, "friendly-snippets": {"branch": "main", "commit": "572f5660cf05f8cd8834e096d7b4c921ba18e175"}, "fugit2.nvim": {"branch": "main", "commit": "bc68dd19bd2f3957a674ccd2da14a922746e93d9"}, "gitsigns.nvim": {"branch": "main", "commit": "e399f9748d7cfd8859747c8d6c4e9c8b4d50a1bd"}, "hererocks": {"branch": "master", "commit": "c9c5444dea1e07e005484014a8231aa667be30b6"}, "image.nvim": {"branch": "master", "commit": "4c51d6202628b3b51e368152c053c3fb5c5f76f2"}, "img-clip.nvim": {"branch": "main", "commit": "08a02e14c8c0d42fa7a92c30a98fd04d6993b35d"}, "incline.nvim": {"branch": "main", "commit": "27040695b3bbfcd3257669037bd008d1a892831d"}, "indent-blankline.nvim": {"branch": "master", "commit": "005b56001b2cb30bfa61b7986bc50657816ba4ba"}, "lazy.nvim": {"branch": "main", "commit": "6c3bda4aca61a13a9c63f1c1d1b16b9d3be90d7a"}, "lazydev.nvim": {"branch": "main", "commit": "2367a6c0a01eb9edb0464731cc0fb61ed9ab9d2c"}, "lspkind.nvim": {"branch": "master", "commit": "d79a1c3299ad0ef94e255d045bed9fa26025dab6"}, "lualine.nvim": {"branch": "master", "commit": "15884cee63a8c205334ab13ab1c891cd4d27101a"}, "markview.nvim": {"branch": "main", "commit": "68c9603b6f88fd962444f8579024418fe5e170f1"}, "mason-lspconfig.nvim": {"branch": "main", "commit": "d39a75bbce4b8aad5d627191ea915179c77c100f"}, "mason.nvim": {"branch": "main", "commit": "7c7318e8bae7e3536ef6b9e86b9e38e74f2e125e"}, "mcphub.nvim": {"branch": "main", "commit": "b254c452b921a8891d2fb7a2864e798f46fbed77"}, "menu": {"branch": "main", "commit": "8adb036ec34c679050913864cbc98cc64eb91f6c"}, "mini.diff": {"branch": "main", "commit": "ec8a5ae365c5d15920721ea42b1351dbc9e61f2d"}, "noice.nvim": {"branch": "main", "commit": "0427460c2d7f673ad60eb02b35f5e9926cf67c59"}, "nui.nvim": {"branch": "main", "commit": "f535005e6ad1016383f24e39559833759453564e"}, "nvim-autopairs": {"branch": "master", "commit": "4d74e75913832866aa7de35e4202463ddf6efd1b"}, "nvim-cmp": {"branch": "main", "commit": "b5311ab3ed9c846b585c0c15b7559be131ec4be9"}, "nvim-lightbulb": {"branch": "master", "commit": "aa3a8b0f4305b25cfe368f6c9be9923a7c9d0805"}, "nvim-lint": {"branch": "master", "commit": "fdb04e9285edefbe25a02a31a35e8fbb10fe054d"}, "nvim-lspconfig": {"branch": "master", "commit": "5a137448fd921a0c5d3939cb75e60d21f64e4606"}, "nvim-notify": {"branch": "master", "commit": "b5825cf9ee881dd8e43309c93374ed5b87b7a896"}, "nvim-tinygit": {"branch": "main", "commit": "3b9b4ce2a876e473e2bf3f87a5a73939cee6f624"}, "nvim-tree.lua": {"branch": "master", "commit": "e7d1b7dadc62fe2eccc17d814354b0a5688621ce"}, "nvim-treesitter": {"branch": "master", "commit": "066fd6505377e3fd4aa219e61ce94c2b8bdb0b79"}, "nvim-ufo": {"branch": "main", "commit": "3c7a3570e9c9dc198a2ad4491b0b0e51c4d4ba08"}, "nvim-web-devicons": {"branch": "master", "commit": "f1420728f59843eb2ef084406b3d0201a0a0932d"}, "outline.nvim": {"branch": "main", "commit": "321f89ef79f168a78685f70d70c52d0e7b563abb"}, "plenary.nvim": {"branch": "master", "commit": "857c5ac632080dba10aae49dba902ce3abf91b35"}, "promise-async": {"branch": "main", "commit": "119e8961014c9bfaf1487bf3c2a393d254f337e2"}, "remote-sshfs.nvim": {"branch": "main", "commit": "1ae5784bf0729c8b03cb7fe6561508a673c9adc8"}, "render-markdown.nvim": {"branch": "main", "commit": "df64d5d5432e13026a79384ec4e2bab185fd4eb5"}, "snacks.nvim": {"branch": "main", "commit": "bc0630e43be5699bb94dadc302c0d21615421d93"}, "tabby.nvim": {"branch": "main", "commit": "0207f9eba073be14688ffdbec68064835066e770"}, "telescope.nvim": {"branch": "master", "commit": "b4da76be54691e854d3e0e02c36b0245f945c2c7"}, "today.nvim": {"branch": "main", "commit": "e9fb7f53cfa27d1da7fdb67de4bcba65ca02dc34"}, "todo-comments.nvim": {"branch": "main", "commit": "304a8d204ee787d2544d8bc23cd38d2f929e7cc5"}, "trouble.nvim": {"branch": "main", "commit": "85bedb7eb7fa331a2ccbecb9202d8abba64d37b3"}, "vim-repeat": {"branch": "master", "commit": "65846025c15494983dafe5e3b46c8f88ab2e9635"}, "vim-surround": {"branch": "master", "commit": "3d188ed2113431cf8dac77be61b842acb64433d9"}, "volt": {"branch": "main", "commit": "c45d5f48da8e802e608b5c6da471ca4d84276dfb"}, "which-key.nvim": {"branch": "main", "commit": "370ec46f710e058c9c1646273e6b225acf47cbed"}, "yanky.nvim": {"branch": "main", "commit": "04775cc6e10ef038c397c407bc17f00a2f52b378"}}