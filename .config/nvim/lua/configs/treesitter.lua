return {
  ensure_installed = { 
    "astro",
    "bash",
    "c",
    "caddy",
    "comment",
    "cpp",
    "css",
    "csv",
    "diff",
    "dockerfile",
    "git_config",
    "git_rebase",
    "go",
    "gomod",
    "gosum",
    "gpg",
    "graphql",
    "hcl",
    "html",
    "http",
    "ini",
    "java",
    "jinja",
    "javascript",
    "jq",
    "lua", 
    "luadoc", 
    "make",
    "nginx",
    "nix",
    "passwd",
    "perl",
    "printf",
    "prisma",
    "python",
    "regex",
    "rust",
    "scss",
    "sql",
    "terraform",
    "tmux",
    "toml",
    "typescript",
    "xml",
    "yaml",
    "vim", 
    "vimdoc" 
  },


  highlight = {
    enable = true,
    use_languagetree = true,
  },

  indent = { enable = true },
}
