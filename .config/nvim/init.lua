vim.g.theme_cache = "/Users/<USER>/.cache/nvim/catppuccin/"
-- vim.fn.stdpath "share" .. "/lazy/catppuccin/" -- sets a path where theme plugin caches its generated Lua files

vim.g.mapleader = " "
vim.deprecate = function() end

-- bootstrap lazy and all plugins
local lazypath = vim.fn.stdpath "data" .. "/lazy/lazy.nvim" -- install location of lazy.nvim

-- -- if not found lazy, will clone from github
if not vim.uv.fs_stat(lazypath) then
  local repo = "https://github.com/folke/lazy.nvim.git"
  vim.fn.system { "git", "clone", "--filter=blob:none", repo, "--branch=stable", lazypath }
end

-- -- add lazy to start of runtime
vim.opt.rtp:prepend(lazypath)

-- load custom lazy config
local lazy_config = require "lazy"
local theme_config = require "plugins.colorscheme"
local plenary_config = require "plugins.plenary"
local nvzone = require "plugins.nvzone"
local devicons = require "plugins.nvim-tree"
local whichKey = require "plugins.which-key"
local conform = require "plugins.conform"
local mason = require "plugins.mason"
local autocomplete = require "plugins.autocomplete"
local indent = require "plugins.indent"
local gitsigns = require "plugins.gitsigns"
local telescope = require "plugins.telescope"
local treesitter = require "plugins.treesitter"
local snacks = require "plugins.snacks"
local editor = require "plugins.editor"
local ai = require "plugins.ai"
local yanky = require "plugins.yanky"
local journal = require "plugins.journal"
local ssh = require "plugins.ssh"
-- local codeium = require "plugins.codeium"
-- load plugins
-- When true, the plugin will only be loaded when needed. Lazy-loaded plugins are automatically loaded when their Lua modules are required, or when one of the lazy-loading handlers triggers
require("lazy").setup({
  { theme_config },
  { plenary_config },
  { nvzone },
  { devicons },
  { whichKey },
  { conform },
  { mason },
  { autocomplete },
  { indent },
  { gitsigns },
  { telescope },
  { treesitter },
  { snacks },
  { ai },
  { yanky },
  { journal },
  { editor },
  { ssh },
  -- { codeium }
}, lazy_config)
-- load theme
-- executes lua files generated by catppuccin
dofile(vim.g.theme_cache .. "macchiato")
dofile(vim.g.theme_cache .. "frappe")
dofile(vim.g.theme_cache .. "latte")
dofile(vim.g.theme_cache .. "mocha")

-- core configurations
require "options"
require "autocmds"

vim.schedule(function()
  require "mappings"
end)

require "configs.noice"
require "configs.editor"
require "configs.ai"
require "configs.lualine"
require "configs.tabby"
require "configs.incline"
-- require('mini.indentscope').setup(
--   {
--   mappings = {
--     -- Textobjects
--     object_scope = '<leader>xs',
--     object_scope_with_border = '<leader>xS',

--     -- Motions (jump to respective border line; if not present - body line)
--     goto_top = '<leader>[S',
--     goto_bottom = '<leader>]S',
--   },
-- }
-- )
-- require("toggleterm").setup{}
-- require'navigator'.setup()
require("configs.ssh")
-- require("configs.nvimtree")
-- require("nvim-tree").setup(require("configs.nvimtree"))
require('ufo').setup({
  provider_selector = function(bufnr, filetype, buftype)
    return ''
  end
})
-- require("notify").setup({
--   timeout = 500,
--   stages = "static"
-- })
require("nvim-lightbulb").setup({
  autocmd = { enabled = true }
})
