{"Comment.nvim": {"branch": "master", "commit": "0236521ea582747b58869cb72f70ccfa967d2e89"}, "LuaSnip": {"branch": "master", "commit": "1def35377854535bb3b0f4cc7a33c083cdb12571"}, "alpha-nvim": {"branch": "main", "commit": "29074eeb869a6cbac9ce1fbbd04f5f5940311b32"}, "bigfile.nvim": {"branch": "main", "commit": "33eb067e3d7029ac77e081cfe7c45361887a311a"}, "bufferline.nvim": {"branch": "main", "commit": "73540cb95f8d95aa1af3ed57713c6720c78af915"}, "cmp-buffer": {"branch": "main", "commit": "3022dbc9166796b644a841a02de8dd1cc1d311fa"}, "cmp-nvim-lsp": {"branch": "main", "commit": "5af77f54de1b16c34b23cba810150689a3a90312"}, "cmp-path": {"branch": "main", "commit": "91ff86cd9c29299a64f968ebb45846c485725f23"}, "cmp_luasnip": {"branch": "master", "commit": "05a9ab28b53f71d1aece421ef32fee2cb857a843"}, "friendly-snippets": {"branch": "main", "commit": "3e9a3f5a0cfcef1741e352c37bda4e82e5eb846a"}, "gitsigns.nvim": {"branch": "main", "commit": "805610a9393fa231f2c2b49cb521bfa413fadb3d"}, "indent-blankline.nvim": {"branch": "master", "commit": "9637670896b68805430e2f72cf5d16be5b97a22a"}, "lazy.nvim": {"branch": "main", "commit": "bef521ac89c8d423f9d092e37b58e8af0c099309"}, "lir.nvim": {"branch": "master", "commit": "7a9d45de08fecd23a04aca1f96688d744830029e"}, "lualine.nvim": {"branch": "master", "commit": "0a5a66803c7407767b799067986b4dc3036e1983"}, "lunar.nvim": {"branch": "master", "commit": "08bbc93b96ad698d22fc2aa01805786bcedc34b9"}, "mason-lspconfig.nvim": {"branch": "main", "commit": "273fdde8ac5e51f3a223ba70980e52bbc09d9f6f"}, "mason.nvim": {"branch": "main", "commit": "751b1fcbf3d3b783fcf8d48865264a9bcd8f9b10"}, "neodev.nvim": {"branch": "main", "commit": "ce9a2e8eaba5649b553529c5498acb43a6c317cd"}, "nlsp-settings.nvim": {"branch": "main", "commit": "707b43110daf27c1aec8c16c2a92c2cb9a06f8df"}, "none-ls.nvim": {"branch": "main", "commit": "3a4826687da4310af379515086d71faca4d21288"}, "nvim-autopairs": {"branch": "master", "commit": "14e97371b2aab6ee70054c1070a123dfaa3e217e"}, "nvim-cmp": {"branch": "main", "commit": "cd2cf0c124d3de577fb5449746568ee8e601afc8"}, "nvim-dap": {"branch": "master", "commit": "13ce59d4852be2bb3cd4967947985cb0ceaff460"}, "nvim-dap-ui": {"branch": "master", "commit": "34160a7ce6072ef332f350ae1d4a6a501daf0159"}, "nvim-lspconfig": {"branch": "master", "commit": "aa5f4f4ee10b2688fb37fa46215672441d5cd5d9"}, "nvim-navic": {"branch": "master", "commit": "8649f694d3e76ee10c19255dece6411c29206a54"}, "nvim-tree.lua": {"branch": "master", "commit": "64f61e4c913047a045ff90bd188dd3b54ee443cf"}, "nvim-treesitter": {"branch": "master", "commit": "d5a1c2b0c8ec5bb377a41c1c414b315d6b3e9432"}, "nvim-ts-context-commentstring": {"branch": "main", "commit": "0bdccb9c67a42a5e2d99384dc9bfa29b1451528f"}, "nvim-web-devicons": {"branch": "master", "commit": "5b9067899ee6a2538891573500e8fd6ff008440f"}, "onedarker.nvim": {"branch": "freeze", "commit": "b00dd2189f264c5aeb4cf04c59439655ecd573ec"}, "plenary.nvim": {"branch": "master", "commit": "08e301982b9a057110ede7a735dd1b5285eb341f"}, "project.nvim": {"branch": "main", "commit": "8c6bad7d22eef1b71144b401c9f74ed01526a4fb"}, "schemastore.nvim": {"branch": "main", "commit": "8c46453bdf84ad91877effb95e0b6c7b51ea7dda"}, "structlog.nvim": {"branch": "main", "commit": "45b26a2b1036bb93c0e83f4225e85ab3cee8f476"}, "telescope-fzf-native.nvim": {"branch": "main", "commit": "9ef21b2e6bb6ebeaf349a0781745549bbb870d27"}, "telescope.nvim": {"branch": "0.1.x", "commit": "6312868392331c9c0f22725041f1ec2bef57c751"}, "toggleterm.nvim": {"branch": "main", "commit": "066cccf48a43553a80a210eb3be89a15d789d6e6"}, "tokyonight.nvim": {"branch": "main", "commit": "67afeaf7fd6ebba000633e89f63c31694057edde"}, "vim-illuminate": {"branch": "master", "commit": "e522e0dd742a83506db0a72e1ced68c9c130f185"}, "vim-test": {"branch": "master", "commit": "b0c3e13249699a522c8b472ff79eff40d2935476"}, "vim-tmux-navigator": {"branch": "master", "commit": "33afa80db65113561dc53fa732b7f5e53d5ecfd0"}, "vimux": {"branch": "master", "commit": "7db6b2f79d432ee3820668b1d4625311dbe1d0ad"}, "which-key.nvim": {"branch": "main", "commit": "4433e5ec9a507e5097571ed55c02ea9658fb268a"}}