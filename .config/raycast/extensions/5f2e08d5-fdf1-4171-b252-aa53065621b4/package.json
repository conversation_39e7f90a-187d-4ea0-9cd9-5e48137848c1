{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "notion", "title": "Notion", "description": "The fastest way to search, create and update Notion pages.", "icon": "notion-logo.png", "author": "<PERSON><PERSON><PERSON><PERSON>", "owner": "notion", "access": "public", "license": "MIT", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "metakirby5", "thoma<PERSON><PERSON><PERSON><PERSON>", "erics118", "bkeys818", "alexs", "yakitrak", "tleo19", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zsidnam", "edomora97", "samuelkraft"], "categories": ["Applications", "Productivity"], "commands": [{"name": "create-database-page", "title": "Create Database Page", "subtitle": "Notion", "description": "Create a page in a Notion database.", "mode": "view", "preferences": [{"name": "closeAfterCreate", "type": "checkbox", "label": "Close Raycast after creating the page", "required": false, "default": false, "description": "This action will be set as the primary action (⌘ + ⏎)."}]}, {"name": "search-page", "title": "Search Notion", "description": "Search for Notion databases and pages.", "mode": "view", "preferences": [{"name": "primaryAction", "type": "dropdown", "title": "Primary Action", "required": false, "default": "notion", "data": [{"title": "Open in Notion", "value": "notion"}, {"title": "Preview in Raycast", "value": "raycast"}], "description": "Choose the primary action for the Notion Pages list."}]}, {"name": "quick-capture", "title": "Quick Capture", "description": "Capture something and put it out in a Notion page.", "mode": "view"}, {"description": "Append or prepend text, along with date, to a Notion page.", "mode": "view", "name": "add-text-to-page", "subtitle": "Notion", "title": "Add Text to Page", "arguments": [{"name": "text", "type": "text", "title": "Text", "placeholder": "Text", "required": false}]}], "tools": [{"name": "search-pages", "title": "Search Pages", "description": "Search for pages in Notion.", "input": {"type": "object", "properties": {"searchText": {"type": "string", "description": "The title of the page to search for. Only use plain text: it doesn't support any operators"}}, "required": ["searchText"]}, "confirmation": false}, {"name": "get-page", "title": "Get <PERSON>", "description": "Get the content of a Notion page.", "input": {"type": "object", "properties": {"pageId": {"type": "string", "description": "The ID of the Notion page to fetch"}}, "required": ["pageId"]}, "confirmation": false}, {"name": "add-to-page", "title": "Add to Page", "description": "Append markdown to a Notion page.", "input": {"type": "object", "properties": {"pageId": {"type": "string", "description": "The ID of the page to append the content to."}, "content": {"type": "string", "description": "The content in markdown format to append to the page."}}, "required": ["pageId", "content"]}, "confirmation": true}, {"name": "create-page", "title": "Create Page", "description": "Create a Notion page in a specific database.", "input": {"type": "object", "properties": {"databaseId": {"type": "string", "description": "The database id to create the page in."}, "title": {"type": "string", "description": "The title of the page to create."}, "content": {"type": "string", "description": "Parses Markdown to Notion Blocks.\n\nIt supports:\n- Headings (levels 4 to 6 are treated as 3 on Notion)\n- Numbered, bulleted, and to-do lists\n- Code blocks, block quotes, and tables\n- Text formatting; italics, bold, strikethrough, inline code, hyperlinks\n\nPlease note that HTML tags and thematic breaks are not supported in Notion due to its limitations."}}, "required": ["databaseId", "title", "content"]}, "confirmation": true}, {"name": "get-databases", "title": "Get Databases", "description": "Get Notion databases.", "confirmation": false}, {"name": "search-database", "title": "Search Database", "description": "Search for pages and/or databases contained in the given Notion database.", "input": {"type": "object", "properties": {"databaseId": {"type": "string", "description": "The ID of the database to search."}, "query": {"type": "string", "description": "The query to search for. Only use plain text: it doesn't support any operators"}}, "required": ["databaseId", "query"]}, "confirmation": false}], "ai": {"instructions": "-If asked to summarize or view a document, look for pages with the title of the document.\\n - When referring to a page or database, always include a link to the page or database. \\n - When creating a page, always include the database name. Ask for the database name if it's not provided.", "evals": [{"input": "@notion Summarize the last marketing meeting notes", "mocks": {"search-pages": [{"id": "12345678-a123-12ab-a1b2-1234567a12ab", "title": "Marketing Meeting Notes", "url": "https://www.notion.so/12345678a12312aba1b21234567a12ab"}], "get-page": {"markdown": "\n## Marketing meeting notes\n Publish tweets about new launch"}}, "expected": [{"callsTool": {"name": "search-pages"}}, {"callsTool": {"name": "get-page", "arguments": {"pageId": "12345678-a123-12ab-a1b2-1234567a12ab"}}}]}, {"input": "@notion Add \"remember to call <PERSON>\" to \"meeting notes\"", "mocks": {"search-pages": [{"id": "123abc", "title": "Marketing Meeting Notes", "url": "https://www.notion.so/Marketing-Meeting-Notes-123abc"}], "add-to-page": {"markdown": "\n\nremember to call joe"}}, "expected": [{"callsTool": {"name": "search-pages"}}, {"callsTool": {"arguments": {"pageId": "123abc"}, "name": "add-to-page"}}]}, {"input": "@notion Create page in \"Marketing\" with the title \"Meeting Notes\" saying: \"Publish tweets about new launch\"", "mocks": {"get-databases": [{"id": "123abc", "title": "Marketing"}], "create-page": {"id": "123abc123abc"}}, "expected": [{"callsTool": {"name": "get-databases"}}, {"callsTool": {"arguments": {"databaseId": "123abc"}, "name": "create-page"}}]}]}, "preferences": [{"name": "notion_token", "type": "password", "title": "Internal Integration Secret", "required": false, "description": "In Notion, go to Settings & members > My connections > Develop or manage integrations > New integration", "placeholder": "secret_FGDeSrZNodQuaJEqWzvxcTyrPlpBHYvdLpTZykrWEu"}, {"name": "open_in", "type": "appPicker", "title": "Open Page in", "required": false, "default": "Notion", "description": "Choose where to open Notion page."}], "dependencies": {"@mozilla/readability": "^0.5.0", "@notionhq/client": "^2.2.15", "@raycast/api": "^1.71.3", "@raycast/utils": "^1.18.1", "@tryfabric/martian": "^1.2.4", "date-fns": "^3.3.1", "linkedom": "^0.16.8", "node-fetch": "^3.3.2", "notion-to-md": "^3.1.1"}, "devDependencies": {"@raycast/eslint-config": "^1.0.8", "@types/node": "20.11.20", "@types/react": "18.2.58", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "prettier": "^3.2.5", "typescript": "^5.3.3"}, "scripts": {"build": "ray build -e dist", "dev": "ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "publish": "npx @raycast/api@latest publish"}}