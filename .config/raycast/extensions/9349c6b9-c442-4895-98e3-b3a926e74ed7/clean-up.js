"use strict";var xe=Object.create;var w=Object.defineProperty;var we=Object.getOwnPropertyDescriptor;var ve=Object.getOwnPropertyNames;var Se=Object.getPrototypeOf,Ce=Object.prototype.hasOwnProperty;var c=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),Pe=(r,e)=>{for(var s in e)w(r,s,{get:e[s],enumerable:!0})},$=(r,e,s,t)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of ve(e))!Ce.call(r,n)&&n!==s&&w(r,n,{get:()=>e[n],enumerable:!(t=we(e,n))||t.enumerable});return r};var b=(r,e,s)=>(s=r!=null?xe(Se(r)):{},$(e||!r||!r.__esModule?w(s,"default",{value:r,enumerable:!0}):s,r)),Ee=r=>$(w({},"__esModule",{value:!0}),r);var A=c((vt,U)=>{"use strict";var{Readable:xt,Writable:wt,Duplex:Ne,Transform:B}=require("stream"),Ae=Symbol.for("object-stream.none"),E=Symbol.for("object-stream.final"),N=Symbol.for("object-stream.many"),Fe=r=>({[E]:r}),Ve=r=>({[N]:r}),Oe=r=>r&&typeof r=="object"&&E in r,Te=r=>r&&typeof r=="object"&&N in r,je=r=>r[E],Ie=r=>r[N],Re=async(r,e)=>{for(;;){let s=r.next();if(s&&typeof s.then=="function"&&(s=await s),s.done)break;let t=s.value;t&&typeof t.then=="function"&&(t=await t),o.sanitize(t,e)}},Ke=r=>new B({writableObjectMode:!0,readableObjectMode:!0,transform(e,s,t){try{let n=r.call(this,e,s);if(n&&typeof n.then=="function"){n.then(a=>(o.sanitize(a,this),t(null)),a=>t(a));return}if(n&&typeof n.next=="function"){Re(n,this).then(()=>t(null),a=>t(a));return}o.sanitize(n,this),t(null)}catch(n){t(n)}}}),Me=r=>new B({writableObjectMode:!0,readableObjectMode:!0,transform(e,s,t){try{let n=e;for(let a=0;a<r.length;++a){let h=r[a].call(this,n,s);if(h===o.none){t(null);return}if(o.isFinal(h)){n=o.getFinalValue(h);break}n=h}o.sanitize(n,this),t(null)}catch(n){t(n)}}}),L=r=>r&&typeof r.pipe=="function"&&typeof r.on=="function"&&(!r._writableState||(typeof r._readableState=="object"?r._readableState.readable:null)!==!1)&&(!r._writableState||r._readableState),q=r=>r&&typeof r.write=="function"&&typeof r.on=="function"&&(!r._readableState||(typeof r._writableState=="object"?r._writableState.writable:null)!==!1),De=r=>r&&typeof r.pipe=="function"&&r._readableState&&typeof r.on=="function"&&typeof r.write=="function",o=class r extends Ne{constructor(e,s){if(super(s||{writableObjectMode:!0,readableObjectMode:!0}),!(e instanceof Array)||!e.length)throw Error("Chain's argument should be a non-empty array.");this.streams=e.filter(t=>t).map((t,n,a)=>{if(typeof t=="function"||t instanceof Array)return r.convertToTransform(t);if(De(t)||!n&&L(t)||n===a.length-1&&q(t))return t;throw Error("Arguments should be functions, arrays or streams.")}).filter(t=>t),this.input=this.streams[0],this.output=this.streams.reduce((t,n)=>t&&t.pipe(n)||n),q(this.input)||(this._write=(t,n,a)=>a(null),this._final=t=>t(null),this.input.on("end",()=>this.end())),L(this.output)?(this.output.on("data",t=>!this.push(t)&&this.output.pause()),this.output.on("end",()=>this.push(null))):(this._read=()=>{},this.resume(),this.output.on("finish",()=>this.push(null))),(!s||!s.skipEvents)&&this.streams.forEach(t=>t.on("error",n=>this.emit("error",n)))}_write(e,s,t){let n=null;try{this.input.write(e,s,a=>t(a||n))}catch(a){n=a}}_final(e){let s=null;try{this.input.end(null,null,t=>e(t||s))}catch(t){s=t}}_read(){this.output.resume()}static make(e,s){return new r(e,s)}static sanitize(e,s){r.isFinal(e)?e=r.getFinalValue(e):r.isMany(e)&&(e=r.getManyValues(e)),e!=null&&e!==r.none&&(e instanceof Array?e.forEach(t=>t!=null&&s.push(t)):s.push(e))}static convertToTransform(e){return typeof e=="function"?Ke(e):e instanceof Array&&e.length?Me(e):null}};o.none=Ae;o.final=Fe;o.isFinal=Oe;o.getFinalValue=je;o.many=Ve;o.isMany=Te;o.getManyValues=Ie;o.chain=o.make;o.make.Constructor=o;U.exports=o});var W=c((St,z)=>{"use strict";var{Transform:$e}=require("stream"),{StringDecoder:Le}=require("string_decoder"),F=class extends $e{constructor(e){super(Object.assign({},e,{writableObjectMode:!1})),this._buffer=""}_transform(e,s,t){typeof e=="string"?this._transform=this._transformString:(this._stringDecoder=new Le,this._transform=this._transformBuffer),this._transform(e,s,t)}_transformBuffer(e,s,t){this._buffer+=this._stringDecoder.write(e),this._processBuffer(t)}_transformString(e,s,t){this._buffer+=e.toString(),this._processBuffer(t)}_processBuffer(e){this._buffer&&(this.push(this._buffer,"utf8"),this._buffer=""),e(null)}_flushInput(){this._stringDecoder&&(this._buffer+=this._stringDecoder.end())}_flush(e){this._flushInput(),this._processBuffer(e)}};z.exports=F});var V=c((Ct,J)=>{"use strict";var qe=W(),i={value1:/^(?:[\"\{\[\]\-\d]|true\b|false\b|null\b|\s{1,256})/,string:/^(?:[^\x00-\x1f\"\\]{1,256}|\\[bfnrt\"\\\/]|\\u[\da-fA-F]{4}|\")/,key1:/^(?:[\"\}]|\s{1,256})/,colon:/^(?:\:|\s{1,256})/,comma:/^(?:[\,\]\}]|\s{1,256})/,ws:/^\s{1,256}/,numberStart:/^\d/,numberDigit:/^\d{0,256}/,numberFraction:/^[\.eE]/,numberExponent:/^[eE]/,numberExpSign:/^[-+]/},Be=16,u=!0;try{new RegExp(".","y"),u=!1}catch{}!u&&Object.keys(i).forEach(r=>{let e=i[r].source.slice(1);e.slice(0,3)==="(?:"&&e.slice(-1)===")"&&(e=e.slice(3,-1)),i[r]=new RegExp(e,"y")});i.numberFracStart=i.numberExpStart=i.numberStart;i.numberFracDigit=i.numberExpDigit=i.numberDigit;var Ue={true:!0,false:!1,null:null},f={object:"objectStop",array:"arrayStop","":"done"},ze=r=>String.fromCharCode(parseInt(r.slice(2),16)),We={b:"\b",f:"\f",n:`
`,r:"\r",t:"	",'"':'"',"\\":"\\","/":"/"},d=class r extends qe{static make(e){return new r(e)}constructor(e){super(Object.assign({},e,{readableObjectMode:!0})),this._packKeys=this._packStrings=this._packNumbers=this._streamKeys=this._streamStrings=this._streamNumbers=!0,e&&("packValues"in e&&(this._packKeys=this._packStrings=this._packNumbers=e.packValues),"packKeys"in e&&(this._packKeys=e.packKeys),"packStrings"in e&&(this._packStrings=e.packStrings),"packNumbers"in e&&(this._packNumbers=e.packNumbers),"streamValues"in e&&(this._streamKeys=this._streamStrings=this._streamNumbers=e.streamValues),"streamKeys"in e&&(this._streamKeys=e.streamKeys),"streamStrings"in e&&(this._streamStrings=e.streamStrings),"streamNumbers"in e&&(this._streamNumbers=e.streamNumbers),this._jsonStreaming=e.jsonStreaming),!this._packKeys&&(this._streamKeys=!0),!this._packStrings&&(this._streamStrings=!0),!this._packNumbers&&(this._streamNumbers=!0),this._done=!1,this._expect=this._jsonStreaming?"done":"value",this._stack=[],this._parent="",this._open_number=!1,this._accumulator=""}_flush(e){this._done=!0,super._flush(s=>{if(s)return e(s);this._open_number&&(this._streamNumbers&&this.push({name:"endNumber"}),this._open_number=!1,this._packNumbers&&(this.push({name:"numberValue",value:this._accumulator}),this._accumulator="")),e(null)})}_processBuffer(e){let s,t,n=0;e:for(;;)switch(this._expect){case"value1":case"value":if(i.value1.lastIndex=n,s=i.value1.exec(this._buffer),!s){if(this._done||n+Be<this._buffer.length)return n<this._buffer.length?e(new Error("Parser cannot parse input: expected a value")):e(new Error("Parser has expected a value"));break e}switch(t=s[0],t){case'"':this._streamStrings&&this.push({name:"startString"}),this._expect="string";break;case"{":this.push({name:"startObject"}),this._stack.push(this._parent),this._parent="object",this._expect="key1";break;case"[":this.push({name:"startArray"}),this._stack.push(this._parent),this._parent="array",this._expect="value1";break;case"]":if(this._expect!=="value1")return e(new Error("Parser cannot parse input: unexpected token ']'"));this._open_number&&(this._streamNumbers&&this.push({name:"endNumber"}),this._open_number=!1,this._packNumbers&&(this.push({name:"numberValue",value:this._accumulator}),this._accumulator="")),this.push({name:"endArray"}),this._parent=this._stack.pop(),this._expect=f[this._parent];break;case"-":this._open_number=!0,this._streamNumbers&&(this.push({name:"startNumber"}),this.push({name:"numberChunk",value:"-"})),this._packNumbers&&(this._accumulator="-"),this._expect="numberStart";break;case"0":this._open_number=!0,this._streamNumbers&&(this.push({name:"startNumber"}),this.push({name:"numberChunk",value:"0"})),this._packNumbers&&(this._accumulator="0"),this._expect="numberFraction";break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":this._open_number=!0,this._streamNumbers&&(this.push({name:"startNumber"}),this.push({name:"numberChunk",value:t})),this._packNumbers&&(this._accumulator=t),this._expect="numberDigit";break;case"true":case"false":case"null":if(this._buffer.length-n===t.length&&!this._done)break e;this.push({name:t+"Value",value:Ue[t]}),this._expect=f[this._parent];break}u?this._buffer=this._buffer.slice(t.length):n+=t.length;break;case"keyVal":case"string":if(i.string.lastIndex=n,s=i.string.exec(this._buffer),!s){if(n<this._buffer.length&&(this._done||this._buffer.length-n>=6))return e(new Error("Parser cannot parse input: escaped characters"));if(this._done)return e(new Error("Parser has expected a string value"));break e}if(t=s[0],t==='"')this._expect==="keyVal"?(this._streamKeys&&this.push({name:"endKey"}),this._packKeys&&(this.push({name:"keyValue",value:this._accumulator}),this._accumulator=""),this._expect="colon"):(this._streamStrings&&this.push({name:"endString"}),this._packStrings&&(this.push({name:"stringValue",value:this._accumulator}),this._accumulator=""),this._expect=f[this._parent]);else if(t.length>1&&t.charAt(0)==="\\"){let a=t.length==2?We[t.charAt(1)]:ze(t);(this._expect==="keyVal"?this._streamKeys:this._streamStrings)&&this.push({name:"stringChunk",value:a}),(this._expect==="keyVal"?this._packKeys:this._packStrings)&&(this._accumulator+=a)}else(this._expect==="keyVal"?this._streamKeys:this._streamStrings)&&this.push({name:"stringChunk",value:t}),(this._expect==="keyVal"?this._packKeys:this._packStrings)&&(this._accumulator+=t);u?this._buffer=this._buffer.slice(t.length):n+=t.length;break;case"key1":case"key":if(i.key1.lastIndex=n,s=i.key1.exec(this._buffer),!s){if(n<this._buffer.length||this._done)return e(new Error("Parser cannot parse input: expected an object key"));break e}if(t=s[0],t==='"')this._streamKeys&&this.push({name:"startKey"}),this._expect="keyVal";else if(t==="}"){if(this._expect!=="key1")return e(new Error("Parser cannot parse input: unexpected token '}'"));this.push({name:"endObject"}),this._parent=this._stack.pop(),this._expect=f[this._parent]}u?this._buffer=this._buffer.slice(t.length):n+=t.length;break;case"colon":if(i.colon.lastIndex=n,s=i.colon.exec(this._buffer),!s){if(n<this._buffer.length||this._done)return e(new Error("Parser cannot parse input: expected ':'"));break e}t=s[0],t===":"&&(this._expect="value"),u?this._buffer=this._buffer.slice(t.length):n+=t.length;break;case"arrayStop":case"objectStop":if(i.comma.lastIndex=n,s=i.comma.exec(this._buffer),!s){if(n<this._buffer.length||this._done)return e(new Error("Parser cannot parse input: expected ','"));break e}if(this._open_number&&(this._streamNumbers&&this.push({name:"endNumber"}),this._open_number=!1,this._packNumbers&&(this.push({name:"numberValue",value:this._accumulator}),this._accumulator="")),t=s[0],t===",")this._expect=this._expect==="arrayStop"?"value":"key";else if(t==="}"||t==="]"){if(t==="}"?this._expect==="arrayStop":this._expect!=="arrayStop")return e(new Error("Parser cannot parse input: expected '"+(this._expect==="arrayStop"?"]":"}")+"'"));this.push({name:t==="}"?"endObject":"endArray"}),this._parent=this._stack.pop(),this._expect=f[this._parent]}u?this._buffer=this._buffer.slice(t.length):n+=t.length;break;case"numberStart":if(i.numberStart.lastIndex=n,s=i.numberStart.exec(this._buffer),!s){if(n<this._buffer.length||this._done)return e(new Error("Parser cannot parse input: expected a starting digit"));break e}t=s[0],this._streamNumbers&&this.push({name:"numberChunk",value:t}),this._packNumbers&&(this._accumulator+=t),this._expect=t==="0"?"numberFraction":"numberDigit",u?this._buffer=this._buffer.slice(t.length):n+=t.length;break;case"numberDigit":if(i.numberDigit.lastIndex=n,s=i.numberDigit.exec(this._buffer),!s){if(n<this._buffer.length||this._done)return e(new Error("Parser cannot parse input: expected a digit"));break e}if(t=s[0],t)this._streamNumbers&&this.push({name:"numberChunk",value:t}),this._packNumbers&&(this._accumulator+=t),u?this._buffer=this._buffer.slice(t.length):n+=t.length;else{if(n<this._buffer.length){this._expect="numberFraction";break}if(this._done){this._expect=f[this._parent];break}break e}break;case"numberFraction":if(i.numberFraction.lastIndex=n,s=i.numberFraction.exec(this._buffer),!s){if(n<this._buffer.length||this._done){this._expect=f[this._parent];break}break e}t=s[0],this._streamNumbers&&this.push({name:"numberChunk",value:t}),this._packNumbers&&(this._accumulator+=t),this._expect=t==="."?"numberFracStart":"numberExpSign",u?this._buffer=this._buffer.slice(t.length):n+=t.length;break;case"numberFracStart":if(i.numberFracStart.lastIndex=n,s=i.numberFracStart.exec(this._buffer),!s){if(n<this._buffer.length||this._done)return e(new Error("Parser cannot parse input: expected a fractional part of a number"));break e}t=s[0],this._streamNumbers&&this.push({name:"numberChunk",value:t}),this._packNumbers&&(this._accumulator+=t),this._expect="numberFracDigit",u?this._buffer=this._buffer.slice(t.length):n+=t.length;break;case"numberFracDigit":if(i.numberFracDigit.lastIndex=n,s=i.numberFracDigit.exec(this._buffer),t=s[0],t)this._streamNumbers&&this.push({name:"numberChunk",value:t}),this._packNumbers&&(this._accumulator+=t),u?this._buffer=this._buffer.slice(t.length):n+=t.length;else{if(n<this._buffer.length){this._expect="numberExponent";break}if(this._done){this._expect=f[this._parent];break}break e}break;case"numberExponent":if(i.numberExponent.lastIndex=n,s=i.numberExponent.exec(this._buffer),!s){if(n<this._buffer.length){this._expect=f[this._parent];break}if(this._done){this._expect="done";break}break e}t=s[0],this._streamNumbers&&this.push({name:"numberChunk",value:t}),this._packNumbers&&(this._accumulator+=t),this._expect="numberExpSign",u?this._buffer=this._buffer.slice(t.length):n+=t.length;break;case"numberExpSign":if(i.numberExpSign.lastIndex=n,s=i.numberExpSign.exec(this._buffer),!s){if(n<this._buffer.length){this._expect="numberExpStart";break}if(this._done)return e(new Error("Parser has expected an exponent value of a number"));break e}t=s[0],this._streamNumbers&&this.push({name:"numberChunk",value:t}),this._packNumbers&&(this._accumulator+=t),this._expect="numberExpStart",u?this._buffer=this._buffer.slice(t.length):n+=t.length;break;case"numberExpStart":if(i.numberExpStart.lastIndex=n,s=i.numberExpStart.exec(this._buffer),!s){if(n<this._buffer.length||this._done)return e(new Error("Parser cannot parse input: expected an exponent part of a number"));break e}t=s[0],this._streamNumbers&&this.push({name:"numberChunk",value:t}),this._packNumbers&&(this._accumulator+=t),this._expect="numberExpDigit",u?this._buffer=this._buffer.slice(t.length):n+=t.length;break;case"numberExpDigit":if(i.numberExpDigit.lastIndex=n,s=i.numberExpDigit.exec(this._buffer),t=s[0],t)this._streamNumbers&&this.push({name:"numberChunk",value:t}),this._packNumbers&&(this._accumulator+=t),u?this._buffer=this._buffer.slice(t.length):n+=t.length;else{if(n<this._buffer.length||this._done){this._expect=f[this._parent];break}break e}break;case"done":if(i.ws.lastIndex=n,s=i.ws.exec(this._buffer),!s){if(n<this._buffer.length){if(this._jsonStreaming){this._expect="value";break}return e(new Error("Parser cannot parse input: unexpected characters"))}break e}t=s[0],this._open_number&&(this._streamNumbers&&this.push({name:"endNumber"}),this._open_number=!1,this._packNumbers&&(this.push({name:"numberValue",value:this._accumulator}),this._accumulator="")),u?this._buffer=this._buffer.slice(t.length):n+=t.length;break}!u&&(this._buffer=this._buffer.slice(n)),e(null)}};d.parser=d.make;d.make.Constructor=d;J.exports=d});var Q=c((Pt,H)=>{"use strict";var Je=r=>r.on("data",e=>r.emit(e.name,e.value));H.exports=Je});var G=c((Et,X)=>{"use strict";var O=V(),He=Q(),T=r=>He(new O(r));T.Parser=O;T.parser=O.parser;X.exports=T});var Y=c((Nt,Z)=>{"use strict";var{Transform:Qe}=require("stream"),m=class r extends Qe{static stringFilter(e,s){return t=>{let n=t.join(s);return n.length===e.length&&n===e||n.length>e.length&&n.substr(0,e.length)===e&&n.substr(e.length,s.length)===s}}static regExpFilter(e,s){return t=>e.test(t.join(s))}static arrayReplacement(e){return()=>e}constructor(e){super(Object.assign({},e,{writableObjectMode:!0,readableObjectMode:!0})),this._transform=this._check,this._stack=[];let s=e&&e.filter,t=e&&e.pathSeparator||".";typeof s=="string"?this._filter=r.stringFilter(s,t):typeof s=="function"?this._filter=s:s instanceof RegExp&&(this._filter=r.regExpFilter(s,t));let n=e&&e.replacement;typeof n=="function"?this._replacement=n:this._replacement=r.arrayReplacement(n||r.defaultReplacement),this._allowEmptyReplacement=e&&e.allowEmptyReplacement,this._streamKeys=!0,e&&("streamValues"in e&&(this._streamKeys=e.streamValues),"streamKeys"in e&&(this._streamKeys=e.streamKeys)),this._once=e&&e.once,this._previousToken=""}_check(e,s,t){switch(e.name){case"startObject":case"startArray":case"startString":case"startNumber":case"nullValue":case"trueValue":case"falseValue":typeof this._stack[this._stack.length-1]=="number"&&++this._stack[this._stack.length-1];break;case"keyValue":this._stack[this._stack.length-1]=e.value;break;case"numberValue":this._previousToken!=="endNumber"&&typeof this._stack[this._stack.length-1]=="number"&&++this._stack[this._stack.length-1];break;case"stringValue":this._previousToken!=="endString"&&typeof this._stack[this._stack.length-1]=="number"&&++this._stack[this._stack.length-1];break}if(this._previousToken=e.name,this._checkChunk(e))return t(null);switch(e.name){case"startObject":this._stack.push(null);break;case"startArray":this._stack.push(-1);break;case"endObject":case"endArray":this._stack.pop();break}t(null)}_passObject(e,s,t){switch(this.push(e),e.name){case"startObject":case"startArray":++this._depth;break;case"endObject":case"endArray":--this._depth;break}this._depth||(this._transform=this._once?this._skip:this._check),t(null)}_pass(e,s,t){this.push(e),t(null)}_skipObject(e,s,t){switch(e.name){case"startObject":case"startArray":++this._depth;break;case"endObject":case"endArray":--this._depth;break}this._depth||(this._transform=this._once?this._pass:this._check),t(null)}_skip(e,s,t){t(null)}};m.defaultReplacement=[{name:"nullValue",value:null}];var j=(r,e)=>function(s,t,n){if(this._expected){let a=this._expected;if(this._expected="",this._transform=this._once?this._skip:this._check,a===s.name)this.push(s);else return this._transform(s,t,n)}else this.push(s),s.name===r&&(this._expected=e);n(null)};m.prototype._passNumber=j("endNumber","numberValue");m.prototype._passString=j("endString","stringValue");m.prototype._passKey=j("endKey","keyValue");var I=(r,e)=>function(s,t,n){if(this._expected){let a=this._expected;if(this._expected="",this._transform=this._once?this._pass:this._check,a!==s.name)return this._transform(s,t,n)}else s.name===r&&(this._expected=e);n(null)};m.prototype._skipNumber=I("endNumber","numberValue");m.prototype._skipString=I("endString","stringValue");m.prototype._skipKey=I("endKey","keyValue");Z.exports=m});var R=c((At,ee)=>{"use strict";var{chain:Xe}=A(),Ge=V(),Ze=(r,e)=>Xe([new Ge(e),r(e)],Object.assign({},e,{writableObjectMode:!1,readableObjectMode:!0}));ee.exports=Ze});var se=c((Ft,te)=>{"use strict";var Ye=Y(),et=R(),g=class r extends Ye{static make(e){return new r(e)}static withParser(e){return et(r.make,e)}constructor(e){super(e),this._once=!1,this._lastStack=[]}_flush(e){this._syncStack(),e(null)}_checkChunk(e){switch(e.name){case"startObject":this._filter(this._stack,e)&&(this._syncStack(),this.push(e),this._lastStack.push(null));break;case"startArray":this._filter(this._stack,e)&&(this._syncStack(),this.push(e),this._lastStack.push(-1));break;case"nullValue":case"trueValue":case"falseValue":case"stringValue":case"numberValue":this._filter(this._stack,e)&&(this._syncStack(),this.push(e));break;case"startString":this._filter(this._stack,e)?(this._syncStack(),this.push(e),this._transform=this._passString):this._transform=this._skipString;break;case"startNumber":this._filter(this._stack,e)?(this._syncStack(),this.push(e),this._transform=this._passNumber):this._transform=this._skipNumber;break}return!1}_syncStack(){let e=this._stack,s=this._lastStack,t=e.length,n=s.length,a=0;for(let h=Math.min(t,n);a<h&&e[a]===s[a];++a);for(let h=n-1;h>a;--h)this.push({name:typeof s[h]=="number"?"endArray":"endObject"});if(a<n)if(a<t){if(typeof e[a]=="string"){let h=e[a];this._streamKeys&&(this.push({name:"startKey"}),this.push({name:"stringChunk",value:h}),this.push({name:"endKey"})),this.push({name:"keyValue",value:h})}++a}else this.push({name:typeof s[a]=="number"?"endArray":"endObject"});for(let h=a;h<t;++h){let x=e[h];typeof x=="number"?x>=0&&this.push({name:"startArray"}):typeof x=="string"&&(this.push({name:"startObject"}),this._streamKeys&&(this.push({name:"startKey"}),this.push({name:"stringChunk",value:x}),this.push({name:"endKey"})),this.push({name:"keyValue",value:x}))}this._lastStack=Array.prototype.concat.call(e)}};g.filter=g.make;g.make.Constructor=g;te.exports=g});var ie=c((Vt,ne)=>{"use strict";var tt=require("events"),re=r=>function(){this.done?this.done=!1:this.stack.push(this.current,this.key),this.current=new r,this.key=null},p=class r extends tt{static connectTo(e,s){return new r(s).connectTo(e)}constructor(e){super(),this.stack=[],this.current=this.key=null,this.done=!0,e&&(this.reviver=typeof e.reviver=="function"&&e.reviver,this.reviver&&(this.stringValue=this._saveValue=this._saveValueWithReviver),e.numberAsString&&(this.numberValue=this.stringValue))}connectTo(e){return e.on("data",s=>{this[s.name]&&(this[s.name](s.value),this.done&&this.emit("done",this))}),this}get depth(){return(this.stack.length>>1)+(this.done?0:1)}get path(){let e=[];for(let s=0;s<this.stack.length;s+=2){let t=this.stack[s+1];e.push(t===null?this.stack[s].length:t)}return e}dropToLevel(e){if(e<this.depth)if(e){let s=e-1<<1;this.current=this.stack[s],this.key=this.stack[s+1],this.stack.splice(s)}else this.stack=[],this.current=this.key=null,this.done=!0;return this}consume(e){return this[e.name]&&this[e.name](e.value),this}keyValue(e){this.key=e}numberValue(e){this._saveValue(parseFloat(e))}nullValue(){this._saveValue(null)}trueValue(){this._saveValue(!0)}falseValue(){this._saveValue(!1)}endObject(){if(this.stack.length){let e=this.current;this.key=this.stack.pop(),this.current=this.stack.pop(),this._saveValue(e)}else this.done=!0}_saveValue(e){this.done?this.current=e:this.current instanceof Array?this.current.push(e):(this.current[this.key]=e,this.key=null)}_saveValueWithReviver(e){this.done?this.current=this.reviver("",e):this.current instanceof Array?(e=this.reviver(""+this.current.length,e),this.current.push(e),e===void 0&&delete this.current[this.current.length-1]):(e=this.reviver(this.key,e),e!==void 0&&(this.current[this.key]=e),this.key=null)}};p.prototype.stringValue=p.prototype._saveValue;p.prototype.startObject=re(Object);p.prototype.startArray=re(Array);p.prototype.endArray=p.prototype.endObject;ne.exports=p});var oe=c((Ot,ae)=>{"use strict";var{Transform:st}=require("stream"),rt=ie(),K=class{constructor(e){this.depth=e}startObject(){++this.depth}endObject(){--this.depth}startArray(){++this.depth}endArray(){--this.depth}},M=class extends st{constructor(e){super(Object.assign({},e,{writableObjectMode:!0,readableObjectMode:!0})),e&&(this.objectFilter=e.objectFilter,this.includeUndecided=e.includeUndecided),typeof this.objectFilter!="function"&&(this._filter=this._transform),this._transform=this._wait||this._filter,this._assembler=new rt(e)}_transform(e,s,t){this._assembler[e.name]&&(this._assembler[e.name](e.value),this._assembler.depth===this._level&&this._push()),t(null)}_filter(e,s,t){if(this._assembler[e.name]){this._assembler[e.name](e.value);let n=this.objectFilter(this._assembler);if(n)return this._assembler.depth===this._level&&(this._push(),this._transform=this._filter),this._transform=this._accept,t(null);if(n===!1)return this._saved_assembler=this._assembler,this._assembler=new K(this._saved_assembler.depth),this._saved_assembler.dropToLevel(this._level),this._assembler.depth===this._level&&(this._assembler=this._saved_assembler,this._transform=this._filter),this._transform=this._reject,t(null);this._assembler.depth===this._level&&this._push(!this.includeUndecided)}t(null)}_accept(e,s,t){this._assembler[e.name]&&(this._assembler[e.name](e.value),this._assembler.depth===this._level&&(this._push(),this._transform=this._filter)),t(null)}_reject(e,s,t){this._assembler[e.name]&&(this._assembler[e.name](e.value),this._assembler.depth===this._level&&(this._assembler=this._saved_assembler,this._transform=this._filter)),t(null)}};ae.exports=M});var he=c((Tt,ue)=>{"use strict";var nt=oe(),it=R(),y=class r extends nt{static make(e){return new r(e)}static withParser(e){return it(r.make,e)}constructor(e){super(e),this._level=1,this._counter=0}_wait(e,s,t){return e.name!=="startArray"?t(new Error("Top-level object should be an array.")):(this._transform=this._filter,this._transform(e,s,t))}_push(e){this._assembler.current.length&&(e?(++this._counter,this._assembler.current.pop()):this.push({key:this._counter++,value:this._assembler.current.pop()}))}};y.streamArray=y.make;y.make.Constructor=y;ue.exports=y});var yt={};Pe(yt,{default:()=>gt});module.exports=Ee(yt);var P=require("@raycast/api");var S=require("child_process"),be=require("util"),de=require("fs"),k=b(require("fs/promises")),C=require("path"),ge=require("os"),ye=require("@raycast/api");var l=require("@raycast/api"),D=b(require("path")),ce=b(require("fs"));var at=b(A()),ot=b(G()),ut=b(se()),ht=b(he());var ct=(()=>{try{ce.default.mkdirSync(l.environment.supportPath,{recursive:!0})}catch{console.log("Failed to create supportPath")}return l.environment.supportPath})(),le=l.environment.supportPath.split(D.default.sep).find(r=>!!r.startsWith("com.raycast"))??"com.raycast.macos";function v(r){return D.default.join(ct,r)}async function fe(r){return new Promise(e=>setTimeout(e,r))}function me(r){let e={style:l.Toast.Style.Animated,title:r.title,message:r.message},s;r.cancelable&&(s=new AbortController,e.primaryAction={title:"Cancel",onAction:()=>{s?.abort(),t.hide()}});let t=new l.Toast(e);return t.show(),s}async function pe(r,e){if(e.name=="AbortError"){console.log("AbortError");return}console.log(`${r}: ${e}`);let s=e.stderr??e.message??`${e}`,t={style:l.Toast.Style.Failure,title:r,message:s,primaryAction:{title:"Copy Error Log",onAction:()=>{l.Clipboard.copy(s)}}};await new l.Toast(t).show()}Array.prototype.first||(Array.prototype.first=function(){return this.length>0?this[0]:void 0});Array.prototype.last||(Array.prototype.last=function(){return this.length>0?this[this.length-1]:void 0});Array.prototype.isTruncated||(Array.prototype.isTruncated=function(){return this.totalLength?this.length<this.totalLength:!1});String.ellipsis||(String.ellipsis="\u2026");var _e=require("@raycast/api"),_=(0,_e.getPreferenceValues)();var ft=(0,be.promisify)(S.exec),mt=(()=>{if(_.customBrewPath&&_.customBrewPath.length>0)return(0,C.join)(_.customBrewPath,"..","..");try{return(0,S.execSync)("brew --prefix",{encoding:"utf8"}).trim()}catch{return(0,ge.cpus)()[0].model.includes("Apple")?"/opt/homebrew":"/usr/local"}})(),pt=r=>(0,C.join)(mt,r),_t=()=>pt("bin/brew"),Kt=v("installedv2.json"),Mt=v("formula.json"),Dt=v("cask.json");async function ke(r,e){let s="cleanup";r&&(s+=" --prune=all"),await bt(s,e)}async function bt(r,e){try{let s=await dt();return await ft(`${_t()} ${r}`,{signal:e?.signal,env:s,maxBuffer:10*1024*1024})}catch(s){let t=s;throw _.customBrewPath&&t&&t.code===127?(t.stderr=`Brew executable not found at: ${_.customBrewPath}`,t):s}}async function dt(){let r=(0,C.join)(ye.environment.assetsPath,"askpass.sh");try{await k.access(r,de.constants.X_OK)}catch{await k.chmod(r,493)}let e=process.env;return e.SUDO_ASKPASS=r,e.HOMEBREW_BROWSER=le,e}var gt=async()=>{try{let r=me({title:"Cleaning files & packages from the cache"+String.ellipsis,cancelable:!0});await ke(_.withoutThreshold,r),(0,P.showToast)(P.Toast.Style.Success,"Cleaning completed")}catch(r){await pe("Cleaning failed",r),await fe(3e3)}};
