{"version": 3, "sources": ["../node_modules/stream-chain/index.js", "../node_modules/stream-json/utils/Utf8Stream.js", "../node_modules/stream-json/Parser.js", "../node_modules/stream-json/utils/emit.js", "../node_modules/stream-json/index.js", "../node_modules/stream-json/filters/FilterBase.js", "../node_modules/stream-json/utils/withParser.js", "../node_modules/stream-json/filters/Filter.js", "../node_modules/stream-json/Assembler.js", "../node_modules/stream-json/streamers/StreamBase.js", "../node_modules/stream-json/streamers/StreamArray.js", "../src/upgrade.tsx", "../src/brew.ts", "../src/utils.ts", "../src/preferences.ts"], "sourcesContent": ["'use strict';\n\nconst {Readable, Writable, Duplex, Transform} = require('stream');\n\nconst none = Symbol.for('object-stream.none');\nconst finalSymbol = Symbol.for('object-stream.final');\nconst manySymbol = Symbol.for('object-stream.many');\n\nconst final = value => ({[finalSymbol]: value});\nconst many = values => ({[manySymbol]: values});\n\nconst isFinal = o => o && typeof o == 'object' && finalSymbol in o;\nconst isMany = o => o && typeof o == 'object' && manySymbol in o;\n\nconst getFinalValue = o => o[finalSymbol];\nconst getManyValues = o => o[manySymbol];\n\nconst runAsyncGenerator = async (gen, stream) => {\n  for (;;) {\n    let data = gen.next();\n    if (data && typeof data.then == 'function') {\n      data = await data;\n    }\n    if (data.done) break;\n    let value = data.value;\n    if (value && typeof value.then == 'function') {\n      value = await value;\n    }\n    Chain.sanitize(value, stream);\n  }\n};\n\nconst wrapFunction = fn =>\n  new Transform({\n    writableObjectMode: true,\n    readableObjectMode: true,\n    transform(chunk, encoding, callback) {\n      try {\n        const result = fn.call(this, chunk, encoding);\n        if (result && typeof result.then == 'function') {\n          // thenable\n          result.then(\n            result => (Chain.sanitize(result, this), callback(null)),\n            error => callback(error)\n          );\n          return;\n        }\n        if (result && typeof result.next == 'function') {\n          // generator\n          runAsyncGenerator(result, this).then(\n            () => callback(null),\n            error => callback(error)\n          );\n          return;\n        }\n        Chain.sanitize(result, this);\n        callback(null);\n      } catch (error) {\n        callback(error);\n      }\n    }\n  });\n\nconst wrapArray = fns =>\n  new Transform({\n    writableObjectMode: true,\n    readableObjectMode: true,\n    transform(chunk, encoding, callback) {\n      try {\n        let value = chunk;\n        for (let i = 0; i < fns.length; ++i) {\n          const result = fns[i].call(this, value, encoding);\n          if (result === Chain.none) {\n            callback(null);\n            return;\n          }\n          if (Chain.isFinal(result)) {\n            value = Chain.getFinalValue(result);\n            break;\n          }\n          value = result;\n        }\n        Chain.sanitize(value, this);\n        callback(null);\n      } catch (error) {\n        callback(error);\n      }\n    }\n  });\n\n// is*NodeStream functions taken from https://github.com/nodejs/node/blob/master/lib/internal/streams/utils.js\nconst isReadableNodeStream = obj =>\n  obj &&\n  typeof obj.pipe === 'function' &&\n  typeof obj.on === 'function' &&\n  (!obj._writableState || (typeof obj._readableState === 'object' ? obj._readableState.readable : null) !== false) && // Duplex\n  (!obj._writableState || obj._readableState); // Writable has .pipe.\n\nconst isWritableNodeStream = obj =>\n  obj &&\n  typeof obj.write === 'function' &&\n  typeof obj.on === 'function' &&\n  (!obj._readableState || (typeof obj._writableState === 'object' ? obj._writableState.writable : null) !== false); // Duplex\n\nconst isDuplexNodeStream = obj =>\n  obj && typeof obj.pipe === 'function' && obj._readableState && typeof obj.on === 'function' && typeof obj.write === 'function';\n\nclass Chain extends Duplex {\n  constructor(fns, options) {\n    super(options || {writableObjectMode: true, readableObjectMode: true});\n\n    if (!(fns instanceof Array) || !fns.length) {\n      throw Error(\"Chain's argument should be a non-empty array.\");\n    }\n\n    this.streams = fns\n      .filter(fn => fn)\n      .map((fn, index, fns) => {\n        if (typeof fn === 'function' || fn instanceof Array) return Chain.convertToTransform(fn);\n        if (isDuplexNodeStream(fn) || (!index && isReadableNodeStream(fn)) || (index === fns.length - 1 && isWritableNodeStream(fn))) {\n          return fn;\n        }\n        throw Error('Arguments should be functions, arrays or streams.');\n      })\n      .filter(s => s);\n    this.input = this.streams[0];\n    this.output = this.streams.reduce((output, stream) => (output && output.pipe(stream)) || stream);\n\n    if (!isWritableNodeStream(this.input)) {\n      this._write = (_1, _2, callback) => callback(null);\n      this._final = callback => callback(null); // unavailable in Node 6\n      this.input.on('end', () => this.end());\n    }\n\n    if (isReadableNodeStream(this.output)) {\n      this.output.on('data', chunk => !this.push(chunk) && this.output.pause());\n      this.output.on('end', () => this.push(null));\n    } else {\n      this._read = () => {}; // nop\n      this.resume();\n      this.output.on('finish', () => this.push(null));\n    }\n\n    // connect events\n    if (!options || !options.skipEvents) {\n      this.streams.forEach(stream => stream.on('error', error => this.emit('error', error)));\n    }\n  }\n  _write(chunk, encoding, callback) {\n    let error = null;\n    try {\n      this.input.write(chunk, encoding, e => callback(e || error));\n    } catch (e) {\n      error = e;\n    }\n  }\n  _final(callback) {\n    let error = null;\n    try {\n      this.input.end(null, null, e => callback(e || error));\n    } catch (e) {\n      error = e;\n    }\n  }\n  _read() {\n    this.output.resume();\n  }\n  static make(fns, options) {\n    return new Chain(fns, options);\n  }\n  static sanitize(result, stream) {\n    if (Chain.isFinal(result)) {\n      result = Chain.getFinalValue(result);\n    } else if (Chain.isMany(result)) {\n      result = Chain.getManyValues(result);\n    }\n    if (result !== undefined && result !== null && result !== Chain.none) {\n      if (result instanceof Array) {\n        result.forEach(value => value !== undefined && value !== null && stream.push(value));\n      } else {\n        stream.push(result);\n      }\n    }\n  }\n  static convertToTransform(fn) {\n    if (typeof fn === 'function') return wrapFunction(fn);\n    if (fn instanceof Array) return fn.length ? wrapArray(fn) : null;\n    return null;\n  }\n}\n\nChain.none = none;\nChain.final = final;\nChain.isFinal = isFinal;\nChain.getFinalValue = getFinalValue;\nChain.many = many;\nChain.isMany = isMany;\nChain.getManyValues = getManyValues;\n\nChain.chain = Chain.make;\nChain.make.Constructor = Chain;\n\nmodule.exports = Chain;\n", "'use strict';\n\nconst {Transform} = require('stream');\nconst {StringDecoder} = require('string_decoder');\n\nclass Utf8Stream extends Transform {\n  constructor(options) {\n    super(Object.assign({}, options, {writableObjectMode: false}));\n    this._buffer = '';\n  }\n\n  _transform(chunk, encoding, callback) {\n    if (typeof chunk == 'string') {\n      this._transform = this._transformString;\n    } else {\n      this._stringDecoder = new StringDecoder();\n      this._transform = this._transformBuffer;\n    }\n    this._transform(chunk, encoding, callback);\n  }\n\n  _transformBuffer(chunk, _, callback) {\n    this._buffer += this._stringDecoder.write(chunk);\n    this._processBuffer(callback);\n  }\n\n  _transformString(chunk, _, callback) {\n    this._buffer += chunk.toString();\n    this._processBuffer(callback);\n  }\n\n  _processBuffer(callback) {\n    if (this._buffer) {\n      this.push(this._buffer, 'utf8');\n      this._buffer = '';\n    }\n    callback(null);\n  }\n\n  _flushInput() {\n    // meant to be called from _flush()\n    if (this._stringDecoder) {\n      this._buffer += this._stringDecoder.end();\n    }\n  }\n\n  _flush(callback) {\n    this._flushInput();\n    this._processBuffer(callback);\n  }\n}\n\nmodule.exports = Utf8Stream;\n", "'use strict';\n\nconst Utf8Stream = require('./utils/Utf8Stream');\n\nconst patterns = {\n  value1: /^(?:[\\\"\\{\\[\\]\\-\\d]|true\\b|false\\b|null\\b|\\s{1,256})/,\n  string: /^(?:[^\\x00-\\x1f\\\"\\\\]{1,256}|\\\\[bfnrt\\\"\\\\\\/]|\\\\u[\\da-fA-F]{4}|\\\")/,\n  key1: /^(?:[\\\"\\}]|\\s{1,256})/,\n  colon: /^(?:\\:|\\s{1,256})/,\n  comma: /^(?:[\\,\\]\\}]|\\s{1,256})/,\n  ws: /^\\s{1,256}/,\n  numberStart: /^\\d/,\n  numberDigit: /^\\d{0,256}/,\n  numberFraction: /^[\\.eE]/,\n  numberExponent: /^[eE]/,\n  numberExpSign: /^[-+]/\n};\nconst MAX_PATTERN_SIZE = 16;\n\nlet noSticky = true;\ntry {\n  new RegExp('.', 'y');\n  noSticky = false;\n} catch (e) {\n  // suppress\n}\n\n!noSticky &&\n  Object.keys(patterns).forEach(key => {\n    let src = patterns[key].source.slice(1); // lop off ^\n    if (src.slice(0, 3) === '(?:' && src.slice(-1) === ')') {\n      src = src.slice(3, -1);\n    }\n    patterns[key] = new RegExp(src, 'y');\n  });\n\npatterns.numberFracStart = patterns.numberExpStart = patterns.numberStart;\npatterns.numberFracDigit = patterns.numberExpDigit = patterns.numberDigit;\n\nconst values = {true: true, false: false, null: null},\n  expected = {object: 'objectStop', array: 'arrayStop', '': 'done'};\n\n// long hexadecimal codes: \\uXXXX\nconst fromHex = s => String.fromCharCode(parseInt(s.slice(2), 16));\n\n// short codes: \\b \\f \\n \\r \\t \\\" \\\\ \\/\nconst codes = {b: '\\b', f: '\\f', n: '\\n', r: '\\r', t: '\\t', '\"': '\"', '\\\\': '\\\\', '/': '/'};\n\nclass Parser extends Utf8Stream {\n  static make(options) {\n    return new Parser(options);\n  }\n\n  constructor(options) {\n    super(Object.assign({}, options, {readableObjectMode: true}));\n\n    this._packKeys = this._packStrings = this._packNumbers = this._streamKeys = this._streamStrings = this._streamNumbers = true;\n    if (options) {\n      'packValues' in options && (this._packKeys = this._packStrings = this._packNumbers = options.packValues);\n      'packKeys' in options && (this._packKeys = options.packKeys);\n      'packStrings' in options && (this._packStrings = options.packStrings);\n      'packNumbers' in options && (this._packNumbers = options.packNumbers);\n      'streamValues' in options && (this._streamKeys = this._streamStrings = this._streamNumbers = options.streamValues);\n      'streamKeys' in options && (this._streamKeys = options.streamKeys);\n      'streamStrings' in options && (this._streamStrings = options.streamStrings);\n      'streamNumbers' in options && (this._streamNumbers = options.streamNumbers);\n      this._jsonStreaming = options.jsonStreaming;\n    }\n    !this._packKeys && (this._streamKeys = true);\n    !this._packStrings && (this._streamStrings = true);\n    !this._packNumbers && (this._streamNumbers = true);\n\n    this._done = false;\n    this._expect = this._jsonStreaming ? 'done' : 'value';\n    this._stack = [];\n    this._parent = '';\n    this._open_number = false;\n    this._accumulator = '';\n  }\n\n  _flush(callback) {\n    this._done = true;\n    super._flush(error => {\n      if (error) return callback(error);\n      if (this._open_number) {\n        if (this._streamNumbers) {\n          this.push({name: 'endNumber'});\n        }\n        this._open_number = false;\n        if (this._packNumbers) {\n          this.push({name: 'numberValue', value: this._accumulator});\n          this._accumulator = '';\n        }\n      }\n      callback(null);\n    });\n  }\n\n  _processBuffer(callback) {\n    let match,\n      value,\n      index = 0;\n    main: for (;;) {\n      switch (this._expect) {\n        case 'value1':\n        case 'value':\n          patterns.value1.lastIndex = index;\n          match = patterns.value1.exec(this._buffer);\n          if (!match) {\n            if (this._done || index + MAX_PATTERN_SIZE < this._buffer.length) {\n              if (index < this._buffer.length) return callback(new Error('Parser cannot parse input: expected a value'));\n              return callback(new Error('Parser has expected a value'));\n            }\n            break main; // wait for more input\n          }\n          value = match[0];\n          switch (value) {\n            case '\"':\n              this._streamStrings && this.push({name: 'startString'});\n              this._expect = 'string';\n              break;\n            case '{':\n              this.push({name: 'startObject'});\n              this._stack.push(this._parent);\n              this._parent = 'object';\n              this._expect = 'key1';\n              break;\n            case '[':\n              this.push({name: 'startArray'});\n              this._stack.push(this._parent);\n              this._parent = 'array';\n              this._expect = 'value1';\n              break;\n            case ']':\n              if (this._expect !== 'value1') return callback(new Error(\"Parser cannot parse input: unexpected token ']'\"));\n              if (this._open_number) {\n                this._streamNumbers && this.push({name: 'endNumber'});\n                this._open_number = false;\n                if (this._packNumbers) {\n                  this.push({name: 'numberValue', value: this._accumulator});\n                  this._accumulator = '';\n                }\n              }\n              this.push({name: 'endArray'});\n              this._parent = this._stack.pop();\n              this._expect = expected[this._parent];\n              break;\n            case '-':\n              this._open_number = true;\n              if (this._streamNumbers) {\n                this.push({name: 'startNumber'});\n                this.push({name: 'numberChunk', value: '-'});\n              }\n              this._packNumbers && (this._accumulator = '-');\n              this._expect = 'numberStart';\n              break;\n            case '0':\n              this._open_number = true;\n              if (this._streamNumbers) {\n                this.push({name: 'startNumber'});\n                this.push({name: 'numberChunk', value: '0'});\n              }\n              this._packNumbers && (this._accumulator = '0');\n              this._expect = 'numberFraction';\n              break;\n            case '1':\n            case '2':\n            case '3':\n            case '4':\n            case '5':\n            case '6':\n            case '7':\n            case '8':\n            case '9':\n              this._open_number = true;\n              if (this._streamNumbers) {\n                this.push({name: 'startNumber'});\n                this.push({name: 'numberChunk', value: value});\n              }\n              this._packNumbers && (this._accumulator = value);\n              this._expect = 'numberDigit';\n              break;\n            case 'true':\n            case 'false':\n            case 'null':\n              if (this._buffer.length - index === value.length && !this._done) break main; // wait for more input\n              this.push({name: value + 'Value', value: values[value]});\n              this._expect = expected[this._parent];\n              break;\n            // default: // ws\n          }\n          if (noSticky) {\n            this._buffer = this._buffer.slice(value.length);\n          } else {\n            index += value.length;\n          }\n          break;\n        case 'keyVal':\n        case 'string':\n          patterns.string.lastIndex = index;\n          match = patterns.string.exec(this._buffer);\n          if (!match) {\n            if (index < this._buffer.length && (this._done || this._buffer.length - index >= 6))\n              return callback(new Error('Parser cannot parse input: escaped characters'));\n            if (this._done) return callback(new Error('Parser has expected a string value'));\n            break main; // wait for more input\n          }\n          value = match[0];\n          if (value === '\"') {\n            if (this._expect === 'keyVal') {\n              this._streamKeys && this.push({name: 'endKey'});\n              if (this._packKeys) {\n                this.push({name: 'keyValue', value: this._accumulator});\n                this._accumulator = '';\n              }\n              this._expect = 'colon';\n            } else {\n              this._streamStrings && this.push({name: 'endString'});\n              if (this._packStrings) {\n                this.push({name: 'stringValue', value: this._accumulator});\n                this._accumulator = '';\n              }\n              this._expect = expected[this._parent];\n            }\n          } else if (value.length > 1 && value.charAt(0) === '\\\\') {\n            const t = value.length == 2 ? codes[value.charAt(1)] : fromHex(value);\n            if (this._expect === 'keyVal' ? this._streamKeys : this._streamStrings) {\n              this.push({name: 'stringChunk', value: t});\n            }\n            if (this._expect === 'keyVal' ? this._packKeys : this._packStrings) {\n              this._accumulator += t;\n            }\n          } else {\n            if (this._expect === 'keyVal' ? this._streamKeys : this._streamStrings) {\n              this.push({name: 'stringChunk', value: value});\n            }\n            if (this._expect === 'keyVal' ? this._packKeys : this._packStrings) {\n              this._accumulator += value;\n            }\n          }\n          if (noSticky) {\n            this._buffer = this._buffer.slice(value.length);\n          } else {\n            index += value.length;\n          }\n          break;\n        case 'key1':\n        case 'key':\n          patterns.key1.lastIndex = index;\n          match = patterns.key1.exec(this._buffer);\n          if (!match) {\n            if (index < this._buffer.length || this._done) return callback(new Error('Parser cannot parse input: expected an object key'));\n            break main; // wait for more input\n          }\n          value = match[0];\n          if (value === '\"') {\n            this._streamKeys && this.push({name: 'startKey'});\n            this._expect = 'keyVal';\n          } else if (value === '}') {\n            if (this._expect !== 'key1') return callback(new Error(\"Parser cannot parse input: unexpected token '}'\"));\n            this.push({name: 'endObject'});\n            this._parent = this._stack.pop();\n            this._expect = expected[this._parent];\n          }\n          if (noSticky) {\n            this._buffer = this._buffer.slice(value.length);\n          } else {\n            index += value.length;\n          }\n          break;\n        case 'colon':\n          patterns.colon.lastIndex = index;\n          match = patterns.colon.exec(this._buffer);\n          if (!match) {\n            if (index < this._buffer.length || this._done) return callback(new Error(\"Parser cannot parse input: expected ':'\"));\n            break main; // wait for more input\n          }\n          value = match[0];\n          value === ':' && (this._expect = 'value');\n          if (noSticky) {\n            this._buffer = this._buffer.slice(value.length);\n          } else {\n            index += value.length;\n          }\n          break;\n        case 'arrayStop':\n        case 'objectStop':\n          patterns.comma.lastIndex = index;\n          match = patterns.comma.exec(this._buffer);\n          if (!match) {\n            if (index < this._buffer.length || this._done) return callback(new Error(\"Parser cannot parse input: expected ','\"));\n            break main; // wait for more input\n          }\n          if (this._open_number) {\n            this._streamNumbers && this.push({name: 'endNumber'});\n            this._open_number = false;\n            if (this._packNumbers) {\n              this.push({name: 'numberValue', value: this._accumulator});\n              this._accumulator = '';\n            }\n          }\n          value = match[0];\n          if (value === ',') {\n            this._expect = this._expect === 'arrayStop' ? 'value' : 'key';\n          } else if (value === '}' || value === ']') {\n            if (value === '}' ? this._expect === 'arrayStop' : this._expect !== 'arrayStop') {\n              return callback(new Error(\"Parser cannot parse input: expected '\" + (this._expect === 'arrayStop' ? ']' : '}') + \"'\"));\n            }\n            this.push({name: value === '}' ? 'endObject' : 'endArray'});\n            this._parent = this._stack.pop();\n            this._expect = expected[this._parent];\n          }\n          if (noSticky) {\n            this._buffer = this._buffer.slice(value.length);\n          } else {\n            index += value.length;\n          }\n          break;\n        // number chunks\n        case 'numberStart': // [0-9]\n          patterns.numberStart.lastIndex = index;\n          match = patterns.numberStart.exec(this._buffer);\n          if (!match) {\n            if (index < this._buffer.length || this._done) return callback(new Error('Parser cannot parse input: expected a starting digit'));\n            break main; // wait for more input\n          }\n          value = match[0];\n          this._streamNumbers && this.push({name: 'numberChunk', value: value});\n          this._packNumbers && (this._accumulator += value);\n          this._expect = value === '0' ? 'numberFraction' : 'numberDigit';\n          if (noSticky) {\n            this._buffer = this._buffer.slice(value.length);\n          } else {\n            index += value.length;\n          }\n          break;\n        case 'numberDigit': // [0-9]*\n          patterns.numberDigit.lastIndex = index;\n          match = patterns.numberDigit.exec(this._buffer);\n          if (!match) {\n            if (index < this._buffer.length || this._done) return callback(new Error('Parser cannot parse input: expected a digit'));\n            break main; // wait for more input\n          }\n          value = match[0];\n          if (value) {\n            this._streamNumbers && this.push({name: 'numberChunk', value: value});\n            this._packNumbers && (this._accumulator += value);\n            if (noSticky) {\n              this._buffer = this._buffer.slice(value.length);\n            } else {\n              index += value.length;\n            }\n          } else {\n            if (index < this._buffer.length) {\n              this._expect = 'numberFraction';\n              break;\n            }\n            if (this._done) {\n              this._expect = expected[this._parent];\n              break;\n            }\n            break main; // wait for more input\n          }\n          break;\n        case 'numberFraction': // [\\.eE]?\n          patterns.numberFraction.lastIndex = index;\n          match = patterns.numberFraction.exec(this._buffer);\n          if (!match) {\n            if (index < this._buffer.length || this._done) {\n              this._expect = expected[this._parent];\n              break;\n            }\n            break main; // wait for more input\n          }\n          value = match[0];\n          this._streamNumbers && this.push({name: 'numberChunk', value: value});\n          this._packNumbers && (this._accumulator += value);\n          this._expect = value === '.' ? 'numberFracStart' : 'numberExpSign';\n          if (noSticky) {\n            this._buffer = this._buffer.slice(value.length);\n          } else {\n            index += value.length;\n          }\n          break;\n        case 'numberFracStart': // [0-9]\n          patterns.numberFracStart.lastIndex = index;\n          match = patterns.numberFracStart.exec(this._buffer);\n          if (!match) {\n            if (index < this._buffer.length || this._done) return callback(new Error('Parser cannot parse input: expected a fractional part of a number'));\n            break main; // wait for more input\n          }\n          value = match[0];\n          this._streamNumbers && this.push({name: 'numberChunk', value: value});\n          this._packNumbers && (this._accumulator += value);\n          this._expect = 'numberFracDigit';\n          if (noSticky) {\n            this._buffer = this._buffer.slice(value.length);\n          } else {\n            index += value.length;\n          }\n          break;\n        case 'numberFracDigit': // [0-9]*\n          patterns.numberFracDigit.lastIndex = index;\n          match = patterns.numberFracDigit.exec(this._buffer);\n          value = match[0];\n          if (value) {\n            this._streamNumbers && this.push({name: 'numberChunk', value: value});\n            this._packNumbers && (this._accumulator += value);\n            if (noSticky) {\n              this._buffer = this._buffer.slice(value.length);\n            } else {\n              index += value.length;\n            }\n          } else {\n            if (index < this._buffer.length) {\n              this._expect = 'numberExponent';\n              break;\n            }\n            if (this._done) {\n              this._expect = expected[this._parent];\n              break;\n            }\n            break main; // wait for more input\n          }\n          break;\n        case 'numberExponent': // [eE]?\n          patterns.numberExponent.lastIndex = index;\n          match = patterns.numberExponent.exec(this._buffer);\n          if (!match) {\n            if (index < this._buffer.length) {\n              this._expect = expected[this._parent];\n              break;\n            }\n            if (this._done) {\n              this._expect = 'done';\n              break;\n            }\n            break main; // wait for more input\n          }\n          value = match[0];\n          this._streamNumbers && this.push({name: 'numberChunk', value: value});\n          this._packNumbers && (this._accumulator += value);\n          this._expect = 'numberExpSign';\n          if (noSticky) {\n            this._buffer = this._buffer.slice(value.length);\n          } else {\n            index += value.length;\n          }\n          break;\n        case 'numberExpSign': // [-+]?\n          patterns.numberExpSign.lastIndex = index;\n          match = patterns.numberExpSign.exec(this._buffer);\n          if (!match) {\n            if (index < this._buffer.length) {\n              this._expect = 'numberExpStart';\n              break;\n            }\n            if (this._done) return callback(new Error('Parser has expected an exponent value of a number'));\n            break main; // wait for more input\n          }\n          value = match[0];\n          this._streamNumbers && this.push({name: 'numberChunk', value: value});\n          this._packNumbers && (this._accumulator += value);\n          this._expect = 'numberExpStart';\n          if (noSticky) {\n            this._buffer = this._buffer.slice(value.length);\n          } else {\n            index += value.length;\n          }\n          break;\n        case 'numberExpStart': // [0-9]\n          patterns.numberExpStart.lastIndex = index;\n          match = patterns.numberExpStart.exec(this._buffer);\n          if (!match) {\n            if (index < this._buffer.length || this._done) return callback(new Error('Parser cannot parse input: expected an exponent part of a number'));\n            break main; // wait for more input\n          }\n          value = match[0];\n          this._streamNumbers && this.push({name: 'numberChunk', value: value});\n          this._packNumbers && (this._accumulator += value);\n          this._expect = 'numberExpDigit';\n          if (noSticky) {\n            this._buffer = this._buffer.slice(value.length);\n          } else {\n            index += value.length;\n          }\n          break;\n        case 'numberExpDigit': // [0-9]*\n          patterns.numberExpDigit.lastIndex = index;\n          match = patterns.numberExpDigit.exec(this._buffer);\n          value = match[0];\n          if (value) {\n            this._streamNumbers && this.push({name: 'numberChunk', value: value});\n            this._packNumbers && (this._accumulator += value);\n            if (noSticky) {\n              this._buffer = this._buffer.slice(value.length);\n            } else {\n              index += value.length;\n            }\n          } else {\n            if (index < this._buffer.length || this._done) {\n              this._expect = expected[this._parent];\n              break;\n            }\n            break main; // wait for more input\n          }\n          break;\n        case 'done':\n          patterns.ws.lastIndex = index;\n          match = patterns.ws.exec(this._buffer);\n          if (!match) {\n            if (index < this._buffer.length) {\n              if (this._jsonStreaming) {\n                this._expect = 'value';\n                break;\n              }\n              return callback(new Error('Parser cannot parse input: unexpected characters'));\n            }\n            break main; // wait for more input\n          }\n          value = match[0];\n          if (this._open_number) {\n            this._streamNumbers && this.push({name: 'endNumber'});\n            this._open_number = false;\n            if (this._packNumbers) {\n              this.push({name: 'numberValue', value: this._accumulator});\n              this._accumulator = '';\n            }\n          }\n          if (noSticky) {\n            this._buffer = this._buffer.slice(value.length);\n          } else {\n            index += value.length;\n          }\n          break;\n      }\n    }\n    !noSticky && (this._buffer = this._buffer.slice(index));\n    callback(null);\n  }\n}\nParser.parser = Parser.make;\nParser.make.Constructor = Parser;\n\nmodule.exports = Parser;\n", "'use strict';\n\nconst emit = stream => stream.on('data', item => stream.emit(item.name, item.value));\n\nmodule.exports = emit;\n", "'use strict';\n\nconst Parser = require('./Parser');\nconst emit = require('./utils/emit');\n\nconst make = options => emit(new Parser(options));\n\nmake.Parser = Parser;\nmake.parser = Parser.parser;\n\nmodule.exports = make;\n", "'use strict';\n\nconst {Transform} = require('stream');\n\nclass FilterBase extends Transform {\n  static stringFilter(string, separator) {\n    return stack => {\n      const path = stack.join(separator);\n      return (\n        (path.length === string.length && path === string) ||\n        (path.length > string.length && path.substr(0, string.length) === string && path.substr(string.length, separator.length) === separator)\n      );\n    };\n  }\n\n  static regExpFilter(regExp, separator) {\n    return stack => regExp.test(stack.join(separator));\n  }\n\n  static arrayReplacement(array) {\n    return () => array;\n  }\n\n  constructor(options) {\n    super(Object.assign({}, options, {writableObjectMode: true, readableObjectMode: true}));\n    this._transform = this._check;\n    this._stack = [];\n\n    const filter = options && options.filter,\n      separator = (options && options.pathSeparator) || '.';\n    if (typeof filter == 'string') {\n      this._filter = FilterBase.stringFilter(filter, separator);\n    } else if (typeof filter == 'function') {\n      this._filter = filter;\n    } else if (filter instanceof RegExp) {\n      this._filter = FilterBase.regExpFilter(filter, separator);\n    }\n\n    const replacement = options && options.replacement;\n    if (typeof replacement == 'function') {\n      this._replacement = replacement;\n    } else {\n      this._replacement = FilterBase.arrayReplacement(replacement || FilterBase.defaultReplacement);\n    }\n    this._allowEmptyReplacement = options && options.allowEmptyReplacement;\n\n    this._streamKeys = true;\n    if (options) {\n      'streamValues' in options && (this._streamKeys = options.streamValues);\n      'streamKeys' in options && (this._streamKeys = options.streamKeys);\n    }\n\n    this._once = options && options.once;\n    this._previousToken = '';\n  }\n\n  _check(chunk, _, callback) {\n    // update the last stack key\n    switch (chunk.name) {\n      case 'startObject':\n      case 'startArray':\n      case 'startString':\n      case 'startNumber':\n      case 'nullValue':\n      case 'trueValue':\n      case 'falseValue':\n        if (typeof this._stack[this._stack.length - 1] == 'number') {\n          // array\n          ++this._stack[this._stack.length - 1];\n        }\n        break;\n      case 'keyValue':\n        this._stack[this._stack.length - 1] = chunk.value;\n        break;\n      case 'numberValue':\n        if (this._previousToken !== 'endNumber' && typeof this._stack[this._stack.length - 1] == 'number') {\n          // array\n          ++this._stack[this._stack.length - 1];\n        }\n        break;\n      case 'stringValue':\n        if (this._previousToken !== 'endString' && typeof this._stack[this._stack.length - 1] == 'number') {\n          // array\n          ++this._stack[this._stack.length - 1];\n        }\n        break;\n    }\n    this._previousToken = chunk.name;\n    // check, if we allow a chunk\n    if (this._checkChunk(chunk)) {\n      return callback(null);\n    }\n    // update the stack\n    switch (chunk.name) {\n      case 'startObject':\n        this._stack.push(null);\n        break;\n      case 'startArray':\n        this._stack.push(-1);\n        break;\n      case 'endObject':\n      case 'endArray':\n        this._stack.pop();\n        break;\n    }\n    callback(null);\n  }\n\n  _passObject(chunk, _, callback) {\n    this.push(chunk);\n    switch (chunk.name) {\n      case 'startObject':\n      case 'startArray':\n        ++this._depth;\n        break;\n      case 'endObject':\n      case 'endArray':\n        --this._depth;\n        break;\n    }\n    if (!this._depth) {\n      this._transform = this._once ? this._skip : this._check;\n    }\n    callback(null);\n  }\n\n  _pass(chunk, _, callback) {\n    this.push(chunk);\n    callback(null);\n  }\n\n  _skipObject(chunk, _, callback) {\n    switch (chunk.name) {\n      case 'startObject':\n      case 'startArray':\n        ++this._depth;\n        break;\n      case 'endObject':\n      case 'endArray':\n        --this._depth;\n        break;\n    }\n    if (!this._depth) {\n      this._transform = this._once ? this._pass : this._check;\n    }\n    callback(null);\n  }\n\n  _skip(chunk, _, callback) {\n    callback(null);\n  }\n}\n\nFilterBase.defaultReplacement = [{name: 'nullValue', value: null}];\n\nconst passValue = (last, post) =>\n  function(chunk, _, callback) {\n    if (this._expected) {\n      const expected = this._expected;\n      this._expected = '';\n      this._transform = this._once ? this._skip : this._check;\n      if (expected === chunk.name) {\n        this.push(chunk);\n      } else {\n        return this._transform(chunk, _, callback);\n      }\n    } else {\n      this.push(chunk);\n      if (chunk.name === last) {\n        this._expected = post;\n      }\n    }\n    callback(null);\n  };\n\nFilterBase.prototype._passNumber = passValue('endNumber', 'numberValue');\nFilterBase.prototype._passString = passValue('endString', 'stringValue');\nFilterBase.prototype._passKey = passValue('endKey', 'keyValue');\n\nconst skipValue = (last, post) =>\n  function(chunk, _, callback) {\n    if (this._expected) {\n      const expected = this._expected;\n      this._expected = '';\n      this._transform = this._once ? this._pass : this._check;\n      if (expected !== chunk.name) {\n        return this._transform(chunk, _, callback);\n      }\n    } else {\n      if (chunk.name === last) {\n        this._expected = post;\n      }\n    }\n    callback(null);\n  };\n\nFilterBase.prototype._skipNumber = skipValue('endNumber', 'numberValue');\nFilterBase.prototype._skipString = skipValue('endString', 'stringValue');\nFilterBase.prototype._skipKey = skipValue('endKey', 'keyValue');\n\nmodule.exports = FilterBase;\n", "'use strict';\n\nconst {chain} = require('stream-chain');\n\nconst Parser = require('../Parser');\n\nconst withParser = (fn, options) =>\n  chain([new Parser(options), fn(options)], Object.assign({}, options, {writableObjectMode: false, readableObjectMode: true}));\n\nmodule.exports = withParser;\n", "'use strict';\n\nconst FilterBase = require('./FilterBase');\nconst withParser = require('../utils/withParser');\n\nclass Filter extends FilterBase {\n  static make(options) {\n    return new Filter(options);\n  }\n\n  static withParser(options) {\n    return withParser(Filter.make, options);\n  }\n\n  constructor(options) {\n    super(options);\n    this._once = false;\n    this._lastStack = [];\n  }\n\n  _flush(callback) {\n    this._syncStack();\n    callback(null);\n  }\n\n  _checkChunk(chunk) {\n    switch (chunk.name) {\n      case 'startObject':\n        if (this._filter(this._stack, chunk)) {\n          this._syncStack();\n          this.push(chunk);\n          this._lastStack.push(null);\n        }\n        break;\n      case 'startArray':\n        if (this._filter(this._stack, chunk)) {\n          this._syncStack();\n          this.push(chunk);\n          this._lastStack.push(-1);\n        }\n        break;\n      case 'nullValue':\n      case 'trueValue':\n      case 'falseValue':\n      case 'stringValue':\n      case 'numberValue':\n        if (this._filter(this._stack, chunk)) {\n          this._syncStack();\n          this.push(chunk);\n        }\n        break;\n      case 'startString':\n        if (this._filter(this._stack, chunk)) {\n          this._syncStack();\n          this.push(chunk);\n          this._transform = this._passString;\n        } else {\n          this._transform = this._skipString;\n        }\n        break;\n      case 'startNumber':\n        if (this._filter(this._stack, chunk)) {\n          this._syncStack();\n          this.push(chunk);\n          this._transform = this._passNumber;\n        } else {\n          this._transform = this._skipNumber;\n        }\n        break;\n    }\n    return false;\n  }\n\n  _syncStack() {\n    const stack = this._stack,\n      last = this._lastStack,\n      stackLength = stack.length,\n      lastLength = last.length;\n\n    // find the common part\n    let commonLength = 0;\n    for (const n = Math.min(stackLength, lastLength); commonLength < n && stack[commonLength] === last[commonLength]; ++commonLength);\n\n    // close old objects\n    for (let i = lastLength - 1; i > commonLength; --i) {\n      this.push({name: typeof last[i] == 'number' ? 'endArray' : 'endObject'});\n    }\n    if (commonLength < lastLength) {\n      if (commonLength < stackLength) {\n        if (typeof stack[commonLength] == 'string') {\n          const key = stack[commonLength];\n          if (this._streamKeys) {\n            this.push({name: 'startKey'});\n            this.push({name: 'stringChunk', value: key});\n            this.push({name: 'endKey'});\n          }\n          this.push({name: 'keyValue', value: key});\n        }\n        ++commonLength;\n      } else {\n        this.push({name: typeof last[commonLength] == 'number' ? 'endArray' : 'endObject'});\n      }\n    }\n\n    // open new objects\n    for (let i = commonLength; i < stackLength; ++i) {\n      const key = stack[i];\n      if (typeof key == 'number') {\n        if (key >= 0) {\n          this.push({name: 'startArray'});\n        }\n      } else if (typeof key == 'string') {\n        this.push({name: 'startObject'});\n        if (this._streamKeys) {\n          this.push({name: 'startKey'});\n          this.push({name: 'stringChunk', value: key});\n          this.push({name: 'endKey'});\n        }\n        this.push({name: 'keyValue', value: key});\n      }\n    }\n\n    // update the last stack\n    this._lastStack = Array.prototype.concat.call(stack);\n  }\n}\nFilter.filter = Filter.make;\nFilter.make.Constructor = Filter;\n\nmodule.exports = Filter;\n", "'use strict';\n\nconst EventEmitter = require('events');\n\nconst startObject = Ctr =>\n  function () {\n    if (this.done) {\n      this.done = false;\n    } else {\n      this.stack.push(this.current, this.key);\n    }\n    this.current = new Ctr();\n    this.key = null;\n  };\n\nclass Assembler extends EventEmitter {\n  static connectTo(stream, options) {\n    return new Assembler(options).connectTo(stream);\n  }\n\n  constructor(options) {\n    super();\n    this.stack = [];\n    this.current = this.key = null;\n    this.done = true;\n    if (options) {\n      this.reviver = typeof options.reviver == 'function' && options.reviver;\n      if (this.reviver) {\n        this.stringValue = this._saveValue = this._saveValueWithReviver;\n      }\n      if (options.numberAsString) {\n        this.numberValue = this.stringValue;\n      }\n    }\n  }\n\n  connectTo(stream) {\n    stream.on('data', chunk => {\n      if (this[chunk.name]) {\n        this[chunk.name](chunk.value);\n        if (this.done) this.emit('done', this);\n      }\n    });\n    return this;\n  }\n\n  get depth() {\n    return (this.stack.length >> 1) + (this.done ? 0 : 1);\n  }\n\n  get path() {\n    const path = [];\n    for (let i = 0; i < this.stack.length; i += 2) {\n      const key = this.stack[i + 1];\n      path.push(key === null ? this.stack[i].length : key);\n    }\n    return path;\n  }\n\n  dropToLevel(level) {\n    if (level < this.depth) {\n      if (level) {\n        const index = (level - 1) << 1;\n        this.current = this.stack[index];\n        this.key = this.stack[index + 1];\n        this.stack.splice(index);\n      } else {\n        this.stack = [];\n        this.current = this.key = null;\n        this.done = true;\n      }\n    }\n    return this;\n  }\n\n  consume(chunk) {\n    this[chunk.name] && this[chunk.name](chunk.value);\n    return this;\n  }\n\n  keyValue(value) {\n    this.key = value;\n  }\n\n  //stringValue() - aliased below to _saveValue()\n\n  numberValue(value) {\n    this._saveValue(parseFloat(value));\n  }\n  nullValue() {\n    this._saveValue(null);\n  }\n  trueValue() {\n    this._saveValue(true);\n  }\n  falseValue() {\n    this._saveValue(false);\n  }\n\n  //startObject() - assigned below\n\n  endObject() {\n    if (this.stack.length) {\n      const value = this.current;\n      this.key = this.stack.pop();\n      this.current = this.stack.pop();\n      this._saveValue(value);\n    } else {\n      this.done = true;\n    }\n  }\n\n  //startArray() - assigned below\n  //endArray() - aliased below to endObject()\n\n  _saveValue(value) {\n    if (this.done) {\n      this.current = value;\n    } else {\n      if (this.current instanceof Array) {\n        this.current.push(value);\n      } else {\n        this.current[this.key] = value;\n        this.key = null;\n      }\n    }\n  }\n  _saveValueWithReviver(value) {\n    if (this.done) {\n      this.current = this.reviver('', value);\n    } else {\n      if (this.current instanceof Array) {\n        value = this.reviver('' + this.current.length, value);\n        this.current.push(value);\n        if (value === undefined) {\n          delete this.current[this.current.length - 1];\n        }\n      } else {\n        value = this.reviver(this.key, value);\n        if (value !== undefined) {\n          this.current[this.key] = value;\n        }\n        this.key = null;\n      }\n    }\n  }\n}\n\nAssembler.prototype.stringValue = Assembler.prototype._saveValue;\nAssembler.prototype.startObject = startObject(Object);\nAssembler.prototype.startArray = startObject(Array);\nAssembler.prototype.endArray = Assembler.prototype.endObject;\n\nmodule.exports = Assembler;\n", "'use strict';\n\nconst {Transform} = require('stream');\nconst Assembler = require('../Assembler');\n\nclass Counter {\n  constructor(initialDepth) {\n    this.depth = initialDepth;\n  }\n  startObject() {\n    ++this.depth;\n  }\n  endObject() {\n    --this.depth;\n  }\n  startArray() {\n    ++this.depth;\n  }\n  endArray() {\n    --this.depth;\n  }\n}\n\nclass StreamBase extends Transform {\n  constructor(options) {\n    super(Object.assign({}, options, {writableObjectMode: true, readableObjectMode: true}));\n    if (options) {\n      this.objectFilter = options.objectFilter;\n      this.includeUndecided = options.includeUndecided;\n    }\n    if (typeof this.objectFilter != 'function') {\n      this._filter = this._transform;\n    }\n    this._transform = this._wait || this._filter;\n    this._assembler = new Assembler(options);\n  }\n\n  _transform(chunk, encoding, callback) {\n    if (this._assembler[chunk.name]) {\n      this._assembler[chunk.name](chunk.value);\n      if (this._assembler.depth === this._level) {\n        this._push();\n      }\n    }\n    callback(null);\n  }\n\n  _filter(chunk, encoding, callback) {\n    if (this._assembler[chunk.name]) {\n      this._assembler[chunk.name](chunk.value);\n      const result = this.objectFilter(this._assembler);\n      if (result) {\n        if (this._assembler.depth === this._level) {\n          this._push();\n          this._transform = this._filter;\n        }\n        this._transform = this._accept;\n        return callback(null);\n      }\n      if (result === false) {\n        this._saved_assembler = this._assembler;\n        this._assembler = new Counter(this._saved_assembler.depth);\n        this._saved_assembler.dropToLevel(this._level);\n        if (this._assembler.depth === this._level) {\n          this._assembler = this._saved_assembler;\n          this._transform = this._filter;\n        }\n        this._transform = this._reject;\n        return callback(null);\n      }\n      if (this._assembler.depth === this._level) {\n        this._push(!this.includeUndecided);\n      }\n    }\n    callback(null);\n  }\n\n  _accept(chunk, encoding, callback) {\n    if (this._assembler[chunk.name]) {\n      this._assembler[chunk.name](chunk.value);\n      if (this._assembler.depth === this._level) {\n        this._push();\n        this._transform = this._filter;\n      }\n    }\n    callback(null);\n  }\n\n  _reject(chunk, encoding, callback) {\n    if (this._assembler[chunk.name]) {\n      this._assembler[chunk.name](chunk.value);\n      if (this._assembler.depth === this._level) {\n        this._assembler = this._saved_assembler;\n        this._transform = this._filter;\n      }\n    }\n    callback(null);\n  }\n}\n\nmodule.exports = StreamBase;\n", "'use strict';\n\nconst StreamBase = require('./StreamBase');\nconst withParser = require('../utils/withParser');\n\nclass StreamArray extends StreamBase {\n  static make(options) {\n    return new StreamArray(options);\n  }\n\n  static withParser(options) {\n    return withParser(StreamArray.make, options);\n  }\n\n  constructor(options) {\n    super(options);\n    this._level = 1;\n    this._counter = 0;\n  }\n\n  _wait(chunk, _, callback) {\n    // first chunk should open an array\n    if (chunk.name !== 'startArray') {\n      return callback(new Error('Top-level object should be an array.'));\n    }\n    this._transform = this._filter;\n    return this._transform(chunk, _, callback);\n  }\n\n  _push(discard) {\n    if (this._assembler.current.length) {\n      if (discard) {\n        ++this._counter;\n        this._assembler.current.pop();\n      } else {\n        this.push({key: this._counter++, value: this._assembler.current.pop()});\n      }\n    }\n  }\n}\nStreamArray.streamArray = StreamArray.make;\nStreamArray.make.Constructor = StreamArray;\n\nmodule.exports = StreamArray;\n", "import { showToast, Toast } from \"@raycast/api\";\nimport { brewUpgradeAll } from \"./brew\";\nimport { preferences } from \"./preferences\";\nimport { showActionToast, showFailureToast, wait } from \"./utils\";\n\nexport default async (): Promise<void> => {\n  try {\n    const abort = showActionToast({ title: \"Upgrading formula & casks\" + String.ellipsis, cancelable: true });\n    await brewUpgradeAll(preferences.greedyUpgrades, abort);\n    showToast(Toast.Style.Success, \"Upgrade completed\");\n  } catch (err) {\n    await showFailureToast(\"Upgrade failed\", err as Error);\n    await wait(3000);\n  }\n};\n", "import { exec, execSync } from \"child_process\";\nimport { promisify } from \"util\";\nimport { constants as fs_constants } from \"fs\";\nimport * as fs from \"fs/promises\";\nimport { join as path_join } from \"path\";\nimport { cpus } from \"os\";\nimport { environment } from \"@raycast/api\";\nimport * as utils from \"./utils\";\nimport { preferences } from \"./preferences\";\n\nconst execp = promisify(exec);\n\nexport interface ExecError extends Error {\n  code: number;\n  stdout: string;\n  stderr: string;\n}\n\nexport interface ExecResult {\n  stdout: string;\n  stderr: string;\n}\n\n/// Types\n\nexport interface Nameable {\n  name: string;\n}\n\ninterface Installable {\n  tap: string;\n  desc?: string;\n  homepage: string;\n  versions: Versions;\n  outdated: boolean;\n  caveats?: string;\n}\n\nexport interface Cask extends Installable {\n  token: string;\n  name: string[];\n  version: string;\n  installed?: string; // version\n  auto_updates: boolean;\n  depends_on: CaskDependency;\n  conflicts_with?: { cask: string[] };\n}\n\nexport interface CaskDependency {\n  macos?: { [key: string]: string[] };\n}\n\nexport interface Formula extends Installable, Nameable {\n  license: string | null;\n  aliases: string[];\n  dependencies: string[];\n  build_dependencies: string[];\n  installed: InstalledVersion[];\n  keg_only: boolean;\n  linked_key: string;\n  pinned: boolean;\n  conflicts_with?: string[];\n}\n\ninterface Outdated extends Nameable {\n  current_version: string;\n}\n\nexport interface OutdatedFormula extends Outdated {\n  installed_versions: string[];\n  pinned_vesion?: string;\n  pinned: boolean;\n}\n\nexport interface OutdatedCask extends Outdated {\n  installed_versions: string;\n}\n\nexport interface InstalledVersion {\n  version: string;\n  installed_as_dependency: boolean;\n  installed_on_request: boolean;\n}\n\nexport interface Versions {\n  stable: string;\n  head?: string;\n  bottle: boolean;\n}\n\nexport interface InstallableResults {\n  formulae: Formula[];\n  casks: Cask[];\n}\n\nexport interface OutdatedResults {\n  formulae: OutdatedFormula[];\n  casks: OutdatedCask[];\n}\n\nexport interface InstalledMap {\n  formulae: Map<string, Formula>;\n  casks: Map<string, Cask>;\n}\n\n/// Paths\n\nexport const brewPrefix = (() => {\n  if (preferences.customBrewPath && preferences.customBrewPath.length > 0)\n    return path_join(preferences.customBrewPath, \"..\", \"..\");\n  try {\n    return execSync(\"brew --prefix\", { encoding: \"utf8\" }).trim();\n  } catch {\n    return cpus()[0].model.includes(\"Apple\") ? \"/opt/homebrew\" : \"/usr/local\";\n  }\n})();\n\nexport const brewPath = (suffix: string) => path_join(brewPrefix, suffix);\nexport const brewExecutable = () => brewPath(\"bin/brew\");\n\n/// Fetching\n\nconst installedCachePath = utils.cachePath(\"installedv2.json\");\nconst formulaCachePath = utils.cachePath(\"formula.json\");\nconst caskCachePath = utils.cachePath(\"cask.json\");\n\nexport async function brewFetchInstalled(\n  useCache: boolean,\n  cancel?: AbortController,\n): Promise<InstalledMap | undefined> {\n  const results = await brewFetchInstallableResults(useCache, cancel);\n  return brewMapInstalled(results);\n}\n\nasync function brewFetchInstallableResults(\n  useCache: boolean,\n  cancel?: AbortController,\n): Promise<InstallableResults | undefined> {\n  async function installed(): Promise<string> {\n    return (await execBrew(`info --json=v2 --installed`, cancel)).stdout;\n  }\n\n  if (!useCache) {\n    return JSON.parse(await installed());\n  }\n\n  async function updateCache(): Promise<InstallableResults> {\n    const info = await installed();\n    try {\n      await fs.writeFile(installedCachePath, info);\n    } catch (err) {\n      console.error(\"Failed to write installed cache:\", err);\n    }\n    return JSON.parse(info);\n  }\n\n  async function mtimeMs(path: string): Promise<number> {\n    return (await fs.stat(path)).mtimeMs;\n  }\n\n  async function readCache(): Promise<InstallableResults> {\n    const cacheTime = await mtimeMs(installedCachePath);\n    // 'var/homebrew/locks' is updated after installed keg_only or linked formula.\n    const locksTime = await mtimeMs(brewPath(\"var/homebrew/locks\"));\n    // Casks\n    const caskroomTime = await mtimeMs(brewPath(\"Caskroom\"));\n\n    // 'var/homebrew/pinned' is updated after pin/unpin actions (but does not exist if there are no pinned formula).\n    let pinnedTime;\n    try {\n      pinnedTime = await mtimeMs(brewPath(\"var/homebrew/pinned\"));\n    } catch {\n      pinnedTime = 0;\n    }\n    // Because '/var/homebrew/pinned can be removed, we need to also check the parent directory'\n    const homebrewTime = await mtimeMs(brewPath(\"var/homebrew\"));\n\n    if (homebrewTime < cacheTime && caskroomTime < cacheTime && locksTime < cacheTime && pinnedTime < cacheTime) {\n      const cacheBuffer = await fs.readFile(installedCachePath);\n      return JSON.parse(cacheBuffer.toString());\n    } else {\n      console.error(\"Invalid cache\");\n      return await updateCache();\n    }\n  }\n\n  try {\n    return await readCache();\n  } catch {\n    return await updateCache();\n  }\n}\n\nfunction brewMapInstalled(installed?: InstallableResults): InstalledMap | undefined {\n  if (!installed) {\n    return undefined;\n  }\n\n  const formulae = new Map<string, Formula>();\n  for (const formula of installed.formulae) {\n    formulae.set(formula.name, formula);\n  }\n\n  const casks = new Map<string, Cask>();\n  for (const cask of installed.casks) {\n    casks.set(cask.token, cask);\n  }\n\n  return { formulae: formulae, casks: casks };\n}\n\nexport async function brewFetchOutdated(greedy: boolean, cancel?: AbortController): Promise<OutdatedResults> {\n  let cmd = `outdated --json=v2`;\n  if (greedy) {\n    cmd += \" --greedy\"; // include auto_update casks\n  }\n  // 'outdated' is only reliable after performing a 'brew update'\n  await brewUpdate(cancel);\n  const output = await execBrew(cmd, cancel);\n  return JSON.parse(output.stdout);\n}\n\n/// Search\n\nconst formulaURL = \"https://formulae.brew.sh/api/formula.json\";\nconst caskURL = \"https://formulae.brew.sh/api/cask.json\";\n\nconst formulaRemote: utils.Remote<Formula> = { url: formulaURL, cachePath: formulaCachePath };\nconst caskRemote: utils.Remote<Cask> = { url: caskURL, cachePath: caskCachePath };\n\n// Store the query so that text entered during the initial fetch is respected.\nlet searchQuery: string | undefined;\n\nexport async function brewFetchFormulae(): Promise<Formula[]> {\n  return await utils.fetchRemote(formulaRemote);\n}\n\nexport async function brewFetchCasks(): Promise<Cask[]> {\n  return await utils.fetchRemote(caskRemote);\n}\n\nexport async function brewSearch(\n  searchText: string,\n  limit?: number,\n  signal?: AbortSignal,\n): Promise<InstallableResults> {\n  searchQuery = searchText;\n\n  let formulae = await brewFetchFormulae();\n\n  if (signal?.aborted) {\n    const error = new Error(\"Aborted\");\n    error.name = \"AbortError\";\n    throw error;\n  }\n\n  let casks = await brewFetchCasks();\n\n  if (signal?.aborted) {\n    const error = new Error(\"Aborted\");\n    error.name = \"AbortError\";\n    throw error;\n  }\n\n  if (searchQuery.length > 0) {\n    const target = searchQuery.toLowerCase();\n    formulae = formulae\n      ?.filter((formula) => {\n        return formula.name.toLowerCase().includes(target) || formula.desc?.toLowerCase().includes(target);\n      })\n      .sort((lhs, rhs) => {\n        return brewCompare(lhs.name, rhs.name, target);\n      });\n\n    casks = casks\n      ?.filter((cask) => {\n        return (\n          cask.token.toLowerCase().includes(target) ||\n          cask.name.some((name) => name.toLowerCase().includes(target)) ||\n          cask.desc?.toLowerCase().includes(target)\n        );\n      })\n      .sort((lhs, rhs) => {\n        return brewCompare(lhs.token, rhs.token, target);\n      });\n  }\n\n  const formulaeLen = formulae.length;\n  const casksLen = casks.length;\n\n  if (limit) {\n    formulae = formulae.slice(0, limit);\n    casks = casks.slice(0, limit);\n  }\n\n  formulae.totalLength = formulaeLen;\n  casks.totalLength = casksLen;\n\n  return { formulae: formulae, casks: casks };\n}\n\n/// Actions\n\nexport async function brewInstall(installable: Cask | Formula, cancel?: AbortController): Promise<void> {\n  const identifier = brewIdentifier(installable);\n  await execBrew(`install ${brewQuarantineOption()} ${brewCaskOption(installable)} ${identifier}`, cancel);\n  if (isCask(installable)) {\n    installable.installed = installable.version;\n  } else {\n    installable.installed = [\n      { version: installable.versions.stable, installed_as_dependency: false, installed_on_request: true },\n    ];\n  }\n}\n\nexport async function brewUninstall(installable: Cask | Nameable, cancel?: AbortController): Promise<void> {\n  const identifier = brewIdentifier(installable);\n  await execBrew(`rm ${brewCaskOption(installable, true)} ${identifier}`, cancel);\n}\n\nexport async function brewUpgrade(upgradable: Cask | Nameable, cancel?: AbortController): Promise<void> {\n  const identifier = brewIdentifier(upgradable);\n  await execBrew(`upgrade ${brewQuarantineOption()} ${brewCaskOption(upgradable)} ${identifier}`, cancel);\n}\n\nexport async function brewUpgradeAll(greedy: boolean, cancel?: AbortController): Promise<void> {\n  let cmd = `upgrade ${brewQuarantineOption()}`;\n  if (greedy) {\n    cmd += \" --greedy\";\n  }\n  await execBrew(cmd, cancel);\n}\n\nexport async function brewCleanup(withoutThreshold: boolean, cancel?: AbortController): Promise<void> {\n  let cmd = `cleanup`;\n  if (withoutThreshold) {\n    cmd += \" --prune=all\";\n  }\n  await execBrew(cmd, cancel);\n}\n\nexport async function brewPinFormula(formula: Formula | OutdatedFormula): Promise<void> {\n  await execBrew(`pin ${formula.name}`);\n  formula.pinned = true;\n}\n\nexport async function brewUnpinFormula(formula: Formula | OutdatedFormula): Promise<void> {\n  await execBrew(`unpin ${formula.name}`);\n  formula.pinned = false;\n}\n\nexport async function brewDoctor(): Promise<string> {\n  try {\n    const output = await execBrew(`doctor`);\n    return output.stdout;\n  } catch (err) {\n    const execErr = err as ExecError;\n    if (execErr?.code === 1) {\n      return execErr.stderr;\n    } else {\n      return `${err}`;\n    }\n  }\n}\n\nexport async function brewUpdate(cancel?: AbortController): Promise<void> {\n  await execBrew(`update`, cancel);\n}\n\n/// Commands\n\nexport function brewInstallCommand(installable: Cask | Formula | Nameable): string {\n  const identifier = brewIdentifier(installable);\n  return `${brewExecutable()} install ${brewCaskOption(installable)} ${identifier}`.replace(/ +/g, \" \");\n}\n\nexport function brewUninstallCommand(installable: Cask | Formula | Nameable): string {\n  const identifier = brewIdentifier(installable);\n  return `${brewExecutable()} uninstall ${brewCaskOption(installable, true)} ${identifier}`.replace(/ +/g, \" \");\n}\n\nexport function brewUpgradeCommand(upgradable: Cask | Formula | Nameable): string {\n  const identifier = brewIdentifier(upgradable);\n  return `${brewExecutable()} upgrade ${brewCaskOption(upgradable)} ${identifier}`.replace(/ +/g, \" \");\n}\n\n/// Utilities\n\nexport function brewName(item: Cask | Nameable): string {\n  if (isCask(item)) {\n    return item.name && item.name[0] ? item.name[0] : \"Unknown\";\n  } else {\n    return item.name;\n  }\n}\n\nexport function brewIsInstalled(installable: Cask | Formula): boolean {\n  if (isCask(installable)) {\n    return caskIsInstalled(installable);\n  } else {\n    return formulaIsInstalled(installable);\n  }\n}\n\nexport function brewInstallPath(installable: Cask | Formula): string {\n  if (isCask(installable)) {\n    return caskInstallPath(installable);\n  } else {\n    return formulaInstallPath(installable);\n  }\n}\n\nexport function brewFormatVersion(installable: Cask | Formula): string {\n  if (isCask(installable)) {\n    return caskFormatVersion(installable);\n  } else {\n    return formulaFormatVersion(installable);\n  }\n}\n\n/// Private\n\nfunction caskFormatVersion(cask: Cask): string {\n  if (!cask.installed) {\n    return \"\";\n  }\n\n  let version = cask.installed;\n  if (cask.outdated) {\n    version += \" (O)\";\n  }\n  return version;\n}\n\nfunction caskIsInstalled(cask: Cask): boolean {\n  if (cask.installed) {\n    return cask.installed.length > 0;\n  }\n  return false;\n}\n\nfunction caskInstallPath(cask: Cask): string {\n  // Casks are not updated as reliably, so we don't include the cask installed version here.\n  const basePath = brewPath(path_join(\"Caskroom\", cask.token));\n  if (cask.installed) {\n    return path_join(basePath, cask.installed);\n  } else {\n    return basePath;\n  }\n}\n\nfunction formulaInstallPath(formula: Formula): string {\n  const basePath = brewPath(path_join(\"Cellar\", formula.name));\n  if (formula.installed.length) {\n    return path_join(basePath, formula.installed[0].version);\n  } else {\n    return basePath;\n  }\n}\n\nfunction formulaFormatVersion(formula: Formula): string {\n  if (!formula.installed.length) {\n    return \"\";\n  }\n\n  const installed_version = formula.installed[0];\n  let version = installed_version.version;\n  let status = \"\";\n  if (installed_version.installed_as_dependency) {\n    status += \"D\";\n  }\n  if (formula.pinned) {\n    status += \"P\";\n  }\n  if (formula.outdated) {\n    status += \"O\";\n  }\n  if (status) {\n    version += ` (${status})`;\n  }\n  return version;\n}\n\nfunction formulaIsInstalled(formula: Formula): boolean {\n  return formula.installed.length > 0;\n}\n\nfunction brewIdentifier(item: Cask | Nameable): string {\n  return isCask(item) ? item.token : item.name;\n}\n\nfunction brewCaskOption(maybeCask: Cask | Nameable, zappable = false): string {\n  return isCask(maybeCask) ? \"--cask\" + (zappable && preferences.zapCask ? \" --zap\" : \"\") : \"\";\n}\n\nfunction brewQuarantineOption(): string {\n  return preferences.quarantine ? \"--quarantine\" : \"--no-quarantine\";\n}\n\nfunction isCask(maybeCask: Cask | Nameable): maybeCask is Cask {\n  return (maybeCask as Cask).token != undefined;\n}\n\nfunction brewCompare(lhs: string, rhs: string, target: string): number {\n  const lhs_matches = lhs.toLowerCase().includes(target);\n  const rhs_matches = rhs.toLowerCase().includes(target);\n  if (lhs_matches && !rhs_matches) {\n    return -1;\n  } else if (rhs_matches && !lhs_matches) {\n    return 1;\n  } else {\n    return lhs.localeCompare(rhs);\n  }\n}\n\nasync function execBrew(cmd: string, cancel?: AbortController): Promise<ExecResult> {\n  try {\n    const env = await execBrewEnv();\n    return await execp(`${brewExecutable()} ${cmd}`, { signal: cancel?.signal, env: env, maxBuffer: 10 * 1024 * 1024 });\n  } catch (err) {\n    const execErr = err as ExecError;\n    if (preferences.customBrewPath && execErr && execErr.code === 127) {\n      execErr.stderr = `Brew executable not found at: ${preferences.customBrewPath}`;\n      throw execErr;\n    } else {\n      throw err;\n    }\n  }\n}\n\nasync function execBrewEnv(): Promise<NodeJS.ProcessEnv> {\n  const askpassPath = path_join(environment.assetsPath, \"askpass.sh\");\n  try {\n    await fs.access(askpassPath, fs_constants.X_OK);\n  } catch {\n    await fs.chmod(askpassPath, 0o755);\n  }\n  const env = process.env;\n  env[\"SUDO_ASKPASS\"] = askpassPath;\n  // Use HOMEBREW_BROWSER to pass through the app's bundle identifier.\n  // Brew will ignore custom environment variables.\n  env[\"HOMEBREW_BROWSER\"] = utils.bundleIdentifier;\n  return env;\n}\n", "import { Clipboard, environment, Toast } from \"@raycast/api\";\nimport path from \"path\";\nimport fs from \"fs\";\nimport { stat } from \"fs/promises\";\nimport fetch, { FetchError } from \"node-fetch\";\nimport { chain } from \"stream-chain\";\nimport { parser } from \"stream-json\";\nimport { filter } from \"stream-json/filters/Filter\";\nimport { streamArray } from \"stream-json/streamers/StreamArray\";\nimport { pipeline as streamPipeline } from \"stream/promises\";\nimport { ExecError } from \"./brew\";\n\n/// Utils\n\nexport const supportPath: string = (() => {\n  try {\n    fs.mkdirSync(environment.supportPath, { recursive: true });\n  } catch (err) {\n    console.log(\"Failed to create supportPath\");\n  }\n  return environment.supportPath;\n})();\n\nexport const bundleIdentifier: string = (() => {\n  return (\n    environment.supportPath.split(path.sep).find((comp) => {\n      if (comp.startsWith(\"com.raycast\")) {\n        return true;\n      }\n      return false;\n    }) ?? \"com.raycast.macos\"\n  );\n})();\n\nexport function cachePath(name: string): string {\n  return path.join(supportPath, name);\n}\n\nexport interface Remote<T> {\n  url: string;\n  cachePath: string;\n  value?: T[];\n  /** in flight fetch of the remote */\n  fetch?: Promise<T[]>;\n}\n\n// Wait around until user has had chance to click the Toast action.\n// Note this only works for \"no view\" commands (actions still break when popping a view based command).\n// See: https://raycastapp.slack.com/archives/C01E6LWGXJ8/p1642676284027700\nexport async function wait(ms: number): Promise<void> {\n  return new Promise((resolve) => setTimeout(resolve, ms));\n}\n\n// Top-level object keys which should be parsed from the raw JSON objects.\nconst valid_keys = [\n  \"name\",\n  \"tap\",\n  \"desc\",\n  \"homepage\",\n  \"versions\",\n  \"outdated\",\n  \"caveats\",\n  \"token\",\n  \"version\",\n  \"installed\",\n  \"auto_updates\",\n  \"depends_on\",\n  \"conflicts_with\",\n  \"license\",\n  \"aliases\",\n  \"dependencies\",\n  \"build_dependencies\",\n  \"installed\",\n  \"keg_only\",\n  \"linked_key\",\n  \"pinned\",\n];\n\nconst maxFetchRetry = 1;\n\nexport async function fetchRemote<T>(remote: Remote<T>): Promise<T[]> {\n  if (remote.value) {\n    return remote.value;\n  } else if (remote.fetch) {\n    return remote.fetch;\n  } else {\n    remote.fetch = _fetchRemote(remote, 0)\n      .then((value) => {\n        remote.value = value;\n        return value;\n      })\n      .finally(() => {\n        remote.fetch = undefined;\n      });\n    return remote.fetch;\n  }\n}\n\nasync function _fetchRemote<T>(remote: Remote<T>, attempt: number): Promise<T[]> {\n  console.log(\"fetchRemote:\", remote.url);\n\n  async function fetchURL(): Promise<void> {\n    const response = await fetch(remote.url);\n    if (!response.ok || !response.body) {\n      throw new Error(`Invalid response ${response.statusText}`);\n    }\n    await streamPipeline(response.body, fs.createWriteStream(remote.cachePath));\n  }\n\n  async function updateCache(): Promise<void> {\n    let cacheInfo: fs.Stats | undefined;\n    let lastModified = 0;\n    try {\n      cacheInfo = await stat(remote.cachePath);\n      const response = await fetch(remote.url, { method: \"HEAD\" });\n      lastModified = Date.parse(response.headers.get(\"last-modified\") ?? \"\");\n    } catch {\n      console.log(\"Missed cache:\", remote.cachePath); // keep prettier happy :-(\n    }\n    if (!cacheInfo || cacheInfo.size == 0 || lastModified > cacheInfo.mtimeMs) {\n      await fetchURL();\n    }\n  }\n\n  async function readCache(): Promise<T[]> {\n    const keysRe = new RegExp(`\\\\b(${valid_keys.join(\"|\")})\\\\b`);\n\n    return new Promise<T[]>((resolve, reject) => {\n      const value: T[] = [];\n      // stream-json/chain is quite slow, so unfortunately not suitable for real-time queries.\n      // migrating to a sqlite backend _might_ help, although the bootstrap cost\n      // (each time json response changes) will probably be high.\n      const pipeline = chain([\n        fs.createReadStream(remote.cachePath),\n        parser(),\n        filter({ filter: keysRe }),\n        streamArray(),\n      ]);\n      pipeline.on(\"data\", (data) => {\n        if (data && typeof data === \"object\" && \"value\" in data) {\n          value.push(data.value);\n        }\n      });\n      pipeline.on(\"end\", () => {\n        resolve(value);\n      });\n      pipeline.on(\"error\", (err) => {\n        if (attempt < maxFetchRetry) {\n          fs.rmSync(remote.cachePath);\n          _fetchRemote(remote, attempt + 1).then(resolve, reject);\n        } else {\n          reject(err);\n        }\n      });\n    });\n  }\n\n  return updateCache().then(readCache);\n}\n\n/// Toast\n\ninterface ActionToastOptions {\n  title: string;\n  message?: string;\n  cancelable: boolean;\n}\n\nexport function showActionToast(actionOptions: ActionToastOptions): AbortController | undefined {\n  const options: Toast.Options = {\n    style: Toast.Style.Animated,\n    title: actionOptions.title,\n    message: actionOptions.message,\n  };\n\n  let controller: AbortController | undefined;\n\n  if (actionOptions.cancelable) {\n    controller = new AbortController();\n    options.primaryAction = {\n      title: \"Cancel\",\n      onAction: () => {\n        controller?.abort();\n        toast.hide();\n      },\n    };\n  }\n\n  const toast = new Toast(options);\n  toast.show();\n  return controller;\n}\n\nexport async function showFailureToast(title: string, error: Error): Promise<void> {\n  if (error.name == \"AbortError\") {\n    console.log(\"AbortError\");\n    return;\n  }\n\n  console.log(`${title}: ${error}`);\n  const stderr = (error as ExecError).stderr ?? (error as FetchError).message ?? `${error}`;\n  const options: Toast.Options = {\n    style: Toast.Style.Failure,\n    title: title,\n    message: stderr,\n    primaryAction: {\n      title: \"Copy Error Log\",\n      onAction: () => {\n        Clipboard.copy(stderr);\n      },\n    },\n  };\n\n  const toast = new Toast(options);\n  await toast.show();\n}\n\n/// Array\n\ndeclare global {\n  interface Array<T> {\n    totalLength?: number;\n    first(): T | undefined;\n    last(): T | undefined;\n    isTruncated(): boolean;\n  }\n}\n\nif (!Array.prototype.first) {\n  Array.prototype.first = function <T>(this: T[]): T | undefined {\n    return this.length > 0 ? this[0] : undefined;\n  };\n}\n\nif (!Array.prototype.last) {\n  Array.prototype.last = function <T>(this: T[]): T | undefined {\n    return this.length > 0 ? this[this.length - 1] : undefined;\n  };\n}\n\nif (!Array.prototype.isTruncated) {\n  Array.prototype.isTruncated = function <T>(this: T[]): boolean {\n    if (this.totalLength) {\n      return this.length < this.totalLength;\n    }\n    return false;\n  };\n}\n\n/// String\n\ndeclare global {\n  interface StringConstructor {\n    ellipsis: string;\n  }\n}\n\nif (!String.ellipsis) {\n  String.ellipsis = \"…\";\n}\n", "import { getPreferenceValues } from \"@raycast/api\";\n\nexport const preferences = <Preferences & Preferences.CleanUp>getPreferenceValues();\n"], "mappings": "goBAAA,IAAAA,EAAAC,EAAA,CAAAC,GAAAC,IAAA,cAEA,GAAM,CAAC,SAAAC,GAAU,SAAAC,GAAU,OAAAC,GAAQ,UAAAC,CAAS,EAAI,QAAQ,QAAQ,EAE1DC,GAAO,OAAO,IAAI,oBAAoB,EACtCC,EAAc,OAAO,IAAI,qBAAqB,EAC9CC,EAAa,OAAO,IAAI,oBAAoB,EAE5CC,GAAQC,IAAU,CAAC,CAACH,CAAW,EAAGG,CAAK,GACvCC,GAAOC,IAAW,CAAC,CAACJ,CAAU,EAAGI,CAAM,GAEvCC,GAAUC,GAAKA,GAAK,OAAOA,GAAK,UAAYP,KAAeO,EAC3DC,GAASD,GAAKA,GAAK,OAAOA,GAAK,UAAYN,KAAcM,EAEzDE,GAAgBF,GAAKA,EAAEP,CAAW,EAClCU,GAAgBH,GAAKA,EAAEN,CAAU,EAEjCU,GAAoB,MAAOC,EAAKC,IAAW,CAC/C,OAAS,CACP,IAAIC,EAAOF,EAAI,KAAK,EAIpB,GAHIE,GAAQ,OAAOA,EAAK,MAAQ,aAC9BA,EAAO,MAAMA,GAEXA,EAAK,KAAM,MACf,IAAIX,EAAQW,EAAK,MACbX,GAAS,OAAOA,EAAM,MAAQ,aAChCA,EAAQ,MAAMA,GAEhBY,EAAM,SAASZ,EAAOU,CAAM,CAC9B,CACF,EAEMG,GAAeC,GACnB,IAAInB,EAAU,CACZ,mBAAoB,GACpB,mBAAoB,GACpB,UAAUoB,EAAOC,EAAUC,EAAU,CACnC,GAAI,CACF,IAAMC,EAASJ,EAAG,KAAK,KAAMC,EAAOC,CAAQ,EAC5C,GAAIE,GAAU,OAAOA,EAAO,MAAQ,WAAY,CAE9CA,EAAO,KACLA,IAAWN,EAAM,SAASM,EAAQ,IAAI,EAAGD,EAAS,IAAI,GACtDE,GAASF,EAASE,CAAK,CACzB,EACA,MACF,CACA,GAAID,GAAU,OAAOA,EAAO,MAAQ,WAAY,CAE9CV,GAAkBU,EAAQ,IAAI,EAAE,KAC9B,IAAMD,EAAS,IAAI,EACnBE,GAASF,EAASE,CAAK,CACzB,EACA,MACF,CACAP,EAAM,SAASM,EAAQ,IAAI,EAC3BD,EAAS,IAAI,CACf,OAASE,EAAO,CACdF,EAASE,CAAK,CAChB,CACF,CACF,CAAC,EAEGC,GAAYC,GAChB,IAAI1B,EAAU,CACZ,mBAAoB,GACpB,mBAAoB,GACpB,UAAUoB,EAAOC,EAAUC,EAAU,CACnC,GAAI,CACF,IAAIjB,EAAQe,EACZ,QAASO,EAAI,EAAGA,EAAID,EAAI,OAAQ,EAAEC,EAAG,CACnC,IAAMJ,EAASG,EAAIC,CAAC,EAAE,KAAK,KAAMtB,EAAOgB,CAAQ,EAChD,GAAIE,IAAWN,EAAM,KAAM,CACzBK,EAAS,IAAI,EACb,MACF,CACA,GAAIL,EAAM,QAAQM,CAAM,EAAG,CACzBlB,EAAQY,EAAM,cAAcM,CAAM,EAClC,KACF,CACAlB,EAAQkB,CACV,CACAN,EAAM,SAASZ,EAAO,IAAI,EAC1BiB,EAAS,IAAI,CACf,OAASE,EAAO,CACdF,EAASE,CAAK,CAChB,CACF,CACF,CAAC,EAGGI,EAAuBC,GAC3BA,GACA,OAAOA,EAAI,MAAS,YACpB,OAAOA,EAAI,IAAO,aACjB,CAACA,EAAI,iBAAmB,OAAOA,EAAI,gBAAmB,SAAWA,EAAI,eAAe,SAAW,QAAU,MACzG,CAACA,EAAI,gBAAkBA,EAAI,gBAExBC,EAAuBD,GAC3BA,GACA,OAAOA,EAAI,OAAU,YACrB,OAAOA,EAAI,IAAO,aACjB,CAACA,EAAI,iBAAmB,OAAOA,EAAI,gBAAmB,SAAWA,EAAI,eAAe,SAAW,QAAU,IAEtGE,GAAqBF,GACzBA,GAAO,OAAOA,EAAI,MAAS,YAAcA,EAAI,gBAAkB,OAAOA,EAAI,IAAO,YAAc,OAAOA,EAAI,OAAU,WAEhHZ,EAAN,MAAMe,UAAcjC,EAAO,CACzB,YAAY2B,EAAKO,EAAS,CAGxB,GAFA,MAAMA,GAAW,CAAC,mBAAoB,GAAM,mBAAoB,EAAI,CAAC,EAEjE,EAAEP,aAAe,QAAU,CAACA,EAAI,OAClC,MAAM,MAAM,+CAA+C,EAG7D,KAAK,QAAUA,EACZ,OAAOP,GAAMA,CAAE,EACf,IAAI,CAACA,EAAIe,EAAOR,IAAQ,CACvB,GAAI,OAAOP,GAAO,YAAcA,aAAc,MAAO,OAAOa,EAAM,mBAAmBb,CAAE,EACvF,GAAIY,GAAmBZ,CAAE,GAAM,CAACe,GAASN,EAAqBT,CAAE,GAAOe,IAAUR,EAAI,OAAS,GAAKI,EAAqBX,CAAE,EACxH,OAAOA,EAET,MAAM,MAAM,mDAAmD,CACjE,CAAC,EACA,OAAOgB,GAAKA,CAAC,EAChB,KAAK,MAAQ,KAAK,QAAQ,CAAC,EAC3B,KAAK,OAAS,KAAK,QAAQ,OAAO,CAACC,EAAQrB,IAAYqB,GAAUA,EAAO,KAAKrB,CAAM,GAAMA,CAAM,EAE1Fe,EAAqB,KAAK,KAAK,IAClC,KAAK,OAAS,CAACO,EAAIC,EAAIhB,IAAaA,EAAS,IAAI,EACjD,KAAK,OAASA,GAAYA,EAAS,IAAI,EACvC,KAAK,MAAM,GAAG,MAAO,IAAM,KAAK,IAAI,CAAC,GAGnCM,EAAqB,KAAK,MAAM,GAClC,KAAK,OAAO,GAAG,OAAQR,GAAS,CAAC,KAAK,KAAKA,CAAK,GAAK,KAAK,OAAO,MAAM,CAAC,EACxE,KAAK,OAAO,GAAG,MAAO,IAAM,KAAK,KAAK,IAAI,CAAC,IAE3C,KAAK,MAAQ,IAAM,CAAC,EACpB,KAAK,OAAO,EACZ,KAAK,OAAO,GAAG,SAAU,IAAM,KAAK,KAAK,IAAI,CAAC,IAI5C,CAACa,GAAW,CAACA,EAAQ,aACvB,KAAK,QAAQ,QAAQlB,GAAUA,EAAO,GAAG,QAASS,GAAS,KAAK,KAAK,QAASA,CAAK,CAAC,CAAC,CAEzF,CACA,OAAOJ,EAAOC,EAAUC,EAAU,CAChC,IAAIE,EAAQ,KACZ,GAAI,CACF,KAAK,MAAM,MAAMJ,EAAOC,EAAUkB,GAAKjB,EAASiB,GAAKf,CAAK,CAAC,CAC7D,OAASe,EAAG,CACVf,EAAQe,CACV,CACF,CACA,OAAOjB,EAAU,CACf,IAAIE,EAAQ,KACZ,GAAI,CACF,KAAK,MAAM,IAAI,KAAM,KAAMe,GAAKjB,EAASiB,GAAKf,CAAK,CAAC,CACtD,OAASe,EAAG,CACVf,EAAQe,CACV,CACF,CACA,OAAQ,CACN,KAAK,OAAO,OAAO,CACrB,CACA,OAAO,KAAKb,EAAKO,EAAS,CACxB,OAAO,IAAID,EAAMN,EAAKO,CAAO,CAC/B,CACA,OAAO,SAASV,EAAQR,EAAQ,CAC1BiB,EAAM,QAAQT,CAAM,EACtBA,EAASS,EAAM,cAAcT,CAAM,EAC1BS,EAAM,OAAOT,CAAM,IAC5BA,EAASS,EAAM,cAAcT,CAAM,GAETA,GAAW,MAAQA,IAAWS,EAAM,OAC1DT,aAAkB,MACpBA,EAAO,QAAQlB,GAAgCA,GAAU,MAAQU,EAAO,KAAKV,CAAK,CAAC,EAEnFU,EAAO,KAAKQ,CAAM,EAGxB,CACA,OAAO,mBAAmBJ,EAAI,CAC5B,OAAI,OAAOA,GAAO,WAAmBD,GAAaC,CAAE,EAChDA,aAAc,OAAcA,EAAG,OAASM,GAAUN,CAAE,EACjD,IACT,CACF,EAEAF,EAAM,KAAOhB,GACbgB,EAAM,MAAQb,GACda,EAAM,QAAUT,GAChBS,EAAM,cAAgBN,GACtBM,EAAM,KAAOX,GACbW,EAAM,OAASP,GACfO,EAAM,cAAgBL,GAEtBK,EAAM,MAAQA,EAAM,KACpBA,EAAM,KAAK,YAAcA,EAEzBrB,EAAO,QAAUqB,IC1MjB,IAAAuB,EAAAC,EAAA,CAAAC,GAAAC,IAAA,cAEA,GAAM,CAAC,UAAAC,EAAS,EAAI,QAAQ,QAAQ,EAC9B,CAAC,cAAAC,EAAa,EAAI,QAAQ,gBAAgB,EAE1CC,EAAN,cAAyBF,EAAU,CACjC,YAAYG,EAAS,CACnB,MAAM,OAAO,OAAO,CAAC,EAAGA,EAAS,CAAC,mBAAoB,EAAK,CAAC,CAAC,EAC7D,KAAK,QAAU,EACjB,CAEA,WAAWC,EAAOC,EAAUC,EAAU,CAChC,OAAOF,GAAS,SAClB,KAAK,WAAa,KAAK,kBAEvB,KAAK,eAAiB,IAAIH,GAC1B,KAAK,WAAa,KAAK,kBAEzB,KAAK,WAAWG,EAAOC,EAAUC,CAAQ,CAC3C,CAEA,iBAAiBF,EAAOG,EAAGD,EAAU,CACnC,KAAK,SAAW,KAAK,eAAe,MAAMF,CAAK,EAC/C,KAAK,eAAeE,CAAQ,CAC9B,CAEA,iBAAiBF,EAAOG,EAAGD,EAAU,CACnC,KAAK,SAAWF,EAAM,SAAS,EAC/B,KAAK,eAAeE,CAAQ,CAC9B,CAEA,eAAeA,EAAU,CACnB,KAAK,UACP,KAAK,KAAK,KAAK,QAAS,MAAM,EAC9B,KAAK,QAAU,IAEjBA,EAAS,IAAI,CACf,CAEA,aAAc,CAER,KAAK,iBACP,KAAK,SAAW,KAAK,eAAe,IAAI,EAE5C,CAEA,OAAOA,EAAU,CACf,KAAK,YAAY,EACjB,KAAK,eAAeA,CAAQ,CAC9B,CACF,EAEAP,EAAO,QAAUG,ICpDjB,IAAAM,EAAAC,EAAA,CAAAC,GAAAC,IAAA,cAEA,IAAMC,GAAa,IAEbC,EAAW,CACf,OAAQ,sDACR,OAAQ,mEACR,KAAM,wBACN,MAAO,oBACP,MAAO,0BACP,GAAI,aACJ,YAAa,MACb,YAAa,aACb,eAAgB,UAChB,eAAgB,QAChB,cAAe,OACjB,EACMC,GAAmB,GAErBC,EAAW,GACf,GAAI,CACF,IAAI,OAAO,IAAK,GAAG,EACnBA,EAAW,EACb,MAAY,CAEZ,CAEA,CAACA,GACC,OAAO,KAAKF,CAAQ,EAAE,QAAQG,GAAO,CACnC,IAAIC,EAAMJ,EAASG,CAAG,EAAE,OAAO,MAAM,CAAC,EAClCC,EAAI,MAAM,EAAG,CAAC,IAAM,OAASA,EAAI,MAAM,EAAE,IAAM,MACjDA,EAAMA,EAAI,MAAM,EAAG,EAAE,GAEvBJ,EAASG,CAAG,EAAI,IAAI,OAAOC,EAAK,GAAG,CACrC,CAAC,EAEHJ,EAAS,gBAAkBA,EAAS,eAAiBA,EAAS,YAC9DA,EAAS,gBAAkBA,EAAS,eAAiBA,EAAS,YAE9D,IAAMK,GAAS,CAAC,KAAM,GAAM,MAAO,GAAO,KAAM,IAAI,EAClDC,EAAW,CAAC,OAAQ,aAAc,MAAO,YAAa,GAAI,MAAM,EAG5DC,GAAUC,GAAK,OAAO,aAAa,SAASA,EAAE,MAAM,CAAC,EAAG,EAAE,CAAC,EAG3DC,GAAQ,CAAC,EAAG,KAAM,EAAG,KAAM,EAAG;AAAA,EAAM,EAAG,KAAM,EAAG,IAAM,IAAK,IAAK,KAAM,KAAM,IAAK,GAAG,EAEpFC,EAAN,MAAMC,UAAeZ,EAAW,CAC9B,OAAO,KAAKa,EAAS,CACnB,OAAO,IAAID,EAAOC,CAAO,CAC3B,CAEA,YAAYA,EAAS,CACnB,MAAM,OAAO,OAAO,CAAC,EAAGA,EAAS,CAAC,mBAAoB,EAAI,CAAC,CAAC,EAE5D,KAAK,UAAY,KAAK,aAAe,KAAK,aAAe,KAAK,YAAc,KAAK,eAAiB,KAAK,eAAiB,GACpHA,IACF,eAAgBA,IAAY,KAAK,UAAY,KAAK,aAAe,KAAK,aAAeA,EAAQ,YAC7F,aAAcA,IAAY,KAAK,UAAYA,EAAQ,UACnD,gBAAiBA,IAAY,KAAK,aAAeA,EAAQ,aACzD,gBAAiBA,IAAY,KAAK,aAAeA,EAAQ,aACzD,iBAAkBA,IAAY,KAAK,YAAc,KAAK,eAAiB,KAAK,eAAiBA,EAAQ,cACrG,eAAgBA,IAAY,KAAK,YAAcA,EAAQ,YACvD,kBAAmBA,IAAY,KAAK,eAAiBA,EAAQ,eAC7D,kBAAmBA,IAAY,KAAK,eAAiBA,EAAQ,eAC7D,KAAK,eAAiBA,EAAQ,eAEhC,CAAC,KAAK,YAAc,KAAK,YAAc,IACvC,CAAC,KAAK,eAAiB,KAAK,eAAiB,IAC7C,CAAC,KAAK,eAAiB,KAAK,eAAiB,IAE7C,KAAK,MAAQ,GACb,KAAK,QAAU,KAAK,eAAiB,OAAS,QAC9C,KAAK,OAAS,CAAC,EACf,KAAK,QAAU,GACf,KAAK,aAAe,GACpB,KAAK,aAAe,EACtB,CAEA,OAAOC,EAAU,CACf,KAAK,MAAQ,GACb,MAAM,OAAOC,GAAS,CACpB,GAAIA,EAAO,OAAOD,EAASC,CAAK,EAC5B,KAAK,eACH,KAAK,gBACP,KAAK,KAAK,CAAC,KAAM,WAAW,CAAC,EAE/B,KAAK,aAAe,GAChB,KAAK,eACP,KAAK,KAAK,CAAC,KAAM,cAAe,MAAO,KAAK,YAAY,CAAC,EACzD,KAAK,aAAe,KAGxBD,EAAS,IAAI,CACf,CAAC,CACH,CAEA,eAAeA,EAAU,CACvB,IAAIE,EACFC,EACAC,EAAQ,EACVC,EAAM,OACJ,OAAQ,KAAK,QAAS,CACpB,IAAK,SACL,IAAK,QAGH,GAFAlB,EAAS,OAAO,UAAYiB,EAC5BF,EAAQf,EAAS,OAAO,KAAK,KAAK,OAAO,EACrC,CAACe,EAAO,CACV,GAAI,KAAK,OAASE,EAAQhB,GAAmB,KAAK,QAAQ,OACxD,OAAIgB,EAAQ,KAAK,QAAQ,OAAeJ,EAAS,IAAI,MAAM,6CAA6C,CAAC,EAClGA,EAAS,IAAI,MAAM,6BAA6B,CAAC,EAE1D,MAAMK,CACR,CAEA,OADAF,EAAQD,EAAM,CAAC,EACPC,EAAO,CACb,IAAK,IACH,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,aAAa,CAAC,EACtD,KAAK,QAAU,SACf,MACF,IAAK,IACH,KAAK,KAAK,CAAC,KAAM,aAAa,CAAC,EAC/B,KAAK,OAAO,KAAK,KAAK,OAAO,EAC7B,KAAK,QAAU,SACf,KAAK,QAAU,OACf,MACF,IAAK,IACH,KAAK,KAAK,CAAC,KAAM,YAAY,CAAC,EAC9B,KAAK,OAAO,KAAK,KAAK,OAAO,EAC7B,KAAK,QAAU,QACf,KAAK,QAAU,SACf,MACF,IAAK,IACH,GAAI,KAAK,UAAY,SAAU,OAAOH,EAAS,IAAI,MAAM,iDAAiD,CAAC,EACvG,KAAK,eACP,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,WAAW,CAAC,EACpD,KAAK,aAAe,GAChB,KAAK,eACP,KAAK,KAAK,CAAC,KAAM,cAAe,MAAO,KAAK,YAAY,CAAC,EACzD,KAAK,aAAe,KAGxB,KAAK,KAAK,CAAC,KAAM,UAAU,CAAC,EAC5B,KAAK,QAAU,KAAK,OAAO,IAAI,EAC/B,KAAK,QAAUP,EAAS,KAAK,OAAO,EACpC,MACF,IAAK,IACH,KAAK,aAAe,GAChB,KAAK,iBACP,KAAK,KAAK,CAAC,KAAM,aAAa,CAAC,EAC/B,KAAK,KAAK,CAAC,KAAM,cAAe,MAAO,GAAG,CAAC,GAE7C,KAAK,eAAiB,KAAK,aAAe,KAC1C,KAAK,QAAU,cACf,MACF,IAAK,IACH,KAAK,aAAe,GAChB,KAAK,iBACP,KAAK,KAAK,CAAC,KAAM,aAAa,CAAC,EAC/B,KAAK,KAAK,CAAC,KAAM,cAAe,MAAO,GAAG,CAAC,GAE7C,KAAK,eAAiB,KAAK,aAAe,KAC1C,KAAK,QAAU,iBACf,MACF,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACH,KAAK,aAAe,GAChB,KAAK,iBACP,KAAK,KAAK,CAAC,KAAM,aAAa,CAAC,EAC/B,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOU,CAAK,CAAC,GAE/C,KAAK,eAAiB,KAAK,aAAeA,GAC1C,KAAK,QAAU,cACf,MACF,IAAK,OACL,IAAK,QACL,IAAK,OACH,GAAI,KAAK,QAAQ,OAASC,IAAUD,EAAM,QAAU,CAAC,KAAK,MAAO,MAAME,EACvE,KAAK,KAAK,CAAC,KAAMF,EAAQ,QAAS,MAAOX,GAAOW,CAAK,CAAC,CAAC,EACvD,KAAK,QAAUV,EAAS,KAAK,OAAO,EACpC,KAEJ,CACIJ,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,OAEjB,MACF,IAAK,SACL,IAAK,SAGH,GAFAhB,EAAS,OAAO,UAAYiB,EAC5BF,EAAQf,EAAS,OAAO,KAAK,KAAK,OAAO,EACrC,CAACe,EAAO,CACV,GAAIE,EAAQ,KAAK,QAAQ,SAAW,KAAK,OAAS,KAAK,QAAQ,OAASA,GAAS,GAC/E,OAAOJ,EAAS,IAAI,MAAM,+CAA+C,CAAC,EAC5E,GAAI,KAAK,MAAO,OAAOA,EAAS,IAAI,MAAM,oCAAoC,CAAC,EAC/E,MAAMK,CACR,CAEA,GADAF,EAAQD,EAAM,CAAC,EACXC,IAAU,IACR,KAAK,UAAY,UACnB,KAAK,aAAe,KAAK,KAAK,CAAC,KAAM,QAAQ,CAAC,EAC1C,KAAK,YACP,KAAK,KAAK,CAAC,KAAM,WAAY,MAAO,KAAK,YAAY,CAAC,EACtD,KAAK,aAAe,IAEtB,KAAK,QAAU,UAEf,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,WAAW,CAAC,EAChD,KAAK,eACP,KAAK,KAAK,CAAC,KAAM,cAAe,MAAO,KAAK,YAAY,CAAC,EACzD,KAAK,aAAe,IAEtB,KAAK,QAAUV,EAAS,KAAK,OAAO,WAE7BU,EAAM,OAAS,GAAKA,EAAM,OAAO,CAAC,IAAM,KAAM,CACvD,IAAMG,EAAIH,EAAM,QAAU,EAAIP,GAAMO,EAAM,OAAO,CAAC,CAAC,EAAIT,GAAQS,CAAK,GAChE,KAAK,UAAY,SAAW,KAAK,YAAc,KAAK,iBACtD,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOG,CAAC,CAAC,GAEvC,KAAK,UAAY,SAAW,KAAK,UAAY,KAAK,gBACpD,KAAK,cAAgBA,EAEzB,MACM,KAAK,UAAY,SAAW,KAAK,YAAc,KAAK,iBACtD,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOH,CAAK,CAAC,GAE3C,KAAK,UAAY,SAAW,KAAK,UAAY,KAAK,gBACpD,KAAK,cAAgBA,GAGrBd,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,OAEjB,MACF,IAAK,OACL,IAAK,MAGH,GAFAhB,EAAS,KAAK,UAAYiB,EAC1BF,EAAQf,EAAS,KAAK,KAAK,KAAK,OAAO,EACnC,CAACe,EAAO,CACV,GAAIE,EAAQ,KAAK,QAAQ,QAAU,KAAK,MAAO,OAAOJ,EAAS,IAAI,MAAM,mDAAmD,CAAC,EAC7H,MAAMK,CACR,CAEA,GADAF,EAAQD,EAAM,CAAC,EACXC,IAAU,IACZ,KAAK,aAAe,KAAK,KAAK,CAAC,KAAM,UAAU,CAAC,EAChD,KAAK,QAAU,iBACNA,IAAU,IAAK,CACxB,GAAI,KAAK,UAAY,OAAQ,OAAOH,EAAS,IAAI,MAAM,iDAAiD,CAAC,EACzG,KAAK,KAAK,CAAC,KAAM,WAAW,CAAC,EAC7B,KAAK,QAAU,KAAK,OAAO,IAAI,EAC/B,KAAK,QAAUP,EAAS,KAAK,OAAO,CACtC,CACIJ,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,OAEjB,MACF,IAAK,QAGH,GAFAhB,EAAS,MAAM,UAAYiB,EAC3BF,EAAQf,EAAS,MAAM,KAAK,KAAK,OAAO,EACpC,CAACe,EAAO,CACV,GAAIE,EAAQ,KAAK,QAAQ,QAAU,KAAK,MAAO,OAAOJ,EAAS,IAAI,MAAM,yCAAyC,CAAC,EACnH,MAAMK,CACR,CACAF,EAAQD,EAAM,CAAC,EACfC,IAAU,MAAQ,KAAK,QAAU,SAC7Bd,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,OAEjB,MACF,IAAK,YACL,IAAK,aAGH,GAFAhB,EAAS,MAAM,UAAYiB,EAC3BF,EAAQf,EAAS,MAAM,KAAK,KAAK,OAAO,EACpC,CAACe,EAAO,CACV,GAAIE,EAAQ,KAAK,QAAQ,QAAU,KAAK,MAAO,OAAOJ,EAAS,IAAI,MAAM,yCAAyC,CAAC,EACnH,MAAMK,CACR,CAUA,GATI,KAAK,eACP,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,WAAW,CAAC,EACpD,KAAK,aAAe,GAChB,KAAK,eACP,KAAK,KAAK,CAAC,KAAM,cAAe,MAAO,KAAK,YAAY,CAAC,EACzD,KAAK,aAAe,KAGxBF,EAAQD,EAAM,CAAC,EACXC,IAAU,IACZ,KAAK,QAAU,KAAK,UAAY,YAAc,QAAU,cAC/CA,IAAU,KAAOA,IAAU,IAAK,CACzC,GAAIA,IAAU,IAAM,KAAK,UAAY,YAAc,KAAK,UAAY,YAClE,OAAOH,EAAS,IAAI,MAAM,yCAA2C,KAAK,UAAY,YAAc,IAAM,KAAO,GAAG,CAAC,EAEvH,KAAK,KAAK,CAAC,KAAMG,IAAU,IAAM,YAAc,UAAU,CAAC,EAC1D,KAAK,QAAU,KAAK,OAAO,IAAI,EAC/B,KAAK,QAAUV,EAAS,KAAK,OAAO,CACtC,CACIJ,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,OAEjB,MAEF,IAAK,cAGH,GAFAhB,EAAS,YAAY,UAAYiB,EACjCF,EAAQf,EAAS,YAAY,KAAK,KAAK,OAAO,EAC1C,CAACe,EAAO,CACV,GAAIE,EAAQ,KAAK,QAAQ,QAAU,KAAK,MAAO,OAAOJ,EAAS,IAAI,MAAM,sDAAsD,CAAC,EAChI,MAAMK,CACR,CACAF,EAAQD,EAAM,CAAC,EACf,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOC,CAAK,CAAC,EACpE,KAAK,eAAiB,KAAK,cAAgBA,GAC3C,KAAK,QAAUA,IAAU,IAAM,iBAAmB,cAC9Cd,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,OAEjB,MACF,IAAK,cAGH,GAFAhB,EAAS,YAAY,UAAYiB,EACjCF,EAAQf,EAAS,YAAY,KAAK,KAAK,OAAO,EAC1C,CAACe,EAAO,CACV,GAAIE,EAAQ,KAAK,QAAQ,QAAU,KAAK,MAAO,OAAOJ,EAAS,IAAI,MAAM,6CAA6C,CAAC,EACvH,MAAMK,CACR,CAEA,GADAF,EAAQD,EAAM,CAAC,EACXC,EACF,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOA,CAAK,CAAC,EACpE,KAAK,eAAiB,KAAK,cAAgBA,GACvCd,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,WAEZ,CACL,GAAIC,EAAQ,KAAK,QAAQ,OAAQ,CAC/B,KAAK,QAAU,iBACf,KACF,CACA,GAAI,KAAK,MAAO,CACd,KAAK,QAAUX,EAAS,KAAK,OAAO,EACpC,KACF,CACA,MAAMY,CACR,CACA,MACF,IAAK,iBAGH,GAFAlB,EAAS,eAAe,UAAYiB,EACpCF,EAAQf,EAAS,eAAe,KAAK,KAAK,OAAO,EAC7C,CAACe,EAAO,CACV,GAAIE,EAAQ,KAAK,QAAQ,QAAU,KAAK,MAAO,CAC7C,KAAK,QAAUX,EAAS,KAAK,OAAO,EACpC,KACF,CACA,MAAMY,CACR,CACAF,EAAQD,EAAM,CAAC,EACf,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOC,CAAK,CAAC,EACpE,KAAK,eAAiB,KAAK,cAAgBA,GAC3C,KAAK,QAAUA,IAAU,IAAM,kBAAoB,gBAC/Cd,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,OAEjB,MACF,IAAK,kBAGH,GAFAhB,EAAS,gBAAgB,UAAYiB,EACrCF,EAAQf,EAAS,gBAAgB,KAAK,KAAK,OAAO,EAC9C,CAACe,EAAO,CACV,GAAIE,EAAQ,KAAK,QAAQ,QAAU,KAAK,MAAO,OAAOJ,EAAS,IAAI,MAAM,mEAAmE,CAAC,EAC7I,MAAMK,CACR,CACAF,EAAQD,EAAM,CAAC,EACf,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOC,CAAK,CAAC,EACpE,KAAK,eAAiB,KAAK,cAAgBA,GAC3C,KAAK,QAAU,kBACXd,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,OAEjB,MACF,IAAK,kBAIH,GAHAhB,EAAS,gBAAgB,UAAYiB,EACrCF,EAAQf,EAAS,gBAAgB,KAAK,KAAK,OAAO,EAClDgB,EAAQD,EAAM,CAAC,EACXC,EACF,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOA,CAAK,CAAC,EACpE,KAAK,eAAiB,KAAK,cAAgBA,GACvCd,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,WAEZ,CACL,GAAIC,EAAQ,KAAK,QAAQ,OAAQ,CAC/B,KAAK,QAAU,iBACf,KACF,CACA,GAAI,KAAK,MAAO,CACd,KAAK,QAAUX,EAAS,KAAK,OAAO,EACpC,KACF,CACA,MAAMY,CACR,CACA,MACF,IAAK,iBAGH,GAFAlB,EAAS,eAAe,UAAYiB,EACpCF,EAAQf,EAAS,eAAe,KAAK,KAAK,OAAO,EAC7C,CAACe,EAAO,CACV,GAAIE,EAAQ,KAAK,QAAQ,OAAQ,CAC/B,KAAK,QAAUX,EAAS,KAAK,OAAO,EACpC,KACF,CACA,GAAI,KAAK,MAAO,CACd,KAAK,QAAU,OACf,KACF,CACA,MAAMY,CACR,CACAF,EAAQD,EAAM,CAAC,EACf,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOC,CAAK,CAAC,EACpE,KAAK,eAAiB,KAAK,cAAgBA,GAC3C,KAAK,QAAU,gBACXd,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,OAEjB,MACF,IAAK,gBAGH,GAFAhB,EAAS,cAAc,UAAYiB,EACnCF,EAAQf,EAAS,cAAc,KAAK,KAAK,OAAO,EAC5C,CAACe,EAAO,CACV,GAAIE,EAAQ,KAAK,QAAQ,OAAQ,CAC/B,KAAK,QAAU,iBACf,KACF,CACA,GAAI,KAAK,MAAO,OAAOJ,EAAS,IAAI,MAAM,mDAAmD,CAAC,EAC9F,MAAMK,CACR,CACAF,EAAQD,EAAM,CAAC,EACf,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOC,CAAK,CAAC,EACpE,KAAK,eAAiB,KAAK,cAAgBA,GAC3C,KAAK,QAAU,iBACXd,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,OAEjB,MACF,IAAK,iBAGH,GAFAhB,EAAS,eAAe,UAAYiB,EACpCF,EAAQf,EAAS,eAAe,KAAK,KAAK,OAAO,EAC7C,CAACe,EAAO,CACV,GAAIE,EAAQ,KAAK,QAAQ,QAAU,KAAK,MAAO,OAAOJ,EAAS,IAAI,MAAM,kEAAkE,CAAC,EAC5I,MAAMK,CACR,CACAF,EAAQD,EAAM,CAAC,EACf,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOC,CAAK,CAAC,EACpE,KAAK,eAAiB,KAAK,cAAgBA,GAC3C,KAAK,QAAU,iBACXd,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,OAEjB,MACF,IAAK,iBAIH,GAHAhB,EAAS,eAAe,UAAYiB,EACpCF,EAAQf,EAAS,eAAe,KAAK,KAAK,OAAO,EACjDgB,EAAQD,EAAM,CAAC,EACXC,EACF,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOA,CAAK,CAAC,EACpE,KAAK,eAAiB,KAAK,cAAgBA,GACvCd,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,WAEZ,CACL,GAAIC,EAAQ,KAAK,QAAQ,QAAU,KAAK,MAAO,CAC7C,KAAK,QAAUX,EAAS,KAAK,OAAO,EACpC,KACF,CACA,MAAMY,CACR,CACA,MACF,IAAK,OAGH,GAFAlB,EAAS,GAAG,UAAYiB,EACxBF,EAAQf,EAAS,GAAG,KAAK,KAAK,OAAO,EACjC,CAACe,EAAO,CACV,GAAIE,EAAQ,KAAK,QAAQ,OAAQ,CAC/B,GAAI,KAAK,eAAgB,CACvB,KAAK,QAAU,QACf,KACF,CACA,OAAOJ,EAAS,IAAI,MAAM,kDAAkD,CAAC,CAC/E,CACA,MAAMK,CACR,CACAF,EAAQD,EAAM,CAAC,EACX,KAAK,eACP,KAAK,gBAAkB,KAAK,KAAK,CAAC,KAAM,WAAW,CAAC,EACpD,KAAK,aAAe,GAChB,KAAK,eACP,KAAK,KAAK,CAAC,KAAM,cAAe,MAAO,KAAK,YAAY,CAAC,EACzD,KAAK,aAAe,KAGpBb,EACF,KAAK,QAAU,KAAK,QAAQ,MAAMc,EAAM,MAAM,EAE9CC,GAASD,EAAM,OAEjB,KACJ,CAEF,CAACd,IAAa,KAAK,QAAU,KAAK,QAAQ,MAAMe,CAAK,GACrDJ,EAAS,IAAI,CACf,CACF,EACAH,EAAO,OAASA,EAAO,KACvBA,EAAO,KAAK,YAAcA,EAE1BZ,EAAO,QAAUY,IChiBjB,IAAAU,EAAAC,EAAA,CAAAC,GAAAC,IAAA,cAEA,IAAMC,GAAOC,GAAUA,EAAO,GAAG,OAAQC,GAAQD,EAAO,KAAKC,EAAK,KAAMA,EAAK,KAAK,CAAC,EAEnFH,EAAO,QAAUC,KCJjB,IAAAG,EAAAC,EAAA,CAAAC,GAAAC,IAAA,cAEA,IAAMC,EAAS,IACTC,GAAO,IAEPC,EAAOC,GAAWF,GAAK,IAAID,EAAOG,CAAO,CAAC,EAEhDD,EAAK,OAASF,EACdE,EAAK,OAASF,EAAO,OAErBD,EAAO,QAAUG,ICVjB,IAAAE,EAAAC,EAAA,CAAAC,GAAAC,IAAA,cAEA,GAAM,CAAC,UAAAC,EAAS,EAAI,QAAQ,QAAQ,EAE9BC,EAAN,MAAMC,UAAmBF,EAAU,CACjC,OAAO,aAAaG,EAAQC,EAAW,CACrC,OAAOC,GAAS,CACd,IAAMC,EAAOD,EAAM,KAAKD,CAAS,EACjC,OACGE,EAAK,SAAWH,EAAO,QAAUG,IAASH,GAC1CG,EAAK,OAASH,EAAO,QAAUG,EAAK,OAAO,EAAGH,EAAO,MAAM,IAAMA,GAAUG,EAAK,OAAOH,EAAO,OAAQC,EAAU,MAAM,IAAMA,CAEjI,CACF,CAEA,OAAO,aAAaG,EAAQH,EAAW,CACrC,OAAOC,GAASE,EAAO,KAAKF,EAAM,KAAKD,CAAS,CAAC,CACnD,CAEA,OAAO,iBAAiBI,EAAO,CAC7B,MAAO,IAAMA,CACf,CAEA,YAAYC,EAAS,CACnB,MAAM,OAAO,OAAO,CAAC,EAAGA,EAAS,CAAC,mBAAoB,GAAM,mBAAoB,EAAI,CAAC,CAAC,EACtF,KAAK,WAAa,KAAK,OACvB,KAAK,OAAS,CAAC,EAEf,IAAMC,EAASD,GAAWA,EAAQ,OAChCL,EAAaK,GAAWA,EAAQ,eAAkB,IAChD,OAAOC,GAAU,SACnB,KAAK,QAAUR,EAAW,aAAaQ,EAAQN,CAAS,EAC/C,OAAOM,GAAU,WAC1B,KAAK,QAAUA,EACNA,aAAkB,SAC3B,KAAK,QAAUR,EAAW,aAAaQ,EAAQN,CAAS,GAG1D,IAAMO,EAAcF,GAAWA,EAAQ,YACnC,OAAOE,GAAe,WACxB,KAAK,aAAeA,EAEpB,KAAK,aAAeT,EAAW,iBAAiBS,GAAeT,EAAW,kBAAkB,EAE9F,KAAK,uBAAyBO,GAAWA,EAAQ,sBAEjD,KAAK,YAAc,GACfA,IACF,iBAAkBA,IAAY,KAAK,YAAcA,EAAQ,cACzD,eAAgBA,IAAY,KAAK,YAAcA,EAAQ,aAGzD,KAAK,MAAQA,GAAWA,EAAQ,KAChC,KAAK,eAAiB,EACxB,CAEA,OAAOG,EAAOC,EAAGC,EAAU,CAEzB,OAAQF,EAAM,KAAM,CAClB,IAAK,cACL,IAAK,aACL,IAAK,cACL,IAAK,cACL,IAAK,YACL,IAAK,YACL,IAAK,aACC,OAAO,KAAK,OAAO,KAAK,OAAO,OAAS,CAAC,GAAK,UAEhD,EAAE,KAAK,OAAO,KAAK,OAAO,OAAS,CAAC,EAEtC,MACF,IAAK,WACH,KAAK,OAAO,KAAK,OAAO,OAAS,CAAC,EAAIA,EAAM,MAC5C,MACF,IAAK,cACC,KAAK,iBAAmB,aAAe,OAAO,KAAK,OAAO,KAAK,OAAO,OAAS,CAAC,GAAK,UAEvF,EAAE,KAAK,OAAO,KAAK,OAAO,OAAS,CAAC,EAEtC,MACF,IAAK,cACC,KAAK,iBAAmB,aAAe,OAAO,KAAK,OAAO,KAAK,OAAO,OAAS,CAAC,GAAK,UAEvF,EAAE,KAAK,OAAO,KAAK,OAAO,OAAS,CAAC,EAEtC,KACJ,CAGA,GAFA,KAAK,eAAiBA,EAAM,KAExB,KAAK,YAAYA,CAAK,EACxB,OAAOE,EAAS,IAAI,EAGtB,OAAQF,EAAM,KAAM,CAClB,IAAK,cACH,KAAK,OAAO,KAAK,IAAI,EACrB,MACF,IAAK,aACH,KAAK,OAAO,KAAK,EAAE,EACnB,MACF,IAAK,YACL,IAAK,WACH,KAAK,OAAO,IAAI,EAChB,KACJ,CACAE,EAAS,IAAI,CACf,CAEA,YAAYF,EAAOC,EAAGC,EAAU,CAE9B,OADA,KAAK,KAAKF,CAAK,EACPA,EAAM,KAAM,CAClB,IAAK,cACL,IAAK,aACH,EAAE,KAAK,OACP,MACF,IAAK,YACL,IAAK,WACH,EAAE,KAAK,OACP,KACJ,CACK,KAAK,SACR,KAAK,WAAa,KAAK,MAAQ,KAAK,MAAQ,KAAK,QAEnDE,EAAS,IAAI,CACf,CAEA,MAAMF,EAAOC,EAAGC,EAAU,CACxB,KAAK,KAAKF,CAAK,EACfE,EAAS,IAAI,CACf,CAEA,YAAYF,EAAOC,EAAGC,EAAU,CAC9B,OAAQF,EAAM,KAAM,CAClB,IAAK,cACL,IAAK,aACH,EAAE,KAAK,OACP,MACF,IAAK,YACL,IAAK,WACH,EAAE,KAAK,OACP,KACJ,CACK,KAAK,SACR,KAAK,WAAa,KAAK,MAAQ,KAAK,MAAQ,KAAK,QAEnDE,EAAS,IAAI,CACf,CAEA,MAAMF,EAAOC,EAAGC,EAAU,CACxBA,EAAS,IAAI,CACf,CACF,EAEAb,EAAW,mBAAqB,CAAC,CAAC,KAAM,YAAa,MAAO,IAAI,CAAC,EAEjE,IAAMc,EAAY,CAACC,EAAMC,IACvB,SAASL,EAAOC,EAAGC,EAAU,CAC3B,GAAI,KAAK,UAAW,CAClB,IAAMI,EAAW,KAAK,UAGtB,GAFA,KAAK,UAAY,GACjB,KAAK,WAAa,KAAK,MAAQ,KAAK,MAAQ,KAAK,OAC7CA,IAAaN,EAAM,KACrB,KAAK,KAAKA,CAAK,MAEf,QAAO,KAAK,WAAWA,EAAOC,EAAGC,CAAQ,CAE7C,MACE,KAAK,KAAKF,CAAK,EACXA,EAAM,OAASI,IACjB,KAAK,UAAYC,GAGrBH,EAAS,IAAI,CACf,EAEFb,EAAW,UAAU,YAAcc,EAAU,YAAa,aAAa,EACvEd,EAAW,UAAU,YAAcc,EAAU,YAAa,aAAa,EACvEd,EAAW,UAAU,SAAWc,EAAU,SAAU,UAAU,EAE9D,IAAMI,EAAY,CAACH,EAAMC,IACvB,SAASL,EAAOC,EAAGC,EAAU,CAC3B,GAAI,KAAK,UAAW,CAClB,IAAMI,EAAW,KAAK,UAGtB,GAFA,KAAK,UAAY,GACjB,KAAK,WAAa,KAAK,MAAQ,KAAK,MAAQ,KAAK,OAC7CA,IAAaN,EAAM,KACrB,OAAO,KAAK,WAAWA,EAAOC,EAAGC,CAAQ,CAE7C,MACMF,EAAM,OAASI,IACjB,KAAK,UAAYC,GAGrBH,EAAS,IAAI,CACf,EAEFb,EAAW,UAAU,YAAckB,EAAU,YAAa,aAAa,EACvElB,EAAW,UAAU,YAAckB,EAAU,YAAa,aAAa,EACvElB,EAAW,UAAU,SAAWkB,EAAU,SAAU,UAAU,EAE9DpB,EAAO,QAAUE,ICxMjB,IAAAmB,EAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,GAAM,CAAC,MAAAC,EAAK,EAAI,IAEVC,GAAS,IAETC,GAAa,CAACC,EAAIC,IACtBJ,GAAM,CAAC,IAAIC,GAAOG,CAAO,EAAGD,EAAGC,CAAO,CAAC,EAAG,OAAO,OAAO,CAAC,EAAGA,EAAS,CAAC,mBAAoB,GAAO,mBAAoB,EAAI,CAAC,CAAC,EAE7HL,GAAO,QAAUG,KCTjB,IAAAG,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAa,IACbC,GAAa,IAEbC,EAAN,MAAMC,UAAeH,EAAW,CAC9B,OAAO,KAAKI,EAAS,CACnB,OAAO,IAAID,EAAOC,CAAO,CAC3B,CAEA,OAAO,WAAWA,EAAS,CACzB,OAAOH,GAAWE,EAAO,KAAMC,CAAO,CACxC,CAEA,YAAYA,EAAS,CACnB,MAAMA,CAAO,EACb,KAAK,MAAQ,GACb,KAAK,WAAa,CAAC,CACrB,CAEA,OAAOC,EAAU,CACf,KAAK,WAAW,EAChBA,EAAS,IAAI,CACf,CAEA,YAAYC,EAAO,CACjB,OAAQA,EAAM,KAAM,CAClB,IAAK,cACC,KAAK,QAAQ,KAAK,OAAQA,CAAK,IACjC,KAAK,WAAW,EAChB,KAAK,KAAKA,CAAK,EACf,KAAK,WAAW,KAAK,IAAI,GAE3B,MACF,IAAK,aACC,KAAK,QAAQ,KAAK,OAAQA,CAAK,IACjC,KAAK,WAAW,EAChB,KAAK,KAAKA,CAAK,EACf,KAAK,WAAW,KAAK,EAAE,GAEzB,MACF,IAAK,YACL,IAAK,YACL,IAAK,aACL,IAAK,cACL,IAAK,cACC,KAAK,QAAQ,KAAK,OAAQA,CAAK,IACjC,KAAK,WAAW,EAChB,KAAK,KAAKA,CAAK,GAEjB,MACF,IAAK,cACC,KAAK,QAAQ,KAAK,OAAQA,CAAK,GACjC,KAAK,WAAW,EAChB,KAAK,KAAKA,CAAK,EACf,KAAK,WAAa,KAAK,aAEvB,KAAK,WAAa,KAAK,YAEzB,MACF,IAAK,cACC,KAAK,QAAQ,KAAK,OAAQA,CAAK,GACjC,KAAK,WAAW,EAChB,KAAK,KAAKA,CAAK,EACf,KAAK,WAAa,KAAK,aAEvB,KAAK,WAAa,KAAK,YAEzB,KACJ,CACA,MAAO,EACT,CAEA,YAAa,CACX,IAAMC,EAAQ,KAAK,OACjBC,EAAO,KAAK,WACZC,EAAcF,EAAM,OACpBG,EAAaF,EAAK,OAGhBG,EAAe,EACnB,QAAWC,EAAI,KAAK,IAAIH,EAAaC,CAAU,EAAGC,EAAeC,GAAKL,EAAMI,CAAY,IAAMH,EAAKG,CAAY,EAAG,EAAEA,EAAa,CAGjI,QAASE,EAAIH,EAAa,EAAGG,EAAIF,EAAc,EAAEE,EAC/C,KAAK,KAAK,CAAC,KAAM,OAAOL,EAAKK,CAAC,GAAK,SAAW,WAAa,WAAW,CAAC,EAEzE,GAAIF,EAAeD,EACjB,GAAIC,EAAeF,EAAa,CAC9B,GAAI,OAAOF,EAAMI,CAAY,GAAK,SAAU,CAC1C,IAAMG,EAAMP,EAAMI,CAAY,EAC1B,KAAK,cACP,KAAK,KAAK,CAAC,KAAM,UAAU,CAAC,EAC5B,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOG,CAAG,CAAC,EAC3C,KAAK,KAAK,CAAC,KAAM,QAAQ,CAAC,GAE5B,KAAK,KAAK,CAAC,KAAM,WAAY,MAAOA,CAAG,CAAC,CAC1C,CACA,EAAEH,CACJ,MACE,KAAK,KAAK,CAAC,KAAM,OAAOH,EAAKG,CAAY,GAAK,SAAW,WAAa,WAAW,CAAC,EAKtF,QAASE,EAAIF,EAAcE,EAAIJ,EAAa,EAAEI,EAAG,CAC/C,IAAMC,EAAMP,EAAMM,CAAC,EACf,OAAOC,GAAO,SACZA,GAAO,GACT,KAAK,KAAK,CAAC,KAAM,YAAY,CAAC,EAEvB,OAAOA,GAAO,WACvB,KAAK,KAAK,CAAC,KAAM,aAAa,CAAC,EAC3B,KAAK,cACP,KAAK,KAAK,CAAC,KAAM,UAAU,CAAC,EAC5B,KAAK,KAAK,CAAC,KAAM,cAAe,MAAOA,CAAG,CAAC,EAC3C,KAAK,KAAK,CAAC,KAAM,QAAQ,CAAC,GAE5B,KAAK,KAAK,CAAC,KAAM,WAAY,MAAOA,CAAG,CAAC,EAE5C,CAGA,KAAK,WAAa,MAAM,UAAU,OAAO,KAAKP,CAAK,CACrD,CACF,EACAL,EAAO,OAASA,EAAO,KACvBA,EAAO,KAAK,YAAcA,EAE1BH,GAAO,QAAUG,ICjIjB,IAAAa,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAe,QAAQ,QAAQ,EAE/BC,GAAcC,GAClB,UAAY,CACN,KAAK,KACP,KAAK,KAAO,GAEZ,KAAK,MAAM,KAAK,KAAK,QAAS,KAAK,GAAG,EAExC,KAAK,QAAU,IAAIA,EACnB,KAAK,IAAM,IACb,EAEIC,EAAN,MAAMC,UAAkBJ,EAAa,CACnC,OAAO,UAAUK,EAAQC,EAAS,CAChC,OAAO,IAAIF,EAAUE,CAAO,EAAE,UAAUD,CAAM,CAChD,CAEA,YAAYC,EAAS,CACnB,MAAM,EACN,KAAK,MAAQ,CAAC,EACd,KAAK,QAAU,KAAK,IAAM,KAC1B,KAAK,KAAO,GACRA,IACF,KAAK,QAAU,OAAOA,EAAQ,SAAW,YAAcA,EAAQ,QAC3D,KAAK,UACP,KAAK,YAAc,KAAK,WAAa,KAAK,uBAExCA,EAAQ,iBACV,KAAK,YAAc,KAAK,aAG9B,CAEA,UAAUD,EAAQ,CAChB,OAAAA,EAAO,GAAG,OAAQE,GAAS,CACrB,KAAKA,EAAM,IAAI,IACjB,KAAKA,EAAM,IAAI,EAAEA,EAAM,KAAK,EACxB,KAAK,MAAM,KAAK,KAAK,OAAQ,IAAI,EAEzC,CAAC,EACM,IACT,CAEA,IAAI,OAAQ,CACV,OAAQ,KAAK,MAAM,QAAU,IAAM,KAAK,KAAO,EAAI,EACrD,CAEA,IAAI,MAAO,CACT,IAAMC,EAAO,CAAC,EACd,QAASC,EAAI,EAAGA,EAAI,KAAK,MAAM,OAAQA,GAAK,EAAG,CAC7C,IAAMC,EAAM,KAAK,MAAMD,EAAI,CAAC,EAC5BD,EAAK,KAAKE,IAAQ,KAAO,KAAK,MAAMD,CAAC,EAAE,OAASC,CAAG,CACrD,CACA,OAAOF,CACT,CAEA,YAAYG,EAAO,CACjB,GAAIA,EAAQ,KAAK,MACf,GAAIA,EAAO,CACT,IAAMC,EAASD,EAAQ,GAAM,EAC7B,KAAK,QAAU,KAAK,MAAMC,CAAK,EAC/B,KAAK,IAAM,KAAK,MAAMA,EAAQ,CAAC,EAC/B,KAAK,MAAM,OAAOA,CAAK,CACzB,MACE,KAAK,MAAQ,CAAC,EACd,KAAK,QAAU,KAAK,IAAM,KAC1B,KAAK,KAAO,GAGhB,OAAO,IACT,CAEA,QAAQL,EAAO,CACb,YAAKA,EAAM,IAAI,GAAK,KAAKA,EAAM,IAAI,EAAEA,EAAM,KAAK,EACzC,IACT,CAEA,SAASM,EAAO,CACd,KAAK,IAAMA,CACb,CAIA,YAAYA,EAAO,CACjB,KAAK,WAAW,WAAWA,CAAK,CAAC,CACnC,CACA,WAAY,CACV,KAAK,WAAW,IAAI,CACtB,CACA,WAAY,CACV,KAAK,WAAW,EAAI,CACtB,CACA,YAAa,CACX,KAAK,WAAW,EAAK,CACvB,CAIA,WAAY,CACV,GAAI,KAAK,MAAM,OAAQ,CACrB,IAAMA,EAAQ,KAAK,QACnB,KAAK,IAAM,KAAK,MAAM,IAAI,EAC1B,KAAK,QAAU,KAAK,MAAM,IAAI,EAC9B,KAAK,WAAWA,CAAK,CACvB,MACE,KAAK,KAAO,EAEhB,CAKA,WAAWA,EAAO,CACZ,KAAK,KACP,KAAK,QAAUA,EAEX,KAAK,mBAAmB,MAC1B,KAAK,QAAQ,KAAKA,CAAK,GAEvB,KAAK,QAAQ,KAAK,GAAG,EAAIA,EACzB,KAAK,IAAM,KAGjB,CACA,sBAAsBA,EAAO,CACvB,KAAK,KACP,KAAK,QAAU,KAAK,QAAQ,GAAIA,CAAK,EAEjC,KAAK,mBAAmB,OAC1BA,EAAQ,KAAK,QAAQ,GAAK,KAAK,QAAQ,OAAQA,CAAK,EACpD,KAAK,QAAQ,KAAKA,CAAK,EACnBA,IAAU,QACZ,OAAO,KAAK,QAAQ,KAAK,QAAQ,OAAS,CAAC,IAG7CA,EAAQ,KAAK,QAAQ,KAAK,IAAKA,CAAK,EAChCA,IAAU,SACZ,KAAK,QAAQ,KAAK,GAAG,EAAIA,GAE3B,KAAK,IAAM,KAGjB,CACF,EAEAV,EAAU,UAAU,YAAcA,EAAU,UAAU,WACtDA,EAAU,UAAU,YAAcF,GAAY,MAAM,EACpDE,EAAU,UAAU,WAAaF,GAAY,KAAK,EAClDE,EAAU,UAAU,SAAWA,EAAU,UAAU,UAEnDJ,GAAO,QAAUI,ICzJjB,IAAAW,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,GAAM,CAAC,UAAAC,EAAS,EAAI,QAAQ,QAAQ,EAC9BC,GAAY,KAEZC,EAAN,KAAc,CACZ,YAAYC,EAAc,CACxB,KAAK,MAAQA,CACf,CACA,aAAc,CACZ,EAAE,KAAK,KACT,CACA,WAAY,CACV,EAAE,KAAK,KACT,CACA,YAAa,CACX,EAAE,KAAK,KACT,CACA,UAAW,CACT,EAAE,KAAK,KACT,CACF,EAEMC,EAAN,cAAyBJ,EAAU,CACjC,YAAYK,EAAS,CACnB,MAAM,OAAO,OAAO,CAAC,EAAGA,EAAS,CAAC,mBAAoB,GAAM,mBAAoB,EAAI,CAAC,CAAC,EAClFA,IACF,KAAK,aAAeA,EAAQ,aAC5B,KAAK,iBAAmBA,EAAQ,kBAE9B,OAAO,KAAK,cAAgB,aAC9B,KAAK,QAAU,KAAK,YAEtB,KAAK,WAAa,KAAK,OAAS,KAAK,QACrC,KAAK,WAAa,IAAIJ,GAAUI,CAAO,CACzC,CAEA,WAAWC,EAAOC,EAAUC,EAAU,CAChC,KAAK,WAAWF,EAAM,IAAI,IAC5B,KAAK,WAAWA,EAAM,IAAI,EAAEA,EAAM,KAAK,EACnC,KAAK,WAAW,QAAU,KAAK,QACjC,KAAK,MAAM,GAGfE,EAAS,IAAI,CACf,CAEA,QAAQF,EAAOC,EAAUC,EAAU,CACjC,GAAI,KAAK,WAAWF,EAAM,IAAI,EAAG,CAC/B,KAAK,WAAWA,EAAM,IAAI,EAAEA,EAAM,KAAK,EACvC,IAAMG,EAAS,KAAK,aAAa,KAAK,UAAU,EAChD,GAAIA,EACF,OAAI,KAAK,WAAW,QAAU,KAAK,SACjC,KAAK,MAAM,EACX,KAAK,WAAa,KAAK,SAEzB,KAAK,WAAa,KAAK,QAChBD,EAAS,IAAI,EAEtB,GAAIC,IAAW,GACb,YAAK,iBAAmB,KAAK,WAC7B,KAAK,WAAa,IAAIP,EAAQ,KAAK,iBAAiB,KAAK,EACzD,KAAK,iBAAiB,YAAY,KAAK,MAAM,EACzC,KAAK,WAAW,QAAU,KAAK,SACjC,KAAK,WAAa,KAAK,iBACvB,KAAK,WAAa,KAAK,SAEzB,KAAK,WAAa,KAAK,QAChBM,EAAS,IAAI,EAElB,KAAK,WAAW,QAAU,KAAK,QACjC,KAAK,MAAM,CAAC,KAAK,gBAAgB,CAErC,CACAA,EAAS,IAAI,CACf,CAEA,QAAQF,EAAOC,EAAUC,EAAU,CAC7B,KAAK,WAAWF,EAAM,IAAI,IAC5B,KAAK,WAAWA,EAAM,IAAI,EAAEA,EAAM,KAAK,EACnC,KAAK,WAAW,QAAU,KAAK,SACjC,KAAK,MAAM,EACX,KAAK,WAAa,KAAK,UAG3BE,EAAS,IAAI,CACf,CAEA,QAAQF,EAAOC,EAAUC,EAAU,CAC7B,KAAK,WAAWF,EAAM,IAAI,IAC5B,KAAK,WAAWA,EAAM,IAAI,EAAEA,EAAM,KAAK,EACnC,KAAK,WAAW,QAAU,KAAK,SACjC,KAAK,WAAa,KAAK,iBACvB,KAAK,WAAa,KAAK,UAG3BE,EAAS,IAAI,CACf,CACF,EAEAT,GAAO,QAAUK,ICpGjB,IAAAM,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAa,KACbC,GAAa,IAEbC,EAAN,MAAMC,UAAoBH,EAAW,CACnC,OAAO,KAAKI,EAAS,CACnB,OAAO,IAAID,EAAYC,CAAO,CAChC,CAEA,OAAO,WAAWA,EAAS,CACzB,OAAOH,GAAWE,EAAY,KAAMC,CAAO,CAC7C,CAEA,YAAYA,EAAS,CACnB,MAAMA,CAAO,EACb,KAAK,OAAS,EACd,KAAK,SAAW,CAClB,CAEA,MAAMC,EAAOC,EAAGC,EAAU,CAExB,OAAIF,EAAM,OAAS,aACVE,EAAS,IAAI,MAAM,sCAAsC,CAAC,GAEnE,KAAK,WAAa,KAAK,QAChB,KAAK,WAAWF,EAAOC,EAAGC,CAAQ,EAC3C,CAEA,MAAMC,EAAS,CACT,KAAK,WAAW,QAAQ,SACtBA,GACF,EAAE,KAAK,SACP,KAAK,WAAW,QAAQ,IAAI,GAE5B,KAAK,KAAK,CAAC,IAAK,KAAK,WAAY,MAAO,KAAK,WAAW,QAAQ,IAAI,CAAC,CAAC,EAG5E,CACF,EACAN,EAAY,YAAcA,EAAY,KACtCA,EAAY,KAAK,YAAcA,EAE/BH,GAAO,QAAUG,IC3CjB,IAAAO,GAAA,GAAAC,GAAAD,GAAA,aAAAE,KAAA,eAAAC,GAAAH,IAAA,IAAAI,EAAiC,wBCAjC,IAAAC,EAA+B,yBAC/BC,GAA0B,gBAC1BC,GAA0C,cAC1CC,EAAoB,0BACpBC,EAAkC,gBAClCC,GAAqB,cACrBC,GAA4B,wBCN5B,IAAAC,EAA8C,wBAC9CC,EAAiB,mBACjBC,GAAe,iBAGf,IAAAC,GAAsB,OACtBC,GAAuB,OACvBC,GAAuB,QACvBC,GAA4B,QAMrB,IAAMC,IAAuB,IAAM,CACxC,GAAI,CACF,GAAAC,QAAG,UAAU,cAAY,YAAa,CAAE,UAAW,EAAK,CAAC,CAC3D,MAAc,CACZ,QAAQ,IAAI,8BAA8B,CAC5C,CACA,OAAO,cAAY,WACrB,GAAG,EAEUC,GAET,cAAY,YAAY,MAAM,EAAAC,QAAK,GAAG,EAAE,KAAMC,GACxC,EAAAA,EAAK,WAAW,aAAa,CAIlC,GAAK,oBAIH,SAASC,EAAUC,EAAsB,CAC9C,OAAO,EAAAH,QAAK,KAAKH,GAAaM,CAAI,CACpC,CAaA,eAAsBC,GAAKC,EAA2B,CACpD,OAAO,IAAI,QAASC,GAAY,WAAWA,EAASD,CAAE,CAAC,CACzD,CAqHO,SAASE,GAAgBC,EAAgE,CAC9F,IAAMC,EAAyB,CAC7B,MAAO,QAAM,MAAM,SACnB,MAAOD,EAAc,MACrB,QAASA,EAAc,OACzB,EAEIE,EAEAF,EAAc,aAChBE,EAAa,IAAI,gBACjBD,EAAQ,cAAgB,CACtB,MAAO,SACP,SAAU,IAAM,CACdC,GAAY,MAAM,EAClBC,EAAM,KAAK,CACb,CACF,GAGF,IAAMA,EAAQ,IAAI,QAAMF,CAAO,EAC/B,OAAAE,EAAM,KAAK,EACJD,CACT,CAEA,eAAsBE,GAAiBC,EAAeC,EAA6B,CACjF,GAAIA,EAAM,MAAQ,aAAc,CAC9B,QAAQ,IAAI,YAAY,EACxB,MACF,CAEA,QAAQ,IAAI,GAAGD,CAAK,KAAKC,CAAK,EAAE,EAChC,IAAMC,EAAUD,EAAoB,QAAWA,EAAqB,SAAW,GAAGA,CAAK,GACjFL,EAAyB,CAC7B,MAAO,QAAM,MAAM,QACnB,MAAOI,EACP,QAASE,EACT,cAAe,CACb,MAAO,iBACP,SAAU,IAAM,CACd,YAAU,KAAKA,CAAM,CACvB,CACF,CACF,EAGA,MADc,IAAI,QAAMN,CAAO,EACnB,KAAK,CACnB,CAaK,MAAM,UAAU,QACnB,MAAM,UAAU,MAAQ,UAAuC,CAC7D,OAAO,KAAK,OAAS,EAAI,KAAK,CAAC,EAAI,MACrC,GAGG,MAAM,UAAU,OACnB,MAAM,UAAU,KAAO,UAAuC,CAC5D,OAAO,KAAK,OAAS,EAAI,KAAK,KAAK,OAAS,CAAC,EAAI,MACnD,GAGG,MAAM,UAAU,cACnB,MAAM,UAAU,YAAc,UAAiC,CAC7D,OAAI,KAAK,YACA,KAAK,OAAS,KAAK,YAErB,EACT,GAWG,OAAO,WACV,OAAO,SAAW,UClQpB,IAAAO,GAAoC,wBAEvBC,KAAiD,wBAAoB,EFQlF,IAAMC,MAAQ,cAAU,MAAI,EAiGfC,IAAc,IAAM,CAC/B,GAAIC,EAAY,gBAAkBA,EAAY,eAAe,OAAS,EACpE,SAAO,EAAAC,MAAUD,EAAY,eAAgB,KAAM,IAAI,EACzD,GAAI,CACF,SAAO,YAAS,gBAAiB,CAAE,SAAU,MAAO,CAAC,EAAE,KAAK,CAC9D,MAAQ,CACN,SAAO,SAAK,EAAE,CAAC,EAAE,MAAM,SAAS,OAAO,EAAI,gBAAkB,YAC/D,CACF,GAAG,EAEUE,GAAYC,MAAmB,EAAAF,MAAUF,GAAYI,CAAM,EAC3DC,GAAiB,IAAMF,GAAS,UAAU,EAIjDG,GAA2BC,EAAU,kBAAkB,EACvDC,GAAyBD,EAAU,cAAc,EACjDE,GAAsBF,EAAU,WAAW,EAyMjD,eAAsBG,GAAeC,EAAiBC,EAAyC,CAC7F,IAAIC,EAAM,WAAWC,GAAqB,CAAC,GACvCH,IACFE,GAAO,aAET,MAAME,GAASF,EAAKD,CAAM,CAC5B,CAoKA,SAASI,IAA+B,CACtC,OAAOC,EAAY,WAAa,eAAiB,iBACnD,CAkBA,eAAeC,GAASC,EAAaC,EAA+C,CAClF,GAAI,CACF,IAAMC,EAAM,MAAMC,GAAY,EAC9B,OAAO,MAAMC,GAAM,GAAGC,GAAe,CAAC,IAAIL,CAAG,GAAI,CAAE,OAAQC,GAAQ,OAAQ,IAAKC,EAAK,UAAW,GAAK,KAAO,IAAK,CAAC,CACpH,OAASI,EAAK,CACZ,IAAMC,EAAUD,EAChB,MAAIE,EAAY,gBAAkBD,GAAWA,EAAQ,OAAS,KAC5DA,EAAQ,OAAS,iCAAiCC,EAAY,cAAc,GACtED,GAEAD,CAEV,CACF,CAEA,eAAeH,IAA0C,CACvD,IAAMM,KAAc,EAAAC,MAAU,eAAY,WAAY,YAAY,EAClE,GAAI,CACF,MAAS,SAAOD,EAAa,GAAAE,UAAa,IAAI,CAChD,MAAQ,CACN,MAAS,QAAMF,EAAa,GAAK,CACnC,CACA,IAAMP,EAAM,QAAQ,IACpB,OAAAA,EAAI,aAAkBO,EAGtBP,EAAI,iBAA4BU,GACzBV,CACT,CD1hBA,IAAOW,GAAQ,SAA2B,CACxC,GAAI,CACF,IAAMC,EAAQC,GAAgB,CAAE,MAAO,4BAA8B,OAAO,SAAU,WAAY,EAAK,CAAC,EACxG,MAAMC,GAAeC,EAAY,eAAgBH,CAAK,KACtD,aAAU,QAAM,MAAM,QAAS,mBAAmB,CACpD,OAASI,EAAK,CACZ,MAAMC,GAAiB,iBAAkBD,CAAY,EACrD,MAAME,GAAK,GAAI,CACjB,CACF", "names": ["require_stream_chain", "__commonJSMin", "exports", "module", "Readable", "Writable", "Duplex", "Transform", "none", "finalSymbol", "manySymbol", "final", "value", "many", "values", "isFinal", "o", "isMany", "getFinalValue", "getMany<PERSON><PERSON>ues", "runAsyncGenerator", "gen", "stream", "data", "Chain", "wrapFunction", "fn", "chunk", "encoding", "callback", "result", "error", "wrapArray", "fns", "i", "isReadableNodeStream", "obj", "isWritableNodeStream", "isDuplexNodeStream", "_Chain", "options", "index", "s", "output", "_1", "_2", "e", "require_Utf8Stream", "__commonJSMin", "exports", "module", "Transform", "StringDecoder", "Utf8Stream", "options", "chunk", "encoding", "callback", "_", "require_Parser", "__commonJSMin", "exports", "module", "Utf8Stream", "patterns", "MAX_PATTERN_SIZE", "noSticky", "key", "src", "values", "expected", "fromHex", "s", "codes", "<PERSON><PERSON><PERSON>", "_<PERSON><PERSON>r", "options", "callback", "error", "match", "value", "index", "main", "t", "require_emit", "__commonJSMin", "exports", "module", "emit", "stream", "item", "require_stream_json", "__commonJSMin", "exports", "module", "<PERSON><PERSON><PERSON>", "emit", "make", "options", "require_FilterBase", "__commonJSMin", "exports", "module", "Transform", "FilterBase", "_FilterBase", "string", "separator", "stack", "path", "regExp", "array", "options", "filter", "replacement", "chunk", "_", "callback", "passValue", "last", "post", "expected", "skip<PERSON><PERSON><PERSON>", "require_withParser", "__commonJSMin", "exports", "module", "chain", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "fn", "options", "require_Filter", "__commonJSMin", "exports", "module", "FilterBase", "<PERSON><PERSON><PERSON><PERSON>", "Filter", "_Filter", "options", "callback", "chunk", "stack", "last", "stack<PERSON>ength", "last<PERSON><PERSON><PERSON>", "common<PERSON><PERSON>th", "n", "i", "key", "require_Assembler", "__commonJSMin", "exports", "module", "EventEmitter", "startObject", "Ctr", "Assembler", "_Assembler", "stream", "options", "chunk", "path", "i", "key", "level", "index", "value", "require_StreamBase", "__commonJSMin", "exports", "module", "Transform", "Assembler", "Counter", "initialDepth", "StreamBase", "options", "chunk", "encoding", "callback", "result", "require_StreamArray", "__commonJSMin", "exports", "module", "StreamBase", "<PERSON><PERSON><PERSON><PERSON>", "StreamArray", "_StreamArray", "options", "chunk", "_", "callback", "discard", "upgrade_exports", "__export", "upgrade_default", "__toCommonJS", "import_api", "import_child_process", "import_util", "import_fs", "fs", "import_path", "import_os", "import_api", "import_api", "import_path", "import_fs", "import_stream_chain", "import_stream_json", "import_Filter", "import_StreamArray", "supportPath", "fs", "bundleIdentifier", "path", "comp", "cachePath", "name", "wait", "ms", "resolve", "showActionToast", "actionOptions", "options", "controller", "toast", "showFailureToast", "title", "error", "stderr", "import_api", "preferences", "execp", "brewPrefix", "preferences", "path_join", "brewPath", "suffix", "brewExecutable", "installedCachePath", "cachePath", "formulaCache<PERSON>ath", "caskCachePath", "brewUpgradeAll", "greedy", "cancel", "cmd", "brewQuarantineOption", "execBrew", "brewQuarantineOption", "preferences", "execBrew", "cmd", "cancel", "env", "execBrewEnv", "execp", "brewExecutable", "err", "execErr", "preferences", "askpassPath", "path_join", "fs_constants", "bundleIdentifier", "upgrade_default", "abort", "showActionToast", "brewUpgradeAll", "preferences", "err", "showFailureToast", "wait"]}