{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "linear", "title": "Linear", "description": "Bring Linear to every corner of your Mac. Create, search, and modify your issues. Stay on top of your notifications in the menu bar.", "icon": "linear-app-icon.png", "owner": "linear", "access": "public", "author": "thoma<PERSON><PERSON><PERSON><PERSON>", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "thomas", "peduarte", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clemb8", "stelo", "fil", "joeb"], "pastContributors": ["mil3na"], "categories": ["Productivity"], "license": "MIT", "commands": [{"name": "create-issue", "title": "Create Issue", "description": "Create and assign new issues.", "mode": "view", "preferences": [{"name": "autofocusField", "title": "Autofocus Field", "type": "dropdown", "required": false, "description": "Select the field to focus automatically once the form is rendered", "default": "title", "data": [{"title": "Team", "value": "teamId"}, {"title": "Title", "value": "title"}]}, {"name": "copyToastAction", "title": "Toast Copy Action", "type": "dropdown", "required": false, "description": "Select the copy action for the toast secondary action", "default": "id", "data": [{"title": "Copy Issue ID", "value": "id"}, {"title": "Copy Issue ID as Link", "value": "id-as-link"}, {"title": "Copy Issue URL", "value": "url"}, {"title": "Copy Issue Title", "value": "title"}, {"title": "Copy Issue Title as Link", "value": "title-as-link"}]}]}, {"name": "search-issues", "title": "Search Issues", "description": "Search issues globally in all projects.", "mode": "view"}, {"name": "assigned-issues", "title": "My Issues", "description": "View and modify issues that are assigned to you.", "mode": "view", "keywords": ["assigned"]}, {"name": "created-issues", "title": "Created Issues", "description": "View and modify issues that you have created.", "mode": "view"}, {"name": "active-cycle", "title": "Active Cycle", "description": "View and modify issues that are in the active cycle.", "mode": "view"}, {"name": "search-projects", "title": "Search Projects", "description": "Explore projects for your Linear team.", "mode": "view"}, {"name": "search-documents", "title": "Search Documents", "description": "Explore documents for your Linear team.", "mode": "view"}, {"name": "create-project", "title": "Create Project", "description": "Create projects for your Linear team.", "mode": "view"}, {"name": "notifications", "title": "Notifications", "description": "Read your latest Linear notifications.", "mode": "view"}, {"name": "unread-notifications", "title": "Unread Notifications", "description": "Show unread notifications in the Menu Bar.", "mode": "menu-bar", "subtitle": "Linear", "interval": "15m", "preferences": [{"type": "checkbox", "label": "Always Show", "name": "alwaysShow", "description": "Show the menu bar extra even when you don't have any unread notifications.", "default": true, "required": false}]}, {"name": "create-issue-for-myself", "title": "Create Issue for Myself", "description": "Create and assign a new issue to yourself.", "mode": "no-view", "preferences": [{"name": "preferredTeamKey", "type": "textfield", "title": "Preferred Team", "placeholder": "RAY", "description": "Specify the Key (e.g. RAY) of the preferred team. If not specified, the first team is used.", "required": false}, {"name": "shouldCloseMainWindow", "type": "checkbox", "title": "Advanced", "label": "Close window immediately", "description": "When enabled, the Raycast window is closed immediately, allowing you to carry on with other work.", "default": false, "required": false}], "arguments": [{"name": "title", "placeholder": "Title", "type": "text", "required": true}, {"name": "description", "placeholder": "Description", "type": "text"}]}, {"name": "quick-add-comment-to-issue", "title": "Quick Add Comment to Issue", "description": "Quickly add a comment to an issue using its ID.", "mode": "no-view", "preferences": [{"name": "shouldCloseMainWindow", "type": "checkbox", "title": "Advanced", "label": "Close window immediately", "description": "When enabled, the Raycast window is closed immediately, allowing you to carry on with other work.", "default": false, "required": false}], "arguments": [{"name": "comment", "placeholder": "Comment", "type": "text", "required": true}, {"name": "issueId", "placeholder": "Issue ID", "type": "text", "required": true}]}, {"name": "favorites", "title": "Favorites", "description": "Browse through your Linear favorites.", "mode": "view"}], "tools": [{"name": "get-current-user", "title": "Get Current User", "description": "Get the Linear information of the current user.", "confirmation": false}, {"name": "create-comment", "title": "Create Comment", "description": "Creates a comment in Linear", "input": {"type": "object", "properties": {"issueId": {"type": "string", "description": "The ID of the issue to associate the comment with. Format is a combination of a team key and a unique number, like `ENG-123`"}, "parentId": {"type": "string", "description": "The ID of the parent comment (for nested comments)"}, "projectUpdateId": {"type": "string", "description": "The ID of the project update to associate the comment with"}, "body": {"type": "string", "description": "The comment content in markdown format"}}, "required": ["body"]}, "confirmation": true}, {"name": "update-comment", "title": "Update Comment", "description": "Updates an existing comment in Linear", "input": {"type": "object", "properties": {"body": {"type": "string", "description": "The comment content in markdown format"}, "id": {"type": "string", "description": "The ID of the comment to update"}}, "required": ["body", "id"]}, "confirmation": true}, {"name": "get-documents", "title": "Get Documents", "description": "Fetches all documents/PRDs from Linear", "input": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query to filter documents"}, "initiativeId": {"type": "string", "description": "Restrict the documents/PRDs returned to a specific initiative"}, "projectId": {"type": "string", "description": "Restrict the documents/PRDs returned to a specific project"}}, "required": []}, "confirmation": false}, {"name": "get-document-content", "title": "Get Document Content", "description": "Get the content of a document/PRD from Linear", "input": {"type": "object", "properties": {"documentId": {"type": "string", "description": "The ID of the document/PRD to fetch"}}, "required": ["documentId"]}, "confirmation": false}, {"name": "create-document", "title": "Create Document", "description": "Creates a new document/PRD in Linear", "input": {"type": "object", "properties": {"content": {"type": "string", "description": "The content of the document/PRD as a Markdown string"}, "title": {"type": "string", "description": "The title of the document/PRD"}, "projectId": {"type": "string", "description": "The ID of the project the document/PRD belongs to"}}, "required": ["content", "title", "projectId"]}, "confirmation": true}, {"name": "update-document", "title": "Update Document", "description": "Updates an existing PRD in Linear", "input": {"type": "object", "properties": {"documentId": {"type": "string", "description": "The ID of the document/PRD to update"}, "content": {"type": "string", "description": "The content of the document/PRD as a Markdown string"}, "title": {"type": "string", "description": "The title of the document/PRD"}, "projectId": {"type": "string", "description": "The ID of the project the document/PRD belongs to"}}, "required": ["documentId"]}, "confirmation": true}, {"name": "full-text-search-issues", "title": "Full Text Search Issues", "description": "Search opened Linear issues for a specific term in their title. Returns at most 25 issues. Use this tool when you need to search for a specific term", "input": {"type": "object", "properties": {"query": {"type": "string", "description": "The query to search for. Only use plain text: it doesn't support any operators"}}, "required": ["query"]}, "confirmation": false}, {"name": "filter-issues", "title": "Filter Issues", "description": "Get opened Linear issues that match the provided filters. Returns at most 25 issues. Use this tool when you need to get issues matching specific criterias, like assignees, priority, etc", "input": {"type": "object", "properties": {"filter": {"type": "string", "description": "Filter object.\n\nExamples:\nTo return all urgent and high priority issues in the workspace, you can use the following query:\n{\n priority: { lte: 2, neq: 0 }\n}\nThe above query will also return any issues that haven't been given any priority (their priority is 0). To exclude them, you can add another not equals comparator:\n{\n  priority: { lte: 2, neq: 0 }\n}\n\n# Comparators\nYou can use the following comparators on string, numeric, and date fields:\n`eq` – Equals the given value\n`neq` – Doesn't equal the given value\n`in` – Value is in the given collection of values\n`nin` – Value is not in the given collection of values\n\nNumeric and date fields additionally have the following comparators:\n`lt` – Less than the given value\n`lte` – Less than or equal to the given value\n`gt` – Greater than then given value\n`gte` – Greater than or equal to the given value\n\nString fields additionally have the following comparators:\n\n`eqIgnoreCase` – Case insensitive `eq`\n`neqIgnoreCase` – Case insensitive `neq`\n`startsWith` – Starts with the given value\n`notStartsWith` – Doesn't start with the given value\n`endsWith` – Ends with the given value\n`notEndsWith` – Doesn't end with the given value\n`contains` – Contains the given value\n`notContains` – Doesn't contain the given value\n`containsIgnoreCase` – Case insensitive `contains`\n`notContainsIgnoreCase` – Case insensitive `notContains`\n\nLogical operators\nBy default, all fields described in the filter need to be matched. The filter merges all the conditions together using a logical and operator.\nFor example, The below example will find all urgent issues that are due in the year 2021:\n{\npriority: { eq: 1 }\ndueDate: { lte: \"2021\" }\n}\n\nList of all available fields for filtering:\nid\nidentifier\ntitle\nbranchName\npriority\npriorityLabel\nestimate\ndueDate\nupdatedAt\nurl\nnumber\n\nFiltering by relationship\nData can also be filtered based on their relations. For example, you can filter issues based on the properties of their assignees. To query all issues assigned to a user with a particular email address, you can execute the following query:\n{\nassignee: { email: { eq: \"<EMAIL>\" } }\n}\n\nMany-to-many relationships can be filtered similarly. The following query will find issues that have the Bug label associated.\n{\n labels: { name: { eq: \"Bug\" } }\n}\n\nList of all available relation fields for filtering:\nlabels {\n id\n  name\n  color\n}\nstate {\n  id\n  type\n  name\n  color\n}\nassignee {\n  id\n  displayName\n  email\n  avatarUrl\n}\nteam {\n  id\n}\ncycle {\n  id\n  number\n  startsAt\n  endsAt\n  completedAt\n}\nparent {\n  id\n  title\n  number\n}\nproject {\n  id\n  name\n  icon\n  color\n}\nprojectMilestone {\n  id\n  name\n}\n\nIf user doesn't provide another instructions, always search for open issues (that are not closed, cancelled or done)\n\nIMPORTANT: Do not try to filter by fields or relations that are not listed above.\nIf you're asked to filter by other fields, just tell that you can't do it.\n\nIMPORTANT: Format filter as a JSON string\nIMPORTANT: When user asks about my issues (assigned to me, my issues, etc), always filter by assignee."}}, "required": ["filter"]}, "confirmation": false}, {"name": "update-issue", "title": "Update Issue", "description": "Updates an existing issue in Linear", "input": {"type": "object", "properties": {"stateId": {"type": "string", "description": "The id of the State (issue or 'workflow' status) of the issue.\nIf an issue is created without a specified stateId (the status field for the issue), the issue will be assigned to the team's first state in the Backlog workflow state category.\nIf the \"Triage\" feature is turned on for the team, then the issue will be assigned to the Triage workflow state."}, "parentId": {"type": "string", "description": "The ID of the parent issue"}, "assigneeId": {"type": "string", "description": "The ID of the user to assign the issue to"}, "issueId": {"type": "string", "description": "The ID of the issue to update"}, "projectMilestoneId": {"type": "string", "description": "The ID of the project milestone the issue belongs to"}, "priority": {"type": "number", "description": "The priority of the issue (0-4, where 0 is no priority and 4 is urgent)"}, "dueDate": {"type": "string", "description": "The due date of the issue in ISO date format (e.g., '2023-12-31')"}, "estimate": {"type": "number", "description": "The estimate of the issue using a 0-5 scale"}, "title": {"type": "string", "description": "The title of the issue"}, "labelIds": {"type": "array", "items": {"type": "string"}, "description": "Array of IDs of labels to be assigned to the issue"}, "projectId": {"type": "string", "description": "The ID of the project the issue belongs to"}, "description": {"type": "string", "description": "A detailed description of the issue"}}, "required": ["issueId"]}, "confirmation": true}, {"name": "create-issue", "title": "Create Issue", "description": "Creates a new issue in Linear with a title, description, dueDate and priority", "input": {"type": "object", "properties": {"priority": {"type": "number", "description": "The priority of the issue (0-4, where 0 is no priority and 4 is urgent)"}, "stateId": {"type": "string", "description": "The id of the State (issue or 'workflow' status) of the issue.\nIf an issue is created without a specified stateId (the status field for the issue), the issue will be assigned to the team's first state in the Backlog workflow state category.\nIf the \"Triage\" feature is turned on for the team, then the issue will be assigned to the Triage workflow state."}, "parentId": {"type": "string", "description": "The ID of the parent issue. Use it to add sub-issues. UUID format"}, "assigneeId": {"type": "string", "description": "The ID of the user to assign the issue to"}, "title": {"type": "string", "description": "The title of the issue"}, "teamId": {"type": "string", "description": "The ID of the team to fetch the statuses for. Do not ask user to specify team if there is only one in the list"}, "labelIds": {"type": "array", "items": {"type": "string"}, "description": "Array of IDs of labels to be assigned to new issue"}, "projectId": {"type": "string", "description": "The ID of the project the issue belongs to"}, "description": {"type": "string", "description": "A detailed description of the issue"}, "projectMilestoneId": {"type": "string", "description": "The ID of the project milestone the issue belongs to"}, "dueDate": {"type": "string", "description": "The due date of the issue in ISO date format (e.g., '2023-12-31')"}, "estimate": {"type": "number", "description": "The estimate of the issue using a 0-5 scale"}}, "required": ["title", "teamId"]}, "confirmation": true}, {"name": "get-teams", "title": "Get Teams", "description": "Returns all Linear teams that the current user is part of", "confirmation": false}, {"name": "get-issue-states", "title": "Get Issue States", "description": "Fetches all available issue states. Issue status could be a synonym of issue state", "input": {"type": "object", "properties": {"teamId": {"type": "string", "description": "The ID of the team to fetch the statuses for. Do not ask user to specify team if there is only one in the list"}}, "required": ["teamId"]}, "confirmation": false}, {"name": "get-members", "title": "Get Members", "description": "Retrieves all members of the Linear organization", "confirmation": false}, {"name": "get-notifications", "title": "Get Notifications", "description": "Retrieves all notifications for current user", "confirmation": false}, {"name": "get-labels", "title": "Get Issue Labels", "description": "Retrieves all labels that can be assigned to a Linear issue", "confirmation": false}, {"name": "add-label", "title": "Add Label to Issue", "description": "Add a Label to an existing Linear issue", "input": {"type": "object", "properties": {"issueId": {"type": "string", "description": "The ID of the issue to add the label to. Format is a combination of a team key and a unique number, like `ENG-123`"}, "labelId": {"type": "string", "description": "The ID of the label to add to the issue. Never use title as ID: you have to use `get-labels` tool to get the actual ID from the list of labels"}}, "required": ["issueId", "labelId"]}, "confirmation": true}, {"name": "remove-label", "title": "Remove Label from Issue", "description": "Remove a Label from an existing Linear issue", "input": {"type": "object", "properties": {"issueId": {"type": "string", "description": "The ID of the issue to add the label to. Format is a combination of a team key and a unique number, like `ENG-123`"}, "labelId": {"type": "string", "description": "The ID of the label to add to the issue. Never use title as ID: you have to use `get-labels` tool to get the actual ID from the list of labels"}}, "required": ["issueId", "labelId"]}, "confirmation": true}, {"name": "get-projects", "title": "Get Projects", "description": "Fetches all projects from Linear", "confirmation": false}, {"name": "get-project-updates", "title": "Get Project Updates", "description": "Retrieves all project updates for a given project ID from Linear", "input": {"type": "object", "properties": {"projectId": {"type": "string", "description": "The ID of the project to fetch the updates for. Use the 'get-projects' tool to get the project ID."}}, "required": ["projectId"]}, "confirmation": false}, {"name": "get-project-statuses", "title": "Get Project Statuses", "description": "Fetches all project statuses from Linear", "confirmation": false}, {"name": "update-project-milestone", "title": "Update an Existing Project Milestone", "description": "Updates an existing project milestone in Linear", "input": {"type": "object", "properties": {"milestoneId": {"type": "string", "description": "The ID of the project update to modify."}, "name": {"type": "string", "description": "The new name of the milestone"}, "description": {"type": "string", "description": "A new detailed description of the milestone"}, "targetDate": {"type": "string", "description": "The new target date of the milestone in ISO date format (e.g., '2023-12-31')"}}, "required": ["milestoneId"]}, "confirmation": true}], "ai": {"instructions": "- Always format object titles and names as markdown links using the object's URL. Example: Instead of saying \"Issue **Implement user profile page** has been created', say \"Issue [Implement user profile page](https://linear.app/company/issue/ISS-2) has been created\"\\n- Do not use names or titles as IDs in the system. Use the actual ID of the object\\n- When there is no exact match of issue / label titles with the provided name, analyze the input and titles of existing objects and use such objects only if the meaning is the same. Do not use objects that are not related to the input\\n- If user asks for his issues, it means issues assigned to him. Use `get-current-user` tool to get the user id and use it to filter\\n- \\n- If user ask to close the issue, it means to move it to done status\\n- When user asks about inbox they mean notifications", "evals": [{"input": "@linear what's in my inbox", "mocks": {"get-notifications": []}, "expected": [{"callsTool": "get-notifications"}]}, {"input": "@linear do I have any open notifications", "usedAsExample": false, "mocks": {"get-notifications": []}, "expected": [{"callsTool": "get-notifications"}]}, {"input": "@linear Create issue 'Implement user profile page'", "mocks": {"create-issue": {"id": "ISS-2", "identifier": "PROJ-2", "title": "Implement user profile page", "url": "https://example.com/issue/ISS-2", "description": "Create a user profile page with editable fields and avatar upload functionality", "priority": 2, "priorityLabel": "High", "state": {"id": "STATE-1", "type": "backlog", "name": "Backlog", "color": "#FFFF00"}, "assignee": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg", "email": "<EMAIL>"}}, "get-teams": [{"id": "TEAM-1", "name": "Raycast", "key": "ENG", "icon": "💻", "color": "#0000FF"}]}, "expected": [{"meetsCriteria": "Tells that issue was added"}, {"meetsCriteria": "Includes markdown link to created issue"}, {"meetsCriteria": "Doesn't include any superfluous wording, for example statements like 'let me know if you have any questions'"}, {"callsTool": "create-issue"}]}, {"input": "@linear what are my tasks?", "mocks": {"filter-issues": {"issues": [{"id": "ISS-1", "title": "Implement new feature", "priority": 2, "priorityLabel": "High", "state": {"id": "STATE-1", "type": "started", "name": "In Progress", "color": "#0000FF"}, "assignee": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg", "email": "<EMAIL>"}, "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}]}, "team": {"id": "TEAM-1", "issueEstimationType": "<PERSON><PERSON><PERSON><PERSON>", "issueEstimationAllowZero": true, "issueEstimationExtended": false}}]}, "get-members": [{"id": "usr_01ABCDEFGHIJKLMNOPQRSTUV", "name": "<PERSON>", "email": "<EMAIL>", "displayName": "<PERSON>", "avatarUrl": "https://avatars.linear.app/user/01ABCDEFGHIJKLMNOPQRSTUV", "active": true, "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-10-15T14:24:04.000Z", "admin": true, "lastSeen": "2023-10-15T14:24:04.000Z", "inviteHash": "abcdef123456", "url": "https://linear.app/team/u/john-doe", "description": "Product Manager", "isMe": true, "statusEmoji": "🚀", "statusLabel": "Launching new features", "statusUntilAt": "2023-10-16T23:59:59.000Z"}], "get-current-user": {"id": "usr_01ABCDEFGHIJKLMNOPQRSTUV", "name": "<PERSON>", "email": "<EMAIL>", "displayName": "<PERSON>", "avatarUrl": "https://avatars.linear.app/user/01ABCDEFGHIJKLMNOPQRSTUV", "active": true, "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-10-15T14:24:04.000Z", "admin": true, "lastSeen": "2023-10-15T14:24:04.000Z", "inviteHash": "abcdef123456", "url": "https://linear.app/team/u/john-doe", "description": "Product Manager", "isMe": true, "statusEmoji": "🚀", "statusLabel": "Launching new features", "statusUntilAt": "2023-10-16T23:59:59.000Z"}}, "expected": [{"callsTool": "get-current-user"}, {"callsTool": {"name": "filter-issues", "arguments": {"filter": [{"includes": "usr_01ABCDEFGHIJKLMNOPQRSTUV"}]}}}, {"includes": "Implement new feature"}]}, {"input": "@linear what are my current high priority issues?", "mocks": {"filter-issues": {"issues": [{"id": "ISS-1", "title": "Implement new feature", "priority": 2, "priorityLabel": "High", "state": {"id": "STATE-1", "type": "started", "name": "In Progress", "color": "#0000FF"}, "assignee": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg", "email": "<EMAIL>"}, "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}]}, "team": {"id": "TEAM-1", "issueEstimationType": "<PERSON><PERSON><PERSON><PERSON>", "issueEstimationAllowZero": true, "issueEstimationExtended": false}}]}, "get-current-user": {"id": "usr_01ABCDEFGHIJKLMNOPQRSTUV", "name": "<PERSON>", "email": "<EMAIL>", "displayName": "<PERSON>", "avatarUrl": "https://avatars.linear.app/user/01ABCDEFGHIJKLMNOPQRSTUV", "active": true, "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-10-15T14:24:04.000Z", "admin": true, "lastSeen": "2023-10-15T14:24:04.000Z", "inviteHash": "abcdef123456", "url": "https://linear.app/team/u/john-doe", "description": "Product Manager", "isMe": true, "statusEmoji": "🚀", "statusLabel": "Launching new features", "statusUntilAt": "2023-10-16T23:59:59.000Z"}}, "expected": [{"callsTool": "get-current-user"}, {"callsTool": "filter-issues"}, {"includes": "Implement new feature"}]}, {"input": "@linear add label 'yo-ho-ho-ho-ho' to Implement new feature", "usedAsExample": false, "mocks": {"add-label": {"id": "ISS-1", "title": "Bug: Lo<PERSON> not working", "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}, {"id": "LABEL-2", "name": "High Priority", "color": "#FF00FF"}]}}, "full-text-search-issues": {"issues": [{"id": "ISS-1", "title": "Implement new feature", "priority": 2, "priorityLabel": "High", "state": {"id": "STATE-1", "type": "started", "name": "In Progress", "color": "#0000FF"}, "assignee": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg", "email": "<EMAIL>"}, "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}]}, "team": {"id": "TEAM-1", "issueEstimationType": "<PERSON><PERSON><PERSON><PERSON>", "issueEstimationAllowZero": true, "issueEstimationExtended": false}}]}, "get-current-user": {"id": "usr_01ABCDEFGHIJKLMNOPQRSTUV", "name": "<PERSON>", "email": "<EMAIL>", "displayName": "<PERSON>", "avatarUrl": "https://avatars.linear.app/user/01ABCDEFGHIJKLMNOPQRSTUV", "active": true, "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-10-15T14:24:04.000Z", "admin": true, "lastSeen": "2023-10-15T14:24:04.000Z", "inviteHash": "abcdef123456", "url": "https://linear.app/team/u/john-doe", "description": "Product Manager", "isMe": true, "statusEmoji": "🚀", "statusLabel": "Launching new features", "statusUntilAt": "2023-10-16T23:59:59.000Z"}, "get-labels": [{"id": "LABEL-1", "name": "Bug", "description": "Something isn't working", "color": "#FF0000"}, {"id": "LABEL-2", "name": "Feature", "description": "New feature or request", "color": "#00FF00"}]}, "expected": [{"meetsCriteria": "Tells that label with this name doesn't exist"}, {"not": {"callsTool": "create-issue"}}]}, {"input": "@linear create label completely-new-label", "usedAsExample": false, "mocks": {"get-labels": [{"id": "LABEL-1", "name": "Bug", "description": "Something isn't working", "color": "#FF0000"}, {"id": "LABEL-2", "name": "Feature", "description": "New feature or request", "color": "#00FF00"}]}, "expected": [{"meetsCriteria": "Tells that it can't find the label or it can't create labels"}, {"not": {"callsTool": "create-issue"}}]}, {"input": "@linear add feature label to AI Evals CLI", "mocks": {"full-text-search-issues": {"issues": [{"id": "ISS-1", "title": "AI Evals CLI", "priority": 2, "priorityLabel": "High", "state": {"id": "STATE-1", "type": "started", "name": "In Progress", "color": "#0000FF"}, "assignee": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg", "email": "<EMAIL>"}, "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}]}, "team": {"id": "TEAM-1", "issueEstimationType": "<PERSON><PERSON><PERSON><PERSON>", "issueEstimationAllowZero": true, "issueEstimationExtended": false}}]}, "add-label": {"id": "ISS-1", "title": "Bug: Lo<PERSON> not working", "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}, {"id": "LABEL-2", "name": "High Priority", "color": "#FF00FF"}]}}, "get-labels": [{"id": "LABEL-1", "name": "Bug", "description": "Something isn't working", "color": "#FF0000"}, {"id": "LABEL-2", "name": "Feature", "description": "New feature or request", "color": "#00FF00"}]}, "expected": [{"meetsCriteria": "Tells that label was added to the issue"}, {"callsTool": {"name": "add-label", "arguments": {"issueId": "ISS-1", "labelId": "LABEL-2"}}}]}, {"input": "@linear remove Bug label from AI tools", "mocks": {"remove-label": {"id": "RAY-15", "title": "AI tools", "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}, {"id": "LABEL-2", "name": "High Priority", "color": "#FF00FF"}]}}, "full-text-search-issues": {"issues": [{"id": "RAY-15", "title": "AI tools", "priority": 2, "priorityLabel": "High", "state": {"id": "STATE-1", "type": "started", "name": "In Progress", "color": "#0000FF"}, "assignee": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg", "email": "<EMAIL>"}, "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "description": "Something isn't working", "color": "#FF0000"}]}, "team": {"id": "TEAM-1", "issueEstimationType": "<PERSON><PERSON><PERSON><PERSON>", "issueEstimationAllowZero": true, "issueEstimationExtended": false}}]}, "filter-issues": {"issues": [{"id": "RAY-15", "title": "AI tools", "priority": 2, "priorityLabel": "High", "state": {"id": "STATE-1", "type": "started", "name": "In Progress", "color": "#0000FF"}, "assignee": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg", "email": "<EMAIL>"}, "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "description": "Something isn't working", "color": "#FF0000"}]}, "team": {"id": "TEAM-1", "issueEstimationType": "<PERSON><PERSON><PERSON><PERSON>", "issueEstimationAllowZero": true, "issueEstimationExtended": false}}]}, "get-labels": [{"id": "LABEL-1", "name": "Bug", "description": "Something isn't working", "color": "#FF0000"}, {"id": "LABEL-2", "name": "Feature", "description": "New feature or request", "color": "#00FF00"}]}, "expected": [{"meetsCriteria": "Tells that label was removed from the issue"}, {"callsTool": "full-text-search-issues"}, {"callsTool": {"name": "remove-label", "arguments": {"issueId": "RAY-15", "labelId": "LABEL-1"}}}]}, {"input": "@linear assign label 'High Priority' to Implement new feature", "mocks": {"add-label": {"id": "ISS-1", "title": "Implement new feature", "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}, {"id": "LABEL-2", "name": "High Priority", "color": "#FF00FF"}]}}, "full-text-search-issues": {"issues": [{"id": "ISS-1", "title": "Implement new feature", "priority": 2, "priorityLabel": "High", "state": {"id": "STATE-1", "type": "started", "name": "In Progress", "color": "#0000FF"}, "assignee": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg", "email": "<EMAIL>"}, "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}]}, "team": {"id": "TEAM-1", "issueEstimationType": "<PERSON><PERSON><PERSON><PERSON>", "issueEstimationAllowZero": true, "issueEstimationExtended": false}}]}, "get-labels": [{"id": "LABEL-1", "name": "Bug", "description": "Something isn't working", "color": "#FF0000"}, {"id": "LABEL-2", "name": "Feature", "description": "New feature or request", "color": "#00FF00"}]}, "expected": [{"meetsCriteria": "Tells that label 'High Priority' doesn't exist"}, {"not": {"callsTool": "add-label"}}]}, {"input": "@linear add comment to Implement landing page: Blocker: waiting for design", "mocks": {"create-comment": {"id": "COMMENT-1", "body": "Blocker: waiting for design", "createdAt": "2024-09-18T16:00:00Z", "user": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg"}}, "full-text-search-issues": {"issues": [{"id": "ISS-1", "title": "Implement landing page", "priority": 2, "priorityLabel": "High", "state": {"id": "STATE-1", "type": "started", "name": "In Progress", "color": "#0000FF"}, "assignee": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg", "email": "<EMAIL>"}, "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}]}, "team": {"id": "TEAM-1", "issueEstimationType": "<PERSON><PERSON><PERSON><PERSON>", "issueEstimationAllowZero": true, "issueEstimationExtended": false}}]}}, "expected": [{"meetsCriteria": "Tells that comment was added to the issue"}, {"matches": "\\[([^\\]]+)\\]\\(([^\\s\\)]+)(?:\\s+\"([^\"]+)\")?\\)"}, {"callsTool": "full-text-search-issues"}, {"callsTool": {"name": "create-comment", "arguments": {"issueId": "ISS-1", "body": {"includes": "waiting for design"}}}}]}, {"input": "@linear add document titled Meeting Notes with content 'TBD' in Project Alpha", "mocks": {"get-documents": [{"id": "DOC-1", "title": "API Documentation", "icon": "📄", "color": "#00FFFF", "createdAt": "2024-09-01T10:00:00Z", "updatedAt": "2024-09-18T15:00:00Z", "url": "https://linear.app/company/doc/doc-1"}, {"id": "DOC-2", "title": "Product Roadmap", "icon": "🗺️", "color": "#FF00FF", "createdAt": "2024-09-05T09:00:00Z", "updatedAt": "2024-09-17T11:30:00Z", "url": "https://linear.app/company/doc/doc-2"}], "create-document": {"id": "DOC-3", "title": "Release Process", "content": "# Release Process\n\nThis document outlines our release process and checklist...", "icon": "📅", "color": "#FF00FF", "createdAt": "2024-09-18T18:00:00Z", "updatedAt": "2024-09-18T18:00:00Z", "url": "https://linear.app/company/doc/doc-3"}, "get-projects": [{"id": "PROJ-1", "name": "Project Alpha", "description": "Our flagship project", "icon": "🚀", "color": "#00FF00", "progress": 75, "targetDate": "2024-12-31T00:00:00Z", "startDate": "2024-01-01T00:00:00Z"}, {"id": "PROJ-2", "name": "Project Beta", "description": "Experimental project", "icon": "🧪", "color": "#0000FF", "progress": 25, "targetDate": "2025-06-30T00:00:00Z", "startDate": "2024-07-01T00:00:00Z"}], "get-document-content": {"id": "DOC-1", "title": "API Documentation", "content": "# API Documentation\n\nThis document describes our API endpoints and how to use them.\n\n## Authentication\n\nAll API requests require authentication using a bearer token...", "icon": "📄", "color": "#00FFFF", "createdAt": "2024-09-01T10:00:00Z", "updatedAt": "2024-09-18T15:00:00Z", "url": "https://linear.app/company/doc/doc-1"}}, "expected": [{"meetsCriteria": "Tells that document has been created successfully"}, {"includes": "Meeting Notes"}, {"meetsCriteria": "Includes markdown link to created document"}, {"callsTool": {"name": "create-document", "arguments": {"projectId": "PROJ-1", "title": [{"includes": "meeting notes"}], "content": [{"includes": "tbd"}]}}}]}, {"input": "@linear what is the status of project Alpha", "mocks": {"get-project-statuses": [{"id": "STATUS-1", "name": "Planned", "color": "#FFFF00"}, {"id": "STATUS-2", "name": "In Progress", "color": "#0000FF"}, {"id": "STATUS-3", "name": "Completed", "color": "#00FF00"}], "get-project-updates": [{"id": "UPDATE-1", "health": "onTrack", "body": "Project is progressing well", "updatedAt": "2024-09-18T10:00:00Z", "url": "https://linear.app/company/project/update-1"}, {"id": "UPDATE-2", "health": "atRisk", "body": "We've encountered some challenges", "updatedAt": "2024-09-17T15:30:00Z", "url": "https://linear.app/company/project/update-2"}], "get-projects": [{"id": "PROJ-1", "name": "Project Alpha", "description": "Our flagship project", "icon": "🚀", "color": "#00FF00", "progress": 75, "targetDate": "2024-12-31T00:00:00Z", "startDate": "2024-01-01T00:00:00Z"}, {"id": "PROJ-2", "name": "Project Beta", "description": "Experimental project", "icon": "🧪", "color": "#0000FF", "progress": 25, "targetDate": "2025-06-30T00:00:00Z", "startDate": "2024-07-01T00:00:00Z"}]}, "expected": [{"meetsCriteria": "Explains current project status"}]}, {"input": "@linear Move Windows client to in-progress", "usedAsExample": true, "mocks": {"full-text-search-issues": {"issues": [{"id": "RAY-100", "title": "Windows client", "priority": 2, "priorityLabel": "High", "state": {"id": "STATE-1", "type": "backlog", "name": "Backlog", "color": "#0000FF"}, "assignee": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg", "email": "<EMAIL>"}, "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}]}, "team": {"id": "TEAM-1", "issueEstimationType": "<PERSON><PERSON><PERSON><PERSON>", "issueEstimationAllowZero": true, "issueEstimationExtended": false}}]}, "get-issue-states": [{"id": "STATE-1", "name": "Backlog", "description": "Issues that are not yet scheduled", "position": 0, "type": "backlog"}, {"id": "STATE-2", "name": "In Progress", "description": "Issues currently being worked on", "position": 1, "type": "started"}, {"id": "STATE-3", "name": "Done", "description": "Completed issues", "position": 2, "type": "completed"}], "update-issue": {"id": "RAY-100", "title": "Windows client", "description": "", "state": {"id": "STATE-2", "type": "started", "name": "In Progress", "color": "#0000FF"}, "priority": 1, "priorityLabel": "<PERSON><PERSON>"}}, "expected": [{"callsTool": "get-issue-states"}, {"callsTool": {"name": "update-issue", "arguments": {"stateId": "STATE-2", "issueId": "RAY-100"}}}, {"meetsCriteria": "Tells that issue status was changed"}, {"matches": "\\[([^\\]]+)\\]\\(([^\\s\\)]+)(?:\\s+\"([^\"]+)\")?\\)"}]}, {"input": "@linear close my issue for the location tool", "usedAsExample": true, "mocks": {"get-current-user": {"id": "usr_01ABCDEFGHIJKLMNOPQRSTUV", "name": "<PERSON>", "email": "<EMAIL>", "displayName": "<PERSON>", "avatarUrl": "https://avatars.linear.app/user/01ABCDEFGHIJKLMNOPQRSTUV", "active": true, "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-10-15T14:24:04.000Z", "admin": true, "lastSeen": "2023-10-15T14:24:04.000Z", "inviteHash": "abcdef123456", "url": "https://linear.app/team/u/john-doe", "description": "Product Manager", "isMe": true, "statusEmoji": "🚀", "statusLabel": "Launching new features", "statusUntilAt": "2023-10-16T23:59:59.000Z"}, "filter-issues": {"issues": [{"id": "RAY-500", "title": "Turn `@location` into a permission instead of a tool", "priority": 2, "priorityLabel": "High", "state": {"id": "STATE-1", "type": "backlog", "name": "Backlog", "color": "#0000FF"}, "assignee": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg", "email": "<EMAIL>"}, "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}]}, "team": {"id": "TEAM-1", "issueEstimationType": "<PERSON><PERSON><PERSON><PERSON>", "issueEstimationAllowZero": true, "issueEstimationExtended": false}}]}, "full-text-search-issues": {"issues": [{"id": "RAY-500", "title": "Turn `@location` into a permission instead of a tool", "priority": 2, "priorityLabel": "High", "state": {"id": "STATE-1", "type": "backlog", "name": "Backlog", "color": "#0000FF"}, "assignee": {"id": "USER-1", "displayName": "<PERSON>", "avatarUrl": "https://example.com/avatar.jpg", "email": "<EMAIL>"}, "labels": {"nodes": [{"id": "LABEL-1", "name": "Bug", "color": "#FF0000"}]}, "team": {"id": "TEAM-1", "issueEstimationType": "<PERSON><PERSON><PERSON><PERSON>", "issueEstimationAllowZero": true, "issueEstimationExtended": false}}]}, "get-issue-states": [{"id": "STATE-1", "name": "Backlog", "description": "Issues that are not yet scheduled", "position": 0, "type": "backlog"}, {"id": "STATE-2", "name": "In Progress", "description": "Issues currently being worked on", "position": 1, "type": "started"}, {"id": "STATE-3", "name": "Done", "description": "Completed issues", "position": 2, "type": "completed"}], "update-issue": {"id": "RAY-100", "title": "Turn `@location` into a permission instead of a tool", "description": "", "state": {"id": "STATE-2", "type": "started", "name": "In Progress", "color": "#0000FF"}, "priority": 1, "priorityLabel": "<PERSON><PERSON>"}}, "expected": [{"callsTool": "get-issue-states"}, {"callsTool": {"name": "update-issue", "arguments": {"stateId": "STATE-3", "issueId": "RAY-500"}}}, {"meetsCriteria": "Tells that issue status was changed"}, {"matches": "\\[([^\\]]+)\\]\\(([^\\s\\)]+)(?:\\s+\"([^\"]+)\")?\\)"}]}, {"input": "@linear add sub-issue `Public release` to AI Extensions API", "mocks": {"full-text-search-issues": {"issues": [{"id": "d11c2ea6-2533-4bab-9a6e-ed6499fbfc6a", "identifier": "RAY-1234567", "title": "AI Extensions API", "branchName": "ray-1234567-ai-extensions-api"}]}, "get-teams": [{"id": "TEAM-1", "name": "Raycast", "key": "ENG", "icon": "💻", "color": "#0000FF"}], "create-issue": {"success": true}}, "expected": [{"callsTool": {"name": "create-issue", "arguments": {"parentId": "d11c2ea6-2533-4bab-9a6e-ed6499fbfc6a"}}}]}]}, "preferences": [{"name": "issueCustomCopyAction", "type": "textfield", "title": "Issue Custom Copy Action", "description": "Add a custom copy action to the issue's actions following the format you want.\nAvailable keys: {ISSUE_TITLE}, {ISSUE_ID}, {ISSUE_URL}, {ISSUE_BRANCH_NAME}\nExample: '{ISSUE_ID}: {ISSUE_TITLE} ({ISSUE_URL})'", "required": false}, {"name": "limit", "title": "API Issue Limit", "description": "The maximum number of issues to fetch from the Linear API at once. Be careful when changing this, as too large a number can cause you to exceed your API rate limit and/or cause heap memory errors.", "type": "textfield", "default": "50", "required": false}, {"name": "shouldHideRedundantIssues", "type": "checkbox", "title": "Advanced", "label": "Hide Redundant Issues", "description": "When enabled, the issues that are marked as Done, Canceled or Duplicate will not show up in search results.", "default": false, "required": false}], "dependencies": {"@linear/sdk": "^39.0.0", "@raycast/api": "^1.95.0", "@raycast/utils": "^1.19.1", "date-fns": "^4.1.0", "file-type": "^20.4.1", "lodash": "^4.17.21", "nanoid": "^5.1.5", "node-emoji": "^2.2.0", "remove-markdown": "^0.6.0"}, "devDependencies": {"@raycast/eslint-config": "^2.0.4", "@types/lodash": "^4.17.16", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/remove-markdown": "^0.3.4", "eslint": "^9.24.0", "eslint-plugin-import": "^2.31.0", "prettier": "^3.5.3", "typescript": "^5.8.3"}, "scripts": {"build": "ray build -e dist", "dev": "ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "publish": "npx @raycast/api@latest publish"}}