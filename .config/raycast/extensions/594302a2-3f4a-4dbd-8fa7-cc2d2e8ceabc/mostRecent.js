"use strict";var s=Object.defineProperty;var d=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var p=Object.prototype.hasOwnProperty;var f=(t,e)=>{for(var r in e)s(t,r,{get:e[r],enumerable:!0})},h=(t,e,r,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of m(e))!p.call(t,n)&&n!==r&&s(t,n,{get:()=>e[n],enumerable:!(a=d(e,n))||a.enumerable});return t};var u=t=>h(s({},"__esModule",{value:!0}),t);var l={};f(l,{default:()=>c});module.exports=u(l);var i=require("@raycast/api"),o=require("fs"),$=(0,i.getPreferenceValues)().downloadsdir;function c(){let t=(0,o.readdirSync)($,{withFileTypes:!0}).sort((e,r)=>{let a=(0,o.statSync)(`${e.path}/${e.name}`).birthtime,n=(0,o.statSync)(`${r.path}/${r.name}`).birthtime;return a<n?1:-1});return(0,i.open)(`${t[0].path}/${t[0].name}`)}
