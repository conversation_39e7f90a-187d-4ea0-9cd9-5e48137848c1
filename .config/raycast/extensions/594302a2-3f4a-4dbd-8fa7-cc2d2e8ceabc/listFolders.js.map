{"version": 3, "sources": ["../src/listFolders.tsx"], "sourcesContent": ["import { ActionPanel, Action, List, Grid, getPreferenceValues } from \"@raycast/api\";\nimport { readdirSync } from \"fs\";\n\nconst preferences = getPreferenceValues<Preferences.ListFolders>();\n\nconst dir = preferences.homedir;\nconst layout = preferences.layout;\n\nexport default function Command() {\n  if (layout == \"List\") {\n    return (\n      <List>\n        {readdirSync(dir, { withFileTypes: true }).map(function (item) {\n          return item.name.charAt(0) != \".\" && item.isDirectory() ? (\n            <List.Item\n              key={item.name}\n              icon={{ fileIcon: `${item.path}/${item.name}` }}\n              title={item.name}\n              actions={\n                <ActionPanel>\n                  <Action.Open title={`Open ${item.name}`} target={`${item.path}/${item.name}`} />\n                </ActionPanel>\n              }\n            />\n          ) : null;\n        })}\n      </List>\n    );\n  } else {\n    return (\n      <Grid columns={Number.parseInt(layout)} inset={Grid.Inset.Small} aspectRatio=\"4/3\">\n        {readdirSync(dir, { withFileTypes: true }).map(function (item) {\n          return item.name.charAt(0) != \".\" && item.isDirectory() ? (\n            <Grid.Item\n              key={item.name}\n              content={{ fileIcon: `${item.path}/${item.name}` }}\n              title={item.name}\n              actions={\n                <ActionPanel>\n                  <Action.Open title={`Open ${item.name}`} target={`${item.path}/${item.name}`} />\n                </ActionPanel>\n              }\n            />\n          ) : null;\n        })}\n      </Grid>\n    );\n  }\n}\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAqE,wBACrEC,EAA4B,cAmBVC,EAAA,6BAjBZC,KAAc,uBAA6C,EAE3DC,EAAMD,EAAY,QAClBE,EAASF,EAAY,OAEZ,SAARL,GAA2B,CAChC,OAAIO,GAAU,UAEV,OAAC,QACE,2BAAYD,EAAK,CAAE,cAAe,EAAK,CAAC,EAAE,IAAI,SAAUE,EAAM,CAC7D,OAAOA,EAAK,KAAK,OAAO,CAAC,GAAK,KAAOA,EAAK,YAAY,KACpD,OAAC,OAAK,KAAL,CAEC,KAAM,CAAE,SAAU,GAAGA,EAAK,IAAI,IAAIA,EAAK,IAAI,EAAG,EAC9C,MAAOA,EAAK,KACZ,WACE,OAAC,eACC,mBAAC,SAAO,KAAP,CAAY,MAAO,QAAQA,EAAK,IAAI,GAAI,OAAQ,GAAGA,EAAK,IAAI,IAAIA,EAAK,IAAI,GAAI,EAChF,GANGA,EAAK,IAQZ,EACE,IACN,CAAC,EACH,KAIA,OAAC,QAAK,QAAS,OAAO,SAASD,CAAM,EAAG,MAAO,OAAK,MAAM,MAAO,YAAY,MAC1E,2BAAYD,EAAK,CAAE,cAAe,EAAK,CAAC,EAAE,IAAI,SAAUE,EAAM,CAC7D,OAAOA,EAAK,KAAK,OAAO,CAAC,GAAK,KAAOA,EAAK,YAAY,KACpD,OAAC,OAAK,KAAL,CAEC,QAAS,CAAE,SAAU,GAAGA,EAAK,IAAI,IAAIA,EAAK,IAAI,EAAG,EACjD,MAAOA,EAAK,KACZ,WACE,OAAC,eACC,mBAAC,SAAO,KAAP,CAAY,MAAO,QAAQA,EAAK,IAAI,GAAI,OAAQ,GAAGA,EAAK,IAAI,IAAIA,EAAK,IAAI,GAAI,EAChF,GANGA,EAAK,IAQZ,EACE,IACN,CAAC,EACH,CAGN", "names": ["listFolders_exports", "__export", "Command", "__toCommonJS", "import_api", "import_fs", "import_jsx_runtime", "preferences", "dir", "layout", "item"]}