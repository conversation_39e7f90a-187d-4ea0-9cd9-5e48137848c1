{"version": 3, "sources": ["../src/folderSpeedDial.tsx"], "sourcesContent": ["import { Action, ActionPanel, Grid, getPreferenceValues, open } from \"@raycast/api\";\n\nconst preferences = getPreferenceValues<Preferences.FolderSpeedDial>();\n\nconst folders = [\n  preferences.dirOne,\n  preferences.dirTwo,\n  preferences.dirThree,\n  preferences.dirFour,\n  preferences.dirFive,\n  preferences.dirSix,\n  preferences.dirSeven,\n  preferences.dirEight,\n];\n\nexport default function Command() {\n  const directories: Map<number, string> = new Map<number, string>();\n\n  folders.forEach((element) => {\n    if (element != null && element != \"\") {\n      directories.set(directories.size, element);\n    }\n  });\n\n  return (\n    <Grid\n      columns={4}\n      inset={Grid.Inset.Small}\n      aspectRatio=\"4/3\"\n      filtering={false}\n      onSearchTextChange={(text) => {\n        const number: number = Number.parseInt(text);\n        if (number != null && number > 0 && number < 9) {\n          const path = directories.get(number - 1);\n          if (path) {\n            open(path);\n          }\n        }\n      }}\n    >\n      {Array.from(directories, ([key, value]) => (\n        <Grid.Item\n          key={key.valueOf()}\n          title={`| ${key + 1} | ${value.substring(value.lastIndexOf(\"/\") + 1, value.length)}`}\n          content={{ fileIcon: value }}\n          actions={\n            <ActionPanel>\n              <Action.Open title={`Open ${value.substring(value.lastIndexOf(\"/\") + 1, value.length)}`} target={value} />\n            </ActionPanel>\n          }\n        />\n      ))}\n    </Grid>\n  );\n}\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAqE,wBA+CvDC,EAAA,6BA7CRC,KAAc,uBAAiD,EAE/DC,EAAU,CACdD,EAAY,OACZA,EAAY,OACZA,EAAY,SACZA,EAAY,QACZA,EAAY,QACZA,EAAY,OACZA,EAAY,SACZA,EAAY,QACd,EAEe,SAARJ,GAA2B,CAChC,IAAMM,EAAmC,IAAI,IAE7C,OAAAD,EAAQ,QAASE,GAAY,CACvBA,GAAW,MAAQA,GAAW,IAChCD,EAAY,IAAIA,EAAY,KAAMC,CAAO,CAE7C,CAAC,KAGC,OAAC,QACC,QAAS,EACT,MAAO,OAAK,MAAM,MAClB,YAAY,MACZ,UAAW,GACX,mBAAqBC,GAAS,CAC5B,IAAMC,EAAiB,OAAO,SAASD,CAAI,EAC3C,GAAIC,GAAU,MAAQA,EAAS,GAAKA,EAAS,EAAG,CAC9C,IAAMC,EAAOJ,EAAY,IAAIG,EAAS,CAAC,EACnCC,MACF,QAAKA,CAAI,CAEb,CACF,EAEC,eAAM,KAAKJ,EAAa,CAAC,CAACK,EAAKC,CAAK,OACnC,OAAC,OAAK,KAAL,CAEC,MAAO,KAAKD,EAAM,CAAC,MAAMC,EAAM,UAAUA,EAAM,YAAY,GAAG,EAAI,EAAGA,EAAM,MAAM,CAAC,GAClF,QAAS,CAAE,SAAUA,CAAM,EAC3B,WACE,OAAC,eACC,mBAAC,SAAO,KAAP,CAAY,MAAO,QAAQA,EAAM,UAAUA,EAAM,YAAY,GAAG,EAAI,EAAGA,EAAM,MAAM,CAAC,GAAI,OAAQA,EAAO,EAC1G,GANGD,EAAI,QAAQ,CAQnB,CACD,EACH,CAEJ", "names": ["folderSpeedDial_exports", "__export", "Command", "__toCommonJS", "import_api", "import_jsx_runtime", "preferences", "folders", "directories", "element", "text", "number", "path", "key", "value"]}