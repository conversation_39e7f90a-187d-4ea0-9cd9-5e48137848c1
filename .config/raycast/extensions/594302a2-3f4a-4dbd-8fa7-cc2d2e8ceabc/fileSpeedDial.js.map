{"version": 3, "sources": ["../src/fileSpeedDial.tsx"], "sourcesContent": ["import { Action, ActionPanel, Grid, getPreferenceValues, open } from \"@raycast/api\";\n\nconst preferences = getPreferenceValues<Preferences.FileSpeedDial>();\n\nconst fileLocations = [\n  preferences.fileOne,\n  preferences.fileTwo,\n  preferences.fileThree,\n  preferences.fileFour,\n  preferences.fileFive,\n  preferences.fileSix,\n  preferences.fileSeven,\n  preferences.fileEight,\n];\n\nexport default function Command() {\n  const files: Map<number, string> = new Map<number, string>();\n\n  fileLocations.forEach((element) => {\n    if (element != null && element != \"\") {\n      files.set(files.size, element);\n    }\n  });\n\n  return (\n    <Grid\n      columns={4}\n      inset={Grid.Inset.Small}\n      aspectRatio=\"4/3\"\n      filtering={false}\n      onSearchTextChange={(text) => {\n        const number: number = Number.parseInt(text);\n        if (number != null && number > 0 && number < 9) {\n          const path = files.get(number - 1);\n          if (path) {\n            open(path);\n          }\n        }\n      }}\n    >\n      {Array.from(files, ([key, value]) => (\n        <Grid.Item\n          key={key.valueOf()}\n          title={`| ${key + 1} | ${value.substring(value.lastIndexOf(\"/\") + 1, value.length)}`}\n          content={{ fileIcon: value }}\n          actions={\n            <ActionPanel>\n              <Action.Open title={`Open ${value.substring(value.lastIndexOf(\"/\") + 1, value.length)}`} target={value} />\n            </ActionPanel>\n          }\n        />\n      ))}\n    </Grid>\n  );\n}\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAqE,wBA+CvDC,EAAA,6BA7CRC,KAAc,uBAA+C,EAE7DC,EAAgB,CACpBD,EAAY,QACZA,EAAY,QACZA,EAAY,UACZA,EAAY,SACZA,EAAY,SACZA,EAAY,QACZA,EAAY,UACZA,EAAY,SACd,EAEe,SAARJ,GAA2B,CAChC,IAAMM,EAA6B,IAAI,IAEvC,OAAAD,EAAc,QAASE,GAAY,CAC7BA,GAAW,MAAQA,GAAW,IAChCD,EAAM,IAAIA,EAAM,KAAMC,CAAO,CAEjC,CAAC,KAGC,OAAC,QACC,QAAS,EACT,MAAO,OAAK,MAAM,MAClB,YAAY,MACZ,UAAW,GACX,mBAAqBC,GAAS,CAC5B,IAAMC,EAAiB,OAAO,SAASD,CAAI,EAC3C,GAAIC,GAAU,MAAQA,EAAS,GAAKA,EAAS,EAAG,CAC9C,IAAMC,EAAOJ,EAAM,IAAIG,EAAS,CAAC,EAC7BC,MACF,QAAKA,CAAI,CAEb,CACF,EAEC,eAAM,KAAKJ,EAAO,CAAC,CAACK,EAAKC,CAAK,OAC7B,OAAC,OAAK,KAAL,CAEC,MAAO,KAAKD,EAAM,CAAC,MAAMC,EAAM,UAAUA,EAAM,YAAY,GAAG,EAAI,EAAGA,EAAM,MAAM,CAAC,GAClF,QAAS,CAAE,SAAUA,CAAM,EAC3B,WACE,OAAC,eACC,mBAAC,SAAO,KAAP,CAAY,MAAO,QAAQA,EAAM,UAAUA,EAAM,YAAY,GAAG,EAAI,EAAGA,EAAM,MAAM,CAAC,GAAI,OAAQA,EAAO,EAC1G,GANGD,EAAI,QAAQ,CAQnB,CACD,EACH,CAEJ", "names": ["fileSpeedDial_exports", "__export", "Command", "__toCommonJS", "import_api", "import_jsx_runtime", "preferences", "fileLocations", "files", "element", "text", "number", "path", "key", "value"]}