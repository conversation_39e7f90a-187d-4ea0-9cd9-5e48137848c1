"use strict";var l=Object.defineProperty;var a=Object.getOwnPropertyDescriptor;var g=Object.getOwnPropertyNames;var p=Object.prototype.hasOwnProperty;var m=(t,e)=>{for(var n in e)l(t,n,{get:e[n],enumerable:!0})},h=(t,e,n,f)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of g(e))!p.call(t,o)&&o!==n&&l(t,o,{get:()=>e[o],enumerable:!(f=a(e,o))||f.enumerable});return t};var u=t=>h(l({},"__esModule",{value:!0}),t);var d={};m(d,{default:()=>c});module.exports=u(d);var i=require("@raycast/api"),s=require("react/jsx-runtime"),r=(0,i.getPreferenceValues)(),b=[r.fileOne,r.fileTwo,r.fileThree,r.fileFour,r.fileFive,r.fileSix,r.fileSeven,r.fileEight];function c(){let t=new Map;return b.forEach(e=>{e!=null&&e!=""&&t.set(t.size,e)}),(0,s.jsx)(i.Grid,{columns:4,inset:i.Grid.Inset.Small,aspectRatio:"4/3",filtering:!1,onSearchTextChange:e=>{let n=Number.parseInt(e);if(n!=null&&n>0&&n<9){let f=t.get(n-1);f&&(0,i.open)(f)}},children:Array.from(t,([e,n])=>(0,s.jsx)(i.Grid.Item,{title:`| ${e+1} | ${n.substring(n.lastIndexOf("/")+1,n.length)}`,content:{fileIcon:n},actions:(0,s.jsx)(i.ActionPanel,{children:(0,s.jsx)(i.Action.Open,{title:`Open ${n.substring(n.lastIndexOf("/")+1,n.length)}`,target:n})})},e.valueOf()))})}
