"use strict";var d=Object.defineProperty;var a=Object.getOwnPropertyDescriptor;var l=Object.getOwnPropertyNames;var g=Object.prototype.hasOwnProperty;var p=(r,e)=>{for(var n in e)d(r,n,{get:e[n],enumerable:!0})},m=(r,e,n,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of l(e))!g.call(r,s)&&s!==n&&d(r,s,{get:()=>e[s],enumerable:!(o=a(e,s))||o.enumerable});return r};var h=r=>m(d({},"__esModule",{value:!0}),r);var b={};p(b,{default:()=>f});module.exports=h(b);var t=require("@raycast/api"),c=require("react/jsx-runtime"),i=(0,t.getPreferenceValues)(),u=[i.dirOne,i.dirTwo,i.dirThree,i.dirFour,i.dirFive,i.dirSix,i.dirSeven,i.dirEight];function f(){let r=new Map;return u.forEach(e=>{e!=null&&e!=""&&r.set(r.size,e)}),(0,c.jsx)(t.Grid,{columns:4,inset:t.Grid.Inset.Small,aspectRatio:"4/3",filtering:!1,onSearchTextChange:e=>{let n=Number.parseInt(e);if(n!=null&&n>0&&n<9){let o=r.get(n-1);o&&(0,t.open)(o)}},children:Array.from(r,([e,n])=>(0,c.jsx)(t.Grid.Item,{title:`| ${e+1} | ${n.substring(n.lastIndexOf("/")+1,n.length)}`,content:{fileIcon:n},actions:(0,c.jsx)(t.ActionPanel,{children:(0,c.jsx)(t.Action.Open,{title:`Open ${n.substring(n.lastIndexOf("/")+1,n.length)}`,target:n})})},e.valueOf()))})}
