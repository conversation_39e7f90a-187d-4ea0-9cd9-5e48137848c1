"use strict";var c=Object.defineProperty;var d=Object.getOwnPropertyDescriptor;var $=Object.getOwnPropertyNames;var h=Object.prototype.hasOwnProperty;var y=(i,r)=>{for(var t in r)c(i,t,{get:r[t],enumerable:!0})},A=(i,r,t,e)=>{if(r&&typeof r=="object"||typeof r=="function")for(let o of $(r))!h.call(i,o)&&o!==t&&c(i,o,{get:()=>r[o],enumerable:!(e=d(r,o))||e.enumerable});return i};var u=i=>A(c({},"__esModule",{value:!0}),i);var I={};y(I,{default:()=>p});module.exports=u(I);var n=require("@raycast/api"),s=require("fs"),a=require("react/jsx-runtime"),l=(0,n.getPreferenceValues)(),D=l.downloadedFilesdir,m=l.layout;function p(){let i=(0,s.readdirSync)(D,{withFileTypes:!0}).sort((t,e)=>{let o=(0,s.statSync)(`${t.path}/${t.name}`).birthtime,f=(0,s.statSync)(`${e.path}/${e.name}`).birthtime;return o<f?1:-1}),r=new Map;return i.forEach(t=>{t.name.charAt(0)!="."&&t.isFile()&&r.set(r.size,t)}),m=="List"?(0,a.jsx)(n.List,{children:Array.from(r,([t,e])=>(0,a.jsx)(n.List.Item,{icon:{fileIcon:e.name},title:e.name,actions:(0,a.jsx)(n.ActionPanel,{children:(0,a.jsx)(n.Action.Open,{title:`Open ${e.name}`,target:`${e.path}/${e.name}`})})},t.valueOf()))}):(0,a.jsx)(n.Grid,{columns:Number.parseInt(m),inset:n.Grid.Inset.Small,aspectRatio:"4/3",children:Array.from(r,([t,e])=>(0,a.jsx)(n.Grid.Item,{content:{fileIcon:e.name},title:e.name,actions:(0,a.jsx)(n.ActionPanel,{children:(0,a.jsx)(n.Action.Open,{title:`Open ${e.name}`,target:`${e.path}/${e.name}`})})},t.valueOf()))})}
