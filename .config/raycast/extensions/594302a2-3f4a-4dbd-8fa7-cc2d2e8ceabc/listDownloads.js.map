{"version": 3, "sources": ["../src/listDownloads.tsx"], "sourcesContent": ["import { ActionPanel, Action, List, Grid, getPreferenceValues } from \"@raycast/api\";\nimport { readdirSync, Dirent, statSync } from \"fs\";\n\nconst preferences = getPreferenceValues<Preferences.ListDownloads>();\nconst dir = preferences.downloadedFilesdir;\nconst layout = preferences.layout;\n\nexport default function Command() {\n  const dirContents = readdirSync(dir, { withFileTypes: true }).sort((a, b) => {\n    const createA: Date = statSync(`${a.path}/${a.name}`).birthtime;\n    const createB: Date = statSync(`${b.path}/${b.name}`).birthtime;\n    return createA < createB ? 1 : -1;\n  });\n\n  const dirFiles: Map<number, Dirent> = new Map<number, Dirent>();\n\n  dirContents.forEach((item) => {\n    if (item.name.charAt(0) != \".\" && item.isFile()) {\n      dirFiles.set(dirFiles.size, item);\n    }\n  });\n\n  if (layout == \"List\") {\n    return (\n      <List>\n        {Array.from(dirFiles, ([key, value]) => (\n          <List.Item\n            key={key.valueOf()}\n            icon={{ fileIcon: value.name }}\n            title={value.name}\n            actions={\n              <ActionPanel>\n                <Action.Open title={`Open ${value.name}`} target={`${value.path}/${value.name}`} />\n              </ActionPanel>\n            }\n          />\n        ))}\n      </List>\n    );\n  } else {\n    return (\n      <Grid columns={Number.parseInt(layout)} inset={Grid.Inset.Small} aspectRatio=\"4/3\">\n        {Array.from(dirFiles, ([key, value]) => (\n          <Grid.Item\n            key={key.valueOf()}\n            content={{ fileIcon: value.name }}\n            title={value.name}\n            actions={\n              <ActionPanel>\n                <Action.Open title={`Open ${value.name}`} target={`${value.path}/${value.name}`} />\n              </ActionPanel>\n            }\n          />\n        ))}\n      </Grid>\n    );\n  }\n}\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAqE,wBACrEC,EAA8C,cA+B9BC,EAAA,6BA7BVC,KAAc,uBAA+C,EAC7DC,EAAMD,EAAY,mBAClBE,EAASF,EAAY,OAEZ,SAARL,GAA2B,CAChC,IAAMQ,KAAc,eAAYF,EAAK,CAAE,cAAe,EAAK,CAAC,EAAE,KAAK,CAACG,EAAGC,IAAM,CAC3E,IAAMC,KAAgB,YAAS,GAAGF,EAAE,IAAI,IAAIA,EAAE,IAAI,EAAE,EAAE,UAChDG,KAAgB,YAAS,GAAGF,EAAE,IAAI,IAAIA,EAAE,IAAI,EAAE,EAAE,UACtD,OAAOC,EAAUC,EAAU,EAAI,EACjC,CAAC,EAEKC,EAAgC,IAAI,IAQ1C,OANAL,EAAY,QAASM,GAAS,CACxBA,EAAK,KAAK,OAAO,CAAC,GAAK,KAAOA,EAAK,OAAO,GAC5CD,EAAS,IAAIA,EAAS,KAAMC,CAAI,CAEpC,CAAC,EAEGP,GAAU,UAEV,OAAC,QACE,eAAM,KAAKM,EAAU,CAAC,CAACE,EAAKC,CAAK,OAChC,OAAC,OAAK,KAAL,CAEC,KAAM,CAAE,SAAUA,EAAM,IAAK,EAC7B,MAAOA,EAAM,KACb,WACE,OAAC,eACC,mBAAC,SAAO,KAAP,CAAY,MAAO,QAAQA,EAAM,IAAI,GAAI,OAAQ,GAAGA,EAAM,IAAI,IAAIA,EAAM,IAAI,GAAI,EACnF,GANGD,EAAI,QAAQ,CAQnB,CACD,EACH,KAIA,OAAC,QAAK,QAAS,OAAO,SAASR,CAAM,EAAG,MAAO,OAAK,MAAM,MAAO,YAAY,MAC1E,eAAM,KAAKM,EAAU,CAAC,CAACE,EAAKC,CAAK,OAChC,OAAC,OAAK,KAAL,CAEC,QAAS,CAAE,SAAUA,EAAM,IAAK,EAChC,MAAOA,EAAM,KACb,WACE,OAAC,eACC,mBAAC,SAAO,KAAP,CAAY,MAAO,QAAQA,EAAM,IAAI,GAAI,OAAQ,GAAGA,EAAM,IAAI,IAAIA,EAAM,IAAI,GAAI,EACnF,GANGD,EAAI,QAAQ,CAQnB,CACD,EACH,CAGN", "names": ["listDownloads_exports", "__export", "Command", "__toCommonJS", "import_api", "import_fs", "import_jsx_runtime", "preferences", "dir", "layout", "dirContents", "a", "b", "createA", "createB", "dirFiles", "item", "key", "value"]}