{"version": 3, "sources": ["../src/projects.tsx"], "sourcesContent": ["import { open, getPreferenceValues } from \"@raycast/api\";\n\nconst dir = getPreferenceValues<Preferences.Projects>().projectsdir;\n\nexport default function Command() {\n  return open(`${dir}`);\n}\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA0C,wBAEpCC,KAAM,uBAA0C,EAAE,YAEzC,SAARH,GAA2B,CAChC,SAAO,QAAK,GAAGG,CAAG,EAAE,CACtB", "names": ["projects_exports", "__export", "Command", "__toCommonJS", "import_api", "dir"]}