{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "whatsapp", "title": "WhatsApp", "description": "Open WhatsApp chats", "icon": "whatsapp.png", "author": "vimtor", "contributors": ["xmok", "panka<PERSON>_gurbani"], "license": "MIT", "commands": [{"name": "open-chat", "title": "Open Chat", "subtitle": "WhatsApp", "description": "Open quickly added WhatsApp chats", "mode": "view"}, {"name": "add-chat", "title": "<PERSON><PERSON>", "subtitle": "WhatsApp", "description": "Add WhatsApp chats to open them later", "mode": "view"}, {"name": "add-existing-group", "title": "Add Existing Group", "subtitle": "WhatsApp", "description": "Add existing WhatsApp groups to open them later", "mode": "view"}], "tools": [{"name": "add-new-whatsapp-chat", "title": "Add new WhatsApp chat", "description": "Adds a new WhatsApp chat.", "instructions": "Add a new WhatsApp chat", "input": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the person"}, "phone": {"type": "string", "description": "The phone number"}, "pinned": {"type": "boolean", "description": "Whether the chat should be pinned"}}, "required": ["name", "phone"]}, "confirmation": true}, {"name": "add-whatsapp-group", "title": "Add WhatsApp Group", "description": "Adds an existing WhatsApp group.", "instructions": "Add an existing WhatsApp group", "input": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the group"}, "groupCode": {"type": "string", "description": "The group code"}, "pinned": {"type": "boolean", "description": "Whether the chat should be pinned"}}, "required": ["name", "groupCode"]}, "confirmation": true}, {"name": "open-chat", "title": "Open Chat", "description": "Opens a chat in WhatsApp", "instructions": "Opens a WhatsApp chat in either the app or web.", "input": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the person to open a chat with"}, "message": {"type": "string", "description": "The message to send"}, "openIn": {"type": "string", "description": "Open in web or app (\"web\" or \"app\")\n\n@default \"app\"", "enum": ["web", "app"]}}, "required": ["name"]}, "confirmation": false}], "ai": {"evals": [{"input": "@whatsapp Add <PERSON> with phone number +15551234567", "mocks": {"add-new-whatsapp-chat": "undefined"}, "expected": [{"callsTool": {"name": "add-new-whatsapp-chat", "arguments": {"name": "<PERSON>", "phone": "+15551234567"}}}, {"meetsCriteria": "Tells that <PERSON> has been added"}]}, {"input": "@whatsapp Add WhatsApp group Raycasters with code AbcdefgHijklmnop", "mocks": {"add-whatsapp-group": "undefined"}, "expected": [{"callsTool": {"name": "add-whatsapp-group", "arguments": {"name": "Raycasters", "groupCode": "AbcdefgHijklmnop"}}}, {"meetsCriteria": "Tells that the WhatsApp group Raycasters has been added"}]}, {"input": "@whatsapp Open chat with <PERSON>", "mocks": {"open-chat": "undefined"}, "expected": [{"callsTool": {"name": "open-chat", "arguments": {"name": "<PERSON>"}}}, {"meetsCriteria": "Tells that the chat with <PERSON> has been opened"}]}, {"input": "@whatsapp Send a message to <PERSON>: Hey <PERSON>, how are you?", "mocks": {"open-chat": "undefined"}, "expected": [{"callsTool": {"name": "open-chat", "arguments": {"name": "<PERSON>", "message": "Hey <PERSON>, how are you?"}}}, {"meetsCriteria": "Tells that the message has been sent to <PERSON>"}]}]}, "categories": ["Applications", "Communication", "Productivity"], "dependencies": {"@raycast/api": "^1.93.0", "@raycast/utils": "^1.19.0", "fromnow": "^3.0.1", "nanoid": "^4.0.1", "phone": "^3.1.35"}, "devDependencies": {"@raycast/eslint-config": "^1.0.8", "@types/node": "^20.8.10", "@types/react": "^18.3.3", "eslint": "^7.32.0", "prettier": "^2.5.1", "react": "^18.2.0", "typescript": "^4.4.3"}, "scripts": {"build": "ray build -e dist", "dev": "ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "prepublishOnly": "echo \"\\n\\nIt seems like you are trying to publish the Raycast extension to npm.\\n\\nIf you did intend to publish it to npm, remove the \\`prepublishOnly\\` script and rerun \\`npm publish\\` again.\\nIf you wanted to publish it to the Raycast Store instead, use \\`npm run publish\\` instead.\\n\\n\" && exit 1", "publish": "npx @raycast/api@latest publish"}}