{"version": 3, "sources": ["../../src/tools/add-whatsapp-group.ts", "../../src/utils/types.ts", "../../node_modules/nanoid/index.js", "../../node_modules/nanoid/url-alphabet/index.js", "../../src/services/saveWhatsappGroup.ts"], "sourcesContent": ["import { Tool, C<PERSON> } from \"@raycast/api\";\nimport { saveWhatsappGroup } from \"../services/saveWhatsappGroup\";\n\ntype Input = {\n  /**\n   * The name of the group\n   */\n  name: string;\n  /**\n   * The group code\n   */\n  groupCode: string;\n  /**\n   * Whether the chat should be pinned\n   */\n  pinned?: boolean;\n};\n\n/**\n * Add an existing WhatsApp group\n */\nexport default async function (input: Input) {\n  const cache = new Cache();\n  const chats = cache.get(\"whatsapp-chats\") || \"[]\";\n  await saveWhatsappGroup({\n    chat: {\n      name: input.name,\n      groupCode: input.groupCode,\n      pinned: input.pinned || false,\n    },\n    chats: JSON.parse(chats),\n    setChats: (chats) => cache.set(\"whatsapp-chats\", JSON.stringify(chats)),\n  });\n}\n\nexport const confirmation: Tool.Confirmation<Input> = async (input) => {\n  return {\n    message: `Do you want to add \"${input.name}\" to your groups?`,\n    info: [\n      {\n        name: \"Name\",\n        value: input.name,\n      },\n      {\n        name: \"Group Code\",\n        value: input.groupCode,\n      },\n      {\n        name: \"Pinned\",\n        value: input.pinned ? \"Yes\" : \"No\",\n      },\n    ],\n  };\n};\n", "export interface Chat {\n  id: string;\n  name: string;\n  pinned: boolean;\n  lastOpened?: number;\n}\n\nexport interface PhoneChat extends Chat {\n  phone: string;\n}\n\nexport interface GroupChat extends Chat {\n  groupCode: string;\n}\n\nexport type WhatsAppChat = PhoneChat | GroupChat;\n\nexport function isGroupChat(chat: WhatsAppChat): chat is GroupChat {\n  return (chat as GroupChat).groupCode !== undefined;\n}\n\nexport function isPhoneChat(chat: WhatsAppChat): chat is PhoneChat {\n  return (chat as PhoneChat).phone !== undefined;\n}\n", "import { randomFillSync } from 'crypto'\nimport { urlAlphabet } from './url-alphabet/index.js'\nexport { urlAlphabet }\nconst POOL_SIZE_MULTIPLIER = 128\nlet pool, poolOffset\nlet fillPool = bytes => {\n  if (!pool || pool.length < bytes) {\n    pool = Buffer.allocUnsafe(bytes * POOL_SIZE_MULTIPLIER)\n    randomFillSync(pool)\n    poolOffset = 0\n  } else if (poolOffset + bytes > pool.length) {\n    randomFillSync(pool)\n    poolOffset = 0\n  }\n  poolOffset += bytes\n}\nexport let random = bytes => {\n  fillPool((bytes -= 0))\n  return pool.subarray(poolOffset - bytes, poolOffset)\n}\nexport let customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << (31 - Math.clz32((alphabet.length - 1) | 1))) - 1\n  let step = Math.ceil((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let i = step\n      while (i--) {\n        id += alphabet[bytes[i] & mask] || ''\n        if (id.length === size) return id\n      }\n    }\n  }\n}\nexport let customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size, random)\nexport let nanoid = (size = 21) => {\n  fillPool((size -= 0))\n  let id = ''\n  for (let i = poolOffset - size; i < poolOffset; i++) {\n    id += urlAlphabet[pool[i] & 63]\n  }\n  return id\n}\n", "export const urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n", "import { isGroupChat, WhatsAppChat, GroupChat } from \"../utils/types\";\nimport { nanoid as randomId } from \"nanoid\";\n\ninterface SaveWhatsappGroupProps {\n  chat: Omit<GroupChat, \"id\"> & { id?: string };\n  chats: WhatsAppChat[];\n  setChats: (chats: WhatsAppChat[]) => void;\n}\n\nexport async function saveWhatsappGroup({ chat, chats, setChats }: SaveWhatsappGroupProps) {\n  const isCreation = !chat.id;\n\n  if (!chat.groupCode) {\n    throw new Error(\"Group Code is required\");\n  }\n\n  const chatToSave: WhatsAppChat = {\n    id: chat.id || randomId(),\n    name: chat.name,\n    pinned: !!chat.pinned,\n    groupCode: chat.groupCode,\n  } as GroupChat;\n\n  const doesGroupCodeAlreadyExist = chats.filter(isGroupChat).some((c) => c.groupCode === chat.groupCode);\n\n  if (isCreation && doesGroupCodeAlreadyExist) {\n    throw new Error(\"Chat already exists\");\n  }\n\n  if (isCreation) {\n    setChats([...chats, chatToSave]);\n  } else {\n    const newChats = chats.map((c) => {\n      if (c.id === chatToSave.id) {\n        return chatToSave;\n      }\n      return c;\n    });\n    setChats(newChats);\n  }\n}\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,kBAAAE,EAAA,YAAAC,IAAA,eAAAC,EAAAJ,GAAA,IAAAK,EAA4B,wBCiBrB,SAASC,EAAYC,EAAuC,CACjE,OAAQA,EAAmB,YAAc,MAC3C,CCnBA,IAAAC,EAA+B,kBCAxB,IAAMC,EACX,mEDEF,IAAMC,EAAuB,IACzBC,EAAMC,EACNC,EAAWC,GAAS,CAClB,CAACH,GAAQA,EAAK,OAASG,GACzBH,EAAO,OAAO,YAAYG,EAAQJ,CAAoB,KACtD,kBAAeC,CAAI,EACnBC,EAAa,GACJA,EAAaE,EAAQH,EAAK,YACnC,kBAAeA,CAAI,EACnBC,EAAa,GAEfA,GAAcE,CAChB,EAsBO,IAAIC,EAAS,CAACC,EAAO,KAAO,CACjCC,EAAUD,GAAQ,CAAE,EACpB,IAAIE,EAAK,GACT,QAASC,EAAIC,EAAaJ,EAAMG,EAAIC,EAAYD,IAC9CD,GAAMG,EAAYC,EAAKH,CAAC,EAAI,EAAE,EAEhC,OAAOD,CACT,EEnCA,eAAsBK,EAAkB,CAAE,KAAAC,EAAM,MAAAC,EAAO,SAAAC,CAAS,EAA2B,CACzF,IAAMC,EAAa,CAACH,EAAK,GAEzB,GAAI,CAACA,EAAK,UACR,MAAM,IAAI,MAAM,wBAAwB,EAG1C,IAAMI,EAA2B,CAC/B,GAAIJ,EAAK,IAAMK,EAAS,EACxB,KAAML,EAAK,KACX,OAAQ,CAAC,CAACA,EAAK,OACf,UAAWA,EAAK,SAClB,EAEMM,EAA4BL,EAAM,OAAOM,CAAW,EAAE,KAAMC,GAAMA,EAAE,YAAcR,EAAK,SAAS,EAEtG,GAAIG,GAAcG,EAChB,MAAM,IAAI,MAAM,qBAAqB,EAGvC,GAAIH,EACFD,EAAS,CAAC,GAAGD,EAAOG,CAAU,CAAC,MAC1B,CACL,IAAMK,EAAWR,EAAM,IAAKO,GACtBA,EAAE,KAAOJ,EAAW,GACfA,EAEFI,CACR,EACDN,EAASO,CAAQ,CACnB,CACF,CJnBA,eAAOC,EAAwBC,EAAc,CAC3C,IAAMC,EAAQ,IAAI,QACZC,EAAQD,EAAM,IAAI,gBAAgB,GAAK,KAC7C,MAAME,EAAkB,CACtB,KAAM,CACJ,KAAMH,EAAM,KACZ,UAAWA,EAAM,UACjB,OAAQA,EAAM,QAAU,EAC1B,EACA,MAAO,KAAK,MAAME,CAAK,EACvB,SAAWA,GAAUD,EAAM,IAAI,iBAAkB,KAAK,UAAUC,CAAK,CAAC,CACxE,CAAC,CACH,CAEO,IAAME,EAAyC,MAAOJ,IACpD,CACL,QAAS,uBAAuBA,EAAM,IAAI,oBAC1C,KAAM,CACJ,CACE,KAAM,OACN,MAAOA,EAAM,IACf,EACA,CACE,KAAM,aACN,MAAOA,EAAM,SACf,EACA,CACE,KAAM,SACN,MAAOA,EAAM,OAAS,MAAQ,IAChC,CACF,CACF", "names": ["add_whatsapp_group_exports", "__export", "confirmation", "add_whatsapp_group_default", "__toCommonJS", "import_api", "isGroupChat", "chat", "import_crypto", "url<PERSON>l<PERSON><PERSON>", "POOL_SIZE_MULTIPLIER", "pool", "poolOffset", "fillPool", "bytes", "nanoid", "size", "fillPool", "id", "i", "poolOffset", "url<PERSON>l<PERSON><PERSON>", "pool", "saveWhatsappGroup", "chat", "chats", "setChats", "isCreation", "chatToSave", "nanoid", "doesGroupCodeAlreadyExist", "isGroupChat", "c", "newChats", "add_whatsapp_group_default", "input", "cache", "chats", "saveWhatsappGroup", "confirmation"]}