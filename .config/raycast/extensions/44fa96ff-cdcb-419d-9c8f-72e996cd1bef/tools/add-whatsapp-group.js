"use strict";var i=Object.defineProperty;var g=Object.getOwnPropertyDescriptor;var x=Object.getOwnPropertyNames;var G=Object.prototype.hasOwnProperty;var w=(e,t)=>{for(var o in t)i(e,o,{get:t[o],enumerable:!0})},A=(e,t,o,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of x(t))!G.call(e,r)&&r!==o&&i(e,r,{get:()=>t[r],enumerable:!(a=g(t,r))||a.enumerable});return e};var P=e=>A(i({},"__esModule",{value:!0}),e);var O={};w(O,{confirmation:()=>I,default:()=>y});module.exports=P(O);var f=require("@raycast/api");function d(e){return e.groupCode!==void 0}var h=require("crypto");var l="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";var W=128,n,p,v=e=>{!n||n.length<e?(n=Buffer.allocUnsafe(e*W),(0,h.randomFillSync)(n),p=0):p+e>n.length&&((0,h.randomFillSync)(n),p=0),p+=e};var C=(e=21)=>{v(e-=0);let t="";for(let o=p-e;o<p;o++)t+=l[n[o]&63];return t};async function m({chat:e,chats:t,setChats:o}){let a=!e.id;if(!e.groupCode)throw new Error("Group Code is required");let r={id:e.id||C(),name:e.name,pinned:!!e.pinned,groupCode:e.groupCode},c=t.filter(d).some(s=>s.groupCode===e.groupCode);if(a&&c)throw new Error("Chat already exists");if(a)o([...t,r]);else{let s=t.map(u=>u.id===r.id?r:u);o(s)}}async function y(e){let t=new f.Cache,o=t.get("whatsapp-chats")||"[]";await m({chat:{name:e.name,groupCode:e.groupCode,pinned:e.pinned||!1},chats:JSON.parse(o),setChats:a=>t.set("whatsapp-chats",JSON.stringify(a))})}var I=async e=>({message:`Do you want to add "${e.name}" to your groups?`,info:[{name:"Name",value:e.name},{name:"Group Code",value:e.groupCode},{name:"Pinned",value:e.pinned?"Yes":"No"}]});0&&(module.exports={confirmation});
