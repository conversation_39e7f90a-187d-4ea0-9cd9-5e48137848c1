"use strict";var u=Object.defineProperty;var w=Object.getOwnPropertyDescriptor;var x=Object.getOwnPropertyNames;var b=Object.prototype.hasOwnProperty;var A=(t,e)=>{for(var n in e)u(t,n,{get:e[n],enumerable:!0})},W=(t,e,n,p)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of x(e))!b.call(t,a)&&a!==n&&u(t,a,{get:()=>e[a],enumerable:!(p=w(e,a))||p.enumerable});return t};var I=t=>W(u({},"__esModule",{value:!0}),t);var y={};A(y,{default:()=>P});module.exports=I(y);var f=require("@raycast/api");var C=require("@raycast/api");function d(t){return t.phone!==void 0}function O(t,e){let n=t.length>e.length?t:e,p=t.length>e.length?e:t,a=n.length;return a==0?1:(a-n.split("").reduce((r,o,c)=>p[c]===o?r:r+1,0))/a}async function g({chatName:t,message:e="",openIn:n="web"}){let p=new C.Cache,a=p.get("whatsapp-chats")||"[]",r=JSON.parse(a),o,c=0;for(let s of r){let h=O(t.toLowerCase(),s.name.toLowerCase());h>c&&(c=h,o=s)}if(!o)throw new Error("Chat not found");(s=>{let h=r.map(i=>i.id===s.id?{...i,lastOpened:Date.now()}:i);p.set("whatsapp-chats",JSON.stringify(h))})(o);let l;if(d(o)){let s=o.phone.replace(/[^D]/,""),h=encodeURIComponent(e),i=`whatsapp://send?phone=${s}&text=${h}`,m=`https://web.whatsapp.com/send?phone=${s}&text=${h}`;l=n==="app"?i:m}else l=`whatsapp://chat?code=${o.groupCode}`;await(0,C.open)(l)}async function P(t){let e=await(0,f.getApplications)(),n=t.openIn??(e.some(p=>p.name.includes("WhatsApp")&&p.bundleId?.includes("net.whatsapp.WhatsApp"))?"app":"web");await g({chatName:t.name,message:t.message,openIn:n})}
