{"version": 3, "sources": ["../../src/tools/open-chat.ts", "../../src/services/openChat.ts", "../../src/utils/types.ts"], "sourcesContent": ["import { getApplications } from \"@raycast/api\";\nimport { openChat } from \"../services/openChat\";\n\ntype Input = {\n  /**\n   * The name of the person to open a chat with\n   */\n  name: string;\n  /**\n   * The message to send\n   */\n  message?: string;\n  /**\n   * Open in web or app (\"web\" or \"app\")\n   * @default \"app\"\n   */\n  openIn?: \"web\" | \"app\";\n};\n\n/**\n * Opens a WhatsApp chat in either the app or web.\n */\nexport default async function (input: Input) {\n  const installedApps = await getApplications();\n  const openIn =\n    input.openIn ??\n    (installedApps.some((app) => app.name.includes(\"WhatsApp\") && app.bundleId?.includes(\"net.whatsapp.WhatsApp\"))\n      ? \"app\"\n      : \"web\");\n  await openChat({ chatName: input.name, message: input.message, openIn });\n}\n", "import { open, Cache } from \"@raycast/api\";\nimport { WhatsAppChat, isPhoneChat } from \"../utils/types\";\n\ninterface OpenChatProps {\n  chatName: string;\n  message?: string;\n  openIn?: \"app\" | \"web\";\n}\n\nfunction calculateSimilarity(str1: string, str2: string): number {\n  const longer = str1.length > str2.length ? str1 : str2;\n  const shorter = str1.length > str2.length ? str2 : str1;\n  const longerLength = longer.length;\n  if (longerLength == 0) {\n    return 1.0;\n  }\n  return (\n    (longerLength -\n      longer.split(\"\").reduce((count, letter, index) => {\n        return shorter[index] === letter ? count : count + 1;\n      }, 0)) /\n    longerLength\n  );\n}\n\nexport async function openChat({ chatName, message = \"\", openIn = \"web\" }: OpenChatProps) {\n  const cache = new Cache();\n  const chatsString = cache.get(\"whatsapp-chats\") || \"[]\";\n  const chats: WhatsAppChat[] = JSON.parse(chatsString);\n\n  let bestMatch: WhatsAppChat | undefined;\n  let bestScore = 0;\n\n  for (const chat of chats) {\n    const score = calculateSimilarity(chatName.toLowerCase(), chat.name.toLowerCase());\n    if (score > bestScore) {\n      bestScore = score;\n      bestMatch = chat;\n    }\n  }\n\n  if (!bestMatch) {\n    throw new Error(\"Chat not found\");\n  }\n\n  const handleOpen = (chat: WhatsAppChat) => {\n    const newChats = chats.map((c) => {\n      if (c.id === chat.id) {\n        return { ...c, lastOpened: Date.now() };\n      }\n      return c;\n    });\n    cache.set(\"whatsapp-chats\", JSON.stringify(newChats));\n  };\n\n  handleOpen(bestMatch);\n\n  let url: string;\n  if (isPhoneChat(bestMatch)) {\n    const phone = bestMatch.phone.replace(/[^D]/, \"\");\n    const text = encodeURIComponent(message);\n    const appUrl = `whatsapp://send?phone=${phone}&text=${text}`;\n    const webUrl = `https://web.whatsapp.com/send?phone=${phone}&text=${text}`;\n    url = openIn === \"app\" ? appUrl : webUrl;\n  } else {\n    const appUrl = `whatsapp://chat?code=${bestMatch.groupCode}`;\n    url = appUrl;\n  }\n\n  await open(url);\n}\n", "export interface Chat {\n  id: string;\n  name: string;\n  pinned: boolean;\n  lastOpened?: number;\n}\n\nexport interface PhoneChat extends Chat {\n  phone: string;\n}\n\nexport interface GroupChat extends Chat {\n  groupCode: string;\n}\n\nexport type WhatsAppChat = PhoneChat | GroupChat;\n\nexport function isGroupChat(chat: WhatsAppChat): chat is GroupChat {\n  return (chat as GroupChat).groupCode !== undefined;\n}\n\nexport function isPhoneChat(chat: WhatsAppChat): chat is PhoneChat {\n  return (chat as PhoneChat).phone !== undefined;\n}\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAgC,wBCAhC,IAAAC,EAA4B,wBCqBrB,SAASC,EAAYC,EAAuC,CACjE,OAAQA,EAAmB,QAAU,MACvC,CDdA,SAASC,EAAoBC,EAAcC,EAAsB,CAC/D,IAAMC,EAASF,EAAK,OAASC,EAAK,OAASD,EAAOC,EAC5CE,EAAUH,EAAK,OAASC,EAAK,OAASA,EAAOD,EAC7CI,EAAeF,EAAO,OAC5B,OAAIE,GAAgB,EACX,GAGNA,EACCF,EAAO,MAAM,EAAE,EAAE,OAAO,CAACG,EAAOC,EAAQC,IAC/BJ,EAAQI,CAAK,IAAMD,EAASD,EAAQA,EAAQ,EAClD,CAAC,GACND,CAEJ,CAEA,eAAsBI,EAAS,CAAE,SAAAC,EAAU,QAAAC,EAAU,GAAI,OAAAC,EAAS,KAAM,EAAkB,CACxF,IAAMC,EAAQ,IAAI,QACZC,EAAcD,EAAM,IAAI,gBAAgB,GAAK,KAC7CE,EAAwB,KAAK,MAAMD,CAAW,EAEhDE,EACAC,EAAY,EAEhB,QAAWC,KAAQH,EAAO,CACxB,IAAMI,EAAQnB,EAAoBU,EAAS,YAAY,EAAGQ,EAAK,KAAK,YAAY,CAAC,EAC7EC,EAAQF,IACVA,EAAYE,EACZH,EAAYE,EAEhB,CAEA,GAAI,CAACF,EACH,MAAM,IAAI,MAAM,gBAAgB,GAGdE,GAAuB,CACzC,IAAME,EAAWL,EAAM,IAAKM,GACtBA,EAAE,KAAOH,EAAK,GACT,CAAE,GAAGG,EAAG,WAAY,KAAK,IAAI,CAAE,EAEjCA,CACR,EACDR,EAAM,IAAI,iBAAkB,KAAK,UAAUO,CAAQ,CAAC,CACtD,GAEWJ,CAAS,EAEpB,IAAIM,EACJ,GAAIC,EAAYP,CAAS,EAAG,CAC1B,IAAMQ,EAAQR,EAAU,MAAM,QAAQ,OAAQ,EAAE,EAC1CS,EAAO,mBAAmBd,CAAO,EACjCe,EAAS,yBAAyBF,CAAK,SAASC,CAAI,GACpDE,EAAS,uCAAuCH,CAAK,SAASC,CAAI,GACxEH,EAAMV,IAAW,MAAQc,EAASC,CACpC,MAEEL,EADe,wBAAwBN,EAAU,SAAS,GAI5D,QAAM,QAAKM,CAAG,CAChB,CDhDA,eAAOM,EAAwBC,EAAc,CAC3C,IAAMC,EAAgB,QAAM,mBAAgB,EACtCC,EACJF,EAAM,SACLC,EAAc,KAAME,GAAQA,EAAI,KAAK,SAAS,UAAU,GAAKA,EAAI,UAAU,SAAS,uBAAuB,CAAC,EACzG,MACA,OACN,MAAMC,EAAS,CAAE,SAAUJ,EAAM,KAAM,QAASA,EAAM,QAAS,OAAAE,CAAO,CAAC,CACzE", "names": ["open_chat_exports", "__export", "open_chat_default", "__toCommonJS", "import_api", "import_api", "isPhoneChat", "chat", "calculateSimilarity", "str1", "str2", "longer", "shorter", "<PERSON><PERSON><PERSON><PERSON>", "count", "letter", "index", "openChat", "chatName", "message", "openIn", "cache", "chatsString", "chats", "bestMatch", "bestScore", "chat", "score", "newChats", "c", "url", "isPhoneChat", "phone", "text", "appUrl", "webUrl", "open_chat_default", "input", "installedApps", "openIn", "app", "openChat"]}