{"version": 3, "sources": ["../../node_modules/phone/dist/data/country_phone_data.js", "../../node_modules/phone/dist/lib/utility.js", "../../node_modules/phone/dist/index.js", "../../src/tools/add-new-whatsapp-chat.ts", "../../src/utils/types.ts", "../../src/services/saveChat.ts", "../../node_modules/nanoid/index.js", "../../node_modules/nanoid/url-alphabet/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.default = [\n    {\n        alpha2: 'US',\n        alpha3: 'USA',\n        country_code: '1',\n        country_name: 'United States',\n        mobile_begin_with: ['201', '202', '203', '205', '206', '207', '208', '209', '210', '212', '213', '214', '215',\n            '216', '217', '218', '219', '220', '223', '224', '225', '227', '228', '229', '231', '234', '239', '240',\n            '248', '251', '252', '253', '254', '256', '260', '262', '267', '269', '270', '272', '274', '276', '278',\n            '281', '283', '301', '302', '303', '304', '305', '307', '308', '309', '310', '312', '313', '314', '315',\n            '316', '317', '318', '319', '320', '321', '323', '325', '327', '330', '331', '332', '334', '336', '337',\n            '339', '341', '346', '347', '351', '352', '360', '361', '364', '369', '380', '385', '386', '401', '402',\n            '404', '405', '406', '407', '408', '409', '410', '412', '413', '414', '415', '417', '419', '423', '424',\n            '425', '430', '432', '434', '435', '440', '441', '442', '443', '445', '447', '458', '463', '464', '469', '470', '475',\n            '478', '479', '480', '484', '501', '502', '503', '504', '505', '507', '508', '509', '510', '512', '513',\n            '515', '516', '517', '518', '520', '530', '531', '534', '539', '540', '541', '551', '557', '559', '561',\n            '562', '563', '564', '567', '570', '571', '572', '573', '574', '575', '580', '582', '585', '586', '601', '602',\n            '603', '605', '606', '607', '608', '609', '610', '612', '614', '615', '616', '617', '618', '619', '620',\n            '623', '626', '627', '628', '629', '630', '631', '636', '640', '641', '646', '650', '651', '656', '657', '659', '660',\n            '661', '662', '667', '669', '678', '679', '680', '681', '682', '689', '701', '702', '703', '704', '706', '707',\n            '708', '712', '713', '714', '715', '716', '717', '718', '719', '720', '724', '725', '726', '727', '730', '731',\n            '732', '734', '737', '740', '743', '747', '752', '754', '757', '760', '762', '763', '764', '765', '769', '770', '771',\n            '772', '773', '774', '775', '779', '781', '785', '786', '787', '801', '802', '803', '804', '805', '806', '808',\n            '810', '812', '813', '814', '815', '816', '817', '818', '820', '828', '830', '831', '832', '835', '838', '840', '843', '845',\n            '847', '848', '850', '854', '856', '857', '858', '859', '860', '862', '863', '864', '865', '870', '872',\n            '878', '901', '903', '904', '906', '907', '908', '909', '910', '912', '913', '914', '915', '916', '917',\n            '918', '919', '920', '925', '927', '928', '929', '930', '931', '934', '935', '936', '937', '938', '939', '940', '941', '945',\n            '947', '949', '951', '952', '954', '956', '957', '959', '970', '971', '972', '973', '975', '978', '979',\n            '980', '984', '985', '986', '989', '888', '800', '833', '844', '855', '866', '877', '279'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'AW',\n        alpha3: 'ABW',\n        country_code: '297',\n        country_name: 'Aruba',\n        mobile_begin_with: ['5', '6', '7', '9'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'AF',\n        alpha3: 'AFG',\n        country_code: '93',\n        country_name: 'Afghanistan',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'AO',\n        alpha3: 'AGO',\n        country_code: '244',\n        country_name: 'Angola',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'AI',\n        alpha3: 'AIA',\n        country_code: '1',\n        country_name: 'Anguilla',\n        mobile_begin_with: ['2645', '2647'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'AX',\n        alpha3: 'ALA',\n        country_code: '358',\n        country_name: 'Åland Islands',\n        mobile_begin_with: ['18'],\n        phone_number_lengths: [6, 7, 8]\n    },\n    {\n        alpha2: 'AL',\n        alpha3: 'ALB',\n        country_code: '355',\n        country_name: 'Albania',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'AD',\n        alpha3: 'AND',\n        country_code: '376',\n        country_name: 'Andorra',\n        mobile_begin_with: ['3', '4', '6'],\n        phone_number_lengths: [6]\n    },\n    // {alpha2: \"AN\", alpha3: \"ANT\", country_code: \"599\", country_name: \"Netherlands Antilles\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'AE',\n        alpha3: 'ARE',\n        country_code: '971',\n        country_name: 'United Arab Emirates',\n        mobile_begin_with: ['5'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'AR',\n        alpha3: 'ARG',\n        country_code: '54',\n        country_name: 'Argentina',\n        mobile_begin_with: ['1', '2', '3'],\n        phone_number_lengths: [8, 9, 10, 11, 12]\n    },\n    {\n        alpha2: 'AM',\n        alpha3: 'ARM',\n        country_code: '374',\n        country_name: 'Armenia',\n        mobile_begin_with: ['3', '4', '5', '7', '9'],\n        phone_number_lengths: [8]\n    },\n    // http://www.howtocallabroad.com/results.php?callfrom=united_states&callto=american_samoa\n    {\n        alpha2: 'AS',\n        alpha3: 'ASM',\n        country_code: '1',\n        country_name: 'American Samoa',\n        mobile_begin_with: ['684733', '684258'],\n        phone_number_lengths: [10]\n    },\n    // {alpha2: \"AQ\", alpha3: \"ATA\", country_code: \"672\", country_name: \"Antarctica\", mobile_begin_with: [], phone_number_lengths: []},\n    // {alpha2: \"TF\", alpha3: \"ATF\", country_code: \"\", country_name: \"French Southern Territories\", mobile_begin_with: [], phone_number_lengths: []},\n    // http://www.howtocallabroad.com/results.php?callfrom=united_states&callto=antigua_barbuda\n    {\n        alpha2: 'AG',\n        alpha3: 'ATG',\n        country_code: '1',\n        country_name: 'Antigua and Barbuda',\n        mobile_begin_with: ['2687'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'AU',\n        alpha3: 'AUS',\n        country_code: '61',\n        country_name: 'Australia',\n        mobile_begin_with: ['4'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'AT',\n        alpha3: 'AUT',\n        country_code: '43',\n        country_name: 'Austria',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [10, 11, 12, 13, 14]\n    },\n    {\n        alpha2: 'AZ',\n        alpha3: 'AZE',\n        country_code: '994',\n        country_name: 'Azerbaijan',\n        mobile_begin_with: ['4', '5', '6', '7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'BI',\n        alpha3: 'BDI',\n        country_code: '257',\n        country_name: 'Burundi',\n        mobile_begin_with: ['7', '29'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'BE',\n        alpha3: 'BEL',\n        country_code: '32',\n        country_name: 'Belgium',\n        mobile_begin_with: ['4', '3'],\n        phone_number_lengths: [9, 8]\n    },\n    {\n        alpha2: 'BJ',\n        alpha3: 'BEN',\n        country_code: '229',\n        country_name: 'Benin',\n        mobile_begin_with: ['4', '6', '9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'BF',\n        alpha3: 'BFA',\n        country_code: '226',\n        country_name: 'Burkina Faso',\n        mobile_begin_with: ['6', '7'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'BD',\n        alpha3: 'BGD',\n        country_code: '880',\n        country_name: 'Bangladesh',\n        mobile_begin_with: ['1'],\n        phone_number_lengths: [8, 9, 10]\n    },\n    {\n        alpha2: 'BG',\n        alpha3: 'BGR',\n        country_code: '359',\n        country_name: 'Bulgaria',\n        mobile_begin_with: ['87', '88', '89', '98', '99', '43'],\n        phone_number_lengths: [8, 9]\n    },\n    {\n        alpha2: 'BH',\n        alpha3: 'BHR',\n        country_code: '973',\n        country_name: 'Bahrain',\n        mobile_begin_with: ['3'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'BS',\n        alpha3: 'BHS',\n        country_code: '1',\n        country_name: 'Bahamas',\n        mobile_begin_with: ['242'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'BA',\n        alpha3: 'BIH',\n        country_code: '387',\n        country_name: 'Bosnia and Herzegovina',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [8]\n    },\n    // {alpha2: \"BL\", alpha3: \"BLM\", country_code: \"590\", country_name: \"Saint Barthélemy\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'BY',\n        alpha3: 'BLR',\n        country_code: '375',\n        country_name: 'Belarus',\n        mobile_begin_with: ['25', '29', '33', '44'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'BZ',\n        alpha3: 'BLZ',\n        country_code: '501',\n        country_name: 'Belize',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [7]\n    },\n    // http://www.howtocallabroad.com/results.php?callfrom=united_states&callto=bermuda\n    {\n        alpha2: 'BM',\n        alpha3: 'BMU',\n        country_code: '1',\n        country_name: 'Bermuda',\n        mobile_begin_with: ['4413', '4415', '4417'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'BO',\n        alpha3: 'BOL',\n        country_code: '591',\n        country_name: 'Bolivia',\n        mobile_begin_with: ['6', '7'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'BR',\n        alpha3: 'BRA',\n        country_code: '55',\n        country_name: 'Brazil',\n        mobile_begin_with: [\n            '119', '129', '139', '149', '159', '169', '179', '189', '199', '219', '229', '249', '279', '289',\n            '319', '329', '339', '349', '359', '379', '389',\n            '419', '429', '439', '449', '459', '469', '479', '489', '499',\n            '519', '539', '549', '559',\n            '619', '629', '639', '649', '659', '669', '679', '689', '699',\n            '719', '739', '749', '759', '779', '799',\n            '819', '829', '839', '849', '859', '869', '879', '889', '899',\n            '919', '929', '939', '949', '959', '969', '979', '989', '999',\n        ],\n        phone_number_lengths: [10, 11]\n    },\n    {\n        alpha2: 'BB',\n        alpha3: 'BRB',\n        country_code: '1',\n        country_name: 'Barbados',\n        mobile_begin_with: ['246'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'BN',\n        alpha3: 'BRN',\n        country_code: '673',\n        country_name: 'Brunei Darussalam',\n        mobile_begin_with: ['7', '8'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'BT',\n        alpha3: 'BTN',\n        country_code: '975',\n        country_name: 'Bhutan',\n        mobile_begin_with: ['17'],\n        phone_number_lengths: [8]\n    },\n    // {alpha2: \"BV\", alpha3: \"BVT\", country_code: \"\", country_name: \"Bouvet Island\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'BW',\n        alpha3: 'BWA',\n        country_code: '267',\n        country_name: 'Botswana',\n        mobile_begin_with: ['71', '72', '73', '74', '75', '76'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'CF',\n        alpha3: 'CAF',\n        country_code: '236',\n        country_name: 'Central African Republic',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [8]\n    },\n    // http://www.howtocallabroad.com/canada/\n    // http://areacode.org/\n    // http://countrycode.org/canada\n    {\n        alpha2: 'CA',\n        alpha3: 'CAN',\n        country_code: '1',\n        country_name: 'Canada',\n        mobile_begin_with: [\n            '204', '226', '236', '249', '250', '263', '289', '306', '343', '354',\n            '365', '367', '368', '403', '416', '418', '431', '437', '438', '450',\n            '468', '474', '506', '514', '519', '548', '579', '581', '584', '587',\n            '600', '604', '613', '639', '647', '672', '683', '705', '709', '742',\n            '753', '778', '780', '782', '807', '819', '825', '867', '873', '902',\n            '905'\n        ],\n        phone_number_lengths: [10]\n    },\n    // {alpha2: \"CC\", alpha3: \"CCK\", country_code: \"61\", country_name: \"Cocos (Keeling) Islands\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'CH',\n        alpha3: 'CHE',\n        country_code: '41',\n        country_name: 'Switzerland',\n        mobile_begin_with: ['74', '75', '76', '77', '78', '79'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'CL',\n        alpha3: 'CHL',\n        country_code: '56',\n        country_name: 'Chile',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'CN',\n        alpha3: 'CHN',\n        country_code: '86',\n        country_name: 'China',\n        mobile_begin_with: ['13', '14', '15', '17', '18', '19', '16'],\n        phone_number_lengths: [11]\n    },\n    {\n        alpha2: 'CI',\n        alpha3: 'CIV',\n        country_code: '225',\n        country_name: \"Côte D'Ivoire\",\n        mobile_begin_with: ['0', '4', '5', '6', '7', '8'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'CM',\n        alpha3: 'CMR',\n        country_code: '237',\n        country_name: 'Cameroon',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'CD',\n        alpha3: 'COD',\n        country_code: '243',\n        country_name: 'Congo, The Democratic Republic Of The',\n        mobile_begin_with: ['8', '9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'CG',\n        alpha3: 'COG',\n        country_code: '242',\n        country_name: 'Congo',\n        mobile_begin_with: ['0'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'CK',\n        alpha3: 'COK',\n        country_code: '682',\n        country_name: 'Cook Islands',\n        mobile_begin_with: ['5', '7'],\n        phone_number_lengths: [5]\n    },\n    {\n        alpha2: 'CO',\n        alpha3: 'COL',\n        country_code: '57',\n        country_name: 'Colombia',\n        mobile_begin_with: ['3'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'CW',\n        alpha3: 'CUW',\n        country_code: '5999',\n        country_name: 'Curaçao',\n        mobile_begin_with: ['5', '6'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'KM',\n        alpha3: 'COM',\n        country_code: '269',\n        country_name: 'Comoros',\n        mobile_begin_with: ['3', '76'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'CV',\n        alpha3: 'CPV',\n        country_code: '238',\n        country_name: 'Cape Verde',\n        mobile_begin_with: ['5', '9'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'CR',\n        alpha3: 'CRI',\n        country_code: '506',\n        country_name: 'Costa Rica',\n        mobile_begin_with: ['5', '6', '7', '8'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'CU',\n        alpha3: 'CUB',\n        country_code: '53',\n        country_name: 'Cuba',\n        mobile_begin_with: ['5'],\n        phone_number_lengths: [8]\n    },\n    // {alpha2: \"CX\", alpha3: \"CXR\", country_code: \"61\", country_name: \"Christmas Island\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'KY',\n        alpha3: 'CYM',\n        country_code: '1',\n        country_name: 'Cayman Islands',\n        mobile_begin_with: ['345'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'CY',\n        alpha3: 'CYP',\n        country_code: '357',\n        country_name: 'Cyprus',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'CZ',\n        alpha3: 'CZE',\n        country_code: '420',\n        country_name: 'Czech Republic',\n        mobile_begin_with: ['6', '7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'DE',\n        alpha3: 'DEU',\n        country_code: '49',\n        country_name: 'Germany',\n        mobile_begin_with: ['15', '16', '17'],\n        phone_number_lengths: [10, 11]\n    },\n    {\n        alpha2: 'DJ',\n        alpha3: 'DJI',\n        country_code: '253',\n        country_name: 'Djibouti',\n        mobile_begin_with: ['77'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'DM',\n        alpha3: 'DMA',\n        country_code: '1',\n        country_name: 'Dominica',\n        mobile_begin_with: ['767'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'DK',\n        alpha3: 'DNK',\n        country_code: '45',\n        country_name: 'Denmark',\n        mobile_begin_with: ['2', '30', '31', '40', '41', '42', '50', '51', '52', '53', '60', '61', '71', '81', '91', '92', '93'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'DO',\n        alpha3: 'DOM',\n        country_code: '1',\n        country_name: 'Dominican Republic',\n        mobile_begin_with: ['809', '829', '849'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'DZ',\n        alpha3: 'DZA',\n        country_code: '213',\n        country_name: 'Algeria',\n        mobile_begin_with: ['5', '6', '7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'EC',\n        alpha3: 'ECU',\n        country_code: '593',\n        country_name: 'Ecuador',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'EG',\n        alpha3: 'EGY',\n        country_code: '20',\n        country_name: 'Egypt',\n        mobile_begin_with: ['1'],\n        phone_number_lengths: [10, 8]\n    },\n    {\n        alpha2: 'ER',\n        alpha3: 'ERI',\n        country_code: '291',\n        country_name: 'Eritrea',\n        mobile_begin_with: ['1', '7', '8'],\n        phone_number_lengths: [7]\n    },\n    // {alpha2: \"EH\", alpha3: \"ESH\", country_code: \"212\", country_name: \"Western Sahara\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'ES',\n        alpha3: 'ESP',\n        country_code: '34',\n        country_name: 'Spain',\n        mobile_begin_with: ['6', '7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'EE',\n        alpha3: 'EST',\n        country_code: '372',\n        country_name: 'Estonia',\n        mobile_begin_with: ['5', '81', '82', '83'],\n        phone_number_lengths: [7, 8]\n    },\n    {\n        alpha2: 'ET',\n        alpha3: 'ETH',\n        country_code: '251',\n        country_name: 'Ethiopia',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'FI',\n        alpha3: 'FIN',\n        country_code: '358',\n        country_name: 'Finland',\n        mobile_begin_with: ['4', '5'],\n        phone_number_lengths: [9, 10]\n    },\n    {\n        alpha2: 'FJ',\n        alpha3: 'FJI',\n        country_code: '679',\n        country_name: 'Fiji',\n        mobile_begin_with: ['2', '7', '8', '9'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'FK',\n        alpha3: 'FLK',\n        country_code: '500',\n        country_name: 'Falkland Islands (Malvinas)',\n        mobile_begin_with: ['5', '6'],\n        phone_number_lengths: [5]\n    },\n    {\n        alpha2: 'FR',\n        alpha3: 'FRA',\n        country_code: '33',\n        country_name: 'France',\n        mobile_begin_with: ['6', '7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'FO',\n        alpha3: 'FRO',\n        country_code: '298',\n        country_name: 'Faroe Islands',\n        mobile_begin_with: [],\n        phone_number_lengths: [6]\n    },\n    {\n        alpha2: 'FM',\n        alpha3: 'FSM',\n        country_code: '691',\n        country_name: 'Micronesia, Federated States Of',\n        mobile_begin_with: [],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'GA',\n        alpha3: 'GAB',\n        country_code: '241',\n        country_name: 'Gabon',\n        mobile_begin_with: ['2', '3', '4', '5', '6', '7'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'GB',\n        alpha3: 'GBR',\n        country_code: '44',\n        country_name: 'United Kingdom',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'GE',\n        alpha3: 'GEO',\n        country_code: '995',\n        country_name: 'Georgia',\n        mobile_begin_with: ['5', '7'],\n        phone_number_lengths: [9]\n    },\n    // {alpha2: \"GG\", alpha3: \"GGY\", country_code: \"44\", country_name: \"Guernsey\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'GH',\n        alpha3: 'GHA',\n        country_code: '233',\n        country_name: 'Ghana',\n        mobile_begin_with: ['2', '5'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'GI',\n        alpha3: 'GIB',\n        country_code: '350',\n        country_name: 'Gibraltar',\n        mobile_begin_with: ['5'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'GN',\n        alpha3: 'GIN',\n        country_code: '224',\n        country_name: 'Guinea',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'GP',\n        alpha3: 'GLP',\n        country_code: '590',\n        country_name: 'Guadeloupe',\n        mobile_begin_with: ['690'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'GM',\n        alpha3: 'GMB',\n        country_code: '220',\n        country_name: 'Gambia',\n        mobile_begin_with: ['7', '9'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'GW',\n        alpha3: 'GNB',\n        country_code: '245',\n        country_name: 'Guinea-Bissau',\n        mobile_begin_with: ['5', '6', '7'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'GQ',\n        alpha3: 'GNQ',\n        country_code: '240',\n        country_name: 'Equatorial Guinea',\n        mobile_begin_with: ['222', '551'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'GR',\n        alpha3: 'GRC',\n        country_code: '30',\n        country_name: 'Greece',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'GD',\n        alpha3: 'GRD',\n        country_code: '1',\n        country_name: 'Grenada',\n        mobile_begin_with: ['473'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'GL',\n        alpha3: 'GRL',\n        country_code: '299',\n        country_name: 'Greenland',\n        mobile_begin_with: ['2', '4', '5'],\n        phone_number_lengths: [6]\n    },\n    {\n        alpha2: 'GT',\n        alpha3: 'GTM',\n        country_code: '502',\n        country_name: 'Guatemala',\n        mobile_begin_with: ['3', '4', '5'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'GF',\n        alpha3: 'GUF',\n        country_code: '594',\n        country_name: 'French Guiana',\n        mobile_begin_with: ['694'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'GU',\n        alpha3: 'GUM',\n        country_code: '1',\n        country_name: 'Guam',\n        mobile_begin_with: ['671'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'GY',\n        alpha3: 'GUY',\n        country_code: '592',\n        country_name: 'Guyana',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'HK',\n        alpha3: 'HKG',\n        country_code: '852',\n        country_name: 'Hong Kong',\n        mobile_begin_with: ['4', '5', '6', '70', '71', '72', '73', '81', '82', '83', '84', '85', '86', '87', '88', '89', '9'],\n        phone_number_lengths: [8]\n    },\n    // {alpha2: \"HM\", alpha3: \"HMD\", country_code: \"\", country_name: \"Heard and McDonald Islands\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'HN',\n        alpha3: 'HND',\n        country_code: '504',\n        country_name: 'Honduras',\n        mobile_begin_with: ['3', '7', '8', '9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'HR',\n        alpha3: 'HRV',\n        country_code: '385',\n        country_name: 'Croatia',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [8, 9]\n    },\n    {\n        alpha2: 'HT',\n        alpha3: 'HTI',\n        country_code: '509',\n        country_name: 'Haiti',\n        mobile_begin_with: ['3', '4'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'HU',\n        alpha3: 'HUN',\n        country_code: '36',\n        country_name: 'Hungary',\n        mobile_begin_with: ['20', '30', '31', '50', '70'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'ID',\n        alpha3: 'IDN',\n        country_code: '62',\n        country_name: 'Indonesia',\n        mobile_begin_with: ['8'],\n        phone_number_lengths: [9, 10, 11, 12]\n    },\n    // {alpha2: \"IM\", alpha3: \"IMN\", country_code: \"44\", country_name: \"Isle of Man\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'IN',\n        alpha3: 'IND',\n        country_code: '91',\n        country_name: 'India',\n        mobile_begin_with: ['6', '7', '8', '9'],\n        phone_number_lengths: [10]\n    },\n    // {alpha2: \"IO\", alpha3: \"IOT\", country_code: \"246\", country_name: \"British Indian Ocean Territory\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'IE',\n        alpha3: 'IRL',\n        country_code: '353',\n        country_name: 'Ireland',\n        mobile_begin_with: ['82', '83', '84', '85', '86', '87', '88', '89'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'IR',\n        alpha3: 'IRN',\n        country_code: '98',\n        country_name: 'Iran, Islamic Republic Of',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'IQ',\n        alpha3: 'IRQ',\n        country_code: '964',\n        country_name: 'Iraq',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'IS',\n        alpha3: 'ISL',\n        country_code: '354',\n        country_name: 'Iceland',\n        mobile_begin_with: ['6', '7', '8'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'IL',\n        alpha3: 'ISR',\n        country_code: '972',\n        country_name: 'Israel',\n        mobile_begin_with: ['5'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'IT',\n        alpha3: 'ITA',\n        country_code: '39',\n        country_name: 'Italy',\n        mobile_begin_with: ['3'],\n        phone_number_lengths: [9, 10]\n    },\n    {\n        alpha2: 'JM',\n        alpha3: 'JAM',\n        country_code: '1',\n        country_name: 'Jamaica',\n        mobile_begin_with: ['876'],\n        phone_number_lengths: [10]\n    },\n    // {alpha2: \"JE\", alpha3: \"JEY\", country_code: \"44\", country_name: \"Jersey\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'JO',\n        alpha3: 'JOR',\n        country_code: '962',\n        country_name: 'Jordan',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'JP',\n        alpha3: 'JPN',\n        country_code: '81',\n        country_name: 'Japan',\n        mobile_begin_with: ['70', '80', '90'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'KZ',\n        alpha3: 'KAZ',\n        country_code: '7',\n        country_name: 'Kazakhstan',\n        mobile_begin_with: ['70', '74', '77'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'KE',\n        alpha3: 'KEN',\n        country_code: '254',\n        country_name: 'Kenya',\n        mobile_begin_with: ['7', '1'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'KG',\n        alpha3: 'KGZ',\n        country_code: '996',\n        country_name: 'Kyrgyzstan',\n        mobile_begin_with: ['5', '7', '8', '9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'KH',\n        alpha3: 'KHM',\n        country_code: '855',\n        country_name: 'Cambodia',\n        mobile_begin_with: ['1', '6', '7', '8', '9'],\n        phone_number_lengths: [8, 9]\n    },\n    {\n        alpha2: 'KI',\n        alpha3: 'KIR',\n        country_code: '686',\n        country_name: 'Kiribati',\n        mobile_begin_with: ['9', '30'],\n        phone_number_lengths: [5]\n    },\n    {\n        alpha2: 'KN',\n        alpha3: 'KNA',\n        country_code: '1',\n        country_name: 'Saint Kitts And Nevis',\n        mobile_begin_with: ['869'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'KR',\n        alpha3: 'KOR',\n        country_code: '82',\n        country_name: 'Korea, Republic of',\n        mobile_begin_with: ['1'],\n        phone_number_lengths: [9, 10]\n    },\n    {\n        alpha2: 'KW',\n        alpha3: 'KWT',\n        country_code: '965',\n        country_name: 'Kuwait',\n        mobile_begin_with: ['5', '6', '9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'LA',\n        alpha3: 'LAO',\n        country_code: '856',\n        country_name: \"Lao People's Democratic Republic\",\n        mobile_begin_with: ['20'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'LB',\n        alpha3: 'LBN',\n        country_code: '961',\n        country_name: 'Lebanon',\n        mobile_begin_with: ['3', '7', '8'],\n        phone_number_lengths: [7, 8]\n    },\n    {\n        alpha2: 'LR',\n        alpha3: 'LBR',\n        country_code: '231',\n        country_name: 'Liberia',\n        mobile_begin_with: ['4', '5', '6', '7'],\n        phone_number_lengths: [7, 8]\n    },\n    {\n        alpha2: 'LY',\n        alpha3: 'LBY',\n        country_code: '218',\n        country_name: 'Libyan Arab Jamahiriya',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'LC',\n        alpha3: 'LCA',\n        country_code: '1',\n        country_name: 'Saint Lucia',\n        mobile_begin_with: ['758'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'LI',\n        alpha3: 'LIE',\n        country_code: '423',\n        country_name: 'Liechtenstein',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'LK',\n        alpha3: 'LKA',\n        country_code: '94',\n        country_name: 'Sri Lanka',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'LS',\n        alpha3: 'LSO',\n        country_code: '266',\n        country_name: 'Lesotho',\n        mobile_begin_with: ['5', '6'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'LT',\n        alpha3: 'LTU',\n        country_code: '370',\n        country_name: 'Lithuania',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'LU',\n        alpha3: 'LUX',\n        country_code: '352',\n        country_name: 'Luxembourg',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'LV',\n        alpha3: 'LVA',\n        country_code: '371',\n        country_name: 'Latvia',\n        mobile_begin_with: ['2'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'MO',\n        alpha3: 'MAC',\n        country_code: '853',\n        country_name: 'Macao',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [8]\n    },\n    // {alpha2: \"MF\", alpha3: \"MAF\", country_code: \"590\", country_name: \"Saint Martin\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'MA',\n        alpha3: 'MAR',\n        country_code: '212',\n        country_name: 'Morocco',\n        mobile_begin_with: ['6', '7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'MC',\n        alpha3: 'MCO',\n        country_code: '377',\n        country_name: 'Monaco',\n        mobile_begin_with: ['4', '6'],\n        phone_number_lengths: [8, 9]\n    },\n    {\n        alpha2: 'MD',\n        alpha3: 'MDA',\n        country_code: '373',\n        country_name: 'Moldova, Republic of',\n        mobile_begin_with: ['6', '7'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'MG',\n        alpha3: 'MDG',\n        country_code: '261',\n        country_name: 'Madagascar',\n        mobile_begin_with: ['3'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'MV',\n        alpha3: 'MDV',\n        country_code: '960',\n        country_name: 'Maldives',\n        mobile_begin_with: ['7', '9'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'MX',\n        alpha3: 'MEX',\n        country_code: '52',\n        country_name: 'Mexico',\n        mobile_begin_with: [''],\n        phone_number_lengths: [10, 11]\n    },\n    {\n        alpha2: 'MH',\n        alpha3: 'MHL',\n        country_code: '692',\n        country_name: 'Marshall Islands',\n        mobile_begin_with: [],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'MK',\n        alpha3: 'MKD',\n        country_code: '389',\n        country_name: 'Macedonia, the Former Yugoslav Republic Of',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'ML',\n        alpha3: 'MLI',\n        country_code: '223',\n        country_name: 'Mali',\n        mobile_begin_with: ['6', '7'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'MT',\n        alpha3: 'MLT',\n        country_code: '356',\n        country_name: 'Malta',\n        mobile_begin_with: ['7', '9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'MM',\n        alpha3: 'MMR',\n        country_code: '95',\n        country_name: 'Myanmar',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [8, 9, 10]\n    },\n    {\n        alpha2: 'ME',\n        alpha3: 'MNE',\n        country_code: '382',\n        country_name: 'Montenegro',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'MN',\n        alpha3: 'MNG',\n        country_code: '976',\n        country_name: 'Mongolia',\n        mobile_begin_with: ['5', '8', '9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'MP',\n        alpha3: 'MNP',\n        country_code: '1',\n        country_name: 'Northern Mariana Islands',\n        mobile_begin_with: ['670'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'MZ',\n        alpha3: 'MOZ',\n        country_code: '258',\n        country_name: 'Mozambique',\n        mobile_begin_with: ['8'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'MR',\n        alpha3: 'MRT',\n        country_code: '222',\n        country_name: 'Mauritania',\n        mobile_begin_with: [],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'MS',\n        alpha3: 'MSR',\n        country_code: '1',\n        country_name: 'Montserrat',\n        mobile_begin_with: ['664'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'MQ',\n        alpha3: 'MTQ',\n        country_code: '596',\n        country_name: 'Martinique',\n        mobile_begin_with: ['696'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'MU',\n        alpha3: 'MUS',\n        country_code: '230',\n        country_name: 'Mauritius',\n        mobile_begin_with: ['5'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'MW',\n        alpha3: 'MWI',\n        country_code: '265',\n        country_name: 'Malawi',\n        mobile_begin_with: ['77', '88', '99'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'MY',\n        alpha3: 'MYS',\n        country_code: '60',\n        country_name: 'Malaysia',\n        mobile_begin_with: ['1', '6'],\n        phone_number_lengths: [9, 10, 8]\n    },\n    {\n        alpha2: 'YT',\n        alpha3: 'MYT',\n        country_code: '262',\n        country_name: 'Mayotte',\n        mobile_begin_with: ['639'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'NA',\n        alpha3: 'NAM',\n        country_code: '264',\n        country_name: 'Namibia',\n        mobile_begin_with: ['60', '81', '82', '85'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'NC',\n        alpha3: 'NCL',\n        country_code: '687',\n        country_name: 'New Caledonia',\n        mobile_begin_with: ['7', '8', '9'],\n        phone_number_lengths: [6]\n    },\n    {\n        alpha2: 'NE',\n        alpha3: 'NER',\n        country_code: '227',\n        country_name: 'Niger',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'NF',\n        alpha3: 'NFK',\n        country_code: '672',\n        country_name: 'Norfolk Island',\n        mobile_begin_with: ['5', '8'],\n        phone_number_lengths: [5]\n    },\n    {\n        alpha2: 'NG',\n        alpha3: 'NGA',\n        country_code: '234',\n        country_name: 'Nigeria',\n        mobile_begin_with: ['70', '80', '81', '90', '91'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'NI',\n        alpha3: 'NIC',\n        country_code: '505',\n        country_name: 'Nicaragua',\n        mobile_begin_with: ['8'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'NU',\n        alpha3: 'NIU',\n        country_code: '683',\n        country_name: 'Niue',\n        mobile_begin_with: [],\n        phone_number_lengths: [4]\n    },\n    {\n        alpha2: 'NL',\n        alpha3: 'NLD',\n        country_code: '31',\n        country_name: 'Netherlands',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'NO',\n        alpha3: 'NOR',\n        country_code: '47',\n        country_name: 'Norway',\n        mobile_begin_with: ['4', '9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'NP',\n        alpha3: 'NPL',\n        country_code: '977',\n        country_name: 'Nepal',\n        mobile_begin_with: ['97', '98'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'NR',\n        alpha3: 'NRU',\n        country_code: '674',\n        country_name: 'Nauru',\n        mobile_begin_with: ['555'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'NZ',\n        alpha3: 'NZL',\n        country_code: '64',\n        country_name: 'New Zealand',\n        mobile_begin_with: ['2'],\n        phone_number_lengths: [8, 9, 10]\n    },\n    {\n        alpha2: 'OM',\n        alpha3: 'OMN',\n        country_code: '968',\n        country_name: 'Oman',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'PK',\n        alpha3: 'PAK',\n        country_code: '92',\n        country_name: 'Pakistan',\n        mobile_begin_with: ['3'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'PA',\n        alpha3: 'PAN',\n        country_code: '507',\n        country_name: 'Panama',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [8]\n    },\n    // {alpha2: \"PN\", alpha3: \"PCN\", country_code: \"\", country_name: \"Pitcairn\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'PE',\n        alpha3: 'PER',\n        country_code: '51',\n        country_name: 'Peru',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'PH',\n        alpha3: 'PHL',\n        country_code: '63',\n        country_name: 'Philippines',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'PW',\n        alpha3: 'PLW',\n        country_code: '680',\n        country_name: 'Palau',\n        mobile_begin_with: [],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'PG',\n        alpha3: 'PNG',\n        country_code: '675',\n        country_name: 'Papua New Guinea',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'PL',\n        alpha3: 'POL',\n        country_code: '48',\n        country_name: 'Poland',\n        mobile_begin_with: ['4', '5', '6', '7', '8'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'PR',\n        alpha3: 'PRI',\n        country_code: '1',\n        country_name: 'Puerto Rico',\n        mobile_begin_with: ['787', '939'],\n        phone_number_lengths: [10]\n    },\n    // {alpha2: \"KP\", alpha3: \"PRK\", country_code: \"850\", country_name: \"Korea, Democratic People's Republic Of\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'PT',\n        alpha3: 'PRT',\n        country_code: '351',\n        country_name: 'Portugal',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'PY',\n        alpha3: 'PRY',\n        country_code: '595',\n        country_name: 'Paraguay',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'PS',\n        alpha3: 'PSE',\n        country_code: '970',\n        country_name: 'Palestinian Territory, Occupied',\n        mobile_begin_with: ['5'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'PF',\n        alpha3: 'PYF',\n        country_code: '689',\n        country_name: 'French Polynesia',\n        mobile_begin_with: ['8'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'QA',\n        alpha3: 'QAT',\n        country_code: '974',\n        country_name: 'Qatar',\n        mobile_begin_with: ['3', '5', '6', '7'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'RE',\n        alpha3: 'REU',\n        country_code: '262',\n        country_name: 'Réunion',\n        mobile_begin_with: ['692', '693'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'RO',\n        alpha3: 'ROU',\n        country_code: '40',\n        country_name: 'Romania',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'RU',\n        alpha3: 'RUS',\n        country_code: '7',\n        country_name: 'Russian Federation',\n        mobile_begin_with: ['9', '495', '498', '499'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'RW',\n        alpha3: 'RWA',\n        country_code: '250',\n        country_name: 'Rwanda',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'SA',\n        alpha3: 'SAU',\n        country_code: '966',\n        country_name: 'Saudi Arabia',\n        mobile_begin_with: ['5'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'SD',\n        alpha3: 'SDN',\n        country_code: '249',\n        country_name: 'Sudan',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'SS',\n        alpha3: 'SSD',\n        country_code: '211',\n        country_name: 'South Sudan',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'SN',\n        alpha3: 'SEN',\n        country_code: '221',\n        country_name: 'Senegal',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'SG',\n        alpha3: 'SGP',\n        country_code: '65',\n        country_name: 'Singapore',\n        mobile_begin_with: ['8', '9'],\n        phone_number_lengths: [8]\n    },\n    // {alpha2: \"GS\", alpha3: \"SGS\", country_code: \"500\", country_name: \"South Georgia and the South Sandwich Islands\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'SH',\n        alpha3: 'SHN',\n        country_code: '290',\n        country_name: 'Saint Helena',\n        mobile_begin_with: [],\n        phone_number_lengths: [4]\n    },\n    {\n        alpha2: 'SJ',\n        alpha3: 'SJM',\n        country_code: '47',\n        country_name: 'Svalbard And Jan Mayen',\n        mobile_begin_with: ['79'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'SB',\n        alpha3: 'SLB',\n        country_code: '677',\n        country_name: 'Solomon Islands',\n        mobile_begin_with: ['7', '8'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'SL',\n        alpha3: 'SLE',\n        country_code: '232',\n        country_name: 'Sierra Leone',\n        mobile_begin_with: ['21', '25', '30', '33', '34', '40', '44', '50', '55', '76', '77', '78', '79', '88'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'SV',\n        alpha3: 'SLV',\n        country_code: '503',\n        country_name: 'El Salvador',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'SM',\n        alpha3: 'SMR',\n        country_code: '378',\n        country_name: 'San Marino',\n        mobile_begin_with: ['3', '6'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'SO',\n        alpha3: 'SOM',\n        country_code: '252',\n        country_name: 'Somalia',\n        mobile_begin_with: ['61', '62', '63', '65', '66', '68', '69', '71', '90'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'SX',\n        alpha3: 'SXM',\n        country_code: '1',\n        country_name: 'Sint Maarten',\n        mobile_begin_with: ['721'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'PM',\n        alpha3: 'SPM',\n        country_code: '508',\n        country_name: 'Saint Pierre And Miquelon',\n        mobile_begin_with: ['55', '41'],\n        phone_number_lengths: [6]\n    },\n    {\n        alpha2: 'RS',\n        alpha3: 'SRB',\n        country_code: '381',\n        country_name: 'Serbia',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [8, 9]\n    },\n    {\n        alpha2: 'ST',\n        alpha3: 'STP',\n        country_code: '239',\n        country_name: 'Sao Tome and Principe',\n        mobile_begin_with: ['98', '99'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'SR',\n        alpha3: 'SUR',\n        country_code: '597',\n        country_name: 'Suriname',\n        mobile_begin_with: ['6', '7', '8'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'SK',\n        alpha3: 'SVK',\n        country_code: '421',\n        country_name: 'Slovakia',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'SI',\n        alpha3: 'SVN',\n        country_code: '386',\n        country_name: 'Slovenia',\n        mobile_begin_with: ['3', '4', '5', '6', '7'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'SE',\n        alpha3: 'SWE',\n        country_code: '46',\n        country_name: 'Sweden',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [9]\n    },\n    // {alpha2: \"SZ\", alpha3: \"SWZ\", country_code: \"268\", country_name: \"Swaziland\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'SC',\n        alpha3: 'SYC',\n        country_code: '248',\n        country_name: 'Seychelles',\n        mobile_begin_with: ['2'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'SY',\n        alpha3: 'SYR',\n        country_code: '963',\n        country_name: 'Syrian Arab Republic',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    // http://www.howtocallabroad.com/turks-caicos/\n    {\n        alpha2: 'TC',\n        alpha3: 'TCA',\n        country_code: '1',\n        country_name: 'Turks and Caicos Islands',\n        mobile_begin_with: ['6492', '6493', '6494'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'TD',\n        alpha3: 'TCD',\n        country_code: '235',\n        country_name: 'Chad',\n        mobile_begin_with: ['6', '7', '9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'TG',\n        alpha3: 'TGO',\n        country_code: '228',\n        country_name: 'Togo',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'TH',\n        alpha3: 'THA',\n        country_code: '66',\n        country_name: 'Thailand',\n        mobile_begin_with: ['6', '8', '9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'TJ',\n        alpha3: 'TJK',\n        country_code: '992',\n        country_name: 'Tajikistan',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'TK',\n        alpha3: 'TKL',\n        country_code: '690',\n        country_name: 'Tokelau',\n        mobile_begin_with: [],\n        phone_number_lengths: [4]\n    },\n    {\n        alpha2: 'TM',\n        alpha3: 'TKM',\n        country_code: '993',\n        country_name: 'Turkmenistan',\n        mobile_begin_with: ['6'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'TL',\n        alpha3: 'TLS',\n        country_code: '670',\n        country_name: 'Timor-Leste',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'TO',\n        alpha3: 'TON',\n        country_code: '676',\n        country_name: 'Tonga',\n        mobile_begin_with: [],\n        phone_number_lengths: [5]\n    },\n    {\n        alpha2: 'TT',\n        alpha3: 'TTO',\n        country_code: '1',\n        country_name: 'Trinidad and Tobago',\n        mobile_begin_with: ['868'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'TN',\n        alpha3: 'TUN',\n        country_code: '216',\n        country_name: 'Tunisia',\n        mobile_begin_with: ['2', '4', '5', '9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'TR',\n        alpha3: 'TUR',\n        country_code: '90',\n        country_name: 'Turkey',\n        mobile_begin_with: ['5'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'TV',\n        alpha3: 'TUV',\n        country_code: '688',\n        country_name: 'Tuvalu',\n        mobile_begin_with: [],\n        phone_number_lengths: [5]\n    },\n    {\n        alpha2: 'TW',\n        alpha3: 'TWN',\n        country_code: '886',\n        country_name: 'Taiwan',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'TZ',\n        alpha3: 'TZA',\n        country_code: '255',\n        country_name: 'Tanzania, United Republic of',\n        mobile_begin_with: ['7', '6'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'UG',\n        alpha3: 'UGA',\n        country_code: '256',\n        country_name: 'Uganda',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'UA',\n        alpha3: 'UKR',\n        country_code: '380',\n        country_name: 'Ukraine',\n        mobile_begin_with: ['39', '50', '63', '66', '67', '68', '73', '9'],\n        phone_number_lengths: [9]\n    },\n    // {alpha2: \"UM\", alpha3: \"UMI\", country_code: \"\", country_name: \"United States Minor Outlying Islands\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'UY',\n        alpha3: 'URY',\n        country_code: '598',\n        country_name: 'Uruguay',\n        mobile_begin_with: ['9'],\n        phone_number_lengths: [8]\n    },\n    {\n        alpha2: 'UZ',\n        alpha3: 'UZB',\n        country_code: '998',\n        country_name: 'Uzbekistan',\n        mobile_begin_with: ['9', '88', '33'],\n        phone_number_lengths: [9]\n    },\n    // {alpha2: \"VA\", alpha3: \"VAT\", country_code: \"39\", country_name: \"Holy See (Vatican City State)\", mobile_begin_with: [], phone_number_lengths: []},\n    {\n        alpha2: 'VC',\n        alpha3: 'VCT',\n        country_code: '1',\n        country_name: 'Saint Vincent And The Grenedines',\n        mobile_begin_with: ['784'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'VE',\n        alpha3: 'VEN',\n        country_code: '58',\n        country_name: 'Venezuela, Bolivarian Republic of',\n        mobile_begin_with: ['4'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'VG',\n        alpha3: 'VGB',\n        country_code: '1',\n        country_name: 'Virgin Islands, British',\n        mobile_begin_with: ['284'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'VI',\n        alpha3: 'VIR',\n        country_code: '1',\n        country_name: 'Virgin Islands, U.S.',\n        mobile_begin_with: ['340'],\n        phone_number_lengths: [10]\n    },\n    {\n        alpha2: 'VN',\n        alpha3: 'VNM',\n        country_code: '84',\n        country_name: 'Viet Nam',\n        mobile_begin_with: ['8', '9', '3', '7', '5'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'VU',\n        alpha3: 'VUT',\n        country_code: '678',\n        country_name: 'Vanuatu',\n        mobile_begin_with: ['5', '7'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'WF',\n        alpha3: 'WLF',\n        country_code: '681',\n        country_name: 'Wallis and Futuna',\n        mobile_begin_with: [],\n        phone_number_lengths: [6]\n    },\n    {\n        alpha2: 'WS',\n        alpha3: 'WSM',\n        country_code: '685',\n        country_name: 'Samoa',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [7]\n    },\n    {\n        alpha2: 'YE',\n        alpha3: 'YEM',\n        country_code: '967',\n        country_name: 'Yemen',\n        mobile_begin_with: ['7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'ZA',\n        alpha3: 'ZAF',\n        country_code: '27',\n        country_name: 'South Africa',\n        mobile_begin_with: ['1', '2', '3', '4', '5', '6', '7', '8'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'ZM',\n        alpha3: 'ZMB',\n        country_code: '260',\n        country_name: 'Zambia',\n        mobile_begin_with: ['9', '7'],\n        phone_number_lengths: [9]\n    },\n    {\n        alpha2: 'ZW',\n        alpha3: 'ZWE',\n        country_code: '263',\n        country_name: 'Zimbabwe',\n        mobile_begin_with: ['71', '73', '77'],\n        phone_number_lengths: [9]\n    }\n];\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validatePhoneISO3166 = exports.findCountryPhoneDataByPhoneNumber = exports.findPossibleCountryPhoneData = exports.findExactCountryPhoneData = exports.findCountryPhoneDataByCountry = void 0;\nconst country_phone_data_1 = __importDefault(require(\"../data/country_phone_data\"));\n/**\n * @param {string=} country - country code alpha 2 or 3\n * @returns {{country_code: string, alpha2: string, country_name: string, alpha3: string, mobile_begin_with, phone_number_lengths: [number]}|{country_code: string, alpha2: string, country_name: string, alpha3: string, mobile_begin_with: [string, string, string, string], phone_number_lengths: [number]}|{country_code: string, alpha2: string, country_name: string, alpha3: string, mobile_begin_with: [string], phone_number_lengths: [number]}|{country_code: string, alpha2: string, country_name: string, alpha3: string, mobile_begin_with: [string], phone_number_lengths: [number]}|{country_code: string, alpha2: string, country_name: string, alpha3: string, mobile_begin_with: [string, string], phone_number_lengths: [number]}|null}\n */\nfunction findCountryPhoneDataByCountry(country) {\n    // if no country provided, assume it's USA\n    if (!country) {\n        return country_phone_data_1.default.find(countryPhoneDatum => countryPhoneDatum.alpha3 === 'USA') || null;\n    }\n    if (country.length === 2) {\n        return country_phone_data_1.default.find(countryPhoneDatum => country.toUpperCase() === countryPhoneDatum.alpha2) || null;\n    }\n    if (country.length === 3) {\n        return country_phone_data_1.default.find(countryPhoneDatum => country.toUpperCase() === countryPhoneDatum.alpha3) || null;\n    }\n    return country_phone_data_1.default.find(countryPhoneDatum => country.toUpperCase() === countryPhoneDatum.country_name.toUpperCase()) || null;\n}\nexports.findCountryPhoneDataByCountry = findCountryPhoneDataByCountry;\nfunction findExactCountryPhoneData(phoneNumber, validateMobilePrefix, countryPhoneDatum) {\n    // check if the phone number length match any one of the length config\n    const phoneNumberLengthMatched = countryPhoneDatum.phone_number_lengths.some(length => {\n        // as the phone number must include the country code,\n        // but countryPhoneDatum.phone_number_lengths is the length without country code\n        // therefore need to add back countryPhoneDatum.country_code.length to length\n        return (countryPhoneDatum.country_code.length + length === phoneNumber.length);\n    });\n    if (!phoneNumberLengthMatched) {\n        return null;\n    }\n    // if no need to validate mobile prefix or the country data does not have mobile begin with\n    // pick the current one as the answer directly\n    if (!countryPhoneDatum.mobile_begin_with.length || !validateMobilePrefix) {\n        return countryPhoneDatum;\n    }\n    // if the mobile begin with is correct, pick as the correct answer\n    if (countryPhoneDatum.mobile_begin_with.some(beginWith => {\n        return phoneNumber.match(new RegExp('^' + countryPhoneDatum.country_code + beginWith));\n    })) {\n        return countryPhoneDatum;\n    }\n    return null;\n}\nexports.findExactCountryPhoneData = findExactCountryPhoneData;\nfunction findPossibleCountryPhoneData(phoneNumber, validateMobilePrefix, countryPhoneDatum) {\n    // check if the phone number length match any one of the length config\n    const phoneNumberLengthMatched = countryPhoneDatum.phone_number_lengths.some(length => {\n        // the phone number must include the country code\n        // countryPhoneDatum.phone_number_lengths is the length without country code\n        // + 1 is assuming there is an unwanted trunk code prepended to the phone number\n        return (countryPhoneDatum.country_code.length + length + 1 === phoneNumber.length);\n    });\n    if (!phoneNumberLengthMatched) {\n        return null;\n    }\n    // if no need to validate mobile prefix or the country data does not have mobile begin with\n    // pick the current one as the answer directly\n    if (!countryPhoneDatum.mobile_begin_with.length || !validateMobilePrefix) {\n        return countryPhoneDatum;\n    }\n    // if the mobile begin with is correct, pick as the correct answer\n    // match another \\d for the unwanted trunk code prepended to the phone number\n    if (countryPhoneDatum.mobile_begin_with.some(beginWith => {\n        return phoneNumber.match(new RegExp('^' + countryPhoneDatum.country_code + '\\\\d?' + beginWith));\n    })) {\n        return countryPhoneDatum;\n    }\n}\nexports.findPossibleCountryPhoneData = findPossibleCountryPhoneData;\n/**\n * get country phone data by phone number\n * the phone number must include country code as the complete phone number includes the plus sign\n * @param phoneNumber\n * @param validateMobilePrefix\n * @returns {{exactCountryPhoneData: (*), possibleCountryPhoneData: (*)}}\n */\nfunction findCountryPhoneDataByPhoneNumber(phoneNumber, validateMobilePrefix) {\n    let exactCountryPhoneData;\n    let possibleCountryPhoneData;\n    for (const countryPhoneDatum of country_phone_data_1.default) {\n        // if the country code is wrong, skip directly\n        if (!phoneNumber.match(new RegExp('^' + countryPhoneDatum.country_code))) {\n            continue;\n        }\n        // process only if exact match not found yet\n        if (!exactCountryPhoneData) {\n            exactCountryPhoneData = findExactCountryPhoneData(phoneNumber, validateMobilePrefix, countryPhoneDatum);\n        }\n        if (!possibleCountryPhoneData) {\n            possibleCountryPhoneData = findPossibleCountryPhoneData(phoneNumber, validateMobilePrefix, countryPhoneDatum);\n        }\n    }\n    return {\n        exactCountryPhoneData,\n        possibleCountryPhoneData\n    };\n}\nexports.findCountryPhoneDataByPhoneNumber = findCountryPhoneDataByPhoneNumber;\n/**\n *\n * @param {string} phone - phone number without plus sign, with or without country calling code\n * @param {Object} countryPhoneDatum - iso 3166 data\n * @param {String} countryPhoneDatum.country_code - country calling codes\n * @param {Array} countryPhoneDatum.phone_number_lengths - all available phone number lengths for this country\n * @param {Array} countryPhoneDatum.mobile_begin_with - mobile begin with number\n * @param {boolean} validateMobilePrefix - true if we skip mobile begin with checking\n * @param {boolean} plusSign - true if the input contains a plus sign\n * @returns {*|boolean}\n */\nfunction validatePhoneISO3166(phone, countryPhoneDatum, validateMobilePrefix, plusSign) {\n    if (!countryPhoneDatum.phone_number_lengths) {\n        return false;\n    }\n    // remove country calling code from the phone number\n    const phoneWithoutCountry = phone.replace(new RegExp('^' + countryPhoneDatum.country_code), '');\n    // if the phone number have +, countryPhoneDatum detected,\n    // but the phone number does not have country calling code\n    // then should consider the phone number as invalid\n    if (plusSign && countryPhoneDatum && phoneWithoutCountry.length === phone.length) {\n        return false;\n    }\n    const phone_number_lengths = countryPhoneDatum.phone_number_lengths;\n    const mobile_begin_with = countryPhoneDatum.mobile_begin_with;\n    const isLengthValid = phone_number_lengths.some(length => phoneWithoutCountry.length === length);\n    // some country doesn't have mobile_begin_with\n    const isBeginWithValid = mobile_begin_with.length ?\n        mobile_begin_with.some(beginWith => phoneWithoutCountry.match(new RegExp('^' + beginWith))) :\n        true;\n    return isLengthValid && (!validateMobilePrefix || isBeginWithValid);\n}\nexports.validatePhoneISO3166 = validatePhoneISO3166;\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.countryPhoneData = exports.phone = void 0;\nconst country_phone_data_1 = __importDefault(require(\"./data/country_phone_data\"));\nexports.countryPhoneData = country_phone_data_1.default;\nconst utility_1 = require(\"./lib/utility\");\n/**\n * @typedef {Object} Option\n * @property {string=} country - country code in ISO3166 alpha 2 or 3\n * @property {boolean=} validateMobilePrefix - true to validate phone number prefix\n * @property {boolean=} strictDetection - true to disable remove truck code and detection logic\n *\n * @param {string} phoneNumber - phone number\n * @param {Option} option\n * @returns {{phoneNumber: string|null, countryIso2: string|null, countryIso3: string|null}}\n */\nfunction phone(phoneNumber, { country = '', validateMobilePrefix = true, strictDetection = false } = {}) {\n    const invalidResult = {\n        isValid: false,\n        phoneNumber: null,\n        countryIso2: null,\n        countryIso3: null,\n        countryCode: null\n    };\n    let processedPhoneNumber = (typeof phoneNumber !== 'string') ? '' : phoneNumber.trim();\n    const processedCountry = (typeof country !== 'string') ? '' : country.trim();\n    const hasPlusSign = Boolean(processedPhoneNumber.match(/^\\+/));\n    // remove any non-digit character, included the +\n    processedPhoneNumber = processedPhoneNumber.replace(/\\D/g, '');\n    let foundCountryPhoneData = (0, utility_1.findCountryPhoneDataByCountry)(processedCountry);\n    if (!foundCountryPhoneData) {\n        return invalidResult;\n    }\n    let defaultCountry = false;\n    // if country provided, only reformat the phone number\n    if (processedCountry) {\n        // remove leading 0s for all countries except 'CIV', 'COG'\n        if (!['CIV', 'COG'].includes(foundCountryPhoneData.alpha3)) {\n            processedPhoneNumber = processedPhoneNumber.replace(/^0+/, '');\n        }\n        // if input 89234567890, RUS, remove the 8\n        if (foundCountryPhoneData.alpha3 === 'RUS' && processedPhoneNumber.length === 11 && processedPhoneNumber.match(/^89/) !== null) {\n            processedPhoneNumber = processedPhoneNumber.replace(/^8+/, '');\n        }\n        // if there's no plus sign and the phone number length is one of the valid length under country phone data\n        // then assume there's no country code, hence add back the country code\n        if (!hasPlusSign && foundCountryPhoneData.phone_number_lengths.includes(processedPhoneNumber.length)) {\n            processedPhoneNumber = `${foundCountryPhoneData.country_code}${processedPhoneNumber}`;\n        }\n    }\n    else if (hasPlusSign) {\n        // if there is a plus sign but no country provided\n        // try to find the country phone data by the phone number\n        const { exactCountryPhoneData, possibleCountryPhoneData } = (0, utility_1.findCountryPhoneDataByPhoneNumber)(processedPhoneNumber, validateMobilePrefix);\n        if (exactCountryPhoneData) {\n            foundCountryPhoneData = exactCountryPhoneData;\n        }\n        else if (possibleCountryPhoneData && !strictDetection) {\n            // for some countries, the phone number usually includes one trunk prefix for local use\n            // The UK mobile phone number ‘07911 123456’ in international format is ‘+44 7911 123456’, so without the first zero.\n            // 8 (AAA) BBB-BB-BB, 0AA-BBBBBBB\n            // the numbers should be omitted in international calls\n            foundCountryPhoneData = possibleCountryPhoneData;\n            processedPhoneNumber = foundCountryPhoneData.country_code + processedPhoneNumber.replace(new RegExp(`^${foundCountryPhoneData.country_code}\\\\d`), '');\n        }\n        else {\n            foundCountryPhoneData = null;\n        }\n    }\n    else if (foundCountryPhoneData.phone_number_lengths.indexOf(processedPhoneNumber.length) !== -1) {\n        // B: no country, no plus sign --> treat it as USA\n        // 1. check length if == 11, or 10, if 10, add +1, then go go D\n        // no plus sign, no country is given. then it must be USA\n        // iso3166 = iso3166_data[0]; already assign by the default value\n        processedPhoneNumber = `1${processedPhoneNumber}`;\n        defaultCountry = true;\n    }\n    if (!foundCountryPhoneData) {\n        return invalidResult;\n    }\n    let validateResult = (0, utility_1.validatePhoneISO3166)(processedPhoneNumber, foundCountryPhoneData, validateMobilePrefix, hasPlusSign);\n    if (validateResult) {\n        return {\n            isValid: true,\n            phoneNumber: `+${processedPhoneNumber}`,\n            countryIso2: foundCountryPhoneData.alpha2,\n            countryIso3: foundCountryPhoneData.alpha3,\n            countryCode: `+${foundCountryPhoneData.country_code}`\n        };\n    }\n    if (defaultCountry) {\n        // also try to validate against CAN for default country, as CAN is also start with +1\n        foundCountryPhoneData = (0, utility_1.findCountryPhoneDataByCountry)('CAN');\n        validateResult = (0, utility_1.validatePhoneISO3166)(processedPhoneNumber, foundCountryPhoneData, validateMobilePrefix, hasPlusSign);\n        if (validateResult) {\n            return {\n                isValid: true,\n                phoneNumber: `+${processedPhoneNumber}`,\n                countryIso2: foundCountryPhoneData.alpha2,\n                countryIso3: foundCountryPhoneData.alpha3,\n                countryCode: `+${foundCountryPhoneData.country_code}`\n            };\n        }\n    }\n    return invalidResult;\n}\nexports.default = phone;\nexports.phone = phone;\n;\n", "import { Tool } from \"@raycast/api\";\nimport { saveChat } from \"../services/saveChat\";\nimport { Cache } from \"@raycast/api\";\n\ntype Input = {\n  /**\n   * The name of the person\n   */\n  name: string;\n  /**\n   * The phone number\n   */\n  phone: string;\n  /**\n   * Whether the chat should be pinned\n   */\n  pinned?: boolean;\n};\n\n/**\n * Add a new WhatsApp chat\n */\nexport default async function (input: Input) {\n  const cache = new Cache();\n  const chats = cache.get(\"whatsapp-chats\") || \"[]\";\n  await saveChat({\n    chat: {\n      name: input.name,\n      phone: input.phone,\n      pinned: input.pinned || false,\n    },\n    chats: JSON.parse(chats),\n    setChats: (chats) => cache.set(\"whatsapp-chats\", JSON.stringify(chats)),\n  });\n}\n\nexport const confirmation: Tool.Confirmation<Input> = async (input) => {\n  return {\n    message: `Do you want to add \"${input.name}\" to your chats?`,\n    info: [\n      {\n        name: \"Name\",\n        value: input.name,\n      },\n      {\n        name: \"<PERSON>\",\n        value: input.phone,\n      },\n      {\n        name: \"Pinned\",\n        value: input.pinned ? \"Yes\" : \"No\",\n      },\n    ],\n  };\n};\n", "export interface Chat {\n  id: string;\n  name: string;\n  pinned: boolean;\n  lastOpened?: number;\n}\n\nexport interface PhoneChat extends Chat {\n  phone: string;\n}\n\nexport interface GroupChat extends Chat {\n  groupCode: string;\n}\n\nexport type WhatsAppChat = PhoneChat | GroupChat;\n\nexport function isGroupChat(chat: WhatsAppChat): chat is GroupChat {\n  return (chat as GroupChat).groupCode !== undefined;\n}\n\nexport function isPhoneChat(chat: WhatsAppChat): chat is PhoneChat {\n  return (chat as PhoneChat).phone !== undefined;\n}\n", "import { isPhoneChat, WhatsAppChat, PhoneChat } from \"../utils/types\";\nimport { phone as parsePhone } from \"phone\";\nimport { nanoid as randomId } from \"nanoid\";\n\ninterface SaveChatProps {\n  chat: Omit<PhoneChat, \"id\"> & { id?: string };\n  chats: WhatsAppChat[];\n  setChats: (chats: WhatsAppChat[]) => void;\n}\n\nexport async function saveChat({ chat, chats, setChats }: SaveChatProps) {\n  const phoneInformation = parsePhone(chat.phone);\n\n  const isCreation = !chat.id;\n\n  if (!phoneInformation.isValid) {\n    throw new Error(\"Phone number is invalid\");\n  }\n\n  const chatToSave: WhatsAppChat = {\n    id: chat.id || randomId(),\n    name: chat.name,\n    pinned: !!chat.pinned,\n    phone: phoneInformation.phoneNumber,\n  } as PhoneChat;\n\n  const doesPhoneNumberAlreadyExist = chats\n    .filter(isPhoneChat)\n    .some((chat) => chat.phone === phoneInformation.phoneNumber);\n\n  if (isCreation && doesPhoneNumberAlreadyExist) {\n    throw new Error(\"Chat already exists\");\n  }\n\n  if (isCreation) {\n    setChats([...chats, chatToSave]);\n  } else {\n    const newChats = chats.map((chat) => {\n      if (chat.id === chatToSave.id) {\n        return chatToSave;\n      }\n      return chat;\n    });\n    setChats(newChats);\n  }\n}\n", "import { randomFillSync } from 'crypto'\nimport { urlAlphabet } from './url-alphabet/index.js'\nexport { urlAlphabet }\nconst POOL_SIZE_MULTIPLIER = 128\nlet pool, poolOffset\nlet fillPool = bytes => {\n  if (!pool || pool.length < bytes) {\n    pool = Buffer.allocUnsafe(bytes * POOL_SIZE_MULTIPLIER)\n    randomFillSync(pool)\n    poolOffset = 0\n  } else if (poolOffset + bytes > pool.length) {\n    randomFillSync(pool)\n    poolOffset = 0\n  }\n  poolOffset += bytes\n}\nexport let random = bytes => {\n  fillPool((bytes -= 0))\n  return pool.subarray(poolOffset - bytes, poolOffset)\n}\nexport let customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << (31 - Math.clz32((alphabet.length - 1) | 1))) - 1\n  let step = Math.ceil((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let i = step\n      while (i--) {\n        id += alphabet[bytes[i] & mask] || ''\n        if (id.length === size) return id\n      }\n    }\n  }\n}\nexport let customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size, random)\nexport let nanoid = (size = 21) => {\n  fillPool((size -= 0))\n  let id = ''\n  for (let i = poolOffset - size; i < poolOffset; i++) {\n    id += urlAlphabet[pool[i] & 63]\n  }\n  return id\n}\n", "export const urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n"], "mappings": "onBAAA,IAAAA,EAAAC,EAAAC,GAAA,cACA,OAAO,eAAeA,EAAS,aAAc,CAAE,MAAO,EAAK,CAAC,EAC5DA,EAAQ,QAAU,CACd,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,gBACd,kBAAmB,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACpG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAClG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAClG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAClG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAClG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAClG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAClG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAChH,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAClG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAClG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACzG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAClG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAChH,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACzG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACzG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAChH,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACzG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACvH,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAClG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAClG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACvH,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAClG,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAK,EAC7F,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,GAAG,EACtC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,cACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,WACd,kBAAmB,CAAC,OAAQ,MAAM,EAClC,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,mBACd,kBAAmB,CAAC,IAAI,EACxB,qBAAsB,CAAC,EAAG,EAAG,CAAC,CAClC,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,uBACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,YACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,EAAG,EAAG,GAAI,GAAI,EAAE,CAC3C,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,GAAG,EAC3C,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,iBACd,kBAAmB,CAAC,SAAU,QAAQ,EACtC,qBAAsB,CAAC,EAAE,CAC7B,EAIA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,sBACd,kBAAmB,CAAC,MAAM,EAC1B,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,YACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,UACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,GAAI,GAAI,GAAI,GAAI,EAAE,CAC7C,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,GAAG,EACtC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,IAAI,EAC7B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,UACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,EAAG,CAAC,CAC/B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,eACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAG,EAAG,EAAE,CACnC,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EACtD,qBAAsB,CAAC,EAAG,CAAC,CAC/B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,UACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,yBACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,KAAM,KAAM,KAAM,IAAI,EAC1C,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,UACd,kBAAmB,CAAC,OAAQ,OAAQ,MAAM,EAC1C,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,SACd,kBAAmB,CACf,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAC3F,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAC1C,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACxD,MAAO,MAAO,MAAO,MACrB,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACxD,MAAO,MAAO,MAAO,MAAO,MAAO,MACnC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MACxD,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAC5D,EACA,qBAAsB,CAAC,GAAI,EAAE,CACjC,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,WACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,oBACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,IAAI,EACxB,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EACtD,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,2BACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EAIA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,SACd,kBAAmB,CACf,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAC/D,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAC/D,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAC/D,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAC/D,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAC/D,KACJ,EACA,qBAAsB,CAAC,EAAE,CAC7B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,cACd,kBAAmB,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EACtD,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,QACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,QACd,kBAAmB,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EAC5D,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,mBACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,GAAG,EAChD,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,wCACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,eACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,WACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,OACd,aAAc,aACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,IAAI,EAC7B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,GAAG,EACtC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,OACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,iBACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,iBACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,UACd,kBAAmB,CAAC,KAAM,KAAM,IAAI,EACpC,qBAAsB,CAAC,GAAI,EAAE,CACjC,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,IAAI,EACxB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,WACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,UACd,kBAAmB,CAAC,IAAK,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EACvH,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,qBACd,kBAAmB,CAAC,MAAO,MAAO,KAAK,EACvC,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,QACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,GAAI,CAAC,CAChC,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,QACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,KAAM,KAAM,IAAI,EACzC,qBAAsB,CAAC,EAAG,CAAC,CAC/B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,EAAG,EAAE,CAChC,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,OACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,GAAG,EACtC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,8BACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,SACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,gBACd,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,kCACd,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,GAAG,EAChD,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,iBACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,YACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,gBACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,oBACd,kBAAmB,CAAC,MAAO,KAAK,EAChC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,UACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,YACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,YACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,gBACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,OACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,YACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,GAAG,EACpH,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,GAAG,EACtC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAG,CAAC,CAC/B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,UACd,kBAAmB,CAAC,KAAM,KAAM,KAAM,KAAM,IAAI,EAChD,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,YACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAG,GAAI,GAAI,EAAE,CACxC,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,QACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,GAAG,EACtC,qBAAsB,CAAC,EAAE,CAC7B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EAClE,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,4BACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,OACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,QACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAG,EAAE,CAChC,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,UACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,QACd,kBAAmB,CAAC,KAAM,KAAM,IAAI,EACpC,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,aACd,kBAAmB,CAAC,KAAM,KAAM,IAAI,EACpC,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,GAAG,EACtC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,GAAG,EAC3C,qBAAsB,CAAC,EAAG,CAAC,CAC/B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,IAAK,IAAI,EAC7B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,wBACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,qBACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAG,EAAE,CAChC,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,mCACd,kBAAmB,CAAC,IAAI,EACxB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,EAAG,CAAC,CAC/B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,GAAG,EACtC,qBAAsB,CAAC,EAAG,CAAC,CAC/B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,yBACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,cACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,gBACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,YACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,YACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,EAAG,CAAC,CAC/B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,uBACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,SACd,kBAAmB,CAAC,EAAE,EACtB,qBAAsB,CAAC,GAAI,EAAE,CACjC,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,mBACd,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,6CACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,OACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,UACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAG,EAAG,EAAE,CACnC,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,2BACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,aACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,YACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,KAAM,KAAM,IAAI,EACpC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,WACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,EAAG,GAAI,CAAC,CACnC,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,KAAM,KAAM,KAAM,IAAI,EAC1C,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,gBACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,iBACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,KAAM,KAAM,KAAM,KAAM,IAAI,EAChD,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,YACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,OACd,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,cACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,SACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,KAAM,IAAI,EAC9B,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,cACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAG,EAAG,EAAE,CACnC,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,OACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,WACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,OACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,cACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,mBACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,SACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,GAAG,EAC3C,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,cACd,kBAAmB,CAAC,MAAO,KAAK,EAChC,qBAAsB,CAAC,EAAE,CAC7B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,kCACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,mBACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,GAAG,EACtC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,MAAO,KAAK,EAChC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,UACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,qBACd,kBAAmB,CAAC,IAAK,MAAO,MAAO,KAAK,EAC5C,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,eACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,cACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,YACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,eACd,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,yBACd,kBAAmB,CAAC,IAAI,EACxB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,kBACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,eACd,kBAAmB,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EACtG,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,cACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EACxE,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,eACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,4BACd,kBAAmB,CAAC,KAAM,IAAI,EAC9B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAG,CAAC,CAC/B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,wBACd,kBAAmB,CAAC,KAAM,IAAI,EAC9B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,GAAG,EAC3C,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,uBACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,2BACd,kBAAmB,CAAC,OAAQ,OAAQ,MAAM,EAC1C,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,OACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,OACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,WACd,kBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,eACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,cACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,sBACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,GAAG,EACtC,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,+BACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,GAAG,EACjE,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,aACd,kBAAmB,CAAC,IAAK,KAAM,IAAI,EACnC,qBAAsB,CAAC,CAAC,CAC5B,EAEA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,mCACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,oCACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,0BACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,IACd,aAAc,uBACd,kBAAmB,CAAC,KAAK,EACzB,qBAAsB,CAAC,EAAE,CAC7B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,WACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,GAAG,EAC3C,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,UACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,oBACd,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,QACd,kBAAmB,CAAC,GAAG,EACvB,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,KACd,aAAc,eACd,kBAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAG,EAC1D,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,SACd,kBAAmB,CAAC,IAAK,GAAG,EAC5B,qBAAsB,CAAC,CAAC,CAC5B,EACA,CACI,OAAQ,KACR,OAAQ,MACR,aAAc,MACd,aAAc,WACd,kBAAmB,CAAC,KAAM,KAAM,IAAI,EACpC,qBAAsB,CAAC,CAAC,CAC5B,CACJ,IC52DA,IAAAC,EAAAC,EAAAC,GAAA,cACA,IAAIC,EAAmBD,GAAQA,EAAK,iBAAoB,SAAUE,EAAK,CACnE,OAAQA,GAAOA,EAAI,WAAcA,EAAM,CAAE,QAAWA,CAAI,CAC5D,EACA,OAAO,eAAeF,EAAS,aAAc,CAAE,MAAO,EAAK,CAAC,EAC5DA,EAAQ,qBAAuBA,EAAQ,kCAAoCA,EAAQ,6BAA+BA,EAAQ,0BAA4BA,EAAQ,8BAAgC,OAC9L,IAAMG,EAAuBF,EAAgB,GAAqC,EAKlF,SAASG,EAA8BC,EAAS,CAE5C,OAAKA,EAGDA,EAAQ,SAAW,EACZF,EAAqB,QAAQ,KAAKG,GAAqBD,EAAQ,YAAY,IAAMC,EAAkB,MAAM,GAAK,KAErHD,EAAQ,SAAW,EACZF,EAAqB,QAAQ,KAAKG,GAAqBD,EAAQ,YAAY,IAAMC,EAAkB,MAAM,GAAK,KAElHH,EAAqB,QAAQ,KAAKG,GAAqBD,EAAQ,YAAY,IAAMC,EAAkB,aAAa,YAAY,CAAC,GAAK,KAR9HH,EAAqB,QAAQ,KAAKG,GAAqBA,EAAkB,SAAW,KAAK,GAAK,IAS7G,CACAN,EAAQ,8BAAgCI,EACxC,SAASG,EAA0BC,EAAaC,EAAsBH,EAAmB,CAQrF,OANiCA,EAAkB,qBAAqB,KAAKI,GAIjEJ,EAAkB,aAAa,OAASI,IAAWF,EAAY,MAC1E,IAMG,CAACF,EAAkB,kBAAkB,QAAU,CAACG,GAIhDH,EAAkB,kBAAkB,KAAKK,GAClCH,EAAY,MAAM,IAAI,OAAO,IAAMF,EAAkB,aAAeK,CAAS,CAAC,CACxF,GACUL,EAXA,IAcf,CACAN,EAAQ,0BAA4BO,EACpC,SAASK,EAA6BJ,EAAaC,EAAsBH,EAAmB,CAQxF,GAAI,CAN6BA,EAAkB,qBAAqB,KAAKI,GAIjEJ,EAAkB,aAAa,OAASI,EAAS,IAAMF,EAAY,MAC9E,EAEG,OAAO,KASX,GALI,CAACF,EAAkB,kBAAkB,QAAU,CAACG,GAKhDH,EAAkB,kBAAkB,KAAKK,GAClCH,EAAY,MAAM,IAAI,OAAO,IAAMF,EAAkB,aAAe,OAASK,CAAS,CAAC,CACjG,EACG,OAAOL,CAEf,CACAN,EAAQ,6BAA+BY,EAQvC,SAASC,EAAkCL,EAAaC,EAAsB,CAC1E,IAAIK,EACAC,EACJ,QAAWT,KAAqBH,EAAqB,QAE5CK,EAAY,MAAM,IAAI,OAAO,IAAMF,EAAkB,YAAY,CAAC,IAIlEQ,IACDA,EAAwBP,EAA0BC,EAAaC,EAAsBH,CAAiB,GAErGS,IACDA,EAA2BH,EAA6BJ,EAAaC,EAAsBH,CAAiB,IAGpH,MAAO,CACH,sBAAAQ,EACA,yBAAAC,CACJ,CACJ,CACAf,EAAQ,kCAAoCa,EAY5C,SAASG,EAAqBC,EAAOX,EAAmBG,EAAsBS,EAAU,CACpF,GAAI,CAACZ,EAAkB,qBACnB,MAAO,GAGX,IAAMa,EAAsBF,EAAM,QAAQ,IAAI,OAAO,IAAMX,EAAkB,YAAY,EAAG,EAAE,EAI9F,GAAIY,GAAYZ,GAAqBa,EAAoB,SAAWF,EAAM,OACtE,MAAO,GAEX,IAAMG,EAAuBd,EAAkB,qBACzCe,EAAoBf,EAAkB,kBACtCgB,EAAgBF,EAAqB,KAAKV,GAAUS,EAAoB,SAAWT,CAAM,EAEzFa,EAAmBF,EAAkB,OACvCA,EAAkB,KAAKV,GAAaQ,EAAoB,MAAM,IAAI,OAAO,IAAMR,CAAS,CAAC,CAAC,EAC1F,GACJ,OAAOW,IAAkB,CAACb,GAAwBc,EACtD,CACAvB,EAAQ,qBAAuBgB,ICxI/B,IAAAQ,EAAAC,EAAAC,GAAA,cACA,IAAIC,EAAmBD,GAAQA,EAAK,iBAAoB,SAAUE,EAAK,CACnE,OAAQA,GAAOA,EAAI,WAAcA,EAAM,CAAE,QAAWA,CAAI,CAC5D,EACA,OAAO,eAAeF,EAAS,aAAc,CAAE,MAAO,EAAK,CAAC,EAC5DA,EAAQ,iBAAmBA,EAAQ,MAAQ,OAC3C,IAAMG,EAAuBF,EAAgB,GAAoC,EACjFD,EAAQ,iBAAmBG,EAAqB,QAChD,IAAMC,EAAY,IAWlB,SAASC,EAAMC,EAAa,CAAE,QAAAC,EAAU,GAAI,qBAAAC,EAAuB,GAAM,gBAAAC,EAAkB,EAAM,EAAI,CAAC,EAAG,CACrG,IAAMC,EAAgB,CAClB,QAAS,GACT,YAAa,KACb,YAAa,KACb,YAAa,KACb,YAAa,IACjB,EACIC,EAAwB,OAAOL,GAAgB,SAAY,GAAKA,EAAY,KAAK,EAC/EM,EAAoB,OAAOL,GAAY,SAAY,GAAKA,EAAQ,KAAK,EACrEM,EAAc,EAAQF,EAAqB,MAAM,KAAK,EAE5DA,EAAuBA,EAAqB,QAAQ,MAAO,EAAE,EAC7D,IAAIG,KAA4BV,EAAU,+BAA+BQ,CAAgB,EACzF,GAAI,CAACE,EACD,OAAOJ,EAEX,IAAIK,EAAiB,GAErB,GAAIH,EAEK,CAAC,MAAO,KAAK,EAAE,SAASE,EAAsB,MAAM,IACrDH,EAAuBA,EAAqB,QAAQ,MAAO,EAAE,GAG7DG,EAAsB,SAAW,OAASH,EAAqB,SAAW,IAAMA,EAAqB,MAAM,KAAK,IAAM,OACtHA,EAAuBA,EAAqB,QAAQ,MAAO,EAAE,GAI7D,CAACE,GAAeC,EAAsB,qBAAqB,SAASH,EAAqB,MAAM,IAC/FA,EAAuB,GAAGG,EAAsB,YAAY,GAAGH,CAAoB,YAGlFE,EAAa,CAGlB,GAAM,CAAE,sBAAAG,EAAuB,yBAAAC,CAAyB,KAAQb,EAAU,mCAAmCO,EAAsBH,CAAoB,EACnJQ,EACAF,EAAwBE,EAEnBC,GAA4B,CAACR,GAKlCK,EAAwBG,EACxBN,EAAuBG,EAAsB,aAAeH,EAAqB,QAAQ,IAAI,OAAO,IAAIG,EAAsB,YAAY,KAAK,EAAG,EAAE,GAGpJA,EAAwB,IAEhC,MACSA,EAAsB,qBAAqB,QAAQH,EAAqB,MAAM,IAAM,KAKzFA,EAAuB,IAAIA,CAAoB,GAC/CI,EAAiB,IAErB,GAAI,CAACD,EACD,OAAOJ,EAEX,IAAIQ,KAAqBd,EAAU,sBAAsBO,EAAsBG,EAAuBN,EAAsBK,CAAW,EACvI,OAAIK,EACO,CACH,QAAS,GACT,YAAa,IAAIP,CAAoB,GACrC,YAAaG,EAAsB,OACnC,YAAaA,EAAsB,OACnC,YAAa,IAAIA,EAAsB,YAAY,EACvD,EAEAC,IAEAD,KAA4BV,EAAU,+BAA+B,KAAK,EAC1Ec,KAAqBd,EAAU,sBAAsBO,EAAsBG,EAAuBN,EAAsBK,CAAW,EAC/HK,GACO,CACH,QAAS,GACT,YAAa,IAAIP,CAAoB,GACrC,YAAaG,EAAsB,OACnC,YAAaA,EAAsB,OACnC,YAAa,IAAIA,EAAsB,YAAY,EACvD,EAGDJ,CACX,CACAV,EAAQ,QAAUK,EAClBL,EAAQ,MAAQK,IC9GhB,IAAAc,GAAA,GAAAC,EAAAD,GAAA,kBAAAE,EAAA,YAAAC,IAAA,eAAAC,EAAAJ,ICqBO,SAASK,EAAYC,EAAuC,CACjE,OAAQA,EAAmB,QAAU,MACvC,CCtBA,IAAAC,EAAoC,OCDpC,IAAAC,EAA+B,kBCAxB,IAAMC,EACX,mEDEF,IAAMC,EAAuB,IACzBC,EAAMC,EACNC,EAAWC,GAAS,CAClB,CAACH,GAAQA,EAAK,OAASG,GACzBH,EAAO,OAAO,YAAYG,EAAQJ,CAAoB,KACtD,kBAAeC,CAAI,EACnBC,EAAa,GACJA,EAAaE,EAAQH,EAAK,YACnC,kBAAeA,CAAI,EACnBC,EAAa,GAEfA,GAAcE,CAChB,EAsBO,IAAIC,EAAS,CAACC,EAAO,KAAO,CACjCC,EAAUD,GAAQ,CAAE,EACpB,IAAIE,EAAK,GACT,QAASC,EAAIC,EAAaJ,EAAMG,EAAIC,EAAYD,IAC9CD,GAAMG,EAAYC,EAAKH,CAAC,EAAI,EAAE,EAEhC,OAAOD,CACT,EDlCA,eAAsBK,EAAS,CAAE,KAAAC,EAAM,MAAAC,EAAO,SAAAC,CAAS,EAAkB,CACvE,IAAMC,KAAmB,EAAAC,OAAWJ,EAAK,KAAK,EAExCK,EAAa,CAACL,EAAK,GAEzB,GAAI,CAACG,EAAiB,QACpB,MAAM,IAAI,MAAM,yBAAyB,EAG3C,IAAMG,EAA2B,CAC/B,GAAIN,EAAK,IAAMO,EAAS,EACxB,KAAMP,EAAK,KACX,OAAQ,CAAC,CAACA,EAAK,OACf,MAAOG,EAAiB,WAC1B,EAEMK,EAA8BP,EACjC,OAAOQ,CAAW,EAClB,KAAMT,GAASA,EAAK,QAAUG,EAAiB,WAAW,EAE7D,GAAIE,GAAcG,EAChB,MAAM,IAAI,MAAM,qBAAqB,EAGvC,GAAIH,EACFH,EAAS,CAAC,GAAGD,EAAOK,CAAU,CAAC,MAC1B,CACL,IAAMI,EAAWT,EAAM,IAAKD,GACtBA,EAAK,KAAOM,EAAW,GAClBA,EAEFN,CACR,EACDE,EAASQ,CAAQ,CACnB,CACF,CF3CA,IAAAC,EAAsB,wBAoBtB,eAAOC,EAAwBC,EAAc,CAC3C,IAAMC,EAAQ,IAAI,QACZC,EAAQD,EAAM,IAAI,gBAAgB,GAAK,KAC7C,MAAME,EAAS,CACb,KAAM,CACJ,KAAMH,EAAM,KACZ,MAAOA,EAAM,MACb,OAAQA,EAAM,QAAU,EAC1B,EACA,MAAO,KAAK,MAAME,CAAK,EACvB,SAAWA,GAAUD,EAAM,IAAI,iBAAkB,KAAK,UAAUC,CAAK,CAAC,CACxE,CAAC,CACH,CAEO,IAAME,EAAyC,MAAOJ,IACpD,CACL,QAAS,uBAAuBA,EAAM,IAAI,mBAC1C,KAAM,CACJ,CACE,KAAM,OACN,MAAOA,EAAM,IACf,EACA,CACE,KAAM,QACN,MAAOA,EAAM,KACf,EACA,CACE,KAAM,SACN,MAAOA,EAAM,OAAS,MAAQ,IAChC,CACF,CACF", "names": ["require_country_phone_data", "__commonJSMin", "exports", "require_utility", "__commonJSMin", "exports", "__importDefault", "mod", "country_phone_data_1", "findCountryPhoneDataByCountry", "country", "countryPhoneDatum", "findExactCountryPhoneData", "phoneNumber", "validateMobilePrefix", "length", "beginWith", "findPossibleCountryPhoneData", "findCountryPhoneDataByPhoneNumber", "exactCountryPhoneData", "possibleCountryPhoneData", "validatePhoneISO3166", "phone", "plusSign", "phoneWithoutCountry", "phone_number_lengths", "mobile_begin_with", "is<PERSON>ength<PERSON><PERSON>d", "isBeginWithValid", "require_dist", "__commonJSMin", "exports", "__importDefault", "mod", "country_phone_data_1", "utility_1", "phone", "phoneNumber", "country", "validateMobilePrefix", "strictDetection", "invalidR<PERSON>ult", "processedPhoneNumber", "processedCountry", "hasPlusSign", "foundCountryPhoneData", "defaultCountry", "exactCountryPhoneData", "possibleCountryPhoneData", "validateResult", "add_new_whatsapp_chat_exports", "__export", "confirmation", "add_new_whatsapp_chat_default", "__toCommonJS", "isPhoneChat", "chat", "import_phone", "import_crypto", "url<PERSON>l<PERSON><PERSON>", "POOL_SIZE_MULTIPLIER", "pool", "poolOffset", "fillPool", "bytes", "nanoid", "size", "fillPool", "id", "i", "poolOffset", "url<PERSON>l<PERSON><PERSON>", "pool", "saveChat", "chat", "chats", "setChats", "phoneInformation", "parsePhone", "isCreation", "chatToSave", "nanoid", "doesPhoneNumberAlreadyExist", "isPhoneChat", "newChats", "import_api", "add_new_whatsapp_chat_default", "input", "cache", "chats", "saveChat", "confirmation"]}