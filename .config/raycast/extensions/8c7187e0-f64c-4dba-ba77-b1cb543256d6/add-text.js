"use strict";var Vo=Object.create;var ht=Object.defineProperty;var Co=Object.getOwnPropertyDescriptor;var Po=Object.getOwnPropertyNames;var ko=Object.getPrototypeOf,Ro=Object.prototype.hasOwnProperty;var I=(p,t)=>()=>(t||p((t={exports:{}}).exports,t),t.exports),Oo=(p,t)=>{for(var i in t)ht(p,i,{get:t[i],enumerable:!0})},tr=(p,t,i,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of Po(t))!Ro.call(p,n)&&n!==i&&ht(p,n,{get:()=>t[n],enumerable:!(r=Co(t,n))||r.enumerable});return p};var p2=(p,t,i)=>(i=p!=null?Vo(ko(p)):{},tr(t||!p||!p.__esModule?ht(i,"default",{value:p,enumerable:!0}):i,p)),Lo=p=>tr(ht({},"__esModule",{value:!0}),p);var rr=I(ir=>{var dr=Object.prototype.hasOwnProperty;function y0(p,t){var i,r;if(p===t)return!0;if(p&&t&&(i=p.constructor)===t.constructor){if(i===Date)return p.getTime()===t.getTime();if(i===RegExp)return p.toString()===t.toString();if(i===Array){if((r=p.length)===t.length)for(;r--&&y0(p[r],t[r]););return r===-1}if(!i||typeof p=="object"){r=0;for(i in p)if(dr.call(p,i)&&++r&&!dr.call(t,i)||!(i in t)||!y0(p[i],t[i]))return!1;return Object.keys(t).length===r}}return p!==p&&t!==t}ir.dequal=y0});var nr=I(vt=>{"use strict";Object.defineProperty(vt,"__esModule",{value:!0});vt.useDeepMemo=void 0;var S0=require("react"),Io=rr();function Fo(p){let t=(0,S0.useRef)(p),i=(0,S0.useRef)(0);return(0,Io.dequal)(p,t.current)||(t.current=p,i.current+=1),(0,S0.useMemo)(()=>t.current,[i.current])}vt.useDeepMemo=Fo});var aa=I(_t=>{"use strict";Object.defineProperty(_t,"__esModule",{value:!0});_t.useLatest=void 0;var Bo=require("react");function Uo(p){let t=(0,Bo.useRef)(p);return t.current=p,t}_t.useLatest=Uo});var t2=I(Le=>{"use strict";var Mo=Le&&Le.__createBinding||(Object.create?function(p,t,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(t,i);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(p,r,n)}:function(p,t,i,r){r===void 0&&(r=i),p[r]=t[i]}),qo=Le&&Le.__setModuleDefault||(Object.create?function(p,t){Object.defineProperty(p,"default",{enumerable:!0,value:t})}:function(p,t){p.default=t}),lr=Le&&Le.__importStar||function(p){if(p&&p.__esModule)return p;var t={};if(p!=null)for(var i in p)i!=="default"&&Object.prototype.hasOwnProperty.call(p,i)&&Mo(t,p,i);return qo(t,p),t};Object.defineProperty(Le,"__esModule",{value:!0});Le.showFailureToast=void 0;var zo=lr(require("fs")),$o=lr(require("path")),up=require("@raycast/api");function Wo(p,t){let i=p instanceof Error?p.message:String(p);return(0,up.showToast)({style:up.Toast.Style.Failure,title:t?.title??"Something went wrong",message:t?.message??i,primaryAction:t?.primaryAction??sr(p),secondaryAction:t?.primaryAction?sr(p):void 0})}Le.showFailureToast=Wo;var sr=p=>{let t=!0,i="[Extension Name]...",r="";try{let v=JSON.parse(zo.readFileSync($o.join(up.environment.assetsPath,"..","package.json"),"utf8"));i=`[${v.title}]...`,r=`https://raycast.com/${v.owner||v.author}/${v.name}`,(!v.owner||v.access==="public")&&(t=!1)}catch{}let n=up.environment.isDevelopment||t,u=p instanceof Error?p?.stack||p?.message||"":String(p);return{title:n?"Copy Logs":"Report Error",onAction(v){v.hide(),n?up.Clipboard.copy(u):(0,up.open)(`https://github.com/raycast/extensions/issues/new?&labels=extension%2Cbug&template=extension_bug_report.yml&title=${encodeURIComponent(i)}&extension-url=${encodeURI(r)}&description=${encodeURIComponent(`#### Error:
\`\`\`
${u}
\`\`\`
`)}`)}}}});var cp=I(wt=>{"use strict";Object.defineProperty(wt,"__esModule",{value:!0});wt.usePromise=void 0;var ie=require("react"),d2=require("@raycast/api"),jo=nr(),wa=aa(),Ho=t2();function Go(p,t,i){let r=(0,ie.useRef)(0),[n,u]=(0,ie.useState)({isLoading:!0}),v=(0,wa.useLatest)(p),_=(0,wa.useLatest)(i?.abortable),f=(0,wa.useLatest)(t||[]),N=(0,wa.useLatest)(i?.onError),A=(0,wa.useLatest)(i?.onData),D=(0,wa.useLatest)(i?.onWillExecute),V=(0,wa.useLatest)(i?.failureToastOptions),F=(0,wa.useLatest)(n.data),C=(0,ie.useRef)(),R=(0,ie.useRef)({page:0}),q=(0,ie.useRef)(!1),P=(0,ie.useRef)(!0),L=(0,ie.useRef)(50),B=(0,ie.useCallback)((...R1)=>{let T1=++r.current;_.current&&(_.current.current?.abort(),_.current.current=new AbortController),D.current?.(R1),u(a1=>({...a1,isLoading:!0}));let H1=Ko(v.current)(...R1);function ne(a1){return a1.name=="AbortError"||T1===r.current&&(N.current?N.current(a1):d2.environment.launchType!==d2.LaunchType.Background&&(0,Ho.showFailureToast)(a1,{title:"Failed to fetch latest data",primaryAction:{title:"Retry",onAction(w1){w1.hide(),C.current?.(...f.current||[])}},...V.current}),u({error:a1,isLoading:!1})),a1}return typeof H1=="function"?(q.current=!0,H1(R.current).then(({data:a1,hasMore:w1,cursor:G1})=>(T1===r.current&&(R.current&&(R.current.cursor=G1,R.current.lastItem=a1?.[a1.length-1]),A.current&&A.current(a1,R.current),w1&&(L.current=a1.length),P.current=w1,u(Rp=>R.current.page===0?{data:a1,isLoading:!1}:{data:(Rp.data||[])?.concat(a1),isLoading:!1})),a1),a1=>(P.current=!1,ne(a1)))):(q.current=!1,H1.then(a1=>(T1===r.current&&(A.current&&A.current(a1),u({data:a1,isLoading:!1})),a1),ne))},[_,A,N,f,v,u,C,D,R,V]);C.current=B;let U=(0,ie.useCallback)(()=>{R.current={page:0};let R1=f.current||[];return B(...R1)},[B,f]),H=(0,ie.useCallback)(async(R1,T1)=>{let H1;try{if(T1?.optimisticUpdate){typeof T1?.rollbackOnError!="function"&&T1?.rollbackOnError!==!1&&(H1=structuredClone(F.current?.value));let ne=T1.optimisticUpdate;u(a1=>({...a1,data:ne(a1.data)}))}return await R1}catch(ne){if(typeof T1?.rollbackOnError=="function"){let a1=T1.rollbackOnError;u(w1=>({...w1,data:a1(w1.data)}))}else T1?.optimisticUpdate&&T1?.rollbackOnError!==!1&&u(a1=>({...a1,data:H1}));throw ne}finally{T1?.shouldRevalidateAfter!==!1&&(d2.environment.launchType===d2.LaunchType.Background||d2.environment.commandMode==="menu-bar"?await U():U())}},[U,F,u]),e1=(0,ie.useCallback)(()=>{R.current.page+=1;let R1=f.current||[];B(...R1)},[R,f,B]);(0,ie.useEffect)(()=>{R.current={page:0},i?.execute!==!1?B(...t||[]):_.current&&_.current.current?.abort()},[(0,jo.useDeepMemo)([t,i?.execute,B]),_,R]),(0,ie.useEffect)(()=>()=>{_.current&&_.current.current?.abort()},[_]);let N1=i?.execute!==!1?n.isLoading:!1,F1={...n,isLoading:N1},Q1=q.current?{pageSize:L.current,hasMore:P.current,onLoadMore:e1}:void 0;return{...F1,revalidate:U,mutate:H,pagination:Q1}}wt.usePromise=Go;function Ko(p){return p===Promise.all||p===Promise.race||p===Promise.resolve||p===Promise.reject?p.bind(Promise):p}});var hr=I((Ia,fr)=>{"use strict";var N0=require("crypto");Ia=fr.exports=i2;function i2(p,t){return t=ur(p,t),Zo(p,t)}Ia.sha1=function(p){return i2(p)};Ia.keys=function(p){return i2(p,{excludeValues:!0,algorithm:"sha1",encoding:"hex"})};Ia.MD5=function(p){return i2(p,{algorithm:"md5",encoding:"hex"})};Ia.keysMD5=function(p){return i2(p,{algorithm:"md5",encoding:"hex",excludeValues:!0})};var fp=N0.getHashes?N0.getHashes().slice():["sha1","md5"];fp.push("passthrough");var mr=["buffer","hex","binary","base64"];function ur(p,t){t=t||{};var i={};if(i.algorithm=t.algorithm||"sha1",i.encoding=t.encoding||"hex",i.excludeValues=!!t.excludeValues,i.algorithm=i.algorithm.toLowerCase(),i.encoding=i.encoding.toLowerCase(),i.ignoreUnknown=t.ignoreUnknown===!0,i.respectType=t.respectType!==!1,i.respectFunctionNames=t.respectFunctionNames!==!1,i.respectFunctionProperties=t.respectFunctionProperties!==!1,i.unorderedArrays=t.unorderedArrays===!0,i.unorderedSets=t.unorderedSets!==!1,i.unorderedObjects=t.unorderedObjects!==!1,i.replacer=t.replacer||void 0,i.excludeKeys=t.excludeKeys||void 0,typeof p>"u")throw new Error("Object argument required.");for(var r=0;r<fp.length;++r)fp[r].toLowerCase()===i.algorithm.toLowerCase()&&(i.algorithm=fp[r]);if(fp.indexOf(i.algorithm)===-1)throw new Error('Algorithm "'+i.algorithm+'"  not supported. supported values: '+fp.join(", "));if(mr.indexOf(i.encoding)===-1&&i.algorithm!=="passthrough")throw new Error('Encoding "'+i.encoding+'"  not supported. supported values: '+mr.join(", "));return i}function or(p){if(typeof p!="function")return!1;var t=/^function\s+\w*\s*\(\s*\)\s*{\s+\[native code\]\s+}$/i;return t.exec(Function.prototype.toString.call(p))!=null}function Zo(p,t){var i;t.algorithm!=="passthrough"?i=N0.createHash(t.algorithm):i=new cr,typeof i.write>"u"&&(i.write=i.update,i.end=i.update);var r=T0(t,i);if(r.dispatch(p),i.update||i.end(""),i.digest)return i.digest(t.encoding==="buffer"?void 0:t.encoding);var n=i.read();return t.encoding==="buffer"?n:n.toString(t.encoding)}Ia.writeToStream=function(p,t,i){return typeof i>"u"&&(i=t,t={}),t=ur(p,t),T0(t,i).dispatch(p)};function T0(p,t,i){i=i||[];var r=function(n){return t.update?t.update(n,"utf8"):t.write(n,"utf8")};return{dispatch:function(n){p.replacer&&(n=p.replacer(n));var u=typeof n;return n===null&&(u="null"),this["_"+u](n)},_object:function(n){var u=/\[object (.*)\]/i,v=Object.prototype.toString.call(n),_=u.exec(v);_?_=_[1]:_="unknown:["+v+"]",_=_.toLowerCase();var f=null;if((f=i.indexOf(n))>=0)return this.dispatch("[CIRCULAR:"+f+"]");if(i.push(n),typeof Buffer<"u"&&Buffer.isBuffer&&Buffer.isBuffer(n))return r("buffer:"),r(n);if(_!=="object"&&_!=="function"&&_!=="asyncfunction")if(this["_"+_])this["_"+_](n);else{if(p.ignoreUnknown)return r("["+_+"]");throw new Error('Unknown object type "'+_+'"')}else{var N=Object.keys(n);p.unorderedObjects&&(N=N.sort()),p.respectType!==!1&&!or(n)&&N.splice(0,0,"prototype","__proto__","constructor"),p.excludeKeys&&(N=N.filter(function(D){return!p.excludeKeys(D)})),r("object:"+N.length+":");var A=this;return N.forEach(function(D){A.dispatch(D),r(":"),p.excludeValues||A.dispatch(n[D]),r(",")})}},_array:function(n,u){u=typeof u<"u"?u:p.unorderedArrays!==!1;var v=this;if(r("array:"+n.length+":"),!u||n.length<=1)return n.forEach(function(N){return v.dispatch(N)});var _=[],f=n.map(function(N){var A=new cr,D=i.slice(),V=T0(p,A,D);return V.dispatch(N),_=_.concat(D.slice(i.length)),A.read().toString()});return i=i.concat(_),f.sort(),this._array(f,!1)},_date:function(n){return r("date:"+n.toJSON())},_symbol:function(n){return r("symbol:"+n.toString())},_error:function(n){return r("error:"+n.toString())},_boolean:function(n){return r("bool:"+n.toString())},_string:function(n){r("string:"+n.length+":"),r(n.toString())},_function:function(n){r("fn:"),or(n)?this.dispatch("[native]"):this.dispatch(n.toString()),p.respectFunctionNames!==!1&&this.dispatch("function-name:"+String(n.name)),p.respectFunctionProperties&&this._object(n)},_number:function(n){return r("number:"+n.toString())},_xml:function(n){return r("xml:"+n.toString())},_null:function(){return r("Null")},_undefined:function(){return r("Undefined")},_regexp:function(n){return r("regex:"+n.toString())},_uint8array:function(n){return r("uint8array:"),this.dispatch(Array.prototype.slice.call(n))},_uint8clampedarray:function(n){return r("uint8clampedarray:"),this.dispatch(Array.prototype.slice.call(n))},_int8array:function(n){return r("int8array:"),this.dispatch(Array.prototype.slice.call(n))},_uint16array:function(n){return r("uint16array:"),this.dispatch(Array.prototype.slice.call(n))},_int16array:function(n){return r("int16array:"),this.dispatch(Array.prototype.slice.call(n))},_uint32array:function(n){return r("uint32array:"),this.dispatch(Array.prototype.slice.call(n))},_int32array:function(n){return r("int32array:"),this.dispatch(Array.prototype.slice.call(n))},_float32array:function(n){return r("float32array:"),this.dispatch(Array.prototype.slice.call(n))},_float64array:function(n){return r("float64array:"),this.dispatch(Array.prototype.slice.call(n))},_arraybuffer:function(n){return r("arraybuffer:"),this.dispatch(new Uint8Array(n))},_url:function(n){return r("url:"+n.toString(),"utf8")},_map:function(n){r("map:");var u=Array.from(n);return this._array(u,p.unorderedSets!==!1)},_set:function(n){r("set:");var u=Array.from(n);return this._array(u,p.unorderedSets!==!1)},_file:function(n){return r("file:"),this.dispatch([n.name,n.size,n.type,n.lastModfied])},_blob:function(){if(p.ignoreUnknown)return r("[blob]");throw Error(`Hashing Blob objects is currently not supported
(see https://github.com/puleos/object-hash/issues/26)
Use "options.replacer" or "options.ignoreUnknown"
`)},_domwindow:function(){return r("domwindow")},_bigint:function(n){return r("bigint:"+n.toString())},_process:function(){return r("process")},_timer:function(){return r("timer")},_pipe:function(){return r("pipe")},_tcp:function(){return r("tcp")},_udp:function(){return r("udp")},_tty:function(){return r("tty")},_statwatcher:function(){return r("statwatcher")},_securecontext:function(){return r("securecontext")},_connection:function(){return r("connection")},_zlib:function(){return r("zlib")},_context:function(){return r("context")},_nodescript:function(){return r("nodescript")},_httpparser:function(){return r("httpparser")},_dataview:function(){return r("dataview")},_signal:function(){return r("signal")},_fsevent:function(){return r("fsevent")},_tlswrap:function(){return r("tlswrap")}}}function cr(){return{buf:"",write:function(p){this.buf+=p},end:function(p){this.buf+=p},read:function(){return this.buf}}}});var Fa=I(Ie=>{"use strict";var Jo=Ie&&Ie.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(Ie,"__esModule",{value:!0});Ie.hash=Ie.reviver=Ie.replacer=void 0;var Yo=Jo(hr());function Qo(p,t){let i=this[p];return i instanceof Date?`__raycast_cached_date__${i.toString()}`:Buffer.isBuffer(i)?`__raycast_cached_buffer__${i.toString("base64")}`:t}Ie.replacer=Qo;function Xo(p,t){return typeof t=="string"&&t.startsWith("__raycast_cached_date__")?new Date(t.replace("__raycast_cached_date__","")):typeof t=="string"&&t.startsWith("__raycast_cached_buffer__")?Buffer.from(t.replace("__raycast_cached_buffer__",""),"base64"):t}Ie.reviver=Xo;function eu(p,t){return(0,Yo.default)(p,{replacer:i=>i instanceof URLSearchParams?i.toString():i,...t})}Ie.hash=eu});var bt=I(gt=>{"use strict";Object.defineProperty(gt,"__esModule",{value:!0});gt.useCachedState=void 0;var x0=require("react"),au=require("@raycast/api"),D0=aa(),vr=Fa(),pu=Symbol("cache without namespace"),_r=new Map;function tu(p,t,i){let r=i?.cacheNamespace||pu,n=_r.get(r)||_r.set(r,new au.Cache({namespace:i?.cacheNamespace})).get(r);if(!n)throw new Error("Missing cache");let u=(0,D0.useLatest)(p),v=(0,D0.useLatest)(t),_=(0,x0.useSyncExternalStore)(n.subscribe,()=>{try{return n.get(u.current)}catch(D){console.error("Could not get Cache data:",D);return}}),f=(0,x0.useMemo)(()=>{if(typeof _<"u"){if(_==="undefined")return;try{return JSON.parse(_,vr.reviver)}catch(D){return console.warn("The cached data is corrupted",D),v.current}}else return v.current},[_,v]),N=(0,D0.useLatest)(f),A=(0,x0.useCallback)(D=>{let V=typeof D=="function"?D(N.current):D;if(typeof V>"u")n.set(u.current,"undefined");else{let F=JSON.stringify(V,vr.replacer);n.set(u.current,F)}return V},[n,u,N]);return[f,A]}gt.useCachedState=tu});var n2=I(yt=>{"use strict";Object.defineProperty(yt,"__esModule",{value:!0});yt.useCachedPromise=void 0;var r2=require("react"),du=bt(),iu=cp(),ru=aa(),wr=Fa(),hp=Symbol();function nu(p,t,i){let{initialData:r,keepPreviousData:n,internal_cacheKeySuffix:u,...v}=i||{},_=(0,r2.useRef)(),[f,N]=(0,du.useCachedState)((0,wr.hash)(t||[])+u,hp,{cacheNamespace:(0,wr.hash)(p)}),A=(0,r2.useRef)(f!==hp?f:r),D=(0,r2.useRef)(void 0),{mutate:V,revalidate:F,...C}=(0,iu.usePromise)(p,t||[],{...v,onData(B,U){D.current=U,v.onData&&v.onData(B,U),!(U&&U.page>0)&&(_.current="promise",A.current=B,N(B))}}),R,q=C.pagination;D.current&&D.current.page>0&&C.data?R=C.data:_.current==="promise"?R=A.current:n&&f!==hp?(R=f,q&&(q.hasMore=!0,q.pageSize=f.length)):n&&f===hp?R=A.current:f!==hp?(R=f,q&&(q.hasMore=!0,q.pageSize=f.length)):R=r;let P=(0,ru.useLatest)(R),L=(0,r2.useCallback)(async(B,U)=>{let H;try{if(U?.optimisticUpdate){typeof U?.rollbackOnError!="function"&&U?.rollbackOnError!==!1&&(H=structuredClone(P.current));let e1=U.optimisticUpdate(P.current);_.current="cache",A.current=e1,N(e1)}return await V(B,{shouldRevalidateAfter:U?.shouldRevalidateAfter})}catch(e1){if(typeof U?.rollbackOnError=="function"){let N1=U.rollbackOnError(P.current);_.current="cache",A.current=N1,N(N1)}else U?.optimisticUpdate&&U?.rollbackOnError!==!1&&(_.current="cache",A.current=H,N(H));throw e1}},[N,V,P,A,_]);return(0,r2.useEffect)(()=>{f!==hp&&(_.current="cache",A.current=f)},[f]),{data:R,isLoading:C.isLoading,error:C.error,mutate:D.current&&D.current.page>0?V:L,pagination:q,revalidate:F}}yt.useCachedPromise=nu});var yr=I((Wh,br)=>{"use strict";var h1={};br.exports=h1;function gr(p){return p<0?-1:1}function su(p){return p%1===.5&&!(p&1)?Math.floor(p):Math.round(p)}function ga(p,t){t.unsigned||--p;let i=t.unsigned?0:-Math.pow(2,p),r=Math.pow(2,p)-1,n=t.moduloBitLength?Math.pow(2,t.moduloBitLength):Math.pow(2,p),u=t.moduloBitLength?Math.pow(2,t.moduloBitLength-1):Math.pow(2,p-1);return function(v,_){_||(_={});let f=+v;if(_.enforceRange){if(!Number.isFinite(f))throw new TypeError("Argument is not a finite number");if(f=gr(f)*Math.floor(Math.abs(f)),f<i||f>r)throw new TypeError("Argument is not in byte range");return f}if(!isNaN(f)&&_.clamp)return f=su(f),f<i&&(f=i),f>r&&(f=r),f;if(!Number.isFinite(f)||f===0)return 0;if(f=gr(f)*Math.floor(Math.abs(f)),f=f%n,!t.unsigned&&f>=u)return f-n;if(t.unsigned){if(f<0)f+=n;else if(f===-0)return 0}return f}}h1.void=function(){};h1.boolean=function(p){return!!p};h1.byte=ga(8,{unsigned:!1});h1.octet=ga(8,{unsigned:!0});h1.short=ga(16,{unsigned:!1});h1["unsigned short"]=ga(16,{unsigned:!0});h1.long=ga(32,{unsigned:!1});h1["unsigned long"]=ga(32,{unsigned:!0});h1["long long"]=ga(32,{unsigned:!1,moduloBitLength:64});h1["unsigned long long"]=ga(32,{unsigned:!0,moduloBitLength:64});h1.double=function(p){let t=+p;if(!Number.isFinite(t))throw new TypeError("Argument is not a finite floating-point value");return t};h1["unrestricted double"]=function(p){let t=+p;if(isNaN(t))throw new TypeError("Argument is NaN");return t};h1.float=h1.double;h1["unrestricted float"]=h1["unrestricted double"];h1.DOMString=function(p,t){return t||(t={}),t.treatNullAsEmptyString&&p===null?"":String(p)};h1.ByteString=function(p,t){let i=String(p),r;for(let n=0;(r=i.codePointAt(n))!==void 0;++n)if(r>255)throw new TypeError("Argument is not a valid bytestring");return i};h1.USVString=function(p){let t=String(p),i=t.length,r=[];for(let n=0;n<i;++n){let u=t.charCodeAt(n);if(u<55296||u>57343)r.push(String.fromCodePoint(u));else if(56320<=u&&u<=57343)r.push(String.fromCodePoint(65533));else if(n===i-1)r.push(String.fromCodePoint(65533));else{let v=t.charCodeAt(n+1);if(56320<=v&&v<=57343){let _=u&1023,f=v&1023;r.push(String.fromCodePoint(65536+1024*_+f)),++n}else r.push(String.fromCodePoint(65533))}}return r.join("")};h1.Date=function(p,t){if(!(p instanceof Date))throw new TypeError("Argument is not a Date object");if(!isNaN(p))return p};h1.RegExp=function(p,t){return p instanceof RegExp||(p=new RegExp(p)),p}});var Sr=I((jh,ba)=>{"use strict";ba.exports.mixin=function(t,i){let r=Object.getOwnPropertyNames(i);for(let n=0;n<r.length;++n)Object.defineProperty(t,r[n],Object.getOwnPropertyDescriptor(i,r[n]))};ba.exports.wrapperSymbol=Symbol("wrapper");ba.exports.implSymbol=Symbol("impl");ba.exports.wrapperForImpl=function(p){return p[ba.exports.wrapperSymbol]};ba.exports.implForWrapper=function(p){return p[ba.exports.implSymbol]}});var Nr=I((Hh,lu)=>{lu.exports=[[[0,44],"disallowed_STD3_valid"],[[45,46],"valid"],[[47,47],"disallowed_STD3_valid"],[[48,57],"valid"],[[58,64],"disallowed_STD3_valid"],[[65,65],"mapped",[97]],[[66,66],"mapped",[98]],[[67,67],"mapped",[99]],[[68,68],"mapped",[100]],[[69,69],"mapped",[101]],[[70,70],"mapped",[102]],[[71,71],"mapped",[103]],[[72,72],"mapped",[104]],[[73,73],"mapped",[105]],[[74,74],"mapped",[106]],[[75,75],"mapped",[107]],[[76,76],"mapped",[108]],[[77,77],"mapped",[109]],[[78,78],"mapped",[110]],[[79,79],"mapped",[111]],[[80,80],"mapped",[112]],[[81,81],"mapped",[113]],[[82,82],"mapped",[114]],[[83,83],"mapped",[115]],[[84,84],"mapped",[116]],[[85,85],"mapped",[117]],[[86,86],"mapped",[118]],[[87,87],"mapped",[119]],[[88,88],"mapped",[120]],[[89,89],"mapped",[121]],[[90,90],"mapped",[122]],[[91,96],"disallowed_STD3_valid"],[[97,122],"valid"],[[123,127],"disallowed_STD3_valid"],[[128,159],"disallowed"],[[160,160],"disallowed_STD3_mapped",[32]],[[161,167],"valid",[],"NV8"],[[168,168],"disallowed_STD3_mapped",[32,776]],[[169,169],"valid",[],"NV8"],[[170,170],"mapped",[97]],[[171,172],"valid",[],"NV8"],[[173,173],"ignored"],[[174,174],"valid",[],"NV8"],[[175,175],"disallowed_STD3_mapped",[32,772]],[[176,177],"valid",[],"NV8"],[[178,178],"mapped",[50]],[[179,179],"mapped",[51]],[[180,180],"disallowed_STD3_mapped",[32,769]],[[181,181],"mapped",[956]],[[182,182],"valid",[],"NV8"],[[183,183],"valid"],[[184,184],"disallowed_STD3_mapped",[32,807]],[[185,185],"mapped",[49]],[[186,186],"mapped",[111]],[[187,187],"valid",[],"NV8"],[[188,188],"mapped",[49,8260,52]],[[189,189],"mapped",[49,8260,50]],[[190,190],"mapped",[51,8260,52]],[[191,191],"valid",[],"NV8"],[[192,192],"mapped",[224]],[[193,193],"mapped",[225]],[[194,194],"mapped",[226]],[[195,195],"mapped",[227]],[[196,196],"mapped",[228]],[[197,197],"mapped",[229]],[[198,198],"mapped",[230]],[[199,199],"mapped",[231]],[[200,200],"mapped",[232]],[[201,201],"mapped",[233]],[[202,202],"mapped",[234]],[[203,203],"mapped",[235]],[[204,204],"mapped",[236]],[[205,205],"mapped",[237]],[[206,206],"mapped",[238]],[[207,207],"mapped",[239]],[[208,208],"mapped",[240]],[[209,209],"mapped",[241]],[[210,210],"mapped",[242]],[[211,211],"mapped",[243]],[[212,212],"mapped",[244]],[[213,213],"mapped",[245]],[[214,214],"mapped",[246]],[[215,215],"valid",[],"NV8"],[[216,216],"mapped",[248]],[[217,217],"mapped",[249]],[[218,218],"mapped",[250]],[[219,219],"mapped",[251]],[[220,220],"mapped",[252]],[[221,221],"mapped",[253]],[[222,222],"mapped",[254]],[[223,223],"deviation",[115,115]],[[224,246],"valid"],[[247,247],"valid",[],"NV8"],[[248,255],"valid"],[[256,256],"mapped",[257]],[[257,257],"valid"],[[258,258],"mapped",[259]],[[259,259],"valid"],[[260,260],"mapped",[261]],[[261,261],"valid"],[[262,262],"mapped",[263]],[[263,263],"valid"],[[264,264],"mapped",[265]],[[265,265],"valid"],[[266,266],"mapped",[267]],[[267,267],"valid"],[[268,268],"mapped",[269]],[[269,269],"valid"],[[270,270],"mapped",[271]],[[271,271],"valid"],[[272,272],"mapped",[273]],[[273,273],"valid"],[[274,274],"mapped",[275]],[[275,275],"valid"],[[276,276],"mapped",[277]],[[277,277],"valid"],[[278,278],"mapped",[279]],[[279,279],"valid"],[[280,280],"mapped",[281]],[[281,281],"valid"],[[282,282],"mapped",[283]],[[283,283],"valid"],[[284,284],"mapped",[285]],[[285,285],"valid"],[[286,286],"mapped",[287]],[[287,287],"valid"],[[288,288],"mapped",[289]],[[289,289],"valid"],[[290,290],"mapped",[291]],[[291,291],"valid"],[[292,292],"mapped",[293]],[[293,293],"valid"],[[294,294],"mapped",[295]],[[295,295],"valid"],[[296,296],"mapped",[297]],[[297,297],"valid"],[[298,298],"mapped",[299]],[[299,299],"valid"],[[300,300],"mapped",[301]],[[301,301],"valid"],[[302,302],"mapped",[303]],[[303,303],"valid"],[[304,304],"mapped",[105,775]],[[305,305],"valid"],[[306,307],"mapped",[105,106]],[[308,308],"mapped",[309]],[[309,309],"valid"],[[310,310],"mapped",[311]],[[311,312],"valid"],[[313,313],"mapped",[314]],[[314,314],"valid"],[[315,315],"mapped",[316]],[[316,316],"valid"],[[317,317],"mapped",[318]],[[318,318],"valid"],[[319,320],"mapped",[108,183]],[[321,321],"mapped",[322]],[[322,322],"valid"],[[323,323],"mapped",[324]],[[324,324],"valid"],[[325,325],"mapped",[326]],[[326,326],"valid"],[[327,327],"mapped",[328]],[[328,328],"valid"],[[329,329],"mapped",[700,110]],[[330,330],"mapped",[331]],[[331,331],"valid"],[[332,332],"mapped",[333]],[[333,333],"valid"],[[334,334],"mapped",[335]],[[335,335],"valid"],[[336,336],"mapped",[337]],[[337,337],"valid"],[[338,338],"mapped",[339]],[[339,339],"valid"],[[340,340],"mapped",[341]],[[341,341],"valid"],[[342,342],"mapped",[343]],[[343,343],"valid"],[[344,344],"mapped",[345]],[[345,345],"valid"],[[346,346],"mapped",[347]],[[347,347],"valid"],[[348,348],"mapped",[349]],[[349,349],"valid"],[[350,350],"mapped",[351]],[[351,351],"valid"],[[352,352],"mapped",[353]],[[353,353],"valid"],[[354,354],"mapped",[355]],[[355,355],"valid"],[[356,356],"mapped",[357]],[[357,357],"valid"],[[358,358],"mapped",[359]],[[359,359],"valid"],[[360,360],"mapped",[361]],[[361,361],"valid"],[[362,362],"mapped",[363]],[[363,363],"valid"],[[364,364],"mapped",[365]],[[365,365],"valid"],[[366,366],"mapped",[367]],[[367,367],"valid"],[[368,368],"mapped",[369]],[[369,369],"valid"],[[370,370],"mapped",[371]],[[371,371],"valid"],[[372,372],"mapped",[373]],[[373,373],"valid"],[[374,374],"mapped",[375]],[[375,375],"valid"],[[376,376],"mapped",[255]],[[377,377],"mapped",[378]],[[378,378],"valid"],[[379,379],"mapped",[380]],[[380,380],"valid"],[[381,381],"mapped",[382]],[[382,382],"valid"],[[383,383],"mapped",[115]],[[384,384],"valid"],[[385,385],"mapped",[595]],[[386,386],"mapped",[387]],[[387,387],"valid"],[[388,388],"mapped",[389]],[[389,389],"valid"],[[390,390],"mapped",[596]],[[391,391],"mapped",[392]],[[392,392],"valid"],[[393,393],"mapped",[598]],[[394,394],"mapped",[599]],[[395,395],"mapped",[396]],[[396,397],"valid"],[[398,398],"mapped",[477]],[[399,399],"mapped",[601]],[[400,400],"mapped",[603]],[[401,401],"mapped",[402]],[[402,402],"valid"],[[403,403],"mapped",[608]],[[404,404],"mapped",[611]],[[405,405],"valid"],[[406,406],"mapped",[617]],[[407,407],"mapped",[616]],[[408,408],"mapped",[409]],[[409,411],"valid"],[[412,412],"mapped",[623]],[[413,413],"mapped",[626]],[[414,414],"valid"],[[415,415],"mapped",[629]],[[416,416],"mapped",[417]],[[417,417],"valid"],[[418,418],"mapped",[419]],[[419,419],"valid"],[[420,420],"mapped",[421]],[[421,421],"valid"],[[422,422],"mapped",[640]],[[423,423],"mapped",[424]],[[424,424],"valid"],[[425,425],"mapped",[643]],[[426,427],"valid"],[[428,428],"mapped",[429]],[[429,429],"valid"],[[430,430],"mapped",[648]],[[431,431],"mapped",[432]],[[432,432],"valid"],[[433,433],"mapped",[650]],[[434,434],"mapped",[651]],[[435,435],"mapped",[436]],[[436,436],"valid"],[[437,437],"mapped",[438]],[[438,438],"valid"],[[439,439],"mapped",[658]],[[440,440],"mapped",[441]],[[441,443],"valid"],[[444,444],"mapped",[445]],[[445,451],"valid"],[[452,454],"mapped",[100,382]],[[455,457],"mapped",[108,106]],[[458,460],"mapped",[110,106]],[[461,461],"mapped",[462]],[[462,462],"valid"],[[463,463],"mapped",[464]],[[464,464],"valid"],[[465,465],"mapped",[466]],[[466,466],"valid"],[[467,467],"mapped",[468]],[[468,468],"valid"],[[469,469],"mapped",[470]],[[470,470],"valid"],[[471,471],"mapped",[472]],[[472,472],"valid"],[[473,473],"mapped",[474]],[[474,474],"valid"],[[475,475],"mapped",[476]],[[476,477],"valid"],[[478,478],"mapped",[479]],[[479,479],"valid"],[[480,480],"mapped",[481]],[[481,481],"valid"],[[482,482],"mapped",[483]],[[483,483],"valid"],[[484,484],"mapped",[485]],[[485,485],"valid"],[[486,486],"mapped",[487]],[[487,487],"valid"],[[488,488],"mapped",[489]],[[489,489],"valid"],[[490,490],"mapped",[491]],[[491,491],"valid"],[[492,492],"mapped",[493]],[[493,493],"valid"],[[494,494],"mapped",[495]],[[495,496],"valid"],[[497,499],"mapped",[100,122]],[[500,500],"mapped",[501]],[[501,501],"valid"],[[502,502],"mapped",[405]],[[503,503],"mapped",[447]],[[504,504],"mapped",[505]],[[505,505],"valid"],[[506,506],"mapped",[507]],[[507,507],"valid"],[[508,508],"mapped",[509]],[[509,509],"valid"],[[510,510],"mapped",[511]],[[511,511],"valid"],[[512,512],"mapped",[513]],[[513,513],"valid"],[[514,514],"mapped",[515]],[[515,515],"valid"],[[516,516],"mapped",[517]],[[517,517],"valid"],[[518,518],"mapped",[519]],[[519,519],"valid"],[[520,520],"mapped",[521]],[[521,521],"valid"],[[522,522],"mapped",[523]],[[523,523],"valid"],[[524,524],"mapped",[525]],[[525,525],"valid"],[[526,526],"mapped",[527]],[[527,527],"valid"],[[528,528],"mapped",[529]],[[529,529],"valid"],[[530,530],"mapped",[531]],[[531,531],"valid"],[[532,532],"mapped",[533]],[[533,533],"valid"],[[534,534],"mapped",[535]],[[535,535],"valid"],[[536,536],"mapped",[537]],[[537,537],"valid"],[[538,538],"mapped",[539]],[[539,539],"valid"],[[540,540],"mapped",[541]],[[541,541],"valid"],[[542,542],"mapped",[543]],[[543,543],"valid"],[[544,544],"mapped",[414]],[[545,545],"valid"],[[546,546],"mapped",[547]],[[547,547],"valid"],[[548,548],"mapped",[549]],[[549,549],"valid"],[[550,550],"mapped",[551]],[[551,551],"valid"],[[552,552],"mapped",[553]],[[553,553],"valid"],[[554,554],"mapped",[555]],[[555,555],"valid"],[[556,556],"mapped",[557]],[[557,557],"valid"],[[558,558],"mapped",[559]],[[559,559],"valid"],[[560,560],"mapped",[561]],[[561,561],"valid"],[[562,562],"mapped",[563]],[[563,563],"valid"],[[564,566],"valid"],[[567,569],"valid"],[[570,570],"mapped",[11365]],[[571,571],"mapped",[572]],[[572,572],"valid"],[[573,573],"mapped",[410]],[[574,574],"mapped",[11366]],[[575,576],"valid"],[[577,577],"mapped",[578]],[[578,578],"valid"],[[579,579],"mapped",[384]],[[580,580],"mapped",[649]],[[581,581],"mapped",[652]],[[582,582],"mapped",[583]],[[583,583],"valid"],[[584,584],"mapped",[585]],[[585,585],"valid"],[[586,586],"mapped",[587]],[[587,587],"valid"],[[588,588],"mapped",[589]],[[589,589],"valid"],[[590,590],"mapped",[591]],[[591,591],"valid"],[[592,680],"valid"],[[681,685],"valid"],[[686,687],"valid"],[[688,688],"mapped",[104]],[[689,689],"mapped",[614]],[[690,690],"mapped",[106]],[[691,691],"mapped",[114]],[[692,692],"mapped",[633]],[[693,693],"mapped",[635]],[[694,694],"mapped",[641]],[[695,695],"mapped",[119]],[[696,696],"mapped",[121]],[[697,705],"valid"],[[706,709],"valid",[],"NV8"],[[710,721],"valid"],[[722,727],"valid",[],"NV8"],[[728,728],"disallowed_STD3_mapped",[32,774]],[[729,729],"disallowed_STD3_mapped",[32,775]],[[730,730],"disallowed_STD3_mapped",[32,778]],[[731,731],"disallowed_STD3_mapped",[32,808]],[[732,732],"disallowed_STD3_mapped",[32,771]],[[733,733],"disallowed_STD3_mapped",[32,779]],[[734,734],"valid",[],"NV8"],[[735,735],"valid",[],"NV8"],[[736,736],"mapped",[611]],[[737,737],"mapped",[108]],[[738,738],"mapped",[115]],[[739,739],"mapped",[120]],[[740,740],"mapped",[661]],[[741,745],"valid",[],"NV8"],[[746,747],"valid",[],"NV8"],[[748,748],"valid"],[[749,749],"valid",[],"NV8"],[[750,750],"valid"],[[751,767],"valid",[],"NV8"],[[768,831],"valid"],[[832,832],"mapped",[768]],[[833,833],"mapped",[769]],[[834,834],"valid"],[[835,835],"mapped",[787]],[[836,836],"mapped",[776,769]],[[837,837],"mapped",[953]],[[838,846],"valid"],[[847,847],"ignored"],[[848,855],"valid"],[[856,860],"valid"],[[861,863],"valid"],[[864,865],"valid"],[[866,866],"valid"],[[867,879],"valid"],[[880,880],"mapped",[881]],[[881,881],"valid"],[[882,882],"mapped",[883]],[[883,883],"valid"],[[884,884],"mapped",[697]],[[885,885],"valid"],[[886,886],"mapped",[887]],[[887,887],"valid"],[[888,889],"disallowed"],[[890,890],"disallowed_STD3_mapped",[32,953]],[[891,893],"valid"],[[894,894],"disallowed_STD3_mapped",[59]],[[895,895],"mapped",[1011]],[[896,899],"disallowed"],[[900,900],"disallowed_STD3_mapped",[32,769]],[[901,901],"disallowed_STD3_mapped",[32,776,769]],[[902,902],"mapped",[940]],[[903,903],"mapped",[183]],[[904,904],"mapped",[941]],[[905,905],"mapped",[942]],[[906,906],"mapped",[943]],[[907,907],"disallowed"],[[908,908],"mapped",[972]],[[909,909],"disallowed"],[[910,910],"mapped",[973]],[[911,911],"mapped",[974]],[[912,912],"valid"],[[913,913],"mapped",[945]],[[914,914],"mapped",[946]],[[915,915],"mapped",[947]],[[916,916],"mapped",[948]],[[917,917],"mapped",[949]],[[918,918],"mapped",[950]],[[919,919],"mapped",[951]],[[920,920],"mapped",[952]],[[921,921],"mapped",[953]],[[922,922],"mapped",[954]],[[923,923],"mapped",[955]],[[924,924],"mapped",[956]],[[925,925],"mapped",[957]],[[926,926],"mapped",[958]],[[927,927],"mapped",[959]],[[928,928],"mapped",[960]],[[929,929],"mapped",[961]],[[930,930],"disallowed"],[[931,931],"mapped",[963]],[[932,932],"mapped",[964]],[[933,933],"mapped",[965]],[[934,934],"mapped",[966]],[[935,935],"mapped",[967]],[[936,936],"mapped",[968]],[[937,937],"mapped",[969]],[[938,938],"mapped",[970]],[[939,939],"mapped",[971]],[[940,961],"valid"],[[962,962],"deviation",[963]],[[963,974],"valid"],[[975,975],"mapped",[983]],[[976,976],"mapped",[946]],[[977,977],"mapped",[952]],[[978,978],"mapped",[965]],[[979,979],"mapped",[973]],[[980,980],"mapped",[971]],[[981,981],"mapped",[966]],[[982,982],"mapped",[960]],[[983,983],"valid"],[[984,984],"mapped",[985]],[[985,985],"valid"],[[986,986],"mapped",[987]],[[987,987],"valid"],[[988,988],"mapped",[989]],[[989,989],"valid"],[[990,990],"mapped",[991]],[[991,991],"valid"],[[992,992],"mapped",[993]],[[993,993],"valid"],[[994,994],"mapped",[995]],[[995,995],"valid"],[[996,996],"mapped",[997]],[[997,997],"valid"],[[998,998],"mapped",[999]],[[999,999],"valid"],[[1e3,1e3],"mapped",[1001]],[[1001,1001],"valid"],[[1002,1002],"mapped",[1003]],[[1003,1003],"valid"],[[1004,1004],"mapped",[1005]],[[1005,1005],"valid"],[[1006,1006],"mapped",[1007]],[[1007,1007],"valid"],[[1008,1008],"mapped",[954]],[[1009,1009],"mapped",[961]],[[1010,1010],"mapped",[963]],[[1011,1011],"valid"],[[1012,1012],"mapped",[952]],[[1013,1013],"mapped",[949]],[[1014,1014],"valid",[],"NV8"],[[1015,1015],"mapped",[1016]],[[1016,1016],"valid"],[[1017,1017],"mapped",[963]],[[1018,1018],"mapped",[1019]],[[1019,1019],"valid"],[[1020,1020],"valid"],[[1021,1021],"mapped",[891]],[[1022,1022],"mapped",[892]],[[1023,1023],"mapped",[893]],[[1024,1024],"mapped",[1104]],[[1025,1025],"mapped",[1105]],[[1026,1026],"mapped",[1106]],[[1027,1027],"mapped",[1107]],[[1028,1028],"mapped",[1108]],[[1029,1029],"mapped",[1109]],[[1030,1030],"mapped",[1110]],[[1031,1031],"mapped",[1111]],[[1032,1032],"mapped",[1112]],[[1033,1033],"mapped",[1113]],[[1034,1034],"mapped",[1114]],[[1035,1035],"mapped",[1115]],[[1036,1036],"mapped",[1116]],[[1037,1037],"mapped",[1117]],[[1038,1038],"mapped",[1118]],[[1039,1039],"mapped",[1119]],[[1040,1040],"mapped",[1072]],[[1041,1041],"mapped",[1073]],[[1042,1042],"mapped",[1074]],[[1043,1043],"mapped",[1075]],[[1044,1044],"mapped",[1076]],[[1045,1045],"mapped",[1077]],[[1046,1046],"mapped",[1078]],[[1047,1047],"mapped",[1079]],[[1048,1048],"mapped",[1080]],[[1049,1049],"mapped",[1081]],[[1050,1050],"mapped",[1082]],[[1051,1051],"mapped",[1083]],[[1052,1052],"mapped",[1084]],[[1053,1053],"mapped",[1085]],[[1054,1054],"mapped",[1086]],[[1055,1055],"mapped",[1087]],[[1056,1056],"mapped",[1088]],[[1057,1057],"mapped",[1089]],[[1058,1058],"mapped",[1090]],[[1059,1059],"mapped",[1091]],[[1060,1060],"mapped",[1092]],[[1061,1061],"mapped",[1093]],[[1062,1062],"mapped",[1094]],[[1063,1063],"mapped",[1095]],[[1064,1064],"mapped",[1096]],[[1065,1065],"mapped",[1097]],[[1066,1066],"mapped",[1098]],[[1067,1067],"mapped",[1099]],[[1068,1068],"mapped",[1100]],[[1069,1069],"mapped",[1101]],[[1070,1070],"mapped",[1102]],[[1071,1071],"mapped",[1103]],[[1072,1103],"valid"],[[1104,1104],"valid"],[[1105,1116],"valid"],[[1117,1117],"valid"],[[1118,1119],"valid"],[[1120,1120],"mapped",[1121]],[[1121,1121],"valid"],[[1122,1122],"mapped",[1123]],[[1123,1123],"valid"],[[1124,1124],"mapped",[1125]],[[1125,1125],"valid"],[[1126,1126],"mapped",[1127]],[[1127,1127],"valid"],[[1128,1128],"mapped",[1129]],[[1129,1129],"valid"],[[1130,1130],"mapped",[1131]],[[1131,1131],"valid"],[[1132,1132],"mapped",[1133]],[[1133,1133],"valid"],[[1134,1134],"mapped",[1135]],[[1135,1135],"valid"],[[1136,1136],"mapped",[1137]],[[1137,1137],"valid"],[[1138,1138],"mapped",[1139]],[[1139,1139],"valid"],[[1140,1140],"mapped",[1141]],[[1141,1141],"valid"],[[1142,1142],"mapped",[1143]],[[1143,1143],"valid"],[[1144,1144],"mapped",[1145]],[[1145,1145],"valid"],[[1146,1146],"mapped",[1147]],[[1147,1147],"valid"],[[1148,1148],"mapped",[1149]],[[1149,1149],"valid"],[[1150,1150],"mapped",[1151]],[[1151,1151],"valid"],[[1152,1152],"mapped",[1153]],[[1153,1153],"valid"],[[1154,1154],"valid",[],"NV8"],[[1155,1158],"valid"],[[1159,1159],"valid"],[[1160,1161],"valid",[],"NV8"],[[1162,1162],"mapped",[1163]],[[1163,1163],"valid"],[[1164,1164],"mapped",[1165]],[[1165,1165],"valid"],[[1166,1166],"mapped",[1167]],[[1167,1167],"valid"],[[1168,1168],"mapped",[1169]],[[1169,1169],"valid"],[[1170,1170],"mapped",[1171]],[[1171,1171],"valid"],[[1172,1172],"mapped",[1173]],[[1173,1173],"valid"],[[1174,1174],"mapped",[1175]],[[1175,1175],"valid"],[[1176,1176],"mapped",[1177]],[[1177,1177],"valid"],[[1178,1178],"mapped",[1179]],[[1179,1179],"valid"],[[1180,1180],"mapped",[1181]],[[1181,1181],"valid"],[[1182,1182],"mapped",[1183]],[[1183,1183],"valid"],[[1184,1184],"mapped",[1185]],[[1185,1185],"valid"],[[1186,1186],"mapped",[1187]],[[1187,1187],"valid"],[[1188,1188],"mapped",[1189]],[[1189,1189],"valid"],[[1190,1190],"mapped",[1191]],[[1191,1191],"valid"],[[1192,1192],"mapped",[1193]],[[1193,1193],"valid"],[[1194,1194],"mapped",[1195]],[[1195,1195],"valid"],[[1196,1196],"mapped",[1197]],[[1197,1197],"valid"],[[1198,1198],"mapped",[1199]],[[1199,1199],"valid"],[[1200,1200],"mapped",[1201]],[[1201,1201],"valid"],[[1202,1202],"mapped",[1203]],[[1203,1203],"valid"],[[1204,1204],"mapped",[1205]],[[1205,1205],"valid"],[[1206,1206],"mapped",[1207]],[[1207,1207],"valid"],[[1208,1208],"mapped",[1209]],[[1209,1209],"valid"],[[1210,1210],"mapped",[1211]],[[1211,1211],"valid"],[[1212,1212],"mapped",[1213]],[[1213,1213],"valid"],[[1214,1214],"mapped",[1215]],[[1215,1215],"valid"],[[1216,1216],"disallowed"],[[1217,1217],"mapped",[1218]],[[1218,1218],"valid"],[[1219,1219],"mapped",[1220]],[[1220,1220],"valid"],[[1221,1221],"mapped",[1222]],[[1222,1222],"valid"],[[1223,1223],"mapped",[1224]],[[1224,1224],"valid"],[[1225,1225],"mapped",[1226]],[[1226,1226],"valid"],[[1227,1227],"mapped",[1228]],[[1228,1228],"valid"],[[1229,1229],"mapped",[1230]],[[1230,1230],"valid"],[[1231,1231],"valid"],[[1232,1232],"mapped",[1233]],[[1233,1233],"valid"],[[1234,1234],"mapped",[1235]],[[1235,1235],"valid"],[[1236,1236],"mapped",[1237]],[[1237,1237],"valid"],[[1238,1238],"mapped",[1239]],[[1239,1239],"valid"],[[1240,1240],"mapped",[1241]],[[1241,1241],"valid"],[[1242,1242],"mapped",[1243]],[[1243,1243],"valid"],[[1244,1244],"mapped",[1245]],[[1245,1245],"valid"],[[1246,1246],"mapped",[1247]],[[1247,1247],"valid"],[[1248,1248],"mapped",[1249]],[[1249,1249],"valid"],[[1250,1250],"mapped",[1251]],[[1251,1251],"valid"],[[1252,1252],"mapped",[1253]],[[1253,1253],"valid"],[[1254,1254],"mapped",[1255]],[[1255,1255],"valid"],[[1256,1256],"mapped",[1257]],[[1257,1257],"valid"],[[1258,1258],"mapped",[1259]],[[1259,1259],"valid"],[[1260,1260],"mapped",[1261]],[[1261,1261],"valid"],[[1262,1262],"mapped",[1263]],[[1263,1263],"valid"],[[1264,1264],"mapped",[1265]],[[1265,1265],"valid"],[[1266,1266],"mapped",[1267]],[[1267,1267],"valid"],[[1268,1268],"mapped",[1269]],[[1269,1269],"valid"],[[1270,1270],"mapped",[1271]],[[1271,1271],"valid"],[[1272,1272],"mapped",[1273]],[[1273,1273],"valid"],[[1274,1274],"mapped",[1275]],[[1275,1275],"valid"],[[1276,1276],"mapped",[1277]],[[1277,1277],"valid"],[[1278,1278],"mapped",[1279]],[[1279,1279],"valid"],[[1280,1280],"mapped",[1281]],[[1281,1281],"valid"],[[1282,1282],"mapped",[1283]],[[1283,1283],"valid"],[[1284,1284],"mapped",[1285]],[[1285,1285],"valid"],[[1286,1286],"mapped",[1287]],[[1287,1287],"valid"],[[1288,1288],"mapped",[1289]],[[1289,1289],"valid"],[[1290,1290],"mapped",[1291]],[[1291,1291],"valid"],[[1292,1292],"mapped",[1293]],[[1293,1293],"valid"],[[1294,1294],"mapped",[1295]],[[1295,1295],"valid"],[[1296,1296],"mapped",[1297]],[[1297,1297],"valid"],[[1298,1298],"mapped",[1299]],[[1299,1299],"valid"],[[1300,1300],"mapped",[1301]],[[1301,1301],"valid"],[[1302,1302],"mapped",[1303]],[[1303,1303],"valid"],[[1304,1304],"mapped",[1305]],[[1305,1305],"valid"],[[1306,1306],"mapped",[1307]],[[1307,1307],"valid"],[[1308,1308],"mapped",[1309]],[[1309,1309],"valid"],[[1310,1310],"mapped",[1311]],[[1311,1311],"valid"],[[1312,1312],"mapped",[1313]],[[1313,1313],"valid"],[[1314,1314],"mapped",[1315]],[[1315,1315],"valid"],[[1316,1316],"mapped",[1317]],[[1317,1317],"valid"],[[1318,1318],"mapped",[1319]],[[1319,1319],"valid"],[[1320,1320],"mapped",[1321]],[[1321,1321],"valid"],[[1322,1322],"mapped",[1323]],[[1323,1323],"valid"],[[1324,1324],"mapped",[1325]],[[1325,1325],"valid"],[[1326,1326],"mapped",[1327]],[[1327,1327],"valid"],[[1328,1328],"disallowed"],[[1329,1329],"mapped",[1377]],[[1330,1330],"mapped",[1378]],[[1331,1331],"mapped",[1379]],[[1332,1332],"mapped",[1380]],[[1333,1333],"mapped",[1381]],[[1334,1334],"mapped",[1382]],[[1335,1335],"mapped",[1383]],[[1336,1336],"mapped",[1384]],[[1337,1337],"mapped",[1385]],[[1338,1338],"mapped",[1386]],[[1339,1339],"mapped",[1387]],[[1340,1340],"mapped",[1388]],[[1341,1341],"mapped",[1389]],[[1342,1342],"mapped",[1390]],[[1343,1343],"mapped",[1391]],[[1344,1344],"mapped",[1392]],[[1345,1345],"mapped",[1393]],[[1346,1346],"mapped",[1394]],[[1347,1347],"mapped",[1395]],[[1348,1348],"mapped",[1396]],[[1349,1349],"mapped",[1397]],[[1350,1350],"mapped",[1398]],[[1351,1351],"mapped",[1399]],[[1352,1352],"mapped",[1400]],[[1353,1353],"mapped",[1401]],[[1354,1354],"mapped",[1402]],[[1355,1355],"mapped",[1403]],[[1356,1356],"mapped",[1404]],[[1357,1357],"mapped",[1405]],[[1358,1358],"mapped",[1406]],[[1359,1359],"mapped",[1407]],[[1360,1360],"mapped",[1408]],[[1361,1361],"mapped",[1409]],[[1362,1362],"mapped",[1410]],[[1363,1363],"mapped",[1411]],[[1364,1364],"mapped",[1412]],[[1365,1365],"mapped",[1413]],[[1366,1366],"mapped",[1414]],[[1367,1368],"disallowed"],[[1369,1369],"valid"],[[1370,1375],"valid",[],"NV8"],[[1376,1376],"disallowed"],[[1377,1414],"valid"],[[1415,1415],"mapped",[1381,1410]],[[1416,1416],"disallowed"],[[1417,1417],"valid",[],"NV8"],[[1418,1418],"valid",[],"NV8"],[[1419,1420],"disallowed"],[[1421,1422],"valid",[],"NV8"],[[1423,1423],"valid",[],"NV8"],[[1424,1424],"disallowed"],[[1425,1441],"valid"],[[1442,1442],"valid"],[[1443,1455],"valid"],[[1456,1465],"valid"],[[1466,1466],"valid"],[[1467,1469],"valid"],[[1470,1470],"valid",[],"NV8"],[[1471,1471],"valid"],[[1472,1472],"valid",[],"NV8"],[[1473,1474],"valid"],[[1475,1475],"valid",[],"NV8"],[[1476,1476],"valid"],[[1477,1477],"valid"],[[1478,1478],"valid",[],"NV8"],[[1479,1479],"valid"],[[1480,1487],"disallowed"],[[1488,1514],"valid"],[[1515,1519],"disallowed"],[[1520,1524],"valid"],[[1525,1535],"disallowed"],[[1536,1539],"disallowed"],[[1540,1540],"disallowed"],[[1541,1541],"disallowed"],[[1542,1546],"valid",[],"NV8"],[[1547,1547],"valid",[],"NV8"],[[1548,1548],"valid",[],"NV8"],[[1549,1551],"valid",[],"NV8"],[[1552,1557],"valid"],[[1558,1562],"valid"],[[1563,1563],"valid",[],"NV8"],[[1564,1564],"disallowed"],[[1565,1565],"disallowed"],[[1566,1566],"valid",[],"NV8"],[[1567,1567],"valid",[],"NV8"],[[1568,1568],"valid"],[[1569,1594],"valid"],[[1595,1599],"valid"],[[1600,1600],"valid",[],"NV8"],[[1601,1618],"valid"],[[1619,1621],"valid"],[[1622,1624],"valid"],[[1625,1630],"valid"],[[1631,1631],"valid"],[[1632,1641],"valid"],[[1642,1645],"valid",[],"NV8"],[[1646,1647],"valid"],[[1648,1652],"valid"],[[1653,1653],"mapped",[1575,1652]],[[1654,1654],"mapped",[1608,1652]],[[1655,1655],"mapped",[1735,1652]],[[1656,1656],"mapped",[1610,1652]],[[1657,1719],"valid"],[[1720,1721],"valid"],[[1722,1726],"valid"],[[1727,1727],"valid"],[[1728,1742],"valid"],[[1743,1743],"valid"],[[1744,1747],"valid"],[[1748,1748],"valid",[],"NV8"],[[1749,1756],"valid"],[[1757,1757],"disallowed"],[[1758,1758],"valid",[],"NV8"],[[1759,1768],"valid"],[[1769,1769],"valid",[],"NV8"],[[1770,1773],"valid"],[[1774,1775],"valid"],[[1776,1785],"valid"],[[1786,1790],"valid"],[[1791,1791],"valid"],[[1792,1805],"valid",[],"NV8"],[[1806,1806],"disallowed"],[[1807,1807],"disallowed"],[[1808,1836],"valid"],[[1837,1839],"valid"],[[1840,1866],"valid"],[[1867,1868],"disallowed"],[[1869,1871],"valid"],[[1872,1901],"valid"],[[1902,1919],"valid"],[[1920,1968],"valid"],[[1969,1969],"valid"],[[1970,1983],"disallowed"],[[1984,2037],"valid"],[[2038,2042],"valid",[],"NV8"],[[2043,2047],"disallowed"],[[2048,2093],"valid"],[[2094,2095],"disallowed"],[[2096,2110],"valid",[],"NV8"],[[2111,2111],"disallowed"],[[2112,2139],"valid"],[[2140,2141],"disallowed"],[[2142,2142],"valid",[],"NV8"],[[2143,2207],"disallowed"],[[2208,2208],"valid"],[[2209,2209],"valid"],[[2210,2220],"valid"],[[2221,2226],"valid"],[[2227,2228],"valid"],[[2229,2274],"disallowed"],[[2275,2275],"valid"],[[2276,2302],"valid"],[[2303,2303],"valid"],[[2304,2304],"valid"],[[2305,2307],"valid"],[[2308,2308],"valid"],[[2309,2361],"valid"],[[2362,2363],"valid"],[[2364,2381],"valid"],[[2382,2382],"valid"],[[2383,2383],"valid"],[[2384,2388],"valid"],[[2389,2389],"valid"],[[2390,2391],"valid"],[[2392,2392],"mapped",[2325,2364]],[[2393,2393],"mapped",[2326,2364]],[[2394,2394],"mapped",[2327,2364]],[[2395,2395],"mapped",[2332,2364]],[[2396,2396],"mapped",[2337,2364]],[[2397,2397],"mapped",[2338,2364]],[[2398,2398],"mapped",[2347,2364]],[[2399,2399],"mapped",[2351,2364]],[[2400,2403],"valid"],[[2404,2405],"valid",[],"NV8"],[[2406,2415],"valid"],[[2416,2416],"valid",[],"NV8"],[[2417,2418],"valid"],[[2419,2423],"valid"],[[2424,2424],"valid"],[[2425,2426],"valid"],[[2427,2428],"valid"],[[2429,2429],"valid"],[[2430,2431],"valid"],[[2432,2432],"valid"],[[2433,2435],"valid"],[[2436,2436],"disallowed"],[[2437,2444],"valid"],[[2445,2446],"disallowed"],[[2447,2448],"valid"],[[2449,2450],"disallowed"],[[2451,2472],"valid"],[[2473,2473],"disallowed"],[[2474,2480],"valid"],[[2481,2481],"disallowed"],[[2482,2482],"valid"],[[2483,2485],"disallowed"],[[2486,2489],"valid"],[[2490,2491],"disallowed"],[[2492,2492],"valid"],[[2493,2493],"valid"],[[2494,2500],"valid"],[[2501,2502],"disallowed"],[[2503,2504],"valid"],[[2505,2506],"disallowed"],[[2507,2509],"valid"],[[2510,2510],"valid"],[[2511,2518],"disallowed"],[[2519,2519],"valid"],[[2520,2523],"disallowed"],[[2524,2524],"mapped",[2465,2492]],[[2525,2525],"mapped",[2466,2492]],[[2526,2526],"disallowed"],[[2527,2527],"mapped",[2479,2492]],[[2528,2531],"valid"],[[2532,2533],"disallowed"],[[2534,2545],"valid"],[[2546,2554],"valid",[],"NV8"],[[2555,2555],"valid",[],"NV8"],[[2556,2560],"disallowed"],[[2561,2561],"valid"],[[2562,2562],"valid"],[[2563,2563],"valid"],[[2564,2564],"disallowed"],[[2565,2570],"valid"],[[2571,2574],"disallowed"],[[2575,2576],"valid"],[[2577,2578],"disallowed"],[[2579,2600],"valid"],[[2601,2601],"disallowed"],[[2602,2608],"valid"],[[2609,2609],"disallowed"],[[2610,2610],"valid"],[[2611,2611],"mapped",[2610,2620]],[[2612,2612],"disallowed"],[[2613,2613],"valid"],[[2614,2614],"mapped",[2616,2620]],[[2615,2615],"disallowed"],[[2616,2617],"valid"],[[2618,2619],"disallowed"],[[2620,2620],"valid"],[[2621,2621],"disallowed"],[[2622,2626],"valid"],[[2627,2630],"disallowed"],[[2631,2632],"valid"],[[2633,2634],"disallowed"],[[2635,2637],"valid"],[[2638,2640],"disallowed"],[[2641,2641],"valid"],[[2642,2648],"disallowed"],[[2649,2649],"mapped",[2582,2620]],[[2650,2650],"mapped",[2583,2620]],[[2651,2651],"mapped",[2588,2620]],[[2652,2652],"valid"],[[2653,2653],"disallowed"],[[2654,2654],"mapped",[2603,2620]],[[2655,2661],"disallowed"],[[2662,2676],"valid"],[[2677,2677],"valid"],[[2678,2688],"disallowed"],[[2689,2691],"valid"],[[2692,2692],"disallowed"],[[2693,2699],"valid"],[[2700,2700],"valid"],[[2701,2701],"valid"],[[2702,2702],"disallowed"],[[2703,2705],"valid"],[[2706,2706],"disallowed"],[[2707,2728],"valid"],[[2729,2729],"disallowed"],[[2730,2736],"valid"],[[2737,2737],"disallowed"],[[2738,2739],"valid"],[[2740,2740],"disallowed"],[[2741,2745],"valid"],[[2746,2747],"disallowed"],[[2748,2757],"valid"],[[2758,2758],"disallowed"],[[2759,2761],"valid"],[[2762,2762],"disallowed"],[[2763,2765],"valid"],[[2766,2767],"disallowed"],[[2768,2768],"valid"],[[2769,2783],"disallowed"],[[2784,2784],"valid"],[[2785,2787],"valid"],[[2788,2789],"disallowed"],[[2790,2799],"valid"],[[2800,2800],"valid",[],"NV8"],[[2801,2801],"valid",[],"NV8"],[[2802,2808],"disallowed"],[[2809,2809],"valid"],[[2810,2816],"disallowed"],[[2817,2819],"valid"],[[2820,2820],"disallowed"],[[2821,2828],"valid"],[[2829,2830],"disallowed"],[[2831,2832],"valid"],[[2833,2834],"disallowed"],[[2835,2856],"valid"],[[2857,2857],"disallowed"],[[2858,2864],"valid"],[[2865,2865],"disallowed"],[[2866,2867],"valid"],[[2868,2868],"disallowed"],[[2869,2869],"valid"],[[2870,2873],"valid"],[[2874,2875],"disallowed"],[[2876,2883],"valid"],[[2884,2884],"valid"],[[2885,2886],"disallowed"],[[2887,2888],"valid"],[[2889,2890],"disallowed"],[[2891,2893],"valid"],[[2894,2901],"disallowed"],[[2902,2903],"valid"],[[2904,2907],"disallowed"],[[2908,2908],"mapped",[2849,2876]],[[2909,2909],"mapped",[2850,2876]],[[2910,2910],"disallowed"],[[2911,2913],"valid"],[[2914,2915],"valid"],[[2916,2917],"disallowed"],[[2918,2927],"valid"],[[2928,2928],"valid",[],"NV8"],[[2929,2929],"valid"],[[2930,2935],"valid",[],"NV8"],[[2936,2945],"disallowed"],[[2946,2947],"valid"],[[2948,2948],"disallowed"],[[2949,2954],"valid"],[[2955,2957],"disallowed"],[[2958,2960],"valid"],[[2961,2961],"disallowed"],[[2962,2965],"valid"],[[2966,2968],"disallowed"],[[2969,2970],"valid"],[[2971,2971],"disallowed"],[[2972,2972],"valid"],[[2973,2973],"disallowed"],[[2974,2975],"valid"],[[2976,2978],"disallowed"],[[2979,2980],"valid"],[[2981,2983],"disallowed"],[[2984,2986],"valid"],[[2987,2989],"disallowed"],[[2990,2997],"valid"],[[2998,2998],"valid"],[[2999,3001],"valid"],[[3002,3005],"disallowed"],[[3006,3010],"valid"],[[3011,3013],"disallowed"],[[3014,3016],"valid"],[[3017,3017],"disallowed"],[[3018,3021],"valid"],[[3022,3023],"disallowed"],[[3024,3024],"valid"],[[3025,3030],"disallowed"],[[3031,3031],"valid"],[[3032,3045],"disallowed"],[[3046,3046],"valid"],[[3047,3055],"valid"],[[3056,3058],"valid",[],"NV8"],[[3059,3066],"valid",[],"NV8"],[[3067,3071],"disallowed"],[[3072,3072],"valid"],[[3073,3075],"valid"],[[3076,3076],"disallowed"],[[3077,3084],"valid"],[[3085,3085],"disallowed"],[[3086,3088],"valid"],[[3089,3089],"disallowed"],[[3090,3112],"valid"],[[3113,3113],"disallowed"],[[3114,3123],"valid"],[[3124,3124],"valid"],[[3125,3129],"valid"],[[3130,3132],"disallowed"],[[3133,3133],"valid"],[[3134,3140],"valid"],[[3141,3141],"disallowed"],[[3142,3144],"valid"],[[3145,3145],"disallowed"],[[3146,3149],"valid"],[[3150,3156],"disallowed"],[[3157,3158],"valid"],[[3159,3159],"disallowed"],[[3160,3161],"valid"],[[3162,3162],"valid"],[[3163,3167],"disallowed"],[[3168,3169],"valid"],[[3170,3171],"valid"],[[3172,3173],"disallowed"],[[3174,3183],"valid"],[[3184,3191],"disallowed"],[[3192,3199],"valid",[],"NV8"],[[3200,3200],"disallowed"],[[3201,3201],"valid"],[[3202,3203],"valid"],[[3204,3204],"disallowed"],[[3205,3212],"valid"],[[3213,3213],"disallowed"],[[3214,3216],"valid"],[[3217,3217],"disallowed"],[[3218,3240],"valid"],[[3241,3241],"disallowed"],[[3242,3251],"valid"],[[3252,3252],"disallowed"],[[3253,3257],"valid"],[[3258,3259],"disallowed"],[[3260,3261],"valid"],[[3262,3268],"valid"],[[3269,3269],"disallowed"],[[3270,3272],"valid"],[[3273,3273],"disallowed"],[[3274,3277],"valid"],[[3278,3284],"disallowed"],[[3285,3286],"valid"],[[3287,3293],"disallowed"],[[3294,3294],"valid"],[[3295,3295],"disallowed"],[[3296,3297],"valid"],[[3298,3299],"valid"],[[3300,3301],"disallowed"],[[3302,3311],"valid"],[[3312,3312],"disallowed"],[[3313,3314],"valid"],[[3315,3328],"disallowed"],[[3329,3329],"valid"],[[3330,3331],"valid"],[[3332,3332],"disallowed"],[[3333,3340],"valid"],[[3341,3341],"disallowed"],[[3342,3344],"valid"],[[3345,3345],"disallowed"],[[3346,3368],"valid"],[[3369,3369],"valid"],[[3370,3385],"valid"],[[3386,3386],"valid"],[[3387,3388],"disallowed"],[[3389,3389],"valid"],[[3390,3395],"valid"],[[3396,3396],"valid"],[[3397,3397],"disallowed"],[[3398,3400],"valid"],[[3401,3401],"disallowed"],[[3402,3405],"valid"],[[3406,3406],"valid"],[[3407,3414],"disallowed"],[[3415,3415],"valid"],[[3416,3422],"disallowed"],[[3423,3423],"valid"],[[3424,3425],"valid"],[[3426,3427],"valid"],[[3428,3429],"disallowed"],[[3430,3439],"valid"],[[3440,3445],"valid",[],"NV8"],[[3446,3448],"disallowed"],[[3449,3449],"valid",[],"NV8"],[[3450,3455],"valid"],[[3456,3457],"disallowed"],[[3458,3459],"valid"],[[3460,3460],"disallowed"],[[3461,3478],"valid"],[[3479,3481],"disallowed"],[[3482,3505],"valid"],[[3506,3506],"disallowed"],[[3507,3515],"valid"],[[3516,3516],"disallowed"],[[3517,3517],"valid"],[[3518,3519],"disallowed"],[[3520,3526],"valid"],[[3527,3529],"disallowed"],[[3530,3530],"valid"],[[3531,3534],"disallowed"],[[3535,3540],"valid"],[[3541,3541],"disallowed"],[[3542,3542],"valid"],[[3543,3543],"disallowed"],[[3544,3551],"valid"],[[3552,3557],"disallowed"],[[3558,3567],"valid"],[[3568,3569],"disallowed"],[[3570,3571],"valid"],[[3572,3572],"valid",[],"NV8"],[[3573,3584],"disallowed"],[[3585,3634],"valid"],[[3635,3635],"mapped",[3661,3634]],[[3636,3642],"valid"],[[3643,3646],"disallowed"],[[3647,3647],"valid",[],"NV8"],[[3648,3662],"valid"],[[3663,3663],"valid",[],"NV8"],[[3664,3673],"valid"],[[3674,3675],"valid",[],"NV8"],[[3676,3712],"disallowed"],[[3713,3714],"valid"],[[3715,3715],"disallowed"],[[3716,3716],"valid"],[[3717,3718],"disallowed"],[[3719,3720],"valid"],[[3721,3721],"disallowed"],[[3722,3722],"valid"],[[3723,3724],"disallowed"],[[3725,3725],"valid"],[[3726,3731],"disallowed"],[[3732,3735],"valid"],[[3736,3736],"disallowed"],[[3737,3743],"valid"],[[3744,3744],"disallowed"],[[3745,3747],"valid"],[[3748,3748],"disallowed"],[[3749,3749],"valid"],[[3750,3750],"disallowed"],[[3751,3751],"valid"],[[3752,3753],"disallowed"],[[3754,3755],"valid"],[[3756,3756],"disallowed"],[[3757,3762],"valid"],[[3763,3763],"mapped",[3789,3762]],[[3764,3769],"valid"],[[3770,3770],"disallowed"],[[3771,3773],"valid"],[[3774,3775],"disallowed"],[[3776,3780],"valid"],[[3781,3781],"disallowed"],[[3782,3782],"valid"],[[3783,3783],"disallowed"],[[3784,3789],"valid"],[[3790,3791],"disallowed"],[[3792,3801],"valid"],[[3802,3803],"disallowed"],[[3804,3804],"mapped",[3755,3737]],[[3805,3805],"mapped",[3755,3745]],[[3806,3807],"valid"],[[3808,3839],"disallowed"],[[3840,3840],"valid"],[[3841,3850],"valid",[],"NV8"],[[3851,3851],"valid"],[[3852,3852],"mapped",[3851]],[[3853,3863],"valid",[],"NV8"],[[3864,3865],"valid"],[[3866,3871],"valid",[],"NV8"],[[3872,3881],"valid"],[[3882,3892],"valid",[],"NV8"],[[3893,3893],"valid"],[[3894,3894],"valid",[],"NV8"],[[3895,3895],"valid"],[[3896,3896],"valid",[],"NV8"],[[3897,3897],"valid"],[[3898,3901],"valid",[],"NV8"],[[3902,3906],"valid"],[[3907,3907],"mapped",[3906,4023]],[[3908,3911],"valid"],[[3912,3912],"disallowed"],[[3913,3916],"valid"],[[3917,3917],"mapped",[3916,4023]],[[3918,3921],"valid"],[[3922,3922],"mapped",[3921,4023]],[[3923,3926],"valid"],[[3927,3927],"mapped",[3926,4023]],[[3928,3931],"valid"],[[3932,3932],"mapped",[3931,4023]],[[3933,3944],"valid"],[[3945,3945],"mapped",[3904,4021]],[[3946,3946],"valid"],[[3947,3948],"valid"],[[3949,3952],"disallowed"],[[3953,3954],"valid"],[[3955,3955],"mapped",[3953,3954]],[[3956,3956],"valid"],[[3957,3957],"mapped",[3953,3956]],[[3958,3958],"mapped",[4018,3968]],[[3959,3959],"mapped",[4018,3953,3968]],[[3960,3960],"mapped",[4019,3968]],[[3961,3961],"mapped",[4019,3953,3968]],[[3962,3968],"valid"],[[3969,3969],"mapped",[3953,3968]],[[3970,3972],"valid"],[[3973,3973],"valid",[],"NV8"],[[3974,3979],"valid"],[[3980,3983],"valid"],[[3984,3986],"valid"],[[3987,3987],"mapped",[3986,4023]],[[3988,3989],"valid"],[[3990,3990],"valid"],[[3991,3991],"valid"],[[3992,3992],"disallowed"],[[3993,3996],"valid"],[[3997,3997],"mapped",[3996,4023]],[[3998,4001],"valid"],[[4002,4002],"mapped",[4001,4023]],[[4003,4006],"valid"],[[4007,4007],"mapped",[4006,4023]],[[4008,4011],"valid"],[[4012,4012],"mapped",[4011,4023]],[[4013,4013],"valid"],[[4014,4016],"valid"],[[4017,4023],"valid"],[[4024,4024],"valid"],[[4025,4025],"mapped",[3984,4021]],[[4026,4028],"valid"],[[4029,4029],"disallowed"],[[4030,4037],"valid",[],"NV8"],[[4038,4038],"valid"],[[4039,4044],"valid",[],"NV8"],[[4045,4045],"disallowed"],[[4046,4046],"valid",[],"NV8"],[[4047,4047],"valid",[],"NV8"],[[4048,4049],"valid",[],"NV8"],[[4050,4052],"valid",[],"NV8"],[[4053,4056],"valid",[],"NV8"],[[4057,4058],"valid",[],"NV8"],[[4059,4095],"disallowed"],[[4096,4129],"valid"],[[4130,4130],"valid"],[[4131,4135],"valid"],[[4136,4136],"valid"],[[4137,4138],"valid"],[[4139,4139],"valid"],[[4140,4146],"valid"],[[4147,4149],"valid"],[[4150,4153],"valid"],[[4154,4159],"valid"],[[4160,4169],"valid"],[[4170,4175],"valid",[],"NV8"],[[4176,4185],"valid"],[[4186,4249],"valid"],[[4250,4253],"valid"],[[4254,4255],"valid",[],"NV8"],[[4256,4293],"disallowed"],[[4294,4294],"disallowed"],[[4295,4295],"mapped",[11559]],[[4296,4300],"disallowed"],[[4301,4301],"mapped",[11565]],[[4302,4303],"disallowed"],[[4304,4342],"valid"],[[4343,4344],"valid"],[[4345,4346],"valid"],[[4347,4347],"valid",[],"NV8"],[[4348,4348],"mapped",[4316]],[[4349,4351],"valid"],[[4352,4441],"valid",[],"NV8"],[[4442,4446],"valid",[],"NV8"],[[4447,4448],"disallowed"],[[4449,4514],"valid",[],"NV8"],[[4515,4519],"valid",[],"NV8"],[[4520,4601],"valid",[],"NV8"],[[4602,4607],"valid",[],"NV8"],[[4608,4614],"valid"],[[4615,4615],"valid"],[[4616,4678],"valid"],[[4679,4679],"valid"],[[4680,4680],"valid"],[[4681,4681],"disallowed"],[[4682,4685],"valid"],[[4686,4687],"disallowed"],[[4688,4694],"valid"],[[4695,4695],"disallowed"],[[4696,4696],"valid"],[[4697,4697],"disallowed"],[[4698,4701],"valid"],[[4702,4703],"disallowed"],[[4704,4742],"valid"],[[4743,4743],"valid"],[[4744,4744],"valid"],[[4745,4745],"disallowed"],[[4746,4749],"valid"],[[4750,4751],"disallowed"],[[4752,4782],"valid"],[[4783,4783],"valid"],[[4784,4784],"valid"],[[4785,4785],"disallowed"],[[4786,4789],"valid"],[[4790,4791],"disallowed"],[[4792,4798],"valid"],[[4799,4799],"disallowed"],[[4800,4800],"valid"],[[4801,4801],"disallowed"],[[4802,4805],"valid"],[[4806,4807],"disallowed"],[[4808,4814],"valid"],[[4815,4815],"valid"],[[4816,4822],"valid"],[[4823,4823],"disallowed"],[[4824,4846],"valid"],[[4847,4847],"valid"],[[4848,4878],"valid"],[[4879,4879],"valid"],[[4880,4880],"valid"],[[4881,4881],"disallowed"],[[4882,4885],"valid"],[[4886,4887],"disallowed"],[[4888,4894],"valid"],[[4895,4895],"valid"],[[4896,4934],"valid"],[[4935,4935],"valid"],[[4936,4954],"valid"],[[4955,4956],"disallowed"],[[4957,4958],"valid"],[[4959,4959],"valid"],[[4960,4960],"valid",[],"NV8"],[[4961,4988],"valid",[],"NV8"],[[4989,4991],"disallowed"],[[4992,5007],"valid"],[[5008,5017],"valid",[],"NV8"],[[5018,5023],"disallowed"],[[5024,5108],"valid"],[[5109,5109],"valid"],[[5110,5111],"disallowed"],[[5112,5112],"mapped",[5104]],[[5113,5113],"mapped",[5105]],[[5114,5114],"mapped",[5106]],[[5115,5115],"mapped",[5107]],[[5116,5116],"mapped",[5108]],[[5117,5117],"mapped",[5109]],[[5118,5119],"disallowed"],[[5120,5120],"valid",[],"NV8"],[[5121,5740],"valid"],[[5741,5742],"valid",[],"NV8"],[[5743,5750],"valid"],[[5751,5759],"valid"],[[5760,5760],"disallowed"],[[5761,5786],"valid"],[[5787,5788],"valid",[],"NV8"],[[5789,5791],"disallowed"],[[5792,5866],"valid"],[[5867,5872],"valid",[],"NV8"],[[5873,5880],"valid"],[[5881,5887],"disallowed"],[[5888,5900],"valid"],[[5901,5901],"disallowed"],[[5902,5908],"valid"],[[5909,5919],"disallowed"],[[5920,5940],"valid"],[[5941,5942],"valid",[],"NV8"],[[5943,5951],"disallowed"],[[5952,5971],"valid"],[[5972,5983],"disallowed"],[[5984,5996],"valid"],[[5997,5997],"disallowed"],[[5998,6e3],"valid"],[[6001,6001],"disallowed"],[[6002,6003],"valid"],[[6004,6015],"disallowed"],[[6016,6067],"valid"],[[6068,6069],"disallowed"],[[6070,6099],"valid"],[[6100,6102],"valid",[],"NV8"],[[6103,6103],"valid"],[[6104,6107],"valid",[],"NV8"],[[6108,6108],"valid"],[[6109,6109],"valid"],[[6110,6111],"disallowed"],[[6112,6121],"valid"],[[6122,6127],"disallowed"],[[6128,6137],"valid",[],"NV8"],[[6138,6143],"disallowed"],[[6144,6149],"valid",[],"NV8"],[[6150,6150],"disallowed"],[[6151,6154],"valid",[],"NV8"],[[6155,6157],"ignored"],[[6158,6158],"disallowed"],[[6159,6159],"disallowed"],[[6160,6169],"valid"],[[6170,6175],"disallowed"],[[6176,6263],"valid"],[[6264,6271],"disallowed"],[[6272,6313],"valid"],[[6314,6314],"valid"],[[6315,6319],"disallowed"],[[6320,6389],"valid"],[[6390,6399],"disallowed"],[[6400,6428],"valid"],[[6429,6430],"valid"],[[6431,6431],"disallowed"],[[6432,6443],"valid"],[[6444,6447],"disallowed"],[[6448,6459],"valid"],[[6460,6463],"disallowed"],[[6464,6464],"valid",[],"NV8"],[[6465,6467],"disallowed"],[[6468,6469],"valid",[],"NV8"],[[6470,6509],"valid"],[[6510,6511],"disallowed"],[[6512,6516],"valid"],[[6517,6527],"disallowed"],[[6528,6569],"valid"],[[6570,6571],"valid"],[[6572,6575],"disallowed"],[[6576,6601],"valid"],[[6602,6607],"disallowed"],[[6608,6617],"valid"],[[6618,6618],"valid",[],"XV8"],[[6619,6621],"disallowed"],[[6622,6623],"valid",[],"NV8"],[[6624,6655],"valid",[],"NV8"],[[6656,6683],"valid"],[[6684,6685],"disallowed"],[[6686,6687],"valid",[],"NV8"],[[6688,6750],"valid"],[[6751,6751],"disallowed"],[[6752,6780],"valid"],[[6781,6782],"disallowed"],[[6783,6793],"valid"],[[6794,6799],"disallowed"],[[6800,6809],"valid"],[[6810,6815],"disallowed"],[[6816,6822],"valid",[],"NV8"],[[6823,6823],"valid"],[[6824,6829],"valid",[],"NV8"],[[6830,6831],"disallowed"],[[6832,6845],"valid"],[[6846,6846],"valid",[],"NV8"],[[6847,6911],"disallowed"],[[6912,6987],"valid"],[[6988,6991],"disallowed"],[[6992,7001],"valid"],[[7002,7018],"valid",[],"NV8"],[[7019,7027],"valid"],[[7028,7036],"valid",[],"NV8"],[[7037,7039],"disallowed"],[[7040,7082],"valid"],[[7083,7085],"valid"],[[7086,7097],"valid"],[[7098,7103],"valid"],[[7104,7155],"valid"],[[7156,7163],"disallowed"],[[7164,7167],"valid",[],"NV8"],[[7168,7223],"valid"],[[7224,7226],"disallowed"],[[7227,7231],"valid",[],"NV8"],[[7232,7241],"valid"],[[7242,7244],"disallowed"],[[7245,7293],"valid"],[[7294,7295],"valid",[],"NV8"],[[7296,7359],"disallowed"],[[7360,7367],"valid",[],"NV8"],[[7368,7375],"disallowed"],[[7376,7378],"valid"],[[7379,7379],"valid",[],"NV8"],[[7380,7410],"valid"],[[7411,7414],"valid"],[[7415,7415],"disallowed"],[[7416,7417],"valid"],[[7418,7423],"disallowed"],[[7424,7467],"valid"],[[7468,7468],"mapped",[97]],[[7469,7469],"mapped",[230]],[[7470,7470],"mapped",[98]],[[7471,7471],"valid"],[[7472,7472],"mapped",[100]],[[7473,7473],"mapped",[101]],[[7474,7474],"mapped",[477]],[[7475,7475],"mapped",[103]],[[7476,7476],"mapped",[104]],[[7477,7477],"mapped",[105]],[[7478,7478],"mapped",[106]],[[7479,7479],"mapped",[107]],[[7480,7480],"mapped",[108]],[[7481,7481],"mapped",[109]],[[7482,7482],"mapped",[110]],[[7483,7483],"valid"],[[7484,7484],"mapped",[111]],[[7485,7485],"mapped",[547]],[[7486,7486],"mapped",[112]],[[7487,7487],"mapped",[114]],[[7488,7488],"mapped",[116]],[[7489,7489],"mapped",[117]],[[7490,7490],"mapped",[119]],[[7491,7491],"mapped",[97]],[[7492,7492],"mapped",[592]],[[7493,7493],"mapped",[593]],[[7494,7494],"mapped",[7426]],[[7495,7495],"mapped",[98]],[[7496,7496],"mapped",[100]],[[7497,7497],"mapped",[101]],[[7498,7498],"mapped",[601]],[[7499,7499],"mapped",[603]],[[7500,7500],"mapped",[604]],[[7501,7501],"mapped",[103]],[[7502,7502],"valid"],[[7503,7503],"mapped",[107]],[[7504,7504],"mapped",[109]],[[7505,7505],"mapped",[331]],[[7506,7506],"mapped",[111]],[[7507,7507],"mapped",[596]],[[7508,7508],"mapped",[7446]],[[7509,7509],"mapped",[7447]],[[7510,7510],"mapped",[112]],[[7511,7511],"mapped",[116]],[[7512,7512],"mapped",[117]],[[7513,7513],"mapped",[7453]],[[7514,7514],"mapped",[623]],[[7515,7515],"mapped",[118]],[[7516,7516],"mapped",[7461]],[[7517,7517],"mapped",[946]],[[7518,7518],"mapped",[947]],[[7519,7519],"mapped",[948]],[[7520,7520],"mapped",[966]],[[7521,7521],"mapped",[967]],[[7522,7522],"mapped",[105]],[[7523,7523],"mapped",[114]],[[7524,7524],"mapped",[117]],[[7525,7525],"mapped",[118]],[[7526,7526],"mapped",[946]],[[7527,7527],"mapped",[947]],[[7528,7528],"mapped",[961]],[[7529,7529],"mapped",[966]],[[7530,7530],"mapped",[967]],[[7531,7531],"valid"],[[7532,7543],"valid"],[[7544,7544],"mapped",[1085]],[[7545,7578],"valid"],[[7579,7579],"mapped",[594]],[[7580,7580],"mapped",[99]],[[7581,7581],"mapped",[597]],[[7582,7582],"mapped",[240]],[[7583,7583],"mapped",[604]],[[7584,7584],"mapped",[102]],[[7585,7585],"mapped",[607]],[[7586,7586],"mapped",[609]],[[7587,7587],"mapped",[613]],[[7588,7588],"mapped",[616]],[[7589,7589],"mapped",[617]],[[7590,7590],"mapped",[618]],[[7591,7591],"mapped",[7547]],[[7592,7592],"mapped",[669]],[[7593,7593],"mapped",[621]],[[7594,7594],"mapped",[7557]],[[7595,7595],"mapped",[671]],[[7596,7596],"mapped",[625]],[[7597,7597],"mapped",[624]],[[7598,7598],"mapped",[626]],[[7599,7599],"mapped",[627]],[[7600,7600],"mapped",[628]],[[7601,7601],"mapped",[629]],[[7602,7602],"mapped",[632]],[[7603,7603],"mapped",[642]],[[7604,7604],"mapped",[643]],[[7605,7605],"mapped",[427]],[[7606,7606],"mapped",[649]],[[7607,7607],"mapped",[650]],[[7608,7608],"mapped",[7452]],[[7609,7609],"mapped",[651]],[[7610,7610],"mapped",[652]],[[7611,7611],"mapped",[122]],[[7612,7612],"mapped",[656]],[[7613,7613],"mapped",[657]],[[7614,7614],"mapped",[658]],[[7615,7615],"mapped",[952]],[[7616,7619],"valid"],[[7620,7626],"valid"],[[7627,7654],"valid"],[[7655,7669],"valid"],[[7670,7675],"disallowed"],[[7676,7676],"valid"],[[7677,7677],"valid"],[[7678,7679],"valid"],[[7680,7680],"mapped",[7681]],[[7681,7681],"valid"],[[7682,7682],"mapped",[7683]],[[7683,7683],"valid"],[[7684,7684],"mapped",[7685]],[[7685,7685],"valid"],[[7686,7686],"mapped",[7687]],[[7687,7687],"valid"],[[7688,7688],"mapped",[7689]],[[7689,7689],"valid"],[[7690,7690],"mapped",[7691]],[[7691,7691],"valid"],[[7692,7692],"mapped",[7693]],[[7693,7693],"valid"],[[7694,7694],"mapped",[7695]],[[7695,7695],"valid"],[[7696,7696],"mapped",[7697]],[[7697,7697],"valid"],[[7698,7698],"mapped",[7699]],[[7699,7699],"valid"],[[7700,7700],"mapped",[7701]],[[7701,7701],"valid"],[[7702,7702],"mapped",[7703]],[[7703,7703],"valid"],[[7704,7704],"mapped",[7705]],[[7705,7705],"valid"],[[7706,7706],"mapped",[7707]],[[7707,7707],"valid"],[[7708,7708],"mapped",[7709]],[[7709,7709],"valid"],[[7710,7710],"mapped",[7711]],[[7711,7711],"valid"],[[7712,7712],"mapped",[7713]],[[7713,7713],"valid"],[[7714,7714],"mapped",[7715]],[[7715,7715],"valid"],[[7716,7716],"mapped",[7717]],[[7717,7717],"valid"],[[7718,7718],"mapped",[7719]],[[7719,7719],"valid"],[[7720,7720],"mapped",[7721]],[[7721,7721],"valid"],[[7722,7722],"mapped",[7723]],[[7723,7723],"valid"],[[7724,7724],"mapped",[7725]],[[7725,7725],"valid"],[[7726,7726],"mapped",[7727]],[[7727,7727],"valid"],[[7728,7728],"mapped",[7729]],[[7729,7729],"valid"],[[7730,7730],"mapped",[7731]],[[7731,7731],"valid"],[[7732,7732],"mapped",[7733]],[[7733,7733],"valid"],[[7734,7734],"mapped",[7735]],[[7735,7735],"valid"],[[7736,7736],"mapped",[7737]],[[7737,7737],"valid"],[[7738,7738],"mapped",[7739]],[[7739,7739],"valid"],[[7740,7740],"mapped",[7741]],[[7741,7741],"valid"],[[7742,7742],"mapped",[7743]],[[7743,7743],"valid"],[[7744,7744],"mapped",[7745]],[[7745,7745],"valid"],[[7746,7746],"mapped",[7747]],[[7747,7747],"valid"],[[7748,7748],"mapped",[7749]],[[7749,7749],"valid"],[[7750,7750],"mapped",[7751]],[[7751,7751],"valid"],[[7752,7752],"mapped",[7753]],[[7753,7753],"valid"],[[7754,7754],"mapped",[7755]],[[7755,7755],"valid"],[[7756,7756],"mapped",[7757]],[[7757,7757],"valid"],[[7758,7758],"mapped",[7759]],[[7759,7759],"valid"],[[7760,7760],"mapped",[7761]],[[7761,7761],"valid"],[[7762,7762],"mapped",[7763]],[[7763,7763],"valid"],[[7764,7764],"mapped",[7765]],[[7765,7765],"valid"],[[7766,7766],"mapped",[7767]],[[7767,7767],"valid"],[[7768,7768],"mapped",[7769]],[[7769,7769],"valid"],[[7770,7770],"mapped",[7771]],[[7771,7771],"valid"],[[7772,7772],"mapped",[7773]],[[7773,7773],"valid"],[[7774,7774],"mapped",[7775]],[[7775,7775],"valid"],[[7776,7776],"mapped",[7777]],[[7777,7777],"valid"],[[7778,7778],"mapped",[7779]],[[7779,7779],"valid"],[[7780,7780],"mapped",[7781]],[[7781,7781],"valid"],[[7782,7782],"mapped",[7783]],[[7783,7783],"valid"],[[7784,7784],"mapped",[7785]],[[7785,7785],"valid"],[[7786,7786],"mapped",[7787]],[[7787,7787],"valid"],[[7788,7788],"mapped",[7789]],[[7789,7789],"valid"],[[7790,7790],"mapped",[7791]],[[7791,7791],"valid"],[[7792,7792],"mapped",[7793]],[[7793,7793],"valid"],[[7794,7794],"mapped",[7795]],[[7795,7795],"valid"],[[7796,7796],"mapped",[7797]],[[7797,7797],"valid"],[[7798,7798],"mapped",[7799]],[[7799,7799],"valid"],[[7800,7800],"mapped",[7801]],[[7801,7801],"valid"],[[7802,7802],"mapped",[7803]],[[7803,7803],"valid"],[[7804,7804],"mapped",[7805]],[[7805,7805],"valid"],[[7806,7806],"mapped",[7807]],[[7807,7807],"valid"],[[7808,7808],"mapped",[7809]],[[7809,7809],"valid"],[[7810,7810],"mapped",[7811]],[[7811,7811],"valid"],[[7812,7812],"mapped",[7813]],[[7813,7813],"valid"],[[7814,7814],"mapped",[7815]],[[7815,7815],"valid"],[[7816,7816],"mapped",[7817]],[[7817,7817],"valid"],[[7818,7818],"mapped",[7819]],[[7819,7819],"valid"],[[7820,7820],"mapped",[7821]],[[7821,7821],"valid"],[[7822,7822],"mapped",[7823]],[[7823,7823],"valid"],[[7824,7824],"mapped",[7825]],[[7825,7825],"valid"],[[7826,7826],"mapped",[7827]],[[7827,7827],"valid"],[[7828,7828],"mapped",[7829]],[[7829,7833],"valid"],[[7834,7834],"mapped",[97,702]],[[7835,7835],"mapped",[7777]],[[7836,7837],"valid"],[[7838,7838],"mapped",[115,115]],[[7839,7839],"valid"],[[7840,7840],"mapped",[7841]],[[7841,7841],"valid"],[[7842,7842],"mapped",[7843]],[[7843,7843],"valid"],[[7844,7844],"mapped",[7845]],[[7845,7845],"valid"],[[7846,7846],"mapped",[7847]],[[7847,7847],"valid"],[[7848,7848],"mapped",[7849]],[[7849,7849],"valid"],[[7850,7850],"mapped",[7851]],[[7851,7851],"valid"],[[7852,7852],"mapped",[7853]],[[7853,7853],"valid"],[[7854,7854],"mapped",[7855]],[[7855,7855],"valid"],[[7856,7856],"mapped",[7857]],[[7857,7857],"valid"],[[7858,7858],"mapped",[7859]],[[7859,7859],"valid"],[[7860,7860],"mapped",[7861]],[[7861,7861],"valid"],[[7862,7862],"mapped",[7863]],[[7863,7863],"valid"],[[7864,7864],"mapped",[7865]],[[7865,7865],"valid"],[[7866,7866],"mapped",[7867]],[[7867,7867],"valid"],[[7868,7868],"mapped",[7869]],[[7869,7869],"valid"],[[7870,7870],"mapped",[7871]],[[7871,7871],"valid"],[[7872,7872],"mapped",[7873]],[[7873,7873],"valid"],[[7874,7874],"mapped",[7875]],[[7875,7875],"valid"],[[7876,7876],"mapped",[7877]],[[7877,7877],"valid"],[[7878,7878],"mapped",[7879]],[[7879,7879],"valid"],[[7880,7880],"mapped",[7881]],[[7881,7881],"valid"],[[7882,7882],"mapped",[7883]],[[7883,7883],"valid"],[[7884,7884],"mapped",[7885]],[[7885,7885],"valid"],[[7886,7886],"mapped",[7887]],[[7887,7887],"valid"],[[7888,7888],"mapped",[7889]],[[7889,7889],"valid"],[[7890,7890],"mapped",[7891]],[[7891,7891],"valid"],[[7892,7892],"mapped",[7893]],[[7893,7893],"valid"],[[7894,7894],"mapped",[7895]],[[7895,7895],"valid"],[[7896,7896],"mapped",[7897]],[[7897,7897],"valid"],[[7898,7898],"mapped",[7899]],[[7899,7899],"valid"],[[7900,7900],"mapped",[7901]],[[7901,7901],"valid"],[[7902,7902],"mapped",[7903]],[[7903,7903],"valid"],[[7904,7904],"mapped",[7905]],[[7905,7905],"valid"],[[7906,7906],"mapped",[7907]],[[7907,7907],"valid"],[[7908,7908],"mapped",[7909]],[[7909,7909],"valid"],[[7910,7910],"mapped",[7911]],[[7911,7911],"valid"],[[7912,7912],"mapped",[7913]],[[7913,7913],"valid"],[[7914,7914],"mapped",[7915]],[[7915,7915],"valid"],[[7916,7916],"mapped",[7917]],[[7917,7917],"valid"],[[7918,7918],"mapped",[7919]],[[7919,7919],"valid"],[[7920,7920],"mapped",[7921]],[[7921,7921],"valid"],[[7922,7922],"mapped",[7923]],[[7923,7923],"valid"],[[7924,7924],"mapped",[7925]],[[7925,7925],"valid"],[[7926,7926],"mapped",[7927]],[[7927,7927],"valid"],[[7928,7928],"mapped",[7929]],[[7929,7929],"valid"],[[7930,7930],"mapped",[7931]],[[7931,7931],"valid"],[[7932,7932],"mapped",[7933]],[[7933,7933],"valid"],[[7934,7934],"mapped",[7935]],[[7935,7935],"valid"],[[7936,7943],"valid"],[[7944,7944],"mapped",[7936]],[[7945,7945],"mapped",[7937]],[[7946,7946],"mapped",[7938]],[[7947,7947],"mapped",[7939]],[[7948,7948],"mapped",[7940]],[[7949,7949],"mapped",[7941]],[[7950,7950],"mapped",[7942]],[[7951,7951],"mapped",[7943]],[[7952,7957],"valid"],[[7958,7959],"disallowed"],[[7960,7960],"mapped",[7952]],[[7961,7961],"mapped",[7953]],[[7962,7962],"mapped",[7954]],[[7963,7963],"mapped",[7955]],[[7964,7964],"mapped",[7956]],[[7965,7965],"mapped",[7957]],[[7966,7967],"disallowed"],[[7968,7975],"valid"],[[7976,7976],"mapped",[7968]],[[7977,7977],"mapped",[7969]],[[7978,7978],"mapped",[7970]],[[7979,7979],"mapped",[7971]],[[7980,7980],"mapped",[7972]],[[7981,7981],"mapped",[7973]],[[7982,7982],"mapped",[7974]],[[7983,7983],"mapped",[7975]],[[7984,7991],"valid"],[[7992,7992],"mapped",[7984]],[[7993,7993],"mapped",[7985]],[[7994,7994],"mapped",[7986]],[[7995,7995],"mapped",[7987]],[[7996,7996],"mapped",[7988]],[[7997,7997],"mapped",[7989]],[[7998,7998],"mapped",[7990]],[[7999,7999],"mapped",[7991]],[[8e3,8005],"valid"],[[8006,8007],"disallowed"],[[8008,8008],"mapped",[8e3]],[[8009,8009],"mapped",[8001]],[[8010,8010],"mapped",[8002]],[[8011,8011],"mapped",[8003]],[[8012,8012],"mapped",[8004]],[[8013,8013],"mapped",[8005]],[[8014,8015],"disallowed"],[[8016,8023],"valid"],[[8024,8024],"disallowed"],[[8025,8025],"mapped",[8017]],[[8026,8026],"disallowed"],[[8027,8027],"mapped",[8019]],[[8028,8028],"disallowed"],[[8029,8029],"mapped",[8021]],[[8030,8030],"disallowed"],[[8031,8031],"mapped",[8023]],[[8032,8039],"valid"],[[8040,8040],"mapped",[8032]],[[8041,8041],"mapped",[8033]],[[8042,8042],"mapped",[8034]],[[8043,8043],"mapped",[8035]],[[8044,8044],"mapped",[8036]],[[8045,8045],"mapped",[8037]],[[8046,8046],"mapped",[8038]],[[8047,8047],"mapped",[8039]],[[8048,8048],"valid"],[[8049,8049],"mapped",[940]],[[8050,8050],"valid"],[[8051,8051],"mapped",[941]],[[8052,8052],"valid"],[[8053,8053],"mapped",[942]],[[8054,8054],"valid"],[[8055,8055],"mapped",[943]],[[8056,8056],"valid"],[[8057,8057],"mapped",[972]],[[8058,8058],"valid"],[[8059,8059],"mapped",[973]],[[8060,8060],"valid"],[[8061,8061],"mapped",[974]],[[8062,8063],"disallowed"],[[8064,8064],"mapped",[7936,953]],[[8065,8065],"mapped",[7937,953]],[[8066,8066],"mapped",[7938,953]],[[8067,8067],"mapped",[7939,953]],[[8068,8068],"mapped",[7940,953]],[[8069,8069],"mapped",[7941,953]],[[8070,8070],"mapped",[7942,953]],[[8071,8071],"mapped",[7943,953]],[[8072,8072],"mapped",[7936,953]],[[8073,8073],"mapped",[7937,953]],[[8074,8074],"mapped",[7938,953]],[[8075,8075],"mapped",[7939,953]],[[8076,8076],"mapped",[7940,953]],[[8077,8077],"mapped",[7941,953]],[[8078,8078],"mapped",[7942,953]],[[8079,8079],"mapped",[7943,953]],[[8080,8080],"mapped",[7968,953]],[[8081,8081],"mapped",[7969,953]],[[8082,8082],"mapped",[7970,953]],[[8083,8083],"mapped",[7971,953]],[[8084,8084],"mapped",[7972,953]],[[8085,8085],"mapped",[7973,953]],[[8086,8086],"mapped",[7974,953]],[[8087,8087],"mapped",[7975,953]],[[8088,8088],"mapped",[7968,953]],[[8089,8089],"mapped",[7969,953]],[[8090,8090],"mapped",[7970,953]],[[8091,8091],"mapped",[7971,953]],[[8092,8092],"mapped",[7972,953]],[[8093,8093],"mapped",[7973,953]],[[8094,8094],"mapped",[7974,953]],[[8095,8095],"mapped",[7975,953]],[[8096,8096],"mapped",[8032,953]],[[8097,8097],"mapped",[8033,953]],[[8098,8098],"mapped",[8034,953]],[[8099,8099],"mapped",[8035,953]],[[8100,8100],"mapped",[8036,953]],[[8101,8101],"mapped",[8037,953]],[[8102,8102],"mapped",[8038,953]],[[8103,8103],"mapped",[8039,953]],[[8104,8104],"mapped",[8032,953]],[[8105,8105],"mapped",[8033,953]],[[8106,8106],"mapped",[8034,953]],[[8107,8107],"mapped",[8035,953]],[[8108,8108],"mapped",[8036,953]],[[8109,8109],"mapped",[8037,953]],[[8110,8110],"mapped",[8038,953]],[[8111,8111],"mapped",[8039,953]],[[8112,8113],"valid"],[[8114,8114],"mapped",[8048,953]],[[8115,8115],"mapped",[945,953]],[[8116,8116],"mapped",[940,953]],[[8117,8117],"disallowed"],[[8118,8118],"valid"],[[8119,8119],"mapped",[8118,953]],[[8120,8120],"mapped",[8112]],[[8121,8121],"mapped",[8113]],[[8122,8122],"mapped",[8048]],[[8123,8123],"mapped",[940]],[[8124,8124],"mapped",[945,953]],[[8125,8125],"disallowed_STD3_mapped",[32,787]],[[8126,8126],"mapped",[953]],[[8127,8127],"disallowed_STD3_mapped",[32,787]],[[8128,8128],"disallowed_STD3_mapped",[32,834]],[[8129,8129],"disallowed_STD3_mapped",[32,776,834]],[[8130,8130],"mapped",[8052,953]],[[8131,8131],"mapped",[951,953]],[[8132,8132],"mapped",[942,953]],[[8133,8133],"disallowed"],[[8134,8134],"valid"],[[8135,8135],"mapped",[8134,953]],[[8136,8136],"mapped",[8050]],[[8137,8137],"mapped",[941]],[[8138,8138],"mapped",[8052]],[[8139,8139],"mapped",[942]],[[8140,8140],"mapped",[951,953]],[[8141,8141],"disallowed_STD3_mapped",[32,787,768]],[[8142,8142],"disallowed_STD3_mapped",[32,787,769]],[[8143,8143],"disallowed_STD3_mapped",[32,787,834]],[[8144,8146],"valid"],[[8147,8147],"mapped",[912]],[[8148,8149],"disallowed"],[[8150,8151],"valid"],[[8152,8152],"mapped",[8144]],[[8153,8153],"mapped",[8145]],[[8154,8154],"mapped",[8054]],[[8155,8155],"mapped",[943]],[[8156,8156],"disallowed"],[[8157,8157],"disallowed_STD3_mapped",[32,788,768]],[[8158,8158],"disallowed_STD3_mapped",[32,788,769]],[[8159,8159],"disallowed_STD3_mapped",[32,788,834]],[[8160,8162],"valid"],[[8163,8163],"mapped",[944]],[[8164,8167],"valid"],[[8168,8168],"mapped",[8160]],[[8169,8169],"mapped",[8161]],[[8170,8170],"mapped",[8058]],[[8171,8171],"mapped",[973]],[[8172,8172],"mapped",[8165]],[[8173,8173],"disallowed_STD3_mapped",[32,776,768]],[[8174,8174],"disallowed_STD3_mapped",[32,776,769]],[[8175,8175],"disallowed_STD3_mapped",[96]],[[8176,8177],"disallowed"],[[8178,8178],"mapped",[8060,953]],[[8179,8179],"mapped",[969,953]],[[8180,8180],"mapped",[974,953]],[[8181,8181],"disallowed"],[[8182,8182],"valid"],[[8183,8183],"mapped",[8182,953]],[[8184,8184],"mapped",[8056]],[[8185,8185],"mapped",[972]],[[8186,8186],"mapped",[8060]],[[8187,8187],"mapped",[974]],[[8188,8188],"mapped",[969,953]],[[8189,8189],"disallowed_STD3_mapped",[32,769]],[[8190,8190],"disallowed_STD3_mapped",[32,788]],[[8191,8191],"disallowed"],[[8192,8202],"disallowed_STD3_mapped",[32]],[[8203,8203],"ignored"],[[8204,8205],"deviation",[]],[[8206,8207],"disallowed"],[[8208,8208],"valid",[],"NV8"],[[8209,8209],"mapped",[8208]],[[8210,8214],"valid",[],"NV8"],[[8215,8215],"disallowed_STD3_mapped",[32,819]],[[8216,8227],"valid",[],"NV8"],[[8228,8230],"disallowed"],[[8231,8231],"valid",[],"NV8"],[[8232,8238],"disallowed"],[[8239,8239],"disallowed_STD3_mapped",[32]],[[8240,8242],"valid",[],"NV8"],[[8243,8243],"mapped",[8242,8242]],[[8244,8244],"mapped",[8242,8242,8242]],[[8245,8245],"valid",[],"NV8"],[[8246,8246],"mapped",[8245,8245]],[[8247,8247],"mapped",[8245,8245,8245]],[[8248,8251],"valid",[],"NV8"],[[8252,8252],"disallowed_STD3_mapped",[33,33]],[[8253,8253],"valid",[],"NV8"],[[8254,8254],"disallowed_STD3_mapped",[32,773]],[[8255,8262],"valid",[],"NV8"],[[8263,8263],"disallowed_STD3_mapped",[63,63]],[[8264,8264],"disallowed_STD3_mapped",[63,33]],[[8265,8265],"disallowed_STD3_mapped",[33,63]],[[8266,8269],"valid",[],"NV8"],[[8270,8274],"valid",[],"NV8"],[[8275,8276],"valid",[],"NV8"],[[8277,8278],"valid",[],"NV8"],[[8279,8279],"mapped",[8242,8242,8242,8242]],[[8280,8286],"valid",[],"NV8"],[[8287,8287],"disallowed_STD3_mapped",[32]],[[8288,8288],"ignored"],[[8289,8291],"disallowed"],[[8292,8292],"ignored"],[[8293,8293],"disallowed"],[[8294,8297],"disallowed"],[[8298,8303],"disallowed"],[[8304,8304],"mapped",[48]],[[8305,8305],"mapped",[105]],[[8306,8307],"disallowed"],[[8308,8308],"mapped",[52]],[[8309,8309],"mapped",[53]],[[8310,8310],"mapped",[54]],[[8311,8311],"mapped",[55]],[[8312,8312],"mapped",[56]],[[8313,8313],"mapped",[57]],[[8314,8314],"disallowed_STD3_mapped",[43]],[[8315,8315],"mapped",[8722]],[[8316,8316],"disallowed_STD3_mapped",[61]],[[8317,8317],"disallowed_STD3_mapped",[40]],[[8318,8318],"disallowed_STD3_mapped",[41]],[[8319,8319],"mapped",[110]],[[8320,8320],"mapped",[48]],[[8321,8321],"mapped",[49]],[[8322,8322],"mapped",[50]],[[8323,8323],"mapped",[51]],[[8324,8324],"mapped",[52]],[[8325,8325],"mapped",[53]],[[8326,8326],"mapped",[54]],[[8327,8327],"mapped",[55]],[[8328,8328],"mapped",[56]],[[8329,8329],"mapped",[57]],[[8330,8330],"disallowed_STD3_mapped",[43]],[[8331,8331],"mapped",[8722]],[[8332,8332],"disallowed_STD3_mapped",[61]],[[8333,8333],"disallowed_STD3_mapped",[40]],[[8334,8334],"disallowed_STD3_mapped",[41]],[[8335,8335],"disallowed"],[[8336,8336],"mapped",[97]],[[8337,8337],"mapped",[101]],[[8338,8338],"mapped",[111]],[[8339,8339],"mapped",[120]],[[8340,8340],"mapped",[601]],[[8341,8341],"mapped",[104]],[[8342,8342],"mapped",[107]],[[8343,8343],"mapped",[108]],[[8344,8344],"mapped",[109]],[[8345,8345],"mapped",[110]],[[8346,8346],"mapped",[112]],[[8347,8347],"mapped",[115]],[[8348,8348],"mapped",[116]],[[8349,8351],"disallowed"],[[8352,8359],"valid",[],"NV8"],[[8360,8360],"mapped",[114,115]],[[8361,8362],"valid",[],"NV8"],[[8363,8363],"valid",[],"NV8"],[[8364,8364],"valid",[],"NV8"],[[8365,8367],"valid",[],"NV8"],[[8368,8369],"valid",[],"NV8"],[[8370,8373],"valid",[],"NV8"],[[8374,8376],"valid",[],"NV8"],[[8377,8377],"valid",[],"NV8"],[[8378,8378],"valid",[],"NV8"],[[8379,8381],"valid",[],"NV8"],[[8382,8382],"valid",[],"NV8"],[[8383,8399],"disallowed"],[[8400,8417],"valid",[],"NV8"],[[8418,8419],"valid",[],"NV8"],[[8420,8426],"valid",[],"NV8"],[[8427,8427],"valid",[],"NV8"],[[8428,8431],"valid",[],"NV8"],[[8432,8432],"valid",[],"NV8"],[[8433,8447],"disallowed"],[[8448,8448],"disallowed_STD3_mapped",[97,47,99]],[[8449,8449],"disallowed_STD3_mapped",[97,47,115]],[[8450,8450],"mapped",[99]],[[8451,8451],"mapped",[176,99]],[[8452,8452],"valid",[],"NV8"],[[8453,8453],"disallowed_STD3_mapped",[99,47,111]],[[8454,8454],"disallowed_STD3_mapped",[99,47,117]],[[8455,8455],"mapped",[603]],[[8456,8456],"valid",[],"NV8"],[[8457,8457],"mapped",[176,102]],[[8458,8458],"mapped",[103]],[[8459,8462],"mapped",[104]],[[8463,8463],"mapped",[295]],[[8464,8465],"mapped",[105]],[[8466,8467],"mapped",[108]],[[8468,8468],"valid",[],"NV8"],[[8469,8469],"mapped",[110]],[[8470,8470],"mapped",[110,111]],[[8471,8472],"valid",[],"NV8"],[[8473,8473],"mapped",[112]],[[8474,8474],"mapped",[113]],[[8475,8477],"mapped",[114]],[[8478,8479],"valid",[],"NV8"],[[8480,8480],"mapped",[115,109]],[[8481,8481],"mapped",[116,101,108]],[[8482,8482],"mapped",[116,109]],[[8483,8483],"valid",[],"NV8"],[[8484,8484],"mapped",[122]],[[8485,8485],"valid",[],"NV8"],[[8486,8486],"mapped",[969]],[[8487,8487],"valid",[],"NV8"],[[8488,8488],"mapped",[122]],[[8489,8489],"valid",[],"NV8"],[[8490,8490],"mapped",[107]],[[8491,8491],"mapped",[229]],[[8492,8492],"mapped",[98]],[[8493,8493],"mapped",[99]],[[8494,8494],"valid",[],"NV8"],[[8495,8496],"mapped",[101]],[[8497,8497],"mapped",[102]],[[8498,8498],"disallowed"],[[8499,8499],"mapped",[109]],[[8500,8500],"mapped",[111]],[[8501,8501],"mapped",[1488]],[[8502,8502],"mapped",[1489]],[[8503,8503],"mapped",[1490]],[[8504,8504],"mapped",[1491]],[[8505,8505],"mapped",[105]],[[8506,8506],"valid",[],"NV8"],[[8507,8507],"mapped",[102,97,120]],[[8508,8508],"mapped",[960]],[[8509,8510],"mapped",[947]],[[8511,8511],"mapped",[960]],[[8512,8512],"mapped",[8721]],[[8513,8516],"valid",[],"NV8"],[[8517,8518],"mapped",[100]],[[8519,8519],"mapped",[101]],[[8520,8520],"mapped",[105]],[[8521,8521],"mapped",[106]],[[8522,8523],"valid",[],"NV8"],[[8524,8524],"valid",[],"NV8"],[[8525,8525],"valid",[],"NV8"],[[8526,8526],"valid"],[[8527,8527],"valid",[],"NV8"],[[8528,8528],"mapped",[49,8260,55]],[[8529,8529],"mapped",[49,8260,57]],[[8530,8530],"mapped",[49,8260,49,48]],[[8531,8531],"mapped",[49,8260,51]],[[8532,8532],"mapped",[50,8260,51]],[[8533,8533],"mapped",[49,8260,53]],[[8534,8534],"mapped",[50,8260,53]],[[8535,8535],"mapped",[51,8260,53]],[[8536,8536],"mapped",[52,8260,53]],[[8537,8537],"mapped",[49,8260,54]],[[8538,8538],"mapped",[53,8260,54]],[[8539,8539],"mapped",[49,8260,56]],[[8540,8540],"mapped",[51,8260,56]],[[8541,8541],"mapped",[53,8260,56]],[[8542,8542],"mapped",[55,8260,56]],[[8543,8543],"mapped",[49,8260]],[[8544,8544],"mapped",[105]],[[8545,8545],"mapped",[105,105]],[[8546,8546],"mapped",[105,105,105]],[[8547,8547],"mapped",[105,118]],[[8548,8548],"mapped",[118]],[[8549,8549],"mapped",[118,105]],[[8550,8550],"mapped",[118,105,105]],[[8551,8551],"mapped",[118,105,105,105]],[[8552,8552],"mapped",[105,120]],[[8553,8553],"mapped",[120]],[[8554,8554],"mapped",[120,105]],[[8555,8555],"mapped",[120,105,105]],[[8556,8556],"mapped",[108]],[[8557,8557],"mapped",[99]],[[8558,8558],"mapped",[100]],[[8559,8559],"mapped",[109]],[[8560,8560],"mapped",[105]],[[8561,8561],"mapped",[105,105]],[[8562,8562],"mapped",[105,105,105]],[[8563,8563],"mapped",[105,118]],[[8564,8564],"mapped",[118]],[[8565,8565],"mapped",[118,105]],[[8566,8566],"mapped",[118,105,105]],[[8567,8567],"mapped",[118,105,105,105]],[[8568,8568],"mapped",[105,120]],[[8569,8569],"mapped",[120]],[[8570,8570],"mapped",[120,105]],[[8571,8571],"mapped",[120,105,105]],[[8572,8572],"mapped",[108]],[[8573,8573],"mapped",[99]],[[8574,8574],"mapped",[100]],[[8575,8575],"mapped",[109]],[[8576,8578],"valid",[],"NV8"],[[8579,8579],"disallowed"],[[8580,8580],"valid"],[[8581,8584],"valid",[],"NV8"],[[8585,8585],"mapped",[48,8260,51]],[[8586,8587],"valid",[],"NV8"],[[8588,8591],"disallowed"],[[8592,8682],"valid",[],"NV8"],[[8683,8691],"valid",[],"NV8"],[[8692,8703],"valid",[],"NV8"],[[8704,8747],"valid",[],"NV8"],[[8748,8748],"mapped",[8747,8747]],[[8749,8749],"mapped",[8747,8747,8747]],[[8750,8750],"valid",[],"NV8"],[[8751,8751],"mapped",[8750,8750]],[[8752,8752],"mapped",[8750,8750,8750]],[[8753,8799],"valid",[],"NV8"],[[8800,8800],"disallowed_STD3_valid"],[[8801,8813],"valid",[],"NV8"],[[8814,8815],"disallowed_STD3_valid"],[[8816,8945],"valid",[],"NV8"],[[8946,8959],"valid",[],"NV8"],[[8960,8960],"valid",[],"NV8"],[[8961,8961],"valid",[],"NV8"],[[8962,9e3],"valid",[],"NV8"],[[9001,9001],"mapped",[12296]],[[9002,9002],"mapped",[12297]],[[9003,9082],"valid",[],"NV8"],[[9083,9083],"valid",[],"NV8"],[[9084,9084],"valid",[],"NV8"],[[9085,9114],"valid",[],"NV8"],[[9115,9166],"valid",[],"NV8"],[[9167,9168],"valid",[],"NV8"],[[9169,9179],"valid",[],"NV8"],[[9180,9191],"valid",[],"NV8"],[[9192,9192],"valid",[],"NV8"],[[9193,9203],"valid",[],"NV8"],[[9204,9210],"valid",[],"NV8"],[[9211,9215],"disallowed"],[[9216,9252],"valid",[],"NV8"],[[9253,9254],"valid",[],"NV8"],[[9255,9279],"disallowed"],[[9280,9290],"valid",[],"NV8"],[[9291,9311],"disallowed"],[[9312,9312],"mapped",[49]],[[9313,9313],"mapped",[50]],[[9314,9314],"mapped",[51]],[[9315,9315],"mapped",[52]],[[9316,9316],"mapped",[53]],[[9317,9317],"mapped",[54]],[[9318,9318],"mapped",[55]],[[9319,9319],"mapped",[56]],[[9320,9320],"mapped",[57]],[[9321,9321],"mapped",[49,48]],[[9322,9322],"mapped",[49,49]],[[9323,9323],"mapped",[49,50]],[[9324,9324],"mapped",[49,51]],[[9325,9325],"mapped",[49,52]],[[9326,9326],"mapped",[49,53]],[[9327,9327],"mapped",[49,54]],[[9328,9328],"mapped",[49,55]],[[9329,9329],"mapped",[49,56]],[[9330,9330],"mapped",[49,57]],[[9331,9331],"mapped",[50,48]],[[9332,9332],"disallowed_STD3_mapped",[40,49,41]],[[9333,9333],"disallowed_STD3_mapped",[40,50,41]],[[9334,9334],"disallowed_STD3_mapped",[40,51,41]],[[9335,9335],"disallowed_STD3_mapped",[40,52,41]],[[9336,9336],"disallowed_STD3_mapped",[40,53,41]],[[9337,9337],"disallowed_STD3_mapped",[40,54,41]],[[9338,9338],"disallowed_STD3_mapped",[40,55,41]],[[9339,9339],"disallowed_STD3_mapped",[40,56,41]],[[9340,9340],"disallowed_STD3_mapped",[40,57,41]],[[9341,9341],"disallowed_STD3_mapped",[40,49,48,41]],[[9342,9342],"disallowed_STD3_mapped",[40,49,49,41]],[[9343,9343],"disallowed_STD3_mapped",[40,49,50,41]],[[9344,9344],"disallowed_STD3_mapped",[40,49,51,41]],[[9345,9345],"disallowed_STD3_mapped",[40,49,52,41]],[[9346,9346],"disallowed_STD3_mapped",[40,49,53,41]],[[9347,9347],"disallowed_STD3_mapped",[40,49,54,41]],[[9348,9348],"disallowed_STD3_mapped",[40,49,55,41]],[[9349,9349],"disallowed_STD3_mapped",[40,49,56,41]],[[9350,9350],"disallowed_STD3_mapped",[40,49,57,41]],[[9351,9351],"disallowed_STD3_mapped",[40,50,48,41]],[[9352,9371],"disallowed"],[[9372,9372],"disallowed_STD3_mapped",[40,97,41]],[[9373,9373],"disallowed_STD3_mapped",[40,98,41]],[[9374,9374],"disallowed_STD3_mapped",[40,99,41]],[[9375,9375],"disallowed_STD3_mapped",[40,100,41]],[[9376,9376],"disallowed_STD3_mapped",[40,101,41]],[[9377,9377],"disallowed_STD3_mapped",[40,102,41]],[[9378,9378],"disallowed_STD3_mapped",[40,103,41]],[[9379,9379],"disallowed_STD3_mapped",[40,104,41]],[[9380,9380],"disallowed_STD3_mapped",[40,105,41]],[[9381,9381],"disallowed_STD3_mapped",[40,106,41]],[[9382,9382],"disallowed_STD3_mapped",[40,107,41]],[[9383,9383],"disallowed_STD3_mapped",[40,108,41]],[[9384,9384],"disallowed_STD3_mapped",[40,109,41]],[[9385,9385],"disallowed_STD3_mapped",[40,110,41]],[[9386,9386],"disallowed_STD3_mapped",[40,111,41]],[[9387,9387],"disallowed_STD3_mapped",[40,112,41]],[[9388,9388],"disallowed_STD3_mapped",[40,113,41]],[[9389,9389],"disallowed_STD3_mapped",[40,114,41]],[[9390,9390],"disallowed_STD3_mapped",[40,115,41]],[[9391,9391],"disallowed_STD3_mapped",[40,116,41]],[[9392,9392],"disallowed_STD3_mapped",[40,117,41]],[[9393,9393],"disallowed_STD3_mapped",[40,118,41]],[[9394,9394],"disallowed_STD3_mapped",[40,119,41]],[[9395,9395],"disallowed_STD3_mapped",[40,120,41]],[[9396,9396],"disallowed_STD3_mapped",[40,121,41]],[[9397,9397],"disallowed_STD3_mapped",[40,122,41]],[[9398,9398],"mapped",[97]],[[9399,9399],"mapped",[98]],[[9400,9400],"mapped",[99]],[[9401,9401],"mapped",[100]],[[9402,9402],"mapped",[101]],[[9403,9403],"mapped",[102]],[[9404,9404],"mapped",[103]],[[9405,9405],"mapped",[104]],[[9406,9406],"mapped",[105]],[[9407,9407],"mapped",[106]],[[9408,9408],"mapped",[107]],[[9409,9409],"mapped",[108]],[[9410,9410],"mapped",[109]],[[9411,9411],"mapped",[110]],[[9412,9412],"mapped",[111]],[[9413,9413],"mapped",[112]],[[9414,9414],"mapped",[113]],[[9415,9415],"mapped",[114]],[[9416,9416],"mapped",[115]],[[9417,9417],"mapped",[116]],[[9418,9418],"mapped",[117]],[[9419,9419],"mapped",[118]],[[9420,9420],"mapped",[119]],[[9421,9421],"mapped",[120]],[[9422,9422],"mapped",[121]],[[9423,9423],"mapped",[122]],[[9424,9424],"mapped",[97]],[[9425,9425],"mapped",[98]],[[9426,9426],"mapped",[99]],[[9427,9427],"mapped",[100]],[[9428,9428],"mapped",[101]],[[9429,9429],"mapped",[102]],[[9430,9430],"mapped",[103]],[[9431,9431],"mapped",[104]],[[9432,9432],"mapped",[105]],[[9433,9433],"mapped",[106]],[[9434,9434],"mapped",[107]],[[9435,9435],"mapped",[108]],[[9436,9436],"mapped",[109]],[[9437,9437],"mapped",[110]],[[9438,9438],"mapped",[111]],[[9439,9439],"mapped",[112]],[[9440,9440],"mapped",[113]],[[9441,9441],"mapped",[114]],[[9442,9442],"mapped",[115]],[[9443,9443],"mapped",[116]],[[9444,9444],"mapped",[117]],[[9445,9445],"mapped",[118]],[[9446,9446],"mapped",[119]],[[9447,9447],"mapped",[120]],[[9448,9448],"mapped",[121]],[[9449,9449],"mapped",[122]],[[9450,9450],"mapped",[48]],[[9451,9470],"valid",[],"NV8"],[[9471,9471],"valid",[],"NV8"],[[9472,9621],"valid",[],"NV8"],[[9622,9631],"valid",[],"NV8"],[[9632,9711],"valid",[],"NV8"],[[9712,9719],"valid",[],"NV8"],[[9720,9727],"valid",[],"NV8"],[[9728,9747],"valid",[],"NV8"],[[9748,9749],"valid",[],"NV8"],[[9750,9751],"valid",[],"NV8"],[[9752,9752],"valid",[],"NV8"],[[9753,9753],"valid",[],"NV8"],[[9754,9839],"valid",[],"NV8"],[[9840,9841],"valid",[],"NV8"],[[9842,9853],"valid",[],"NV8"],[[9854,9855],"valid",[],"NV8"],[[9856,9865],"valid",[],"NV8"],[[9866,9873],"valid",[],"NV8"],[[9874,9884],"valid",[],"NV8"],[[9885,9885],"valid",[],"NV8"],[[9886,9887],"valid",[],"NV8"],[[9888,9889],"valid",[],"NV8"],[[9890,9905],"valid",[],"NV8"],[[9906,9906],"valid",[],"NV8"],[[9907,9916],"valid",[],"NV8"],[[9917,9919],"valid",[],"NV8"],[[9920,9923],"valid",[],"NV8"],[[9924,9933],"valid",[],"NV8"],[[9934,9934],"valid",[],"NV8"],[[9935,9953],"valid",[],"NV8"],[[9954,9954],"valid",[],"NV8"],[[9955,9955],"valid",[],"NV8"],[[9956,9959],"valid",[],"NV8"],[[9960,9983],"valid",[],"NV8"],[[9984,9984],"valid",[],"NV8"],[[9985,9988],"valid",[],"NV8"],[[9989,9989],"valid",[],"NV8"],[[9990,9993],"valid",[],"NV8"],[[9994,9995],"valid",[],"NV8"],[[9996,10023],"valid",[],"NV8"],[[10024,10024],"valid",[],"NV8"],[[10025,10059],"valid",[],"NV8"],[[10060,10060],"valid",[],"NV8"],[[10061,10061],"valid",[],"NV8"],[[10062,10062],"valid",[],"NV8"],[[10063,10066],"valid",[],"NV8"],[[10067,10069],"valid",[],"NV8"],[[10070,10070],"valid",[],"NV8"],[[10071,10071],"valid",[],"NV8"],[[10072,10078],"valid",[],"NV8"],[[10079,10080],"valid",[],"NV8"],[[10081,10087],"valid",[],"NV8"],[[10088,10101],"valid",[],"NV8"],[[10102,10132],"valid",[],"NV8"],[[10133,10135],"valid",[],"NV8"],[[10136,10159],"valid",[],"NV8"],[[10160,10160],"valid",[],"NV8"],[[10161,10174],"valid",[],"NV8"],[[10175,10175],"valid",[],"NV8"],[[10176,10182],"valid",[],"NV8"],[[10183,10186],"valid",[],"NV8"],[[10187,10187],"valid",[],"NV8"],[[10188,10188],"valid",[],"NV8"],[[10189,10189],"valid",[],"NV8"],[[10190,10191],"valid",[],"NV8"],[[10192,10219],"valid",[],"NV8"],[[10220,10223],"valid",[],"NV8"],[[10224,10239],"valid",[],"NV8"],[[10240,10495],"valid",[],"NV8"],[[10496,10763],"valid",[],"NV8"],[[10764,10764],"mapped",[8747,8747,8747,8747]],[[10765,10867],"valid",[],"NV8"],[[10868,10868],"disallowed_STD3_mapped",[58,58,61]],[[10869,10869],"disallowed_STD3_mapped",[61,61]],[[10870,10870],"disallowed_STD3_mapped",[61,61,61]],[[10871,10971],"valid",[],"NV8"],[[10972,10972],"mapped",[10973,824]],[[10973,11007],"valid",[],"NV8"],[[11008,11021],"valid",[],"NV8"],[[11022,11027],"valid",[],"NV8"],[[11028,11034],"valid",[],"NV8"],[[11035,11039],"valid",[],"NV8"],[[11040,11043],"valid",[],"NV8"],[[11044,11084],"valid",[],"NV8"],[[11085,11087],"valid",[],"NV8"],[[11088,11092],"valid",[],"NV8"],[[11093,11097],"valid",[],"NV8"],[[11098,11123],"valid",[],"NV8"],[[11124,11125],"disallowed"],[[11126,11157],"valid",[],"NV8"],[[11158,11159],"disallowed"],[[11160,11193],"valid",[],"NV8"],[[11194,11196],"disallowed"],[[11197,11208],"valid",[],"NV8"],[[11209,11209],"disallowed"],[[11210,11217],"valid",[],"NV8"],[[11218,11243],"disallowed"],[[11244,11247],"valid",[],"NV8"],[[11248,11263],"disallowed"],[[11264,11264],"mapped",[11312]],[[11265,11265],"mapped",[11313]],[[11266,11266],"mapped",[11314]],[[11267,11267],"mapped",[11315]],[[11268,11268],"mapped",[11316]],[[11269,11269],"mapped",[11317]],[[11270,11270],"mapped",[11318]],[[11271,11271],"mapped",[11319]],[[11272,11272],"mapped",[11320]],[[11273,11273],"mapped",[11321]],[[11274,11274],"mapped",[11322]],[[11275,11275],"mapped",[11323]],[[11276,11276],"mapped",[11324]],[[11277,11277],"mapped",[11325]],[[11278,11278],"mapped",[11326]],[[11279,11279],"mapped",[11327]],[[11280,11280],"mapped",[11328]],[[11281,11281],"mapped",[11329]],[[11282,11282],"mapped",[11330]],[[11283,11283],"mapped",[11331]],[[11284,11284],"mapped",[11332]],[[11285,11285],"mapped",[11333]],[[11286,11286],"mapped",[11334]],[[11287,11287],"mapped",[11335]],[[11288,11288],"mapped",[11336]],[[11289,11289],"mapped",[11337]],[[11290,11290],"mapped",[11338]],[[11291,11291],"mapped",[11339]],[[11292,11292],"mapped",[11340]],[[11293,11293],"mapped",[11341]],[[11294,11294],"mapped",[11342]],[[11295,11295],"mapped",[11343]],[[11296,11296],"mapped",[11344]],[[11297,11297],"mapped",[11345]],[[11298,11298],"mapped",[11346]],[[11299,11299],"mapped",[11347]],[[11300,11300],"mapped",[11348]],[[11301,11301],"mapped",[11349]],[[11302,11302],"mapped",[11350]],[[11303,11303],"mapped",[11351]],[[11304,11304],"mapped",[11352]],[[11305,11305],"mapped",[11353]],[[11306,11306],"mapped",[11354]],[[11307,11307],"mapped",[11355]],[[11308,11308],"mapped",[11356]],[[11309,11309],"mapped",[11357]],[[11310,11310],"mapped",[11358]],[[11311,11311],"disallowed"],[[11312,11358],"valid"],[[11359,11359],"disallowed"],[[11360,11360],"mapped",[11361]],[[11361,11361],"valid"],[[11362,11362],"mapped",[619]],[[11363,11363],"mapped",[7549]],[[11364,11364],"mapped",[637]],[[11365,11366],"valid"],[[11367,11367],"mapped",[11368]],[[11368,11368],"valid"],[[11369,11369],"mapped",[11370]],[[11370,11370],"valid"],[[11371,11371],"mapped",[11372]],[[11372,11372],"valid"],[[11373,11373],"mapped",[593]],[[11374,11374],"mapped",[625]],[[11375,11375],"mapped",[592]],[[11376,11376],"mapped",[594]],[[11377,11377],"valid"],[[11378,11378],"mapped",[11379]],[[11379,11379],"valid"],[[11380,11380],"valid"],[[11381,11381],"mapped",[11382]],[[11382,11383],"valid"],[[11384,11387],"valid"],[[11388,11388],"mapped",[106]],[[11389,11389],"mapped",[118]],[[11390,11390],"mapped",[575]],[[11391,11391],"mapped",[576]],[[11392,11392],"mapped",[11393]],[[11393,11393],"valid"],[[11394,11394],"mapped",[11395]],[[11395,11395],"valid"],[[11396,11396],"mapped",[11397]],[[11397,11397],"valid"],[[11398,11398],"mapped",[11399]],[[11399,11399],"valid"],[[11400,11400],"mapped",[11401]],[[11401,11401],"valid"],[[11402,11402],"mapped",[11403]],[[11403,11403],"valid"],[[11404,11404],"mapped",[11405]],[[11405,11405],"valid"],[[11406,11406],"mapped",[11407]],[[11407,11407],"valid"],[[11408,11408],"mapped",[11409]],[[11409,11409],"valid"],[[11410,11410],"mapped",[11411]],[[11411,11411],"valid"],[[11412,11412],"mapped",[11413]],[[11413,11413],"valid"],[[11414,11414],"mapped",[11415]],[[11415,11415],"valid"],[[11416,11416],"mapped",[11417]],[[11417,11417],"valid"],[[11418,11418],"mapped",[11419]],[[11419,11419],"valid"],[[11420,11420],"mapped",[11421]],[[11421,11421],"valid"],[[11422,11422],"mapped",[11423]],[[11423,11423],"valid"],[[11424,11424],"mapped",[11425]],[[11425,11425],"valid"],[[11426,11426],"mapped",[11427]],[[11427,11427],"valid"],[[11428,11428],"mapped",[11429]],[[11429,11429],"valid"],[[11430,11430],"mapped",[11431]],[[11431,11431],"valid"],[[11432,11432],"mapped",[11433]],[[11433,11433],"valid"],[[11434,11434],"mapped",[11435]],[[11435,11435],"valid"],[[11436,11436],"mapped",[11437]],[[11437,11437],"valid"],[[11438,11438],"mapped",[11439]],[[11439,11439],"valid"],[[11440,11440],"mapped",[11441]],[[11441,11441],"valid"],[[11442,11442],"mapped",[11443]],[[11443,11443],"valid"],[[11444,11444],"mapped",[11445]],[[11445,11445],"valid"],[[11446,11446],"mapped",[11447]],[[11447,11447],"valid"],[[11448,11448],"mapped",[11449]],[[11449,11449],"valid"],[[11450,11450],"mapped",[11451]],[[11451,11451],"valid"],[[11452,11452],"mapped",[11453]],[[11453,11453],"valid"],[[11454,11454],"mapped",[11455]],[[11455,11455],"valid"],[[11456,11456],"mapped",[11457]],[[11457,11457],"valid"],[[11458,11458],"mapped",[11459]],[[11459,11459],"valid"],[[11460,11460],"mapped",[11461]],[[11461,11461],"valid"],[[11462,11462],"mapped",[11463]],[[11463,11463],"valid"],[[11464,11464],"mapped",[11465]],[[11465,11465],"valid"],[[11466,11466],"mapped",[11467]],[[11467,11467],"valid"],[[11468,11468],"mapped",[11469]],[[11469,11469],"valid"],[[11470,11470],"mapped",[11471]],[[11471,11471],"valid"],[[11472,11472],"mapped",[11473]],[[11473,11473],"valid"],[[11474,11474],"mapped",[11475]],[[11475,11475],"valid"],[[11476,11476],"mapped",[11477]],[[11477,11477],"valid"],[[11478,11478],"mapped",[11479]],[[11479,11479],"valid"],[[11480,11480],"mapped",[11481]],[[11481,11481],"valid"],[[11482,11482],"mapped",[11483]],[[11483,11483],"valid"],[[11484,11484],"mapped",[11485]],[[11485,11485],"valid"],[[11486,11486],"mapped",[11487]],[[11487,11487],"valid"],[[11488,11488],"mapped",[11489]],[[11489,11489],"valid"],[[11490,11490],"mapped",[11491]],[[11491,11492],"valid"],[[11493,11498],"valid",[],"NV8"],[[11499,11499],"mapped",[11500]],[[11500,11500],"valid"],[[11501,11501],"mapped",[11502]],[[11502,11505],"valid"],[[11506,11506],"mapped",[11507]],[[11507,11507],"valid"],[[11508,11512],"disallowed"],[[11513,11519],"valid",[],"NV8"],[[11520,11557],"valid"],[[11558,11558],"disallowed"],[[11559,11559],"valid"],[[11560,11564],"disallowed"],[[11565,11565],"valid"],[[11566,11567],"disallowed"],[[11568,11621],"valid"],[[11622,11623],"valid"],[[11624,11630],"disallowed"],[[11631,11631],"mapped",[11617]],[[11632,11632],"valid",[],"NV8"],[[11633,11646],"disallowed"],[[11647,11647],"valid"],[[11648,11670],"valid"],[[11671,11679],"disallowed"],[[11680,11686],"valid"],[[11687,11687],"disallowed"],[[11688,11694],"valid"],[[11695,11695],"disallowed"],[[11696,11702],"valid"],[[11703,11703],"disallowed"],[[11704,11710],"valid"],[[11711,11711],"disallowed"],[[11712,11718],"valid"],[[11719,11719],"disallowed"],[[11720,11726],"valid"],[[11727,11727],"disallowed"],[[11728,11734],"valid"],[[11735,11735],"disallowed"],[[11736,11742],"valid"],[[11743,11743],"disallowed"],[[11744,11775],"valid"],[[11776,11799],"valid",[],"NV8"],[[11800,11803],"valid",[],"NV8"],[[11804,11805],"valid",[],"NV8"],[[11806,11822],"valid",[],"NV8"],[[11823,11823],"valid"],[[11824,11824],"valid",[],"NV8"],[[11825,11825],"valid",[],"NV8"],[[11826,11835],"valid",[],"NV8"],[[11836,11842],"valid",[],"NV8"],[[11843,11903],"disallowed"],[[11904,11929],"valid",[],"NV8"],[[11930,11930],"disallowed"],[[11931,11934],"valid",[],"NV8"],[[11935,11935],"mapped",[27597]],[[11936,12018],"valid",[],"NV8"],[[12019,12019],"mapped",[40863]],[[12020,12031],"disallowed"],[[12032,12032],"mapped",[19968]],[[12033,12033],"mapped",[20008]],[[12034,12034],"mapped",[20022]],[[12035,12035],"mapped",[20031]],[[12036,12036],"mapped",[20057]],[[12037,12037],"mapped",[20101]],[[12038,12038],"mapped",[20108]],[[12039,12039],"mapped",[20128]],[[12040,12040],"mapped",[20154]],[[12041,12041],"mapped",[20799]],[[12042,12042],"mapped",[20837]],[[12043,12043],"mapped",[20843]],[[12044,12044],"mapped",[20866]],[[12045,12045],"mapped",[20886]],[[12046,12046],"mapped",[20907]],[[12047,12047],"mapped",[20960]],[[12048,12048],"mapped",[20981]],[[12049,12049],"mapped",[20992]],[[12050,12050],"mapped",[21147]],[[12051,12051],"mapped",[21241]],[[12052,12052],"mapped",[21269]],[[12053,12053],"mapped",[21274]],[[12054,12054],"mapped",[21304]],[[12055,12055],"mapped",[21313]],[[12056,12056],"mapped",[21340]],[[12057,12057],"mapped",[21353]],[[12058,12058],"mapped",[21378]],[[12059,12059],"mapped",[21430]],[[12060,12060],"mapped",[21448]],[[12061,12061],"mapped",[21475]],[[12062,12062],"mapped",[22231]],[[12063,12063],"mapped",[22303]],[[12064,12064],"mapped",[22763]],[[12065,12065],"mapped",[22786]],[[12066,12066],"mapped",[22794]],[[12067,12067],"mapped",[22805]],[[12068,12068],"mapped",[22823]],[[12069,12069],"mapped",[22899]],[[12070,12070],"mapped",[23376]],[[12071,12071],"mapped",[23424]],[[12072,12072],"mapped",[23544]],[[12073,12073],"mapped",[23567]],[[12074,12074],"mapped",[23586]],[[12075,12075],"mapped",[23608]],[[12076,12076],"mapped",[23662]],[[12077,12077],"mapped",[23665]],[[12078,12078],"mapped",[24027]],[[12079,12079],"mapped",[24037]],[[12080,12080],"mapped",[24049]],[[12081,12081],"mapped",[24062]],[[12082,12082],"mapped",[24178]],[[12083,12083],"mapped",[24186]],[[12084,12084],"mapped",[24191]],[[12085,12085],"mapped",[24308]],[[12086,12086],"mapped",[24318]],[[12087,12087],"mapped",[24331]],[[12088,12088],"mapped",[24339]],[[12089,12089],"mapped",[24400]],[[12090,12090],"mapped",[24417]],[[12091,12091],"mapped",[24435]],[[12092,12092],"mapped",[24515]],[[12093,12093],"mapped",[25096]],[[12094,12094],"mapped",[25142]],[[12095,12095],"mapped",[25163]],[[12096,12096],"mapped",[25903]],[[12097,12097],"mapped",[25908]],[[12098,12098],"mapped",[25991]],[[12099,12099],"mapped",[26007]],[[12100,12100],"mapped",[26020]],[[12101,12101],"mapped",[26041]],[[12102,12102],"mapped",[26080]],[[12103,12103],"mapped",[26085]],[[12104,12104],"mapped",[26352]],[[12105,12105],"mapped",[26376]],[[12106,12106],"mapped",[26408]],[[12107,12107],"mapped",[27424]],[[12108,12108],"mapped",[27490]],[[12109,12109],"mapped",[27513]],[[12110,12110],"mapped",[27571]],[[12111,12111],"mapped",[27595]],[[12112,12112],"mapped",[27604]],[[12113,12113],"mapped",[27611]],[[12114,12114],"mapped",[27663]],[[12115,12115],"mapped",[27668]],[[12116,12116],"mapped",[27700]],[[12117,12117],"mapped",[28779]],[[12118,12118],"mapped",[29226]],[[12119,12119],"mapped",[29238]],[[12120,12120],"mapped",[29243]],[[12121,12121],"mapped",[29247]],[[12122,12122],"mapped",[29255]],[[12123,12123],"mapped",[29273]],[[12124,12124],"mapped",[29275]],[[12125,12125],"mapped",[29356]],[[12126,12126],"mapped",[29572]],[[12127,12127],"mapped",[29577]],[[12128,12128],"mapped",[29916]],[[12129,12129],"mapped",[29926]],[[12130,12130],"mapped",[29976]],[[12131,12131],"mapped",[29983]],[[12132,12132],"mapped",[29992]],[[12133,12133],"mapped",[3e4]],[[12134,12134],"mapped",[30091]],[[12135,12135],"mapped",[30098]],[[12136,12136],"mapped",[30326]],[[12137,12137],"mapped",[30333]],[[12138,12138],"mapped",[30382]],[[12139,12139],"mapped",[30399]],[[12140,12140],"mapped",[30446]],[[12141,12141],"mapped",[30683]],[[12142,12142],"mapped",[30690]],[[12143,12143],"mapped",[30707]],[[12144,12144],"mapped",[31034]],[[12145,12145],"mapped",[31160]],[[12146,12146],"mapped",[31166]],[[12147,12147],"mapped",[31348]],[[12148,12148],"mapped",[31435]],[[12149,12149],"mapped",[31481]],[[12150,12150],"mapped",[31859]],[[12151,12151],"mapped",[31992]],[[12152,12152],"mapped",[32566]],[[12153,12153],"mapped",[32593]],[[12154,12154],"mapped",[32650]],[[12155,12155],"mapped",[32701]],[[12156,12156],"mapped",[32769]],[[12157,12157],"mapped",[32780]],[[12158,12158],"mapped",[32786]],[[12159,12159],"mapped",[32819]],[[12160,12160],"mapped",[32895]],[[12161,12161],"mapped",[32905]],[[12162,12162],"mapped",[33251]],[[12163,12163],"mapped",[33258]],[[12164,12164],"mapped",[33267]],[[12165,12165],"mapped",[33276]],[[12166,12166],"mapped",[33292]],[[12167,12167],"mapped",[33307]],[[12168,12168],"mapped",[33311]],[[12169,12169],"mapped",[33390]],[[12170,12170],"mapped",[33394]],[[12171,12171],"mapped",[33400]],[[12172,12172],"mapped",[34381]],[[12173,12173],"mapped",[34411]],[[12174,12174],"mapped",[34880]],[[12175,12175],"mapped",[34892]],[[12176,12176],"mapped",[34915]],[[12177,12177],"mapped",[35198]],[[12178,12178],"mapped",[35211]],[[12179,12179],"mapped",[35282]],[[12180,12180],"mapped",[35328]],[[12181,12181],"mapped",[35895]],[[12182,12182],"mapped",[35910]],[[12183,12183],"mapped",[35925]],[[12184,12184],"mapped",[35960]],[[12185,12185],"mapped",[35997]],[[12186,12186],"mapped",[36196]],[[12187,12187],"mapped",[36208]],[[12188,12188],"mapped",[36275]],[[12189,12189],"mapped",[36523]],[[12190,12190],"mapped",[36554]],[[12191,12191],"mapped",[36763]],[[12192,12192],"mapped",[36784]],[[12193,12193],"mapped",[36789]],[[12194,12194],"mapped",[37009]],[[12195,12195],"mapped",[37193]],[[12196,12196],"mapped",[37318]],[[12197,12197],"mapped",[37324]],[[12198,12198],"mapped",[37329]],[[12199,12199],"mapped",[38263]],[[12200,12200],"mapped",[38272]],[[12201,12201],"mapped",[38428]],[[12202,12202],"mapped",[38582]],[[12203,12203],"mapped",[38585]],[[12204,12204],"mapped",[38632]],[[12205,12205],"mapped",[38737]],[[12206,12206],"mapped",[38750]],[[12207,12207],"mapped",[38754]],[[12208,12208],"mapped",[38761]],[[12209,12209],"mapped",[38859]],[[12210,12210],"mapped",[38893]],[[12211,12211],"mapped",[38899]],[[12212,12212],"mapped",[38913]],[[12213,12213],"mapped",[39080]],[[12214,12214],"mapped",[39131]],[[12215,12215],"mapped",[39135]],[[12216,12216],"mapped",[39318]],[[12217,12217],"mapped",[39321]],[[12218,12218],"mapped",[39340]],[[12219,12219],"mapped",[39592]],[[12220,12220],"mapped",[39640]],[[12221,12221],"mapped",[39647]],[[12222,12222],"mapped",[39717]],[[12223,12223],"mapped",[39727]],[[12224,12224],"mapped",[39730]],[[12225,12225],"mapped",[39740]],[[12226,12226],"mapped",[39770]],[[12227,12227],"mapped",[40165]],[[12228,12228],"mapped",[40565]],[[12229,12229],"mapped",[40575]],[[12230,12230],"mapped",[40613]],[[12231,12231],"mapped",[40635]],[[12232,12232],"mapped",[40643]],[[12233,12233],"mapped",[40653]],[[12234,12234],"mapped",[40657]],[[12235,12235],"mapped",[40697]],[[12236,12236],"mapped",[40701]],[[12237,12237],"mapped",[40718]],[[12238,12238],"mapped",[40723]],[[12239,12239],"mapped",[40736]],[[12240,12240],"mapped",[40763]],[[12241,12241],"mapped",[40778]],[[12242,12242],"mapped",[40786]],[[12243,12243],"mapped",[40845]],[[12244,12244],"mapped",[40860]],[[12245,12245],"mapped",[40864]],[[12246,12271],"disallowed"],[[12272,12283],"disallowed"],[[12284,12287],"disallowed"],[[12288,12288],"disallowed_STD3_mapped",[32]],[[12289,12289],"valid",[],"NV8"],[[12290,12290],"mapped",[46]],[[12291,12292],"valid",[],"NV8"],[[12293,12295],"valid"],[[12296,12329],"valid",[],"NV8"],[[12330,12333],"valid"],[[12334,12341],"valid",[],"NV8"],[[12342,12342],"mapped",[12306]],[[12343,12343],"valid",[],"NV8"],[[12344,12344],"mapped",[21313]],[[12345,12345],"mapped",[21316]],[[12346,12346],"mapped",[21317]],[[12347,12347],"valid",[],"NV8"],[[12348,12348],"valid"],[[12349,12349],"valid",[],"NV8"],[[12350,12350],"valid",[],"NV8"],[[12351,12351],"valid",[],"NV8"],[[12352,12352],"disallowed"],[[12353,12436],"valid"],[[12437,12438],"valid"],[[12439,12440],"disallowed"],[[12441,12442],"valid"],[[12443,12443],"disallowed_STD3_mapped",[32,12441]],[[12444,12444],"disallowed_STD3_mapped",[32,12442]],[[12445,12446],"valid"],[[12447,12447],"mapped",[12424,12426]],[[12448,12448],"valid",[],"NV8"],[[12449,12542],"valid"],[[12543,12543],"mapped",[12467,12488]],[[12544,12548],"disallowed"],[[12549,12588],"valid"],[[12589,12589],"valid"],[[12590,12592],"disallowed"],[[12593,12593],"mapped",[4352]],[[12594,12594],"mapped",[4353]],[[12595,12595],"mapped",[4522]],[[12596,12596],"mapped",[4354]],[[12597,12597],"mapped",[4524]],[[12598,12598],"mapped",[4525]],[[12599,12599],"mapped",[4355]],[[12600,12600],"mapped",[4356]],[[12601,12601],"mapped",[4357]],[[12602,12602],"mapped",[4528]],[[12603,12603],"mapped",[4529]],[[12604,12604],"mapped",[4530]],[[12605,12605],"mapped",[4531]],[[12606,12606],"mapped",[4532]],[[12607,12607],"mapped",[4533]],[[12608,12608],"mapped",[4378]],[[12609,12609],"mapped",[4358]],[[12610,12610],"mapped",[4359]],[[12611,12611],"mapped",[4360]],[[12612,12612],"mapped",[4385]],[[12613,12613],"mapped",[4361]],[[12614,12614],"mapped",[4362]],[[12615,12615],"mapped",[4363]],[[12616,12616],"mapped",[4364]],[[12617,12617],"mapped",[4365]],[[12618,12618],"mapped",[4366]],[[12619,12619],"mapped",[4367]],[[12620,12620],"mapped",[4368]],[[12621,12621],"mapped",[4369]],[[12622,12622],"mapped",[4370]],[[12623,12623],"mapped",[4449]],[[12624,12624],"mapped",[4450]],[[12625,12625],"mapped",[4451]],[[12626,12626],"mapped",[4452]],[[12627,12627],"mapped",[4453]],[[12628,12628],"mapped",[4454]],[[12629,12629],"mapped",[4455]],[[12630,12630],"mapped",[4456]],[[12631,12631],"mapped",[4457]],[[12632,12632],"mapped",[4458]],[[12633,12633],"mapped",[4459]],[[12634,12634],"mapped",[4460]],[[12635,12635],"mapped",[4461]],[[12636,12636],"mapped",[4462]],[[12637,12637],"mapped",[4463]],[[12638,12638],"mapped",[4464]],[[12639,12639],"mapped",[4465]],[[12640,12640],"mapped",[4466]],[[12641,12641],"mapped",[4467]],[[12642,12642],"mapped",[4468]],[[12643,12643],"mapped",[4469]],[[12644,12644],"disallowed"],[[12645,12645],"mapped",[4372]],[[12646,12646],"mapped",[4373]],[[12647,12647],"mapped",[4551]],[[12648,12648],"mapped",[4552]],[[12649,12649],"mapped",[4556]],[[12650,12650],"mapped",[4558]],[[12651,12651],"mapped",[4563]],[[12652,12652],"mapped",[4567]],[[12653,12653],"mapped",[4569]],[[12654,12654],"mapped",[4380]],[[12655,12655],"mapped",[4573]],[[12656,12656],"mapped",[4575]],[[12657,12657],"mapped",[4381]],[[12658,12658],"mapped",[4382]],[[12659,12659],"mapped",[4384]],[[12660,12660],"mapped",[4386]],[[12661,12661],"mapped",[4387]],[[12662,12662],"mapped",[4391]],[[12663,12663],"mapped",[4393]],[[12664,12664],"mapped",[4395]],[[12665,12665],"mapped",[4396]],[[12666,12666],"mapped",[4397]],[[12667,12667],"mapped",[4398]],[[12668,12668],"mapped",[4399]],[[12669,12669],"mapped",[4402]],[[12670,12670],"mapped",[4406]],[[12671,12671],"mapped",[4416]],[[12672,12672],"mapped",[4423]],[[12673,12673],"mapped",[4428]],[[12674,12674],"mapped",[4593]],[[12675,12675],"mapped",[4594]],[[12676,12676],"mapped",[4439]],[[12677,12677],"mapped",[4440]],[[12678,12678],"mapped",[4441]],[[12679,12679],"mapped",[4484]],[[12680,12680],"mapped",[4485]],[[12681,12681],"mapped",[4488]],[[12682,12682],"mapped",[4497]],[[12683,12683],"mapped",[4498]],[[12684,12684],"mapped",[4500]],[[12685,12685],"mapped",[4510]],[[12686,12686],"mapped",[4513]],[[12687,12687],"disallowed"],[[12688,12689],"valid",[],"NV8"],[[12690,12690],"mapped",[19968]],[[12691,12691],"mapped",[20108]],[[12692,12692],"mapped",[19977]],[[12693,12693],"mapped",[22235]],[[12694,12694],"mapped",[19978]],[[12695,12695],"mapped",[20013]],[[12696,12696],"mapped",[19979]],[[12697,12697],"mapped",[30002]],[[12698,12698],"mapped",[20057]],[[12699,12699],"mapped",[19993]],[[12700,12700],"mapped",[19969]],[[12701,12701],"mapped",[22825]],[[12702,12702],"mapped",[22320]],[[12703,12703],"mapped",[20154]],[[12704,12727],"valid"],[[12728,12730],"valid"],[[12731,12735],"disallowed"],[[12736,12751],"valid",[],"NV8"],[[12752,12771],"valid",[],"NV8"],[[12772,12783],"disallowed"],[[12784,12799],"valid"],[[12800,12800],"disallowed_STD3_mapped",[40,4352,41]],[[12801,12801],"disallowed_STD3_mapped",[40,4354,41]],[[12802,12802],"disallowed_STD3_mapped",[40,4355,41]],[[12803,12803],"disallowed_STD3_mapped",[40,4357,41]],[[12804,12804],"disallowed_STD3_mapped",[40,4358,41]],[[12805,12805],"disallowed_STD3_mapped",[40,4359,41]],[[12806,12806],"disallowed_STD3_mapped",[40,4361,41]],[[12807,12807],"disallowed_STD3_mapped",[40,4363,41]],[[12808,12808],"disallowed_STD3_mapped",[40,4364,41]],[[12809,12809],"disallowed_STD3_mapped",[40,4366,41]],[[12810,12810],"disallowed_STD3_mapped",[40,4367,41]],[[12811,12811],"disallowed_STD3_mapped",[40,4368,41]],[[12812,12812],"disallowed_STD3_mapped",[40,4369,41]],[[12813,12813],"disallowed_STD3_mapped",[40,4370,41]],[[12814,12814],"disallowed_STD3_mapped",[40,44032,41]],[[12815,12815],"disallowed_STD3_mapped",[40,45208,41]],[[12816,12816],"disallowed_STD3_mapped",[40,45796,41]],[[12817,12817],"disallowed_STD3_mapped",[40,46972,41]],[[12818,12818],"disallowed_STD3_mapped",[40,47560,41]],[[12819,12819],"disallowed_STD3_mapped",[40,48148,41]],[[12820,12820],"disallowed_STD3_mapped",[40,49324,41]],[[12821,12821],"disallowed_STD3_mapped",[40,50500,41]],[[12822,12822],"disallowed_STD3_mapped",[40,51088,41]],[[12823,12823],"disallowed_STD3_mapped",[40,52264,41]],[[12824,12824],"disallowed_STD3_mapped",[40,52852,41]],[[12825,12825],"disallowed_STD3_mapped",[40,53440,41]],[[12826,12826],"disallowed_STD3_mapped",[40,54028,41]],[[12827,12827],"disallowed_STD3_mapped",[40,54616,41]],[[12828,12828],"disallowed_STD3_mapped",[40,51452,41]],[[12829,12829],"disallowed_STD3_mapped",[40,50724,51204,41]],[[12830,12830],"disallowed_STD3_mapped",[40,50724,54980,41]],[[12831,12831],"disallowed"],[[12832,12832],"disallowed_STD3_mapped",[40,19968,41]],[[12833,12833],"disallowed_STD3_mapped",[40,20108,41]],[[12834,12834],"disallowed_STD3_mapped",[40,19977,41]],[[12835,12835],"disallowed_STD3_mapped",[40,22235,41]],[[12836,12836],"disallowed_STD3_mapped",[40,20116,41]],[[12837,12837],"disallowed_STD3_mapped",[40,20845,41]],[[12838,12838],"disallowed_STD3_mapped",[40,19971,41]],[[12839,12839],"disallowed_STD3_mapped",[40,20843,41]],[[12840,12840],"disallowed_STD3_mapped",[40,20061,41]],[[12841,12841],"disallowed_STD3_mapped",[40,21313,41]],[[12842,12842],"disallowed_STD3_mapped",[40,26376,41]],[[12843,12843],"disallowed_STD3_mapped",[40,28779,41]],[[12844,12844],"disallowed_STD3_mapped",[40,27700,41]],[[12845,12845],"disallowed_STD3_mapped",[40,26408,41]],[[12846,12846],"disallowed_STD3_mapped",[40,37329,41]],[[12847,12847],"disallowed_STD3_mapped",[40,22303,41]],[[12848,12848],"disallowed_STD3_mapped",[40,26085,41]],[[12849,12849],"disallowed_STD3_mapped",[40,26666,41]],[[12850,12850],"disallowed_STD3_mapped",[40,26377,41]],[[12851,12851],"disallowed_STD3_mapped",[40,31038,41]],[[12852,12852],"disallowed_STD3_mapped",[40,21517,41]],[[12853,12853],"disallowed_STD3_mapped",[40,29305,41]],[[12854,12854],"disallowed_STD3_mapped",[40,36001,41]],[[12855,12855],"disallowed_STD3_mapped",[40,31069,41]],[[12856,12856],"disallowed_STD3_mapped",[40,21172,41]],[[12857,12857],"disallowed_STD3_mapped",[40,20195,41]],[[12858,12858],"disallowed_STD3_mapped",[40,21628,41]],[[12859,12859],"disallowed_STD3_mapped",[40,23398,41]],[[12860,12860],"disallowed_STD3_mapped",[40,30435,41]],[[12861,12861],"disallowed_STD3_mapped",[40,20225,41]],[[12862,12862],"disallowed_STD3_mapped",[40,36039,41]],[[12863,12863],"disallowed_STD3_mapped",[40,21332,41]],[[12864,12864],"disallowed_STD3_mapped",[40,31085,41]],[[12865,12865],"disallowed_STD3_mapped",[40,20241,41]],[[12866,12866],"disallowed_STD3_mapped",[40,33258,41]],[[12867,12867],"disallowed_STD3_mapped",[40,33267,41]],[[12868,12868],"mapped",[21839]],[[12869,12869],"mapped",[24188]],[[12870,12870],"mapped",[25991]],[[12871,12871],"mapped",[31631]],[[12872,12879],"valid",[],"NV8"],[[12880,12880],"mapped",[112,116,101]],[[12881,12881],"mapped",[50,49]],[[12882,12882],"mapped",[50,50]],[[12883,12883],"mapped",[50,51]],[[12884,12884],"mapped",[50,52]],[[12885,12885],"mapped",[50,53]],[[12886,12886],"mapped",[50,54]],[[12887,12887],"mapped",[50,55]],[[12888,12888],"mapped",[50,56]],[[12889,12889],"mapped",[50,57]],[[12890,12890],"mapped",[51,48]],[[12891,12891],"mapped",[51,49]],[[12892,12892],"mapped",[51,50]],[[12893,12893],"mapped",[51,51]],[[12894,12894],"mapped",[51,52]],[[12895,12895],"mapped",[51,53]],[[12896,12896],"mapped",[4352]],[[12897,12897],"mapped",[4354]],[[12898,12898],"mapped",[4355]],[[12899,12899],"mapped",[4357]],[[12900,12900],"mapped",[4358]],[[12901,12901],"mapped",[4359]],[[12902,12902],"mapped",[4361]],[[12903,12903],"mapped",[4363]],[[12904,12904],"mapped",[4364]],[[12905,12905],"mapped",[4366]],[[12906,12906],"mapped",[4367]],[[12907,12907],"mapped",[4368]],[[12908,12908],"mapped",[4369]],[[12909,12909],"mapped",[4370]],[[12910,12910],"mapped",[44032]],[[12911,12911],"mapped",[45208]],[[12912,12912],"mapped",[45796]],[[12913,12913],"mapped",[46972]],[[12914,12914],"mapped",[47560]],[[12915,12915],"mapped",[48148]],[[12916,12916],"mapped",[49324]],[[12917,12917],"mapped",[50500]],[[12918,12918],"mapped",[51088]],[[12919,12919],"mapped",[52264]],[[12920,12920],"mapped",[52852]],[[12921,12921],"mapped",[53440]],[[12922,12922],"mapped",[54028]],[[12923,12923],"mapped",[54616]],[[12924,12924],"mapped",[52280,44256]],[[12925,12925],"mapped",[51452,51032]],[[12926,12926],"mapped",[50864]],[[12927,12927],"valid",[],"NV8"],[[12928,12928],"mapped",[19968]],[[12929,12929],"mapped",[20108]],[[12930,12930],"mapped",[19977]],[[12931,12931],"mapped",[22235]],[[12932,12932],"mapped",[20116]],[[12933,12933],"mapped",[20845]],[[12934,12934],"mapped",[19971]],[[12935,12935],"mapped",[20843]],[[12936,12936],"mapped",[20061]],[[12937,12937],"mapped",[21313]],[[12938,12938],"mapped",[26376]],[[12939,12939],"mapped",[28779]],[[12940,12940],"mapped",[27700]],[[12941,12941],"mapped",[26408]],[[12942,12942],"mapped",[37329]],[[12943,12943],"mapped",[22303]],[[12944,12944],"mapped",[26085]],[[12945,12945],"mapped",[26666]],[[12946,12946],"mapped",[26377]],[[12947,12947],"mapped",[31038]],[[12948,12948],"mapped",[21517]],[[12949,12949],"mapped",[29305]],[[12950,12950],"mapped",[36001]],[[12951,12951],"mapped",[31069]],[[12952,12952],"mapped",[21172]],[[12953,12953],"mapped",[31192]],[[12954,12954],"mapped",[30007]],[[12955,12955],"mapped",[22899]],[[12956,12956],"mapped",[36969]],[[12957,12957],"mapped",[20778]],[[12958,12958],"mapped",[21360]],[[12959,12959],"mapped",[27880]],[[12960,12960],"mapped",[38917]],[[12961,12961],"mapped",[20241]],[[12962,12962],"mapped",[20889]],[[12963,12963],"mapped",[27491]],[[12964,12964],"mapped",[19978]],[[12965,12965],"mapped",[20013]],[[12966,12966],"mapped",[19979]],[[12967,12967],"mapped",[24038]],[[12968,12968],"mapped",[21491]],[[12969,12969],"mapped",[21307]],[[12970,12970],"mapped",[23447]],[[12971,12971],"mapped",[23398]],[[12972,12972],"mapped",[30435]],[[12973,12973],"mapped",[20225]],[[12974,12974],"mapped",[36039]],[[12975,12975],"mapped",[21332]],[[12976,12976],"mapped",[22812]],[[12977,12977],"mapped",[51,54]],[[12978,12978],"mapped",[51,55]],[[12979,12979],"mapped",[51,56]],[[12980,12980],"mapped",[51,57]],[[12981,12981],"mapped",[52,48]],[[12982,12982],"mapped",[52,49]],[[12983,12983],"mapped",[52,50]],[[12984,12984],"mapped",[52,51]],[[12985,12985],"mapped",[52,52]],[[12986,12986],"mapped",[52,53]],[[12987,12987],"mapped",[52,54]],[[12988,12988],"mapped",[52,55]],[[12989,12989],"mapped",[52,56]],[[12990,12990],"mapped",[52,57]],[[12991,12991],"mapped",[53,48]],[[12992,12992],"mapped",[49,26376]],[[12993,12993],"mapped",[50,26376]],[[12994,12994],"mapped",[51,26376]],[[12995,12995],"mapped",[52,26376]],[[12996,12996],"mapped",[53,26376]],[[12997,12997],"mapped",[54,26376]],[[12998,12998],"mapped",[55,26376]],[[12999,12999],"mapped",[56,26376]],[[13e3,13e3],"mapped",[57,26376]],[[13001,13001],"mapped",[49,48,26376]],[[13002,13002],"mapped",[49,49,26376]],[[13003,13003],"mapped",[49,50,26376]],[[13004,13004],"mapped",[104,103]],[[13005,13005],"mapped",[101,114,103]],[[13006,13006],"mapped",[101,118]],[[13007,13007],"mapped",[108,116,100]],[[13008,13008],"mapped",[12450]],[[13009,13009],"mapped",[12452]],[[13010,13010],"mapped",[12454]],[[13011,13011],"mapped",[12456]],[[13012,13012],"mapped",[12458]],[[13013,13013],"mapped",[12459]],[[13014,13014],"mapped",[12461]],[[13015,13015],"mapped",[12463]],[[13016,13016],"mapped",[12465]],[[13017,13017],"mapped",[12467]],[[13018,13018],"mapped",[12469]],[[13019,13019],"mapped",[12471]],[[13020,13020],"mapped",[12473]],[[13021,13021],"mapped",[12475]],[[13022,13022],"mapped",[12477]],[[13023,13023],"mapped",[12479]],[[13024,13024],"mapped",[12481]],[[13025,13025],"mapped",[12484]],[[13026,13026],"mapped",[12486]],[[13027,13027],"mapped",[12488]],[[13028,13028],"mapped",[12490]],[[13029,13029],"mapped",[12491]],[[13030,13030],"mapped",[12492]],[[13031,13031],"mapped",[12493]],[[13032,13032],"mapped",[12494]],[[13033,13033],"mapped",[12495]],[[13034,13034],"mapped",[12498]],[[13035,13035],"mapped",[12501]],[[13036,13036],"mapped",[12504]],[[13037,13037],"mapped",[12507]],[[13038,13038],"mapped",[12510]],[[13039,13039],"mapped",[12511]],[[13040,13040],"mapped",[12512]],[[13041,13041],"mapped",[12513]],[[13042,13042],"mapped",[12514]],[[13043,13043],"mapped",[12516]],[[13044,13044],"mapped",[12518]],[[13045,13045],"mapped",[12520]],[[13046,13046],"mapped",[12521]],[[13047,13047],"mapped",[12522]],[[13048,13048],"mapped",[12523]],[[13049,13049],"mapped",[12524]],[[13050,13050],"mapped",[12525]],[[13051,13051],"mapped",[12527]],[[13052,13052],"mapped",[12528]],[[13053,13053],"mapped",[12529]],[[13054,13054],"mapped",[12530]],[[13055,13055],"disallowed"],[[13056,13056],"mapped",[12450,12497,12540,12488]],[[13057,13057],"mapped",[12450,12523,12501,12449]],[[13058,13058],"mapped",[12450,12531,12506,12450]],[[13059,13059],"mapped",[12450,12540,12523]],[[13060,13060],"mapped",[12452,12491,12531,12464]],[[13061,13061],"mapped",[12452,12531,12481]],[[13062,13062],"mapped",[12454,12457,12531]],[[13063,13063],"mapped",[12456,12473,12463,12540,12489]],[[13064,13064],"mapped",[12456,12540,12459,12540]],[[13065,13065],"mapped",[12458,12531,12473]],[[13066,13066],"mapped",[12458,12540,12512]],[[13067,13067],"mapped",[12459,12452,12522]],[[13068,13068],"mapped",[12459,12521,12483,12488]],[[13069,13069],"mapped",[12459,12525,12522,12540]],[[13070,13070],"mapped",[12460,12525,12531]],[[13071,13071],"mapped",[12460,12531,12510]],[[13072,13072],"mapped",[12462,12460]],[[13073,13073],"mapped",[12462,12491,12540]],[[13074,13074],"mapped",[12461,12517,12522,12540]],[[13075,13075],"mapped",[12462,12523,12480,12540]],[[13076,13076],"mapped",[12461,12525]],[[13077,13077],"mapped",[12461,12525,12464,12521,12512]],[[13078,13078],"mapped",[12461,12525,12513,12540,12488,12523]],[[13079,13079],"mapped",[12461,12525,12527,12483,12488]],[[13080,13080],"mapped",[12464,12521,12512]],[[13081,13081],"mapped",[12464,12521,12512,12488,12531]],[[13082,13082],"mapped",[12463,12523,12476,12452,12525]],[[13083,13083],"mapped",[12463,12525,12540,12493]],[[13084,13084],"mapped",[12465,12540,12473]],[[13085,13085],"mapped",[12467,12523,12490]],[[13086,13086],"mapped",[12467,12540,12509]],[[13087,13087],"mapped",[12469,12452,12463,12523]],[[13088,13088],"mapped",[12469,12531,12481,12540,12512]],[[13089,13089],"mapped",[12471,12522,12531,12464]],[[13090,13090],"mapped",[12475,12531,12481]],[[13091,13091],"mapped",[12475,12531,12488]],[[13092,13092],"mapped",[12480,12540,12473]],[[13093,13093],"mapped",[12487,12471]],[[13094,13094],"mapped",[12489,12523]],[[13095,13095],"mapped",[12488,12531]],[[13096,13096],"mapped",[12490,12494]],[[13097,13097],"mapped",[12494,12483,12488]],[[13098,13098],"mapped",[12495,12452,12484]],[[13099,13099],"mapped",[12497,12540,12475,12531,12488]],[[13100,13100],"mapped",[12497,12540,12484]],[[13101,13101],"mapped",[12496,12540,12524,12523]],[[13102,13102],"mapped",[12500,12450,12473,12488,12523]],[[13103,13103],"mapped",[12500,12463,12523]],[[13104,13104],"mapped",[12500,12467]],[[13105,13105],"mapped",[12499,12523]],[[13106,13106],"mapped",[12501,12449,12521,12483,12489]],[[13107,13107],"mapped",[12501,12451,12540,12488]],[[13108,13108],"mapped",[12502,12483,12471,12455,12523]],[[13109,13109],"mapped",[12501,12521,12531]],[[13110,13110],"mapped",[12504,12463,12479,12540,12523]],[[13111,13111],"mapped",[12506,12477]],[[13112,13112],"mapped",[12506,12491,12498]],[[13113,13113],"mapped",[12504,12523,12484]],[[13114,13114],"mapped",[12506,12531,12473]],[[13115,13115],"mapped",[12506,12540,12472]],[[13116,13116],"mapped",[12505,12540,12479]],[[13117,13117],"mapped",[12509,12452,12531,12488]],[[13118,13118],"mapped",[12508,12523,12488]],[[13119,13119],"mapped",[12507,12531]],[[13120,13120],"mapped",[12509,12531,12489]],[[13121,13121],"mapped",[12507,12540,12523]],[[13122,13122],"mapped",[12507,12540,12531]],[[13123,13123],"mapped",[12510,12452,12463,12525]],[[13124,13124],"mapped",[12510,12452,12523]],[[13125,13125],"mapped",[12510,12483,12495]],[[13126,13126],"mapped",[12510,12523,12463]],[[13127,13127],"mapped",[12510,12531,12471,12519,12531]],[[13128,13128],"mapped",[12511,12463,12525,12531]],[[13129,13129],"mapped",[12511,12522]],[[13130,13130],"mapped",[12511,12522,12496,12540,12523]],[[13131,13131],"mapped",[12513,12460]],[[13132,13132],"mapped",[12513,12460,12488,12531]],[[13133,13133],"mapped",[12513,12540,12488,12523]],[[13134,13134],"mapped",[12516,12540,12489]],[[13135,13135],"mapped",[12516,12540,12523]],[[13136,13136],"mapped",[12518,12450,12531]],[[13137,13137],"mapped",[12522,12483,12488,12523]],[[13138,13138],"mapped",[12522,12521]],[[13139,13139],"mapped",[12523,12500,12540]],[[13140,13140],"mapped",[12523,12540,12502,12523]],[[13141,13141],"mapped",[12524,12512]],[[13142,13142],"mapped",[12524,12531,12488,12466,12531]],[[13143,13143],"mapped",[12527,12483,12488]],[[13144,13144],"mapped",[48,28857]],[[13145,13145],"mapped",[49,28857]],[[13146,13146],"mapped",[50,28857]],[[13147,13147],"mapped",[51,28857]],[[13148,13148],"mapped",[52,28857]],[[13149,13149],"mapped",[53,28857]],[[13150,13150],"mapped",[54,28857]],[[13151,13151],"mapped",[55,28857]],[[13152,13152],"mapped",[56,28857]],[[13153,13153],"mapped",[57,28857]],[[13154,13154],"mapped",[49,48,28857]],[[13155,13155],"mapped",[49,49,28857]],[[13156,13156],"mapped",[49,50,28857]],[[13157,13157],"mapped",[49,51,28857]],[[13158,13158],"mapped",[49,52,28857]],[[13159,13159],"mapped",[49,53,28857]],[[13160,13160],"mapped",[49,54,28857]],[[13161,13161],"mapped",[49,55,28857]],[[13162,13162],"mapped",[49,56,28857]],[[13163,13163],"mapped",[49,57,28857]],[[13164,13164],"mapped",[50,48,28857]],[[13165,13165],"mapped",[50,49,28857]],[[13166,13166],"mapped",[50,50,28857]],[[13167,13167],"mapped",[50,51,28857]],[[13168,13168],"mapped",[50,52,28857]],[[13169,13169],"mapped",[104,112,97]],[[13170,13170],"mapped",[100,97]],[[13171,13171],"mapped",[97,117]],[[13172,13172],"mapped",[98,97,114]],[[13173,13173],"mapped",[111,118]],[[13174,13174],"mapped",[112,99]],[[13175,13175],"mapped",[100,109]],[[13176,13176],"mapped",[100,109,50]],[[13177,13177],"mapped",[100,109,51]],[[13178,13178],"mapped",[105,117]],[[13179,13179],"mapped",[24179,25104]],[[13180,13180],"mapped",[26157,21644]],[[13181,13181],"mapped",[22823,27491]],[[13182,13182],"mapped",[26126,27835]],[[13183,13183],"mapped",[26666,24335,20250,31038]],[[13184,13184],"mapped",[112,97]],[[13185,13185],"mapped",[110,97]],[[13186,13186],"mapped",[956,97]],[[13187,13187],"mapped",[109,97]],[[13188,13188],"mapped",[107,97]],[[13189,13189],"mapped",[107,98]],[[13190,13190],"mapped",[109,98]],[[13191,13191],"mapped",[103,98]],[[13192,13192],"mapped",[99,97,108]],[[13193,13193],"mapped",[107,99,97,108]],[[13194,13194],"mapped",[112,102]],[[13195,13195],"mapped",[110,102]],[[13196,13196],"mapped",[956,102]],[[13197,13197],"mapped",[956,103]],[[13198,13198],"mapped",[109,103]],[[13199,13199],"mapped",[107,103]],[[13200,13200],"mapped",[104,122]],[[13201,13201],"mapped",[107,104,122]],[[13202,13202],"mapped",[109,104,122]],[[13203,13203],"mapped",[103,104,122]],[[13204,13204],"mapped",[116,104,122]],[[13205,13205],"mapped",[956,108]],[[13206,13206],"mapped",[109,108]],[[13207,13207],"mapped",[100,108]],[[13208,13208],"mapped",[107,108]],[[13209,13209],"mapped",[102,109]],[[13210,13210],"mapped",[110,109]],[[13211,13211],"mapped",[956,109]],[[13212,13212],"mapped",[109,109]],[[13213,13213],"mapped",[99,109]],[[13214,13214],"mapped",[107,109]],[[13215,13215],"mapped",[109,109,50]],[[13216,13216],"mapped",[99,109,50]],[[13217,13217],"mapped",[109,50]],[[13218,13218],"mapped",[107,109,50]],[[13219,13219],"mapped",[109,109,51]],[[13220,13220],"mapped",[99,109,51]],[[13221,13221],"mapped",[109,51]],[[13222,13222],"mapped",[107,109,51]],[[13223,13223],"mapped",[109,8725,115]],[[13224,13224],"mapped",[109,8725,115,50]],[[13225,13225],"mapped",[112,97]],[[13226,13226],"mapped",[107,112,97]],[[13227,13227],"mapped",[109,112,97]],[[13228,13228],"mapped",[103,112,97]],[[13229,13229],"mapped",[114,97,100]],[[13230,13230],"mapped",[114,97,100,8725,115]],[[13231,13231],"mapped",[114,97,100,8725,115,50]],[[13232,13232],"mapped",[112,115]],[[13233,13233],"mapped",[110,115]],[[13234,13234],"mapped",[956,115]],[[13235,13235],"mapped",[109,115]],[[13236,13236],"mapped",[112,118]],[[13237,13237],"mapped",[110,118]],[[13238,13238],"mapped",[956,118]],[[13239,13239],"mapped",[109,118]],[[13240,13240],"mapped",[107,118]],[[13241,13241],"mapped",[109,118]],[[13242,13242],"mapped",[112,119]],[[13243,13243],"mapped",[110,119]],[[13244,13244],"mapped",[956,119]],[[13245,13245],"mapped",[109,119]],[[13246,13246],"mapped",[107,119]],[[13247,13247],"mapped",[109,119]],[[13248,13248],"mapped",[107,969]],[[13249,13249],"mapped",[109,969]],[[13250,13250],"disallowed"],[[13251,13251],"mapped",[98,113]],[[13252,13252],"mapped",[99,99]],[[13253,13253],"mapped",[99,100]],[[13254,13254],"mapped",[99,8725,107,103]],[[13255,13255],"disallowed"],[[13256,13256],"mapped",[100,98]],[[13257,13257],"mapped",[103,121]],[[13258,13258],"mapped",[104,97]],[[13259,13259],"mapped",[104,112]],[[13260,13260],"mapped",[105,110]],[[13261,13261],"mapped",[107,107]],[[13262,13262],"mapped",[107,109]],[[13263,13263],"mapped",[107,116]],[[13264,13264],"mapped",[108,109]],[[13265,13265],"mapped",[108,110]],[[13266,13266],"mapped",[108,111,103]],[[13267,13267],"mapped",[108,120]],[[13268,13268],"mapped",[109,98]],[[13269,13269],"mapped",[109,105,108]],[[13270,13270],"mapped",[109,111,108]],[[13271,13271],"mapped",[112,104]],[[13272,13272],"disallowed"],[[13273,13273],"mapped",[112,112,109]],[[13274,13274],"mapped",[112,114]],[[13275,13275],"mapped",[115,114]],[[13276,13276],"mapped",[115,118]],[[13277,13277],"mapped",[119,98]],[[13278,13278],"mapped",[118,8725,109]],[[13279,13279],"mapped",[97,8725,109]],[[13280,13280],"mapped",[49,26085]],[[13281,13281],"mapped",[50,26085]],[[13282,13282],"mapped",[51,26085]],[[13283,13283],"mapped",[52,26085]],[[13284,13284],"mapped",[53,26085]],[[13285,13285],"mapped",[54,26085]],[[13286,13286],"mapped",[55,26085]],[[13287,13287],"mapped",[56,26085]],[[13288,13288],"mapped",[57,26085]],[[13289,13289],"mapped",[49,48,26085]],[[13290,13290],"mapped",[49,49,26085]],[[13291,13291],"mapped",[49,50,26085]],[[13292,13292],"mapped",[49,51,26085]],[[13293,13293],"mapped",[49,52,26085]],[[13294,13294],"mapped",[49,53,26085]],[[13295,13295],"mapped",[49,54,26085]],[[13296,13296],"mapped",[49,55,26085]],[[13297,13297],"mapped",[49,56,26085]],[[13298,13298],"mapped",[49,57,26085]],[[13299,13299],"mapped",[50,48,26085]],[[13300,13300],"mapped",[50,49,26085]],[[13301,13301],"mapped",[50,50,26085]],[[13302,13302],"mapped",[50,51,26085]],[[13303,13303],"mapped",[50,52,26085]],[[13304,13304],"mapped",[50,53,26085]],[[13305,13305],"mapped",[50,54,26085]],[[13306,13306],"mapped",[50,55,26085]],[[13307,13307],"mapped",[50,56,26085]],[[13308,13308],"mapped",[50,57,26085]],[[13309,13309],"mapped",[51,48,26085]],[[13310,13310],"mapped",[51,49,26085]],[[13311,13311],"mapped",[103,97,108]],[[13312,19893],"valid"],[[19894,19903],"disallowed"],[[19904,19967],"valid",[],"NV8"],[[19968,40869],"valid"],[[40870,40891],"valid"],[[40892,40899],"valid"],[[40900,40907],"valid"],[[40908,40908],"valid"],[[40909,40917],"valid"],[[40918,40959],"disallowed"],[[40960,42124],"valid"],[[42125,42127],"disallowed"],[[42128,42145],"valid",[],"NV8"],[[42146,42147],"valid",[],"NV8"],[[42148,42163],"valid",[],"NV8"],[[42164,42164],"valid",[],"NV8"],[[42165,42176],"valid",[],"NV8"],[[42177,42177],"valid",[],"NV8"],[[42178,42180],"valid",[],"NV8"],[[42181,42181],"valid",[],"NV8"],[[42182,42182],"valid",[],"NV8"],[[42183,42191],"disallowed"],[[42192,42237],"valid"],[[42238,42239],"valid",[],"NV8"],[[42240,42508],"valid"],[[42509,42511],"valid",[],"NV8"],[[42512,42539],"valid"],[[42540,42559],"disallowed"],[[42560,42560],"mapped",[42561]],[[42561,42561],"valid"],[[42562,42562],"mapped",[42563]],[[42563,42563],"valid"],[[42564,42564],"mapped",[42565]],[[42565,42565],"valid"],[[42566,42566],"mapped",[42567]],[[42567,42567],"valid"],[[42568,42568],"mapped",[42569]],[[42569,42569],"valid"],[[42570,42570],"mapped",[42571]],[[42571,42571],"valid"],[[42572,42572],"mapped",[42573]],[[42573,42573],"valid"],[[42574,42574],"mapped",[42575]],[[42575,42575],"valid"],[[42576,42576],"mapped",[42577]],[[42577,42577],"valid"],[[42578,42578],"mapped",[42579]],[[42579,42579],"valid"],[[42580,42580],"mapped",[42581]],[[42581,42581],"valid"],[[42582,42582],"mapped",[42583]],[[42583,42583],"valid"],[[42584,42584],"mapped",[42585]],[[42585,42585],"valid"],[[42586,42586],"mapped",[42587]],[[42587,42587],"valid"],[[42588,42588],"mapped",[42589]],[[42589,42589],"valid"],[[42590,42590],"mapped",[42591]],[[42591,42591],"valid"],[[42592,42592],"mapped",[42593]],[[42593,42593],"valid"],[[42594,42594],"mapped",[42595]],[[42595,42595],"valid"],[[42596,42596],"mapped",[42597]],[[42597,42597],"valid"],[[42598,42598],"mapped",[42599]],[[42599,42599],"valid"],[[42600,42600],"mapped",[42601]],[[42601,42601],"valid"],[[42602,42602],"mapped",[42603]],[[42603,42603],"valid"],[[42604,42604],"mapped",[42605]],[[42605,42607],"valid"],[[42608,42611],"valid",[],"NV8"],[[42612,42619],"valid"],[[42620,42621],"valid"],[[42622,42622],"valid",[],"NV8"],[[42623,42623],"valid"],[[42624,42624],"mapped",[42625]],[[42625,42625],"valid"],[[42626,42626],"mapped",[42627]],[[42627,42627],"valid"],[[42628,42628],"mapped",[42629]],[[42629,42629],"valid"],[[42630,42630],"mapped",[42631]],[[42631,42631],"valid"],[[42632,42632],"mapped",[42633]],[[42633,42633],"valid"],[[42634,42634],"mapped",[42635]],[[42635,42635],"valid"],[[42636,42636],"mapped",[42637]],[[42637,42637],"valid"],[[42638,42638],"mapped",[42639]],[[42639,42639],"valid"],[[42640,42640],"mapped",[42641]],[[42641,42641],"valid"],[[42642,42642],"mapped",[42643]],[[42643,42643],"valid"],[[42644,42644],"mapped",[42645]],[[42645,42645],"valid"],[[42646,42646],"mapped",[42647]],[[42647,42647],"valid"],[[42648,42648],"mapped",[42649]],[[42649,42649],"valid"],[[42650,42650],"mapped",[42651]],[[42651,42651],"valid"],[[42652,42652],"mapped",[1098]],[[42653,42653],"mapped",[1100]],[[42654,42654],"valid"],[[42655,42655],"valid"],[[42656,42725],"valid"],[[42726,42735],"valid",[],"NV8"],[[42736,42737],"valid"],[[42738,42743],"valid",[],"NV8"],[[42744,42751],"disallowed"],[[42752,42774],"valid",[],"NV8"],[[42775,42778],"valid"],[[42779,42783],"valid"],[[42784,42785],"valid",[],"NV8"],[[42786,42786],"mapped",[42787]],[[42787,42787],"valid"],[[42788,42788],"mapped",[42789]],[[42789,42789],"valid"],[[42790,42790],"mapped",[42791]],[[42791,42791],"valid"],[[42792,42792],"mapped",[42793]],[[42793,42793],"valid"],[[42794,42794],"mapped",[42795]],[[42795,42795],"valid"],[[42796,42796],"mapped",[42797]],[[42797,42797],"valid"],[[42798,42798],"mapped",[42799]],[[42799,42801],"valid"],[[42802,42802],"mapped",[42803]],[[42803,42803],"valid"],[[42804,42804],"mapped",[42805]],[[42805,42805],"valid"],[[42806,42806],"mapped",[42807]],[[42807,42807],"valid"],[[42808,42808],"mapped",[42809]],[[42809,42809],"valid"],[[42810,42810],"mapped",[42811]],[[42811,42811],"valid"],[[42812,42812],"mapped",[42813]],[[42813,42813],"valid"],[[42814,42814],"mapped",[42815]],[[42815,42815],"valid"],[[42816,42816],"mapped",[42817]],[[42817,42817],"valid"],[[42818,42818],"mapped",[42819]],[[42819,42819],"valid"],[[42820,42820],"mapped",[42821]],[[42821,42821],"valid"],[[42822,42822],"mapped",[42823]],[[42823,42823],"valid"],[[42824,42824],"mapped",[42825]],[[42825,42825],"valid"],[[42826,42826],"mapped",[42827]],[[42827,42827],"valid"],[[42828,42828],"mapped",[42829]],[[42829,42829],"valid"],[[42830,42830],"mapped",[42831]],[[42831,42831],"valid"],[[42832,42832],"mapped",[42833]],[[42833,42833],"valid"],[[42834,42834],"mapped",[42835]],[[42835,42835],"valid"],[[42836,42836],"mapped",[42837]],[[42837,42837],"valid"],[[42838,42838],"mapped",[42839]],[[42839,42839],"valid"],[[42840,42840],"mapped",[42841]],[[42841,42841],"valid"],[[42842,42842],"mapped",[42843]],[[42843,42843],"valid"],[[42844,42844],"mapped",[42845]],[[42845,42845],"valid"],[[42846,42846],"mapped",[42847]],[[42847,42847],"valid"],[[42848,42848],"mapped",[42849]],[[42849,42849],"valid"],[[42850,42850],"mapped",[42851]],[[42851,42851],"valid"],[[42852,42852],"mapped",[42853]],[[42853,42853],"valid"],[[42854,42854],"mapped",[42855]],[[42855,42855],"valid"],[[42856,42856],"mapped",[42857]],[[42857,42857],"valid"],[[42858,42858],"mapped",[42859]],[[42859,42859],"valid"],[[42860,42860],"mapped",[42861]],[[42861,42861],"valid"],[[42862,42862],"mapped",[42863]],[[42863,42863],"valid"],[[42864,42864],"mapped",[42863]],[[42865,42872],"valid"],[[42873,42873],"mapped",[42874]],[[42874,42874],"valid"],[[42875,42875],"mapped",[42876]],[[42876,42876],"valid"],[[42877,42877],"mapped",[7545]],[[42878,42878],"mapped",[42879]],[[42879,42879],"valid"],[[42880,42880],"mapped",[42881]],[[42881,42881],"valid"],[[42882,42882],"mapped",[42883]],[[42883,42883],"valid"],[[42884,42884],"mapped",[42885]],[[42885,42885],"valid"],[[42886,42886],"mapped",[42887]],[[42887,42888],"valid"],[[42889,42890],"valid",[],"NV8"],[[42891,42891],"mapped",[42892]],[[42892,42892],"valid"],[[42893,42893],"mapped",[613]],[[42894,42894],"valid"],[[42895,42895],"valid"],[[42896,42896],"mapped",[42897]],[[42897,42897],"valid"],[[42898,42898],"mapped",[42899]],[[42899,42899],"valid"],[[42900,42901],"valid"],[[42902,42902],"mapped",[42903]],[[42903,42903],"valid"],[[42904,42904],"mapped",[42905]],[[42905,42905],"valid"],[[42906,42906],"mapped",[42907]],[[42907,42907],"valid"],[[42908,42908],"mapped",[42909]],[[42909,42909],"valid"],[[42910,42910],"mapped",[42911]],[[42911,42911],"valid"],[[42912,42912],"mapped",[42913]],[[42913,42913],"valid"],[[42914,42914],"mapped",[42915]],[[42915,42915],"valid"],[[42916,42916],"mapped",[42917]],[[42917,42917],"valid"],[[42918,42918],"mapped",[42919]],[[42919,42919],"valid"],[[42920,42920],"mapped",[42921]],[[42921,42921],"valid"],[[42922,42922],"mapped",[614]],[[42923,42923],"mapped",[604]],[[42924,42924],"mapped",[609]],[[42925,42925],"mapped",[620]],[[42926,42927],"disallowed"],[[42928,42928],"mapped",[670]],[[42929,42929],"mapped",[647]],[[42930,42930],"mapped",[669]],[[42931,42931],"mapped",[43859]],[[42932,42932],"mapped",[42933]],[[42933,42933],"valid"],[[42934,42934],"mapped",[42935]],[[42935,42935],"valid"],[[42936,42998],"disallowed"],[[42999,42999],"valid"],[[43e3,43e3],"mapped",[295]],[[43001,43001],"mapped",[339]],[[43002,43002],"valid"],[[43003,43007],"valid"],[[43008,43047],"valid"],[[43048,43051],"valid",[],"NV8"],[[43052,43055],"disallowed"],[[43056,43065],"valid",[],"NV8"],[[43066,43071],"disallowed"],[[43072,43123],"valid"],[[43124,43127],"valid",[],"NV8"],[[43128,43135],"disallowed"],[[43136,43204],"valid"],[[43205,43213],"disallowed"],[[43214,43215],"valid",[],"NV8"],[[43216,43225],"valid"],[[43226,43231],"disallowed"],[[43232,43255],"valid"],[[43256,43258],"valid",[],"NV8"],[[43259,43259],"valid"],[[43260,43260],"valid",[],"NV8"],[[43261,43261],"valid"],[[43262,43263],"disallowed"],[[43264,43309],"valid"],[[43310,43311],"valid",[],"NV8"],[[43312,43347],"valid"],[[43348,43358],"disallowed"],[[43359,43359],"valid",[],"NV8"],[[43360,43388],"valid",[],"NV8"],[[43389,43391],"disallowed"],[[43392,43456],"valid"],[[43457,43469],"valid",[],"NV8"],[[43470,43470],"disallowed"],[[43471,43481],"valid"],[[43482,43485],"disallowed"],[[43486,43487],"valid",[],"NV8"],[[43488,43518],"valid"],[[43519,43519],"disallowed"],[[43520,43574],"valid"],[[43575,43583],"disallowed"],[[43584,43597],"valid"],[[43598,43599],"disallowed"],[[43600,43609],"valid"],[[43610,43611],"disallowed"],[[43612,43615],"valid",[],"NV8"],[[43616,43638],"valid"],[[43639,43641],"valid",[],"NV8"],[[43642,43643],"valid"],[[43644,43647],"valid"],[[43648,43714],"valid"],[[43715,43738],"disallowed"],[[43739,43741],"valid"],[[43742,43743],"valid",[],"NV8"],[[43744,43759],"valid"],[[43760,43761],"valid",[],"NV8"],[[43762,43766],"valid"],[[43767,43776],"disallowed"],[[43777,43782],"valid"],[[43783,43784],"disallowed"],[[43785,43790],"valid"],[[43791,43792],"disallowed"],[[43793,43798],"valid"],[[43799,43807],"disallowed"],[[43808,43814],"valid"],[[43815,43815],"disallowed"],[[43816,43822],"valid"],[[43823,43823],"disallowed"],[[43824,43866],"valid"],[[43867,43867],"valid",[],"NV8"],[[43868,43868],"mapped",[42791]],[[43869,43869],"mapped",[43831]],[[43870,43870],"mapped",[619]],[[43871,43871],"mapped",[43858]],[[43872,43875],"valid"],[[43876,43877],"valid"],[[43878,43887],"disallowed"],[[43888,43888],"mapped",[5024]],[[43889,43889],"mapped",[5025]],[[43890,43890],"mapped",[5026]],[[43891,43891],"mapped",[5027]],[[43892,43892],"mapped",[5028]],[[43893,43893],"mapped",[5029]],[[43894,43894],"mapped",[5030]],[[43895,43895],"mapped",[5031]],[[43896,43896],"mapped",[5032]],[[43897,43897],"mapped",[5033]],[[43898,43898],"mapped",[5034]],[[43899,43899],"mapped",[5035]],[[43900,43900],"mapped",[5036]],[[43901,43901],"mapped",[5037]],[[43902,43902],"mapped",[5038]],[[43903,43903],"mapped",[5039]],[[43904,43904],"mapped",[5040]],[[43905,43905],"mapped",[5041]],[[43906,43906],"mapped",[5042]],[[43907,43907],"mapped",[5043]],[[43908,43908],"mapped",[5044]],[[43909,43909],"mapped",[5045]],[[43910,43910],"mapped",[5046]],[[43911,43911],"mapped",[5047]],[[43912,43912],"mapped",[5048]],[[43913,43913],"mapped",[5049]],[[43914,43914],"mapped",[5050]],[[43915,43915],"mapped",[5051]],[[43916,43916],"mapped",[5052]],[[43917,43917],"mapped",[5053]],[[43918,43918],"mapped",[5054]],[[43919,43919],"mapped",[5055]],[[43920,43920],"mapped",[5056]],[[43921,43921],"mapped",[5057]],[[43922,43922],"mapped",[5058]],[[43923,43923],"mapped",[5059]],[[43924,43924],"mapped",[5060]],[[43925,43925],"mapped",[5061]],[[43926,43926],"mapped",[5062]],[[43927,43927],"mapped",[5063]],[[43928,43928],"mapped",[5064]],[[43929,43929],"mapped",[5065]],[[43930,43930],"mapped",[5066]],[[43931,43931],"mapped",[5067]],[[43932,43932],"mapped",[5068]],[[43933,43933],"mapped",[5069]],[[43934,43934],"mapped",[5070]],[[43935,43935],"mapped",[5071]],[[43936,43936],"mapped",[5072]],[[43937,43937],"mapped",[5073]],[[43938,43938],"mapped",[5074]],[[43939,43939],"mapped",[5075]],[[43940,43940],"mapped",[5076]],[[43941,43941],"mapped",[5077]],[[43942,43942],"mapped",[5078]],[[43943,43943],"mapped",[5079]],[[43944,43944],"mapped",[5080]],[[43945,43945],"mapped",[5081]],[[43946,43946],"mapped",[5082]],[[43947,43947],"mapped",[5083]],[[43948,43948],"mapped",[5084]],[[43949,43949],"mapped",[5085]],[[43950,43950],"mapped",[5086]],[[43951,43951],"mapped",[5087]],[[43952,43952],"mapped",[5088]],[[43953,43953],"mapped",[5089]],[[43954,43954],"mapped",[5090]],[[43955,43955],"mapped",[5091]],[[43956,43956],"mapped",[5092]],[[43957,43957],"mapped",[5093]],[[43958,43958],"mapped",[5094]],[[43959,43959],"mapped",[5095]],[[43960,43960],"mapped",[5096]],[[43961,43961],"mapped",[5097]],[[43962,43962],"mapped",[5098]],[[43963,43963],"mapped",[5099]],[[43964,43964],"mapped",[5100]],[[43965,43965],"mapped",[5101]],[[43966,43966],"mapped",[5102]],[[43967,43967],"mapped",[5103]],[[43968,44010],"valid"],[[44011,44011],"valid",[],"NV8"],[[44012,44013],"valid"],[[44014,44015],"disallowed"],[[44016,44025],"valid"],[[44026,44031],"disallowed"],[[44032,55203],"valid"],[[55204,55215],"disallowed"],[[55216,55238],"valid",[],"NV8"],[[55239,55242],"disallowed"],[[55243,55291],"valid",[],"NV8"],[[55292,55295],"disallowed"],[[55296,57343],"disallowed"],[[57344,63743],"disallowed"],[[63744,63744],"mapped",[35912]],[[63745,63745],"mapped",[26356]],[[63746,63746],"mapped",[36554]],[[63747,63747],"mapped",[36040]],[[63748,63748],"mapped",[28369]],[[63749,63749],"mapped",[20018]],[[63750,63750],"mapped",[21477]],[[63751,63752],"mapped",[40860]],[[63753,63753],"mapped",[22865]],[[63754,63754],"mapped",[37329]],[[63755,63755],"mapped",[21895]],[[63756,63756],"mapped",[22856]],[[63757,63757],"mapped",[25078]],[[63758,63758],"mapped",[30313]],[[63759,63759],"mapped",[32645]],[[63760,63760],"mapped",[34367]],[[63761,63761],"mapped",[34746]],[[63762,63762],"mapped",[35064]],[[63763,63763],"mapped",[37007]],[[63764,63764],"mapped",[27138]],[[63765,63765],"mapped",[27931]],[[63766,63766],"mapped",[28889]],[[63767,63767],"mapped",[29662]],[[63768,63768],"mapped",[33853]],[[63769,63769],"mapped",[37226]],[[63770,63770],"mapped",[39409]],[[63771,63771],"mapped",[20098]],[[63772,63772],"mapped",[21365]],[[63773,63773],"mapped",[27396]],[[63774,63774],"mapped",[29211]],[[63775,63775],"mapped",[34349]],[[63776,63776],"mapped",[40478]],[[63777,63777],"mapped",[23888]],[[63778,63778],"mapped",[28651]],[[63779,63779],"mapped",[34253]],[[63780,63780],"mapped",[35172]],[[63781,63781],"mapped",[25289]],[[63782,63782],"mapped",[33240]],[[63783,63783],"mapped",[34847]],[[63784,63784],"mapped",[24266]],[[63785,63785],"mapped",[26391]],[[63786,63786],"mapped",[28010]],[[63787,63787],"mapped",[29436]],[[63788,63788],"mapped",[37070]],[[63789,63789],"mapped",[20358]],[[63790,63790],"mapped",[20919]],[[63791,63791],"mapped",[21214]],[[63792,63792],"mapped",[25796]],[[63793,63793],"mapped",[27347]],[[63794,63794],"mapped",[29200]],[[63795,63795],"mapped",[30439]],[[63796,63796],"mapped",[32769]],[[63797,63797],"mapped",[34310]],[[63798,63798],"mapped",[34396]],[[63799,63799],"mapped",[36335]],[[63800,63800],"mapped",[38706]],[[63801,63801],"mapped",[39791]],[[63802,63802],"mapped",[40442]],[[63803,63803],"mapped",[30860]],[[63804,63804],"mapped",[31103]],[[63805,63805],"mapped",[32160]],[[63806,63806],"mapped",[33737]],[[63807,63807],"mapped",[37636]],[[63808,63808],"mapped",[40575]],[[63809,63809],"mapped",[35542]],[[63810,63810],"mapped",[22751]],[[63811,63811],"mapped",[24324]],[[63812,63812],"mapped",[31840]],[[63813,63813],"mapped",[32894]],[[63814,63814],"mapped",[29282]],[[63815,63815],"mapped",[30922]],[[63816,63816],"mapped",[36034]],[[63817,63817],"mapped",[38647]],[[63818,63818],"mapped",[22744]],[[63819,63819],"mapped",[23650]],[[63820,63820],"mapped",[27155]],[[63821,63821],"mapped",[28122]],[[63822,63822],"mapped",[28431]],[[63823,63823],"mapped",[32047]],[[63824,63824],"mapped",[32311]],[[63825,63825],"mapped",[38475]],[[63826,63826],"mapped",[21202]],[[63827,63827],"mapped",[32907]],[[63828,63828],"mapped",[20956]],[[63829,63829],"mapped",[20940]],[[63830,63830],"mapped",[31260]],[[63831,63831],"mapped",[32190]],[[63832,63832],"mapped",[33777]],[[63833,63833],"mapped",[38517]],[[63834,63834],"mapped",[35712]],[[63835,63835],"mapped",[25295]],[[63836,63836],"mapped",[27138]],[[63837,63837],"mapped",[35582]],[[63838,63838],"mapped",[20025]],[[63839,63839],"mapped",[23527]],[[63840,63840],"mapped",[24594]],[[63841,63841],"mapped",[29575]],[[63842,63842],"mapped",[30064]],[[63843,63843],"mapped",[21271]],[[63844,63844],"mapped",[30971]],[[63845,63845],"mapped",[20415]],[[63846,63846],"mapped",[24489]],[[63847,63847],"mapped",[19981]],[[63848,63848],"mapped",[27852]],[[63849,63849],"mapped",[25976]],[[63850,63850],"mapped",[32034]],[[63851,63851],"mapped",[21443]],[[63852,63852],"mapped",[22622]],[[63853,63853],"mapped",[30465]],[[63854,63854],"mapped",[33865]],[[63855,63855],"mapped",[35498]],[[63856,63856],"mapped",[27578]],[[63857,63857],"mapped",[36784]],[[63858,63858],"mapped",[27784]],[[63859,63859],"mapped",[25342]],[[63860,63860],"mapped",[33509]],[[63861,63861],"mapped",[25504]],[[63862,63862],"mapped",[30053]],[[63863,63863],"mapped",[20142]],[[63864,63864],"mapped",[20841]],[[63865,63865],"mapped",[20937]],[[63866,63866],"mapped",[26753]],[[63867,63867],"mapped",[31975]],[[63868,63868],"mapped",[33391]],[[63869,63869],"mapped",[35538]],[[63870,63870],"mapped",[37327]],[[63871,63871],"mapped",[21237]],[[63872,63872],"mapped",[21570]],[[63873,63873],"mapped",[22899]],[[63874,63874],"mapped",[24300]],[[63875,63875],"mapped",[26053]],[[63876,63876],"mapped",[28670]],[[63877,63877],"mapped",[31018]],[[63878,63878],"mapped",[38317]],[[63879,63879],"mapped",[39530]],[[63880,63880],"mapped",[40599]],[[63881,63881],"mapped",[40654]],[[63882,63882],"mapped",[21147]],[[63883,63883],"mapped",[26310]],[[63884,63884],"mapped",[27511]],[[63885,63885],"mapped",[36706]],[[63886,63886],"mapped",[24180]],[[63887,63887],"mapped",[24976]],[[63888,63888],"mapped",[25088]],[[63889,63889],"mapped",[25754]],[[63890,63890],"mapped",[28451]],[[63891,63891],"mapped",[29001]],[[63892,63892],"mapped",[29833]],[[63893,63893],"mapped",[31178]],[[63894,63894],"mapped",[32244]],[[63895,63895],"mapped",[32879]],[[63896,63896],"mapped",[36646]],[[63897,63897],"mapped",[34030]],[[63898,63898],"mapped",[36899]],[[63899,63899],"mapped",[37706]],[[63900,63900],"mapped",[21015]],[[63901,63901],"mapped",[21155]],[[63902,63902],"mapped",[21693]],[[63903,63903],"mapped",[28872]],[[63904,63904],"mapped",[35010]],[[63905,63905],"mapped",[35498]],[[63906,63906],"mapped",[24265]],[[63907,63907],"mapped",[24565]],[[63908,63908],"mapped",[25467]],[[63909,63909],"mapped",[27566]],[[63910,63910],"mapped",[31806]],[[63911,63911],"mapped",[29557]],[[63912,63912],"mapped",[20196]],[[63913,63913],"mapped",[22265]],[[63914,63914],"mapped",[23527]],[[63915,63915],"mapped",[23994]],[[63916,63916],"mapped",[24604]],[[63917,63917],"mapped",[29618]],[[63918,63918],"mapped",[29801]],[[63919,63919],"mapped",[32666]],[[63920,63920],"mapped",[32838]],[[63921,63921],"mapped",[37428]],[[63922,63922],"mapped",[38646]],[[63923,63923],"mapped",[38728]],[[63924,63924],"mapped",[38936]],[[63925,63925],"mapped",[20363]],[[63926,63926],"mapped",[31150]],[[63927,63927],"mapped",[37300]],[[63928,63928],"mapped",[38584]],[[63929,63929],"mapped",[24801]],[[63930,63930],"mapped",[20102]],[[63931,63931],"mapped",[20698]],[[63932,63932],"mapped",[23534]],[[63933,63933],"mapped",[23615]],[[63934,63934],"mapped",[26009]],[[63935,63935],"mapped",[27138]],[[63936,63936],"mapped",[29134]],[[63937,63937],"mapped",[30274]],[[63938,63938],"mapped",[34044]],[[63939,63939],"mapped",[36988]],[[63940,63940],"mapped",[40845]],[[63941,63941],"mapped",[26248]],[[63942,63942],"mapped",[38446]],[[63943,63943],"mapped",[21129]],[[63944,63944],"mapped",[26491]],[[63945,63945],"mapped",[26611]],[[63946,63946],"mapped",[27969]],[[63947,63947],"mapped",[28316]],[[63948,63948],"mapped",[29705]],[[63949,63949],"mapped",[30041]],[[63950,63950],"mapped",[30827]],[[63951,63951],"mapped",[32016]],[[63952,63952],"mapped",[39006]],[[63953,63953],"mapped",[20845]],[[63954,63954],"mapped",[25134]],[[63955,63955],"mapped",[38520]],[[63956,63956],"mapped",[20523]],[[63957,63957],"mapped",[23833]],[[63958,63958],"mapped",[28138]],[[63959,63959],"mapped",[36650]],[[63960,63960],"mapped",[24459]],[[63961,63961],"mapped",[24900]],[[63962,63962],"mapped",[26647]],[[63963,63963],"mapped",[29575]],[[63964,63964],"mapped",[38534]],[[63965,63965],"mapped",[21033]],[[63966,63966],"mapped",[21519]],[[63967,63967],"mapped",[23653]],[[63968,63968],"mapped",[26131]],[[63969,63969],"mapped",[26446]],[[63970,63970],"mapped",[26792]],[[63971,63971],"mapped",[27877]],[[63972,63972],"mapped",[29702]],[[63973,63973],"mapped",[30178]],[[63974,63974],"mapped",[32633]],[[63975,63975],"mapped",[35023]],[[63976,63976],"mapped",[35041]],[[63977,63977],"mapped",[37324]],[[63978,63978],"mapped",[38626]],[[63979,63979],"mapped",[21311]],[[63980,63980],"mapped",[28346]],[[63981,63981],"mapped",[21533]],[[63982,63982],"mapped",[29136]],[[63983,63983],"mapped",[29848]],[[63984,63984],"mapped",[34298]],[[63985,63985],"mapped",[38563]],[[63986,63986],"mapped",[40023]],[[63987,63987],"mapped",[40607]],[[63988,63988],"mapped",[26519]],[[63989,63989],"mapped",[28107]],[[63990,63990],"mapped",[33256]],[[63991,63991],"mapped",[31435]],[[63992,63992],"mapped",[31520]],[[63993,63993],"mapped",[31890]],[[63994,63994],"mapped",[29376]],[[63995,63995],"mapped",[28825]],[[63996,63996],"mapped",[35672]],[[63997,63997],"mapped",[20160]],[[63998,63998],"mapped",[33590]],[[63999,63999],"mapped",[21050]],[[64e3,64e3],"mapped",[20999]],[[64001,64001],"mapped",[24230]],[[64002,64002],"mapped",[25299]],[[64003,64003],"mapped",[31958]],[[64004,64004],"mapped",[23429]],[[64005,64005],"mapped",[27934]],[[64006,64006],"mapped",[26292]],[[64007,64007],"mapped",[36667]],[[64008,64008],"mapped",[34892]],[[64009,64009],"mapped",[38477]],[[64010,64010],"mapped",[35211]],[[64011,64011],"mapped",[24275]],[[64012,64012],"mapped",[20800]],[[64013,64013],"mapped",[21952]],[[64014,64015],"valid"],[[64016,64016],"mapped",[22618]],[[64017,64017],"valid"],[[64018,64018],"mapped",[26228]],[[64019,64020],"valid"],[[64021,64021],"mapped",[20958]],[[64022,64022],"mapped",[29482]],[[64023,64023],"mapped",[30410]],[[64024,64024],"mapped",[31036]],[[64025,64025],"mapped",[31070]],[[64026,64026],"mapped",[31077]],[[64027,64027],"mapped",[31119]],[[64028,64028],"mapped",[38742]],[[64029,64029],"mapped",[31934]],[[64030,64030],"mapped",[32701]],[[64031,64031],"valid"],[[64032,64032],"mapped",[34322]],[[64033,64033],"valid"],[[64034,64034],"mapped",[35576]],[[64035,64036],"valid"],[[64037,64037],"mapped",[36920]],[[64038,64038],"mapped",[37117]],[[64039,64041],"valid"],[[64042,64042],"mapped",[39151]],[[64043,64043],"mapped",[39164]],[[64044,64044],"mapped",[39208]],[[64045,64045],"mapped",[40372]],[[64046,64046],"mapped",[37086]],[[64047,64047],"mapped",[38583]],[[64048,64048],"mapped",[20398]],[[64049,64049],"mapped",[20711]],[[64050,64050],"mapped",[20813]],[[64051,64051],"mapped",[21193]],[[64052,64052],"mapped",[21220]],[[64053,64053],"mapped",[21329]],[[64054,64054],"mapped",[21917]],[[64055,64055],"mapped",[22022]],[[64056,64056],"mapped",[22120]],[[64057,64057],"mapped",[22592]],[[64058,64058],"mapped",[22696]],[[64059,64059],"mapped",[23652]],[[64060,64060],"mapped",[23662]],[[64061,64061],"mapped",[24724]],[[64062,64062],"mapped",[24936]],[[64063,64063],"mapped",[24974]],[[64064,64064],"mapped",[25074]],[[64065,64065],"mapped",[25935]],[[64066,64066],"mapped",[26082]],[[64067,64067],"mapped",[26257]],[[64068,64068],"mapped",[26757]],[[64069,64069],"mapped",[28023]],[[64070,64070],"mapped",[28186]],[[64071,64071],"mapped",[28450]],[[64072,64072],"mapped",[29038]],[[64073,64073],"mapped",[29227]],[[64074,64074],"mapped",[29730]],[[64075,64075],"mapped",[30865]],[[64076,64076],"mapped",[31038]],[[64077,64077],"mapped",[31049]],[[64078,64078],"mapped",[31048]],[[64079,64079],"mapped",[31056]],[[64080,64080],"mapped",[31062]],[[64081,64081],"mapped",[31069]],[[64082,64082],"mapped",[31117]],[[64083,64083],"mapped",[31118]],[[64084,64084],"mapped",[31296]],[[64085,64085],"mapped",[31361]],[[64086,64086],"mapped",[31680]],[[64087,64087],"mapped",[32244]],[[64088,64088],"mapped",[32265]],[[64089,64089],"mapped",[32321]],[[64090,64090],"mapped",[32626]],[[64091,64091],"mapped",[32773]],[[64092,64092],"mapped",[33261]],[[64093,64094],"mapped",[33401]],[[64095,64095],"mapped",[33879]],[[64096,64096],"mapped",[35088]],[[64097,64097],"mapped",[35222]],[[64098,64098],"mapped",[35585]],[[64099,64099],"mapped",[35641]],[[64100,64100],"mapped",[36051]],[[64101,64101],"mapped",[36104]],[[64102,64102],"mapped",[36790]],[[64103,64103],"mapped",[36920]],[[64104,64104],"mapped",[38627]],[[64105,64105],"mapped",[38911]],[[64106,64106],"mapped",[38971]],[[64107,64107],"mapped",[24693]],[[64108,64108],"mapped",[148206]],[[64109,64109],"mapped",[33304]],[[64110,64111],"disallowed"],[[64112,64112],"mapped",[20006]],[[64113,64113],"mapped",[20917]],[[64114,64114],"mapped",[20840]],[[64115,64115],"mapped",[20352]],[[64116,64116],"mapped",[20805]],[[64117,64117],"mapped",[20864]],[[64118,64118],"mapped",[21191]],[[64119,64119],"mapped",[21242]],[[64120,64120],"mapped",[21917]],[[64121,64121],"mapped",[21845]],[[64122,64122],"mapped",[21913]],[[64123,64123],"mapped",[21986]],[[64124,64124],"mapped",[22618]],[[64125,64125],"mapped",[22707]],[[64126,64126],"mapped",[22852]],[[64127,64127],"mapped",[22868]],[[64128,64128],"mapped",[23138]],[[64129,64129],"mapped",[23336]],[[64130,64130],"mapped",[24274]],[[64131,64131],"mapped",[24281]],[[64132,64132],"mapped",[24425]],[[64133,64133],"mapped",[24493]],[[64134,64134],"mapped",[24792]],[[64135,64135],"mapped",[24910]],[[64136,64136],"mapped",[24840]],[[64137,64137],"mapped",[24974]],[[64138,64138],"mapped",[24928]],[[64139,64139],"mapped",[25074]],[[64140,64140],"mapped",[25140]],[[64141,64141],"mapped",[25540]],[[64142,64142],"mapped",[25628]],[[64143,64143],"mapped",[25682]],[[64144,64144],"mapped",[25942]],[[64145,64145],"mapped",[26228]],[[64146,64146],"mapped",[26391]],[[64147,64147],"mapped",[26395]],[[64148,64148],"mapped",[26454]],[[64149,64149],"mapped",[27513]],[[64150,64150],"mapped",[27578]],[[64151,64151],"mapped",[27969]],[[64152,64152],"mapped",[28379]],[[64153,64153],"mapped",[28363]],[[64154,64154],"mapped",[28450]],[[64155,64155],"mapped",[28702]],[[64156,64156],"mapped",[29038]],[[64157,64157],"mapped",[30631]],[[64158,64158],"mapped",[29237]],[[64159,64159],"mapped",[29359]],[[64160,64160],"mapped",[29482]],[[64161,64161],"mapped",[29809]],[[64162,64162],"mapped",[29958]],[[64163,64163],"mapped",[30011]],[[64164,64164],"mapped",[30237]],[[64165,64165],"mapped",[30239]],[[64166,64166],"mapped",[30410]],[[64167,64167],"mapped",[30427]],[[64168,64168],"mapped",[30452]],[[64169,64169],"mapped",[30538]],[[64170,64170],"mapped",[30528]],[[64171,64171],"mapped",[30924]],[[64172,64172],"mapped",[31409]],[[64173,64173],"mapped",[31680]],[[64174,64174],"mapped",[31867]],[[64175,64175],"mapped",[32091]],[[64176,64176],"mapped",[32244]],[[64177,64177],"mapped",[32574]],[[64178,64178],"mapped",[32773]],[[64179,64179],"mapped",[33618]],[[64180,64180],"mapped",[33775]],[[64181,64181],"mapped",[34681]],[[64182,64182],"mapped",[35137]],[[64183,64183],"mapped",[35206]],[[64184,64184],"mapped",[35222]],[[64185,64185],"mapped",[35519]],[[64186,64186],"mapped",[35576]],[[64187,64187],"mapped",[35531]],[[64188,64188],"mapped",[35585]],[[64189,64189],"mapped",[35582]],[[64190,64190],"mapped",[35565]],[[64191,64191],"mapped",[35641]],[[64192,64192],"mapped",[35722]],[[64193,64193],"mapped",[36104]],[[64194,64194],"mapped",[36664]],[[64195,64195],"mapped",[36978]],[[64196,64196],"mapped",[37273]],[[64197,64197],"mapped",[37494]],[[64198,64198],"mapped",[38524]],[[64199,64199],"mapped",[38627]],[[64200,64200],"mapped",[38742]],[[64201,64201],"mapped",[38875]],[[64202,64202],"mapped",[38911]],[[64203,64203],"mapped",[38923]],[[64204,64204],"mapped",[38971]],[[64205,64205],"mapped",[39698]],[[64206,64206],"mapped",[40860]],[[64207,64207],"mapped",[141386]],[[64208,64208],"mapped",[141380]],[[64209,64209],"mapped",[144341]],[[64210,64210],"mapped",[15261]],[[64211,64211],"mapped",[16408]],[[64212,64212],"mapped",[16441]],[[64213,64213],"mapped",[152137]],[[64214,64214],"mapped",[154832]],[[64215,64215],"mapped",[163539]],[[64216,64216],"mapped",[40771]],[[64217,64217],"mapped",[40846]],[[64218,64255],"disallowed"],[[64256,64256],"mapped",[102,102]],[[64257,64257],"mapped",[102,105]],[[64258,64258],"mapped",[102,108]],[[64259,64259],"mapped",[102,102,105]],[[64260,64260],"mapped",[102,102,108]],[[64261,64262],"mapped",[115,116]],[[64263,64274],"disallowed"],[[64275,64275],"mapped",[1396,1398]],[[64276,64276],"mapped",[1396,1381]],[[64277,64277],"mapped",[1396,1387]],[[64278,64278],"mapped",[1406,1398]],[[64279,64279],"mapped",[1396,1389]],[[64280,64284],"disallowed"],[[64285,64285],"mapped",[1497,1460]],[[64286,64286],"valid"],[[64287,64287],"mapped",[1522,1463]],[[64288,64288],"mapped",[1506]],[[64289,64289],"mapped",[1488]],[[64290,64290],"mapped",[1491]],[[64291,64291],"mapped",[1492]],[[64292,64292],"mapped",[1499]],[[64293,64293],"mapped",[1500]],[[64294,64294],"mapped",[1501]],[[64295,64295],"mapped",[1512]],[[64296,64296],"mapped",[1514]],[[64297,64297],"disallowed_STD3_mapped",[43]],[[64298,64298],"mapped",[1513,1473]],[[64299,64299],"mapped",[1513,1474]],[[64300,64300],"mapped",[1513,1468,1473]],[[64301,64301],"mapped",[1513,1468,1474]],[[64302,64302],"mapped",[1488,1463]],[[64303,64303],"mapped",[1488,1464]],[[64304,64304],"mapped",[1488,1468]],[[64305,64305],"mapped",[1489,1468]],[[64306,64306],"mapped",[1490,1468]],[[64307,64307],"mapped",[1491,1468]],[[64308,64308],"mapped",[1492,1468]],[[64309,64309],"mapped",[1493,1468]],[[64310,64310],"mapped",[1494,1468]],[[64311,64311],"disallowed"],[[64312,64312],"mapped",[1496,1468]],[[64313,64313],"mapped",[1497,1468]],[[64314,64314],"mapped",[1498,1468]],[[64315,64315],"mapped",[1499,1468]],[[64316,64316],"mapped",[1500,1468]],[[64317,64317],"disallowed"],[[64318,64318],"mapped",[1502,1468]],[[64319,64319],"disallowed"],[[64320,64320],"mapped",[1504,1468]],[[64321,64321],"mapped",[1505,1468]],[[64322,64322],"disallowed"],[[64323,64323],"mapped",[1507,1468]],[[64324,64324],"mapped",[1508,1468]],[[64325,64325],"disallowed"],[[64326,64326],"mapped",[1510,1468]],[[64327,64327],"mapped",[1511,1468]],[[64328,64328],"mapped",[1512,1468]],[[64329,64329],"mapped",[1513,1468]],[[64330,64330],"mapped",[1514,1468]],[[64331,64331],"mapped",[1493,1465]],[[64332,64332],"mapped",[1489,1471]],[[64333,64333],"mapped",[1499,1471]],[[64334,64334],"mapped",[1508,1471]],[[64335,64335],"mapped",[1488,1500]],[[64336,64337],"mapped",[1649]],[[64338,64341],"mapped",[1659]],[[64342,64345],"mapped",[1662]],[[64346,64349],"mapped",[1664]],[[64350,64353],"mapped",[1658]],[[64354,64357],"mapped",[1663]],[[64358,64361],"mapped",[1657]],[[64362,64365],"mapped",[1700]],[[64366,64369],"mapped",[1702]],[[64370,64373],"mapped",[1668]],[[64374,64377],"mapped",[1667]],[[64378,64381],"mapped",[1670]],[[64382,64385],"mapped",[1671]],[[64386,64387],"mapped",[1677]],[[64388,64389],"mapped",[1676]],[[64390,64391],"mapped",[1678]],[[64392,64393],"mapped",[1672]],[[64394,64395],"mapped",[1688]],[[64396,64397],"mapped",[1681]],[[64398,64401],"mapped",[1705]],[[64402,64405],"mapped",[1711]],[[64406,64409],"mapped",[1715]],[[64410,64413],"mapped",[1713]],[[64414,64415],"mapped",[1722]],[[64416,64419],"mapped",[1723]],[[64420,64421],"mapped",[1728]],[[64422,64425],"mapped",[1729]],[[64426,64429],"mapped",[1726]],[[64430,64431],"mapped",[1746]],[[64432,64433],"mapped",[1747]],[[64434,64449],"valid",[],"NV8"],[[64450,64466],"disallowed"],[[64467,64470],"mapped",[1709]],[[64471,64472],"mapped",[1735]],[[64473,64474],"mapped",[1734]],[[64475,64476],"mapped",[1736]],[[64477,64477],"mapped",[1735,1652]],[[64478,64479],"mapped",[1739]],[[64480,64481],"mapped",[1733]],[[64482,64483],"mapped",[1737]],[[64484,64487],"mapped",[1744]],[[64488,64489],"mapped",[1609]],[[64490,64491],"mapped",[1574,1575]],[[64492,64493],"mapped",[1574,1749]],[[64494,64495],"mapped",[1574,1608]],[[64496,64497],"mapped",[1574,1735]],[[64498,64499],"mapped",[1574,1734]],[[64500,64501],"mapped",[1574,1736]],[[64502,64504],"mapped",[1574,1744]],[[64505,64507],"mapped",[1574,1609]],[[64508,64511],"mapped",[1740]],[[64512,64512],"mapped",[1574,1580]],[[64513,64513],"mapped",[1574,1581]],[[64514,64514],"mapped",[1574,1605]],[[64515,64515],"mapped",[1574,1609]],[[64516,64516],"mapped",[1574,1610]],[[64517,64517],"mapped",[1576,1580]],[[64518,64518],"mapped",[1576,1581]],[[64519,64519],"mapped",[1576,1582]],[[64520,64520],"mapped",[1576,1605]],[[64521,64521],"mapped",[1576,1609]],[[64522,64522],"mapped",[1576,1610]],[[64523,64523],"mapped",[1578,1580]],[[64524,64524],"mapped",[1578,1581]],[[64525,64525],"mapped",[1578,1582]],[[64526,64526],"mapped",[1578,1605]],[[64527,64527],"mapped",[1578,1609]],[[64528,64528],"mapped",[1578,1610]],[[64529,64529],"mapped",[1579,1580]],[[64530,64530],"mapped",[1579,1605]],[[64531,64531],"mapped",[1579,1609]],[[64532,64532],"mapped",[1579,1610]],[[64533,64533],"mapped",[1580,1581]],[[64534,64534],"mapped",[1580,1605]],[[64535,64535],"mapped",[1581,1580]],[[64536,64536],"mapped",[1581,1605]],[[64537,64537],"mapped",[1582,1580]],[[64538,64538],"mapped",[1582,1581]],[[64539,64539],"mapped",[1582,1605]],[[64540,64540],"mapped",[1587,1580]],[[64541,64541],"mapped",[1587,1581]],[[64542,64542],"mapped",[1587,1582]],[[64543,64543],"mapped",[1587,1605]],[[64544,64544],"mapped",[1589,1581]],[[64545,64545],"mapped",[1589,1605]],[[64546,64546],"mapped",[1590,1580]],[[64547,64547],"mapped",[1590,1581]],[[64548,64548],"mapped",[1590,1582]],[[64549,64549],"mapped",[1590,1605]],[[64550,64550],"mapped",[1591,1581]],[[64551,64551],"mapped",[1591,1605]],[[64552,64552],"mapped",[1592,1605]],[[64553,64553],"mapped",[1593,1580]],[[64554,64554],"mapped",[1593,1605]],[[64555,64555],"mapped",[1594,1580]],[[64556,64556],"mapped",[1594,1605]],[[64557,64557],"mapped",[1601,1580]],[[64558,64558],"mapped",[1601,1581]],[[64559,64559],"mapped",[1601,1582]],[[64560,64560],"mapped",[1601,1605]],[[64561,64561],"mapped",[1601,1609]],[[64562,64562],"mapped",[1601,1610]],[[64563,64563],"mapped",[1602,1581]],[[64564,64564],"mapped",[1602,1605]],[[64565,64565],"mapped",[1602,1609]],[[64566,64566],"mapped",[1602,1610]],[[64567,64567],"mapped",[1603,1575]],[[64568,64568],"mapped",[1603,1580]],[[64569,64569],"mapped",[1603,1581]],[[64570,64570],"mapped",[1603,1582]],[[64571,64571],"mapped",[1603,1604]],[[64572,64572],"mapped",[1603,1605]],[[64573,64573],"mapped",[1603,1609]],[[64574,64574],"mapped",[1603,1610]],[[64575,64575],"mapped",[1604,1580]],[[64576,64576],"mapped",[1604,1581]],[[64577,64577],"mapped",[1604,1582]],[[64578,64578],"mapped",[1604,1605]],[[64579,64579],"mapped",[1604,1609]],[[64580,64580],"mapped",[1604,1610]],[[64581,64581],"mapped",[1605,1580]],[[64582,64582],"mapped",[1605,1581]],[[64583,64583],"mapped",[1605,1582]],[[64584,64584],"mapped",[1605,1605]],[[64585,64585],"mapped",[1605,1609]],[[64586,64586],"mapped",[1605,1610]],[[64587,64587],"mapped",[1606,1580]],[[64588,64588],"mapped",[1606,1581]],[[64589,64589],"mapped",[1606,1582]],[[64590,64590],"mapped",[1606,1605]],[[64591,64591],"mapped",[1606,1609]],[[64592,64592],"mapped",[1606,1610]],[[64593,64593],"mapped",[1607,1580]],[[64594,64594],"mapped",[1607,1605]],[[64595,64595],"mapped",[1607,1609]],[[64596,64596],"mapped",[1607,1610]],[[64597,64597],"mapped",[1610,1580]],[[64598,64598],"mapped",[1610,1581]],[[64599,64599],"mapped",[1610,1582]],[[64600,64600],"mapped",[1610,1605]],[[64601,64601],"mapped",[1610,1609]],[[64602,64602],"mapped",[1610,1610]],[[64603,64603],"mapped",[1584,1648]],[[64604,64604],"mapped",[1585,1648]],[[64605,64605],"mapped",[1609,1648]],[[64606,64606],"disallowed_STD3_mapped",[32,1612,1617]],[[64607,64607],"disallowed_STD3_mapped",[32,1613,1617]],[[64608,64608],"disallowed_STD3_mapped",[32,1614,1617]],[[64609,64609],"disallowed_STD3_mapped",[32,1615,1617]],[[64610,64610],"disallowed_STD3_mapped",[32,1616,1617]],[[64611,64611],"disallowed_STD3_mapped",[32,1617,1648]],[[64612,64612],"mapped",[1574,1585]],[[64613,64613],"mapped",[1574,1586]],[[64614,64614],"mapped",[1574,1605]],[[64615,64615],"mapped",[1574,1606]],[[64616,64616],"mapped",[1574,1609]],[[64617,64617],"mapped",[1574,1610]],[[64618,64618],"mapped",[1576,1585]],[[64619,64619],"mapped",[1576,1586]],[[64620,64620],"mapped",[1576,1605]],[[64621,64621],"mapped",[1576,1606]],[[64622,64622],"mapped",[1576,1609]],[[64623,64623],"mapped",[1576,1610]],[[64624,64624],"mapped",[1578,1585]],[[64625,64625],"mapped",[1578,1586]],[[64626,64626],"mapped",[1578,1605]],[[64627,64627],"mapped",[1578,1606]],[[64628,64628],"mapped",[1578,1609]],[[64629,64629],"mapped",[1578,1610]],[[64630,64630],"mapped",[1579,1585]],[[64631,64631],"mapped",[1579,1586]],[[64632,64632],"mapped",[1579,1605]],[[64633,64633],"mapped",[1579,1606]],[[64634,64634],"mapped",[1579,1609]],[[64635,64635],"mapped",[1579,1610]],[[64636,64636],"mapped",[1601,1609]],[[64637,64637],"mapped",[1601,1610]],[[64638,64638],"mapped",[1602,1609]],[[64639,64639],"mapped",[1602,1610]],[[64640,64640],"mapped",[1603,1575]],[[64641,64641],"mapped",[1603,1604]],[[64642,64642],"mapped",[1603,1605]],[[64643,64643],"mapped",[1603,1609]],[[64644,64644],"mapped",[1603,1610]],[[64645,64645],"mapped",[1604,1605]],[[64646,64646],"mapped",[1604,1609]],[[64647,64647],"mapped",[1604,1610]],[[64648,64648],"mapped",[1605,1575]],[[64649,64649],"mapped",[1605,1605]],[[64650,64650],"mapped",[1606,1585]],[[64651,64651],"mapped",[1606,1586]],[[64652,64652],"mapped",[1606,1605]],[[64653,64653],"mapped",[1606,1606]],[[64654,64654],"mapped",[1606,1609]],[[64655,64655],"mapped",[1606,1610]],[[64656,64656],"mapped",[1609,1648]],[[64657,64657],"mapped",[1610,1585]],[[64658,64658],"mapped",[1610,1586]],[[64659,64659],"mapped",[1610,1605]],[[64660,64660],"mapped",[1610,1606]],[[64661,64661],"mapped",[1610,1609]],[[64662,64662],"mapped",[1610,1610]],[[64663,64663],"mapped",[1574,1580]],[[64664,64664],"mapped",[1574,1581]],[[64665,64665],"mapped",[1574,1582]],[[64666,64666],"mapped",[1574,1605]],[[64667,64667],"mapped",[1574,1607]],[[64668,64668],"mapped",[1576,1580]],[[64669,64669],"mapped",[1576,1581]],[[64670,64670],"mapped",[1576,1582]],[[64671,64671],"mapped",[1576,1605]],[[64672,64672],"mapped",[1576,1607]],[[64673,64673],"mapped",[1578,1580]],[[64674,64674],"mapped",[1578,1581]],[[64675,64675],"mapped",[1578,1582]],[[64676,64676],"mapped",[1578,1605]],[[64677,64677],"mapped",[1578,1607]],[[64678,64678],"mapped",[1579,1605]],[[64679,64679],"mapped",[1580,1581]],[[64680,64680],"mapped",[1580,1605]],[[64681,64681],"mapped",[1581,1580]],[[64682,64682],"mapped",[1581,1605]],[[64683,64683],"mapped",[1582,1580]],[[64684,64684],"mapped",[1582,1605]],[[64685,64685],"mapped",[1587,1580]],[[64686,64686],"mapped",[1587,1581]],[[64687,64687],"mapped",[1587,1582]],[[64688,64688],"mapped",[1587,1605]],[[64689,64689],"mapped",[1589,1581]],[[64690,64690],"mapped",[1589,1582]],[[64691,64691],"mapped",[1589,1605]],[[64692,64692],"mapped",[1590,1580]],[[64693,64693],"mapped",[1590,1581]],[[64694,64694],"mapped",[1590,1582]],[[64695,64695],"mapped",[1590,1605]],[[64696,64696],"mapped",[1591,1581]],[[64697,64697],"mapped",[1592,1605]],[[64698,64698],"mapped",[1593,1580]],[[64699,64699],"mapped",[1593,1605]],[[64700,64700],"mapped",[1594,1580]],[[64701,64701],"mapped",[1594,1605]],[[64702,64702],"mapped",[1601,1580]],[[64703,64703],"mapped",[1601,1581]],[[64704,64704],"mapped",[1601,1582]],[[64705,64705],"mapped",[1601,1605]],[[64706,64706],"mapped",[1602,1581]],[[64707,64707],"mapped",[1602,1605]],[[64708,64708],"mapped",[1603,1580]],[[64709,64709],"mapped",[1603,1581]],[[64710,64710],"mapped",[1603,1582]],[[64711,64711],"mapped",[1603,1604]],[[64712,64712],"mapped",[1603,1605]],[[64713,64713],"mapped",[1604,1580]],[[64714,64714],"mapped",[1604,1581]],[[64715,64715],"mapped",[1604,1582]],[[64716,64716],"mapped",[1604,1605]],[[64717,64717],"mapped",[1604,1607]],[[64718,64718],"mapped",[1605,1580]],[[64719,64719],"mapped",[1605,1581]],[[64720,64720],"mapped",[1605,1582]],[[64721,64721],"mapped",[1605,1605]],[[64722,64722],"mapped",[1606,1580]],[[64723,64723],"mapped",[1606,1581]],[[64724,64724],"mapped",[1606,1582]],[[64725,64725],"mapped",[1606,1605]],[[64726,64726],"mapped",[1606,1607]],[[64727,64727],"mapped",[1607,1580]],[[64728,64728],"mapped",[1607,1605]],[[64729,64729],"mapped",[1607,1648]],[[64730,64730],"mapped",[1610,1580]],[[64731,64731],"mapped",[1610,1581]],[[64732,64732],"mapped",[1610,1582]],[[64733,64733],"mapped",[1610,1605]],[[64734,64734],"mapped",[1610,1607]],[[64735,64735],"mapped",[1574,1605]],[[64736,64736],"mapped",[1574,1607]],[[64737,64737],"mapped",[1576,1605]],[[64738,64738],"mapped",[1576,1607]],[[64739,64739],"mapped",[1578,1605]],[[64740,64740],"mapped",[1578,1607]],[[64741,64741],"mapped",[1579,1605]],[[64742,64742],"mapped",[1579,1607]],[[64743,64743],"mapped",[1587,1605]],[[64744,64744],"mapped",[1587,1607]],[[64745,64745],"mapped",[1588,1605]],[[64746,64746],"mapped",[1588,1607]],[[64747,64747],"mapped",[1603,1604]],[[64748,64748],"mapped",[1603,1605]],[[64749,64749],"mapped",[1604,1605]],[[64750,64750],"mapped",[1606,1605]],[[64751,64751],"mapped",[1606,1607]],[[64752,64752],"mapped",[1610,1605]],[[64753,64753],"mapped",[1610,1607]],[[64754,64754],"mapped",[1600,1614,1617]],[[64755,64755],"mapped",[1600,1615,1617]],[[64756,64756],"mapped",[1600,1616,1617]],[[64757,64757],"mapped",[1591,1609]],[[64758,64758],"mapped",[1591,1610]],[[64759,64759],"mapped",[1593,1609]],[[64760,64760],"mapped",[1593,1610]],[[64761,64761],"mapped",[1594,1609]],[[64762,64762],"mapped",[1594,1610]],[[64763,64763],"mapped",[1587,1609]],[[64764,64764],"mapped",[1587,1610]],[[64765,64765],"mapped",[1588,1609]],[[64766,64766],"mapped",[1588,1610]],[[64767,64767],"mapped",[1581,1609]],[[64768,64768],"mapped",[1581,1610]],[[64769,64769],"mapped",[1580,1609]],[[64770,64770],"mapped",[1580,1610]],[[64771,64771],"mapped",[1582,1609]],[[64772,64772],"mapped",[1582,1610]],[[64773,64773],"mapped",[1589,1609]],[[64774,64774],"mapped",[1589,1610]],[[64775,64775],"mapped",[1590,1609]],[[64776,64776],"mapped",[1590,1610]],[[64777,64777],"mapped",[1588,1580]],[[64778,64778],"mapped",[1588,1581]],[[64779,64779],"mapped",[1588,1582]],[[64780,64780],"mapped",[1588,1605]],[[64781,64781],"mapped",[1588,1585]],[[64782,64782],"mapped",[1587,1585]],[[64783,64783],"mapped",[1589,1585]],[[64784,64784],"mapped",[1590,1585]],[[64785,64785],"mapped",[1591,1609]],[[64786,64786],"mapped",[1591,1610]],[[64787,64787],"mapped",[1593,1609]],[[64788,64788],"mapped",[1593,1610]],[[64789,64789],"mapped",[1594,1609]],[[64790,64790],"mapped",[1594,1610]],[[64791,64791],"mapped",[1587,1609]],[[64792,64792],"mapped",[1587,1610]],[[64793,64793],"mapped",[1588,1609]],[[64794,64794],"mapped",[1588,1610]],[[64795,64795],"mapped",[1581,1609]],[[64796,64796],"mapped",[1581,1610]],[[64797,64797],"mapped",[1580,1609]],[[64798,64798],"mapped",[1580,1610]],[[64799,64799],"mapped",[1582,1609]],[[64800,64800],"mapped",[1582,1610]],[[64801,64801],"mapped",[1589,1609]],[[64802,64802],"mapped",[1589,1610]],[[64803,64803],"mapped",[1590,1609]],[[64804,64804],"mapped",[1590,1610]],[[64805,64805],"mapped",[1588,1580]],[[64806,64806],"mapped",[1588,1581]],[[64807,64807],"mapped",[1588,1582]],[[64808,64808],"mapped",[1588,1605]],[[64809,64809],"mapped",[1588,1585]],[[64810,64810],"mapped",[1587,1585]],[[64811,64811],"mapped",[1589,1585]],[[64812,64812],"mapped",[1590,1585]],[[64813,64813],"mapped",[1588,1580]],[[64814,64814],"mapped",[1588,1581]],[[64815,64815],"mapped",[1588,1582]],[[64816,64816],"mapped",[1588,1605]],[[64817,64817],"mapped",[1587,1607]],[[64818,64818],"mapped",[1588,1607]],[[64819,64819],"mapped",[1591,1605]],[[64820,64820],"mapped",[1587,1580]],[[64821,64821],"mapped",[1587,1581]],[[64822,64822],"mapped",[1587,1582]],[[64823,64823],"mapped",[1588,1580]],[[64824,64824],"mapped",[1588,1581]],[[64825,64825],"mapped",[1588,1582]],[[64826,64826],"mapped",[1591,1605]],[[64827,64827],"mapped",[1592,1605]],[[64828,64829],"mapped",[1575,1611]],[[64830,64831],"valid",[],"NV8"],[[64832,64847],"disallowed"],[[64848,64848],"mapped",[1578,1580,1605]],[[64849,64850],"mapped",[1578,1581,1580]],[[64851,64851],"mapped",[1578,1581,1605]],[[64852,64852],"mapped",[1578,1582,1605]],[[64853,64853],"mapped",[1578,1605,1580]],[[64854,64854],"mapped",[1578,1605,1581]],[[64855,64855],"mapped",[1578,1605,1582]],[[64856,64857],"mapped",[1580,1605,1581]],[[64858,64858],"mapped",[1581,1605,1610]],[[64859,64859],"mapped",[1581,1605,1609]],[[64860,64860],"mapped",[1587,1581,1580]],[[64861,64861],"mapped",[1587,1580,1581]],[[64862,64862],"mapped",[1587,1580,1609]],[[64863,64864],"mapped",[1587,1605,1581]],[[64865,64865],"mapped",[1587,1605,1580]],[[64866,64867],"mapped",[1587,1605,1605]],[[64868,64869],"mapped",[1589,1581,1581]],[[64870,64870],"mapped",[1589,1605,1605]],[[64871,64872],"mapped",[1588,1581,1605]],[[64873,64873],"mapped",[1588,1580,1610]],[[64874,64875],"mapped",[1588,1605,1582]],[[64876,64877],"mapped",[1588,1605,1605]],[[64878,64878],"mapped",[1590,1581,1609]],[[64879,64880],"mapped",[1590,1582,1605]],[[64881,64882],"mapped",[1591,1605,1581]],[[64883,64883],"mapped",[1591,1605,1605]],[[64884,64884],"mapped",[1591,1605,1610]],[[64885,64885],"mapped",[1593,1580,1605]],[[64886,64887],"mapped",[1593,1605,1605]],[[64888,64888],"mapped",[1593,1605,1609]],[[64889,64889],"mapped",[1594,1605,1605]],[[64890,64890],"mapped",[1594,1605,1610]],[[64891,64891],"mapped",[1594,1605,1609]],[[64892,64893],"mapped",[1601,1582,1605]],[[64894,64894],"mapped",[1602,1605,1581]],[[64895,64895],"mapped",[1602,1605,1605]],[[64896,64896],"mapped",[1604,1581,1605]],[[64897,64897],"mapped",[1604,1581,1610]],[[64898,64898],"mapped",[1604,1581,1609]],[[64899,64900],"mapped",[1604,1580,1580]],[[64901,64902],"mapped",[1604,1582,1605]],[[64903,64904],"mapped",[1604,1605,1581]],[[64905,64905],"mapped",[1605,1581,1580]],[[64906,64906],"mapped",[1605,1581,1605]],[[64907,64907],"mapped",[1605,1581,1610]],[[64908,64908],"mapped",[1605,1580,1581]],[[64909,64909],"mapped",[1605,1580,1605]],[[64910,64910],"mapped",[1605,1582,1580]],[[64911,64911],"mapped",[1605,1582,1605]],[[64912,64913],"disallowed"],[[64914,64914],"mapped",[1605,1580,1582]],[[64915,64915],"mapped",[1607,1605,1580]],[[64916,64916],"mapped",[1607,1605,1605]],[[64917,64917],"mapped",[1606,1581,1605]],[[64918,64918],"mapped",[1606,1581,1609]],[[64919,64920],"mapped",[1606,1580,1605]],[[64921,64921],"mapped",[1606,1580,1609]],[[64922,64922],"mapped",[1606,1605,1610]],[[64923,64923],"mapped",[1606,1605,1609]],[[64924,64925],"mapped",[1610,1605,1605]],[[64926,64926],"mapped",[1576,1582,1610]],[[64927,64927],"mapped",[1578,1580,1610]],[[64928,64928],"mapped",[1578,1580,1609]],[[64929,64929],"mapped",[1578,1582,1610]],[[64930,64930],"mapped",[1578,1582,1609]],[[64931,64931],"mapped",[1578,1605,1610]],[[64932,64932],"mapped",[1578,1605,1609]],[[64933,64933],"mapped",[1580,1605,1610]],[[64934,64934],"mapped",[1580,1581,1609]],[[64935,64935],"mapped",[1580,1605,1609]],[[64936,64936],"mapped",[1587,1582,1609]],[[64937,64937],"mapped",[1589,1581,1610]],[[64938,64938],"mapped",[1588,1581,1610]],[[64939,64939],"mapped",[1590,1581,1610]],[[64940,64940],"mapped",[1604,1580,1610]],[[64941,64941],"mapped",[1604,1605,1610]],[[64942,64942],"mapped",[1610,1581,1610]],[[64943,64943],"mapped",[1610,1580,1610]],[[64944,64944],"mapped",[1610,1605,1610]],[[64945,64945],"mapped",[1605,1605,1610]],[[64946,64946],"mapped",[1602,1605,1610]],[[64947,64947],"mapped",[1606,1581,1610]],[[64948,64948],"mapped",[1602,1605,1581]],[[64949,64949],"mapped",[1604,1581,1605]],[[64950,64950],"mapped",[1593,1605,1610]],[[64951,64951],"mapped",[1603,1605,1610]],[[64952,64952],"mapped",[1606,1580,1581]],[[64953,64953],"mapped",[1605,1582,1610]],[[64954,64954],"mapped",[1604,1580,1605]],[[64955,64955],"mapped",[1603,1605,1605]],[[64956,64956],"mapped",[1604,1580,1605]],[[64957,64957],"mapped",[1606,1580,1581]],[[64958,64958],"mapped",[1580,1581,1610]],[[64959,64959],"mapped",[1581,1580,1610]],[[64960,64960],"mapped",[1605,1580,1610]],[[64961,64961],"mapped",[1601,1605,1610]],[[64962,64962],"mapped",[1576,1581,1610]],[[64963,64963],"mapped",[1603,1605,1605]],[[64964,64964],"mapped",[1593,1580,1605]],[[64965,64965],"mapped",[1589,1605,1605]],[[64966,64966],"mapped",[1587,1582,1610]],[[64967,64967],"mapped",[1606,1580,1610]],[[64968,64975],"disallowed"],[[64976,65007],"disallowed"],[[65008,65008],"mapped",[1589,1604,1746]],[[65009,65009],"mapped",[1602,1604,1746]],[[65010,65010],"mapped",[1575,1604,1604,1607]],[[65011,65011],"mapped",[1575,1603,1576,1585]],[[65012,65012],"mapped",[1605,1581,1605,1583]],[[65013,65013],"mapped",[1589,1604,1593,1605]],[[65014,65014],"mapped",[1585,1587,1608,1604]],[[65015,65015],"mapped",[1593,1604,1610,1607]],[[65016,65016],"mapped",[1608,1587,1604,1605]],[[65017,65017],"mapped",[1589,1604,1609]],[[65018,65018],"disallowed_STD3_mapped",[1589,1604,1609,32,1575,1604,1604,1607,32,1593,1604,1610,1607,32,1608,1587,1604,1605]],[[65019,65019],"disallowed_STD3_mapped",[1580,1604,32,1580,1604,1575,1604,1607]],[[65020,65020],"mapped",[1585,1740,1575,1604]],[[65021,65021],"valid",[],"NV8"],[[65022,65023],"disallowed"],[[65024,65039],"ignored"],[[65040,65040],"disallowed_STD3_mapped",[44]],[[65041,65041],"mapped",[12289]],[[65042,65042],"disallowed"],[[65043,65043],"disallowed_STD3_mapped",[58]],[[65044,65044],"disallowed_STD3_mapped",[59]],[[65045,65045],"disallowed_STD3_mapped",[33]],[[65046,65046],"disallowed_STD3_mapped",[63]],[[65047,65047],"mapped",[12310]],[[65048,65048],"mapped",[12311]],[[65049,65049],"disallowed"],[[65050,65055],"disallowed"],[[65056,65059],"valid"],[[65060,65062],"valid"],[[65063,65069],"valid"],[[65070,65071],"valid"],[[65072,65072],"disallowed"],[[65073,65073],"mapped",[8212]],[[65074,65074],"mapped",[8211]],[[65075,65076],"disallowed_STD3_mapped",[95]],[[65077,65077],"disallowed_STD3_mapped",[40]],[[65078,65078],"disallowed_STD3_mapped",[41]],[[65079,65079],"disallowed_STD3_mapped",[123]],[[65080,65080],"disallowed_STD3_mapped",[125]],[[65081,65081],"mapped",[12308]],[[65082,65082],"mapped",[12309]],[[65083,65083],"mapped",[12304]],[[65084,65084],"mapped",[12305]],[[65085,65085],"mapped",[12298]],[[65086,65086],"mapped",[12299]],[[65087,65087],"mapped",[12296]],[[65088,65088],"mapped",[12297]],[[65089,65089],"mapped",[12300]],[[65090,65090],"mapped",[12301]],[[65091,65091],"mapped",[12302]],[[65092,65092],"mapped",[12303]],[[65093,65094],"valid",[],"NV8"],[[65095,65095],"disallowed_STD3_mapped",[91]],[[65096,65096],"disallowed_STD3_mapped",[93]],[[65097,65100],"disallowed_STD3_mapped",[32,773]],[[65101,65103],"disallowed_STD3_mapped",[95]],[[65104,65104],"disallowed_STD3_mapped",[44]],[[65105,65105],"mapped",[12289]],[[65106,65106],"disallowed"],[[65107,65107],"disallowed"],[[65108,65108],"disallowed_STD3_mapped",[59]],[[65109,65109],"disallowed_STD3_mapped",[58]],[[65110,65110],"disallowed_STD3_mapped",[63]],[[65111,65111],"disallowed_STD3_mapped",[33]],[[65112,65112],"mapped",[8212]],[[65113,65113],"disallowed_STD3_mapped",[40]],[[65114,65114],"disallowed_STD3_mapped",[41]],[[65115,65115],"disallowed_STD3_mapped",[123]],[[65116,65116],"disallowed_STD3_mapped",[125]],[[65117,65117],"mapped",[12308]],[[65118,65118],"mapped",[12309]],[[65119,65119],"disallowed_STD3_mapped",[35]],[[65120,65120],"disallowed_STD3_mapped",[38]],[[65121,65121],"disallowed_STD3_mapped",[42]],[[65122,65122],"disallowed_STD3_mapped",[43]],[[65123,65123],"mapped",[45]],[[65124,65124],"disallowed_STD3_mapped",[60]],[[65125,65125],"disallowed_STD3_mapped",[62]],[[65126,65126],"disallowed_STD3_mapped",[61]],[[65127,65127],"disallowed"],[[65128,65128],"disallowed_STD3_mapped",[92]],[[65129,65129],"disallowed_STD3_mapped",[36]],[[65130,65130],"disallowed_STD3_mapped",[37]],[[65131,65131],"disallowed_STD3_mapped",[64]],[[65132,65135],"disallowed"],[[65136,65136],"disallowed_STD3_mapped",[32,1611]],[[65137,65137],"mapped",[1600,1611]],[[65138,65138],"disallowed_STD3_mapped",[32,1612]],[[65139,65139],"valid"],[[65140,65140],"disallowed_STD3_mapped",[32,1613]],[[65141,65141],"disallowed"],[[65142,65142],"disallowed_STD3_mapped",[32,1614]],[[65143,65143],"mapped",[1600,1614]],[[65144,65144],"disallowed_STD3_mapped",[32,1615]],[[65145,65145],"mapped",[1600,1615]],[[65146,65146],"disallowed_STD3_mapped",[32,1616]],[[65147,65147],"mapped",[1600,1616]],[[65148,65148],"disallowed_STD3_mapped",[32,1617]],[[65149,65149],"mapped",[1600,1617]],[[65150,65150],"disallowed_STD3_mapped",[32,1618]],[[65151,65151],"mapped",[1600,1618]],[[65152,65152],"mapped",[1569]],[[65153,65154],"mapped",[1570]],[[65155,65156],"mapped",[1571]],[[65157,65158],"mapped",[1572]],[[65159,65160],"mapped",[1573]],[[65161,65164],"mapped",[1574]],[[65165,65166],"mapped",[1575]],[[65167,65170],"mapped",[1576]],[[65171,65172],"mapped",[1577]],[[65173,65176],"mapped",[1578]],[[65177,65180],"mapped",[1579]],[[65181,65184],"mapped",[1580]],[[65185,65188],"mapped",[1581]],[[65189,65192],"mapped",[1582]],[[65193,65194],"mapped",[1583]],[[65195,65196],"mapped",[1584]],[[65197,65198],"mapped",[1585]],[[65199,65200],"mapped",[1586]],[[65201,65204],"mapped",[1587]],[[65205,65208],"mapped",[1588]],[[65209,65212],"mapped",[1589]],[[65213,65216],"mapped",[1590]],[[65217,65220],"mapped",[1591]],[[65221,65224],"mapped",[1592]],[[65225,65228],"mapped",[1593]],[[65229,65232],"mapped",[1594]],[[65233,65236],"mapped",[1601]],[[65237,65240],"mapped",[1602]],[[65241,65244],"mapped",[1603]],[[65245,65248],"mapped",[1604]],[[65249,65252],"mapped",[1605]],[[65253,65256],"mapped",[1606]],[[65257,65260],"mapped",[1607]],[[65261,65262],"mapped",[1608]],[[65263,65264],"mapped",[1609]],[[65265,65268],"mapped",[1610]],[[65269,65270],"mapped",[1604,1570]],[[65271,65272],"mapped",[1604,1571]],[[65273,65274],"mapped",[1604,1573]],[[65275,65276],"mapped",[1604,1575]],[[65277,65278],"disallowed"],[[65279,65279],"ignored"],[[65280,65280],"disallowed"],[[65281,65281],"disallowed_STD3_mapped",[33]],[[65282,65282],"disallowed_STD3_mapped",[34]],[[65283,65283],"disallowed_STD3_mapped",[35]],[[65284,65284],"disallowed_STD3_mapped",[36]],[[65285,65285],"disallowed_STD3_mapped",[37]],[[65286,65286],"disallowed_STD3_mapped",[38]],[[65287,65287],"disallowed_STD3_mapped",[39]],[[65288,65288],"disallowed_STD3_mapped",[40]],[[65289,65289],"disallowed_STD3_mapped",[41]],[[65290,65290],"disallowed_STD3_mapped",[42]],[[65291,65291],"disallowed_STD3_mapped",[43]],[[65292,65292],"disallowed_STD3_mapped",[44]],[[65293,65293],"mapped",[45]],[[65294,65294],"mapped",[46]],[[65295,65295],"disallowed_STD3_mapped",[47]],[[65296,65296],"mapped",[48]],[[65297,65297],"mapped",[49]],[[65298,65298],"mapped",[50]],[[65299,65299],"mapped",[51]],[[65300,65300],"mapped",[52]],[[65301,65301],"mapped",[53]],[[65302,65302],"mapped",[54]],[[65303,65303],"mapped",[55]],[[65304,65304],"mapped",[56]],[[65305,65305],"mapped",[57]],[[65306,65306],"disallowed_STD3_mapped",[58]],[[65307,65307],"disallowed_STD3_mapped",[59]],[[65308,65308],"disallowed_STD3_mapped",[60]],[[65309,65309],"disallowed_STD3_mapped",[61]],[[65310,65310],"disallowed_STD3_mapped",[62]],[[65311,65311],"disallowed_STD3_mapped",[63]],[[65312,65312],"disallowed_STD3_mapped",[64]],[[65313,65313],"mapped",[97]],[[65314,65314],"mapped",[98]],[[65315,65315],"mapped",[99]],[[65316,65316],"mapped",[100]],[[65317,65317],"mapped",[101]],[[65318,65318],"mapped",[102]],[[65319,65319],"mapped",[103]],[[65320,65320],"mapped",[104]],[[65321,65321],"mapped",[105]],[[65322,65322],"mapped",[106]],[[65323,65323],"mapped",[107]],[[65324,65324],"mapped",[108]],[[65325,65325],"mapped",[109]],[[65326,65326],"mapped",[110]],[[65327,65327],"mapped",[111]],[[65328,65328],"mapped",[112]],[[65329,65329],"mapped",[113]],[[65330,65330],"mapped",[114]],[[65331,65331],"mapped",[115]],[[65332,65332],"mapped",[116]],[[65333,65333],"mapped",[117]],[[65334,65334],"mapped",[118]],[[65335,65335],"mapped",[119]],[[65336,65336],"mapped",[120]],[[65337,65337],"mapped",[121]],[[65338,65338],"mapped",[122]],[[65339,65339],"disallowed_STD3_mapped",[91]],[[65340,65340],"disallowed_STD3_mapped",[92]],[[65341,65341],"disallowed_STD3_mapped",[93]],[[65342,65342],"disallowed_STD3_mapped",[94]],[[65343,65343],"disallowed_STD3_mapped",[95]],[[65344,65344],"disallowed_STD3_mapped",[96]],[[65345,65345],"mapped",[97]],[[65346,65346],"mapped",[98]],[[65347,65347],"mapped",[99]],[[65348,65348],"mapped",[100]],[[65349,65349],"mapped",[101]],[[65350,65350],"mapped",[102]],[[65351,65351],"mapped",[103]],[[65352,65352],"mapped",[104]],[[65353,65353],"mapped",[105]],[[65354,65354],"mapped",[106]],[[65355,65355],"mapped",[107]],[[65356,65356],"mapped",[108]],[[65357,65357],"mapped",[109]],[[65358,65358],"mapped",[110]],[[65359,65359],"mapped",[111]],[[65360,65360],"mapped",[112]],[[65361,65361],"mapped",[113]],[[65362,65362],"mapped",[114]],[[65363,65363],"mapped",[115]],[[65364,65364],"mapped",[116]],[[65365,65365],"mapped",[117]],[[65366,65366],"mapped",[118]],[[65367,65367],"mapped",[119]],[[65368,65368],"mapped",[120]],[[65369,65369],"mapped",[121]],[[65370,65370],"mapped",[122]],[[65371,65371],"disallowed_STD3_mapped",[123]],[[65372,65372],"disallowed_STD3_mapped",[124]],[[65373,65373],"disallowed_STD3_mapped",[125]],[[65374,65374],"disallowed_STD3_mapped",[126]],[[65375,65375],"mapped",[10629]],[[65376,65376],"mapped",[10630]],[[65377,65377],"mapped",[46]],[[65378,65378],"mapped",[12300]],[[65379,65379],"mapped",[12301]],[[65380,65380],"mapped",[12289]],[[65381,65381],"mapped",[12539]],[[65382,65382],"mapped",[12530]],[[65383,65383],"mapped",[12449]],[[65384,65384],"mapped",[12451]],[[65385,65385],"mapped",[12453]],[[65386,65386],"mapped",[12455]],[[65387,65387],"mapped",[12457]],[[65388,65388],"mapped",[12515]],[[65389,65389],"mapped",[12517]],[[65390,65390],"mapped",[12519]],[[65391,65391],"mapped",[12483]],[[65392,65392],"mapped",[12540]],[[65393,65393],"mapped",[12450]],[[65394,65394],"mapped",[12452]],[[65395,65395],"mapped",[12454]],[[65396,65396],"mapped",[12456]],[[65397,65397],"mapped",[12458]],[[65398,65398],"mapped",[12459]],[[65399,65399],"mapped",[12461]],[[65400,65400],"mapped",[12463]],[[65401,65401],"mapped",[12465]],[[65402,65402],"mapped",[12467]],[[65403,65403],"mapped",[12469]],[[65404,65404],"mapped",[12471]],[[65405,65405],"mapped",[12473]],[[65406,65406],"mapped",[12475]],[[65407,65407],"mapped",[12477]],[[65408,65408],"mapped",[12479]],[[65409,65409],"mapped",[12481]],[[65410,65410],"mapped",[12484]],[[65411,65411],"mapped",[12486]],[[65412,65412],"mapped",[12488]],[[65413,65413],"mapped",[12490]],[[65414,65414],"mapped",[12491]],[[65415,65415],"mapped",[12492]],[[65416,65416],"mapped",[12493]],[[65417,65417],"mapped",[12494]],[[65418,65418],"mapped",[12495]],[[65419,65419],"mapped",[12498]],[[65420,65420],"mapped",[12501]],[[65421,65421],"mapped",[12504]],[[65422,65422],"mapped",[12507]],[[65423,65423],"mapped",[12510]],[[65424,65424],"mapped",[12511]],[[65425,65425],"mapped",[12512]],[[65426,65426],"mapped",[12513]],[[65427,65427],"mapped",[12514]],[[65428,65428],"mapped",[12516]],[[65429,65429],"mapped",[12518]],[[65430,65430],"mapped",[12520]],[[65431,65431],"mapped",[12521]],[[65432,65432],"mapped",[12522]],[[65433,65433],"mapped",[12523]],[[65434,65434],"mapped",[12524]],[[65435,65435],"mapped",[12525]],[[65436,65436],"mapped",[12527]],[[65437,65437],"mapped",[12531]],[[65438,65438],"mapped",[12441]],[[65439,65439],"mapped",[12442]],[[65440,65440],"disallowed"],[[65441,65441],"mapped",[4352]],[[65442,65442],"mapped",[4353]],[[65443,65443],"mapped",[4522]],[[65444,65444],"mapped",[4354]],[[65445,65445],"mapped",[4524]],[[65446,65446],"mapped",[4525]],[[65447,65447],"mapped",[4355]],[[65448,65448],"mapped",[4356]],[[65449,65449],"mapped",[4357]],[[65450,65450],"mapped",[4528]],[[65451,65451],"mapped",[4529]],[[65452,65452],"mapped",[4530]],[[65453,65453],"mapped",[4531]],[[65454,65454],"mapped",[4532]],[[65455,65455],"mapped",[4533]],[[65456,65456],"mapped",[4378]],[[65457,65457],"mapped",[4358]],[[65458,65458],"mapped",[4359]],[[65459,65459],"mapped",[4360]],[[65460,65460],"mapped",[4385]],[[65461,65461],"mapped",[4361]],[[65462,65462],"mapped",[4362]],[[65463,65463],"mapped",[4363]],[[65464,65464],"mapped",[4364]],[[65465,65465],"mapped",[4365]],[[65466,65466],"mapped",[4366]],[[65467,65467],"mapped",[4367]],[[65468,65468],"mapped",[4368]],[[65469,65469],"mapped",[4369]],[[65470,65470],"mapped",[4370]],[[65471,65473],"disallowed"],[[65474,65474],"mapped",[4449]],[[65475,65475],"mapped",[4450]],[[65476,65476],"mapped",[4451]],[[65477,65477],"mapped",[4452]],[[65478,65478],"mapped",[4453]],[[65479,65479],"mapped",[4454]],[[65480,65481],"disallowed"],[[65482,65482],"mapped",[4455]],[[65483,65483],"mapped",[4456]],[[65484,65484],"mapped",[4457]],[[65485,65485],"mapped",[4458]],[[65486,65486],"mapped",[4459]],[[65487,65487],"mapped",[4460]],[[65488,65489],"disallowed"],[[65490,65490],"mapped",[4461]],[[65491,65491],"mapped",[4462]],[[65492,65492],"mapped",[4463]],[[65493,65493],"mapped",[4464]],[[65494,65494],"mapped",[4465]],[[65495,65495],"mapped",[4466]],[[65496,65497],"disallowed"],[[65498,65498],"mapped",[4467]],[[65499,65499],"mapped",[4468]],[[65500,65500],"mapped",[4469]],[[65501,65503],"disallowed"],[[65504,65504],"mapped",[162]],[[65505,65505],"mapped",[163]],[[65506,65506],"mapped",[172]],[[65507,65507],"disallowed_STD3_mapped",[32,772]],[[65508,65508],"mapped",[166]],[[65509,65509],"mapped",[165]],[[65510,65510],"mapped",[8361]],[[65511,65511],"disallowed"],[[65512,65512],"mapped",[9474]],[[65513,65513],"mapped",[8592]],[[65514,65514],"mapped",[8593]],[[65515,65515],"mapped",[8594]],[[65516,65516],"mapped",[8595]],[[65517,65517],"mapped",[9632]],[[65518,65518],"mapped",[9675]],[[65519,65528],"disallowed"],[[65529,65531],"disallowed"],[[65532,65532],"disallowed"],[[65533,65533],"disallowed"],[[65534,65535],"disallowed"],[[65536,65547],"valid"],[[65548,65548],"disallowed"],[[65549,65574],"valid"],[[65575,65575],"disallowed"],[[65576,65594],"valid"],[[65595,65595],"disallowed"],[[65596,65597],"valid"],[[65598,65598],"disallowed"],[[65599,65613],"valid"],[[65614,65615],"disallowed"],[[65616,65629],"valid"],[[65630,65663],"disallowed"],[[65664,65786],"valid"],[[65787,65791],"disallowed"],[[65792,65794],"valid",[],"NV8"],[[65795,65798],"disallowed"],[[65799,65843],"valid",[],"NV8"],[[65844,65846],"disallowed"],[[65847,65855],"valid",[],"NV8"],[[65856,65930],"valid",[],"NV8"],[[65931,65932],"valid",[],"NV8"],[[65933,65935],"disallowed"],[[65936,65947],"valid",[],"NV8"],[[65948,65951],"disallowed"],[[65952,65952],"valid",[],"NV8"],[[65953,65999],"disallowed"],[[66e3,66044],"valid",[],"NV8"],[[66045,66045],"valid"],[[66046,66175],"disallowed"],[[66176,66204],"valid"],[[66205,66207],"disallowed"],[[66208,66256],"valid"],[[66257,66271],"disallowed"],[[66272,66272],"valid"],[[66273,66299],"valid",[],"NV8"],[[66300,66303],"disallowed"],[[66304,66334],"valid"],[[66335,66335],"valid"],[[66336,66339],"valid",[],"NV8"],[[66340,66351],"disallowed"],[[66352,66368],"valid"],[[66369,66369],"valid",[],"NV8"],[[66370,66377],"valid"],[[66378,66378],"valid",[],"NV8"],[[66379,66383],"disallowed"],[[66384,66426],"valid"],[[66427,66431],"disallowed"],[[66432,66461],"valid"],[[66462,66462],"disallowed"],[[66463,66463],"valid",[],"NV8"],[[66464,66499],"valid"],[[66500,66503],"disallowed"],[[66504,66511],"valid"],[[66512,66517],"valid",[],"NV8"],[[66518,66559],"disallowed"],[[66560,66560],"mapped",[66600]],[[66561,66561],"mapped",[66601]],[[66562,66562],"mapped",[66602]],[[66563,66563],"mapped",[66603]],[[66564,66564],"mapped",[66604]],[[66565,66565],"mapped",[66605]],[[66566,66566],"mapped",[66606]],[[66567,66567],"mapped",[66607]],[[66568,66568],"mapped",[66608]],[[66569,66569],"mapped",[66609]],[[66570,66570],"mapped",[66610]],[[66571,66571],"mapped",[66611]],[[66572,66572],"mapped",[66612]],[[66573,66573],"mapped",[66613]],[[66574,66574],"mapped",[66614]],[[66575,66575],"mapped",[66615]],[[66576,66576],"mapped",[66616]],[[66577,66577],"mapped",[66617]],[[66578,66578],"mapped",[66618]],[[66579,66579],"mapped",[66619]],[[66580,66580],"mapped",[66620]],[[66581,66581],"mapped",[66621]],[[66582,66582],"mapped",[66622]],[[66583,66583],"mapped",[66623]],[[66584,66584],"mapped",[66624]],[[66585,66585],"mapped",[66625]],[[66586,66586],"mapped",[66626]],[[66587,66587],"mapped",[66627]],[[66588,66588],"mapped",[66628]],[[66589,66589],"mapped",[66629]],[[66590,66590],"mapped",[66630]],[[66591,66591],"mapped",[66631]],[[66592,66592],"mapped",[66632]],[[66593,66593],"mapped",[66633]],[[66594,66594],"mapped",[66634]],[[66595,66595],"mapped",[66635]],[[66596,66596],"mapped",[66636]],[[66597,66597],"mapped",[66637]],[[66598,66598],"mapped",[66638]],[[66599,66599],"mapped",[66639]],[[66600,66637],"valid"],[[66638,66717],"valid"],[[66718,66719],"disallowed"],[[66720,66729],"valid"],[[66730,66815],"disallowed"],[[66816,66855],"valid"],[[66856,66863],"disallowed"],[[66864,66915],"valid"],[[66916,66926],"disallowed"],[[66927,66927],"valid",[],"NV8"],[[66928,67071],"disallowed"],[[67072,67382],"valid"],[[67383,67391],"disallowed"],[[67392,67413],"valid"],[[67414,67423],"disallowed"],[[67424,67431],"valid"],[[67432,67583],"disallowed"],[[67584,67589],"valid"],[[67590,67591],"disallowed"],[[67592,67592],"valid"],[[67593,67593],"disallowed"],[[67594,67637],"valid"],[[67638,67638],"disallowed"],[[67639,67640],"valid"],[[67641,67643],"disallowed"],[[67644,67644],"valid"],[[67645,67646],"disallowed"],[[67647,67647],"valid"],[[67648,67669],"valid"],[[67670,67670],"disallowed"],[[67671,67679],"valid",[],"NV8"],[[67680,67702],"valid"],[[67703,67711],"valid",[],"NV8"],[[67712,67742],"valid"],[[67743,67750],"disallowed"],[[67751,67759],"valid",[],"NV8"],[[67760,67807],"disallowed"],[[67808,67826],"valid"],[[67827,67827],"disallowed"],[[67828,67829],"valid"],[[67830,67834],"disallowed"],[[67835,67839],"valid",[],"NV8"],[[67840,67861],"valid"],[[67862,67865],"valid",[],"NV8"],[[67866,67867],"valid",[],"NV8"],[[67868,67870],"disallowed"],[[67871,67871],"valid",[],"NV8"],[[67872,67897],"valid"],[[67898,67902],"disallowed"],[[67903,67903],"valid",[],"NV8"],[[67904,67967],"disallowed"],[[67968,68023],"valid"],[[68024,68027],"disallowed"],[[68028,68029],"valid",[],"NV8"],[[68030,68031],"valid"],[[68032,68047],"valid",[],"NV8"],[[68048,68049],"disallowed"],[[68050,68095],"valid",[],"NV8"],[[68096,68099],"valid"],[[68100,68100],"disallowed"],[[68101,68102],"valid"],[[68103,68107],"disallowed"],[[68108,68115],"valid"],[[68116,68116],"disallowed"],[[68117,68119],"valid"],[[68120,68120],"disallowed"],[[68121,68147],"valid"],[[68148,68151],"disallowed"],[[68152,68154],"valid"],[[68155,68158],"disallowed"],[[68159,68159],"valid"],[[68160,68167],"valid",[],"NV8"],[[68168,68175],"disallowed"],[[68176,68184],"valid",[],"NV8"],[[68185,68191],"disallowed"],[[68192,68220],"valid"],[[68221,68223],"valid",[],"NV8"],[[68224,68252],"valid"],[[68253,68255],"valid",[],"NV8"],[[68256,68287],"disallowed"],[[68288,68295],"valid"],[[68296,68296],"valid",[],"NV8"],[[68297,68326],"valid"],[[68327,68330],"disallowed"],[[68331,68342],"valid",[],"NV8"],[[68343,68351],"disallowed"],[[68352,68405],"valid"],[[68406,68408],"disallowed"],[[68409,68415],"valid",[],"NV8"],[[68416,68437],"valid"],[[68438,68439],"disallowed"],[[68440,68447],"valid",[],"NV8"],[[68448,68466],"valid"],[[68467,68471],"disallowed"],[[68472,68479],"valid",[],"NV8"],[[68480,68497],"valid"],[[68498,68504],"disallowed"],[[68505,68508],"valid",[],"NV8"],[[68509,68520],"disallowed"],[[68521,68527],"valid",[],"NV8"],[[68528,68607],"disallowed"],[[68608,68680],"valid"],[[68681,68735],"disallowed"],[[68736,68736],"mapped",[68800]],[[68737,68737],"mapped",[68801]],[[68738,68738],"mapped",[68802]],[[68739,68739],"mapped",[68803]],[[68740,68740],"mapped",[68804]],[[68741,68741],"mapped",[68805]],[[68742,68742],"mapped",[68806]],[[68743,68743],"mapped",[68807]],[[68744,68744],"mapped",[68808]],[[68745,68745],"mapped",[68809]],[[68746,68746],"mapped",[68810]],[[68747,68747],"mapped",[68811]],[[68748,68748],"mapped",[68812]],[[68749,68749],"mapped",[68813]],[[68750,68750],"mapped",[68814]],[[68751,68751],"mapped",[68815]],[[68752,68752],"mapped",[68816]],[[68753,68753],"mapped",[68817]],[[68754,68754],"mapped",[68818]],[[68755,68755],"mapped",[68819]],[[68756,68756],"mapped",[68820]],[[68757,68757],"mapped",[68821]],[[68758,68758],"mapped",[68822]],[[68759,68759],"mapped",[68823]],[[68760,68760],"mapped",[68824]],[[68761,68761],"mapped",[68825]],[[68762,68762],"mapped",[68826]],[[68763,68763],"mapped",[68827]],[[68764,68764],"mapped",[68828]],[[68765,68765],"mapped",[68829]],[[68766,68766],"mapped",[68830]],[[68767,68767],"mapped",[68831]],[[68768,68768],"mapped",[68832]],[[68769,68769],"mapped",[68833]],[[68770,68770],"mapped",[68834]],[[68771,68771],"mapped",[68835]],[[68772,68772],"mapped",[68836]],[[68773,68773],"mapped",[68837]],[[68774,68774],"mapped",[68838]],[[68775,68775],"mapped",[68839]],[[68776,68776],"mapped",[68840]],[[68777,68777],"mapped",[68841]],[[68778,68778],"mapped",[68842]],[[68779,68779],"mapped",[68843]],[[68780,68780],"mapped",[68844]],[[68781,68781],"mapped",[68845]],[[68782,68782],"mapped",[68846]],[[68783,68783],"mapped",[68847]],[[68784,68784],"mapped",[68848]],[[68785,68785],"mapped",[68849]],[[68786,68786],"mapped",[68850]],[[68787,68799],"disallowed"],[[68800,68850],"valid"],[[68851,68857],"disallowed"],[[68858,68863],"valid",[],"NV8"],[[68864,69215],"disallowed"],[[69216,69246],"valid",[],"NV8"],[[69247,69631],"disallowed"],[[69632,69702],"valid"],[[69703,69709],"valid",[],"NV8"],[[69710,69713],"disallowed"],[[69714,69733],"valid",[],"NV8"],[[69734,69743],"valid"],[[69744,69758],"disallowed"],[[69759,69759],"valid"],[[69760,69818],"valid"],[[69819,69820],"valid",[],"NV8"],[[69821,69821],"disallowed"],[[69822,69825],"valid",[],"NV8"],[[69826,69839],"disallowed"],[[69840,69864],"valid"],[[69865,69871],"disallowed"],[[69872,69881],"valid"],[[69882,69887],"disallowed"],[[69888,69940],"valid"],[[69941,69941],"disallowed"],[[69942,69951],"valid"],[[69952,69955],"valid",[],"NV8"],[[69956,69967],"disallowed"],[[69968,70003],"valid"],[[70004,70005],"valid",[],"NV8"],[[70006,70006],"valid"],[[70007,70015],"disallowed"],[[70016,70084],"valid"],[[70085,70088],"valid",[],"NV8"],[[70089,70089],"valid",[],"NV8"],[[70090,70092],"valid"],[[70093,70093],"valid",[],"NV8"],[[70094,70095],"disallowed"],[[70096,70105],"valid"],[[70106,70106],"valid"],[[70107,70107],"valid",[],"NV8"],[[70108,70108],"valid"],[[70109,70111],"valid",[],"NV8"],[[70112,70112],"disallowed"],[[70113,70132],"valid",[],"NV8"],[[70133,70143],"disallowed"],[[70144,70161],"valid"],[[70162,70162],"disallowed"],[[70163,70199],"valid"],[[70200,70205],"valid",[],"NV8"],[[70206,70271],"disallowed"],[[70272,70278],"valid"],[[70279,70279],"disallowed"],[[70280,70280],"valid"],[[70281,70281],"disallowed"],[[70282,70285],"valid"],[[70286,70286],"disallowed"],[[70287,70301],"valid"],[[70302,70302],"disallowed"],[[70303,70312],"valid"],[[70313,70313],"valid",[],"NV8"],[[70314,70319],"disallowed"],[[70320,70378],"valid"],[[70379,70383],"disallowed"],[[70384,70393],"valid"],[[70394,70399],"disallowed"],[[70400,70400],"valid"],[[70401,70403],"valid"],[[70404,70404],"disallowed"],[[70405,70412],"valid"],[[70413,70414],"disallowed"],[[70415,70416],"valid"],[[70417,70418],"disallowed"],[[70419,70440],"valid"],[[70441,70441],"disallowed"],[[70442,70448],"valid"],[[70449,70449],"disallowed"],[[70450,70451],"valid"],[[70452,70452],"disallowed"],[[70453,70457],"valid"],[[70458,70459],"disallowed"],[[70460,70468],"valid"],[[70469,70470],"disallowed"],[[70471,70472],"valid"],[[70473,70474],"disallowed"],[[70475,70477],"valid"],[[70478,70479],"disallowed"],[[70480,70480],"valid"],[[70481,70486],"disallowed"],[[70487,70487],"valid"],[[70488,70492],"disallowed"],[[70493,70499],"valid"],[[70500,70501],"disallowed"],[[70502,70508],"valid"],[[70509,70511],"disallowed"],[[70512,70516],"valid"],[[70517,70783],"disallowed"],[[70784,70853],"valid"],[[70854,70854],"valid",[],"NV8"],[[70855,70855],"valid"],[[70856,70863],"disallowed"],[[70864,70873],"valid"],[[70874,71039],"disallowed"],[[71040,71093],"valid"],[[71094,71095],"disallowed"],[[71096,71104],"valid"],[[71105,71113],"valid",[],"NV8"],[[71114,71127],"valid",[],"NV8"],[[71128,71133],"valid"],[[71134,71167],"disallowed"],[[71168,71232],"valid"],[[71233,71235],"valid",[],"NV8"],[[71236,71236],"valid"],[[71237,71247],"disallowed"],[[71248,71257],"valid"],[[71258,71295],"disallowed"],[[71296,71351],"valid"],[[71352,71359],"disallowed"],[[71360,71369],"valid"],[[71370,71423],"disallowed"],[[71424,71449],"valid"],[[71450,71452],"disallowed"],[[71453,71467],"valid"],[[71468,71471],"disallowed"],[[71472,71481],"valid"],[[71482,71487],"valid",[],"NV8"],[[71488,71839],"disallowed"],[[71840,71840],"mapped",[71872]],[[71841,71841],"mapped",[71873]],[[71842,71842],"mapped",[71874]],[[71843,71843],"mapped",[71875]],[[71844,71844],"mapped",[71876]],[[71845,71845],"mapped",[71877]],[[71846,71846],"mapped",[71878]],[[71847,71847],"mapped",[71879]],[[71848,71848],"mapped",[71880]],[[71849,71849],"mapped",[71881]],[[71850,71850],"mapped",[71882]],[[71851,71851],"mapped",[71883]],[[71852,71852],"mapped",[71884]],[[71853,71853],"mapped",[71885]],[[71854,71854],"mapped",[71886]],[[71855,71855],"mapped",[71887]],[[71856,71856],"mapped",[71888]],[[71857,71857],"mapped",[71889]],[[71858,71858],"mapped",[71890]],[[71859,71859],"mapped",[71891]],[[71860,71860],"mapped",[71892]],[[71861,71861],"mapped",[71893]],[[71862,71862],"mapped",[71894]],[[71863,71863],"mapped",[71895]],[[71864,71864],"mapped",[71896]],[[71865,71865],"mapped",[71897]],[[71866,71866],"mapped",[71898]],[[71867,71867],"mapped",[71899]],[[71868,71868],"mapped",[71900]],[[71869,71869],"mapped",[71901]],[[71870,71870],"mapped",[71902]],[[71871,71871],"mapped",[71903]],[[71872,71913],"valid"],[[71914,71922],"valid",[],"NV8"],[[71923,71934],"disallowed"],[[71935,71935],"valid"],[[71936,72383],"disallowed"],[[72384,72440],"valid"],[[72441,73727],"disallowed"],[[73728,74606],"valid"],[[74607,74648],"valid"],[[74649,74649],"valid"],[[74650,74751],"disallowed"],[[74752,74850],"valid",[],"NV8"],[[74851,74862],"valid",[],"NV8"],[[74863,74863],"disallowed"],[[74864,74867],"valid",[],"NV8"],[[74868,74868],"valid",[],"NV8"],[[74869,74879],"disallowed"],[[74880,75075],"valid"],[[75076,77823],"disallowed"],[[77824,78894],"valid"],[[78895,82943],"disallowed"],[[82944,83526],"valid"],[[83527,92159],"disallowed"],[[92160,92728],"valid"],[[92729,92735],"disallowed"],[[92736,92766],"valid"],[[92767,92767],"disallowed"],[[92768,92777],"valid"],[[92778,92781],"disallowed"],[[92782,92783],"valid",[],"NV8"],[[92784,92879],"disallowed"],[[92880,92909],"valid"],[[92910,92911],"disallowed"],[[92912,92916],"valid"],[[92917,92917],"valid",[],"NV8"],[[92918,92927],"disallowed"],[[92928,92982],"valid"],[[92983,92991],"valid",[],"NV8"],[[92992,92995],"valid"],[[92996,92997],"valid",[],"NV8"],[[92998,93007],"disallowed"],[[93008,93017],"valid"],[[93018,93018],"disallowed"],[[93019,93025],"valid",[],"NV8"],[[93026,93026],"disallowed"],[[93027,93047],"valid"],[[93048,93052],"disallowed"],[[93053,93071],"valid"],[[93072,93951],"disallowed"],[[93952,94020],"valid"],[[94021,94031],"disallowed"],[[94032,94078],"valid"],[[94079,94094],"disallowed"],[[94095,94111],"valid"],[[94112,110591],"disallowed"],[[110592,110593],"valid"],[[110594,113663],"disallowed"],[[113664,113770],"valid"],[[113771,113775],"disallowed"],[[113776,113788],"valid"],[[113789,113791],"disallowed"],[[113792,113800],"valid"],[[113801,113807],"disallowed"],[[113808,113817],"valid"],[[113818,113819],"disallowed"],[[113820,113820],"valid",[],"NV8"],[[113821,113822],"valid"],[[113823,113823],"valid",[],"NV8"],[[113824,113827],"ignored"],[[113828,118783],"disallowed"],[[118784,119029],"valid",[],"NV8"],[[119030,119039],"disallowed"],[[119040,119078],"valid",[],"NV8"],[[119079,119080],"disallowed"],[[119081,119081],"valid",[],"NV8"],[[119082,119133],"valid",[],"NV8"],[[119134,119134],"mapped",[119127,119141]],[[119135,119135],"mapped",[119128,119141]],[[119136,119136],"mapped",[119128,119141,119150]],[[119137,119137],"mapped",[119128,119141,119151]],[[119138,119138],"mapped",[119128,119141,119152]],[[119139,119139],"mapped",[119128,119141,119153]],[[119140,119140],"mapped",[119128,119141,119154]],[[119141,119154],"valid",[],"NV8"],[[119155,119162],"disallowed"],[[119163,119226],"valid",[],"NV8"],[[119227,119227],"mapped",[119225,119141]],[[119228,119228],"mapped",[119226,119141]],[[119229,119229],"mapped",[119225,119141,119150]],[[119230,119230],"mapped",[119226,119141,119150]],[[119231,119231],"mapped",[119225,119141,119151]],[[119232,119232],"mapped",[119226,119141,119151]],[[119233,119261],"valid",[],"NV8"],[[119262,119272],"valid",[],"NV8"],[[119273,119295],"disallowed"],[[119296,119365],"valid",[],"NV8"],[[119366,119551],"disallowed"],[[119552,119638],"valid",[],"NV8"],[[119639,119647],"disallowed"],[[119648,119665],"valid",[],"NV8"],[[119666,119807],"disallowed"],[[119808,119808],"mapped",[97]],[[119809,119809],"mapped",[98]],[[119810,119810],"mapped",[99]],[[119811,119811],"mapped",[100]],[[119812,119812],"mapped",[101]],[[119813,119813],"mapped",[102]],[[119814,119814],"mapped",[103]],[[119815,119815],"mapped",[104]],[[119816,119816],"mapped",[105]],[[119817,119817],"mapped",[106]],[[119818,119818],"mapped",[107]],[[119819,119819],"mapped",[108]],[[119820,119820],"mapped",[109]],[[119821,119821],"mapped",[110]],[[119822,119822],"mapped",[111]],[[119823,119823],"mapped",[112]],[[119824,119824],"mapped",[113]],[[119825,119825],"mapped",[114]],[[119826,119826],"mapped",[115]],[[119827,119827],"mapped",[116]],[[119828,119828],"mapped",[117]],[[119829,119829],"mapped",[118]],[[119830,119830],"mapped",[119]],[[119831,119831],"mapped",[120]],[[119832,119832],"mapped",[121]],[[119833,119833],"mapped",[122]],[[119834,119834],"mapped",[97]],[[119835,119835],"mapped",[98]],[[119836,119836],"mapped",[99]],[[119837,119837],"mapped",[100]],[[119838,119838],"mapped",[101]],[[119839,119839],"mapped",[102]],[[119840,119840],"mapped",[103]],[[119841,119841],"mapped",[104]],[[119842,119842],"mapped",[105]],[[119843,119843],"mapped",[106]],[[119844,119844],"mapped",[107]],[[119845,119845],"mapped",[108]],[[119846,119846],"mapped",[109]],[[119847,119847],"mapped",[110]],[[119848,119848],"mapped",[111]],[[119849,119849],"mapped",[112]],[[119850,119850],"mapped",[113]],[[119851,119851],"mapped",[114]],[[119852,119852],"mapped",[115]],[[119853,119853],"mapped",[116]],[[119854,119854],"mapped",[117]],[[119855,119855],"mapped",[118]],[[119856,119856],"mapped",[119]],[[119857,119857],"mapped",[120]],[[119858,119858],"mapped",[121]],[[119859,119859],"mapped",[122]],[[119860,119860],"mapped",[97]],[[119861,119861],"mapped",[98]],[[119862,119862],"mapped",[99]],[[119863,119863],"mapped",[100]],[[119864,119864],"mapped",[101]],[[119865,119865],"mapped",[102]],[[119866,119866],"mapped",[103]],[[119867,119867],"mapped",[104]],[[119868,119868],"mapped",[105]],[[119869,119869],"mapped",[106]],[[119870,119870],"mapped",[107]],[[119871,119871],"mapped",[108]],[[119872,119872],"mapped",[109]],[[119873,119873],"mapped",[110]],[[119874,119874],"mapped",[111]],[[119875,119875],"mapped",[112]],[[119876,119876],"mapped",[113]],[[119877,119877],"mapped",[114]],[[119878,119878],"mapped",[115]],[[119879,119879],"mapped",[116]],[[119880,119880],"mapped",[117]],[[119881,119881],"mapped",[118]],[[119882,119882],"mapped",[119]],[[119883,119883],"mapped",[120]],[[119884,119884],"mapped",[121]],[[119885,119885],"mapped",[122]],[[119886,119886],"mapped",[97]],[[119887,119887],"mapped",[98]],[[119888,119888],"mapped",[99]],[[119889,119889],"mapped",[100]],[[119890,119890],"mapped",[101]],[[119891,119891],"mapped",[102]],[[119892,119892],"mapped",[103]],[[119893,119893],"disallowed"],[[119894,119894],"mapped",[105]],[[119895,119895],"mapped",[106]],[[119896,119896],"mapped",[107]],[[119897,119897],"mapped",[108]],[[119898,119898],"mapped",[109]],[[119899,119899],"mapped",[110]],[[119900,119900],"mapped",[111]],[[119901,119901],"mapped",[112]],[[119902,119902],"mapped",[113]],[[119903,119903],"mapped",[114]],[[119904,119904],"mapped",[115]],[[119905,119905],"mapped",[116]],[[119906,119906],"mapped",[117]],[[119907,119907],"mapped",[118]],[[119908,119908],"mapped",[119]],[[119909,119909],"mapped",[120]],[[119910,119910],"mapped",[121]],[[119911,119911],"mapped",[122]],[[119912,119912],"mapped",[97]],[[119913,119913],"mapped",[98]],[[119914,119914],"mapped",[99]],[[119915,119915],"mapped",[100]],[[119916,119916],"mapped",[101]],[[119917,119917],"mapped",[102]],[[119918,119918],"mapped",[103]],[[119919,119919],"mapped",[104]],[[119920,119920],"mapped",[105]],[[119921,119921],"mapped",[106]],[[119922,119922],"mapped",[107]],[[119923,119923],"mapped",[108]],[[119924,119924],"mapped",[109]],[[119925,119925],"mapped",[110]],[[119926,119926],"mapped",[111]],[[119927,119927],"mapped",[112]],[[119928,119928],"mapped",[113]],[[119929,119929],"mapped",[114]],[[119930,119930],"mapped",[115]],[[119931,119931],"mapped",[116]],[[119932,119932],"mapped",[117]],[[119933,119933],"mapped",[118]],[[119934,119934],"mapped",[119]],[[119935,119935],"mapped",[120]],[[119936,119936],"mapped",[121]],[[119937,119937],"mapped",[122]],[[119938,119938],"mapped",[97]],[[119939,119939],"mapped",[98]],[[119940,119940],"mapped",[99]],[[119941,119941],"mapped",[100]],[[119942,119942],"mapped",[101]],[[119943,119943],"mapped",[102]],[[119944,119944],"mapped",[103]],[[119945,119945],"mapped",[104]],[[119946,119946],"mapped",[105]],[[119947,119947],"mapped",[106]],[[119948,119948],"mapped",[107]],[[119949,119949],"mapped",[108]],[[119950,119950],"mapped",[109]],[[119951,119951],"mapped",[110]],[[119952,119952],"mapped",[111]],[[119953,119953],"mapped",[112]],[[119954,119954],"mapped",[113]],[[119955,119955],"mapped",[114]],[[119956,119956],"mapped",[115]],[[119957,119957],"mapped",[116]],[[119958,119958],"mapped",[117]],[[119959,119959],"mapped",[118]],[[119960,119960],"mapped",[119]],[[119961,119961],"mapped",[120]],[[119962,119962],"mapped",[121]],[[119963,119963],"mapped",[122]],[[119964,119964],"mapped",[97]],[[119965,119965],"disallowed"],[[119966,119966],"mapped",[99]],[[119967,119967],"mapped",[100]],[[119968,119969],"disallowed"],[[119970,119970],"mapped",[103]],[[119971,119972],"disallowed"],[[119973,119973],"mapped",[106]],[[119974,119974],"mapped",[107]],[[119975,119976],"disallowed"],[[119977,119977],"mapped",[110]],[[119978,119978],"mapped",[111]],[[119979,119979],"mapped",[112]],[[119980,119980],"mapped",[113]],[[119981,119981],"disallowed"],[[119982,119982],"mapped",[115]],[[119983,119983],"mapped",[116]],[[119984,119984],"mapped",[117]],[[119985,119985],"mapped",[118]],[[119986,119986],"mapped",[119]],[[119987,119987],"mapped",[120]],[[119988,119988],"mapped",[121]],[[119989,119989],"mapped",[122]],[[119990,119990],"mapped",[97]],[[119991,119991],"mapped",[98]],[[119992,119992],"mapped",[99]],[[119993,119993],"mapped",[100]],[[119994,119994],"disallowed"],[[119995,119995],"mapped",[102]],[[119996,119996],"disallowed"],[[119997,119997],"mapped",[104]],[[119998,119998],"mapped",[105]],[[119999,119999],"mapped",[106]],[[12e4,12e4],"mapped",[107]],[[120001,120001],"mapped",[108]],[[120002,120002],"mapped",[109]],[[120003,120003],"mapped",[110]],[[120004,120004],"disallowed"],[[120005,120005],"mapped",[112]],[[120006,120006],"mapped",[113]],[[120007,120007],"mapped",[114]],[[120008,120008],"mapped",[115]],[[120009,120009],"mapped",[116]],[[120010,120010],"mapped",[117]],[[120011,120011],"mapped",[118]],[[120012,120012],"mapped",[119]],[[120013,120013],"mapped",[120]],[[120014,120014],"mapped",[121]],[[120015,120015],"mapped",[122]],[[120016,120016],"mapped",[97]],[[120017,120017],"mapped",[98]],[[120018,120018],"mapped",[99]],[[120019,120019],"mapped",[100]],[[120020,120020],"mapped",[101]],[[120021,120021],"mapped",[102]],[[120022,120022],"mapped",[103]],[[120023,120023],"mapped",[104]],[[120024,120024],"mapped",[105]],[[120025,120025],"mapped",[106]],[[120026,120026],"mapped",[107]],[[120027,120027],"mapped",[108]],[[120028,120028],"mapped",[109]],[[120029,120029],"mapped",[110]],[[120030,120030],"mapped",[111]],[[120031,120031],"mapped",[112]],[[120032,120032],"mapped",[113]],[[120033,120033],"mapped",[114]],[[120034,120034],"mapped",[115]],[[120035,120035],"mapped",[116]],[[120036,120036],"mapped",[117]],[[120037,120037],"mapped",[118]],[[120038,120038],"mapped",[119]],[[120039,120039],"mapped",[120]],[[120040,120040],"mapped",[121]],[[120041,120041],"mapped",[122]],[[120042,120042],"mapped",[97]],[[120043,120043],"mapped",[98]],[[120044,120044],"mapped",[99]],[[120045,120045],"mapped",[100]],[[120046,120046],"mapped",[101]],[[120047,120047],"mapped",[102]],[[120048,120048],"mapped",[103]],[[120049,120049],"mapped",[104]],[[120050,120050],"mapped",[105]],[[120051,120051],"mapped",[106]],[[120052,120052],"mapped",[107]],[[120053,120053],"mapped",[108]],[[120054,120054],"mapped",[109]],[[120055,120055],"mapped",[110]],[[120056,120056],"mapped",[111]],[[120057,120057],"mapped",[112]],[[120058,120058],"mapped",[113]],[[120059,120059],"mapped",[114]],[[120060,120060],"mapped",[115]],[[120061,120061],"mapped",[116]],[[120062,120062],"mapped",[117]],[[120063,120063],"mapped",[118]],[[120064,120064],"mapped",[119]],[[120065,120065],"mapped",[120]],[[120066,120066],"mapped",[121]],[[120067,120067],"mapped",[122]],[[120068,120068],"mapped",[97]],[[120069,120069],"mapped",[98]],[[120070,120070],"disallowed"],[[120071,120071],"mapped",[100]],[[120072,120072],"mapped",[101]],[[120073,120073],"mapped",[102]],[[120074,120074],"mapped",[103]],[[120075,120076],"disallowed"],[[120077,120077],"mapped",[106]],[[120078,120078],"mapped",[107]],[[120079,120079],"mapped",[108]],[[120080,120080],"mapped",[109]],[[120081,120081],"mapped",[110]],[[120082,120082],"mapped",[111]],[[120083,120083],"mapped",[112]],[[120084,120084],"mapped",[113]],[[120085,120085],"disallowed"],[[120086,120086],"mapped",[115]],[[120087,120087],"mapped",[116]],[[120088,120088],"mapped",[117]],[[120089,120089],"mapped",[118]],[[120090,120090],"mapped",[119]],[[120091,120091],"mapped",[120]],[[120092,120092],"mapped",[121]],[[120093,120093],"disallowed"],[[120094,120094],"mapped",[97]],[[120095,120095],"mapped",[98]],[[120096,120096],"mapped",[99]],[[120097,120097],"mapped",[100]],[[120098,120098],"mapped",[101]],[[120099,120099],"mapped",[102]],[[120100,120100],"mapped",[103]],[[120101,120101],"mapped",[104]],[[120102,120102],"mapped",[105]],[[120103,120103],"mapped",[106]],[[120104,120104],"mapped",[107]],[[120105,120105],"mapped",[108]],[[120106,120106],"mapped",[109]],[[120107,120107],"mapped",[110]],[[120108,120108],"mapped",[111]],[[120109,120109],"mapped",[112]],[[120110,120110],"mapped",[113]],[[120111,120111],"mapped",[114]],[[120112,120112],"mapped",[115]],[[120113,120113],"mapped",[116]],[[120114,120114],"mapped",[117]],[[120115,120115],"mapped",[118]],[[120116,120116],"mapped",[119]],[[120117,120117],"mapped",[120]],[[120118,120118],"mapped",[121]],[[120119,120119],"mapped",[122]],[[120120,120120],"mapped",[97]],[[120121,120121],"mapped",[98]],[[120122,120122],"disallowed"],[[120123,120123],"mapped",[100]],[[120124,120124],"mapped",[101]],[[120125,120125],"mapped",[102]],[[120126,120126],"mapped",[103]],[[120127,120127],"disallowed"],[[120128,120128],"mapped",[105]],[[120129,120129],"mapped",[106]],[[120130,120130],"mapped",[107]],[[120131,120131],"mapped",[108]],[[120132,120132],"mapped",[109]],[[120133,120133],"disallowed"],[[120134,120134],"mapped",[111]],[[120135,120137],"disallowed"],[[120138,120138],"mapped",[115]],[[120139,120139],"mapped",[116]],[[120140,120140],"mapped",[117]],[[120141,120141],"mapped",[118]],[[120142,120142],"mapped",[119]],[[120143,120143],"mapped",[120]],[[120144,120144],"mapped",[121]],[[120145,120145],"disallowed"],[[120146,120146],"mapped",[97]],[[120147,120147],"mapped",[98]],[[120148,120148],"mapped",[99]],[[120149,120149],"mapped",[100]],[[120150,120150],"mapped",[101]],[[120151,120151],"mapped",[102]],[[120152,120152],"mapped",[103]],[[120153,120153],"mapped",[104]],[[120154,120154],"mapped",[105]],[[120155,120155],"mapped",[106]],[[120156,120156],"mapped",[107]],[[120157,120157],"mapped",[108]],[[120158,120158],"mapped",[109]],[[120159,120159],"mapped",[110]],[[120160,120160],"mapped",[111]],[[120161,120161],"mapped",[112]],[[120162,120162],"mapped",[113]],[[120163,120163],"mapped",[114]],[[120164,120164],"mapped",[115]],[[120165,120165],"mapped",[116]],[[120166,120166],"mapped",[117]],[[120167,120167],"mapped",[118]],[[120168,120168],"mapped",[119]],[[120169,120169],"mapped",[120]],[[120170,120170],"mapped",[121]],[[120171,120171],"mapped",[122]],[[120172,120172],"mapped",[97]],[[120173,120173],"mapped",[98]],[[120174,120174],"mapped",[99]],[[120175,120175],"mapped",[100]],[[120176,120176],"mapped",[101]],[[120177,120177],"mapped",[102]],[[120178,120178],"mapped",[103]],[[120179,120179],"mapped",[104]],[[120180,120180],"mapped",[105]],[[120181,120181],"mapped",[106]],[[120182,120182],"mapped",[107]],[[120183,120183],"mapped",[108]],[[120184,120184],"mapped",[109]],[[120185,120185],"mapped",[110]],[[120186,120186],"mapped",[111]],[[120187,120187],"mapped",[112]],[[120188,120188],"mapped",[113]],[[120189,120189],"mapped",[114]],[[120190,120190],"mapped",[115]],[[120191,120191],"mapped",[116]],[[120192,120192],"mapped",[117]],[[120193,120193],"mapped",[118]],[[120194,120194],"mapped",[119]],[[120195,120195],"mapped",[120]],[[120196,120196],"mapped",[121]],[[120197,120197],"mapped",[122]],[[120198,120198],"mapped",[97]],[[120199,120199],"mapped",[98]],[[120200,120200],"mapped",[99]],[[120201,120201],"mapped",[100]],[[120202,120202],"mapped",[101]],[[120203,120203],"mapped",[102]],[[120204,120204],"mapped",[103]],[[120205,120205],"mapped",[104]],[[120206,120206],"mapped",[105]],[[120207,120207],"mapped",[106]],[[120208,120208],"mapped",[107]],[[120209,120209],"mapped",[108]],[[120210,120210],"mapped",[109]],[[120211,120211],"mapped",[110]],[[120212,120212],"mapped",[111]],[[120213,120213],"mapped",[112]],[[120214,120214],"mapped",[113]],[[120215,120215],"mapped",[114]],[[120216,120216],"mapped",[115]],[[120217,120217],"mapped",[116]],[[120218,120218],"mapped",[117]],[[120219,120219],"mapped",[118]],[[120220,120220],"mapped",[119]],[[120221,120221],"mapped",[120]],[[120222,120222],"mapped",[121]],[[120223,120223],"mapped",[122]],[[120224,120224],"mapped",[97]],[[120225,120225],"mapped",[98]],[[120226,120226],"mapped",[99]],[[120227,120227],"mapped",[100]],[[120228,120228],"mapped",[101]],[[120229,120229],"mapped",[102]],[[120230,120230],"mapped",[103]],[[120231,120231],"mapped",[104]],[[120232,120232],"mapped",[105]],[[120233,120233],"mapped",[106]],[[120234,120234],"mapped",[107]],[[120235,120235],"mapped",[108]],[[120236,120236],"mapped",[109]],[[120237,120237],"mapped",[110]],[[120238,120238],"mapped",[111]],[[120239,120239],"mapped",[112]],[[120240,120240],"mapped",[113]],[[120241,120241],"mapped",[114]],[[120242,120242],"mapped",[115]],[[120243,120243],"mapped",[116]],[[120244,120244],"mapped",[117]],[[120245,120245],"mapped",[118]],[[120246,120246],"mapped",[119]],[[120247,120247],"mapped",[120]],[[120248,120248],"mapped",[121]],[[120249,120249],"mapped",[122]],[[120250,120250],"mapped",[97]],[[120251,120251],"mapped",[98]],[[120252,120252],"mapped",[99]],[[120253,120253],"mapped",[100]],[[120254,120254],"mapped",[101]],[[120255,120255],"mapped",[102]],[[120256,120256],"mapped",[103]],[[120257,120257],"mapped",[104]],[[120258,120258],"mapped",[105]],[[120259,120259],"mapped",[106]],[[120260,120260],"mapped",[107]],[[120261,120261],"mapped",[108]],[[120262,120262],"mapped",[109]],[[120263,120263],"mapped",[110]],[[120264,120264],"mapped",[111]],[[120265,120265],"mapped",[112]],[[120266,120266],"mapped",[113]],[[120267,120267],"mapped",[114]],[[120268,120268],"mapped",[115]],[[120269,120269],"mapped",[116]],[[120270,120270],"mapped",[117]],[[120271,120271],"mapped",[118]],[[120272,120272],"mapped",[119]],[[120273,120273],"mapped",[120]],[[120274,120274],"mapped",[121]],[[120275,120275],"mapped",[122]],[[120276,120276],"mapped",[97]],[[120277,120277],"mapped",[98]],[[120278,120278],"mapped",[99]],[[120279,120279],"mapped",[100]],[[120280,120280],"mapped",[101]],[[120281,120281],"mapped",[102]],[[120282,120282],"mapped",[103]],[[120283,120283],"mapped",[104]],[[120284,120284],"mapped",[105]],[[120285,120285],"mapped",[106]],[[120286,120286],"mapped",[107]],[[120287,120287],"mapped",[108]],[[120288,120288],"mapped",[109]],[[120289,120289],"mapped",[110]],[[120290,120290],"mapped",[111]],[[120291,120291],"mapped",[112]],[[120292,120292],"mapped",[113]],[[120293,120293],"mapped",[114]],[[120294,120294],"mapped",[115]],[[120295,120295],"mapped",[116]],[[120296,120296],"mapped",[117]],[[120297,120297],"mapped",[118]],[[120298,120298],"mapped",[119]],[[120299,120299],"mapped",[120]],[[120300,120300],"mapped",[121]],[[120301,120301],"mapped",[122]],[[120302,120302],"mapped",[97]],[[120303,120303],"mapped",[98]],[[120304,120304],"mapped",[99]],[[120305,120305],"mapped",[100]],[[120306,120306],"mapped",[101]],[[120307,120307],"mapped",[102]],[[120308,120308],"mapped",[103]],[[120309,120309],"mapped",[104]],[[120310,120310],"mapped",[105]],[[120311,120311],"mapped",[106]],[[120312,120312],"mapped",[107]],[[120313,120313],"mapped",[108]],[[120314,120314],"mapped",[109]],[[120315,120315],"mapped",[110]],[[120316,120316],"mapped",[111]],[[120317,120317],"mapped",[112]],[[120318,120318],"mapped",[113]],[[120319,120319],"mapped",[114]],[[120320,120320],"mapped",[115]],[[120321,120321],"mapped",[116]],[[120322,120322],"mapped",[117]],[[120323,120323],"mapped",[118]],[[120324,120324],"mapped",[119]],[[120325,120325],"mapped",[120]],[[120326,120326],"mapped",[121]],[[120327,120327],"mapped",[122]],[[120328,120328],"mapped",[97]],[[120329,120329],"mapped",[98]],[[120330,120330],"mapped",[99]],[[120331,120331],"mapped",[100]],[[120332,120332],"mapped",[101]],[[120333,120333],"mapped",[102]],[[120334,120334],"mapped",[103]],[[120335,120335],"mapped",[104]],[[120336,120336],"mapped",[105]],[[120337,120337],"mapped",[106]],[[120338,120338],"mapped",[107]],[[120339,120339],"mapped",[108]],[[120340,120340],"mapped",[109]],[[120341,120341],"mapped",[110]],[[120342,120342],"mapped",[111]],[[120343,120343],"mapped",[112]],[[120344,120344],"mapped",[113]],[[120345,120345],"mapped",[114]],[[120346,120346],"mapped",[115]],[[120347,120347],"mapped",[116]],[[120348,120348],"mapped",[117]],[[120349,120349],"mapped",[118]],[[120350,120350],"mapped",[119]],[[120351,120351],"mapped",[120]],[[120352,120352],"mapped",[121]],[[120353,120353],"mapped",[122]],[[120354,120354],"mapped",[97]],[[120355,120355],"mapped",[98]],[[120356,120356],"mapped",[99]],[[120357,120357],"mapped",[100]],[[120358,120358],"mapped",[101]],[[120359,120359],"mapped",[102]],[[120360,120360],"mapped",[103]],[[120361,120361],"mapped",[104]],[[120362,120362],"mapped",[105]],[[120363,120363],"mapped",[106]],[[120364,120364],"mapped",[107]],[[120365,120365],"mapped",[108]],[[120366,120366],"mapped",[109]],[[120367,120367],"mapped",[110]],[[120368,120368],"mapped",[111]],[[120369,120369],"mapped",[112]],[[120370,120370],"mapped",[113]],[[120371,120371],"mapped",[114]],[[120372,120372],"mapped",[115]],[[120373,120373],"mapped",[116]],[[120374,120374],"mapped",[117]],[[120375,120375],"mapped",[118]],[[120376,120376],"mapped",[119]],[[120377,120377],"mapped",[120]],[[120378,120378],"mapped",[121]],[[120379,120379],"mapped",[122]],[[120380,120380],"mapped",[97]],[[120381,120381],"mapped",[98]],[[120382,120382],"mapped",[99]],[[120383,120383],"mapped",[100]],[[120384,120384],"mapped",[101]],[[120385,120385],"mapped",[102]],[[120386,120386],"mapped",[103]],[[120387,120387],"mapped",[104]],[[120388,120388],"mapped",[105]],[[120389,120389],"mapped",[106]],[[120390,120390],"mapped",[107]],[[120391,120391],"mapped",[108]],[[120392,120392],"mapped",[109]],[[120393,120393],"mapped",[110]],[[120394,120394],"mapped",[111]],[[120395,120395],"mapped",[112]],[[120396,120396],"mapped",[113]],[[120397,120397],"mapped",[114]],[[120398,120398],"mapped",[115]],[[120399,120399],"mapped",[116]],[[120400,120400],"mapped",[117]],[[120401,120401],"mapped",[118]],[[120402,120402],"mapped",[119]],[[120403,120403],"mapped",[120]],[[120404,120404],"mapped",[121]],[[120405,120405],"mapped",[122]],[[120406,120406],"mapped",[97]],[[120407,120407],"mapped",[98]],[[120408,120408],"mapped",[99]],[[120409,120409],"mapped",[100]],[[120410,120410],"mapped",[101]],[[120411,120411],"mapped",[102]],[[120412,120412],"mapped",[103]],[[120413,120413],"mapped",[104]],[[120414,120414],"mapped",[105]],[[120415,120415],"mapped",[106]],[[120416,120416],"mapped",[107]],[[120417,120417],"mapped",[108]],[[120418,120418],"mapped",[109]],[[120419,120419],"mapped",[110]],[[120420,120420],"mapped",[111]],[[120421,120421],"mapped",[112]],[[120422,120422],"mapped",[113]],[[120423,120423],"mapped",[114]],[[120424,120424],"mapped",[115]],[[120425,120425],"mapped",[116]],[[120426,120426],"mapped",[117]],[[120427,120427],"mapped",[118]],[[120428,120428],"mapped",[119]],[[120429,120429],"mapped",[120]],[[120430,120430],"mapped",[121]],[[120431,120431],"mapped",[122]],[[120432,120432],"mapped",[97]],[[120433,120433],"mapped",[98]],[[120434,120434],"mapped",[99]],[[120435,120435],"mapped",[100]],[[120436,120436],"mapped",[101]],[[120437,120437],"mapped",[102]],[[120438,120438],"mapped",[103]],[[120439,120439],"mapped",[104]],[[120440,120440],"mapped",[105]],[[120441,120441],"mapped",[106]],[[120442,120442],"mapped",[107]],[[120443,120443],"mapped",[108]],[[120444,120444],"mapped",[109]],[[120445,120445],"mapped",[110]],[[120446,120446],"mapped",[111]],[[120447,120447],"mapped",[112]],[[120448,120448],"mapped",[113]],[[120449,120449],"mapped",[114]],[[120450,120450],"mapped",[115]],[[120451,120451],"mapped",[116]],[[120452,120452],"mapped",[117]],[[120453,120453],"mapped",[118]],[[120454,120454],"mapped",[119]],[[120455,120455],"mapped",[120]],[[120456,120456],"mapped",[121]],[[120457,120457],"mapped",[122]],[[120458,120458],"mapped",[97]],[[120459,120459],"mapped",[98]],[[120460,120460],"mapped",[99]],[[120461,120461],"mapped",[100]],[[120462,120462],"mapped",[101]],[[120463,120463],"mapped",[102]],[[120464,120464],"mapped",[103]],[[120465,120465],"mapped",[104]],[[120466,120466],"mapped",[105]],[[120467,120467],"mapped",[106]],[[120468,120468],"mapped",[107]],[[120469,120469],"mapped",[108]],[[120470,120470],"mapped",[109]],[[120471,120471],"mapped",[110]],[[120472,120472],"mapped",[111]],[[120473,120473],"mapped",[112]],[[120474,120474],"mapped",[113]],[[120475,120475],"mapped",[114]],[[120476,120476],"mapped",[115]],[[120477,120477],"mapped",[116]],[[120478,120478],"mapped",[117]],[[120479,120479],"mapped",[118]],[[120480,120480],"mapped",[119]],[[120481,120481],"mapped",[120]],[[120482,120482],"mapped",[121]],[[120483,120483],"mapped",[122]],[[120484,120484],"mapped",[305]],[[120485,120485],"mapped",[567]],[[120486,120487],"disallowed"],[[120488,120488],"mapped",[945]],[[120489,120489],"mapped",[946]],[[120490,120490],"mapped",[947]],[[120491,120491],"mapped",[948]],[[120492,120492],"mapped",[949]],[[120493,120493],"mapped",[950]],[[120494,120494],"mapped",[951]],[[120495,120495],"mapped",[952]],[[120496,120496],"mapped",[953]],[[120497,120497],"mapped",[954]],[[120498,120498],"mapped",[955]],[[120499,120499],"mapped",[956]],[[120500,120500],"mapped",[957]],[[120501,120501],"mapped",[958]],[[120502,120502],"mapped",[959]],[[120503,120503],"mapped",[960]],[[120504,120504],"mapped",[961]],[[120505,120505],"mapped",[952]],[[120506,120506],"mapped",[963]],[[120507,120507],"mapped",[964]],[[120508,120508],"mapped",[965]],[[120509,120509],"mapped",[966]],[[120510,120510],"mapped",[967]],[[120511,120511],"mapped",[968]],[[120512,120512],"mapped",[969]],[[120513,120513],"mapped",[8711]],[[120514,120514],"mapped",[945]],[[120515,120515],"mapped",[946]],[[120516,120516],"mapped",[947]],[[120517,120517],"mapped",[948]],[[120518,120518],"mapped",[949]],[[120519,120519],"mapped",[950]],[[120520,120520],"mapped",[951]],[[120521,120521],"mapped",[952]],[[120522,120522],"mapped",[953]],[[120523,120523],"mapped",[954]],[[120524,120524],"mapped",[955]],[[120525,120525],"mapped",[956]],[[120526,120526],"mapped",[957]],[[120527,120527],"mapped",[958]],[[120528,120528],"mapped",[959]],[[120529,120529],"mapped",[960]],[[120530,120530],"mapped",[961]],[[120531,120532],"mapped",[963]],[[120533,120533],"mapped",[964]],[[120534,120534],"mapped",[965]],[[120535,120535],"mapped",[966]],[[120536,120536],"mapped",[967]],[[120537,120537],"mapped",[968]],[[120538,120538],"mapped",[969]],[[120539,120539],"mapped",[8706]],[[120540,120540],"mapped",[949]],[[120541,120541],"mapped",[952]],[[120542,120542],"mapped",[954]],[[120543,120543],"mapped",[966]],[[120544,120544],"mapped",[961]],[[120545,120545],"mapped",[960]],[[120546,120546],"mapped",[945]],[[120547,120547],"mapped",[946]],[[120548,120548],"mapped",[947]],[[120549,120549],"mapped",[948]],[[120550,120550],"mapped",[949]],[[120551,120551],"mapped",[950]],[[120552,120552],"mapped",[951]],[[120553,120553],"mapped",[952]],[[120554,120554],"mapped",[953]],[[120555,120555],"mapped",[954]],[[120556,120556],"mapped",[955]],[[120557,120557],"mapped",[956]],[[120558,120558],"mapped",[957]],[[120559,120559],"mapped",[958]],[[120560,120560],"mapped",[959]],[[120561,120561],"mapped",[960]],[[120562,120562],"mapped",[961]],[[120563,120563],"mapped",[952]],[[120564,120564],"mapped",[963]],[[120565,120565],"mapped",[964]],[[120566,120566],"mapped",[965]],[[120567,120567],"mapped",[966]],[[120568,120568],"mapped",[967]],[[120569,120569],"mapped",[968]],[[120570,120570],"mapped",[969]],[[120571,120571],"mapped",[8711]],[[120572,120572],"mapped",[945]],[[120573,120573],"mapped",[946]],[[120574,120574],"mapped",[947]],[[120575,120575],"mapped",[948]],[[120576,120576],"mapped",[949]],[[120577,120577],"mapped",[950]],[[120578,120578],"mapped",[951]],[[120579,120579],"mapped",[952]],[[120580,120580],"mapped",[953]],[[120581,120581],"mapped",[954]],[[120582,120582],"mapped",[955]],[[120583,120583],"mapped",[956]],[[120584,120584],"mapped",[957]],[[120585,120585],"mapped",[958]],[[120586,120586],"mapped",[959]],[[120587,120587],"mapped",[960]],[[120588,120588],"mapped",[961]],[[120589,120590],"mapped",[963]],[[120591,120591],"mapped",[964]],[[120592,120592],"mapped",[965]],[[120593,120593],"mapped",[966]],[[120594,120594],"mapped",[967]],[[120595,120595],"mapped",[968]],[[120596,120596],"mapped",[969]],[[120597,120597],"mapped",[8706]],[[120598,120598],"mapped",[949]],[[120599,120599],"mapped",[952]],[[120600,120600],"mapped",[954]],[[120601,120601],"mapped",[966]],[[120602,120602],"mapped",[961]],[[120603,120603],"mapped",[960]],[[120604,120604],"mapped",[945]],[[120605,120605],"mapped",[946]],[[120606,120606],"mapped",[947]],[[120607,120607],"mapped",[948]],[[120608,120608],"mapped",[949]],[[120609,120609],"mapped",[950]],[[120610,120610],"mapped",[951]],[[120611,120611],"mapped",[952]],[[120612,120612],"mapped",[953]],[[120613,120613],"mapped",[954]],[[120614,120614],"mapped",[955]],[[120615,120615],"mapped",[956]],[[120616,120616],"mapped",[957]],[[120617,120617],"mapped",[958]],[[120618,120618],"mapped",[959]],[[120619,120619],"mapped",[960]],[[120620,120620],"mapped",[961]],[[120621,120621],"mapped",[952]],[[120622,120622],"mapped",[963]],[[120623,120623],"mapped",[964]],[[120624,120624],"mapped",[965]],[[120625,120625],"mapped",[966]],[[120626,120626],"mapped",[967]],[[120627,120627],"mapped",[968]],[[120628,120628],"mapped",[969]],[[120629,120629],"mapped",[8711]],[[120630,120630],"mapped",[945]],[[120631,120631],"mapped",[946]],[[120632,120632],"mapped",[947]],[[120633,120633],"mapped",[948]],[[120634,120634],"mapped",[949]],[[120635,120635],"mapped",[950]],[[120636,120636],"mapped",[951]],[[120637,120637],"mapped",[952]],[[120638,120638],"mapped",[953]],[[120639,120639],"mapped",[954]],[[120640,120640],"mapped",[955]],[[120641,120641],"mapped",[956]],[[120642,120642],"mapped",[957]],[[120643,120643],"mapped",[958]],[[120644,120644],"mapped",[959]],[[120645,120645],"mapped",[960]],[[120646,120646],"mapped",[961]],[[120647,120648],"mapped",[963]],[[120649,120649],"mapped",[964]],[[120650,120650],"mapped",[965]],[[120651,120651],"mapped",[966]],[[120652,120652],"mapped",[967]],[[120653,120653],"mapped",[968]],[[120654,120654],"mapped",[969]],[[120655,120655],"mapped",[8706]],[[120656,120656],"mapped",[949]],[[120657,120657],"mapped",[952]],[[120658,120658],"mapped",[954]],[[120659,120659],"mapped",[966]],[[120660,120660],"mapped",[961]],[[120661,120661],"mapped",[960]],[[120662,120662],"mapped",[945]],[[120663,120663],"mapped",[946]],[[120664,120664],"mapped",[947]],[[120665,120665],"mapped",[948]],[[120666,120666],"mapped",[949]],[[120667,120667],"mapped",[950]],[[120668,120668],"mapped",[951]],[[120669,120669],"mapped",[952]],[[120670,120670],"mapped",[953]],[[120671,120671],"mapped",[954]],[[120672,120672],"mapped",[955]],[[120673,120673],"mapped",[956]],[[120674,120674],"mapped",[957]],[[120675,120675],"mapped",[958]],[[120676,120676],"mapped",[959]],[[120677,120677],"mapped",[960]],[[120678,120678],"mapped",[961]],[[120679,120679],"mapped",[952]],[[120680,120680],"mapped",[963]],[[120681,120681],"mapped",[964]],[[120682,120682],"mapped",[965]],[[120683,120683],"mapped",[966]],[[120684,120684],"mapped",[967]],[[120685,120685],"mapped",[968]],[[120686,120686],"mapped",[969]],[[120687,120687],"mapped",[8711]],[[120688,120688],"mapped",[945]],[[120689,120689],"mapped",[946]],[[120690,120690],"mapped",[947]],[[120691,120691],"mapped",[948]],[[120692,120692],"mapped",[949]],[[120693,120693],"mapped",[950]],[[120694,120694],"mapped",[951]],[[120695,120695],"mapped",[952]],[[120696,120696],"mapped",[953]],[[120697,120697],"mapped",[954]],[[120698,120698],"mapped",[955]],[[120699,120699],"mapped",[956]],[[120700,120700],"mapped",[957]],[[120701,120701],"mapped",[958]],[[120702,120702],"mapped",[959]],[[120703,120703],"mapped",[960]],[[120704,120704],"mapped",[961]],[[120705,120706],"mapped",[963]],[[120707,120707],"mapped",[964]],[[120708,120708],"mapped",[965]],[[120709,120709],"mapped",[966]],[[120710,120710],"mapped",[967]],[[120711,120711],"mapped",[968]],[[120712,120712],"mapped",[969]],[[120713,120713],"mapped",[8706]],[[120714,120714],"mapped",[949]],[[120715,120715],"mapped",[952]],[[120716,120716],"mapped",[954]],[[120717,120717],"mapped",[966]],[[120718,120718],"mapped",[961]],[[120719,120719],"mapped",[960]],[[120720,120720],"mapped",[945]],[[120721,120721],"mapped",[946]],[[120722,120722],"mapped",[947]],[[120723,120723],"mapped",[948]],[[120724,120724],"mapped",[949]],[[120725,120725],"mapped",[950]],[[120726,120726],"mapped",[951]],[[120727,120727],"mapped",[952]],[[120728,120728],"mapped",[953]],[[120729,120729],"mapped",[954]],[[120730,120730],"mapped",[955]],[[120731,120731],"mapped",[956]],[[120732,120732],"mapped",[957]],[[120733,120733],"mapped",[958]],[[120734,120734],"mapped",[959]],[[120735,120735],"mapped",[960]],[[120736,120736],"mapped",[961]],[[120737,120737],"mapped",[952]],[[120738,120738],"mapped",[963]],[[120739,120739],"mapped",[964]],[[120740,120740],"mapped",[965]],[[120741,120741],"mapped",[966]],[[120742,120742],"mapped",[967]],[[120743,120743],"mapped",[968]],[[120744,120744],"mapped",[969]],[[120745,120745],"mapped",[8711]],[[120746,120746],"mapped",[945]],[[120747,120747],"mapped",[946]],[[120748,120748],"mapped",[947]],[[120749,120749],"mapped",[948]],[[120750,120750],"mapped",[949]],[[120751,120751],"mapped",[950]],[[120752,120752],"mapped",[951]],[[120753,120753],"mapped",[952]],[[120754,120754],"mapped",[953]],[[120755,120755],"mapped",[954]],[[120756,120756],"mapped",[955]],[[120757,120757],"mapped",[956]],[[120758,120758],"mapped",[957]],[[120759,120759],"mapped",[958]],[[120760,120760],"mapped",[959]],[[120761,120761],"mapped",[960]],[[120762,120762],"mapped",[961]],[[120763,120764],"mapped",[963]],[[120765,120765],"mapped",[964]],[[120766,120766],"mapped",[965]],[[120767,120767],"mapped",[966]],[[120768,120768],"mapped",[967]],[[120769,120769],"mapped",[968]],[[120770,120770],"mapped",[969]],[[120771,120771],"mapped",[8706]],[[120772,120772],"mapped",[949]],[[120773,120773],"mapped",[952]],[[120774,120774],"mapped",[954]],[[120775,120775],"mapped",[966]],[[120776,120776],"mapped",[961]],[[120777,120777],"mapped",[960]],[[120778,120779],"mapped",[989]],[[120780,120781],"disallowed"],[[120782,120782],"mapped",[48]],[[120783,120783],"mapped",[49]],[[120784,120784],"mapped",[50]],[[120785,120785],"mapped",[51]],[[120786,120786],"mapped",[52]],[[120787,120787],"mapped",[53]],[[120788,120788],"mapped",[54]],[[120789,120789],"mapped",[55]],[[120790,120790],"mapped",[56]],[[120791,120791],"mapped",[57]],[[120792,120792],"mapped",[48]],[[120793,120793],"mapped",[49]],[[120794,120794],"mapped",[50]],[[120795,120795],"mapped",[51]],[[120796,120796],"mapped",[52]],[[120797,120797],"mapped",[53]],[[120798,120798],"mapped",[54]],[[120799,120799],"mapped",[55]],[[120800,120800],"mapped",[56]],[[120801,120801],"mapped",[57]],[[120802,120802],"mapped",[48]],[[120803,120803],"mapped",[49]],[[120804,120804],"mapped",[50]],[[120805,120805],"mapped",[51]],[[120806,120806],"mapped",[52]],[[120807,120807],"mapped",[53]],[[120808,120808],"mapped",[54]],[[120809,120809],"mapped",[55]],[[120810,120810],"mapped",[56]],[[120811,120811],"mapped",[57]],[[120812,120812],"mapped",[48]],[[120813,120813],"mapped",[49]],[[120814,120814],"mapped",[50]],[[120815,120815],"mapped",[51]],[[120816,120816],"mapped",[52]],[[120817,120817],"mapped",[53]],[[120818,120818],"mapped",[54]],[[120819,120819],"mapped",[55]],[[120820,120820],"mapped",[56]],[[120821,120821],"mapped",[57]],[[120822,120822],"mapped",[48]],[[120823,120823],"mapped",[49]],[[120824,120824],"mapped",[50]],[[120825,120825],"mapped",[51]],[[120826,120826],"mapped",[52]],[[120827,120827],"mapped",[53]],[[120828,120828],"mapped",[54]],[[120829,120829],"mapped",[55]],[[120830,120830],"mapped",[56]],[[120831,120831],"mapped",[57]],[[120832,121343],"valid",[],"NV8"],[[121344,121398],"valid"],[[121399,121402],"valid",[],"NV8"],[[121403,121452],"valid"],[[121453,121460],"valid",[],"NV8"],[[121461,121461],"valid"],[[121462,121475],"valid",[],"NV8"],[[121476,121476],"valid"],[[121477,121483],"valid",[],"NV8"],[[121484,121498],"disallowed"],[[121499,121503],"valid"],[[121504,121504],"disallowed"],[[121505,121519],"valid"],[[121520,124927],"disallowed"],[[124928,125124],"valid"],[[125125,125126],"disallowed"],[[125127,125135],"valid",[],"NV8"],[[125136,125142],"valid"],[[125143,126463],"disallowed"],[[126464,126464],"mapped",[1575]],[[126465,126465],"mapped",[1576]],[[126466,126466],"mapped",[1580]],[[126467,126467],"mapped",[1583]],[[126468,126468],"disallowed"],[[126469,126469],"mapped",[1608]],[[126470,126470],"mapped",[1586]],[[126471,126471],"mapped",[1581]],[[126472,126472],"mapped",[1591]],[[126473,126473],"mapped",[1610]],[[126474,126474],"mapped",[1603]],[[126475,126475],"mapped",[1604]],[[126476,126476],"mapped",[1605]],[[126477,126477],"mapped",[1606]],[[126478,126478],"mapped",[1587]],[[126479,126479],"mapped",[1593]],[[126480,126480],"mapped",[1601]],[[126481,126481],"mapped",[1589]],[[126482,126482],"mapped",[1602]],[[126483,126483],"mapped",[1585]],[[126484,126484],"mapped",[1588]],[[126485,126485],"mapped",[1578]],[[126486,126486],"mapped",[1579]],[[126487,126487],"mapped",[1582]],[[126488,126488],"mapped",[1584]],[[126489,126489],"mapped",[1590]],[[126490,126490],"mapped",[1592]],[[126491,126491],"mapped",[1594]],[[126492,126492],"mapped",[1646]],[[126493,126493],"mapped",[1722]],[[126494,126494],"mapped",[1697]],[[126495,126495],"mapped",[1647]],[[126496,126496],"disallowed"],[[126497,126497],"mapped",[1576]],[[126498,126498],"mapped",[1580]],[[126499,126499],"disallowed"],[[126500,126500],"mapped",[1607]],[[126501,126502],"disallowed"],[[126503,126503],"mapped",[1581]],[[126504,126504],"disallowed"],[[126505,126505],"mapped",[1610]],[[126506,126506],"mapped",[1603]],[[126507,126507],"mapped",[1604]],[[126508,126508],"mapped",[1605]],[[126509,126509],"mapped",[1606]],[[126510,126510],"mapped",[1587]],[[126511,126511],"mapped",[1593]],[[126512,126512],"mapped",[1601]],[[126513,126513],"mapped",[1589]],[[126514,126514],"mapped",[1602]],[[126515,126515],"disallowed"],[[126516,126516],"mapped",[1588]],[[126517,126517],"mapped",[1578]],[[126518,126518],"mapped",[1579]],[[126519,126519],"mapped",[1582]],[[126520,126520],"disallowed"],[[126521,126521],"mapped",[1590]],[[126522,126522],"disallowed"],[[126523,126523],"mapped",[1594]],[[126524,126529],"disallowed"],[[126530,126530],"mapped",[1580]],[[126531,126534],"disallowed"],[[126535,126535],"mapped",[1581]],[[126536,126536],"disallowed"],[[126537,126537],"mapped",[1610]],[[126538,126538],"disallowed"],[[126539,126539],"mapped",[1604]],[[126540,126540],"disallowed"],[[126541,126541],"mapped",[1606]],[[126542,126542],"mapped",[1587]],[[126543,126543],"mapped",[1593]],[[126544,126544],"disallowed"],[[126545,126545],"mapped",[1589]],[[126546,126546],"mapped",[1602]],[[126547,126547],"disallowed"],[[126548,126548],"mapped",[1588]],[[126549,126550],"disallowed"],[[126551,126551],"mapped",[1582]],[[126552,126552],"disallowed"],[[126553,126553],"mapped",[1590]],[[126554,126554],"disallowed"],[[126555,126555],"mapped",[1594]],[[126556,126556],"disallowed"],[[126557,126557],"mapped",[1722]],[[126558,126558],"disallowed"],[[126559,126559],"mapped",[1647]],[[126560,126560],"disallowed"],[[126561,126561],"mapped",[1576]],[[126562,126562],"mapped",[1580]],[[126563,126563],"disallowed"],[[126564,126564],"mapped",[1607]],[[126565,126566],"disallowed"],[[126567,126567],"mapped",[1581]],[[126568,126568],"mapped",[1591]],[[126569,126569],"mapped",[1610]],[[126570,126570],"mapped",[1603]],[[126571,126571],"disallowed"],[[126572,126572],"mapped",[1605]],[[126573,126573],"mapped",[1606]],[[126574,126574],"mapped",[1587]],[[126575,126575],"mapped",[1593]],[[126576,126576],"mapped",[1601]],[[126577,126577],"mapped",[1589]],[[126578,126578],"mapped",[1602]],[[126579,126579],"disallowed"],[[126580,126580],"mapped",[1588]],[[126581,126581],"mapped",[1578]],[[126582,126582],"mapped",[1579]],[[126583,126583],"mapped",[1582]],[[126584,126584],"disallowed"],[[126585,126585],"mapped",[1590]],[[126586,126586],"mapped",[1592]],[[126587,126587],"mapped",[1594]],[[126588,126588],"mapped",[1646]],[[126589,126589],"disallowed"],[[126590,126590],"mapped",[1697]],[[126591,126591],"disallowed"],[[126592,126592],"mapped",[1575]],[[126593,126593],"mapped",[1576]],[[126594,126594],"mapped",[1580]],[[126595,126595],"mapped",[1583]],[[126596,126596],"mapped",[1607]],[[126597,126597],"mapped",[1608]],[[126598,126598],"mapped",[1586]],[[126599,126599],"mapped",[1581]],[[126600,126600],"mapped",[1591]],[[126601,126601],"mapped",[1610]],[[126602,126602],"disallowed"],[[126603,126603],"mapped",[1604]],[[126604,126604],"mapped",[1605]],[[126605,126605],"mapped",[1606]],[[126606,126606],"mapped",[1587]],[[126607,126607],"mapped",[1593]],[[126608,126608],"mapped",[1601]],[[126609,126609],"mapped",[1589]],[[126610,126610],"mapped",[1602]],[[126611,126611],"mapped",[1585]],[[126612,126612],"mapped",[1588]],[[126613,126613],"mapped",[1578]],[[126614,126614],"mapped",[1579]],[[126615,126615],"mapped",[1582]],[[126616,126616],"mapped",[1584]],[[126617,126617],"mapped",[1590]],[[126618,126618],"mapped",[1592]],[[126619,126619],"mapped",[1594]],[[126620,126624],"disallowed"],[[126625,126625],"mapped",[1576]],[[126626,126626],"mapped",[1580]],[[126627,126627],"mapped",[1583]],[[126628,126628],"disallowed"],[[126629,126629],"mapped",[1608]],[[126630,126630],"mapped",[1586]],[[126631,126631],"mapped",[1581]],[[126632,126632],"mapped",[1591]],[[126633,126633],"mapped",[1610]],[[126634,126634],"disallowed"],[[126635,126635],"mapped",[1604]],[[126636,126636],"mapped",[1605]],[[126637,126637],"mapped",[1606]],[[126638,126638],"mapped",[1587]],[[126639,126639],"mapped",[1593]],[[126640,126640],"mapped",[1601]],[[126641,126641],"mapped",[1589]],[[126642,126642],"mapped",[1602]],[[126643,126643],"mapped",[1585]],[[126644,126644],"mapped",[1588]],[[126645,126645],"mapped",[1578]],[[126646,126646],"mapped",[1579]],[[126647,126647],"mapped",[1582]],[[126648,126648],"mapped",[1584]],[[126649,126649],"mapped",[1590]],[[126650,126650],"mapped",[1592]],[[126651,126651],"mapped",[1594]],[[126652,126703],"disallowed"],[[126704,126705],"valid",[],"NV8"],[[126706,126975],"disallowed"],[[126976,127019],"valid",[],"NV8"],[[127020,127023],"disallowed"],[[127024,127123],"valid",[],"NV8"],[[127124,127135],"disallowed"],[[127136,127150],"valid",[],"NV8"],[[127151,127152],"disallowed"],[[127153,127166],"valid",[],"NV8"],[[127167,127167],"valid",[],"NV8"],[[127168,127168],"disallowed"],[[127169,127183],"valid",[],"NV8"],[[127184,127184],"disallowed"],[[127185,127199],"valid",[],"NV8"],[[127200,127221],"valid",[],"NV8"],[[127222,127231],"disallowed"],[[127232,127232],"disallowed"],[[127233,127233],"disallowed_STD3_mapped",[48,44]],[[127234,127234],"disallowed_STD3_mapped",[49,44]],[[127235,127235],"disallowed_STD3_mapped",[50,44]],[[127236,127236],"disallowed_STD3_mapped",[51,44]],[[127237,127237],"disallowed_STD3_mapped",[52,44]],[[127238,127238],"disallowed_STD3_mapped",[53,44]],[[127239,127239],"disallowed_STD3_mapped",[54,44]],[[127240,127240],"disallowed_STD3_mapped",[55,44]],[[127241,127241],"disallowed_STD3_mapped",[56,44]],[[127242,127242],"disallowed_STD3_mapped",[57,44]],[[127243,127244],"valid",[],"NV8"],[[127245,127247],"disallowed"],[[127248,127248],"disallowed_STD3_mapped",[40,97,41]],[[127249,127249],"disallowed_STD3_mapped",[40,98,41]],[[127250,127250],"disallowed_STD3_mapped",[40,99,41]],[[127251,127251],"disallowed_STD3_mapped",[40,100,41]],[[127252,127252],"disallowed_STD3_mapped",[40,101,41]],[[127253,127253],"disallowed_STD3_mapped",[40,102,41]],[[127254,127254],"disallowed_STD3_mapped",[40,103,41]],[[127255,127255],"disallowed_STD3_mapped",[40,104,41]],[[127256,127256],"disallowed_STD3_mapped",[40,105,41]],[[127257,127257],"disallowed_STD3_mapped",[40,106,41]],[[127258,127258],"disallowed_STD3_mapped",[40,107,41]],[[127259,127259],"disallowed_STD3_mapped",[40,108,41]],[[127260,127260],"disallowed_STD3_mapped",[40,109,41]],[[127261,127261],"disallowed_STD3_mapped",[40,110,41]],[[127262,127262],"disallowed_STD3_mapped",[40,111,41]],[[127263,127263],"disallowed_STD3_mapped",[40,112,41]],[[127264,127264],"disallowed_STD3_mapped",[40,113,41]],[[127265,127265],"disallowed_STD3_mapped",[40,114,41]],[[127266,127266],"disallowed_STD3_mapped",[40,115,41]],[[127267,127267],"disallowed_STD3_mapped",[40,116,41]],[[127268,127268],"disallowed_STD3_mapped",[40,117,41]],[[127269,127269],"disallowed_STD3_mapped",[40,118,41]],[[127270,127270],"disallowed_STD3_mapped",[40,119,41]],[[127271,127271],"disallowed_STD3_mapped",[40,120,41]],[[127272,127272],"disallowed_STD3_mapped",[40,121,41]],[[127273,127273],"disallowed_STD3_mapped",[40,122,41]],[[127274,127274],"mapped",[12308,115,12309]],[[127275,127275],"mapped",[99]],[[127276,127276],"mapped",[114]],[[127277,127277],"mapped",[99,100]],[[127278,127278],"mapped",[119,122]],[[127279,127279],"disallowed"],[[127280,127280],"mapped",[97]],[[127281,127281],"mapped",[98]],[[127282,127282],"mapped",[99]],[[127283,127283],"mapped",[100]],[[127284,127284],"mapped",[101]],[[127285,127285],"mapped",[102]],[[127286,127286],"mapped",[103]],[[127287,127287],"mapped",[104]],[[127288,127288],"mapped",[105]],[[127289,127289],"mapped",[106]],[[127290,127290],"mapped",[107]],[[127291,127291],"mapped",[108]],[[127292,127292],"mapped",[109]],[[127293,127293],"mapped",[110]],[[127294,127294],"mapped",[111]],[[127295,127295],"mapped",[112]],[[127296,127296],"mapped",[113]],[[127297,127297],"mapped",[114]],[[127298,127298],"mapped",[115]],[[127299,127299],"mapped",[116]],[[127300,127300],"mapped",[117]],[[127301,127301],"mapped",[118]],[[127302,127302],"mapped",[119]],[[127303,127303],"mapped",[120]],[[127304,127304],"mapped",[121]],[[127305,127305],"mapped",[122]],[[127306,127306],"mapped",[104,118]],[[127307,127307],"mapped",[109,118]],[[127308,127308],"mapped",[115,100]],[[127309,127309],"mapped",[115,115]],[[127310,127310],"mapped",[112,112,118]],[[127311,127311],"mapped",[119,99]],[[127312,127318],"valid",[],"NV8"],[[127319,127319],"valid",[],"NV8"],[[127320,127326],"valid",[],"NV8"],[[127327,127327],"valid",[],"NV8"],[[127328,127337],"valid",[],"NV8"],[[127338,127338],"mapped",[109,99]],[[127339,127339],"mapped",[109,100]],[[127340,127343],"disallowed"],[[127344,127352],"valid",[],"NV8"],[[127353,127353],"valid",[],"NV8"],[[127354,127354],"valid",[],"NV8"],[[127355,127356],"valid",[],"NV8"],[[127357,127358],"valid",[],"NV8"],[[127359,127359],"valid",[],"NV8"],[[127360,127369],"valid",[],"NV8"],[[127370,127373],"valid",[],"NV8"],[[127374,127375],"valid",[],"NV8"],[[127376,127376],"mapped",[100,106]],[[127377,127386],"valid",[],"NV8"],[[127387,127461],"disallowed"],[[127462,127487],"valid",[],"NV8"],[[127488,127488],"mapped",[12411,12363]],[[127489,127489],"mapped",[12467,12467]],[[127490,127490],"mapped",[12469]],[[127491,127503],"disallowed"],[[127504,127504],"mapped",[25163]],[[127505,127505],"mapped",[23383]],[[127506,127506],"mapped",[21452]],[[127507,127507],"mapped",[12487]],[[127508,127508],"mapped",[20108]],[[127509,127509],"mapped",[22810]],[[127510,127510],"mapped",[35299]],[[127511,127511],"mapped",[22825]],[[127512,127512],"mapped",[20132]],[[127513,127513],"mapped",[26144]],[[127514,127514],"mapped",[28961]],[[127515,127515],"mapped",[26009]],[[127516,127516],"mapped",[21069]],[[127517,127517],"mapped",[24460]],[[127518,127518],"mapped",[20877]],[[127519,127519],"mapped",[26032]],[[127520,127520],"mapped",[21021]],[[127521,127521],"mapped",[32066]],[[127522,127522],"mapped",[29983]],[[127523,127523],"mapped",[36009]],[[127524,127524],"mapped",[22768]],[[127525,127525],"mapped",[21561]],[[127526,127526],"mapped",[28436]],[[127527,127527],"mapped",[25237]],[[127528,127528],"mapped",[25429]],[[127529,127529],"mapped",[19968]],[[127530,127530],"mapped",[19977]],[[127531,127531],"mapped",[36938]],[[127532,127532],"mapped",[24038]],[[127533,127533],"mapped",[20013]],[[127534,127534],"mapped",[21491]],[[127535,127535],"mapped",[25351]],[[127536,127536],"mapped",[36208]],[[127537,127537],"mapped",[25171]],[[127538,127538],"mapped",[31105]],[[127539,127539],"mapped",[31354]],[[127540,127540],"mapped",[21512]],[[127541,127541],"mapped",[28288]],[[127542,127542],"mapped",[26377]],[[127543,127543],"mapped",[26376]],[[127544,127544],"mapped",[30003]],[[127545,127545],"mapped",[21106]],[[127546,127546],"mapped",[21942]],[[127547,127551],"disallowed"],[[127552,127552],"mapped",[12308,26412,12309]],[[127553,127553],"mapped",[12308,19977,12309]],[[127554,127554],"mapped",[12308,20108,12309]],[[127555,127555],"mapped",[12308,23433,12309]],[[127556,127556],"mapped",[12308,28857,12309]],[[127557,127557],"mapped",[12308,25171,12309]],[[127558,127558],"mapped",[12308,30423,12309]],[[127559,127559],"mapped",[12308,21213,12309]],[[127560,127560],"mapped",[12308,25943,12309]],[[127561,127567],"disallowed"],[[127568,127568],"mapped",[24471]],[[127569,127569],"mapped",[21487]],[[127570,127743],"disallowed"],[[127744,127776],"valid",[],"NV8"],[[127777,127788],"valid",[],"NV8"],[[127789,127791],"valid",[],"NV8"],[[127792,127797],"valid",[],"NV8"],[[127798,127798],"valid",[],"NV8"],[[127799,127868],"valid",[],"NV8"],[[127869,127869],"valid",[],"NV8"],[[127870,127871],"valid",[],"NV8"],[[127872,127891],"valid",[],"NV8"],[[127892,127903],"valid",[],"NV8"],[[127904,127940],"valid",[],"NV8"],[[127941,127941],"valid",[],"NV8"],[[127942,127946],"valid",[],"NV8"],[[127947,127950],"valid",[],"NV8"],[[127951,127955],"valid",[],"NV8"],[[127956,127967],"valid",[],"NV8"],[[127968,127984],"valid",[],"NV8"],[[127985,127991],"valid",[],"NV8"],[[127992,127999],"valid",[],"NV8"],[[128e3,128062],"valid",[],"NV8"],[[128063,128063],"valid",[],"NV8"],[[128064,128064],"valid",[],"NV8"],[[128065,128065],"valid",[],"NV8"],[[128066,128247],"valid",[],"NV8"],[[128248,128248],"valid",[],"NV8"],[[128249,128252],"valid",[],"NV8"],[[128253,128254],"valid",[],"NV8"],[[128255,128255],"valid",[],"NV8"],[[128256,128317],"valid",[],"NV8"],[[128318,128319],"valid",[],"NV8"],[[128320,128323],"valid",[],"NV8"],[[128324,128330],"valid",[],"NV8"],[[128331,128335],"valid",[],"NV8"],[[128336,128359],"valid",[],"NV8"],[[128360,128377],"valid",[],"NV8"],[[128378,128378],"disallowed"],[[128379,128419],"valid",[],"NV8"],[[128420,128420],"disallowed"],[[128421,128506],"valid",[],"NV8"],[[128507,128511],"valid",[],"NV8"],[[128512,128512],"valid",[],"NV8"],[[128513,128528],"valid",[],"NV8"],[[128529,128529],"valid",[],"NV8"],[[128530,128532],"valid",[],"NV8"],[[128533,128533],"valid",[],"NV8"],[[128534,128534],"valid",[],"NV8"],[[128535,128535],"valid",[],"NV8"],[[128536,128536],"valid",[],"NV8"],[[128537,128537],"valid",[],"NV8"],[[128538,128538],"valid",[],"NV8"],[[128539,128539],"valid",[],"NV8"],[[128540,128542],"valid",[],"NV8"],[[128543,128543],"valid",[],"NV8"],[[128544,128549],"valid",[],"NV8"],[[128550,128551],"valid",[],"NV8"],[[128552,128555],"valid",[],"NV8"],[[128556,128556],"valid",[],"NV8"],[[128557,128557],"valid",[],"NV8"],[[128558,128559],"valid",[],"NV8"],[[128560,128563],"valid",[],"NV8"],[[128564,128564],"valid",[],"NV8"],[[128565,128576],"valid",[],"NV8"],[[128577,128578],"valid",[],"NV8"],[[128579,128580],"valid",[],"NV8"],[[128581,128591],"valid",[],"NV8"],[[128592,128639],"valid",[],"NV8"],[[128640,128709],"valid",[],"NV8"],[[128710,128719],"valid",[],"NV8"],[[128720,128720],"valid",[],"NV8"],[[128721,128735],"disallowed"],[[128736,128748],"valid",[],"NV8"],[[128749,128751],"disallowed"],[[128752,128755],"valid",[],"NV8"],[[128756,128767],"disallowed"],[[128768,128883],"valid",[],"NV8"],[[128884,128895],"disallowed"],[[128896,128980],"valid",[],"NV8"],[[128981,129023],"disallowed"],[[129024,129035],"valid",[],"NV8"],[[129036,129039],"disallowed"],[[129040,129095],"valid",[],"NV8"],[[129096,129103],"disallowed"],[[129104,129113],"valid",[],"NV8"],[[129114,129119],"disallowed"],[[129120,129159],"valid",[],"NV8"],[[129160,129167],"disallowed"],[[129168,129197],"valid",[],"NV8"],[[129198,129295],"disallowed"],[[129296,129304],"valid",[],"NV8"],[[129305,129407],"disallowed"],[[129408,129412],"valid",[],"NV8"],[[129413,129471],"disallowed"],[[129472,129472],"valid",[],"NV8"],[[129473,131069],"disallowed"],[[131070,131071],"disallowed"],[[131072,173782],"valid"],[[173783,173823],"disallowed"],[[173824,177972],"valid"],[[177973,177983],"disallowed"],[[177984,178205],"valid"],[[178206,178207],"disallowed"],[[178208,183969],"valid"],[[183970,194559],"disallowed"],[[194560,194560],"mapped",[20029]],[[194561,194561],"mapped",[20024]],[[194562,194562],"mapped",[20033]],[[194563,194563],"mapped",[131362]],[[194564,194564],"mapped",[20320]],[[194565,194565],"mapped",[20398]],[[194566,194566],"mapped",[20411]],[[194567,194567],"mapped",[20482]],[[194568,194568],"mapped",[20602]],[[194569,194569],"mapped",[20633]],[[194570,194570],"mapped",[20711]],[[194571,194571],"mapped",[20687]],[[194572,194572],"mapped",[13470]],[[194573,194573],"mapped",[132666]],[[194574,194574],"mapped",[20813]],[[194575,194575],"mapped",[20820]],[[194576,194576],"mapped",[20836]],[[194577,194577],"mapped",[20855]],[[194578,194578],"mapped",[132380]],[[194579,194579],"mapped",[13497]],[[194580,194580],"mapped",[20839]],[[194581,194581],"mapped",[20877]],[[194582,194582],"mapped",[132427]],[[194583,194583],"mapped",[20887]],[[194584,194584],"mapped",[20900]],[[194585,194585],"mapped",[20172]],[[194586,194586],"mapped",[20908]],[[194587,194587],"mapped",[20917]],[[194588,194588],"mapped",[168415]],[[194589,194589],"mapped",[20981]],[[194590,194590],"mapped",[20995]],[[194591,194591],"mapped",[13535]],[[194592,194592],"mapped",[21051]],[[194593,194593],"mapped",[21062]],[[194594,194594],"mapped",[21106]],[[194595,194595],"mapped",[21111]],[[194596,194596],"mapped",[13589]],[[194597,194597],"mapped",[21191]],[[194598,194598],"mapped",[21193]],[[194599,194599],"mapped",[21220]],[[194600,194600],"mapped",[21242]],[[194601,194601],"mapped",[21253]],[[194602,194602],"mapped",[21254]],[[194603,194603],"mapped",[21271]],[[194604,194604],"mapped",[21321]],[[194605,194605],"mapped",[21329]],[[194606,194606],"mapped",[21338]],[[194607,194607],"mapped",[21363]],[[194608,194608],"mapped",[21373]],[[194609,194611],"mapped",[21375]],[[194612,194612],"mapped",[133676]],[[194613,194613],"mapped",[28784]],[[194614,194614],"mapped",[21450]],[[194615,194615],"mapped",[21471]],[[194616,194616],"mapped",[133987]],[[194617,194617],"mapped",[21483]],[[194618,194618],"mapped",[21489]],[[194619,194619],"mapped",[21510]],[[194620,194620],"mapped",[21662]],[[194621,194621],"mapped",[21560]],[[194622,194622],"mapped",[21576]],[[194623,194623],"mapped",[21608]],[[194624,194624],"mapped",[21666]],[[194625,194625],"mapped",[21750]],[[194626,194626],"mapped",[21776]],[[194627,194627],"mapped",[21843]],[[194628,194628],"mapped",[21859]],[[194629,194630],"mapped",[21892]],[[194631,194631],"mapped",[21913]],[[194632,194632],"mapped",[21931]],[[194633,194633],"mapped",[21939]],[[194634,194634],"mapped",[21954]],[[194635,194635],"mapped",[22294]],[[194636,194636],"mapped",[22022]],[[194637,194637],"mapped",[22295]],[[194638,194638],"mapped",[22097]],[[194639,194639],"mapped",[22132]],[[194640,194640],"mapped",[20999]],[[194641,194641],"mapped",[22766]],[[194642,194642],"mapped",[22478]],[[194643,194643],"mapped",[22516]],[[194644,194644],"mapped",[22541]],[[194645,194645],"mapped",[22411]],[[194646,194646],"mapped",[22578]],[[194647,194647],"mapped",[22577]],[[194648,194648],"mapped",[22700]],[[194649,194649],"mapped",[136420]],[[194650,194650],"mapped",[22770]],[[194651,194651],"mapped",[22775]],[[194652,194652],"mapped",[22790]],[[194653,194653],"mapped",[22810]],[[194654,194654],"mapped",[22818]],[[194655,194655],"mapped",[22882]],[[194656,194656],"mapped",[136872]],[[194657,194657],"mapped",[136938]],[[194658,194658],"mapped",[23020]],[[194659,194659],"mapped",[23067]],[[194660,194660],"mapped",[23079]],[[194661,194661],"mapped",[23e3]],[[194662,194662],"mapped",[23142]],[[194663,194663],"mapped",[14062]],[[194664,194664],"disallowed"],[[194665,194665],"mapped",[23304]],[[194666,194667],"mapped",[23358]],[[194668,194668],"mapped",[137672]],[[194669,194669],"mapped",[23491]],[[194670,194670],"mapped",[23512]],[[194671,194671],"mapped",[23527]],[[194672,194672],"mapped",[23539]],[[194673,194673],"mapped",[138008]],[[194674,194674],"mapped",[23551]],[[194675,194675],"mapped",[23558]],[[194676,194676],"disallowed"],[[194677,194677],"mapped",[23586]],[[194678,194678],"mapped",[14209]],[[194679,194679],"mapped",[23648]],[[194680,194680],"mapped",[23662]],[[194681,194681],"mapped",[23744]],[[194682,194682],"mapped",[23693]],[[194683,194683],"mapped",[138724]],[[194684,194684],"mapped",[23875]],[[194685,194685],"mapped",[138726]],[[194686,194686],"mapped",[23918]],[[194687,194687],"mapped",[23915]],[[194688,194688],"mapped",[23932]],[[194689,194689],"mapped",[24033]],[[194690,194690],"mapped",[24034]],[[194691,194691],"mapped",[14383]],[[194692,194692],"mapped",[24061]],[[194693,194693],"mapped",[24104]],[[194694,194694],"mapped",[24125]],[[194695,194695],"mapped",[24169]],[[194696,194696],"mapped",[14434]],[[194697,194697],"mapped",[139651]],[[194698,194698],"mapped",[14460]],[[194699,194699],"mapped",[24240]],[[194700,194700],"mapped",[24243]],[[194701,194701],"mapped",[24246]],[[194702,194702],"mapped",[24266]],[[194703,194703],"mapped",[172946]],[[194704,194704],"mapped",[24318]],[[194705,194706],"mapped",[140081]],[[194707,194707],"mapped",[33281]],[[194708,194709],"mapped",[24354]],[[194710,194710],"mapped",[14535]],[[194711,194711],"mapped",[144056]],[[194712,194712],"mapped",[156122]],[[194713,194713],"mapped",[24418]],[[194714,194714],"mapped",[24427]],[[194715,194715],"mapped",[14563]],[[194716,194716],"mapped",[24474]],[[194717,194717],"mapped",[24525]],[[194718,194718],"mapped",[24535]],[[194719,194719],"mapped",[24569]],[[194720,194720],"mapped",[24705]],[[194721,194721],"mapped",[14650]],[[194722,194722],"mapped",[14620]],[[194723,194723],"mapped",[24724]],[[194724,194724],"mapped",[141012]],[[194725,194725],"mapped",[24775]],[[194726,194726],"mapped",[24904]],[[194727,194727],"mapped",[24908]],[[194728,194728],"mapped",[24910]],[[194729,194729],"mapped",[24908]],[[194730,194730],"mapped",[24954]],[[194731,194731],"mapped",[24974]],[[194732,194732],"mapped",[25010]],[[194733,194733],"mapped",[24996]],[[194734,194734],"mapped",[25007]],[[194735,194735],"mapped",[25054]],[[194736,194736],"mapped",[25074]],[[194737,194737],"mapped",[25078]],[[194738,194738],"mapped",[25104]],[[194739,194739],"mapped",[25115]],[[194740,194740],"mapped",[25181]],[[194741,194741],"mapped",[25265]],[[194742,194742],"mapped",[25300]],[[194743,194743],"mapped",[25424]],[[194744,194744],"mapped",[142092]],[[194745,194745],"mapped",[25405]],[[194746,194746],"mapped",[25340]],[[194747,194747],"mapped",[25448]],[[194748,194748],"mapped",[25475]],[[194749,194749],"mapped",[25572]],[[194750,194750],"mapped",[142321]],[[194751,194751],"mapped",[25634]],[[194752,194752],"mapped",[25541]],[[194753,194753],"mapped",[25513]],[[194754,194754],"mapped",[14894]],[[194755,194755],"mapped",[25705]],[[194756,194756],"mapped",[25726]],[[194757,194757],"mapped",[25757]],[[194758,194758],"mapped",[25719]],[[194759,194759],"mapped",[14956]],[[194760,194760],"mapped",[25935]],[[194761,194761],"mapped",[25964]],[[194762,194762],"mapped",[143370]],[[194763,194763],"mapped",[26083]],[[194764,194764],"mapped",[26360]],[[194765,194765],"mapped",[26185]],[[194766,194766],"mapped",[15129]],[[194767,194767],"mapped",[26257]],[[194768,194768],"mapped",[15112]],[[194769,194769],"mapped",[15076]],[[194770,194770],"mapped",[20882]],[[194771,194771],"mapped",[20885]],[[194772,194772],"mapped",[26368]],[[194773,194773],"mapped",[26268]],[[194774,194774],"mapped",[32941]],[[194775,194775],"mapped",[17369]],[[194776,194776],"mapped",[26391]],[[194777,194777],"mapped",[26395]],[[194778,194778],"mapped",[26401]],[[194779,194779],"mapped",[26462]],[[194780,194780],"mapped",[26451]],[[194781,194781],"mapped",[144323]],[[194782,194782],"mapped",[15177]],[[194783,194783],"mapped",[26618]],[[194784,194784],"mapped",[26501]],[[194785,194785],"mapped",[26706]],[[194786,194786],"mapped",[26757]],[[194787,194787],"mapped",[144493]],[[194788,194788],"mapped",[26766]],[[194789,194789],"mapped",[26655]],[[194790,194790],"mapped",[26900]],[[194791,194791],"mapped",[15261]],[[194792,194792],"mapped",[26946]],[[194793,194793],"mapped",[27043]],[[194794,194794],"mapped",[27114]],[[194795,194795],"mapped",[27304]],[[194796,194796],"mapped",[145059]],[[194797,194797],"mapped",[27355]],[[194798,194798],"mapped",[15384]],[[194799,194799],"mapped",[27425]],[[194800,194800],"mapped",[145575]],[[194801,194801],"mapped",[27476]],[[194802,194802],"mapped",[15438]],[[194803,194803],"mapped",[27506]],[[194804,194804],"mapped",[27551]],[[194805,194805],"mapped",[27578]],[[194806,194806],"mapped",[27579]],[[194807,194807],"mapped",[146061]],[[194808,194808],"mapped",[138507]],[[194809,194809],"mapped",[146170]],[[194810,194810],"mapped",[27726]],[[194811,194811],"mapped",[146620]],[[194812,194812],"mapped",[27839]],[[194813,194813],"mapped",[27853]],[[194814,194814],"mapped",[27751]],[[194815,194815],"mapped",[27926]],[[194816,194816],"mapped",[27966]],[[194817,194817],"mapped",[28023]],[[194818,194818],"mapped",[27969]],[[194819,194819],"mapped",[28009]],[[194820,194820],"mapped",[28024]],[[194821,194821],"mapped",[28037]],[[194822,194822],"mapped",[146718]],[[194823,194823],"mapped",[27956]],[[194824,194824],"mapped",[28207]],[[194825,194825],"mapped",[28270]],[[194826,194826],"mapped",[15667]],[[194827,194827],"mapped",[28363]],[[194828,194828],"mapped",[28359]],[[194829,194829],"mapped",[147153]],[[194830,194830],"mapped",[28153]],[[194831,194831],"mapped",[28526]],[[194832,194832],"mapped",[147294]],[[194833,194833],"mapped",[147342]],[[194834,194834],"mapped",[28614]],[[194835,194835],"mapped",[28729]],[[194836,194836],"mapped",[28702]],[[194837,194837],"mapped",[28699]],[[194838,194838],"mapped",[15766]],[[194839,194839],"mapped",[28746]],[[194840,194840],"mapped",[28797]],[[194841,194841],"mapped",[28791]],[[194842,194842],"mapped",[28845]],[[194843,194843],"mapped",[132389]],[[194844,194844],"mapped",[28997]],[[194845,194845],"mapped",[148067]],[[194846,194846],"mapped",[29084]],[[194847,194847],"disallowed"],[[194848,194848],"mapped",[29224]],[[194849,194849],"mapped",[29237]],[[194850,194850],"mapped",[29264]],[[194851,194851],"mapped",[149e3]],[[194852,194852],"mapped",[29312]],[[194853,194853],"mapped",[29333]],[[194854,194854],"mapped",[149301]],[[194855,194855],"mapped",[149524]],[[194856,194856],"mapped",[29562]],[[194857,194857],"mapped",[29579]],[[194858,194858],"mapped",[16044]],[[194859,194859],"mapped",[29605]],[[194860,194861],"mapped",[16056]],[[194862,194862],"mapped",[29767]],[[194863,194863],"mapped",[29788]],[[194864,194864],"mapped",[29809]],[[194865,194865],"mapped",[29829]],[[194866,194866],"mapped",[29898]],[[194867,194867],"mapped",[16155]],[[194868,194868],"mapped",[29988]],[[194869,194869],"mapped",[150582]],[[194870,194870],"mapped",[30014]],[[194871,194871],"mapped",[150674]],[[194872,194872],"mapped",[30064]],[[194873,194873],"mapped",[139679]],[[194874,194874],"mapped",[30224]],[[194875,194875],"mapped",[151457]],[[194876,194876],"mapped",[151480]],[[194877,194877],"mapped",[151620]],[[194878,194878],"mapped",[16380]],[[194879,194879],"mapped",[16392]],[[194880,194880],"mapped",[30452]],[[194881,194881],"mapped",[151795]],[[194882,194882],"mapped",[151794]],[[194883,194883],"mapped",[151833]],[[194884,194884],"mapped",[151859]],[[194885,194885],"mapped",[30494]],[[194886,194887],"mapped",[30495]],[[194888,194888],"mapped",[30538]],[[194889,194889],"mapped",[16441]],[[194890,194890],"mapped",[30603]],[[194891,194891],"mapped",[16454]],[[194892,194892],"mapped",[16534]],[[194893,194893],"mapped",[152605]],[[194894,194894],"mapped",[30798]],[[194895,194895],"mapped",[30860]],[[194896,194896],"mapped",[30924]],[[194897,194897],"mapped",[16611]],[[194898,194898],"mapped",[153126]],[[194899,194899],"mapped",[31062]],[[194900,194900],"mapped",[153242]],[[194901,194901],"mapped",[153285]],[[194902,194902],"mapped",[31119]],[[194903,194903],"mapped",[31211]],[[194904,194904],"mapped",[16687]],[[194905,194905],"mapped",[31296]],[[194906,194906],"mapped",[31306]],[[194907,194907],"mapped",[31311]],[[194908,194908],"mapped",[153980]],[[194909,194910],"mapped",[154279]],[[194911,194911],"disallowed"],[[194912,194912],"mapped",[16898]],[[194913,194913],"mapped",[154539]],[[194914,194914],"mapped",[31686]],[[194915,194915],"mapped",[31689]],[[194916,194916],"mapped",[16935]],[[194917,194917],"mapped",[154752]],[[194918,194918],"mapped",[31954]],[[194919,194919],"mapped",[17056]],[[194920,194920],"mapped",[31976]],[[194921,194921],"mapped",[31971]],[[194922,194922],"mapped",[32e3]],[[194923,194923],"mapped",[155526]],[[194924,194924],"mapped",[32099]],[[194925,194925],"mapped",[17153]],[[194926,194926],"mapped",[32199]],[[194927,194927],"mapped",[32258]],[[194928,194928],"mapped",[32325]],[[194929,194929],"mapped",[17204]],[[194930,194930],"mapped",[156200]],[[194931,194931],"mapped",[156231]],[[194932,194932],"mapped",[17241]],[[194933,194933],"mapped",[156377]],[[194934,194934],"mapped",[32634]],[[194935,194935],"mapped",[156478]],[[194936,194936],"mapped",[32661]],[[194937,194937],"mapped",[32762]],[[194938,194938],"mapped",[32773]],[[194939,194939],"mapped",[156890]],[[194940,194940],"mapped",[156963]],[[194941,194941],"mapped",[32864]],[[194942,194942],"mapped",[157096]],[[194943,194943],"mapped",[32880]],[[194944,194944],"mapped",[144223]],[[194945,194945],"mapped",[17365]],[[194946,194946],"mapped",[32946]],[[194947,194947],"mapped",[33027]],[[194948,194948],"mapped",[17419]],[[194949,194949],"mapped",[33086]],[[194950,194950],"mapped",[23221]],[[194951,194951],"mapped",[157607]],[[194952,194952],"mapped",[157621]],[[194953,194953],"mapped",[144275]],[[194954,194954],"mapped",[144284]],[[194955,194955],"mapped",[33281]],[[194956,194956],"mapped",[33284]],[[194957,194957],"mapped",[36766]],[[194958,194958],"mapped",[17515]],[[194959,194959],"mapped",[33425]],[[194960,194960],"mapped",[33419]],[[194961,194961],"mapped",[33437]],[[194962,194962],"mapped",[21171]],[[194963,194963],"mapped",[33457]],[[194964,194964],"mapped",[33459]],[[194965,194965],"mapped",[33469]],[[194966,194966],"mapped",[33510]],[[194967,194967],"mapped",[158524]],[[194968,194968],"mapped",[33509]],[[194969,194969],"mapped",[33565]],[[194970,194970],"mapped",[33635]],[[194971,194971],"mapped",[33709]],[[194972,194972],"mapped",[33571]],[[194973,194973],"mapped",[33725]],[[194974,194974],"mapped",[33767]],[[194975,194975],"mapped",[33879]],[[194976,194976],"mapped",[33619]],[[194977,194977],"mapped",[33738]],[[194978,194978],"mapped",[33740]],[[194979,194979],"mapped",[33756]],[[194980,194980],"mapped",[158774]],[[194981,194981],"mapped",[159083]],[[194982,194982],"mapped",[158933]],[[194983,194983],"mapped",[17707]],[[194984,194984],"mapped",[34033]],[[194985,194985],"mapped",[34035]],[[194986,194986],"mapped",[34070]],[[194987,194987],"mapped",[160714]],[[194988,194988],"mapped",[34148]],[[194989,194989],"mapped",[159532]],[[194990,194990],"mapped",[17757]],[[194991,194991],"mapped",[17761]],[[194992,194992],"mapped",[159665]],[[194993,194993],"mapped",[159954]],[[194994,194994],"mapped",[17771]],[[194995,194995],"mapped",[34384]],[[194996,194996],"mapped",[34396]],[[194997,194997],"mapped",[34407]],[[194998,194998],"mapped",[34409]],[[194999,194999],"mapped",[34473]],[[195e3,195e3],"mapped",[34440]],[[195001,195001],"mapped",[34574]],[[195002,195002],"mapped",[34530]],[[195003,195003],"mapped",[34681]],[[195004,195004],"mapped",[34600]],[[195005,195005],"mapped",[34667]],[[195006,195006],"mapped",[34694]],[[195007,195007],"disallowed"],[[195008,195008],"mapped",[34785]],[[195009,195009],"mapped",[34817]],[[195010,195010],"mapped",[17913]],[[195011,195011],"mapped",[34912]],[[195012,195012],"mapped",[34915]],[[195013,195013],"mapped",[161383]],[[195014,195014],"mapped",[35031]],[[195015,195015],"mapped",[35038]],[[195016,195016],"mapped",[17973]],[[195017,195017],"mapped",[35066]],[[195018,195018],"mapped",[13499]],[[195019,195019],"mapped",[161966]],[[195020,195020],"mapped",[162150]],[[195021,195021],"mapped",[18110]],[[195022,195022],"mapped",[18119]],[[195023,195023],"mapped",[35488]],[[195024,195024],"mapped",[35565]],[[195025,195025],"mapped",[35722]],[[195026,195026],"mapped",[35925]],[[195027,195027],"mapped",[162984]],[[195028,195028],"mapped",[36011]],[[195029,195029],"mapped",[36033]],[[195030,195030],"mapped",[36123]],[[195031,195031],"mapped",[36215]],[[195032,195032],"mapped",[163631]],[[195033,195033],"mapped",[133124]],[[195034,195034],"mapped",[36299]],[[195035,195035],"mapped",[36284]],[[195036,195036],"mapped",[36336]],[[195037,195037],"mapped",[133342]],[[195038,195038],"mapped",[36564]],[[195039,195039],"mapped",[36664]],[[195040,195040],"mapped",[165330]],[[195041,195041],"mapped",[165357]],[[195042,195042],"mapped",[37012]],[[195043,195043],"mapped",[37105]],[[195044,195044],"mapped",[37137]],[[195045,195045],"mapped",[165678]],[[195046,195046],"mapped",[37147]],[[195047,195047],"mapped",[37432]],[[195048,195048],"mapped",[37591]],[[195049,195049],"mapped",[37592]],[[195050,195050],"mapped",[37500]],[[195051,195051],"mapped",[37881]],[[195052,195052],"mapped",[37909]],[[195053,195053],"mapped",[166906]],[[195054,195054],"mapped",[38283]],[[195055,195055],"mapped",[18837]],[[195056,195056],"mapped",[38327]],[[195057,195057],"mapped",[167287]],[[195058,195058],"mapped",[18918]],[[195059,195059],"mapped",[38595]],[[195060,195060],"mapped",[23986]],[[195061,195061],"mapped",[38691]],[[195062,195062],"mapped",[168261]],[[195063,195063],"mapped",[168474]],[[195064,195064],"mapped",[19054]],[[195065,195065],"mapped",[19062]],[[195066,195066],"mapped",[38880]],[[195067,195067],"mapped",[168970]],[[195068,195068],"mapped",[19122]],[[195069,195069],"mapped",[169110]],[[195070,195071],"mapped",[38923]],[[195072,195072],"mapped",[38953]],[[195073,195073],"mapped",[169398]],[[195074,195074],"mapped",[39138]],[[195075,195075],"mapped",[19251]],[[195076,195076],"mapped",[39209]],[[195077,195077],"mapped",[39335]],[[195078,195078],"mapped",[39362]],[[195079,195079],"mapped",[39422]],[[195080,195080],"mapped",[19406]],[[195081,195081],"mapped",[170800]],[[195082,195082],"mapped",[39698]],[[195083,195083],"mapped",[4e4]],[[195084,195084],"mapped",[40189]],[[195085,195085],"mapped",[19662]],[[195086,195086],"mapped",[19693]],[[195087,195087],"mapped",[40295]],[[195088,195088],"mapped",[172238]],[[195089,195089],"mapped",[19704]],[[195090,195090],"mapped",[172293]],[[195091,195091],"mapped",[172558]],[[195092,195092],"mapped",[172689]],[[195093,195093],"mapped",[40635]],[[195094,195094],"mapped",[19798]],[[195095,195095],"mapped",[40697]],[[195096,195096],"mapped",[40702]],[[195097,195097],"mapped",[40709]],[[195098,195098],"mapped",[40719]],[[195099,195099],"mapped",[40726]],[[195100,195100],"mapped",[40763]],[[195101,195101],"mapped",[173568]],[[195102,196605],"disallowed"],[[196606,196607],"disallowed"],[[196608,262141],"disallowed"],[[262142,262143],"disallowed"],[[262144,327677],"disallowed"],[[327678,327679],"disallowed"],[[327680,393213],"disallowed"],[[393214,393215],"disallowed"],[[393216,458749],"disallowed"],[[458750,458751],"disallowed"],[[458752,524285],"disallowed"],[[524286,524287],"disallowed"],[[524288,589821],"disallowed"],[[589822,589823],"disallowed"],[[589824,655357],"disallowed"],[[655358,655359],"disallowed"],[[655360,720893],"disallowed"],[[720894,720895],"disallowed"],[[720896,786429],"disallowed"],[[786430,786431],"disallowed"],[[786432,851965],"disallowed"],[[851966,851967],"disallowed"],[[851968,917501],"disallowed"],[[917502,917503],"disallowed"],[[917504,917504],"disallowed"],[[917505,917505],"disallowed"],[[917506,917535],"disallowed"],[[917536,917631],"disallowed"],[[917632,917759],"disallowed"],[[917760,917999],"ignored"],[[918e3,983037],"disallowed"],[[983038,983039],"disallowed"],[[983040,1048573],"disallowed"],[[1048574,1048575],"disallowed"],[[1048576,1114109],"disallowed"],[[1114110,1114111],"disallowed"]]});var Vr=I((Gh,Nt)=>{"use strict";var xr=require("punycode"),Tr=Nr(),vp={TRANSITIONAL:0,NONTRANSITIONAL:1};function Dr(p){return p.split("\0").map(function(t){return t.normalize("NFC")}).join("\0")}function Ar(p){for(var t=0,i=Tr.length-1;t<=i;){var r=Math.floor((t+i)/2),n=Tr[r];if(n[0][0]<=p&&n[0][1]>=p)return n;n[0][0]>p?i=r-1:t=r+1}return null}var mu=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g;function Er(p){return p.replace(mu,"_").length}function ou(p,t,i){for(var r=!1,n="",u=Er(p),v=0;v<u;++v){var _=p.codePointAt(v),f=Ar(_);switch(f[1]){case"disallowed":r=!0,n+=String.fromCodePoint(_);break;case"ignored":break;case"mapped":n+=String.fromCodePoint.apply(String,f[2]);break;case"deviation":i===vp.TRANSITIONAL?n+=String.fromCodePoint.apply(String,f[2]):n+=String.fromCodePoint(_);break;case"valid":n+=String.fromCodePoint(_);break;case"disallowed_STD3_mapped":t?(r=!0,n+=String.fromCodePoint(_)):n+=String.fromCodePoint.apply(String,f[2]);break;case"disallowed_STD3_valid":t&&(r=!0),n+=String.fromCodePoint(_);break}}return{string:n,error:r}}var uu=/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08E4-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C03\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D01-\u0D03\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u18A9\u1920-\u192B\u1930-\u193B\u19B0-\u19C0\u19C8\u19C9\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF8\u1CF9\u1DC0-\u1DF5\u1DFC-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C4\uA8E0-\uA8F1\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2D]|\uD800[\uDDFD\uDEE0\uDF76-\uDF7A]|\uD802[\uDE01-\uDE03\uDE05\uDE06\uDE0C-\uDE0F\uDE38-\uDE3A\uDE3F\uDEE5\uDEE6]|\uD804[\uDC00-\uDC02\uDC38-\uDC46\uDC7F-\uDC82\uDCB0-\uDCBA\uDD00-\uDD02\uDD27-\uDD34\uDD73\uDD80-\uDD82\uDDB3-\uDDC0\uDE2C-\uDE37\uDEDF-\uDEEA\uDF01-\uDF03\uDF3C\uDF3E-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF57\uDF62\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDCB0-\uDCC3\uDDAF-\uDDB5\uDDB8-\uDDC0\uDE30-\uDE40\uDEAB-\uDEB7]|\uD81A[\uDEF0-\uDEF4\uDF30-\uDF36]|\uD81B[\uDF51-\uDF7E\uDF8F-\uDF92]|\uD82F[\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD83A[\uDCD0-\uDCD6]|\uDB40[\uDD00-\uDDEF]/;function cu(p,t){p.substr(0,4)==="xn--"&&(p=xr.toUnicode(p),t=vp.NONTRANSITIONAL);var i=!1;(Dr(p)!==p||p[3]==="-"&&p[4]==="-"||p[0]==="-"||p[p.length-1]==="-"||p.indexOf(".")!==-1||p.search(uu)===0)&&(i=!0);for(var r=Er(p),n=0;n<r;++n){var u=Ar(p.codePointAt(n));if(St===vp.TRANSITIONAL&&u[1]!=="valid"||St===vp.NONTRANSITIONAL&&u[1]!=="valid"&&u[1]!=="deviation"){i=!0;break}}return{label:p,error:i}}function St(p,t,i){var r=ou(p,t,i);r.string=Dr(r.string);for(var n=r.string.split("."),u=0;u<n.length;++u)try{var v=cu(n[u]);n[u]=v.label,r.error=r.error||v.error}catch{r.error=!0}return{string:n.join("."),error:r.error}}Nt.exports.toASCII=function(p,t,i,r){var n=St(p,t,i),u=n.string.split(".");if(u=u.map(function(f){try{return xr.toASCII(f)}catch{return n.error=!0,f}}),r){var v=u.slice(0,u.length-1).join(".").length;(v.length>253||v.length===0)&&(n.error=!0);for(var _=0;_<u.length;++_)if(u.length>63||u.length===0){n.error=!0;break}}return n.error?null:u.join(".")};Nt.exports.toUnicode=function(p,t){var i=St(p,t,vp.NONTRANSITIONAL);return{domain:i.string,error:i.error}};Nt.exports.PROCESSING_OPTIONS=vp});var Fe=I((Kh,re)=>{"use strict";var _p=require("punycode"),Cr=Vr(),Or={ftp:21,file:null,gopher:70,http:80,https:443,ws:80,wss:443},Q=Symbol("failure");function Pr(p){return _p.ucs2.decode(p).length}function kr(p,t){let i=p[t];return isNaN(i)?void 0:String.fromCodePoint(i)}function s2(p){return p>=48&&p<=57}function l2(p){return p>=65&&p<=90||p>=97&&p<=122}function fu(p){return l2(p)||s2(p)}function Ae(p){return s2(p)||p>=65&&p<=70||p>=97&&p<=102}function Rr(p){return p==="."||p.toLowerCase()==="%2e"}function hu(p){return p=p.toLowerCase(),p===".."||p==="%2e."||p===".%2e"||p==="%2e%2e"}function vu(p,t){return l2(p)&&(t===58||t===124)}function Lr(p){return p.length===2&&l2(p.codePointAt(0))&&(p[1]===":"||p[1]==="|")}function _u(p){return p.length===2&&l2(p.codePointAt(0))&&p[1]===":"}function wu(p){return p.search(/\u0000|\u0009|\u000A|\u000D|\u0020|#|%|\/|:|\?|@|\[|\\|\]/)!==-1}function gu(p){return p.search(/\u0000|\u0009|\u000A|\u000D|\u0020|#|\/|:|\?|@|\[|\\|\]/)!==-1}function A0(p){return Or[p]!==void 0}function A1(p){return A0(p.scheme)}function bu(p){return Or[p]}function Ir(p){let t=p.toString(16).toUpperCase();return t.length===1&&(t="0"+t),"%"+t}function yu(p){let t=new Buffer(p),i="";for(let r=0;r<t.length;++r)i+=Ir(t[r]);return i}function Su(p){let t=new Buffer(p),i=[];for(let r=0;r<t.length;++r)t[r]!==37?i.push(t[r]):t[r]===37&&Ae(t[r+1])&&Ae(t[r+2])?(i.push(parseInt(t.slice(r+1,r+3).toString(),16)),r+=2):i.push(t[r]);return new Buffer(i).toString()}function Tt(p){return p<=31||p>126}var Nu=new Set([32,34,35,60,62,63,96,123,125]);function Fr(p){return Tt(p)||Nu.has(p)}var Tu=new Set([47,58,59,61,64,91,92,93,94,124]);function V0(p){return Fr(p)||Tu.has(p)}function Ba(p,t){let i=String.fromCodePoint(p);return t(p)?yu(i):i}function xu(p){let t=10;return p.length>=2&&p.charAt(0)==="0"&&p.charAt(1).toLowerCase()==="x"?(p=p.substring(2),t=16):p.length>=2&&p.charAt(0)==="0"&&(p=p.substring(1),t=8),p===""?0:(t===10?/[^0-9]/:t===16?/[^0-9A-Fa-f]/:/[^0-7]/).test(p)?Q:parseInt(p,t)}function Du(p){let t=p.split(".");if(t[t.length-1]===""&&t.length>1&&t.pop(),t.length>4)return p;let i=[];for(let u of t){if(u==="")return p;let v=xu(u);if(v===Q)return p;i.push(v)}for(let u=0;u<i.length-1;++u)if(i[u]>255)return Q;if(i[i.length-1]>=Math.pow(256,5-i.length))return Q;let r=i.pop(),n=0;for(let u of i)r+=u*Math.pow(256,3-n),++n;return r}function Au(p){let t="",i=p;for(let r=1;r<=4;++r)t=String(i%256)+t,r!==4&&(t="."+t),i=Math.floor(i/256);return t}function Eu(p){let t=[0,0,0,0,0,0,0,0],i=0,r=null,n=0;if(p=_p.ucs2.decode(p),p[n]===58){if(p[n+1]!==58)return Q;n+=2,++i,r=i}for(;n<p.length;){if(i===8)return Q;if(p[n]===58){if(r!==null)return Q;++n,++i,r=i;continue}let u=0,v=0;for(;v<4&&Ae(p[n]);)u=u*16+parseInt(kr(p,n),16),++n,++v;if(p[n]===46){if(v===0||(n-=v,i>6))return Q;let _=0;for(;p[n]!==void 0;){let f=null;if(_>0)if(p[n]===46&&_<4)++n;else return Q;if(!s2(p[n]))return Q;for(;s2(p[n]);){let N=parseInt(kr(p,n));if(f===null)f=N;else{if(f===0)return Q;f=f*10+N}if(f>255)return Q;++n}t[i]=t[i]*256+f,++_,(_===2||_===4)&&++i}if(_!==4)return Q;break}else if(p[n]===58){if(++n,p[n]===void 0)return Q}else if(p[n]!==void 0)return Q;t[i]=u,++i}if(r!==null){let u=i-r;for(i=7;i!==0&&u>0;){let v=t[r+u-1];t[r+u-1]=t[i],t[i]=v,--i,--u}}else if(r===null&&i!==8)return Q;return t}function Vu(p){let t="",r=Pu(p).idx,n=!1;for(let u=0;u<=7;++u)if(!(n&&p[u]===0)){if(n&&(n=!1),r===u){t+=u===0?"::":":",n=!0;continue}t+=p[u].toString(16),u!==7&&(t+=":")}return t}function E0(p,t){if(p[0]==="[")return p[p.length-1]!=="]"?Q:Eu(p.substring(1,p.length-1));if(!t)return Cu(p);let i=Su(p),r=Cr.toASCII(i,!1,Cr.PROCESSING_OPTIONS.NONTRANSITIONAL,!1);if(r===null||wu(r))return Q;let n=Du(r);return typeof n=="number"||n===Q?n:r}function Cu(p){if(gu(p))return Q;let t="",i=_p.ucs2.decode(p);for(let r=0;r<i.length;++r)t+=Ba(i[r],Tt);return t}function Pu(p){let t=null,i=1,r=null,n=0;for(let u=0;u<p.length;++u)p[u]!==0?(n>i&&(t=r,i=n),r=null,n=0):(r===null&&(r=u),++n);return n>i&&(t=r,i=n),{idx:t,len:i}}function C0(p){return typeof p=="number"?Au(p):p instanceof Array?"["+Vu(p)+"]":p}function ku(p){return p.replace(/^[\u0000-\u001F\u0020]+|[\u0000-\u001F\u0020]+$/g,"")}function Ru(p){return p.replace(/\u0009|\u000A|\u000D/g,"")}function Br(p){let t=p.path;t.length!==0&&(p.scheme==="file"&&t.length===1&&Lu(t[0])||t.pop())}function Ur(p){return p.username!==""||p.password!==""}function Ou(p){return p.host===null||p.host===""||p.cannotBeABaseURL||p.scheme==="file"}function Lu(p){return/^[A-Za-z]:$/.test(p)}function v1(p,t,i,r,n){if(this.pointer=0,this.input=p,this.base=t||null,this.encodingOverride=i||"utf-8",this.stateOverride=n,this.url=r,this.failure=!1,this.parseError=!1,!this.url){this.url={scheme:"",username:"",password:"",host:null,port:null,path:[],query:null,fragment:null,cannotBeABaseURL:!1};let v=ku(this.input);v!==this.input&&(this.parseError=!0),this.input=v}let u=Ru(this.input);for(u!==this.input&&(this.parseError=!0),this.input=u,this.state=n||"scheme start",this.buffer="",this.atFlag=!1,this.arrFlag=!1,this.passwordTokenSeenFlag=!1,this.input=_p.ucs2.decode(this.input);this.pointer<=this.input.length;++this.pointer){let v=this.input[this.pointer],_=isNaN(v)?void 0:String.fromCodePoint(v),f=this["parse "+this.state](v,_);if(f){if(f===Q){this.failure=!0;break}}else break}}v1.prototype["parse scheme start"]=function(t,i){if(l2(t))this.buffer+=i.toLowerCase(),this.state="scheme";else if(!this.stateOverride)this.state="no scheme",--this.pointer;else return this.parseError=!0,Q;return!0};v1.prototype["parse scheme"]=function(t,i){if(fu(t)||t===43||t===45||t===46)this.buffer+=i.toLowerCase();else if(t===58){if(this.stateOverride&&(A1(this.url)&&!A0(this.buffer)||!A1(this.url)&&A0(this.buffer)||(Ur(this.url)||this.url.port!==null)&&this.buffer==="file"||this.url.scheme==="file"&&(this.url.host===""||this.url.host===null))||(this.url.scheme=this.buffer,this.buffer="",this.stateOverride))return!1;this.url.scheme==="file"?((this.input[this.pointer+1]!==47||this.input[this.pointer+2]!==47)&&(this.parseError=!0),this.state="file"):A1(this.url)&&this.base!==null&&this.base.scheme===this.url.scheme?this.state="special relative or authority":A1(this.url)?this.state="special authority slashes":this.input[this.pointer+1]===47?(this.state="path or authority",++this.pointer):(this.url.cannotBeABaseURL=!0,this.url.path.push(""),this.state="cannot-be-a-base-URL path")}else if(!this.stateOverride)this.buffer="",this.state="no scheme",this.pointer=-1;else return this.parseError=!0,Q;return!0};v1.prototype["parse no scheme"]=function(t){return this.base===null||this.base.cannotBeABaseURL&&t!==35?Q:(this.base.cannotBeABaseURL&&t===35?(this.url.scheme=this.base.scheme,this.url.path=this.base.path.slice(),this.url.query=this.base.query,this.url.fragment="",this.url.cannotBeABaseURL=!0,this.state="fragment"):this.base.scheme==="file"?(this.state="file",--this.pointer):(this.state="relative",--this.pointer),!0)};v1.prototype["parse special relative or authority"]=function(t){return t===47&&this.input[this.pointer+1]===47?(this.state="special authority ignore slashes",++this.pointer):(this.parseError=!0,this.state="relative",--this.pointer),!0};v1.prototype["parse path or authority"]=function(t){return t===47?this.state="authority":(this.state="path",--this.pointer),!0};v1.prototype["parse relative"]=function(t){return this.url.scheme=this.base.scheme,isNaN(t)?(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(),this.url.query=this.base.query):t===47?this.state="relative slash":t===63?(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(),this.url.query="",this.state="query"):t===35?(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(),this.url.query=this.base.query,this.url.fragment="",this.state="fragment"):A1(this.url)&&t===92?(this.parseError=!0,this.state="relative slash"):(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(0,this.base.path.length-1),this.state="path",--this.pointer),!0};v1.prototype["parse relative slash"]=function(t){return A1(this.url)&&(t===47||t===92)?(t===92&&(this.parseError=!0),this.state="special authority ignore slashes"):t===47?this.state="authority":(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.state="path",--this.pointer),!0};v1.prototype["parse special authority slashes"]=function(t){return t===47&&this.input[this.pointer+1]===47?(this.state="special authority ignore slashes",++this.pointer):(this.parseError=!0,this.state="special authority ignore slashes",--this.pointer),!0};v1.prototype["parse special authority ignore slashes"]=function(t){return t!==47&&t!==92?(this.state="authority",--this.pointer):this.parseError=!0,!0};v1.prototype["parse authority"]=function(t,i){if(t===64){this.parseError=!0,this.atFlag&&(this.buffer="%40"+this.buffer),this.atFlag=!0;let r=Pr(this.buffer);for(let n=0;n<r;++n){let u=this.buffer.codePointAt(n);if(u===58&&!this.passwordTokenSeenFlag){this.passwordTokenSeenFlag=!0;continue}let v=Ba(u,V0);this.passwordTokenSeenFlag?this.url.password+=v:this.url.username+=v}this.buffer=""}else if(isNaN(t)||t===47||t===63||t===35||A1(this.url)&&t===92){if(this.atFlag&&this.buffer==="")return this.parseError=!0,Q;this.pointer-=Pr(this.buffer)+1,this.buffer="",this.state="host"}else this.buffer+=i;return!0};v1.prototype["parse hostname"]=v1.prototype["parse host"]=function(t,i){if(this.stateOverride&&this.url.scheme==="file")--this.pointer,this.state="file host";else if(t===58&&!this.arrFlag){if(this.buffer==="")return this.parseError=!0,Q;let r=E0(this.buffer,A1(this.url));if(r===Q)return Q;if(this.url.host=r,this.buffer="",this.state="port",this.stateOverride==="hostname")return!1}else if(isNaN(t)||t===47||t===63||t===35||A1(this.url)&&t===92){if(--this.pointer,A1(this.url)&&this.buffer==="")return this.parseError=!0,Q;if(this.stateOverride&&this.buffer===""&&(Ur(this.url)||this.url.port!==null))return this.parseError=!0,!1;let r=E0(this.buffer,A1(this.url));if(r===Q)return Q;if(this.url.host=r,this.buffer="",this.state="path start",this.stateOverride)return!1}else t===91?this.arrFlag=!0:t===93&&(this.arrFlag=!1),this.buffer+=i;return!0};v1.prototype["parse port"]=function(t,i){if(s2(t))this.buffer+=i;else if(isNaN(t)||t===47||t===63||t===35||A1(this.url)&&t===92||this.stateOverride){if(this.buffer!==""){let r=parseInt(this.buffer);if(r>Math.pow(2,16)-1)return this.parseError=!0,Q;this.url.port=r===bu(this.url.scheme)?null:r,this.buffer=""}if(this.stateOverride)return!1;this.state="path start",--this.pointer}else return this.parseError=!0,Q;return!0};var Iu=new Set([47,92,63,35]);v1.prototype["parse file"]=function(t){return this.url.scheme="file",t===47||t===92?(t===92&&(this.parseError=!0),this.state="file slash"):this.base!==null&&this.base.scheme==="file"?isNaN(t)?(this.url.host=this.base.host,this.url.path=this.base.path.slice(),this.url.query=this.base.query):t===63?(this.url.host=this.base.host,this.url.path=this.base.path.slice(),this.url.query="",this.state="query"):t===35?(this.url.host=this.base.host,this.url.path=this.base.path.slice(),this.url.query=this.base.query,this.url.fragment="",this.state="fragment"):(this.input.length-this.pointer-1===0||!vu(t,this.input[this.pointer+1])||this.input.length-this.pointer-1>=2&&!Iu.has(this.input[this.pointer+2])?(this.url.host=this.base.host,this.url.path=this.base.path.slice(),Br(this.url)):this.parseError=!0,this.state="path",--this.pointer):(this.state="path",--this.pointer),!0};v1.prototype["parse file slash"]=function(t){return t===47||t===92?(t===92&&(this.parseError=!0),this.state="file host"):(this.base!==null&&this.base.scheme==="file"&&(_u(this.base.path[0])?this.url.path.push(this.base.path[0]):this.url.host=this.base.host),this.state="path",--this.pointer),!0};v1.prototype["parse file host"]=function(t,i){if(isNaN(t)||t===47||t===92||t===63||t===35)if(--this.pointer,!this.stateOverride&&Lr(this.buffer))this.parseError=!0,this.state="path";else if(this.buffer===""){if(this.url.host="",this.stateOverride)return!1;this.state="path start"}else{let r=E0(this.buffer,A1(this.url));if(r===Q)return Q;if(r==="localhost"&&(r=""),this.url.host=r,this.stateOverride)return!1;this.buffer="",this.state="path start"}else this.buffer+=i;return!0};v1.prototype["parse path start"]=function(t){return A1(this.url)?(t===92&&(this.parseError=!0),this.state="path",t!==47&&t!==92&&--this.pointer):!this.stateOverride&&t===63?(this.url.query="",this.state="query"):!this.stateOverride&&t===35?(this.url.fragment="",this.state="fragment"):t!==void 0&&(this.state="path",t!==47&&--this.pointer),!0};v1.prototype["parse path"]=function(t){if(isNaN(t)||t===47||A1(this.url)&&t===92||!this.stateOverride&&(t===63||t===35)){if(A1(this.url)&&t===92&&(this.parseError=!0),hu(this.buffer)?(Br(this.url),t!==47&&!(A1(this.url)&&t===92)&&this.url.path.push("")):Rr(this.buffer)&&t!==47&&!(A1(this.url)&&t===92)?this.url.path.push(""):Rr(this.buffer)||(this.url.scheme==="file"&&this.url.path.length===0&&Lr(this.buffer)&&(this.url.host!==""&&this.url.host!==null&&(this.parseError=!0,this.url.host=""),this.buffer=this.buffer[0]+":"),this.url.path.push(this.buffer)),this.buffer="",this.url.scheme==="file"&&(t===void 0||t===63||t===35))for(;this.url.path.length>1&&this.url.path[0]==="";)this.parseError=!0,this.url.path.shift();t===63&&(this.url.query="",this.state="query"),t===35&&(this.url.fragment="",this.state="fragment")}else t===37&&(!Ae(this.input[this.pointer+1])||!Ae(this.input[this.pointer+2]))&&(this.parseError=!0),this.buffer+=Ba(t,Fr);return!0};v1.prototype["parse cannot-be-a-base-URL path"]=function(t){return t===63?(this.url.query="",this.state="query"):t===35?(this.url.fragment="",this.state="fragment"):(!isNaN(t)&&t!==37&&(this.parseError=!0),t===37&&(!Ae(this.input[this.pointer+1])||!Ae(this.input[this.pointer+2]))&&(this.parseError=!0),isNaN(t)||(this.url.path[0]=this.url.path[0]+Ba(t,Tt))),!0};v1.prototype["parse query"]=function(t,i){if(isNaN(t)||!this.stateOverride&&t===35){(!A1(this.url)||this.url.scheme==="ws"||this.url.scheme==="wss")&&(this.encodingOverride="utf-8");let r=new Buffer(this.buffer);for(let n=0;n<r.length;++n)r[n]<33||r[n]>126||r[n]===34||r[n]===35||r[n]===60||r[n]===62?this.url.query+=Ir(r[n]):this.url.query+=String.fromCodePoint(r[n]);this.buffer="",t===35&&(this.url.fragment="",this.state="fragment")}else t===37&&(!Ae(this.input[this.pointer+1])||!Ae(this.input[this.pointer+2]))&&(this.parseError=!0),this.buffer+=i;return!0};v1.prototype["parse fragment"]=function(t){return isNaN(t)||(t===0?this.parseError=!0:(t===37&&(!Ae(this.input[this.pointer+1])||!Ae(this.input[this.pointer+2]))&&(this.parseError=!0),this.url.fragment+=Ba(t,Tt))),!0};function Fu(p,t){let i=p.scheme+":";if(p.host!==null?(i+="//",(p.username!==""||p.password!=="")&&(i+=p.username,p.password!==""&&(i+=":"+p.password),i+="@"),i+=C0(p.host),p.port!==null&&(i+=":"+p.port)):p.host===null&&p.scheme==="file"&&(i+="//"),p.cannotBeABaseURL)i+=p.path[0];else for(let r of p.path)i+="/"+r;return p.query!==null&&(i+="?"+p.query),!t&&p.fragment!==null&&(i+="#"+p.fragment),i}function Bu(p){let t=p.scheme+"://";return t+=C0(p.host),p.port!==null&&(t+=":"+p.port),t}re.exports.serializeURL=Fu;re.exports.serializeURLOrigin=function(p){switch(p.scheme){case"blob":try{return re.exports.serializeURLOrigin(re.exports.parseURL(p.path[0]))}catch{return"null"}case"ftp":case"gopher":case"http":case"https":case"ws":case"wss":return Bu({scheme:p.scheme,host:p.host,port:p.port});case"file":return"file://";default:return"null"}};re.exports.basicURLParse=function(p,t){t===void 0&&(t={});let i=new v1(p,t.baseURL,t.encodingOverride,t.url,t.stateOverride);return i.failure?"failure":i.url};re.exports.setTheUsername=function(p,t){p.username="";let i=_p.ucs2.decode(t);for(let r=0;r<i.length;++r)p.username+=Ba(i[r],V0)};re.exports.setThePassword=function(p,t){p.password="";let i=_p.ucs2.decode(t);for(let r=0;r<i.length;++r)p.password+=Ba(i[r],V0)};re.exports.serializeHost=C0;re.exports.cannotHaveAUsernamePasswordPort=Ou;re.exports.serializeInteger=function(p){return String(p)};re.exports.parseURL=function(p,t){return t===void 0&&(t={}),re.exports.basicURLParse(p,{baseURL:t.baseURL,encodingOverride:t.encodingOverride})}});var qr=I(Mr=>{"use strict";var _1=Fe();Mr.implementation=class{constructor(t){let i=t[0],r=t[1],n=null;if(r!==void 0&&(n=_1.basicURLParse(r),n==="failure"))throw new TypeError("Invalid base URL");let u=_1.basicURLParse(i,{baseURL:n});if(u==="failure")throw new TypeError("Invalid URL");this._url=u}get href(){return _1.serializeURL(this._url)}set href(t){let i=_1.basicURLParse(t);if(i==="failure")throw new TypeError("Invalid URL");this._url=i}get origin(){return _1.serializeURLOrigin(this._url)}get protocol(){return this._url.scheme+":"}set protocol(t){_1.basicURLParse(t+":",{url:this._url,stateOverride:"scheme start"})}get username(){return this._url.username}set username(t){_1.cannotHaveAUsernamePasswordPort(this._url)||_1.setTheUsername(this._url,t)}get password(){return this._url.password}set password(t){_1.cannotHaveAUsernamePasswordPort(this._url)||_1.setThePassword(this._url,t)}get host(){let t=this._url;return t.host===null?"":t.port===null?_1.serializeHost(t.host):_1.serializeHost(t.host)+":"+_1.serializeInteger(t.port)}set host(t){this._url.cannotBeABaseURL||_1.basicURLParse(t,{url:this._url,stateOverride:"host"})}get hostname(){return this._url.host===null?"":_1.serializeHost(this._url.host)}set hostname(t){this._url.cannotBeABaseURL||_1.basicURLParse(t,{url:this._url,stateOverride:"hostname"})}get port(){return this._url.port===null?"":_1.serializeInteger(this._url.port)}set port(t){_1.cannotHaveAUsernamePasswordPort(this._url)||(t===""?this._url.port=null:_1.basicURLParse(t,{url:this._url,stateOverride:"port"}))}get pathname(){return this._url.cannotBeABaseURL?this._url.path[0]:this._url.path.length===0?"":"/"+this._url.path.join("/")}set pathname(t){this._url.cannotBeABaseURL||(this._url.path=[],_1.basicURLParse(t,{url:this._url,stateOverride:"path start"}))}get search(){return this._url.query===null||this._url.query===""?"":"?"+this._url.query}set search(t){let i=this._url;if(t===""){i.query=null;return}let r=t[0]==="?"?t.substring(1):t;i.query="",_1.basicURLParse(r,{url:i,stateOverride:"query"})}get hash(){return this._url.fragment===null||this._url.fragment===""?"":"#"+this._url.fragment}set hash(t){if(t===""){this._url.fragment=null;return}let i=t[0]==="#"?t.substring(1):t;this._url.fragment="",_1.basicURLParse(i,{url:this._url,stateOverride:"fragment"})}toJSON(){return this.href}}});var Wr=I((Yh,m2)=>{"use strict";var ve=yr(),$r=Sr(),zr=qr(),n1=$r.implSymbol;function C1(p){if(!this||this[n1]||!(this instanceof C1))throw new TypeError("Failed to construct 'URL': Please use the 'new' operator, this DOM object constructor cannot be called as a function.");if(arguments.length<1)throw new TypeError("Failed to construct 'URL': 1 argument required, but only "+arguments.length+" present.");let t=[];for(let i=0;i<arguments.length&&i<2;++i)t[i]=arguments[i];t[0]=ve.USVString(t[0]),t[1]!==void 0&&(t[1]=ve.USVString(t[1])),m2.exports.setup(this,t)}C1.prototype.toJSON=function(){if(!this||!m2.exports.is(this))throw new TypeError("Illegal invocation");let t=[];for(let i=0;i<arguments.length&&i<0;++i)t[i]=arguments[i];return this[n1].toJSON.apply(this[n1],t)};Object.defineProperty(C1.prototype,"href",{get(){return this[n1].href},set(p){p=ve.USVString(p),this[n1].href=p},enumerable:!0,configurable:!0});C1.prototype.toString=function(){if(!this||!m2.exports.is(this))throw new TypeError("Illegal invocation");return this.href};Object.defineProperty(C1.prototype,"origin",{get(){return this[n1].origin},enumerable:!0,configurable:!0});Object.defineProperty(C1.prototype,"protocol",{get(){return this[n1].protocol},set(p){p=ve.USVString(p),this[n1].protocol=p},enumerable:!0,configurable:!0});Object.defineProperty(C1.prototype,"username",{get(){return this[n1].username},set(p){p=ve.USVString(p),this[n1].username=p},enumerable:!0,configurable:!0});Object.defineProperty(C1.prototype,"password",{get(){return this[n1].password},set(p){p=ve.USVString(p),this[n1].password=p},enumerable:!0,configurable:!0});Object.defineProperty(C1.prototype,"host",{get(){return this[n1].host},set(p){p=ve.USVString(p),this[n1].host=p},enumerable:!0,configurable:!0});Object.defineProperty(C1.prototype,"hostname",{get(){return this[n1].hostname},set(p){p=ve.USVString(p),this[n1].hostname=p},enumerable:!0,configurable:!0});Object.defineProperty(C1.prototype,"port",{get(){return this[n1].port},set(p){p=ve.USVString(p),this[n1].port=p},enumerable:!0,configurable:!0});Object.defineProperty(C1.prototype,"pathname",{get(){return this[n1].pathname},set(p){p=ve.USVString(p),this[n1].pathname=p},enumerable:!0,configurable:!0});Object.defineProperty(C1.prototype,"search",{get(){return this[n1].search},set(p){p=ve.USVString(p),this[n1].search=p},enumerable:!0,configurable:!0});Object.defineProperty(C1.prototype,"hash",{get(){return this[n1].hash},set(p){p=ve.USVString(p),this[n1].hash=p},enumerable:!0,configurable:!0});m2.exports={is(p){return!!p&&p[n1]instanceof zr.implementation},create(p,t){let i=Object.create(C1.prototype);return this.setup(i,p,t),i},setup(p,t,i){i||(i={}),i.wrapper=p,p[n1]=new zr.implementation(t,i),p[n1][$r.wrapperSymbol]=p},interface:C1,expose:{Window:{URL:C1},Worker:{URL:C1}}}});var jr=I(Be=>{"use strict";Be.URL=Wr().interface;Be.serializeURL=Fe().serializeURL;Be.serializeURLOrigin=Fe().serializeURLOrigin;Be.basicURLParse=Fe().basicURLParse;Be.setTheUsername=Fe().setTheUsername;Be.setThePassword=Fe().setThePassword;Be.serializeHost=Fe().serializeHost;Be.serializeInteger=Fe().serializeInteger;Be.parseURL=Fe().parseURL});var t9=I((Ve,p9)=>{"use strict";Object.defineProperty(Ve,"__esModule",{value:!0});function yp(p){return p&&typeof p=="object"&&"default"in p?p.default:p}var Ee=yp(require("stream")),Zr=yp(require("http")),Dt=yp(require("url")),Jr=yp(jr()),Uu=yp(require("https")),Ua=yp(require("zlib")),Mu=Ee.Readable,pa=Symbol("buffer"),P0=Symbol("type"),u2=class p{constructor(){this[P0]="";let t=arguments[0],i=arguments[1],r=[],n=0;if(t){let v=t,_=Number(v.length);for(let f=0;f<_;f++){let N=v[f],A;N instanceof Buffer?A=N:ArrayBuffer.isView(N)?A=Buffer.from(N.buffer,N.byteOffset,N.byteLength):N instanceof ArrayBuffer?A=Buffer.from(N):N instanceof p?A=N[pa]:A=Buffer.from(typeof N=="string"?N:String(N)),n+=A.length,r.push(A)}}this[pa]=Buffer.concat(r);let u=i&&i.type!==void 0&&String(i.type).toLowerCase();u&&!/[^\u0020-\u007E]/.test(u)&&(this[P0]=u)}get size(){return this[pa].length}get type(){return this[P0]}text(){return Promise.resolve(this[pa].toString())}arrayBuffer(){let t=this[pa],i=t.buffer.slice(t.byteOffset,t.byteOffset+t.byteLength);return Promise.resolve(i)}stream(){let t=new Mu;return t._read=function(){},t.push(this[pa]),t.push(null),t}toString(){return"[object Blob]"}slice(){let t=this.size,i=arguments[0],r=arguments[1],n,u;i===void 0?n=0:i<0?n=Math.max(t+i,0):n=Math.min(i,t),r===void 0?u=t:r<0?u=Math.max(t+r,0):u=Math.min(r,t);let v=Math.max(u-n,0),f=this[pa].slice(n,n+v),N=new p([],{type:arguments[2]});return N[pa]=f,N}};Object.defineProperties(u2.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}});Object.defineProperty(u2.prototype,Symbol.toStringTag,{value:"Blob",writable:!1,enumerable:!1,configurable:!0});function L1(p,t,i){Error.call(this,p),this.message=p,this.type=t,i&&(this.code=this.errno=i.code),Error.captureStackTrace(this,this.constructor)}L1.prototype=Object.create(Error.prototype);L1.prototype.constructor=L1;L1.prototype.name="FetchError";var L0;try{L0=require("encoding").convert}catch{}var da=Symbol("Body internals"),Hr=Ee.PassThrough;function P1(p){var t=this,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=i.size;let n=r===void 0?0:r;var u=i.timeout;let v=u===void 0?0:u;p==null?p=null:Yr(p)?p=Buffer.from(p.toString()):f2(p)||Buffer.isBuffer(p)||(Object.prototype.toString.call(p)==="[object ArrayBuffer]"?p=Buffer.from(p):ArrayBuffer.isView(p)?p=Buffer.from(p.buffer,p.byteOffset,p.byteLength):p instanceof Ee||(p=Buffer.from(String(p)))),this[da]={body:p,disturbed:!1,error:null},this.size=n,this.timeout=v,p instanceof Ee&&p.on("error",function(_){let f=_.name==="AbortError"?_:new L1(`Invalid response body while trying to fetch ${t.url}: ${_.message}`,"system",_);t[da].error=f})}P1.prototype={get body(){return this[da].body},get bodyUsed(){return this[da].disturbed},arrayBuffer(){return wp.call(this).then(function(p){return p.buffer.slice(p.byteOffset,p.byteOffset+p.byteLength)})},blob(){let p=this.headers&&this.headers.get("content-type")||"";return wp.call(this).then(function(t){return Object.assign(new u2([],{type:p.toLowerCase()}),{[pa]:t})})},json(){var p=this;return wp.call(this).then(function(t){try{return JSON.parse(t.toString())}catch(i){return P1.Promise.reject(new L1(`invalid json response body at ${p.url} reason: ${i.message}`,"invalid-json"))}})},text(){return wp.call(this).then(function(p){return p.toString()})},buffer(){return wp.call(this)},textConverted(){var p=this;return wp.call(this).then(function(t){return qu(t,p.headers)})}};Object.defineProperties(P1.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0}});P1.mixIn=function(p){for(let t of Object.getOwnPropertyNames(P1.prototype))if(!(t in p)){let i=Object.getOwnPropertyDescriptor(P1.prototype,t);Object.defineProperty(p,t,i)}};function wp(){var p=this;if(this[da].disturbed)return P1.Promise.reject(new TypeError(`body used already for: ${this.url}`));if(this[da].disturbed=!0,this[da].error)return P1.Promise.reject(this[da].error);let t=this.body;if(t===null)return P1.Promise.resolve(Buffer.alloc(0));if(f2(t)&&(t=t.stream()),Buffer.isBuffer(t))return P1.Promise.resolve(t);if(!(t instanceof Ee))return P1.Promise.resolve(Buffer.alloc(0));let i=[],r=0,n=!1;return new P1.Promise(function(u,v){let _;p.timeout&&(_=setTimeout(function(){n=!0,v(new L1(`Response timeout while trying to fetch ${p.url} (over ${p.timeout}ms)`,"body-timeout"))},p.timeout)),t.on("error",function(f){f.name==="AbortError"?(n=!0,v(f)):v(new L1(`Invalid response body while trying to fetch ${p.url}: ${f.message}`,"system",f))}),t.on("data",function(f){if(!(n||f===null)){if(p.size&&r+f.length>p.size){n=!0,v(new L1(`content size at ${p.url} over limit: ${p.size}`,"max-size"));return}r+=f.length,i.push(f)}}),t.on("end",function(){if(!n){clearTimeout(_);try{u(Buffer.concat(i,r))}catch(f){v(new L1(`Could not create Buffer from response body for ${p.url}: ${f.message}`,"system",f))}}})})}function qu(p,t){if(typeof L0!="function")throw new Error("The package `encoding` must be installed to use the textConverted() function");let i=t.get("content-type"),r="utf-8",n,u;return i&&(n=/charset=([^;]*)/i.exec(i)),u=p.slice(0,1024).toString(),!n&&u&&(n=/<meta.+?charset=(['"])(.+?)\1/i.exec(u)),!n&&u&&(n=/<meta[\s]+?http-equiv=(['"])content-type\1[\s]+?content=(['"])(.+?)\2/i.exec(u),n||(n=/<meta[\s]+?content=(['"])(.+?)\1[\s]+?http-equiv=(['"])content-type\3/i.exec(u),n&&n.pop()),n&&(n=/charset=(.*)/i.exec(n.pop()))),!n&&u&&(n=/<\?xml.+?encoding=(['"])(.+?)\1/i.exec(u)),n&&(r=n.pop(),(r==="gb2312"||r==="gbk")&&(r="gb18030")),L0(p,"UTF-8",r).toString()}function Yr(p){return typeof p!="object"||typeof p.append!="function"||typeof p.delete!="function"||typeof p.get!="function"||typeof p.getAll!="function"||typeof p.has!="function"||typeof p.set!="function"?!1:p.constructor.name==="URLSearchParams"||Object.prototype.toString.call(p)==="[object URLSearchParams]"||typeof p.sort=="function"}function f2(p){return typeof p=="object"&&typeof p.arrayBuffer=="function"&&typeof p.type=="string"&&typeof p.stream=="function"&&typeof p.constructor=="function"&&typeof p.constructor.name=="string"&&/^(Blob|File)$/.test(p.constructor.name)&&/^(Blob|File)$/.test(p[Symbol.toStringTag])}function Qr(p){let t,i,r=p.body;if(p.bodyUsed)throw new Error("cannot clone body after it is used");return r instanceof Ee&&typeof r.getBoundary!="function"&&(t=new Hr,i=new Hr,r.pipe(t),r.pipe(i),p[da].body=t,r=i),r}function Xr(p){return p===null?null:typeof p=="string"?"text/plain;charset=UTF-8":Yr(p)?"application/x-www-form-urlencoded;charset=UTF-8":f2(p)?p.type||null:Buffer.isBuffer(p)||Object.prototype.toString.call(p)==="[object ArrayBuffer]"||ArrayBuffer.isView(p)?null:typeof p.getBoundary=="function"?`multipart/form-data;boundary=${p.getBoundary()}`:p instanceof Ee?null:"text/plain;charset=UTF-8"}function e9(p){let t=p.body;return t===null?0:f2(t)?t.size:Buffer.isBuffer(t)?t.length:t&&typeof t.getLengthSync=="function"&&(t._lengthRetrievers&&t._lengthRetrievers.length==0||t.hasKnownLength&&t.hasKnownLength())?t.getLengthSync():null}function zu(p,t){let i=t.body;i===null?p.end():f2(i)?i.stream().pipe(p):Buffer.isBuffer(i)?(p.write(i),p.end()):i.pipe(p)}P1.Promise=global.Promise;var a9=/[^\^_`a-zA-Z\-0-9!#$%&'*+.|~]/,I0=/[^\t\x20-\x7e\x80-\xff]/;function o2(p){if(p=`${p}`,a9.test(p)||p==="")throw new TypeError(`${p} is not a legal HTTP header name`)}function Gr(p){if(p=`${p}`,I0.test(p))throw new TypeError(`${p} is not a legal HTTP header value`)}function gp(p,t){t=t.toLowerCase();for(let i in p)if(i.toLowerCase()===t)return i}var y1=Symbol("map"),we=class p{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:void 0;if(this[y1]=Object.create(null),t instanceof p){let i=t.raw(),r=Object.keys(i);for(let n of r)for(let u of i[n])this.append(n,u);return}if(t!=null)if(typeof t=="object"){let i=t[Symbol.iterator];if(i!=null){if(typeof i!="function")throw new TypeError("Header pairs must be iterable");let r=[];for(let n of t){if(typeof n!="object"||typeof n[Symbol.iterator]!="function")throw new TypeError("Each header pair must be iterable");r.push(Array.from(n))}for(let n of r){if(n.length!==2)throw new TypeError("Each header pair must be a name/value tuple");this.append(n[0],n[1])}}else for(let r of Object.keys(t)){let n=t[r];this.append(r,n)}}else throw new TypeError("Provided initializer must be an object")}get(t){t=`${t}`,o2(t);let i=gp(this[y1],t);return i===void 0?null:this[y1][i].join(", ")}forEach(t){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:void 0,r=F0(this),n=0;for(;n<r.length;){var u=r[n];let v=u[0],_=u[1];t.call(i,_,v,this),r=F0(this),n++}}set(t,i){t=`${t}`,i=`${i}`,o2(t),Gr(i);let r=gp(this[y1],t);this[y1][r!==void 0?r:t]=[i]}append(t,i){t=`${t}`,i=`${i}`,o2(t),Gr(i);let r=gp(this[y1],t);r!==void 0?this[y1][r].push(i):this[y1][t]=[i]}has(t){return t=`${t}`,o2(t),gp(this[y1],t)!==void 0}delete(t){t=`${t}`,o2(t);let i=gp(this[y1],t);i!==void 0&&delete this[y1][i]}raw(){return this[y1]}keys(){return k0(this,"key")}values(){return k0(this,"value")}[Symbol.iterator](){return k0(this,"key+value")}};we.prototype.entries=we.prototype[Symbol.iterator];Object.defineProperty(we.prototype,Symbol.toStringTag,{value:"Headers",writable:!1,enumerable:!1,configurable:!0});Object.defineProperties(we.prototype,{get:{enumerable:!0},forEach:{enumerable:!0},set:{enumerable:!0},append:{enumerable:!0},has:{enumerable:!0},delete:{enumerable:!0},keys:{enumerable:!0},values:{enumerable:!0},entries:{enumerable:!0}});function F0(p){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"key+value";return Object.keys(p[y1]).sort().map(t==="key"?function(r){return r.toLowerCase()}:t==="value"?function(r){return p[y1][r].join(", ")}:function(r){return[r.toLowerCase(),p[y1][r].join(", ")]})}var B0=Symbol("internal");function k0(p,t){let i=Object.create(U0);return i[B0]={target:p,kind:t,index:0},i}var U0=Object.setPrototypeOf({next(){if(!this||Object.getPrototypeOf(this)!==U0)throw new TypeError("Value of `this` is not a HeadersIterator");var p=this[B0];let t=p.target,i=p.kind,r=p.index,n=F0(t,i),u=n.length;return r>=u?{value:void 0,done:!0}:(this[B0].index=r+1,{value:n[r],done:!1})}},Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));Object.defineProperty(U0,Symbol.toStringTag,{value:"HeadersIterator",writable:!1,enumerable:!1,configurable:!0});function $u(p){let t=Object.assign({__proto__:null},p[y1]),i=gp(p[y1],"Host");return i!==void 0&&(t[i]=t[i][0]),t}function Wu(p){let t=new we;for(let i of Object.keys(p))if(!a9.test(i))if(Array.isArray(p[i]))for(let r of p[i])I0.test(r)||(t[y1][i]===void 0?t[y1][i]=[r]:t[y1][i].push(r));else I0.test(p[i])||(t[y1][i]=[p[i]]);return t}var ya=Symbol("Response internals"),ju=Zr.STATUS_CODES,_e=class p{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};P1.call(this,t,i);let r=i.status||200,n=new we(i.headers);if(t!=null&&!n.has("Content-Type")){let u=Xr(t);u&&n.append("Content-Type",u)}this[ya]={url:i.url,status:r,statusText:i.statusText||ju[r],headers:n,counter:i.counter}}get url(){return this[ya].url||""}get status(){return this[ya].status}get ok(){return this[ya].status>=200&&this[ya].status<300}get redirected(){return this[ya].counter>0}get statusText(){return this[ya].statusText}get headers(){return this[ya].headers}clone(){return new p(Qr(this),{url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected})}};P1.mixIn(_e.prototype);Object.defineProperties(_e.prototype,{url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});Object.defineProperty(_e.prototype,Symbol.toStringTag,{value:"Response",writable:!1,enumerable:!1,configurable:!0});var ta=Symbol("Request internals"),Hu=Dt.URL||Jr.URL,Gu=Dt.parse,Ku=Dt.format;function R0(p){return/^[a-zA-Z][a-zA-Z\d+\-.]*:/.exec(p)&&(p=new Hu(p).toString()),Gu(p)}var Zu="destroy"in Ee.Readable.prototype;function xt(p){return typeof p=="object"&&typeof p[ta]=="object"}function Ju(p){let t=p&&typeof p=="object"&&Object.getPrototypeOf(p);return!!(t&&t.constructor.name==="AbortSignal")}var Na=class p{constructor(t){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r;xt(t)?r=R0(t.url):(t&&t.href?r=R0(t.href):r=R0(`${t}`),t={});let n=i.method||t.method||"GET";if(n=n.toUpperCase(),(i.body!=null||xt(t)&&t.body!==null)&&(n==="GET"||n==="HEAD"))throw new TypeError("Request with GET/HEAD method cannot have body");let u=i.body!=null?i.body:xt(t)&&t.body!==null?Qr(t):null;P1.call(this,u,{timeout:i.timeout||t.timeout||0,size:i.size||t.size||0});let v=new we(i.headers||t.headers||{});if(u!=null&&!v.has("Content-Type")){let f=Xr(u);f&&v.append("Content-Type",f)}let _=xt(t)?t.signal:null;if("signal"in i&&(_=i.signal),_!=null&&!Ju(_))throw new TypeError("Expected signal to be an instanceof AbortSignal");this[ta]={method:n,redirect:i.redirect||t.redirect||"follow",headers:v,parsedURL:r,signal:_},this.follow=i.follow!==void 0?i.follow:t.follow!==void 0?t.follow:20,this.compress=i.compress!==void 0?i.compress:t.compress!==void 0?t.compress:!0,this.counter=i.counter||t.counter||0,this.agent=i.agent||t.agent}get method(){return this[ta].method}get url(){return Ku(this[ta].parsedURL)}get headers(){return this[ta].headers}get redirect(){return this[ta].redirect}get signal(){return this[ta].signal}clone(){return new p(this)}};P1.mixIn(Na.prototype);Object.defineProperty(Na.prototype,Symbol.toStringTag,{value:"Request",writable:!1,enumerable:!1,configurable:!0});Object.defineProperties(Na.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0}});function Yu(p){let t=p[ta].parsedURL,i=new we(p[ta].headers);if(i.has("Accept")||i.set("Accept","*/*"),!t.protocol||!t.hostname)throw new TypeError("Only absolute URLs are supported");if(!/^https?:$/.test(t.protocol))throw new TypeError("Only HTTP(S) protocols are supported");if(p.signal&&p.body instanceof Ee.Readable&&!Zu)throw new Error("Cancellation of streamed requests with AbortSignal is not supported in node < 8");let r=null;if(p.body==null&&/^(POST|PUT)$/i.test(p.method)&&(r="0"),p.body!=null){let u=e9(p);typeof u=="number"&&(r=String(u))}r&&i.set("Content-Length",r),i.has("User-Agent")||i.set("User-Agent","node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"),p.compress&&!i.has("Accept-Encoding")&&i.set("Accept-Encoding","gzip,deflate");let n=p.agent;return typeof n=="function"&&(n=n(t)),Object.assign({},t,{method:p.method,headers:$u(i),agent:n})}function bp(p){Error.call(this,p),this.type="aborted",this.message=p,Error.captureStackTrace(this,this.constructor)}bp.prototype=Object.create(Error.prototype);bp.prototype.constructor=bp;bp.prototype.name="AbortError";var c2=Dt.URL||Jr.URL,Kr=Ee.PassThrough,Qu=function(t,i){let r=new c2(i).hostname,n=new c2(t).hostname;return r===n||r[r.length-n.length-1]==="."&&r.endsWith(n)},Xu=function(t,i){let r=new c2(i).protocol,n=new c2(t).protocol;return r===n};function Sa(p,t){if(!Sa.Promise)throw new Error("native promise missing, set fetch.Promise to your favorite alternative");return P1.Promise=Sa.Promise,new Sa.Promise(function(i,r){let n=new Na(p,t),u=Yu(n),v=(u.protocol==="https:"?Uu:Zr).request,_=n.signal,f=null,N=function(){let R=new bp("The user aborted a request.");r(R),n.body&&n.body instanceof Ee.Readable&&O0(n.body,R),!(!f||!f.body)&&f.body.emit("error",R)};if(_&&_.aborted){N();return}let A=function(){N(),F()},D=v(u),V;_&&_.addEventListener("abort",A);function F(){D.abort(),_&&_.removeEventListener("abort",A),clearTimeout(V)}n.timeout&&D.once("socket",function(C){V=setTimeout(function(){r(new L1(`network timeout at: ${n.url}`,"request-timeout")),F()},n.timeout)}),D.on("error",function(C){r(new L1(`request to ${n.url} failed, reason: ${C.message}`,"system",C)),f&&f.body&&O0(f.body,C),F()}),ec(D,function(C){_&&_.aborted||f&&f.body&&O0(f.body,C)}),parseInt(process.version.substring(1))<14&&D.on("socket",function(C){C.addListener("close",function(R){let q=C.listenerCount("data")>0;if(f&&q&&!R&&!(_&&_.aborted)){let P=new Error("Premature close");P.code="ERR_STREAM_PREMATURE_CLOSE",f.body.emit("error",P)}})}),D.on("response",function(C){clearTimeout(V);let R=Wu(C.headers);if(Sa.isRedirect(C.statusCode)){let U=R.get("Location"),H=null;try{H=U===null?null:new c2(U,n.url).toString()}catch{if(n.redirect!=="manual"){r(new L1(`uri requested responds with an invalid redirect URL: ${U}`,"invalid-redirect")),F();return}}switch(n.redirect){case"error":r(new L1(`uri requested responds with a redirect, redirect mode is set to error: ${n.url}`,"no-redirect")),F();return;case"manual":if(H!==null)try{R.set("Location",H)}catch(N1){r(N1)}break;case"follow":if(H===null)break;if(n.counter>=n.follow){r(new L1(`maximum redirect reached at: ${n.url}`,"max-redirect")),F();return}let e1={headers:new we(n.headers),follow:n.follow,counter:n.counter+1,agent:n.agent,compress:n.compress,method:n.method,body:n.body,signal:n.signal,timeout:n.timeout,size:n.size};if(!Qu(n.url,H)||!Xu(n.url,H))for(let N1 of["authorization","www-authenticate","cookie","cookie2"])e1.headers.delete(N1);if(C.statusCode!==303&&n.body&&e9(n)===null){r(new L1("Cannot follow redirect with body being a readable stream","unsupported-redirect")),F();return}(C.statusCode===303||(C.statusCode===301||C.statusCode===302)&&n.method==="POST")&&(e1.method="GET",e1.body=void 0,e1.headers.delete("content-length")),i(Sa(new Na(H,e1))),F();return}}C.once("end",function(){_&&_.removeEventListener("abort",A)});let q=C.pipe(new Kr),P={url:n.url,status:C.statusCode,statusText:C.statusMessage,headers:R,size:n.size,timeout:n.timeout,counter:n.counter},L=R.get("Content-Encoding");if(!n.compress||n.method==="HEAD"||L===null||C.statusCode===204||C.statusCode===304){f=new _e(q,P),i(f);return}let B={flush:Ua.Z_SYNC_FLUSH,finishFlush:Ua.Z_SYNC_FLUSH};if(L=="gzip"||L=="x-gzip"){q=q.pipe(Ua.createGunzip(B)),f=new _e(q,P),i(f);return}if(L=="deflate"||L=="x-deflate"){let U=C.pipe(new Kr);U.once("data",function(H){(H[0]&15)===8?q=q.pipe(Ua.createInflate()):q=q.pipe(Ua.createInflateRaw()),f=new _e(q,P),i(f)}),U.on("end",function(){f||(f=new _e(q,P),i(f))});return}if(L=="br"&&typeof Ua.createBrotliDecompress=="function"){q=q.pipe(Ua.createBrotliDecompress()),f=new _e(q,P),i(f);return}f=new _e(q,P),i(f)}),zu(D,n)})}function ec(p,t){let i;p.on("socket",function(r){i=r}),p.on("response",function(r){let n=r.headers;n["transfer-encoding"]==="chunked"&&!n["content-length"]&&r.once("close",function(u){if(i&&i.listenerCount("data")>0&&!u){let _=new Error("Premature close");_.code="ERR_STREAM_PREMATURE_CLOSE",t(_)}})})}function O0(p,t){p.destroy?p.destroy(t):(p.emit("error",t),p.end())}Sa.isRedirect=function(p){return p===301||p===302||p===303||p===307||p===308};Sa.Promise=global.Promise;p9.exports=Ve=Sa;Object.defineProperty(Ve,"__esModule",{value:!0});Ve.default=Ve;Ve.Headers=we;Ve.Request=Na;Ve.Response=_e;Ve.FetchError=L1;Ve.AbortError=bp});var Et=I((Ma,d9)=>{var h2=t9(),ac=h2.default||h2,At=function(p,t){return/^\/\//.test(p)&&(p="https:"+p),ac.call(this,p,t)};At.ponyfill=!0;d9.exports=Ma=At;Ma.fetch=At;Ma.Headers=h2.Headers;Ma.Request=h2.Request;Ma.Response=h2.Response;Ma.default=At});var M0=I(Vt=>{"use strict";Object.defineProperty(Vt,"__esModule",{value:!0});Vt.isJSON=void 0;function pc(p){if(p){let t=dc(p);if(!t)return!1;if(t.subtype==="json"||t.suffix==="json"||t.suffix&&/\bjson\b/i.test(t.suffix)||t.subtype&&/\bjson\b/i.test(t.subtype))return!0}return!1}Vt.isJSON=pc;var tc=/^([A-Za-z0-9][A-Za-z0-9!#$&^_-]{0,126})\/([A-Za-z0-9][A-Za-z0-9!#$&^_.+-]{0,126});?$/;function dc(p){let t=p.indexOf(";"),i=t!==-1?p.slice(0,t).trim():p.trim(),r=tc.exec(i.toLowerCase().toLowerCase());if(!r)return;let n=r[1],u=r[2],v,_=u.lastIndexOf("+");return _!==-1&&(v=u.substring(_+1),u=u.substring(0,_)),{type:n,subtype:u,suffix:v}}});var s9=I(Ct=>{"use strict";Object.defineProperty(Ct,"__esModule",{value:!0});Ct.useFetch=void 0;var Sp=require("react"),ic=n2(),i9=aa(),r9=Et(),rc=M0(),n9=Fa();async function nc(p){if(!p.ok)throw new Error(p.statusText);let t=p.headers.get("content-type");return t&&(0,rc.isJSON)(t)?await p.json():await p.text()}function sc(p){return{data:p,hasMore:!1}}function lc(p,t){let{parseResponse:i,mapResult:r,initialData:n,execute:u,keepPreviousData:v,onError:_,onData:f,onWillExecute:N,failureToastOptions:A,...D}=t||{},V={initialData:n,execute:u,keepPreviousData:v,onError:_,onData:f,onWillExecute:N,failureToastOptions:A},F=(0,i9.useLatest)(i||nc),C=(0,i9.useLatest)(r||sc),R=(0,Sp.useRef)(),q=(0,Sp.useRef)(),P=typeof p=="function"?p({page:0}):void 0;(!R.current||typeof q.current>"u"||q.current!==P)&&(R.current=p),q.current=P;let L=(0,Sp.useRef)(),B=(0,Sp.useCallback)((e1,N1)=>async F1=>{let Q1=await(0,r9.fetch)(e1(F1),{signal:L.current?.signal,...N1}),R1=await F.current(Q1);return C.current?.(R1)},[F,C]),U=(0,Sp.useCallback)(async(e1,N1)=>{let F1=await(0,r9.fetch)(e1,{signal:L.current?.signal,...N1}),Q1=await F.current(F1);return C.current(Q1)?.data},[F,C]),H=(0,Sp.useMemo)(()=>q.current?B:U,[q,U,B]);return(0,ic.useCachedPromise)(H,[R.current,D],{...V,internal_cacheKeySuffix:q.current+(0,n9.hash)(C.current)+(0,n9.hash)(F.current),abortable:L})}Ct.useFetch=lc});var l9=I(qa=>{"use strict";Object.defineProperty(qa,"__esModule",{value:!0});qa.signals=void 0;qa.signals=[];qa.signals.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&qa.signals.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&qa.signals.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT")});var m9=I(Ue=>{"use strict";var Pt;Object.defineProperty(Ue,"__esModule",{value:!0});Ue.unload=Ue.load=Ue.onExit=Ue.signals=void 0;var kt=l9();Object.defineProperty(Ue,"signals",{enumerable:!0,get:function(){return kt.signals}});var Rt=p=>!!p&&typeof p=="object"&&typeof p.removeListener=="function"&&typeof p.emit=="function"&&typeof p.reallyExit=="function"&&typeof p.listeners=="function"&&typeof p.kill=="function"&&typeof p.pid=="number"&&typeof p.on=="function",q0=Symbol.for("signal-exit emitter"),z0=globalThis,mc=Object.defineProperty.bind(Object),$0=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(z0[q0])return z0[q0];mc(z0,q0,{value:this,writable:!1,enumerable:!1,configurable:!1})}on(t,i){this.listeners[t].push(i)}removeListener(t,i){let r=this.listeners[t],n=r.indexOf(i);n!==-1&&(n===0&&r.length===1?r.length=0:r.splice(n,1))}emit(t,i,r){if(this.emitted[t])return!1;this.emitted[t]=!0;let n=!1;for(let u of this.listeners[t])n=u(i,r)===!0||n;return t==="exit"&&(n=this.emit("afterExit",i,r)||n),n}},Ot=class{},oc=p=>({onExit(t,i){return p.onExit(t,i)},load(){return p.load()},unload(){return p.unload()}}),W0=class extends Ot{onExit(){return()=>{}}load(){}unload(){}},j0=class extends Ot{#r=H0.platform==="win32"?"SIGINT":"SIGHUP";#a=new $0;#e;#d;#i;#t={};#p=!1;constructor(t){super(),this.#e=t,this.#t={};for(let i of kt.signals)this.#t[i]=()=>{let r=this.#e.listeners(i),{count:n}=this.#a,u=t;if(typeof u.__signal_exit_emitter__=="object"&&typeof u.__signal_exit_emitter__.count=="number"&&(n+=u.__signal_exit_emitter__.count),r.length===n){this.unload();let v=this.#a.emit("exit",null,i),_=i==="SIGHUP"?this.#r:i;v||t.kill(t.pid,_)}};this.#i=t.reallyExit,this.#d=t.emit}onExit(t,i){if(!Rt(this.#e))return()=>{};this.#p===!1&&this.load();let r=i?.alwaysLast?"afterExit":"exit";return this.#a.on(r,t),()=>{this.#a.removeListener(r,t),this.#a.listeners.exit.length===0&&this.#a.listeners.afterExit.length===0&&this.unload()}}load(){if(!this.#p){this.#p=!0,this.#a.count+=1;for(let t of kt.signals)try{let i=this.#t[t];i&&this.#e.on(t,i)}catch{}this.#e.emit=(t,...i)=>this.#s(t,...i),this.#e.reallyExit=t=>this.#n(t)}}unload(){this.#p&&(this.#p=!1,kt.signals.forEach(t=>{let i=this.#t[t];if(!i)throw new Error("Listener not defined for signal: "+t);try{this.#e.removeListener(t,i)}catch{}}),this.#e.emit=this.#d,this.#e.reallyExit=this.#i,this.#a.count-=1)}#n(t){return Rt(this.#e)?(this.#e.exitCode=t||0,this.#a.emit("exit",this.#e.exitCode,null),this.#i.call(this.#e,this.#e.exitCode)):0}#s(t,...i){let r=this.#d;if(t==="exit"&&Rt(this.#e)){typeof i[0]=="number"&&(this.#e.exitCode=i[0]);let n=r.call(this.#e,t,...i);return this.#a.emit("exit",this.#e.exitCode,null),n}else return r.call(this.#e,t,...i)}},H0=globalThis.process;Pt=oc(Rt(H0)?new j0(H0):new W0),Ue.onExit=Pt.onExit,Ue.load=Pt.load,Ue.unload=Pt.unload});var Lt=I(ge=>{"use strict";var uc=ge&&ge.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(ge,"__esModule",{value:!0});ge.defaultParsing=ge.handleOutput=ge.getSpawnedResult=ge.getSpawnedPromise=void 0;var cc=require("node:buffer"),c9=uc(require("node:stream")),fc=require("node:util"),hc=m9();function vc(p,{timeout:t}={}){let i=new Promise((_,f)=>{p.on("exit",(N,A)=>{_({exitCode:N,signal:A,timedOut:!1})}),p.on("error",N=>{f(N)}),p.stdin&&p.stdin.on("error",N=>{f(N)})});if(t===0||t===void 0)return i;let r,n=new Promise((_,f)=>{r=setTimeout(()=>{p.kill("SIGTERM"),f(Object.assign(new Error("Timed out"),{timedOut:!0,signal:"SIGTERM"}))},t)}),u=i.finally(()=>{clearTimeout(r)}),v=(0,hc.onExit)(()=>{p.kill()});return Promise.race([n,u]).finally(()=>v())}ge.getSpawnedPromise=vc;var G0=class extends Error{constructor(){super("The output is too big"),this.name="MaxBufferError"}},_c=(0,fc.promisify)(c9.default.pipeline);function wc(p){let{encoding:t}=p,i=t==="buffer",r=new c9.default.PassThrough({objectMode:!1});t&&t!=="buffer"&&r.setEncoding(t);let n=0,u=[];return r.on("data",v=>{u.push(v),n+=v.length}),r.getBufferedValue=()=>i?Buffer.concat(u,n):u.join(""),r.getBufferedLength=()=>n,r}async function o9(p,t){let i=wc(t);return await new Promise((r,n)=>{let u=v=>{v&&i.getBufferedLength()<=cc.constants.MAX_LENGTH&&(v.bufferedData=i.getBufferedValue()),n(v)};(async()=>{try{await _c(p,i),r()}catch(v){u(v)}})(),i.on("data",()=>{i.getBufferedLength()>1e3*1e3*80&&u(new G0)})}),i.getBufferedValue()}async function u9(p,t){p.destroy();try{return await t}catch(i){return i.bufferedData}}async function gc({stdout:p,stderr:t},{encoding:i},r){let n=o9(p,{encoding:i}),u=o9(t,{encoding:i});try{return await Promise.all([r,n,u])}catch(v){return Promise.all([{error:v,exitCode:null,signal:v.signal,timedOut:v.timedOut||!1},u9(p,n),u9(t,u)])}}ge.getSpawnedResult=gc;function bc(p){let t=typeof p=="string"?`
`:10,i=typeof p=="string"?"\r":13;return p[p.length-1]===t&&(p=p.slice(0,-1)),p[p.length-1]===i&&(p=p.slice(0,-1)),p}function yc(p,t){return p.stripFinalNewline?bc(t):t}ge.handleOutput=yc;var Sc=({timedOut:p,timeout:t,signal:i,exitCode:r})=>p?`timed out after ${t} milliseconds`:i!=null?`was killed with ${i}`:r!=null?`failed with exit code ${r}`:"failed",Nc=({stdout:p,stderr:t,error:i,signal:r,exitCode:n,command:u,timedOut:v,options:_,parentError:f})=>{let A=`Command ${Sc({timedOut:v,timeout:_?.timeout,signal:r,exitCode:n})}: ${u}`,D=i?`${A}
${i.message}`:A,V=[D,t,p].filter(Boolean).join(`
`);return i?i.originalMessage=i.message:i=f,i.message=V,i.shortMessage=D,i.command=u,i.exitCode=n,i.signal=r,i.stdout=p,i.stderr=t,"bufferedData"in i&&delete i.bufferedData,i};function Tc({stdout:p,stderr:t,error:i,exitCode:r,signal:n,timedOut:u,command:v,options:_,parentError:f}){if(i||r!==0||n!==null)throw Nc({error:i,exitCode:r,signal:n,stdout:p,stderr:t,command:v,timedOut:u,options:_,parentError:f});return p}ge.defaultParsing=Tc});var h9=I(Np=>{"use strict";var xc=Np&&Np.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(Np,"__esModule",{value:!0});Np.useExec=void 0;var Dc=xc(require("node:child_process")),f9=require("react"),Ac=n2(),Ec=aa(),v2=Lt(),Vc=/ +/g;function Cc(p,t){if(t)return[p,...t];let i=[];for(let r of p.trim().split(Vc)){let n=i[i.length-1];n&&n.endsWith("\\")?i[i.length-1]=`${n.slice(0,-1)} ${r}`:i.push(r)}return i}function Pc(p,t,i){let{parseOutput:r,input:n,onData:u,onWillExecute:v,initialData:_,execute:f,keepPreviousData:N,onError:A,failureToastOptions:D,...V}=Array.isArray(t)?i||{}:t||{},F={initialData:_,execute:f,keepPreviousData:N,onError:A,onData:u,onWillExecute:v,failureToastOptions:D},C=(0,f9.useRef)(),R=(0,Ec.useLatest)(r||v2.defaultParsing),q=(0,f9.useCallback)(async(P,L,B,U)=>{let[H,...e1]=Cc(P,L),N1=[H,...e1].join(" "),F1={stripFinalNewline:!0,...B,timeout:B?.timeout||1e4,signal:C.current?.signal,encoding:B?.encoding===null?"buffer":B?.encoding||"utf8",env:{PATH:"/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin",...process.env,...B?.env}},Q1=Dc.default.spawn(H,e1,F1),R1=(0,v2.getSpawnedPromise)(Q1,F1);U&&Q1.stdin.end(U);let[{error:T1,exitCode:H1,signal:ne,timedOut:a1},w1,G1]=await(0,v2.getSpawnedResult)(Q1,F1,R1),Rp=(0,v2.handleOutput)(F1,w1),Ja=(0,v2.handleOutput)(F1,G1);return R.current({stdout:Rp,stderr:Ja,error:T1,exitCode:H1,signal:ne,timedOut:a1,command:N1,options:F1,parentError:new Error})},[R]);return(0,Ac.useCachedPromise)(q,[p,Array.isArray(t)?t:[],V,n],{...F,abortable:C})}Np.useExec=Pc});var J0=I((nv,g9)=>{"use strict";var{Readable:iv,Writable:rv,Duplex:kc,Transform:w9}=require("stream"),Rc=Symbol.for("object-stream.none"),K0=Symbol.for("object-stream.final"),Z0=Symbol.for("object-stream.many"),Oc=p=>({[K0]:p}),Lc=p=>({[Z0]:p}),Ic=p=>p&&typeof p=="object"&&K0 in p,Fc=p=>p&&typeof p=="object"&&Z0 in p,Bc=p=>p[K0],Uc=p=>p[Z0],Mc=async(p,t)=>{for(;;){let i=p.next();if(i&&typeof i.then=="function"&&(i=await i),i.done)break;let r=i.value;r&&typeof r.then=="function"&&(r=await r),S1.sanitize(r,t)}},qc=p=>new w9({writableObjectMode:!0,readableObjectMode:!0,transform(t,i,r){try{let n=p.call(this,t,i);if(n&&typeof n.then=="function"){n.then(u=>(S1.sanitize(u,this),r(null)),u=>r(u));return}if(n&&typeof n.next=="function"){Mc(n,this).then(()=>r(null),u=>r(u));return}S1.sanitize(n,this),r(null)}catch(n){r(n)}}}),zc=p=>new w9({writableObjectMode:!0,readableObjectMode:!0,transform(t,i,r){try{let n=t;for(let u=0;u<p.length;++u){let v=p[u].call(this,n,i);if(v===S1.none){r(null);return}if(S1.isFinal(v)){n=S1.getFinalValue(v);break}n=v}S1.sanitize(n,this),r(null)}catch(n){r(n)}}}),v9=p=>p&&typeof p.pipe=="function"&&typeof p.on=="function"&&(!p._writableState||(typeof p._readableState=="object"?p._readableState.readable:null)!==!1)&&(!p._writableState||p._readableState),_9=p=>p&&typeof p.write=="function"&&typeof p.on=="function"&&(!p._readableState||(typeof p._writableState=="object"?p._writableState.writable:null)!==!1),$c=p=>p&&typeof p.pipe=="function"&&p._readableState&&typeof p.on=="function"&&typeof p.write=="function",S1=class p extends kc{constructor(t,i){if(super(i||{writableObjectMode:!0,readableObjectMode:!0}),!(t instanceof Array)||!t.length)throw Error("Chain's argument should be a non-empty array.");this.streams=t.filter(r=>r).map((r,n,u)=>{if(typeof r=="function"||r instanceof Array)return p.convertToTransform(r);if($c(r)||!n&&v9(r)||n===u.length-1&&_9(r))return r;throw Error("Arguments should be functions, arrays or streams.")}).filter(r=>r),this.input=this.streams[0],this.output=this.streams.reduce((r,n)=>r&&r.pipe(n)||n),_9(this.input)||(this._write=(r,n,u)=>u(null),this._final=r=>r(null),this.input.on("end",()=>this.end())),v9(this.output)?(this.output.on("data",r=>!this.push(r)&&this.output.pause()),this.output.on("end",()=>this.push(null))):(this._read=()=>{},this.resume(),this.output.on("finish",()=>this.push(null))),(!i||!i.skipEvents)&&this.streams.forEach(r=>r.on("error",n=>this.emit("error",n)))}_write(t,i,r){let n=null;try{this.input.write(t,i,u=>r(u||n))}catch(u){n=u}}_final(t){let i=null;try{this.input.end(null,null,r=>t(r||i))}catch(r){i=r}}_read(){this.output.resume()}static make(t,i){return new p(t,i)}static sanitize(t,i){p.isFinal(t)?t=p.getFinalValue(t):p.isMany(t)&&(t=p.getManyValues(t)),t!=null&&t!==p.none&&(t instanceof Array?t.forEach(r=>r!=null&&i.push(r)):i.push(t))}static convertToTransform(t){return typeof t=="function"?qc(t):t instanceof Array&&t.length?zc(t):null}};S1.none=Rc;S1.final=Oc;S1.isFinal=Ic;S1.getFinalValue=Bc;S1.many=Lc;S1.isMany=Fc;S1.getManyValues=Uc;S1.chain=S1.make;S1.make.Constructor=S1;g9.exports=S1});var y9=I((sv,b9)=>{"use strict";var{Transform:Wc}=require("stream"),{StringDecoder:jc}=require("string_decoder"),Y0=class extends Wc{constructor(t){super(Object.assign({},t,{writableObjectMode:!1})),this._buffer=""}_transform(t,i,r){typeof t=="string"?this._transform=this._transformString:(this._stringDecoder=new jc,this._transform=this._transformBuffer),this._transform(t,i,r)}_transformBuffer(t,i,r){this._buffer+=this._stringDecoder.write(t),this._processBuffer(r)}_transformString(t,i,r){this._buffer+=t.toString(),this._processBuffer(r)}_processBuffer(t){this._buffer&&(this.push(this._buffer,"utf8"),this._buffer=""),t(null)}_flushInput(){this._stringDecoder&&(this._buffer+=this._stringDecoder.end())}_flush(t){this._flushInput(),this._processBuffer(t)}};b9.exports=Y0});var Q0=I((lv,S9)=>{"use strict";var Hc=y9(),J={value1:/^(?:[\"\{\[\]\-\d]|true\b|false\b|null\b|\s{1,256})/,string:/^(?:[^\"\\]{1,256}|\\[bfnrt\"\\\/]|\\u[\da-fA-F]{4}|\")/,key1:/^(?:[\"\}]|\s{1,256})/,colon:/^(?:\:|\s{1,256})/,comma:/^(?:[\,\]\}]|\s{1,256})/,ws:/^\s{1,256}/,numberStart:/^\d/,numberDigit:/^\d{0,256}/,numberFraction:/^[\.eE]/,numberExponent:/^[eE]/,numberExpSign:/^[-+]/},Gc=16,k1=!0;try{new RegExp(".","y"),k1=!1}catch{}!k1&&Object.keys(J).forEach(p=>{let t=J[p].source.slice(1);t.slice(0,3)==="(?:"&&t.slice(-1)===")"&&(t=t.slice(3,-1)),J[p]=new RegExp(t,"y")});J.numberFracStart=J.numberExpStart=J.numberStart;J.numberFracDigit=J.numberExpDigit=J.numberDigit;var Kc={true:!0,false:!1,null:null},Me={object:"objectStop",array:"arrayStop","":"done"},Zc=p=>String.fromCharCode(parseInt(p.slice(2),16)),Jc={b:"\b",f:"\f",n:`
`,r:"\r",t:"	",'"':'"',"\\":"\\","/":"/"},za=class p extends Hc{static make(t){return new p(t)}constructor(t){super(Object.assign({},t,{readableObjectMode:!0})),this._packKeys=this._packStrings=this._packNumbers=this._streamKeys=this._streamStrings=this._streamNumbers=!0,t&&("packValues"in t&&(this._packKeys=this._packStrings=this._packNumbers=t.packValues),"packKeys"in t&&(this._packKeys=t.packKeys),"packStrings"in t&&(this._packStrings=t.packStrings),"packNumbers"in t&&(this._packNumbers=t.packNumbers),"streamValues"in t&&(this._streamKeys=this._streamStrings=this._streamNumbers=t.streamValues),"streamKeys"in t&&(this._streamKeys=t.streamKeys),"streamStrings"in t&&(this._streamStrings=t.streamStrings),"streamNumbers"in t&&(this._streamNumbers=t.streamNumbers),this._jsonStreaming=t.jsonStreaming),!this._packKeys&&(this._streamKeys=!0),!this._packStrings&&(this._streamStrings=!0),!this._packNumbers&&(this._streamNumbers=!0),this._done=!1,this._expect=this._jsonStreaming?"done":"value",this._stack=[],this._parent="",this._open_number=!1,this._accumulator=""}_flush(t){this._done=!0,super._flush(i=>{if(i)return t(i);this._open_number&&(this._streamNumbers&&this.push({name:"endNumber"}),this._open_number=!1,this._packNumbers&&(this.push({name:"numberValue",value:this._accumulator}),this._accumulator="")),t(null)})}_processBuffer(t){let i,r,n=0;e:for(;;)switch(this._expect){case"value1":case"value":if(J.value1.lastIndex=n,i=J.value1.exec(this._buffer),!i){if(this._done||n+Gc<this._buffer.length)return n<this._buffer.length?t(new Error("Parser cannot parse input: expected a value")):t(new Error("Parser has expected a value"));break e}switch(r=i[0],r){case'"':this._streamStrings&&this.push({name:"startString"}),this._expect="string";break;case"{":this.push({name:"startObject"}),this._stack.push(this._parent),this._parent="object",this._expect="key1";break;case"[":this.push({name:"startArray"}),this._stack.push(this._parent),this._parent="array",this._expect="value1";break;case"]":if(this._expect!=="value1")return t(new Error("Parser cannot parse input: unexpected token ']'"));this._open_number&&(this._streamNumbers&&this.push({name:"endNumber"}),this._open_number=!1,this._packNumbers&&(this.push({name:"numberValue",value:this._accumulator}),this._accumulator="")),this.push({name:"endArray"}),this._parent=this._stack.pop(),this._expect=Me[this._parent];break;case"-":this._open_number=!0,this._streamNumbers&&(this.push({name:"startNumber"}),this.push({name:"numberChunk",value:"-"})),this._packNumbers&&(this._accumulator="-"),this._expect="numberStart";break;case"0":this._open_number=!0,this._streamNumbers&&(this.push({name:"startNumber"}),this.push({name:"numberChunk",value:"0"})),this._packNumbers&&(this._accumulator="0"),this._expect="numberFraction";break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":this._open_number=!0,this._streamNumbers&&(this.push({name:"startNumber"}),this.push({name:"numberChunk",value:r})),this._packNumbers&&(this._accumulator=r),this._expect="numberDigit";break;case"true":case"false":case"null":if(this._buffer.length-n===r.length&&!this._done)break e;this.push({name:r+"Value",value:Kc[r]}),this._expect=Me[this._parent];break}k1?this._buffer=this._buffer.slice(r.length):n+=r.length;break;case"keyVal":case"string":if(J.string.lastIndex=n,i=J.string.exec(this._buffer),!i){if(n<this._buffer.length&&(this._done||this._buffer.length-n>=6))return t(new Error("Parser cannot parse input: escaped characters"));if(this._done)return t(new Error("Parser has expected a string value"));break e}if(r=i[0],r==='"')this._expect==="keyVal"?(this._streamKeys&&this.push({name:"endKey"}),this._packKeys&&(this.push({name:"keyValue",value:this._accumulator}),this._accumulator=""),this._expect="colon"):(this._streamStrings&&this.push({name:"endString"}),this._packStrings&&(this.push({name:"stringValue",value:this._accumulator}),this._accumulator=""),this._expect=Me[this._parent]);else if(r.length>1&&r.charAt(0)==="\\"){let u=r.length==2?Jc[r.charAt(1)]:Zc(r);(this._expect==="keyVal"?this._streamKeys:this._streamStrings)&&this.push({name:"stringChunk",value:u}),(this._expect==="keyVal"?this._packKeys:this._packStrings)&&(this._accumulator+=u)}else(this._expect==="keyVal"?this._streamKeys:this._streamStrings)&&this.push({name:"stringChunk",value:r}),(this._expect==="keyVal"?this._packKeys:this._packStrings)&&(this._accumulator+=r);k1?this._buffer=this._buffer.slice(r.length):n+=r.length;break;case"key1":case"key":if(J.key1.lastIndex=n,i=J.key1.exec(this._buffer),!i){if(n<this._buffer.length||this._done)return t(new Error("Parser cannot parse input: expected an object key"));break e}if(r=i[0],r==='"')this._streamKeys&&this.push({name:"startKey"}),this._expect="keyVal";else if(r==="}"){if(this._expect!=="key1")return t(new Error("Parser cannot parse input: unexpected token '}'"));this.push({name:"endObject"}),this._parent=this._stack.pop(),this._expect=Me[this._parent]}k1?this._buffer=this._buffer.slice(r.length):n+=r.length;break;case"colon":if(J.colon.lastIndex=n,i=J.colon.exec(this._buffer),!i){if(n<this._buffer.length||this._done)return t(new Error("Parser cannot parse input: expected ':'"));break e}r=i[0],r===":"&&(this._expect="value"),k1?this._buffer=this._buffer.slice(r.length):n+=r.length;break;case"arrayStop":case"objectStop":if(J.comma.lastIndex=n,i=J.comma.exec(this._buffer),!i){if(n<this._buffer.length||this._done)return t(new Error("Parser cannot parse input: expected ','"));break e}if(this._open_number&&(this._streamNumbers&&this.push({name:"endNumber"}),this._open_number=!1,this._packNumbers&&(this.push({name:"numberValue",value:this._accumulator}),this._accumulator="")),r=i[0],r===",")this._expect=this._expect==="arrayStop"?"value":"key";else if(r==="}"||r==="]"){if(r==="}"?this._expect==="arrayStop":this._expect!=="arrayStop")return t(new Error("Parser cannot parse input: expected '"+(this._expect==="arrayStop"?"]":"}")+"'"));this.push({name:r==="}"?"endObject":"endArray"}),this._parent=this._stack.pop(),this._expect=Me[this._parent]}k1?this._buffer=this._buffer.slice(r.length):n+=r.length;break;case"numberStart":if(J.numberStart.lastIndex=n,i=J.numberStart.exec(this._buffer),!i){if(n<this._buffer.length||this._done)return t(new Error("Parser cannot parse input: expected a starting digit"));break e}r=i[0],this._streamNumbers&&this.push({name:"numberChunk",value:r}),this._packNumbers&&(this._accumulator+=r),this._expect=r==="0"?"numberFraction":"numberDigit",k1?this._buffer=this._buffer.slice(r.length):n+=r.length;break;case"numberDigit":if(J.numberDigit.lastIndex=n,i=J.numberDigit.exec(this._buffer),!i){if(n<this._buffer.length||this._done)return t(new Error("Parser cannot parse input: expected a digit"));break e}if(r=i[0],r)this._streamNumbers&&this.push({name:"numberChunk",value:r}),this._packNumbers&&(this._accumulator+=r),k1?this._buffer=this._buffer.slice(r.length):n+=r.length;else{if(n<this._buffer.length){this._expect="numberFraction";break}if(this._done){this._expect=Me[this._parent];break}break e}break;case"numberFraction":if(J.numberFraction.lastIndex=n,i=J.numberFraction.exec(this._buffer),!i){if(n<this._buffer.length||this._done){this._expect=Me[this._parent];break}break e}r=i[0],this._streamNumbers&&this.push({name:"numberChunk",value:r}),this._packNumbers&&(this._accumulator+=r),this._expect=r==="."?"numberFracStart":"numberExpSign",k1?this._buffer=this._buffer.slice(r.length):n+=r.length;break;case"numberFracStart":if(J.numberFracStart.lastIndex=n,i=J.numberFracStart.exec(this._buffer),!i){if(n<this._buffer.length||this._done)return t(new Error("Parser cannot parse input: expected a fractional part of a number"));break e}r=i[0],this._streamNumbers&&this.push({name:"numberChunk",value:r}),this._packNumbers&&(this._accumulator+=r),this._expect="numberFracDigit",k1?this._buffer=this._buffer.slice(r.length):n+=r.length;break;case"numberFracDigit":if(J.numberFracDigit.lastIndex=n,i=J.numberFracDigit.exec(this._buffer),r=i[0],r)this._streamNumbers&&this.push({name:"numberChunk",value:r}),this._packNumbers&&(this._accumulator+=r),k1?this._buffer=this._buffer.slice(r.length):n+=r.length;else{if(n<this._buffer.length){this._expect="numberExponent";break}if(this._done){this._expect=Me[this._parent];break}break e}break;case"numberExponent":if(J.numberExponent.lastIndex=n,i=J.numberExponent.exec(this._buffer),!i){if(n<this._buffer.length){this._expect=Me[this._parent];break}if(this._done){this._expect="done";break}break e}r=i[0],this._streamNumbers&&this.push({name:"numberChunk",value:r}),this._packNumbers&&(this._accumulator+=r),this._expect="numberExpSign",k1?this._buffer=this._buffer.slice(r.length):n+=r.length;break;case"numberExpSign":if(J.numberExpSign.lastIndex=n,i=J.numberExpSign.exec(this._buffer),!i){if(n<this._buffer.length){this._expect="numberExpStart";break}if(this._done)return t(new Error("Parser has expected an exponent value of a number"));break e}r=i[0],this._streamNumbers&&this.push({name:"numberChunk",value:r}),this._packNumbers&&(this._accumulator+=r),this._expect="numberExpStart",k1?this._buffer=this._buffer.slice(r.length):n+=r.length;break;case"numberExpStart":if(J.numberExpStart.lastIndex=n,i=J.numberExpStart.exec(this._buffer),!i){if(n<this._buffer.length||this._done)return t(new Error("Parser cannot parse input: expected an exponent part of a number"));break e}r=i[0],this._streamNumbers&&this.push({name:"numberChunk",value:r}),this._packNumbers&&(this._accumulator+=r),this._expect="numberExpDigit",k1?this._buffer=this._buffer.slice(r.length):n+=r.length;break;case"numberExpDigit":if(J.numberExpDigit.lastIndex=n,i=J.numberExpDigit.exec(this._buffer),r=i[0],r)this._streamNumbers&&this.push({name:"numberChunk",value:r}),this._packNumbers&&(this._accumulator+=r),k1?this._buffer=this._buffer.slice(r.length):n+=r.length;else{if(n<this._buffer.length||this._done){this._expect=Me[this._parent];break}break e}break;case"done":if(J.ws.lastIndex=n,i=J.ws.exec(this._buffer),!i){if(n<this._buffer.length){if(this._jsonStreaming){this._expect="value";break}return t(new Error("Parser cannot parse input: unexpected characters"))}break e}r=i[0],this._open_number&&(this._streamNumbers&&this.push({name:"endNumber"}),this._open_number=!1,this._packNumbers&&(this.push({name:"numberValue",value:this._accumulator}),this._accumulator="")),k1?this._buffer=this._buffer.slice(r.length):n+=r.length;break}!k1&&(this._buffer=this._buffer.slice(n)),t(null)}};za.parser=za.make;za.make.Constructor=za;S9.exports=za});var T9=I((mv,N9)=>{"use strict";var Yc=p=>p.on("data",t=>p.emit(t.name,t.value));N9.exports=Yc});var D9=I((ov,x9)=>{"use strict";var X0=Q0(),Qc=T9(),e6=p=>Qc(new X0(p));e6.Parser=X0;e6.parser=X0.parser;x9.exports=e6});var E9=I((uv,A9)=>{"use strict";var{Transform:Xc}=require("stream"),qe=class p extends Xc{static stringFilter(t,i){return r=>{let n=r.join(i);return n.length===t.length&&n===t||n.length>t.length&&n.substr(0,t.length)===t&&n.substr(t.length,i.length)===i}}static regExpFilter(t,i){return r=>t.test(r.join(i))}static arrayReplacement(t){return()=>t}constructor(t){super(Object.assign({},t,{writableObjectMode:!0,readableObjectMode:!0})),this._transform=this._check,this._stack=[];let i=t&&t.filter,r=t&&t.pathSeparator||".";typeof i=="string"?this._filter=p.stringFilter(i,r):typeof i=="function"?this._filter=i:i instanceof RegExp&&(this._filter=p.regExpFilter(i,r));let n=t&&t.replacement;typeof n=="function"?this._replacement=n:this._replacement=p.arrayReplacement(n||p.defaultReplacement),this._allowEmptyReplacement=t&&t.allowEmptyReplacement,this._streamKeys=!0,t&&("streamValues"in t&&(this._streamKeys=t.streamValues),"streamKeys"in t&&(this._streamKeys=t.streamKeys)),this._once=t&&t.once,this._previousToken=""}_check(t,i,r){switch(t.name){case"startObject":case"startArray":case"startString":case"startNumber":case"nullValue":case"trueValue":case"falseValue":typeof this._stack[this._stack.length-1]=="number"&&++this._stack[this._stack.length-1];break;case"keyValue":this._stack[this._stack.length-1]=t.value;break;case"numberValue":this._previousToken!=="endNumber"&&typeof this._stack[this._stack.length-1]=="number"&&++this._stack[this._stack.length-1];break;case"stringValue":this._previousToken!=="endString"&&typeof this._stack[this._stack.length-1]=="number"&&++this._stack[this._stack.length-1];break}if(this._previousToken=t.name,this._checkChunk(t))return r(null);switch(t.name){case"startObject":this._stack.push(null);break;case"startArray":this._stack.push(-1);break;case"endObject":case"endArray":this._stack.pop();break}r(null)}_passObject(t,i,r){switch(this.push(t),t.name){case"startObject":case"startArray":++this._depth;break;case"endObject":case"endArray":--this._depth;break}this._depth||(this._transform=this._once?this._skip:this._check),r(null)}_pass(t,i,r){this.push(t),r(null)}_skipObject(t,i,r){switch(t.name){case"startObject":case"startArray":++this._depth;break;case"endObject":case"endArray":--this._depth;break}this._depth||(this._transform=this._once?this._pass:this._check),r(null)}_skip(t,i,r){r(null)}};qe.defaultReplacement=[{name:"nullValue",value:null}];var a6=(p,t)=>function(i,r,n){if(this._expected){let u=this._expected;if(this._expected="",this._transform=this._once?this._skip:this._check,u===i.name)this.push(i);else return this._transform(i,r,n)}else this.push(i),i.name===p&&(this._expected=t);n(null)};qe.prototype._passNumber=a6("endNumber","numberValue");qe.prototype._passString=a6("endString","stringValue");qe.prototype._passKey=a6("endKey","keyValue");var p6=(p,t)=>function(i,r,n){if(this._expected){let u=this._expected;if(this._expected="",this._transform=this._once?this._pass:this._check,u!==i.name)return this._transform(i,r,n)}else i.name===p&&(this._expected=t);n(null)};qe.prototype._skipNumber=p6("endNumber","numberValue");qe.prototype._skipString=p6("endString","stringValue");qe.prototype._skipKey=p6("endKey","keyValue");A9.exports=qe});var t6=I((cv,V9)=>{"use strict";var ef=J0(),af=Q0(),pf=(p,t)=>new ef([new af(t),p(t)],Object.assign({},t,{writableObjectMode:!1,readableObjectMode:!0}));V9.exports=pf});var P9=I((fv,C9)=>{"use strict";var tf=E9(),df=t6(),$a=class p extends tf{static make(t){return new p(t)}static withParser(t){return df(p.make,t)}_checkChunk(t){switch(t.name){case"startObject":case"startArray":if(this._filter(this._stack,t))return this.push(t),this._transform=this._passObject,this._depth=1,!0;break;case"startString":if(this._filter(this._stack,t))return this.push(t),this._transform=this._passString,!0;break;case"startNumber":if(this._filter(this._stack,t))return this.push(t),this._transform=this._passNumber,!0;break;case"nullValue":case"trueValue":case"falseValue":case"stringValue":case"numberValue":if(this._filter(this._stack,t))return this.push(t),this._transform=this._once?this._skip:this._check,!0;break}return!1}};$a.pick=$a.make;$a.make.Constructor=$a;C9.exports=$a});var O9=I((hv,R9)=>{"use strict";var rf=require("events"),k9=p=>function(){this.done?this.done=!1:this.stack.push(this.current,this.key),this.current=new p,this.key=null},ia=class p extends rf{static connectTo(t,i){return new p(i).connectTo(t)}constructor(t){super(),this.stack=[],this.current=this.key=null,this.done=!0,t&&(this.reviver=typeof t.reviver=="function"&&t.reviver,this.reviver&&(this.stringValue=this._saveValue=this._saveValueWithReviver),t.numberAsString&&(this.numberValue=this.stringValue))}connectTo(t){return t.on("data",i=>{this[i.name]&&(this[i.name](i.value),this.done&&this.emit("done",this))}),this}get depth(){return(this.stack.length>>1)+(this.done?0:1)}get path(){let t=[];for(let i=0;i<this.stack.length;i+=2){let r=this.stack[i+1];t.push(r===null?this.stack[i].length:r)}return t}dropToLevel(t){if(t<this.depth)if(t){let i=t-1<<1;this.current=this.stack[i],this.key=this.stack[i+1],this.stack.splice(i)}else this.stack=[],this.current=this.key=null,this.done=!0;return this}consume(t){return this[t.name]&&this[t.name](t.value),this}keyValue(t){this.key=t}numberValue(t){this._saveValue(parseFloat(t))}nullValue(){this._saveValue(null)}trueValue(){this._saveValue(!0)}falseValue(){this._saveValue(!1)}endObject(){if(this.stack.length){let t=this.current;this.key=this.stack.pop(),this.current=this.stack.pop(),this._saveValue(t)}else this.done=!0}_saveValue(t){this.done?this.current=t:this.current instanceof Array?this.current.push(t):(this.current[this.key]=t,this.key=null)}_saveValueWithReviver(t){this.done?this.current=this.reviver("",t):this.current instanceof Array?(t=this.reviver(""+this.current.length,t),this.current.push(t),t===void 0&&delete this.current[this.current.length-1]):(t=this.reviver(this.key,t),t!==void 0&&(this.current[this.key]=t),this.key=null)}};ia.prototype.stringValue=ia.prototype._saveValue;ia.prototype.startObject=k9(Object);ia.prototype.startArray=k9(Array);ia.prototype.endArray=ia.prototype.endObject;R9.exports=ia});var I9=I((vv,L9)=>{"use strict";var{Transform:nf}=require("stream"),sf=O9(),d6=class{constructor(t){this.depth=t}startObject(){++this.depth}endObject(){--this.depth}startArray(){++this.depth}endArray(){--this.depth}},i6=class extends nf{constructor(t){super(Object.assign({},t,{writableObjectMode:!0,readableObjectMode:!0})),t&&(this.objectFilter=t.objectFilter,this.includeUndecided=t.includeUndecided),typeof this.objectFilter!="function"&&(this._filter=this._transform),this._transform=this._wait||this._filter,this._assembler=new sf(t)}_transform(t,i,r){this._assembler[t.name]&&(this._assembler[t.name](t.value),this._assembler.depth===this._level&&this._push()),r(null)}_filter(t,i,r){if(this._assembler[t.name]){this._assembler[t.name](t.value);let n=this.objectFilter(this._assembler);if(n)return this._assembler.depth===this._level&&(this._push(),this._transform=this._filter),this._transform=this._accept,r(null);if(n===!1)return this._saved_assembler=this._assembler,this._assembler=new d6(this._saved_assembler.depth),this._saved_assembler.dropToLevel(this._level),this._assembler.depth===this._level&&(this._assembler=this._saved_assembler,this._transform=this._filter),this._transform=this._reject,r(null);this._assembler.depth===this._level&&this._push(!this.includeUndecided)}r(null)}_accept(t,i,r){this._assembler[t.name]&&(this._assembler[t.name](t.value),this._assembler.depth===this._level&&(this._push(),this._transform=this._filter)),r(null)}_reject(t,i,r){this._assembler[t.name]&&(this._assembler[t.name](t.value),this._assembler.depth===this._level&&(this._assembler=this._saved_assembler,this._transform=this._filter)),r(null)}};L9.exports=i6});var B9=I((_v,F9)=>{"use strict";var lf=I9(),mf=t6(),Wa=class p extends lf{static make(t){return new p(t)}static withParser(t){return mf(p.make,t)}constructor(t){super(t),this._level=1,this._counter=0}_wait(t,i,r){return t.name!=="startArray"?r(new Error("Top-level object should be an array.")):(this._transform=this._filter,this._transform(t,i,r))}_push(t){this._assembler.current.length&&(t?(++this._counter,this._assembler.current.pop()):this.push({key:this._counter++,value:this._assembler.current.pop()}))}};Wa.streamArray=Wa.make;Wa.make.Constructor=Wa;F9.exports=Wa});var z9=I(Tp=>{"use strict";var Ft=Tp&&Tp.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(Tp,"__esModule",{value:!0});Tp.useStreamJSON=void 0;var of=require("@raycast/api"),U9=Ft(Et()),g2=require("node:fs"),r6=require("node:fs/promises"),It=require("node:path"),M9=require("node:stream/promises"),_2=require("react"),uf=Ft(J0()),cf=D9(),ff=Ft(P9()),hf=Ft(B9()),q9=M0(),vf=n2(),_f=Fa();async function w2(p,t,i){if(typeof p=="object"||p.startsWith("http://")||p.startsWith("https://"))return await wf(p,t,i);if(p.startsWith("file://"))return await gf((0,It.normalize)(decodeURIComponent(new URL(p).pathname)),t,i?.signal?i.signal:void 0);throw new Error("Only HTTP(S) or file URLs are supported")}async function wf(p,t,i){let r=await(0,U9.default)(p,i);if(!r.ok)throw new Error("Failed to fetch URL");if(!(0,q9.isJSON)(r.headers.get("content-type")))throw new Error("URL does not return JSON");if(!r.body)throw new Error("Failed to retrieve expected JSON content: Response body is missing or inaccessible.");await(0,M9.pipeline)(r.body,(0,g2.createWriteStream)(t),i?.signal?{signal:i.signal}:void 0)}async function gf(p,t,i){await(0,M9.pipeline)((0,g2.createReadStream)(p),(0,g2.createWriteStream)(t),i?{signal:i}:void 0)}async function bf(p,t,i,r,n){let u=(0,It.join)(t,i);try{await(0,r6.stat)(t)}catch{(0,g2.mkdirSync)(t,{recursive:!0}),await w2(p,u,n);return}if(r){await w2(p,u,n);return}let v;try{v=await(0,r6.stat)(u)}catch{await w2(p,u,n);return}if(typeof p=="object"||p.startsWith("http://")||p.startsWith("https://")){let _=await(0,U9.default)(p,{...n,method:"HEAD"});if(!_.ok)throw new Error("Could not fetch URL");if(!(0,q9.isJSON)(_.headers.get("content-type")))throw new Error("URL does not return JSON");let f=Date.parse(_.headers.get("last-modified")??"");if(v.size===0||Number.isNaN(f)||f>v.mtimeMs){await w2(p,u,n);return}}else if(p.startsWith("file://"))try{(await(0,r6.stat)((0,It.normalize)(decodeURIComponent(new URL(p).pathname)))).mtimeMs>v.mtimeMs&&await w2(p,u,n)}catch{throw new Error("Source file could not be read")}else throw new Error("Only HTTP(S) or file URLs are supported")}async function*yf(p,t,i,r,n,u){let v=[],_=new uf.default([(0,g2.createReadStream)(p),r?ff.default.withParser({filter:r}):(0,cf.parser)(),new hf.default,f=>u?.(f.value)??f.value]);i?.addEventListener("abort",()=>{_.destroy()});try{for await(let f of _){if(i?.aborted)return[];(!n||n(f))&&v.push(f),v.length>=t&&(yield v,v=[])}}catch(f){throw _.destroy(),f}return v.length>0&&(yield v),[]}function Sf(p,t){let{initialData:i,execute:r,keepPreviousData:n,onError:u,onData:v,onWillExecute:_,failureToastOptions:f,dataPath:N,filter:A,transform:D,pageSize:V=20,...F}=t??{},C=(0,_2.useRef)(),R=(0,_2.useRef)(),q={initialData:i,execute:r,keepPreviousData:n,onError:u,onData:v,onWillExecute:_,failureToastOptions:f},P=(0,_2.useRef)(null),L=(0,_2.useRef)(null),B=(0,_2.useRef)(!1);return(0,vf.useCachedPromise)((U,H,e1,N1,F1,Q1)=>async({page:R1})=>{let T1=(0,_f.hash)(U)+".json",H1=of.environment.supportPath;if(R1===0){L.current?.abort(),L.current=new AbortController;let w1=(0,It.join)(H1,T1),G1=!!(C.current&&C.current!==U&&R.current&&R.current===w1);C.current=U,R.current=w1,await bf(U,H1,T1,G1,{...e1,signal:L.current?.signal}),P.current=yf(w1,H,L.current?.signal,N1,F1,Q1)}if(!P.current)return{hasMore:B.current,data:[]};let{value:ne,done:a1}=await P.current.next();return B.current=!a1,{hasMore:B.current,data:ne??[]}},[p,V,F,N,A,D],q)}Tp.useStreamJSON=Sf});var s6=I(ze=>{"use strict";var n6=ze&&ze.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(ze,"__esModule",{value:!0});ze.baseExecuteSQL=ze.isPermissionError=ze.PermissionError=void 0;var Nf=require("node:fs"),Bt=require("node:fs/promises"),Tf=n6(require("node:os")),$9=n6(require("node:child_process")),W9=n6(require("node:path")),Ut=Lt(),xf=Fa(),qt=class extends Error{constructor(t){super(t),this.name="PermissionError"}};ze.PermissionError=qt;function Df(p){return p instanceof Error&&p.name==="PermissionError"}ze.isPermissionError=Df;async function Af(p,t,i){if(!(0,Nf.existsSync)(p))throw new Error("The database does not exist");let r=i?.signal,n,u=$9.default.spawn("sqlite3",["--json","--readonly",p,t],{signal:r}),v=(0,Ut.getSpawnedPromise)(u),[{error:_,exitCode:f,signal:N},A,D]=await(0,Ut.getSpawnedResult)(u,{encoding:"utf-8"},v);if(Mt(r),D.match("(5)")||D.match("(14)")){if(!n){let V=W9.default.join(Tf.default.tmpdir(),"useSQL",(0,xf.hash)(p));await(0,Bt.mkdir)(V,{recursive:!0}),Mt(r),n=W9.default.join(V,"db.db"),await(0,Bt.copyFile)(p,n),await(0,Bt.writeFile)(n+"-shm",""),await(0,Bt.writeFile)(n+"-wal",""),Mt(r)}u=$9.default.spawn("sqlite3",["--json","--readonly","--vfs","unix-none",n,t],{signal:r}),v=(0,Ut.getSpawnedPromise)(u),[{error:_,exitCode:f,signal:N},A,D]=await(0,Ut.getSpawnedResult)(u,{encoding:"utf-8"},v),Mt(r)}if(_||f!==0||N!==null)throw D.includes("authorization denied")?new qt("You do not have permission to access the database."):new Error(D||"Unknown error");return JSON.parse(A.trim()||"[]")}ze.baseExecuteSQL=Af;function Mt(p){if(p?.aborted){let t=new Error("aborted");throw t.name="AbortError",t}}});var H9=I(xp=>{"use strict";var Ef=xp&&xp.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(xp,"__esModule",{value:!0});xp.useSQL=void 0;var $e=require("react/jsx-runtime"),W1=require("@raycast/api"),Vf=require("node:fs"),Cf=Ef(require("node:os")),zt=require("react"),Pf=cp(),kf=aa(),Rf=t2(),l6=s6();function Of(p,t,i){let{permissionPriming:r,...n}=i||{},[u,v]=(0,zt.useState)(),_=(0,kf.useLatest)(i||{}),f=(0,zt.useRef)(),N=(0,zt.useCallback)(D=>{console.error(D);let V=D instanceof Error&&D.message.includes("authorization denied")?new l6.PermissionError("You do not have permission to access the database."):D;(0,l6.isPermissionError)(V)?v((0,$e.jsx)(Lf,{priming:_.current.permissionPriming})):_.current.onError?_.current.onError(V):W1.environment.launchType!==W1.LaunchType.Background&&(0,Rf.showFailureToast)(V,{title:"Cannot query the data"})},[_]),A=(0,zt.useMemo)(()=>{if(!(0,Vf.existsSync)(p))throw new Error("The database does not exist");return async(D,V)=>{let F=f.current?.signal;return(0,l6.baseExecuteSQL)(D,V,{signal:F})}},[p]);return{...(0,Pf.usePromise)(A,[p,t],{...n,onError:N}),permissionView:u}}xp.useSQL=Of;var j9=parseInt(Cf.default.release().split(".")[0])>=22,m6=j9?"Settings":"Preferences";function Lf(p){let t=j9?{title:"Open System Settings -> Privacy",target:"x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles"}:{title:"Open System Preferences -> Security",target:"x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles"};return W1.environment.commandMode==="menu-bar"?(0,$e.jsxs)(W1.MenuBarExtra,{icon:W1.Icon.Warning,title:W1.environment.commandName,children:[(0,$e.jsx)(W1.MenuBarExtra.Item,{title:"Raycast needs full disk access",tooltip:`You can revert this access in ${m6} whenever you want`}),p.priming?(0,$e.jsx)(W1.MenuBarExtra.Item,{title:p.priming,tooltip:`You can revert this access in ${m6} whenever you want`}):null,(0,$e.jsx)(W1.MenuBarExtra.Separator,{}),(0,$e.jsx)(W1.MenuBarExtra.Item,{title:t.title,onAction:()=>(0,W1.open)(t.target)})]}):(0,$e.jsx)(W1.List,{children:(0,$e.jsx)(W1.List.EmptyView,{icon:{source:{light:"https://raycast.com/uploads/extensions-utils-security-permissions-light.png",dark:"https://raycast.com/uploads/extensions-utils-security-permissions-dark.png"}},title:"Raycast needs full disk access.",description:`${p.priming?p.priming+`
`:""}You can revert this access in ${m6} whenever you want.`,actions:(0,$e.jsx)(W1.ActionPanel,{children:(0,$e.jsx)(W1.Action.Open,{...t})})})})}});var K9=I(Dp=>{"use strict";Object.defineProperty(Dp,"__esModule",{value:!0});Dp.useForm=Dp.FormValidation=void 0;var ra=require("react"),G9=aa(),u6;(function(p){p.Required="required"})(u6||(Dp.FormValidation=u6={}));function o6(p,t){if(p){if(typeof p=="function")return p(t);if(p===u6.Required){let i=typeof t<"u"&&t!==null;if(i)switch(typeof t){case"string":i=t.length>0;break;case"object":Array.isArray(t)?i=t.length>0:t instanceof Date&&(i=t.getTime()>0);break;default:break}if(!i)return"The item is required"}}}function If(p){let{onSubmit:t,validation:i,initialValues:r={}}=p,[n,u]=(0,ra.useState)(r),[v,_]=(0,ra.useState)({}),f=(0,ra.useRef)({}),N=(0,G9.useLatest)(i||{}),A=(0,G9.useLatest)(t),D=(0,ra.useCallback)(P=>{f.current[P]?.focus()},[f]),V=(0,ra.useCallback)(async P=>{let L=!1;for(let[U,H]of Object.entries(N.current)){let e1=o6(H,P[U]);e1&&(L||(L={},D(U)),L[U]=e1)}if(L)return _(L),!1;let B=await A.current(P);return typeof B=="boolean"?B:!0},[N,A,D]),F=(0,ra.useCallback)((P,L)=>{_(B=>({...B,[P]:L}))},[_]),C=(0,ra.useCallback)(function(P,L){u(B=>({...B,[P]:typeof L=="function"?L(B[P]):L}))},[u]),R=(0,ra.useMemo)(()=>new Proxy({},{get(P,L){let B=N.current[L],U=n[L];return{onChange(H){v[L]&&(o6(B,H)||F(L,void 0)),C(L,H)},onBlur(H){let e1=o6(B,H.target.value);e1&&F(L,e1)},error:v[L],id:L,value:typeof U>"u"?null:U,ref:H=>{f.current[L]=H}}}}),[v,N,F,n,f,C]),q=(0,ra.useCallback)(P=>{_({}),Object.entries(f.current).forEach(([L,B])=>{P?.[L]||B?.reset()}),P&&u(P)},[u,_,f]);return{handleSubmit:V,setValidationError:F,setValue:C,values:n,itemProps:R,focus:D,reset:q}}Dp.useForm=If});var J9=I($t=>{"use strict";Object.defineProperty($t,"__esModule",{value:!0});$t.useAI=void 0;var Z9=require("react"),Ff=require("@raycast/api"),Bf=cp();function Uf(p,t={}){let{creativity:i,stream:r,model:n,...u}=t,[v,_]=(0,Z9.useState)(""),f=(0,Z9.useRef)(),{isLoading:N,error:A,revalidate:D}=(0,Bf.usePromise)(async(V,F,C)=>{_("");let R=Ff.AI.ask(V,{creativity:F,model:n,signal:f.current?.signal});C===!1?_(await R):(R.on("data",q=>{_(P=>P+q)}),await R)},[p,i,r],{...u,abortable:f});return{isLoading:N,data:v,error:A,revalidate:D}}$t.useAI=Uf});var X9=I(Wt=>{"use strict";Object.defineProperty(Wt,"__esModule",{value:!0});Wt.useFrecencySorting=void 0;var c6=require("react"),Y9=aa(),Mf=bt(),qf=10,Q9=24*60*60*1e3,zf=Math.log(2)/(qf*Q9),$f={Default:100,Embed:0,Bookmark:140};function Wf(p){let t=Date.now(),i=p?p.lastVisited:0,r=p?p.frecency:0,n=(t-i)/Q9,u=$f.Default*Math.exp(-zf*n),v=r+u;return{lastVisited:t,frecency:v}}var jf=p=>{if(process.env.NODE_ENV!=="production"&&(typeof p!="object"||!p||!("id"in p)||typeof p.id!="string"))throw new Error("Specify a key function or make sure your items have an 'id' property");return p.id};function Hf(p,t){let i=(0,Y9.useLatest)(t?.key||jf),r=(0,Y9.useLatest)(t?.sortUnvisited),[n,u]=(0,Mf.useCachedState)(`raycast_frecency_${t?.namespace}`,{}),v=(0,c6.useCallback)(async function(A){let D=i.current(A);u(V=>{let F=V[D],C=Wf(F);return{...V,[D]:C}})},[i,u]),_=(0,c6.useCallback)(async function(A){let D=i.current(A);u(V=>{let F={...V};return delete F[D],F})},[i,u]);return{data:(0,c6.useMemo)(()=>p?p.sort((N,A)=>{let D=n[i.current(N)],V=n[i.current(A)];return D&&!V?-1:!D&&V?1:D&&V?V.frecency-D.frecency:r.current?r.current(N,A):0}):[],[n,p,i,r]),visitItem:v,resetRanking:_}}Wt.useFrecencySorting=Hf});var p3=I(jt=>{"use strict";Object.defineProperty(jt,"__esModule",{value:!0});jt.useLocalStorage=void 0;var f6=require("@raycast/api"),e3=t2(),a3=Fa(),Gf=cp();function Kf(p,t){let{data:i,isLoading:r,mutate:n}=(0,Gf.usePromise)(async _=>{let f=await f6.LocalStorage.getItem(_);return typeof f<"u"?JSON.parse(f,a3.reviver):t},[p]);async function u(_){try{await n(f6.LocalStorage.setItem(p,JSON.stringify(_,a3.replacer)),{optimisticUpdate(f){return f}})}catch(f){await(0,e3.showFailureToast)(f,{title:"Failed to set value in local storage"})}}async function v(){try{await n(f6.LocalStorage.removeItem(p),{optimisticUpdate(){}})}catch(_){await(0,e3.showFailureToast)(_,{title:"Failed to remove value from local storage"})}}return{value:i,setValue:u,removeValue:v,isLoading:r}}jt.useLocalStorage=Kf});var n3=I(Ap=>{"use strict";Object.defineProperty(Ap,"__esModule",{value:!0});Ap.slightlyLighterColor=Ap.slightlyDarkerColor=void 0;function Zf(p){let t=0,i=0,r=0;if(p.length===4)t=parseInt(`${p[1]}${p[1]}`,16),i=parseInt(`${p[2]}${p[2]}`,16),r=parseInt(`${p[3]}${p[3]}`,16);else if(p.length===7)t=parseInt(`${p[1]}${p[2]}`,16),i=parseInt(`${p[3]}${p[4]}`,16),r=parseInt(`${p[5]}${p[6]}`,16);else throw new Error(`Malformed hex color: ${p}`);return{r:t,g:i,b:r}}function Jf({r:p,g:t,b:i}){let r=p.toString(16),n=t.toString(16),u=i.toString(16);return r.length===1&&(r=`0${r}`),n.length===1&&(n=`0${n}`),u.length===1&&(u=`0${u}`),`#${r}${n}${u}`}function Yf({r:p,g:t,b:i}){p/=255,t/=255,i/=255;let r=Math.min(p,t,i),n=Math.max(p,t,i),u=n-r,v=0,_=0,f=0;return u===0?v=0:n===p?v=(t-i)/u%6:n===t?v=(i-p)/u+2:v=(p-t)/u+4,v=Math.round(v*60),v<0&&(v+=360),f=(n+r)/2,_=u===0?0:u/(1-Math.abs(2*f-1)),_=+(_*100).toFixed(1),f=+(f*100).toFixed(1),{h:v,s:_,l:f}}function Qf({h:p,s:t,l:i}){t/=100,i/=100;let r=(1-Math.abs(2*i-1))*t,n=r*(1-Math.abs(p/60%2-1)),u=i-r/2,v=0,_=0,f=0;return p>=0&&p<60?(v=r,_=n,f=0):p>=60&&p<120?(v=n,_=r,f=0):p>=120&&p<180?(v=0,_=r,f=n):p>=180&&p<240?(v=0,_=n,f=r):p>=240&&p<300?(v=n,_=0,f=r):p>=300&&p<360&&(v=r,_=0,f=n),v=Math.round((v+u)*255),_=Math.round((_+u)*255),f=Math.round((f+u)*255),{r:v,g:_,b:f}}function t3(p){return Yf(Zf(p))}function d3(p){return Jf(Qf(p))}function i3(p,t,i){return t<i?p<t?t:p>i?i:p:p<i?i:p>t?t:p}var r3=12;function Xf(p){let t=t3(p);return d3({h:t.h,s:t.s,l:i3(t.l-r3,0,100)})}Ap.slightlyDarkerColor=Xf;function eh(p){let t=t3(p);return d3({h:t.h,s:t.s,l:i3(t.l+r3,0,100)})}Ap.slightlyLighterColor=eh});var m3=I(Ht=>{"use strict";Object.defineProperty(Ht,"__esModule",{value:!0});Ht.getAvatarIcon=void 0;var s3=n3();function Ep(p,t){let i=p.charCodeAt(t);if(Number.isNaN(i))return["",t];if(i<55296||i>57343)return[p.charAt(t),t];if(55296<=i&&i<=56319){if(p.length<=t+1)throw new Error("High surrogate without following low surrogate");let n=p.charCodeAt(t+1);if(56320>n||n>57343)throw new Error("High surrogate without following low surrogate");return[p.charAt(t)+p.charAt(t+1),t+1]}if(t===0)throw new Error("Low surrogate without preceding high surrogate");let r=p.charCodeAt(t-1);if(55296>r||r>56319)throw new Error("Low surrogate without preceding high surrogate");return[p.charAt(t+1),t+1]}var l3=["#DC829A","#D64854","#D47600","#D36CDD","#52A9E4","#7871E8","#70920F","#43B93A","#EB6B3E","#26B795","#D85A9B","#A067DC","#BD9500","#5385D9"];function ah(p,t){let i=p.trim().split(" "),r;if(i.length==1&&Ep(i[0],0)[0])r=Ep(i[0],0)[0];else if(i.length>1){let f=Ep(i[0],0)[0]||"",N=Ep(i[i.length-1],0)[0]??"";r=f+N}else r="";let n;if(t?.background)n=t?.background;else{let f=0,[N,A]=Ep(r,0);for(;N;)f+=N.charCodeAt(0),[N,A]=Ep(r,A+1);let D=f%l3.length;n=l3[D]}let v=50-0;return`data:image/svg+xml,${`<svg width="100px" height="100px">
  ${t?.gradient!==!1?`<defs>
      <linearGradient id="Gradient" x1="0.25" x2="0.75" y1="0" y2="1">
        <stop offset="0%" stop-color="${(0,s3.slightlyLighterColor)(n)}"/>
        <stop offset="50%" stop-color="${n}"/>
        <stop offset="100%" stop-color="${(0,s3.slightlyDarkerColor)(n)}"/>
      </linearGradient>
  </defs>`:""}
      <circle cx="50" cy="50" r="${v}" fill="${t?.gradient!==!1?"url(#Gradient)":n}" />
      ${r?`<text x="50" y="80" font-size="${v-1}" font-family="Inter, sans-serif" text-anchor="middle" fill="white">${r.toUpperCase()}</text>`:""}
    </svg>
  `.replaceAll(`
`,"")}`}Ht.getAvatarIcon=ah});var u3=I(Gt=>{"use strict";Object.defineProperty(Gt,"__esModule",{value:!0});Gt.getFavicon=void 0;var o3=require("@raycast/api"),ph=require("url");function th(p,t){try{let r=(typeof p=="string"?new ph.URL(p):p).hostname;return{source:`https://www.google.com/s2/favicons?sz=${t?.size??64}&domain=${r}`,fallback:t?.fallback??o3.Icon.Link,mask:t?.mask}}catch(i){return console.error(i),o3.Icon.Link}}Gt.getFavicon=th});var h3=I(Kt=>{"use strict";Object.defineProperty(Kt,"__esModule",{value:!0});Kt.getProgressIcon=void 0;var c3=require("@raycast/api");function f3(p,t,i,r){let n=(r-90)*Math.PI/180;return{x:p+i*Math.cos(n),y:t+i*Math.sin(n)}}function dh(p,t,i,r,n){let u=f3(p,t,i,n),v=f3(p,t,i,r),_=n-r<=180?"0":"1";return["M",u.x,u.y,"A",i,i,0,_,0,v.x,v.y].join(" ")}function ih(p,t=c3.Color.Red,i){let r=i?.background||(c3.environment.appearance==="light"?"black":"white"),n=i?.backgroundOpacity||.1,u=10,_=50-5-u/2;return`data:image/svg+xml,${`<svg width="100px" height="100px">
      <circle cx="50" cy="50" r="${_}" stroke-width="${u}" stroke="${p<1?r:t}" opacity="${p<1?n:"1"}" fill="none" />
      ${p>0&&p<1?`<path d="${dh(50,50,_,0,p*360)}" stroke="${t}" stroke-width="${u}" fill="none" />`:""}
    </svg>
  `.replaceAll(`
`,"")}`}Kt.getProgressIcon=ih});var v3=I(na=>{"use strict";var rh=na&&na.__createBinding||(Object.create?function(p,t,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(t,i);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(p,r,n)}:function(p,t,i,r){r===void 0&&(r=i),p[r]=t[i]}),h6=na&&na.__exportStar||function(p,t){for(var i in p)i!=="default"&&!Object.prototype.hasOwnProperty.call(t,i)&&rh(t,p,i)};Object.defineProperty(na,"__esModule",{value:!0});h6(m3(),na);h6(u3(),na);h6(h3(),na)});var _3=I(M1=>{"use strict";Object.defineProperty(M1,"__esModule",{value:!0});M1.zoomService=M1.slackService=M1.linearService=M1.jiraService=M1.googleService=M1.githubService=M1.asanaService=void 0;var j1=require("@raycast/api"),ja=v6(),Zt={asana:"1191201745684312",github:"7235fe8d42157f1f38c0",linear:"c8ff37b9225c3c9aefd7d66ea0e5b6f1",slack:"851756884692.5546927290212"},Ta=p=>`data:image/svg+xml,${p}`,Ha={asana:Ta('<svg xmlns="http://www.w3.org/2000/svg" width="251" height="232" fill="none"><path fill="#F06A6A" d="M179.383 54.373c0 30.017-24.337 54.374-54.354 54.374-30.035 0-54.373-24.338-54.373-54.374C70.656 24.338 94.993 0 125.029 0c30.017 0 54.354 24.338 54.354 54.373ZM54.393 122.33C24.376 122.33.02 146.668.02 176.685c0 30.017 24.337 54.373 54.373 54.373 30.035 0 54.373-24.338 54.373-54.373 0-30.017-24.338-54.355-54.373-54.355Zm141.253 0c-30.035 0-54.373 24.338-54.373 54.374 0 30.035 24.338 54.373 54.373 54.373 30.017 0 54.374-24.338 54.374-54.373 0-30.036-24.338-54.374-54.374-54.374Z"/></svg>'),github:{source:Ta('<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z"/></svg>'),tintColor:j1.Color.PrimaryText},google:Ta('<svg xmlns="http://www.w3.org/2000/svg" style="display:block" viewBox="0 0 48 48"><path fill="#EA4335" d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"/><path fill="#4285F4" d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"/><path fill="#FBBC05" d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"/><path fill="#34A853" d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"/><path fill="none" d="M0 0h48v48H0z"/></svg>'),jira:Ta('<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="2361" height="2500" viewBox="2.59 0 214.091 224"><linearGradient id="a" x1="102.4" x2="56.15" y1="218.63" y2="172.39" gradientTransform="matrix(1 0 0 -1 0 264)" gradientUnits="userSpaceOnUse"><stop offset=".18" stop-color="#0052cc"/><stop offset="1" stop-color="#2684ff"/></linearGradient><linearGradient xlink:href="#a" id="b" x1="114.65" x2="160.81" y1="85.77" y2="131.92"/><path fill="#2684ff" d="M214.06 105.73 117.67 9.34 108.33 0 35.77 72.56 2.59 105.73a8.89 8.89 0 0 0 0 12.54l66.29 66.29L108.33 224l72.55-72.56 1.13-1.12 32.05-32a8.87 8.87 0 0 0 0-12.59zm-105.73 39.39L75.21 112l33.12-33.12L141.44 112z"/><path fill="url(#a)" d="M108.33 78.88a55.75 55.75 0 0 1-.24-78.61L35.62 72.71l39.44 39.44z"/><path fill="url(#b)" d="m141.53 111.91-33.2 33.21a55.77 55.77 0 0 1 0 78.86L181 151.35z"/></svg>'),linear:{source:{light:Ta('<svg xmlns="http://www.w3.org/2000/svg" fill="#222326" width="200" height="200" viewBox="0 0 100 100"><path d="M1.22541 61.5228c-.2225-.9485.90748-1.5459 1.59638-.857L39.3342 97.1782c.6889.6889.0915 1.8189-.857 1.5964C20.0515 94.4522 5.54779 79.9485 1.22541 61.5228ZM.00189135 46.8891c-.01764375.2833.08887215.5599.28957165.7606L52.3503 99.7085c.2007.2007.4773.3075.7606.2896 2.3692-.1476 4.6938-.46 6.9624-.9259.7645-.157 1.0301-1.0963.4782-1.6481L2.57595 39.4485c-.55186-.5519-1.49117-.2863-1.648174.4782-.465915 2.2686-.77832 4.5932-.92588465 6.9624ZM4.21093 29.7054c-.16649.3738-.08169.8106.20765 1.1l64.77602 64.776c.2894.2894.7262.3742 1.1.2077 1.7861-.7956 3.5171-1.6927 5.1855-2.684.5521-.328.6373-1.0867.1832-1.5407L8.43566 24.3367c-.45409-.4541-1.21271-.3689-1.54074.1832-.99132 1.6684-1.88843 3.3994-2.68399 5.1855ZM12.6587 18.074c-.3701-.3701-.393-.9637-.0443-1.3541C21.7795 6.45931 35.1114 0 49.9519 0 77.5927 0 100 22.4073 100 50.0481c0 14.8405-6.4593 28.1724-16.7199 37.3375-.3903.3487-.984.3258-1.3542-.0443L12.6587 18.074Z"/></svg>'),dark:Ta('<svg xmlns="http://www.w3.org/2000/svg" fill="#fff" width="200" height="200" viewBox="0 0 100 100"><path d="M1.22541 61.5228c-.2225-.9485.90748-1.5459 1.59638-.857L39.3342 97.1782c.6889.6889.0915 1.8189-.857 1.5964C20.0515 94.4522 5.54779 79.9485 1.22541 61.5228ZM.00189135 46.8891c-.01764375.2833.08887215.5599.28957165.7606L52.3503 99.7085c.2007.2007.4773.3075.7606.2896 2.3692-.1476 4.6938-.46 6.9624-.9259.7645-.157 1.0301-1.0963.4782-1.6481L2.57595 39.4485c-.55186-.5519-1.49117-.2863-1.648174.4782-.465915 2.2686-.77832 4.5932-.92588465 6.9624ZM4.21093 29.7054c-.16649.3738-.08169.8106.20765 1.1l64.77602 64.776c.2894.2894.7262.3742 1.1.2077 1.7861-.7956 3.5171-1.6927 5.1855-2.684.5521-.328.6373-1.0867.1832-1.5407L8.43566 24.3367c-.45409-.4541-1.21271-.3689-1.54074.1832-.99132 1.6684-1.88843 3.3994-2.68399 5.1855ZM12.6587 18.074c-.3701-.3701-.393-.9637-.0443-1.3541C21.7795 6.45931 35.1114 0 49.9519 0 77.5927 0 100 22.4073 100 50.0481c0 14.8405-6.4593 28.1724-16.7199 37.3375-.3903.3487-.984.3258-1.3542-.0443L12.6587 18.074Z" /></svg>')}},slack:Ta('<svg xmlns="http://www.w3.org/2000/svg" viewBox="73 73 124 124"><style>.st0{fill:#e01e5a}.st1{fill:#36c5f0}.st2{fill:#2eb67d}.st3{fill:#ecb22e}</style><path d="M99.4 151.2c0 7.1-5.8 12.9-12.9 12.9-7.1 0-12.9-5.8-12.9-12.9 0-7.1 5.8-12.9 12.9-12.9h12.9v12.9zM105.9 151.2c0-7.1 5.8-12.9 12.9-12.9s12.9 5.8 12.9 12.9v32.3c0 7.1-5.8 12.9-12.9 12.9s-12.9-5.8-12.9-12.9v-32.3z" class="st0"/><path d="M118.8 99.4c-7.1 0-12.9-5.8-12.9-12.9 0-7.1 5.8-12.9 12.9-12.9s12.9 5.8 12.9 12.9v12.9h-12.9zM118.8 105.9c7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9H86.5c-7.1 0-12.9-5.8-12.9-12.9s5.8-12.9 12.9-12.9h32.3z" class="st1"/><path d="M170.6 118.8c0-7.1 5.8-12.9 12.9-12.9 7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9h-12.9v-12.9zM164.1 118.8c0 7.1-5.8 12.9-12.9 12.9-7.1 0-12.9-5.8-12.9-12.9V86.5c0-7.1 5.8-12.9 12.9-12.9 7.1 0 12.9 5.8 12.9 12.9v32.3z" class="st2"/><path d="M151.2 170.6c7.1 0 12.9 5.8 12.9 12.9 0 7.1-5.8 12.9-12.9 12.9-7.1 0-12.9-5.8-12.9-12.9v-12.9h12.9zM151.2 164.1c-7.1 0-12.9-5.8-12.9-12.9 0-7.1 5.8-12.9 12.9-12.9h32.3c7.1 0 12.9 5.8 12.9 12.9 0 7.1-5.8 12.9-12.9 12.9h-32.3z" class="st3"/></svg>'),zoom:Ta('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 351.845 80"><path d="M73.786 78.835H10.88A10.842 10.842 0 0 1 .833 72.122a10.841 10.841 0 0 1 2.357-11.85L46.764 16.7h-31.23C6.954 16.699 0 9.744 0 1.165h58.014c4.414 0 8.357 2.634 10.046 6.712a10.843 10.843 0 0 1-2.356 11.85L22.13 63.302h36.122c8.58 0 15.534 6.955 15.534 15.534Zm278.059-48.544C351.845 13.588 338.256 0 321.553 0c-8.934 0-16.975 3.89-22.524 10.063C293.48 3.89 285.44 0 276.505 0c-16.703 0-30.291 13.588-30.291 30.291v48.544c8.579 0 15.534-6.955 15.534-15.534v-33.01c0-8.137 6.62-14.757 14.757-14.757s14.757 6.62 14.757 14.757v33.01c0 8.58 6.955 15.534 15.534 15.534V30.291c0-8.137 6.62-14.757 14.757-14.757s14.758 6.62 14.758 14.757v33.01c0 8.58 6.954 15.534 15.534 15.534V30.291ZM238.447 40c0 22.091-17.909 40-40 40s-40-17.909-40-40 17.908-40 40-40 40 17.909 40 40Zm-15.534 0c0-13.512-10.954-24.466-24.466-24.466S173.98 26.488 173.98 40s10.953 24.466 24.466 24.466S222.913 53.512 222.913 40Zm-70.68 0c0 22.091-17.909 40-40 40s-40-17.909-40-40 17.909-40 40-40 40 17.909 40 40Zm-15.534 0c0-13.512-10.954-24.466-24.466-24.466S87.767 26.488 87.767 40s10.954 24.466 24.466 24.466S136.699 53.512 136.699 40Z" style="fill:#0b5cff"/></svg>')},nh=p=>new ja.OAuthService({client:new j1.OAuth.PKCEClient({redirectMethod:j1.OAuth.RedirectMethod.Web,providerName:"Asana",providerIcon:Ha.asana,providerId:"asana",description:"Connect your Asana account"}),clientId:p.clientId??Zt.asana,authorizeUrl:p.authorizeUrl??"https://asana.oauth.raycast.com/authorize",tokenUrl:p.tokenUrl??"https://asana.oauth.raycast.com/token",refreshTokenUrl:p.refreshTokenUrl??"https://asana.oauth.raycast.com/refresh-token",scope:p.scope,personalAccessToken:p.personalAccessToken,onAuthorize:p.onAuthorize,bodyEncoding:p.bodyEncoding,tokenRefreshResponseParser:p.tokenRefreshResponseParser,tokenResponseParser:p.tokenResponseParser});M1.asanaService=nh;var sh=p=>new ja.OAuthService({client:new j1.OAuth.PKCEClient({redirectMethod:j1.OAuth.RedirectMethod.Web,providerName:"GitHub",providerIcon:Ha.github,providerId:"github",description:"Connect your GitHub account"}),clientId:p.clientId??Zt.github,authorizeUrl:p.authorizeUrl??"https://github.oauth.raycast.com/authorize",tokenUrl:p.tokenUrl??"https://github.oauth.raycast.com/token",refreshTokenUrl:p.refreshTokenUrl??"https://github.oauth.raycast.com/refresh-token",scope:p.scope,personalAccessToken:p.personalAccessToken,onAuthorize:p.onAuthorize,bodyEncoding:p.bodyEncoding,tokenRefreshResponseParser:p.tokenRefreshResponseParser,tokenResponseParser:p.tokenResponseParser});M1.githubService=sh;var lh=p=>new ja.OAuthService({client:new j1.OAuth.PKCEClient({redirectMethod:j1.OAuth.RedirectMethod.AppURI,providerName:"Google",providerIcon:Ha.google,providerId:"google",description:"Connect your Google account"}),clientId:p.clientId,authorizeUrl:p.authorizeUrl??"https://accounts.google.com/o/oauth2/v2/auth",tokenUrl:p.tokenUrl??"https://oauth2.googleapis.com/token",refreshTokenUrl:p.tokenUrl,scope:p.scope,personalAccessToken:p.personalAccessToken,bodyEncoding:p.bodyEncoding??"url-encoded",onAuthorize:p.onAuthorize,tokenRefreshResponseParser:p.tokenRefreshResponseParser,tokenResponseParser:p.tokenResponseParser});M1.googleService=lh;var mh=p=>new ja.OAuthService({client:new j1.OAuth.PKCEClient({redirectMethod:j1.OAuth.RedirectMethod.Web,providerName:"Jira",providerIcon:Ha.jira,providerId:"jira",description:"Connect your Jira account"}),clientId:p.clientId,authorizeUrl:p.authorizeUrl??"https://auth.atlassian.com/authorize",tokenUrl:p.tokenUrl??"https://auth.atlassian.com/oauth/token",refreshTokenUrl:p.refreshTokenUrl,scope:p.scope,personalAccessToken:p.personalAccessToken,onAuthorize:p.onAuthorize,bodyEncoding:p.bodyEncoding,tokenRefreshResponseParser:p.tokenRefreshResponseParser,tokenResponseParser:p.tokenResponseParser});M1.jiraService=mh;var oh=p=>new ja.OAuthService({client:new j1.OAuth.PKCEClient({redirectMethod:j1.OAuth.RedirectMethod.Web,providerName:"Linear",providerIcon:Ha.linear,providerId:"linear",description:"Connect your Linear account"}),clientId:p.clientId??Zt.linear,authorizeUrl:p.authorizeUrl??"https://linear.oauth.raycast.com/authorize",tokenUrl:p.tokenUrl??"https://linear.oauth.raycast.com/token",refreshTokenUrl:p.refreshTokenUrl??"https://linear.oauth.raycast.com/refresh-token",scope:p.scope,extraParameters:{actor:"user"},onAuthorize:p.onAuthorize,bodyEncoding:p.bodyEncoding,tokenRefreshResponseParser:p.tokenRefreshResponseParser,tokenResponseParser:p.tokenResponseParser});M1.linearService=oh;var uh=p=>new ja.OAuthService({client:new j1.OAuth.PKCEClient({redirectMethod:j1.OAuth.RedirectMethod.Web,providerName:"Slack",providerIcon:Ha.slack,providerId:"slack",description:"Connect your Slack account"}),clientId:p.clientId??Zt.slack,authorizeUrl:p.authorizeUrl??"https://slack.oauth.raycast.com/authorize",tokenUrl:p.tokenUrl??"https://slack.oauth.raycast.com/token",refreshTokenUrl:p.tokenUrl??"https://slack.oauth.raycast.com/refresh-token",scope:"",extraParameters:{user_scope:p.scope},personalAccessToken:p.personalAccessToken,bodyEncoding:p.tokenUrl?p.bodyEncoding??"url-encoded":"json",onAuthorize:p.onAuthorize,tokenRefreshResponseParser:p.tokenRefreshResponseParser,tokenResponseParser:p.tokenResponseParser??(t=>({access_token:t.authed_user.access_token,scope:t.authed_user.scope}))});M1.slackService=uh;var ch=p=>new ja.OAuthService({client:new j1.OAuth.PKCEClient({redirectMethod:j1.OAuth.RedirectMethod.Web,providerName:"Zoom",providerIcon:Ha.zoom,providerId:"zoom",description:"Connect your Zoom account"}),clientId:p.clientId,authorizeUrl:p.authorizeUrl??"https://zoom.us/oauth/authorize",tokenUrl:p.tokenUrl??"https://zoom.us/oauth/token",refreshTokenUrl:p.refreshTokenUrl,scope:p.scope,personalAccessToken:p.personalAccessToken,bodyEncoding:p.bodyEncoding??"url-encoded",onAuthorize:p.onAuthorize,tokenRefreshResponseParser:p.tokenRefreshResponseParser,tokenResponseParser:p.tokenResponseParser});M1.zoomService=ch});var v6=I(Vp=>{"use strict";var fh=Vp&&Vp.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(Vp,"__esModule",{value:!0});Vp.OAuthService=void 0;var w3=fh(Et()),Ga=_3(),We=class{constructor(t){this.clientId=t.clientId,this.scope=Array.isArray(t.scope)?t.scope.join(" "):t.scope,this.personalAccessToken=t.personalAccessToken,this.bodyEncoding=t.bodyEncoding,this.client=t.client,this.extraParameters=t.extraParameters,this.authorizeUrl=t.authorizeUrl,this.tokenUrl=t.tokenUrl,this.refreshTokenUrl=t.refreshTokenUrl,this.onAuthorize=t.onAuthorize,this.tokenResponseParser=t.tokenResponseParser??(i=>i),this.tokenRefreshResponseParser=t.tokenRefreshResponseParser??(i=>i),this.authorize=this.authorize.bind(this)}async authorize(){let t=await this.client.getTokens();if(t?.accessToken){if(t.refreshToken&&t.isExpired()){let u=await this.refreshTokens({token:t.refreshToken});if(u)return await this.client.setTokens(u),u.access_token}return t.accessToken}let i=await this.client.authorizationRequest({endpoint:this.authorizeUrl,clientId:this.clientId,scope:this.scope,extraParameters:this.extraParameters}),{authorizationCode:r}=await this.client.authorize(i),n=await this.fetchTokens({authRequest:i,authorizationCode:r});return await this.client.setTokens(n),n.access_token}async fetchTokens({authRequest:t,authorizationCode:i}){let r;if(this.bodyEncoding==="url-encoded"){let v=new URLSearchParams;v.append("client_id",this.clientId),v.append("code",i),v.append("code_verifier",t.codeVerifier),v.append("grant_type","authorization_code"),v.append("redirect_uri",t.redirectURI),r={body:v}}else r={body:JSON.stringify({client_id:this.clientId,code:i,code_verifier:t.codeVerifier,grant_type:"authorization_code",redirect_uri:t.redirectURI}),headers:{"Content-Type":"application/json"}};let n=await(0,w3.default)(this.tokenUrl,{method:"POST",...r});if(!n.ok){let v=await n.text();throw console.error("fetch tokens error:",v),new Error(`Error while fetching tokens: ${n.status} (${n.statusText})
${v}`)}let u=this.tokenResponseParser(await n.json());return Array.isArray(u.scope)?{...u,scope:u.scope.join(" ")}:u}async refreshTokens({token:t}){let i;if(this.bodyEncoding==="url-encoded"){let n=new URLSearchParams;n.append("client_id",this.clientId),n.append("refresh_token",t),n.append("grant_type","refresh_token"),i={body:n}}else i={body:JSON.stringify({client_id:this.clientId,refresh_token:t,grant_type:"refresh_token"}),headers:{"Content-Type":"application/json"}};let r=await(0,w3.default)(this.refreshTokenUrl??this.tokenUrl,{method:"POST",...i});if(r.ok){let n=this.tokenRefreshResponseParser(await r.json());return n.refresh_token=n.refresh_token??t,n}else{let n=await r.text();console.error("refresh tokens error:",n),this.client.description=`${this.client.providerName} needs you to sign-in again. Press \u23CE or click the button below to continue.`,await this.client.removeTokens(),await this.authorize()}}};Vp.OAuthService=We;We.asana=Ga.asanaService;We.github=Ga.githubService;We.google=Ga.googleService;We.jira=Ga.jiraService;We.linear=Ga.linearService;We.slack=Ga.slackService;We.zoom=Ga.zoomService});var g3=I(Cp=>{"use strict";Object.defineProperty(Cp,"__esModule",{value:!0});Cp.getAccessToken=Cp.withAccessToken=void 0;var hh=require("react/jsx-runtime"),vh=require("@raycast/api"),xa=null,Ka=null,_6=null,w6=null,g6=null;function _h(p){return vh.environment.commandMode==="no-view"?t=>async r=>{if(!xa){xa=p.personalAccessToken??await p.authorize(),Ka=p.personalAccessToken?"personal":"oauth";let n=(await p.client?.getTokens())?.idToken;p.onAuthorize&&await Promise.resolve(p.onAuthorize({token:xa,type:Ka,idToken:n}))}return t(r)}:t=>{let i=r=>{p.personalAccessToken?(xa=p.personalAccessToken,Ka="personal"):(_6||(_6=b6(p.authorize())),xa=_6.read(),Ka="oauth");let n;return p.client&&(w6||(w6=b6(p.client.getTokens())),n=w6.read()?.idToken),!g6&&p.onAuthorize&&(g6=b6(Promise.resolve(p.onAuthorize({token:xa,type:Ka,idToken:n})))),g6?.read(),(0,hh.jsx)(t,{...r})};return i.displayName=`withAccessToken(${t.displayName||t.name})`,i}}Cp.withAccessToken=_h;function wh(){if(!xa||!Ka)throw new Error("getAccessToken must be used when authenticated (eg. used inside `withAccessToken`)");return{token:xa,type:Ka}}Cp.getAccessToken=wh;function b6(p){let t="pending",i,r=p.then(u=>{t="success",i=u},u=>{t="error",i=u});return{read:()=>{switch(t){case"pending":throw r;case"error":throw i;default:return i}}}}});var y3=I(Da=>{"use strict";var gh=Da&&Da.__createBinding||(Object.create?function(p,t,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(t,i);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(p,r,n)}:function(p,t,i,r){r===void 0&&(r=i),p[r]=t[i]}),b3=Da&&Da.__exportStar||function(p,t){for(var i in p)i!=="default"&&!Object.prototype.hasOwnProperty.call(t,i)&&gh(t,p,i)};Object.defineProperty(Da,"__esModule",{value:!0});b3(v6(),Da);b3(g3(),Da)});var D3=I(be=>{"use strict";var S3=be&&be.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(be,"__esModule",{value:!0});be.createDeeplink=be.createExtensionDeeplink=be.createScriptCommandDeeplink=be.DeeplinkType=void 0;var S6=require("@raycast/api"),bh=S3(require("node:fs")),yh=S3(require("node:path")),y6;(function(p){p.ScriptCommand="script-command",p.Extension="extension"})(y6||(be.DeeplinkType=y6={}));function N3(){return S6.environment.raycastVersion.includes("alpha")?"raycastinternal://":"raycast://"}function Sh(){let p=JSON.parse(bh.default.readFileSync(yh.default.join(S6.environment.assetsPath,"..","package.json"),"utf8"));return p.owner||p.author}function T3(p){let t=`${N3()}script-commands/${p.command}`;if(p.arguments){let i="";for(let r of p.arguments)i+="&arguments="+encodeURIComponent(r);t+="?"+i.substring(1)}return t}be.createScriptCommandDeeplink=T3;function x3(p){let t=Sh(),i=S6.environment.extensionName;"ownerOrAuthorName"in p&&"extensionName"in p&&(t=p.ownerOrAuthorName,i=p.extensionName);let r=`${N3()}extensions/${t}/${i}/${p.command}`,n="";return p.launchType&&(n+="&launchType="+encodeURIComponent(p.launchType)),p.arguments&&(n+="&arguments="+encodeURIComponent(JSON.stringify(p.arguments))),p.context&&(n+="&context="+encodeURIComponent(JSON.stringify(p.context))),p.fallbackText&&(n+="&fallbackText="+encodeURIComponent(p.fallbackText)),n&&(r+="?"+n.substring(1)),r}be.createExtensionDeeplink=x3;function Nh(p){return p.type===y6.ScriptCommand?T3(p):x3(p)}be.createDeeplink=Nh});var A3=I(Jt=>{"use strict";Object.defineProperty(Jt,"__esModule",{value:!0});Jt.executeSQL=void 0;var Th=s6();function xh(p,t){return(0,Th.baseExecuteSQL)(p,t)}Jt.executeSQL=xh});var E3=I(Pp=>{"use strict";var Dh=Pp&&Pp.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(Pp,"__esModule",{value:!0});Pp.runAppleScript=void 0;var Ah=Dh(require("node:child_process")),b2=Lt();async function Eh(p,t,i){let{humanReadableOutput:r,language:n,timeout:u,...v}=Array.isArray(t)?i||{}:t||{},_=r!==!1?[]:["-ss"];n==="JavaScript"&&_.push("-l","JavaScript"),Array.isArray(t)&&_.push("-",...t);let f=Ah.default.spawn("osascript",_,{...v,env:{PATH:"/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"}}),N=(0,b2.getSpawnedPromise)(f,{timeout:u??1e4});f.stdin.end(p);let[{error:A,exitCode:D,signal:V,timedOut:F},C,R]=await(0,b2.getSpawnedResult)(f,{encoding:"utf8"},N),q=(0,b2.handleOutput)({stripFinalNewline:!0},C),P=(0,b2.handleOutput)({stripFinalNewline:!0},R);return(0,b2.defaultParsing)({stdout:q,stderr:P,error:A,exitCode:D,signal:V,timedOut:F,command:"osascript",options:i,parentError:new Error})}Pp.runAppleScript=Eh});var Yt=I(c1=>{"use strict";var Vh=c1&&c1.__createBinding||(Object.create?function(p,t,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(t,i);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(p,r,n)}:function(p,t,i,r){r===void 0&&(r=i),p[r]=t[i]}),I1=c1&&c1.__exportStar||function(p,t){for(var i in p)i!=="default"&&!Object.prototype.hasOwnProperty.call(t,i)&&Vh(t,p,i)};Object.defineProperty(c1,"__esModule",{value:!0});I1(cp(),c1);I1(bt(),c1);I1(n2(),c1);I1(s9(),c1);I1(h9(),c1);I1(z9(),c1);I1(H9(),c1);I1(K9(),c1);I1(J9(),c1);I1(X9(),c1);I1(p3(),c1);I1(v3(),c1);I1(y3(),c1);I1(D3(),c1);I1(A3(),c1);I1(E3(),c1);I1(t2(),c1)});var R3=I((kp,y2)=>{(function(){var p,t="4.17.21",i=200,r="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",n="Expected a function",u="Invalid `variable` option passed into `_.template`",v="__lodash_hash_undefined__",_=500,f="__lodash_placeholder__",N=1,A=2,D=4,V=1,F=2,C=1,R=2,q=4,P=8,L=16,B=32,U=64,H=128,e1=256,N1=512,F1=30,Q1="...",R1=800,T1=16,H1=1,ne=2,a1=3,w1=1/0,G1=9007199254740991,Rp=17976931348623157e292,Ja=NaN,Pe=4294967295,U3=Pe-1,M3=Pe>>>1,q3=[["ary",H],["bind",C],["bindKey",R],["curry",P],["curryRight",L],["flip",N1],["partial",B],["partialRight",U],["rearg",e1]],Ya="[object Arguments]",N2="[object Array]",z3="[object AsyncFunction]",Op="[object Boolean]",Lp="[object Date]",$3="[object DOMException]",T2="[object Error]",x2="[object Function]",D6="[object GeneratorFunction]",ye="[object Map]",Ip="[object Number]",W3="[object Null]",je="[object Object]",A6="[object Promise]",j3="[object Proxy]",Fp="[object RegExp]",Se="[object Set]",Bp="[object String]",D2="[object Symbol]",H3="[object Undefined]",Up="[object WeakMap]",G3="[object WeakSet]",Mp="[object ArrayBuffer]",Qa="[object DataView]",ed="[object Float32Array]",ad="[object Float64Array]",pd="[object Int8Array]",td="[object Int16Array]",dd="[object Int32Array]",id="[object Uint8Array]",rd="[object Uint8ClampedArray]",nd="[object Uint16Array]",sd="[object Uint32Array]",K3=/\b__p \+= '';/g,Z3=/\b(__p \+=) '' \+/g,J3=/(__e\(.*?\)|\b__t\)) \+\n'';/g,E6=/&(?:amp|lt|gt|quot|#39);/g,V6=/[&<>"']/g,Y3=RegExp(E6.source),Q3=RegExp(V6.source),X3=/<%-([\s\S]+?)%>/g,en=/<%([\s\S]+?)%>/g,C6=/<%=([\s\S]+?)%>/g,an=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,pn=/^\w*$/,tn=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ld=/[\\^$.*+?()[\]{}|]/g,dn=RegExp(ld.source),md=/^\s+/,rn=/\s/,nn=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,sn=/\{\n\/\* \[wrapped with (.+)\] \*/,ln=/,? & /,mn=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,on=/[()=,{}\[\]\/\s]/,un=/\\(\\)?/g,cn=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,P6=/\w*$/,fn=/^[-+]0x[0-9a-f]+$/i,hn=/^0b[01]+$/i,vn=/^\[object .+?Constructor\]$/,_n=/^0o[0-7]+$/i,wn=/^(?:0|[1-9]\d*)$/,gn=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,A2=/($^)/,bn=/['\n\r\u2028\u2029\\]/g,E2="\\ud800-\\udfff",yn="\\u0300-\\u036f",Sn="\\ufe20-\\ufe2f",Nn="\\u20d0-\\u20ff",k6=yn+Sn+Nn,R6="\\u2700-\\u27bf",O6="a-z\\xdf-\\xf6\\xf8-\\xff",Tn="\\xac\\xb1\\xd7\\xf7",xn="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",Dn="\\u2000-\\u206f",An=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",L6="A-Z\\xc0-\\xd6\\xd8-\\xde",I6="\\ufe0e\\ufe0f",F6=Tn+xn+Dn+An,od="['\u2019]",En="["+E2+"]",B6="["+F6+"]",V2="["+k6+"]",U6="\\d+",Vn="["+R6+"]",M6="["+O6+"]",q6="[^"+E2+F6+U6+R6+O6+L6+"]",ud="\\ud83c[\\udffb-\\udfff]",Cn="(?:"+V2+"|"+ud+")",z6="[^"+E2+"]",cd="(?:\\ud83c[\\udde6-\\uddff]){2}",fd="[\\ud800-\\udbff][\\udc00-\\udfff]",Xa="["+L6+"]",$6="\\u200d",W6="(?:"+M6+"|"+q6+")",Pn="(?:"+Xa+"|"+q6+")",j6="(?:"+od+"(?:d|ll|m|re|s|t|ve))?",H6="(?:"+od+"(?:D|LL|M|RE|S|T|VE))?",G6=Cn+"?",K6="["+I6+"]?",kn="(?:"+$6+"(?:"+[z6,cd,fd].join("|")+")"+K6+G6+")*",Rn="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",On="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Z6=K6+G6+kn,Ln="(?:"+[Vn,cd,fd].join("|")+")"+Z6,In="(?:"+[z6+V2+"?",V2,cd,fd,En].join("|")+")",Fn=RegExp(od,"g"),Bn=RegExp(V2,"g"),hd=RegExp(ud+"(?="+ud+")|"+In+Z6,"g"),Un=RegExp([Xa+"?"+M6+"+"+j6+"(?="+[B6,Xa,"$"].join("|")+")",Pn+"+"+H6+"(?="+[B6,Xa+W6,"$"].join("|")+")",Xa+"?"+W6+"+"+j6,Xa+"+"+H6,On,Rn,U6,Ln].join("|"),"g"),Mn=RegExp("["+$6+E2+k6+I6+"]"),qn=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,zn=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],$n=-1,l1={};l1[ed]=l1[ad]=l1[pd]=l1[td]=l1[dd]=l1[id]=l1[rd]=l1[nd]=l1[sd]=!0,l1[Ya]=l1[N2]=l1[Mp]=l1[Op]=l1[Qa]=l1[Lp]=l1[T2]=l1[x2]=l1[ye]=l1[Ip]=l1[je]=l1[Fp]=l1[Se]=l1[Bp]=l1[Up]=!1;var s1={};s1[Ya]=s1[N2]=s1[Mp]=s1[Qa]=s1[Op]=s1[Lp]=s1[ed]=s1[ad]=s1[pd]=s1[td]=s1[dd]=s1[ye]=s1[Ip]=s1[je]=s1[Fp]=s1[Se]=s1[Bp]=s1[D2]=s1[id]=s1[rd]=s1[nd]=s1[sd]=!0,s1[T2]=s1[x2]=s1[Up]=!1;var Wn={\u00C0:"A",\u00C1:"A",\u00C2:"A",\u00C3:"A",\u00C4:"A",\u00C5:"A",\u00E0:"a",\u00E1:"a",\u00E2:"a",\u00E3:"a",\u00E4:"a",\u00E5:"a",\u00C7:"C",\u00E7:"c",\u00D0:"D",\u00F0:"d",\u00C8:"E",\u00C9:"E",\u00CA:"E",\u00CB:"E",\u00E8:"e",\u00E9:"e",\u00EA:"e",\u00EB:"e",\u00CC:"I",\u00CD:"I",\u00CE:"I",\u00CF:"I",\u00EC:"i",\u00ED:"i",\u00EE:"i",\u00EF:"i",\u00D1:"N",\u00F1:"n",\u00D2:"O",\u00D3:"O",\u00D4:"O",\u00D5:"O",\u00D6:"O",\u00D8:"O",\u00F2:"o",\u00F3:"o",\u00F4:"o",\u00F5:"o",\u00F6:"o",\u00F8:"o",\u00D9:"U",\u00DA:"U",\u00DB:"U",\u00DC:"U",\u00F9:"u",\u00FA:"u",\u00FB:"u",\u00FC:"u",\u00DD:"Y",\u00FD:"y",\u00FF:"y",\u00C6:"Ae",\u00E6:"ae",\u00DE:"Th",\u00FE:"th",\u00DF:"ss",\u0100:"A",\u0102:"A",\u0104:"A",\u0101:"a",\u0103:"a",\u0105:"a",\u0106:"C",\u0108:"C",\u010A:"C",\u010C:"C",\u0107:"c",\u0109:"c",\u010B:"c",\u010D:"c",\u010E:"D",\u0110:"D",\u010F:"d",\u0111:"d",\u0112:"E",\u0114:"E",\u0116:"E",\u0118:"E",\u011A:"E",\u0113:"e",\u0115:"e",\u0117:"e",\u0119:"e",\u011B:"e",\u011C:"G",\u011E:"G",\u0120:"G",\u0122:"G",\u011D:"g",\u011F:"g",\u0121:"g",\u0123:"g",\u0124:"H",\u0126:"H",\u0125:"h",\u0127:"h",\u0128:"I",\u012A:"I",\u012C:"I",\u012E:"I",\u0130:"I",\u0129:"i",\u012B:"i",\u012D:"i",\u012F:"i",\u0131:"i",\u0134:"J",\u0135:"j",\u0136:"K",\u0137:"k",\u0138:"k",\u0139:"L",\u013B:"L",\u013D:"L",\u013F:"L",\u0141:"L",\u013A:"l",\u013C:"l",\u013E:"l",\u0140:"l",\u0142:"l",\u0143:"N",\u0145:"N",\u0147:"N",\u014A:"N",\u0144:"n",\u0146:"n",\u0148:"n",\u014B:"n",\u014C:"O",\u014E:"O",\u0150:"O",\u014D:"o",\u014F:"o",\u0151:"o",\u0154:"R",\u0156:"R",\u0158:"R",\u0155:"r",\u0157:"r",\u0159:"r",\u015A:"S",\u015C:"S",\u015E:"S",\u0160:"S",\u015B:"s",\u015D:"s",\u015F:"s",\u0161:"s",\u0162:"T",\u0164:"T",\u0166:"T",\u0163:"t",\u0165:"t",\u0167:"t",\u0168:"U",\u016A:"U",\u016C:"U",\u016E:"U",\u0170:"U",\u0172:"U",\u0169:"u",\u016B:"u",\u016D:"u",\u016F:"u",\u0171:"u",\u0173:"u",\u0174:"W",\u0175:"w",\u0176:"Y",\u0177:"y",\u0178:"Y",\u0179:"Z",\u017B:"Z",\u017D:"Z",\u017A:"z",\u017C:"z",\u017E:"z",\u0132:"IJ",\u0133:"ij",\u0152:"Oe",\u0153:"oe",\u0149:"'n",\u017F:"s"},jn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Hn={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},Gn={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Kn=parseFloat,Zn=parseInt,J6=typeof global=="object"&&global&&global.Object===Object&&global,Jn=typeof self=="object"&&self&&self.Object===Object&&self,E1=J6||Jn||Function("return this")(),vd=typeof kp=="object"&&kp&&!kp.nodeType&&kp,Aa=vd&&typeof y2=="object"&&y2&&!y2.nodeType&&y2,Y6=Aa&&Aa.exports===vd,_d=Y6&&J6.process,se=function(){try{var w=Aa&&Aa.require&&Aa.require("util").types;return w||_d&&_d.binding&&_d.binding("util")}catch{}}(),Q6=se&&se.isArrayBuffer,X6=se&&se.isDate,e4=se&&se.isMap,a4=se&&se.isRegExp,p4=se&&se.isSet,t4=se&&se.isTypedArray;function X1(w,y,b){switch(b.length){case 0:return w.call(y);case 1:return w.call(y,b[0]);case 2:return w.call(y,b[0],b[1]);case 3:return w.call(y,b[0],b[1],b[2])}return w.apply(y,b)}function Yn(w,y,b,k){for(var W=-1,t1=w==null?0:w.length;++W<t1;){var x1=w[W];y(k,x1,b(x1),w)}return k}function le(w,y){for(var b=-1,k=w==null?0:w.length;++b<k&&y(w[b],b,w)!==!1;);return w}function Qn(w,y){for(var b=w==null?0:w.length;b--&&y(w[b],b,w)!==!1;);return w}function d4(w,y){for(var b=-1,k=w==null?0:w.length;++b<k;)if(!y(w[b],b,w))return!1;return!0}function sa(w,y){for(var b=-1,k=w==null?0:w.length,W=0,t1=[];++b<k;){var x1=w[b];y(x1,b,w)&&(t1[W++]=x1)}return t1}function C2(w,y){var b=w==null?0:w.length;return!!b&&ep(w,y,0)>-1}function wd(w,y,b){for(var k=-1,W=w==null?0:w.length;++k<W;)if(b(y,w[k]))return!0;return!1}function m1(w,y){for(var b=-1,k=w==null?0:w.length,W=Array(k);++b<k;)W[b]=y(w[b],b,w);return W}function la(w,y){for(var b=-1,k=y.length,W=w.length;++b<k;)w[W+b]=y[b];return w}function gd(w,y,b,k){var W=-1,t1=w==null?0:w.length;for(k&&t1&&(b=w[++W]);++W<t1;)b=y(b,w[W],W,w);return b}function Xn(w,y,b,k){var W=w==null?0:w.length;for(k&&W&&(b=w[--W]);W--;)b=y(b,w[W],W,w);return b}function bd(w,y){for(var b=-1,k=w==null?0:w.length;++b<k;)if(y(w[b],b,w))return!0;return!1}var e5=yd("length");function a5(w){return w.split("")}function p5(w){return w.match(mn)||[]}function i4(w,y,b){var k;return b(w,function(W,t1,x1){if(y(W,t1,x1))return k=t1,!1}),k}function P2(w,y,b,k){for(var W=w.length,t1=b+(k?1:-1);k?t1--:++t1<W;)if(y(w[t1],t1,w))return t1;return-1}function ep(w,y,b){return y===y?f5(w,y,b):P2(w,r4,b)}function t5(w,y,b,k){for(var W=b-1,t1=w.length;++W<t1;)if(k(w[W],y))return W;return-1}function r4(w){return w!==w}function n4(w,y){var b=w==null?0:w.length;return b?Nd(w,y)/b:Ja}function yd(w){return function(y){return y==null?p:y[w]}}function Sd(w){return function(y){return w==null?p:w[y]}}function s4(w,y,b,k,W){return W(w,function(t1,x1,r1){b=k?(k=!1,t1):y(b,t1,x1,r1)}),b}function d5(w,y){var b=w.length;for(w.sort(y);b--;)w[b]=w[b].value;return w}function Nd(w,y){for(var b,k=-1,W=w.length;++k<W;){var t1=y(w[k]);t1!==p&&(b=b===p?t1:b+t1)}return b}function Td(w,y){for(var b=-1,k=Array(w);++b<w;)k[b]=y(b);return k}function i5(w,y){return m1(y,function(b){return[b,w[b]]})}function l4(w){return w&&w.slice(0,c4(w)+1).replace(md,"")}function ee(w){return function(y){return w(y)}}function xd(w,y){return m1(y,function(b){return w[b]})}function qp(w,y){return w.has(y)}function m4(w,y){for(var b=-1,k=w.length;++b<k&&ep(y,w[b],0)>-1;);return b}function o4(w,y){for(var b=w.length;b--&&ep(y,w[b],0)>-1;);return b}function r5(w,y){for(var b=w.length,k=0;b--;)w[b]===y&&++k;return k}var n5=Sd(Wn),s5=Sd(jn);function l5(w){return"\\"+Gn[w]}function m5(w,y){return w==null?p:w[y]}function ap(w){return Mn.test(w)}function o5(w){return qn.test(w)}function u5(w){for(var y,b=[];!(y=w.next()).done;)b.push(y.value);return b}function Dd(w){var y=-1,b=Array(w.size);return w.forEach(function(k,W){b[++y]=[W,k]}),b}function u4(w,y){return function(b){return w(y(b))}}function ma(w,y){for(var b=-1,k=w.length,W=0,t1=[];++b<k;){var x1=w[b];(x1===y||x1===f)&&(w[b]=f,t1[W++]=b)}return t1}function k2(w){var y=-1,b=Array(w.size);return w.forEach(function(k){b[++y]=k}),b}function c5(w){var y=-1,b=Array(w.size);return w.forEach(function(k){b[++y]=[k,k]}),b}function f5(w,y,b){for(var k=b-1,W=w.length;++k<W;)if(w[k]===y)return k;return-1}function h5(w,y,b){for(var k=b+1;k--;)if(w[k]===y)return k;return k}function pp(w){return ap(w)?_5(w):e5(w)}function Ne(w){return ap(w)?w5(w):a5(w)}function c4(w){for(var y=w.length;y--&&rn.test(w.charAt(y)););return y}var v5=Sd(Hn);function _5(w){for(var y=hd.lastIndex=0;hd.test(w);)++y;return y}function w5(w){return w.match(hd)||[]}function g5(w){return w.match(Un)||[]}var b5=function w(y){y=y==null?E1:oa.defaults(E1.Object(),y,oa.pick(E1,zn));var b=y.Array,k=y.Date,W=y.Error,t1=y.Function,x1=y.Math,r1=y.Object,Ad=y.RegExp,y5=y.String,me=y.TypeError,R2=b.prototype,S5=t1.prototype,tp=r1.prototype,O2=y["__core-js_shared__"],L2=S5.toString,i1=tp.hasOwnProperty,N5=0,f4=function(){var e=/[^.]+$/.exec(O2&&O2.keys&&O2.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),I2=tp.toString,T5=L2.call(r1),x5=E1._,D5=Ad("^"+L2.call(i1).replace(ld,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),F2=Y6?y.Buffer:p,ua=y.Symbol,B2=y.Uint8Array,h4=F2?F2.allocUnsafe:p,U2=u4(r1.getPrototypeOf,r1),v4=r1.create,_4=tp.propertyIsEnumerable,M2=R2.splice,w4=ua?ua.isConcatSpreadable:p,zp=ua?ua.iterator:p,Ea=ua?ua.toStringTag:p,q2=function(){try{var e=Ra(r1,"defineProperty");return e({},"",{}),e}catch{}}(),A5=y.clearTimeout!==E1.clearTimeout&&y.clearTimeout,E5=k&&k.now!==E1.Date.now&&k.now,V5=y.setTimeout!==E1.setTimeout&&y.setTimeout,z2=x1.ceil,$2=x1.floor,Ed=r1.getOwnPropertySymbols,C5=F2?F2.isBuffer:p,g4=y.isFinite,P5=R2.join,k5=u4(r1.keys,r1),D1=x1.max,B1=x1.min,R5=k.now,O5=y.parseInt,b4=x1.random,L5=R2.reverse,Vd=Ra(y,"DataView"),$p=Ra(y,"Map"),Cd=Ra(y,"Promise"),dp=Ra(y,"Set"),Wp=Ra(y,"WeakMap"),jp=Ra(r1,"create"),W2=Wp&&new Wp,ip={},I5=Oa(Vd),F5=Oa($p),B5=Oa(Cd),U5=Oa(dp),M5=Oa(Wp),j2=ua?ua.prototype:p,Hp=j2?j2.valueOf:p,y4=j2?j2.toString:p;function m(e){if(f1(e)&&!j(e)&&!(e instanceof X)){if(e instanceof oe)return e;if(i1.call(e,"__wrapped__"))return Si(e)}return new oe(e)}var rp=function(){function e(){}return function(a){if(!u1(a))return{};if(v4)return v4(a);e.prototype=a;var d=new e;return e.prototype=p,d}}();function H2(){}function oe(e,a){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!a,this.__index__=0,this.__values__=p}m.templateSettings={escape:X3,evaluate:en,interpolate:C6,variable:"",imports:{_:m}},m.prototype=H2.prototype,m.prototype.constructor=m,oe.prototype=rp(H2.prototype),oe.prototype.constructor=oe;function X(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=Pe,this.__views__=[]}function q5(){var e=new X(this.__wrapped__);return e.__actions__=K1(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=K1(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=K1(this.__views__),e}function z5(){if(this.__filtered__){var e=new X(this);e.__dir__=-1,e.__filtered__=!0}else e=this.clone(),e.__dir__*=-1;return e}function $5(){var e=this.__wrapped__.value(),a=this.__dir__,d=j(e),s=a<0,l=d?e.length:0,o=a7(0,l,this.__views__),c=o.start,h=o.end,g=h-c,S=s?h:c-1,T=this.__iteratees__,x=T.length,E=0,O=B1(g,this.__takeCount__);if(!d||!s&&l==g&&O==g)return j4(e,this.__actions__);var z=[];e:for(;g--&&E<O;){S+=a;for(var K=-1,$=e[S];++K<x;){var Y=T[K],p1=Y.iteratee,te=Y.type,$1=p1($);if(te==ne)$=$1;else if(!$1){if(te==H1)continue e;break e}}z[E++]=$}return z}X.prototype=rp(H2.prototype),X.prototype.constructor=X;function Va(e){var a=-1,d=e==null?0:e.length;for(this.clear();++a<d;){var s=e[a];this.set(s[0],s[1])}}function W5(){this.__data__=jp?jp(null):{},this.size=0}function j5(e){var a=this.has(e)&&delete this.__data__[e];return this.size-=a?1:0,a}function H5(e){var a=this.__data__;if(jp){var d=a[e];return d===v?p:d}return i1.call(a,e)?a[e]:p}function G5(e){var a=this.__data__;return jp?a[e]!==p:i1.call(a,e)}function K5(e,a){var d=this.__data__;return this.size+=this.has(e)?0:1,d[e]=jp&&a===p?v:a,this}Va.prototype.clear=W5,Va.prototype.delete=j5,Va.prototype.get=H5,Va.prototype.has=G5,Va.prototype.set=K5;function He(e){var a=-1,d=e==null?0:e.length;for(this.clear();++a<d;){var s=e[a];this.set(s[0],s[1])}}function Z5(){this.__data__=[],this.size=0}function J5(e){var a=this.__data__,d=G2(a,e);if(d<0)return!1;var s=a.length-1;return d==s?a.pop():M2.call(a,d,1),--this.size,!0}function Y5(e){var a=this.__data__,d=G2(a,e);return d<0?p:a[d][1]}function Q5(e){return G2(this.__data__,e)>-1}function X5(e,a){var d=this.__data__,s=G2(d,e);return s<0?(++this.size,d.push([e,a])):d[s][1]=a,this}He.prototype.clear=Z5,He.prototype.delete=J5,He.prototype.get=Y5,He.prototype.has=Q5,He.prototype.set=X5;function Ge(e){var a=-1,d=e==null?0:e.length;for(this.clear();++a<d;){var s=e[a];this.set(s[0],s[1])}}function e8(){this.size=0,this.__data__={hash:new Va,map:new($p||He),string:new Va}}function a8(e){var a=it(this,e).delete(e);return this.size-=a?1:0,a}function p8(e){return it(this,e).get(e)}function t8(e){return it(this,e).has(e)}function d8(e,a){var d=it(this,e),s=d.size;return d.set(e,a),this.size+=d.size==s?0:1,this}Ge.prototype.clear=e8,Ge.prototype.delete=a8,Ge.prototype.get=p8,Ge.prototype.has=t8,Ge.prototype.set=d8;function Ca(e){var a=-1,d=e==null?0:e.length;for(this.__data__=new Ge;++a<d;)this.add(e[a])}function i8(e){return this.__data__.set(e,v),this}function r8(e){return this.__data__.has(e)}Ca.prototype.add=Ca.prototype.push=i8,Ca.prototype.has=r8;function Te(e){var a=this.__data__=new He(e);this.size=a.size}function n8(){this.__data__=new He,this.size=0}function s8(e){var a=this.__data__,d=a.delete(e);return this.size=a.size,d}function l8(e){return this.__data__.get(e)}function m8(e){return this.__data__.has(e)}function o8(e,a){var d=this.__data__;if(d instanceof He){var s=d.__data__;if(!$p||s.length<i-1)return s.push([e,a]),this.size=++d.size,this;d=this.__data__=new Ge(s)}return d.set(e,a),this.size=d.size,this}Te.prototype.clear=n8,Te.prototype.delete=s8,Te.prototype.get=l8,Te.prototype.has=m8,Te.prototype.set=o8;function S4(e,a){var d=j(e),s=!d&&La(e),l=!d&&!s&&_a(e),o=!d&&!s&&!l&&mp(e),c=d||s||l||o,h=c?Td(e.length,y5):[],g=h.length;for(var S in e)(a||i1.call(e,S))&&!(c&&(S=="length"||l&&(S=="offset"||S=="parent")||o&&(S=="buffer"||S=="byteLength"||S=="byteOffset")||Ye(S,g)))&&h.push(S);return h}function N4(e){var a=e.length;return a?e[qd(0,a-1)]:p}function u8(e,a){return rt(K1(e),Pa(a,0,e.length))}function c8(e){return rt(K1(e))}function Pd(e,a,d){(d!==p&&!xe(e[a],d)||d===p&&!(a in e))&&Ke(e,a,d)}function Gp(e,a,d){var s=e[a];(!(i1.call(e,a)&&xe(s,d))||d===p&&!(a in e))&&Ke(e,a,d)}function G2(e,a){for(var d=e.length;d--;)if(xe(e[d][0],a))return d;return-1}function f8(e,a,d,s){return ca(e,function(l,o,c){a(s,l,d(l),c)}),s}function T4(e,a){return e&&Re(a,V1(a),e)}function h8(e,a){return e&&Re(a,J1(a),e)}function Ke(e,a,d){a=="__proto__"&&q2?q2(e,a,{configurable:!0,enumerable:!0,value:d,writable:!0}):e[a]=d}function kd(e,a){for(var d=-1,s=a.length,l=b(s),o=e==null;++d<s;)l[d]=o?p:u0(e,a[d]);return l}function Pa(e,a,d){return e===e&&(d!==p&&(e=e<=d?e:d),a!==p&&(e=e>=a?e:a)),e}function ue(e,a,d,s,l,o){var c,h=a&N,g=a&A,S=a&D;if(d&&(c=l?d(e,s,l,o):d(e)),c!==p)return c;if(!u1(e))return e;var T=j(e);if(T){if(c=t7(e),!h)return K1(e,c)}else{var x=U1(e),E=x==x2||x==D6;if(_a(e))return K4(e,h);if(x==je||x==Ya||E&&!l){if(c=g||E?{}:ci(e),!h)return g?H8(e,h8(c,e)):j8(e,T4(c,e))}else{if(!s1[x])return l?e:{};c=d7(e,x,h)}}o||(o=new Te);var O=o.get(e);if(O)return O;o.set(e,c),zi(e)?e.forEach(function($){c.add(ue($,a,d,$,e,o))}):Mi(e)&&e.forEach(function($,Y){c.set(Y,ue($,a,d,Y,e,o))});var z=S?g?Qd:Yd:g?J1:V1,K=T?p:z(e);return le(K||e,function($,Y){K&&(Y=$,$=e[Y]),Gp(c,Y,ue($,a,d,Y,e,o))}),c}function v8(e){var a=V1(e);return function(d){return x4(d,e,a)}}function x4(e,a,d){var s=d.length;if(e==null)return!s;for(e=r1(e);s--;){var l=d[s],o=a[l],c=e[l];if(c===p&&!(l in e)||!o(c))return!1}return!0}function D4(e,a,d){if(typeof e!="function")throw new me(n);return e2(function(){e.apply(p,d)},a)}function Kp(e,a,d,s){var l=-1,o=C2,c=!0,h=e.length,g=[],S=a.length;if(!h)return g;d&&(a=m1(a,ee(d))),s?(o=wd,c=!1):a.length>=i&&(o=qp,c=!1,a=new Ca(a));e:for(;++l<h;){var T=e[l],x=d==null?T:d(T);if(T=s||T!==0?T:0,c&&x===x){for(var E=S;E--;)if(a[E]===x)continue e;g.push(T)}else o(a,x,s)||g.push(T)}return g}var ca=X4(ke),A4=X4(Od,!0);function _8(e,a){var d=!0;return ca(e,function(s,l,o){return d=!!a(s,l,o),d}),d}function K2(e,a,d){for(var s=-1,l=e.length;++s<l;){var o=e[s],c=a(o);if(c!=null&&(h===p?c===c&&!pe(c):d(c,h)))var h=c,g=o}return g}function w8(e,a,d,s){var l=e.length;for(d=G(d),d<0&&(d=-d>l?0:l+d),s=s===p||s>l?l:G(s),s<0&&(s+=l),s=d>s?0:Wi(s);d<s;)e[d++]=a;return e}function E4(e,a){var d=[];return ca(e,function(s,l,o){a(s,l,o)&&d.push(s)}),d}function O1(e,a,d,s,l){var o=-1,c=e.length;for(d||(d=r7),l||(l=[]);++o<c;){var h=e[o];a>0&&d(h)?a>1?O1(h,a-1,d,s,l):la(l,h):s||(l[l.length]=h)}return l}var Rd=ei(),V4=ei(!0);function ke(e,a){return e&&Rd(e,a,V1)}function Od(e,a){return e&&V4(e,a,V1)}function Z2(e,a){return sa(a,function(d){return Qe(e[d])})}function ka(e,a){a=ha(a,e);for(var d=0,s=a.length;e!=null&&d<s;)e=e[Oe(a[d++])];return d&&d==s?e:p}function C4(e,a,d){var s=a(e);return j(e)?s:la(s,d(e))}function q1(e){return e==null?e===p?H3:W3:Ea&&Ea in r1(e)?e7(e):c7(e)}function Ld(e,a){return e>a}function g8(e,a){return e!=null&&i1.call(e,a)}function b8(e,a){return e!=null&&a in r1(e)}function y8(e,a,d){return e>=B1(a,d)&&e<D1(a,d)}function Id(e,a,d){for(var s=d?wd:C2,l=e[0].length,o=e.length,c=o,h=b(o),g=1/0,S=[];c--;){var T=e[c];c&&a&&(T=m1(T,ee(a))),g=B1(T.length,g),h[c]=!d&&(a||l>=120&&T.length>=120)?new Ca(c&&T):p}T=e[0];var x=-1,E=h[0];e:for(;++x<l&&S.length<g;){var O=T[x],z=a?a(O):O;if(O=d||O!==0?O:0,!(E?qp(E,z):s(S,z,d))){for(c=o;--c;){var K=h[c];if(!(K?qp(K,z):s(e[c],z,d)))continue e}E&&E.push(z),S.push(O)}}return S}function S8(e,a,d,s){return ke(e,function(l,o,c){a(s,d(l),o,c)}),s}function Zp(e,a,d){a=ha(a,e),e=_i(e,a);var s=e==null?e:e[Oe(fe(a))];return s==null?p:X1(s,e,d)}function P4(e){return f1(e)&&q1(e)==Ya}function N8(e){return f1(e)&&q1(e)==Mp}function T8(e){return f1(e)&&q1(e)==Lp}function Jp(e,a,d,s,l){return e===a?!0:e==null||a==null||!f1(e)&&!f1(a)?e!==e&&a!==a:x8(e,a,d,s,Jp,l)}function x8(e,a,d,s,l,o){var c=j(e),h=j(a),g=c?N2:U1(e),S=h?N2:U1(a);g=g==Ya?je:g,S=S==Ya?je:S;var T=g==je,x=S==je,E=g==S;if(E&&_a(e)){if(!_a(a))return!1;c=!0,T=!1}if(E&&!T)return o||(o=new Te),c||mp(e)?mi(e,a,d,s,l,o):Q8(e,a,g,d,s,l,o);if(!(d&V)){var O=T&&i1.call(e,"__wrapped__"),z=x&&i1.call(a,"__wrapped__");if(O||z){var K=O?e.value():e,$=z?a.value():a;return o||(o=new Te),l(K,$,d,s,o)}}return E?(o||(o=new Te),X8(e,a,d,s,l,o)):!1}function D8(e){return f1(e)&&U1(e)==ye}function Fd(e,a,d,s){var l=d.length,o=l,c=!s;if(e==null)return!o;for(e=r1(e);l--;){var h=d[l];if(c&&h[2]?h[1]!==e[h[0]]:!(h[0]in e))return!1}for(;++l<o;){h=d[l];var g=h[0],S=e[g],T=h[1];if(c&&h[2]){if(S===p&&!(g in e))return!1}else{var x=new Te;if(s)var E=s(S,T,g,e,a,x);if(!(E===p?Jp(T,S,V|F,s,x):E))return!1}}return!0}function k4(e){if(!u1(e)||s7(e))return!1;var a=Qe(e)?D5:vn;return a.test(Oa(e))}function A8(e){return f1(e)&&q1(e)==Fp}function E8(e){return f1(e)&&U1(e)==Se}function V8(e){return f1(e)&&ut(e.length)&&!!l1[q1(e)]}function R4(e){return typeof e=="function"?e:e==null?Y1:typeof e=="object"?j(e)?I4(e[0],e[1]):L4(e):ar(e)}function Bd(e){if(!Xp(e))return k5(e);var a=[];for(var d in r1(e))i1.call(e,d)&&d!="constructor"&&a.push(d);return a}function C8(e){if(!u1(e))return u7(e);var a=Xp(e),d=[];for(var s in e)s=="constructor"&&(a||!i1.call(e,s))||d.push(s);return d}function Ud(e,a){return e<a}function O4(e,a){var d=-1,s=Z1(e)?b(e.length):[];return ca(e,function(l,o,c){s[++d]=a(l,o,c)}),s}function L4(e){var a=e0(e);return a.length==1&&a[0][2]?hi(a[0][0],a[0][1]):function(d){return d===e||Fd(d,e,a)}}function I4(e,a){return p0(e)&&fi(a)?hi(Oe(e),a):function(d){var s=u0(d,e);return s===p&&s===a?c0(d,e):Jp(a,s,V|F)}}function J2(e,a,d,s,l){e!==a&&Rd(a,function(o,c){if(l||(l=new Te),u1(o))P8(e,a,c,d,J2,s,l);else{var h=s?s(d0(e,c),o,c+"",e,a,l):p;h===p&&(h=o),Pd(e,c,h)}},J1)}function P8(e,a,d,s,l,o,c){var h=d0(e,d),g=d0(a,d),S=c.get(g);if(S){Pd(e,d,S);return}var T=o?o(h,g,d+"",e,a,c):p,x=T===p;if(x){var E=j(g),O=!E&&_a(g),z=!E&&!O&&mp(g);T=g,E||O||z?j(h)?T=h:g1(h)?T=K1(h):O?(x=!1,T=K4(g,!0)):z?(x=!1,T=Z4(g,!0)):T=[]:a2(g)||La(g)?(T=h,La(h)?T=ji(h):(!u1(h)||Qe(h))&&(T=ci(g))):x=!1}x&&(c.set(g,T),l(T,g,s,o,c),c.delete(g)),Pd(e,d,T)}function F4(e,a){var d=e.length;if(d)return a+=a<0?d:0,Ye(a,d)?e[a]:p}function B4(e,a,d){a.length?a=m1(a,function(o){return j(o)?function(c){return ka(c,o.length===1?o[0]:o)}:o}):a=[Y1];var s=-1;a=m1(a,ee(M()));var l=O4(e,function(o,c,h){var g=m1(a,function(S){return S(o)});return{criteria:g,index:++s,value:o}});return d5(l,function(o,c){return W8(o,c,d)})}function k8(e,a){return U4(e,a,function(d,s){return c0(e,s)})}function U4(e,a,d){for(var s=-1,l=a.length,o={};++s<l;){var c=a[s],h=ka(e,c);d(h,c)&&Yp(o,ha(c,e),h)}return o}function R8(e){return function(a){return ka(a,e)}}function Md(e,a,d,s){var l=s?t5:ep,o=-1,c=a.length,h=e;for(e===a&&(a=K1(a)),d&&(h=m1(e,ee(d)));++o<c;)for(var g=0,S=a[o],T=d?d(S):S;(g=l(h,T,g,s))>-1;)h!==e&&M2.call(h,g,1),M2.call(e,g,1);return e}function M4(e,a){for(var d=e?a.length:0,s=d-1;d--;){var l=a[d];if(d==s||l!==o){var o=l;Ye(l)?M2.call(e,l,1):Wd(e,l)}}return e}function qd(e,a){return e+$2(b4()*(a-e+1))}function O8(e,a,d,s){for(var l=-1,o=D1(z2((a-e)/(d||1)),0),c=b(o);o--;)c[s?o:++l]=e,e+=d;return c}function zd(e,a){var d="";if(!e||a<1||a>G1)return d;do a%2&&(d+=e),a=$2(a/2),a&&(e+=e);while(a);return d}function Z(e,a){return i0(vi(e,a,Y1),e+"")}function L8(e){return N4(op(e))}function I8(e,a){var d=op(e);return rt(d,Pa(a,0,d.length))}function Yp(e,a,d,s){if(!u1(e))return e;a=ha(a,e);for(var l=-1,o=a.length,c=o-1,h=e;h!=null&&++l<o;){var g=Oe(a[l]),S=d;if(g==="__proto__"||g==="constructor"||g==="prototype")return e;if(l!=c){var T=h[g];S=s?s(T,g,h):p,S===p&&(S=u1(T)?T:Ye(a[l+1])?[]:{})}Gp(h,g,S),h=h[g]}return e}var q4=W2?function(e,a){return W2.set(e,a),e}:Y1,F8=q2?function(e,a){return q2(e,"toString",{configurable:!0,enumerable:!1,value:h0(a),writable:!0})}:Y1;function B8(e){return rt(op(e))}function ce(e,a,d){var s=-1,l=e.length;a<0&&(a=-a>l?0:l+a),d=d>l?l:d,d<0&&(d+=l),l=a>d?0:d-a>>>0,a>>>=0;for(var o=b(l);++s<l;)o[s]=e[s+a];return o}function U8(e,a){var d;return ca(e,function(s,l,o){return d=a(s,l,o),!d}),!!d}function Y2(e,a,d){var s=0,l=e==null?s:e.length;if(typeof a=="number"&&a===a&&l<=M3){for(;s<l;){var o=s+l>>>1,c=e[o];c!==null&&!pe(c)&&(d?c<=a:c<a)?s=o+1:l=o}return l}return $d(e,a,Y1,d)}function $d(e,a,d,s){var l=0,o=e==null?0:e.length;if(o===0)return 0;a=d(a);for(var c=a!==a,h=a===null,g=pe(a),S=a===p;l<o;){var T=$2((l+o)/2),x=d(e[T]),E=x!==p,O=x===null,z=x===x,K=pe(x);if(c)var $=s||z;else S?$=z&&(s||E):h?$=z&&E&&(s||!O):g?$=z&&E&&!O&&(s||!K):O||K?$=!1:$=s?x<=a:x<a;$?l=T+1:o=T}return B1(o,U3)}function z4(e,a){for(var d=-1,s=e.length,l=0,o=[];++d<s;){var c=e[d],h=a?a(c):c;if(!d||!xe(h,g)){var g=h;o[l++]=c===0?0:c}}return o}function $4(e){return typeof e=="number"?e:pe(e)?Ja:+e}function ae(e){if(typeof e=="string")return e;if(j(e))return m1(e,ae)+"";if(pe(e))return y4?y4.call(e):"";var a=e+"";return a=="0"&&1/e==-w1?"-0":a}function fa(e,a,d){var s=-1,l=C2,o=e.length,c=!0,h=[],g=h;if(d)c=!1,l=wd;else if(o>=i){var S=a?null:J8(e);if(S)return k2(S);c=!1,l=qp,g=new Ca}else g=a?[]:h;e:for(;++s<o;){var T=e[s],x=a?a(T):T;if(T=d||T!==0?T:0,c&&x===x){for(var E=g.length;E--;)if(g[E]===x)continue e;a&&g.push(x),h.push(T)}else l(g,x,d)||(g!==h&&g.push(x),h.push(T))}return h}function Wd(e,a){return a=ha(a,e),e=_i(e,a),e==null||delete e[Oe(fe(a))]}function W4(e,a,d,s){return Yp(e,a,d(ka(e,a)),s)}function Q2(e,a,d,s){for(var l=e.length,o=s?l:-1;(s?o--:++o<l)&&a(e[o],o,e););return d?ce(e,s?0:o,s?o+1:l):ce(e,s?o+1:0,s?l:o)}function j4(e,a){var d=e;return d instanceof X&&(d=d.value()),gd(a,function(s,l){return l.func.apply(l.thisArg,la([s],l.args))},d)}function jd(e,a,d){var s=e.length;if(s<2)return s?fa(e[0]):[];for(var l=-1,o=b(s);++l<s;)for(var c=e[l],h=-1;++h<s;)h!=l&&(o[l]=Kp(o[l]||c,e[h],a,d));return fa(O1(o,1),a,d)}function H4(e,a,d){for(var s=-1,l=e.length,o=a.length,c={};++s<l;){var h=s<o?a[s]:p;d(c,e[s],h)}return c}function Hd(e){return g1(e)?e:[]}function Gd(e){return typeof e=="function"?e:Y1}function ha(e,a){return j(e)?e:p0(e,a)?[e]:yi(d1(e))}var M8=Z;function va(e,a,d){var s=e.length;return d=d===p?s:d,!a&&d>=s?e:ce(e,a,d)}var G4=A5||function(e){return E1.clearTimeout(e)};function K4(e,a){if(a)return e.slice();var d=e.length,s=h4?h4(d):new e.constructor(d);return e.copy(s),s}function Kd(e){var a=new e.constructor(e.byteLength);return new B2(a).set(new B2(e)),a}function q8(e,a){var d=a?Kd(e.buffer):e.buffer;return new e.constructor(d,e.byteOffset,e.byteLength)}function z8(e){var a=new e.constructor(e.source,P6.exec(e));return a.lastIndex=e.lastIndex,a}function $8(e){return Hp?r1(Hp.call(e)):{}}function Z4(e,a){var d=a?Kd(e.buffer):e.buffer;return new e.constructor(d,e.byteOffset,e.length)}function J4(e,a){if(e!==a){var d=e!==p,s=e===null,l=e===e,o=pe(e),c=a!==p,h=a===null,g=a===a,S=pe(a);if(!h&&!S&&!o&&e>a||o&&c&&g&&!h&&!S||s&&c&&g||!d&&g||!l)return 1;if(!s&&!o&&!S&&e<a||S&&d&&l&&!s&&!o||h&&d&&l||!c&&l||!g)return-1}return 0}function W8(e,a,d){for(var s=-1,l=e.criteria,o=a.criteria,c=l.length,h=d.length;++s<c;){var g=J4(l[s],o[s]);if(g){if(s>=h)return g;var S=d[s];return g*(S=="desc"?-1:1)}}return e.index-a.index}function Y4(e,a,d,s){for(var l=-1,o=e.length,c=d.length,h=-1,g=a.length,S=D1(o-c,0),T=b(g+S),x=!s;++h<g;)T[h]=a[h];for(;++l<c;)(x||l<o)&&(T[d[l]]=e[l]);for(;S--;)T[h++]=e[l++];return T}function Q4(e,a,d,s){for(var l=-1,o=e.length,c=-1,h=d.length,g=-1,S=a.length,T=D1(o-h,0),x=b(T+S),E=!s;++l<T;)x[l]=e[l];for(var O=l;++g<S;)x[O+g]=a[g];for(;++c<h;)(E||l<o)&&(x[O+d[c]]=e[l++]);return x}function K1(e,a){var d=-1,s=e.length;for(a||(a=b(s));++d<s;)a[d]=e[d];return a}function Re(e,a,d,s){var l=!d;d||(d={});for(var o=-1,c=a.length;++o<c;){var h=a[o],g=s?s(d[h],e[h],h,d,e):p;g===p&&(g=e[h]),l?Ke(d,h,g):Gp(d,h,g)}return d}function j8(e,a){return Re(e,a0(e),a)}function H8(e,a){return Re(e,oi(e),a)}function X2(e,a){return function(d,s){var l=j(d)?Yn:f8,o=a?a():{};return l(d,e,M(s,2),o)}}function np(e){return Z(function(a,d){var s=-1,l=d.length,o=l>1?d[l-1]:p,c=l>2?d[2]:p;for(o=e.length>3&&typeof o=="function"?(l--,o):p,c&&z1(d[0],d[1],c)&&(o=l<3?p:o,l=1),a=r1(a);++s<l;){var h=d[s];h&&e(a,h,s,o)}return a})}function X4(e,a){return function(d,s){if(d==null)return d;if(!Z1(d))return e(d,s);for(var l=d.length,o=a?l:-1,c=r1(d);(a?o--:++o<l)&&s(c[o],o,c)!==!1;);return d}}function ei(e){return function(a,d,s){for(var l=-1,o=r1(a),c=s(a),h=c.length;h--;){var g=c[e?h:++l];if(d(o[g],g,o)===!1)break}return a}}function G8(e,a,d){var s=a&C,l=Qp(e);function o(){var c=this&&this!==E1&&this instanceof o?l:e;return c.apply(s?d:this,arguments)}return o}function ai(e){return function(a){a=d1(a);var d=ap(a)?Ne(a):p,s=d?d[0]:a.charAt(0),l=d?va(d,1).join(""):a.slice(1);return s[e]()+l}}function sp(e){return function(a){return gd(Xi(Qi(a).replace(Fn,"")),e,"")}}function Qp(e){return function(){var a=arguments;switch(a.length){case 0:return new e;case 1:return new e(a[0]);case 2:return new e(a[0],a[1]);case 3:return new e(a[0],a[1],a[2]);case 4:return new e(a[0],a[1],a[2],a[3]);case 5:return new e(a[0],a[1],a[2],a[3],a[4]);case 6:return new e(a[0],a[1],a[2],a[3],a[4],a[5]);case 7:return new e(a[0],a[1],a[2],a[3],a[4],a[5],a[6])}var d=rp(e.prototype),s=e.apply(d,a);return u1(s)?s:d}}function K8(e,a,d){var s=Qp(e);function l(){for(var o=arguments.length,c=b(o),h=o,g=lp(l);h--;)c[h]=arguments[h];var S=o<3&&c[0]!==g&&c[o-1]!==g?[]:ma(c,g);if(o-=S.length,o<d)return ri(e,a,et,l.placeholder,p,c,S,p,p,d-o);var T=this&&this!==E1&&this instanceof l?s:e;return X1(T,this,c)}return l}function pi(e){return function(a,d,s){var l=r1(a);if(!Z1(a)){var o=M(d,3);a=V1(a),d=function(h){return o(l[h],h,l)}}var c=e(a,d,s);return c>-1?l[o?a[c]:c]:p}}function ti(e){return Je(function(a){var d=a.length,s=d,l=oe.prototype.thru;for(e&&a.reverse();s--;){var o=a[s];if(typeof o!="function")throw new me(n);if(l&&!c&&dt(o)=="wrapper")var c=new oe([],!0)}for(s=c?s:d;++s<d;){o=a[s];var h=dt(o),g=h=="wrapper"?Xd(o):p;g&&t0(g[0])&&g[1]==(H|P|B|e1)&&!g[4].length&&g[9]==1?c=c[dt(g[0])].apply(c,g[3]):c=o.length==1&&t0(o)?c[h]():c.thru(o)}return function(){var S=arguments,T=S[0];if(c&&S.length==1&&j(T))return c.plant(T).value();for(var x=0,E=d?a[x].apply(this,S):T;++x<d;)E=a[x].call(this,E);return E}})}function et(e,a,d,s,l,o,c,h,g,S){var T=a&H,x=a&C,E=a&R,O=a&(P|L),z=a&N1,K=E?p:Qp(e);function $(){for(var Y=arguments.length,p1=b(Y),te=Y;te--;)p1[te]=arguments[te];if(O)var $1=lp($),de=r5(p1,$1);if(s&&(p1=Y4(p1,s,l,O)),o&&(p1=Q4(p1,o,c,O)),Y-=de,O&&Y<S){var b1=ma(p1,$1);return ri(e,a,et,$.placeholder,d,p1,b1,h,g,S-Y)}var De=x?d:this,ea=E?De[e]:e;return Y=p1.length,h?p1=f7(p1,h):z&&Y>1&&p1.reverse(),T&&g<Y&&(p1.length=g),this&&this!==E1&&this instanceof $&&(ea=K||Qp(ea)),ea.apply(De,p1)}return $}function di(e,a){return function(d,s){return S8(d,e,a(s),{})}}function at(e,a){return function(d,s){var l;if(d===p&&s===p)return a;if(d!==p&&(l=d),s!==p){if(l===p)return s;typeof d=="string"||typeof s=="string"?(d=ae(d),s=ae(s)):(d=$4(d),s=$4(s)),l=e(d,s)}return l}}function Zd(e){return Je(function(a){return a=m1(a,ee(M())),Z(function(d){var s=this;return e(a,function(l){return X1(l,s,d)})})})}function pt(e,a){a=a===p?" ":ae(a);var d=a.length;if(d<2)return d?zd(a,e):a;var s=zd(a,z2(e/pp(a)));return ap(a)?va(Ne(s),0,e).join(""):s.slice(0,e)}function Z8(e,a,d,s){var l=a&C,o=Qp(e);function c(){for(var h=-1,g=arguments.length,S=-1,T=s.length,x=b(T+g),E=this&&this!==E1&&this instanceof c?o:e;++S<T;)x[S]=s[S];for(;g--;)x[S++]=arguments[++h];return X1(E,l?d:this,x)}return c}function ii(e){return function(a,d,s){return s&&typeof s!="number"&&z1(a,d,s)&&(d=s=p),a=Xe(a),d===p?(d=a,a=0):d=Xe(d),s=s===p?a<d?1:-1:Xe(s),O8(a,d,s,e)}}function tt(e){return function(a,d){return typeof a=="string"&&typeof d=="string"||(a=he(a),d=he(d)),e(a,d)}}function ri(e,a,d,s,l,o,c,h,g,S){var T=a&P,x=T?c:p,E=T?p:c,O=T?o:p,z=T?p:o;a|=T?B:U,a&=~(T?U:B),a&q||(a&=~(C|R));var K=[e,a,l,O,x,z,E,h,g,S],$=d.apply(p,K);return t0(e)&&wi($,K),$.placeholder=s,gi($,e,a)}function Jd(e){var a=x1[e];return function(d,s){if(d=he(d),s=s==null?0:B1(G(s),292),s&&g4(d)){var l=(d1(d)+"e").split("e"),o=a(l[0]+"e"+(+l[1]+s));return l=(d1(o)+"e").split("e"),+(l[0]+"e"+(+l[1]-s))}return a(d)}}var J8=dp&&1/k2(new dp([,-0]))[1]==w1?function(e){return new dp(e)}:w0;function ni(e){return function(a){var d=U1(a);return d==ye?Dd(a):d==Se?c5(a):i5(a,e(a))}}function Ze(e,a,d,s,l,o,c,h){var g=a&R;if(!g&&typeof e!="function")throw new me(n);var S=s?s.length:0;if(S||(a&=~(B|U),s=l=p),c=c===p?c:D1(G(c),0),h=h===p?h:G(h),S-=l?l.length:0,a&U){var T=s,x=l;s=l=p}var E=g?p:Xd(e),O=[e,a,d,s,l,T,x,o,c,h];if(E&&o7(O,E),e=O[0],a=O[1],d=O[2],s=O[3],l=O[4],h=O[9]=O[9]===p?g?0:e.length:D1(O[9]-S,0),!h&&a&(P|L)&&(a&=~(P|L)),!a||a==C)var z=G8(e,a,d);else a==P||a==L?z=K8(e,a,h):(a==B||a==(C|B))&&!l.length?z=Z8(e,a,d,s):z=et.apply(p,O);var K=E?q4:wi;return gi(K(z,O),e,a)}function si(e,a,d,s){return e===p||xe(e,tp[d])&&!i1.call(s,d)?a:e}function li(e,a,d,s,l,o){return u1(e)&&u1(a)&&(o.set(a,e),J2(e,a,p,li,o),o.delete(a)),e}function Y8(e){return a2(e)?p:e}function mi(e,a,d,s,l,o){var c=d&V,h=e.length,g=a.length;if(h!=g&&!(c&&g>h))return!1;var S=o.get(e),T=o.get(a);if(S&&T)return S==a&&T==e;var x=-1,E=!0,O=d&F?new Ca:p;for(o.set(e,a),o.set(a,e);++x<h;){var z=e[x],K=a[x];if(s)var $=c?s(K,z,x,a,e,o):s(z,K,x,e,a,o);if($!==p){if($)continue;E=!1;break}if(O){if(!bd(a,function(Y,p1){if(!qp(O,p1)&&(z===Y||l(z,Y,d,s,o)))return O.push(p1)})){E=!1;break}}else if(!(z===K||l(z,K,d,s,o))){E=!1;break}}return o.delete(e),o.delete(a),E}function Q8(e,a,d,s,l,o,c){switch(d){case Qa:if(e.byteLength!=a.byteLength||e.byteOffset!=a.byteOffset)return!1;e=e.buffer,a=a.buffer;case Mp:return!(e.byteLength!=a.byteLength||!o(new B2(e),new B2(a)));case Op:case Lp:case Ip:return xe(+e,+a);case T2:return e.name==a.name&&e.message==a.message;case Fp:case Bp:return e==a+"";case ye:var h=Dd;case Se:var g=s&V;if(h||(h=k2),e.size!=a.size&&!g)return!1;var S=c.get(e);if(S)return S==a;s|=F,c.set(e,a);var T=mi(h(e),h(a),s,l,o,c);return c.delete(e),T;case D2:if(Hp)return Hp.call(e)==Hp.call(a)}return!1}function X8(e,a,d,s,l,o){var c=d&V,h=Yd(e),g=h.length,S=Yd(a),T=S.length;if(g!=T&&!c)return!1;for(var x=g;x--;){var E=h[x];if(!(c?E in a:i1.call(a,E)))return!1}var O=o.get(e),z=o.get(a);if(O&&z)return O==a&&z==e;var K=!0;o.set(e,a),o.set(a,e);for(var $=c;++x<g;){E=h[x];var Y=e[E],p1=a[E];if(s)var te=c?s(p1,Y,E,a,e,o):s(Y,p1,E,e,a,o);if(!(te===p?Y===p1||l(Y,p1,d,s,o):te)){K=!1;break}$||($=E=="constructor")}if(K&&!$){var $1=e.constructor,de=a.constructor;$1!=de&&"constructor"in e&&"constructor"in a&&!(typeof $1=="function"&&$1 instanceof $1&&typeof de=="function"&&de instanceof de)&&(K=!1)}return o.delete(e),o.delete(a),K}function Je(e){return i0(vi(e,p,xi),e+"")}function Yd(e){return C4(e,V1,a0)}function Qd(e){return C4(e,J1,oi)}var Xd=W2?function(e){return W2.get(e)}:w0;function dt(e){for(var a=e.name+"",d=ip[a],s=i1.call(ip,a)?d.length:0;s--;){var l=d[s],o=l.func;if(o==null||o==e)return l.name}return a}function lp(e){var a=i1.call(m,"placeholder")?m:e;return a.placeholder}function M(){var e=m.iteratee||v0;return e=e===v0?R4:e,arguments.length?e(arguments[0],arguments[1]):e}function it(e,a){var d=e.__data__;return n7(a)?d[typeof a=="string"?"string":"hash"]:d.map}function e0(e){for(var a=V1(e),d=a.length;d--;){var s=a[d],l=e[s];a[d]=[s,l,fi(l)]}return a}function Ra(e,a){var d=m5(e,a);return k4(d)?d:p}function e7(e){var a=i1.call(e,Ea),d=e[Ea];try{e[Ea]=p;var s=!0}catch{}var l=I2.call(e);return s&&(a?e[Ea]=d:delete e[Ea]),l}var a0=Ed?function(e){return e==null?[]:(e=r1(e),sa(Ed(e),function(a){return _4.call(e,a)}))}:g0,oi=Ed?function(e){for(var a=[];e;)la(a,a0(e)),e=U2(e);return a}:g0,U1=q1;(Vd&&U1(new Vd(new ArrayBuffer(1)))!=Qa||$p&&U1(new $p)!=ye||Cd&&U1(Cd.resolve())!=A6||dp&&U1(new dp)!=Se||Wp&&U1(new Wp)!=Up)&&(U1=function(e){var a=q1(e),d=a==je?e.constructor:p,s=d?Oa(d):"";if(s)switch(s){case I5:return Qa;case F5:return ye;case B5:return A6;case U5:return Se;case M5:return Up}return a});function a7(e,a,d){for(var s=-1,l=d.length;++s<l;){var o=d[s],c=o.size;switch(o.type){case"drop":e+=c;break;case"dropRight":a-=c;break;case"take":a=B1(a,e+c);break;case"takeRight":e=D1(e,a-c);break}}return{start:e,end:a}}function p7(e){var a=e.match(sn);return a?a[1].split(ln):[]}function ui(e,a,d){a=ha(a,e);for(var s=-1,l=a.length,o=!1;++s<l;){var c=Oe(a[s]);if(!(o=e!=null&&d(e,c)))break;e=e[c]}return o||++s!=l?o:(l=e==null?0:e.length,!!l&&ut(l)&&Ye(c,l)&&(j(e)||La(e)))}function t7(e){var a=e.length,d=new e.constructor(a);return a&&typeof e[0]=="string"&&i1.call(e,"index")&&(d.index=e.index,d.input=e.input),d}function ci(e){return typeof e.constructor=="function"&&!Xp(e)?rp(U2(e)):{}}function d7(e,a,d){var s=e.constructor;switch(a){case Mp:return Kd(e);case Op:case Lp:return new s(+e);case Qa:return q8(e,d);case ed:case ad:case pd:case td:case dd:case id:case rd:case nd:case sd:return Z4(e,d);case ye:return new s;case Ip:case Bp:return new s(e);case Fp:return z8(e);case Se:return new s;case D2:return $8(e)}}function i7(e,a){var d=a.length;if(!d)return e;var s=d-1;return a[s]=(d>1?"& ":"")+a[s],a=a.join(d>2?", ":" "),e.replace(nn,`{
/* [wrapped with `+a+`] */
`)}function r7(e){return j(e)||La(e)||!!(w4&&e&&e[w4])}function Ye(e,a){var d=typeof e;return a=a??G1,!!a&&(d=="number"||d!="symbol"&&wn.test(e))&&e>-1&&e%1==0&&e<a}function z1(e,a,d){if(!u1(d))return!1;var s=typeof a;return(s=="number"?Z1(d)&&Ye(a,d.length):s=="string"&&a in d)?xe(d[a],e):!1}function p0(e,a){if(j(e))return!1;var d=typeof e;return d=="number"||d=="symbol"||d=="boolean"||e==null||pe(e)?!0:pn.test(e)||!an.test(e)||a!=null&&e in r1(a)}function n7(e){var a=typeof e;return a=="string"||a=="number"||a=="symbol"||a=="boolean"?e!=="__proto__":e===null}function t0(e){var a=dt(e),d=m[a];if(typeof d!="function"||!(a in X.prototype))return!1;if(e===d)return!0;var s=Xd(d);return!!s&&e===s[0]}function s7(e){return!!f4&&f4 in e}var l7=O2?Qe:b0;function Xp(e){var a=e&&e.constructor,d=typeof a=="function"&&a.prototype||tp;return e===d}function fi(e){return e===e&&!u1(e)}function hi(e,a){return function(d){return d==null?!1:d[e]===a&&(a!==p||e in r1(d))}}function m7(e){var a=mt(e,function(s){return d.size===_&&d.clear(),s}),d=a.cache;return a}function o7(e,a){var d=e[1],s=a[1],l=d|s,o=l<(C|R|H),c=s==H&&d==P||s==H&&d==e1&&e[7].length<=a[8]||s==(H|e1)&&a[7].length<=a[8]&&d==P;if(!(o||c))return e;s&C&&(e[2]=a[2],l|=d&C?0:q);var h=a[3];if(h){var g=e[3];e[3]=g?Y4(g,h,a[4]):h,e[4]=g?ma(e[3],f):a[4]}return h=a[5],h&&(g=e[5],e[5]=g?Q4(g,h,a[6]):h,e[6]=g?ma(e[5],f):a[6]),h=a[7],h&&(e[7]=h),s&H&&(e[8]=e[8]==null?a[8]:B1(e[8],a[8])),e[9]==null&&(e[9]=a[9]),e[0]=a[0],e[1]=l,e}function u7(e){var a=[];if(e!=null)for(var d in r1(e))a.push(d);return a}function c7(e){return I2.call(e)}function vi(e,a,d){return a=D1(a===p?e.length-1:a,0),function(){for(var s=arguments,l=-1,o=D1(s.length-a,0),c=b(o);++l<o;)c[l]=s[a+l];l=-1;for(var h=b(a+1);++l<a;)h[l]=s[l];return h[a]=d(c),X1(e,this,h)}}function _i(e,a){return a.length<2?e:ka(e,ce(a,0,-1))}function f7(e,a){for(var d=e.length,s=B1(a.length,d),l=K1(e);s--;){var o=a[s];e[s]=Ye(o,d)?l[o]:p}return e}function d0(e,a){if(!(a==="constructor"&&typeof e[a]=="function")&&a!="__proto__")return e[a]}var wi=bi(q4),e2=V5||function(e,a){return E1.setTimeout(e,a)},i0=bi(F8);function gi(e,a,d){var s=a+"";return i0(e,i7(s,h7(p7(s),d)))}function bi(e){var a=0,d=0;return function(){var s=R5(),l=T1-(s-d);if(d=s,l>0){if(++a>=R1)return arguments[0]}else a=0;return e.apply(p,arguments)}}function rt(e,a){var d=-1,s=e.length,l=s-1;for(a=a===p?s:a;++d<a;){var o=qd(d,l),c=e[o];e[o]=e[d],e[d]=c}return e.length=a,e}var yi=m7(function(e){var a=[];return e.charCodeAt(0)===46&&a.push(""),e.replace(tn,function(d,s,l,o){a.push(l?o.replace(un,"$1"):s||d)}),a});function Oe(e){if(typeof e=="string"||pe(e))return e;var a=e+"";return a=="0"&&1/e==-w1?"-0":a}function Oa(e){if(e!=null){try{return L2.call(e)}catch{}try{return e+""}catch{}}return""}function h7(e,a){return le(q3,function(d){var s="_."+d[0];a&d[1]&&!C2(e,s)&&e.push(s)}),e.sort()}function Si(e){if(e instanceof X)return e.clone();var a=new oe(e.__wrapped__,e.__chain__);return a.__actions__=K1(e.__actions__),a.__index__=e.__index__,a.__values__=e.__values__,a}function v7(e,a,d){(d?z1(e,a,d):a===p)?a=1:a=D1(G(a),0);var s=e==null?0:e.length;if(!s||a<1)return[];for(var l=0,o=0,c=b(z2(s/a));l<s;)c[o++]=ce(e,l,l+=a);return c}function _7(e){for(var a=-1,d=e==null?0:e.length,s=0,l=[];++a<d;){var o=e[a];o&&(l[s++]=o)}return l}function w7(){var e=arguments.length;if(!e)return[];for(var a=b(e-1),d=arguments[0],s=e;s--;)a[s-1]=arguments[s];return la(j(d)?K1(d):[d],O1(a,1))}var g7=Z(function(e,a){return g1(e)?Kp(e,O1(a,1,g1,!0)):[]}),b7=Z(function(e,a){var d=fe(a);return g1(d)&&(d=p),g1(e)?Kp(e,O1(a,1,g1,!0),M(d,2)):[]}),y7=Z(function(e,a){var d=fe(a);return g1(d)&&(d=p),g1(e)?Kp(e,O1(a,1,g1,!0),p,d):[]});function S7(e,a,d){var s=e==null?0:e.length;return s?(a=d||a===p?1:G(a),ce(e,a<0?0:a,s)):[]}function N7(e,a,d){var s=e==null?0:e.length;return s?(a=d||a===p?1:G(a),a=s-a,ce(e,0,a<0?0:a)):[]}function T7(e,a){return e&&e.length?Q2(e,M(a,3),!0,!0):[]}function x7(e,a){return e&&e.length?Q2(e,M(a,3),!0):[]}function D7(e,a,d,s){var l=e==null?0:e.length;return l?(d&&typeof d!="number"&&z1(e,a,d)&&(d=0,s=l),w8(e,a,d,s)):[]}function Ni(e,a,d){var s=e==null?0:e.length;if(!s)return-1;var l=d==null?0:G(d);return l<0&&(l=D1(s+l,0)),P2(e,M(a,3),l)}function Ti(e,a,d){var s=e==null?0:e.length;if(!s)return-1;var l=s-1;return d!==p&&(l=G(d),l=d<0?D1(s+l,0):B1(l,s-1)),P2(e,M(a,3),l,!0)}function xi(e){var a=e==null?0:e.length;return a?O1(e,1):[]}function A7(e){var a=e==null?0:e.length;return a?O1(e,w1):[]}function E7(e,a){var d=e==null?0:e.length;return d?(a=a===p?1:G(a),O1(e,a)):[]}function V7(e){for(var a=-1,d=e==null?0:e.length,s={};++a<d;){var l=e[a];s[l[0]]=l[1]}return s}function Di(e){return e&&e.length?e[0]:p}function C7(e,a,d){var s=e==null?0:e.length;if(!s)return-1;var l=d==null?0:G(d);return l<0&&(l=D1(s+l,0)),ep(e,a,l)}function P7(e){var a=e==null?0:e.length;return a?ce(e,0,-1):[]}var k7=Z(function(e){var a=m1(e,Hd);return a.length&&a[0]===e[0]?Id(a):[]}),R7=Z(function(e){var a=fe(e),d=m1(e,Hd);return a===fe(d)?a=p:d.pop(),d.length&&d[0]===e[0]?Id(d,M(a,2)):[]}),O7=Z(function(e){var a=fe(e),d=m1(e,Hd);return a=typeof a=="function"?a:p,a&&d.pop(),d.length&&d[0]===e[0]?Id(d,p,a):[]});function L7(e,a){return e==null?"":P5.call(e,a)}function fe(e){var a=e==null?0:e.length;return a?e[a-1]:p}function I7(e,a,d){var s=e==null?0:e.length;if(!s)return-1;var l=s;return d!==p&&(l=G(d),l=l<0?D1(s+l,0):B1(l,s-1)),a===a?h5(e,a,l):P2(e,r4,l,!0)}function F7(e,a){return e&&e.length?F4(e,G(a)):p}var B7=Z(Ai);function Ai(e,a){return e&&e.length&&a&&a.length?Md(e,a):e}function U7(e,a,d){return e&&e.length&&a&&a.length?Md(e,a,M(d,2)):e}function M7(e,a,d){return e&&e.length&&a&&a.length?Md(e,a,p,d):e}var q7=Je(function(e,a){var d=e==null?0:e.length,s=kd(e,a);return M4(e,m1(a,function(l){return Ye(l,d)?+l:l}).sort(J4)),s});function z7(e,a){var d=[];if(!(e&&e.length))return d;var s=-1,l=[],o=e.length;for(a=M(a,3);++s<o;){var c=e[s];a(c,s,e)&&(d.push(c),l.push(s))}return M4(e,l),d}function r0(e){return e==null?e:L5.call(e)}function $7(e,a,d){var s=e==null?0:e.length;return s?(d&&typeof d!="number"&&z1(e,a,d)?(a=0,d=s):(a=a==null?0:G(a),d=d===p?s:G(d)),ce(e,a,d)):[]}function W7(e,a){return Y2(e,a)}function j7(e,a,d){return $d(e,a,M(d,2))}function H7(e,a){var d=e==null?0:e.length;if(d){var s=Y2(e,a);if(s<d&&xe(e[s],a))return s}return-1}function G7(e,a){return Y2(e,a,!0)}function K7(e,a,d){return $d(e,a,M(d,2),!0)}function Z7(e,a){var d=e==null?0:e.length;if(d){var s=Y2(e,a,!0)-1;if(xe(e[s],a))return s}return-1}function J7(e){return e&&e.length?z4(e):[]}function Y7(e,a){return e&&e.length?z4(e,M(a,2)):[]}function Q7(e){var a=e==null?0:e.length;return a?ce(e,1,a):[]}function X7(e,a,d){return e&&e.length?(a=d||a===p?1:G(a),ce(e,0,a<0?0:a)):[]}function es(e,a,d){var s=e==null?0:e.length;return s?(a=d||a===p?1:G(a),a=s-a,ce(e,a<0?0:a,s)):[]}function as(e,a){return e&&e.length?Q2(e,M(a,3),!1,!0):[]}function ps(e,a){return e&&e.length?Q2(e,M(a,3)):[]}var ts=Z(function(e){return fa(O1(e,1,g1,!0))}),ds=Z(function(e){var a=fe(e);return g1(a)&&(a=p),fa(O1(e,1,g1,!0),M(a,2))}),is=Z(function(e){var a=fe(e);return a=typeof a=="function"?a:p,fa(O1(e,1,g1,!0),p,a)});function rs(e){return e&&e.length?fa(e):[]}function ns(e,a){return e&&e.length?fa(e,M(a,2)):[]}function ss(e,a){return a=typeof a=="function"?a:p,e&&e.length?fa(e,p,a):[]}function n0(e){if(!(e&&e.length))return[];var a=0;return e=sa(e,function(d){if(g1(d))return a=D1(d.length,a),!0}),Td(a,function(d){return m1(e,yd(d))})}function Ei(e,a){if(!(e&&e.length))return[];var d=n0(e);return a==null?d:m1(d,function(s){return X1(a,p,s)})}var ls=Z(function(e,a){return g1(e)?Kp(e,a):[]}),ms=Z(function(e){return jd(sa(e,g1))}),os=Z(function(e){var a=fe(e);return g1(a)&&(a=p),jd(sa(e,g1),M(a,2))}),us=Z(function(e){var a=fe(e);return a=typeof a=="function"?a:p,jd(sa(e,g1),p,a)}),cs=Z(n0);function fs(e,a){return H4(e||[],a||[],Gp)}function hs(e,a){return H4(e||[],a||[],Yp)}var vs=Z(function(e){var a=e.length,d=a>1?e[a-1]:p;return d=typeof d=="function"?(e.pop(),d):p,Ei(e,d)});function Vi(e){var a=m(e);return a.__chain__=!0,a}function _s(e,a){return a(e),e}function nt(e,a){return a(e)}var ws=Je(function(e){var a=e.length,d=a?e[0]:0,s=this.__wrapped__,l=function(o){return kd(o,e)};return a>1||this.__actions__.length||!(s instanceof X)||!Ye(d)?this.thru(l):(s=s.slice(d,+d+(a?1:0)),s.__actions__.push({func:nt,args:[l],thisArg:p}),new oe(s,this.__chain__).thru(function(o){return a&&!o.length&&o.push(p),o}))});function gs(){return Vi(this)}function bs(){return new oe(this.value(),this.__chain__)}function ys(){this.__values__===p&&(this.__values__=$i(this.value()));var e=this.__index__>=this.__values__.length,a=e?p:this.__values__[this.__index__++];return{done:e,value:a}}function Ss(){return this}function Ns(e){for(var a,d=this;d instanceof H2;){var s=Si(d);s.__index__=0,s.__values__=p,a?l.__wrapped__=s:a=s;var l=s;d=d.__wrapped__}return l.__wrapped__=e,a}function Ts(){var e=this.__wrapped__;if(e instanceof X){var a=e;return this.__actions__.length&&(a=new X(this)),a=a.reverse(),a.__actions__.push({func:nt,args:[r0],thisArg:p}),new oe(a,this.__chain__)}return this.thru(r0)}function xs(){return j4(this.__wrapped__,this.__actions__)}var Ds=X2(function(e,a,d){i1.call(e,d)?++e[d]:Ke(e,d,1)});function As(e,a,d){var s=j(e)?d4:_8;return d&&z1(e,a,d)&&(a=p),s(e,M(a,3))}function Es(e,a){var d=j(e)?sa:E4;return d(e,M(a,3))}var Vs=pi(Ni),Cs=pi(Ti);function Ps(e,a){return O1(st(e,a),1)}function ks(e,a){return O1(st(e,a),w1)}function Rs(e,a,d){return d=d===p?1:G(d),O1(st(e,a),d)}function Ci(e,a){var d=j(e)?le:ca;return d(e,M(a,3))}function Pi(e,a){var d=j(e)?Qn:A4;return d(e,M(a,3))}var Os=X2(function(e,a,d){i1.call(e,d)?e[d].push(a):Ke(e,d,[a])});function Ls(e,a,d,s){e=Z1(e)?e:op(e),d=d&&!s?G(d):0;var l=e.length;return d<0&&(d=D1(l+d,0)),ct(e)?d<=l&&e.indexOf(a,d)>-1:!!l&&ep(e,a,d)>-1}var Is=Z(function(e,a,d){var s=-1,l=typeof a=="function",o=Z1(e)?b(e.length):[];return ca(e,function(c){o[++s]=l?X1(a,c,d):Zp(c,a,d)}),o}),Fs=X2(function(e,a,d){Ke(e,d,a)});function st(e,a){var d=j(e)?m1:O4;return d(e,M(a,3))}function Bs(e,a,d,s){return e==null?[]:(j(a)||(a=a==null?[]:[a]),d=s?p:d,j(d)||(d=d==null?[]:[d]),B4(e,a,d))}var Us=X2(function(e,a,d){e[d?0:1].push(a)},function(){return[[],[]]});function Ms(e,a,d){var s=j(e)?gd:s4,l=arguments.length<3;return s(e,M(a,4),d,l,ca)}function qs(e,a,d){var s=j(e)?Xn:s4,l=arguments.length<3;return s(e,M(a,4),d,l,A4)}function zs(e,a){var d=j(e)?sa:E4;return d(e,ot(M(a,3)))}function $s(e){var a=j(e)?N4:L8;return a(e)}function Ws(e,a,d){(d?z1(e,a,d):a===p)?a=1:a=G(a);var s=j(e)?u8:I8;return s(e,a)}function js(e){var a=j(e)?c8:B8;return a(e)}function Hs(e){if(e==null)return 0;if(Z1(e))return ct(e)?pp(e):e.length;var a=U1(e);return a==ye||a==Se?e.size:Bd(e).length}function Gs(e,a,d){var s=j(e)?bd:U8;return d&&z1(e,a,d)&&(a=p),s(e,M(a,3))}var Ks=Z(function(e,a){if(e==null)return[];var d=a.length;return d>1&&z1(e,a[0],a[1])?a=[]:d>2&&z1(a[0],a[1],a[2])&&(a=[a[0]]),B4(e,O1(a,1),[])}),lt=E5||function(){return E1.Date.now()};function Zs(e,a){if(typeof a!="function")throw new me(n);return e=G(e),function(){if(--e<1)return a.apply(this,arguments)}}function ki(e,a,d){return a=d?p:a,a=e&&a==null?e.length:a,Ze(e,H,p,p,p,p,a)}function Ri(e,a){var d;if(typeof a!="function")throw new me(n);return e=G(e),function(){return--e>0&&(d=a.apply(this,arguments)),e<=1&&(a=p),d}}var s0=Z(function(e,a,d){var s=C;if(d.length){var l=ma(d,lp(s0));s|=B}return Ze(e,s,a,d,l)}),Oi=Z(function(e,a,d){var s=C|R;if(d.length){var l=ma(d,lp(Oi));s|=B}return Ze(a,s,e,d,l)});function Li(e,a,d){a=d?p:a;var s=Ze(e,P,p,p,p,p,p,a);return s.placeholder=Li.placeholder,s}function Ii(e,a,d){a=d?p:a;var s=Ze(e,L,p,p,p,p,p,a);return s.placeholder=Ii.placeholder,s}function Fi(e,a,d){var s,l,o,c,h,g,S=0,T=!1,x=!1,E=!0;if(typeof e!="function")throw new me(n);a=he(a)||0,u1(d)&&(T=!!d.leading,x="maxWait"in d,o=x?D1(he(d.maxWait)||0,a):o,E="trailing"in d?!!d.trailing:E);function O(b1){var De=s,ea=l;return s=l=p,S=b1,c=e.apply(ea,De),c}function z(b1){return S=b1,h=e2(Y,a),T?O(b1):c}function K(b1){var De=b1-g,ea=b1-S,pr=a-De;return x?B1(pr,o-ea):pr}function $(b1){var De=b1-g,ea=b1-S;return g===p||De>=a||De<0||x&&ea>=o}function Y(){var b1=lt();if($(b1))return p1(b1);h=e2(Y,K(b1))}function p1(b1){return h=p,E&&s?O(b1):(s=l=p,c)}function te(){h!==p&&G4(h),S=0,s=g=l=h=p}function $1(){return h===p?c:p1(lt())}function de(){var b1=lt(),De=$(b1);if(s=arguments,l=this,g=b1,De){if(h===p)return z(g);if(x)return G4(h),h=e2(Y,a),O(g)}return h===p&&(h=e2(Y,a)),c}return de.cancel=te,de.flush=$1,de}var Js=Z(function(e,a){return D4(e,1,a)}),Ys=Z(function(e,a,d){return D4(e,he(a)||0,d)});function Qs(e){return Ze(e,N1)}function mt(e,a){if(typeof e!="function"||a!=null&&typeof a!="function")throw new me(n);var d=function(){var s=arguments,l=a?a.apply(this,s):s[0],o=d.cache;if(o.has(l))return o.get(l);var c=e.apply(this,s);return d.cache=o.set(l,c)||o,c};return d.cache=new(mt.Cache||Ge),d}mt.Cache=Ge;function ot(e){if(typeof e!="function")throw new me(n);return function(){var a=arguments;switch(a.length){case 0:return!e.call(this);case 1:return!e.call(this,a[0]);case 2:return!e.call(this,a[0],a[1]);case 3:return!e.call(this,a[0],a[1],a[2])}return!e.apply(this,a)}}function Xs(e){return Ri(2,e)}var el=M8(function(e,a){a=a.length==1&&j(a[0])?m1(a[0],ee(M())):m1(O1(a,1),ee(M()));var d=a.length;return Z(function(s){for(var l=-1,o=B1(s.length,d);++l<o;)s[l]=a[l].call(this,s[l]);return X1(e,this,s)})}),l0=Z(function(e,a){var d=ma(a,lp(l0));return Ze(e,B,p,a,d)}),Bi=Z(function(e,a){var d=ma(a,lp(Bi));return Ze(e,U,p,a,d)}),al=Je(function(e,a){return Ze(e,e1,p,p,p,a)});function pl(e,a){if(typeof e!="function")throw new me(n);return a=a===p?a:G(a),Z(e,a)}function tl(e,a){if(typeof e!="function")throw new me(n);return a=a==null?0:D1(G(a),0),Z(function(d){var s=d[a],l=va(d,0,a);return s&&la(l,s),X1(e,this,l)})}function dl(e,a,d){var s=!0,l=!0;if(typeof e!="function")throw new me(n);return u1(d)&&(s="leading"in d?!!d.leading:s,l="trailing"in d?!!d.trailing:l),Fi(e,a,{leading:s,maxWait:a,trailing:l})}function il(e){return ki(e,1)}function rl(e,a){return l0(Gd(a),e)}function nl(){if(!arguments.length)return[];var e=arguments[0];return j(e)?e:[e]}function sl(e){return ue(e,D)}function ll(e,a){return a=typeof a=="function"?a:p,ue(e,D,a)}function ml(e){return ue(e,N|D)}function ol(e,a){return a=typeof a=="function"?a:p,ue(e,N|D,a)}function ul(e,a){return a==null||x4(e,a,V1(a))}function xe(e,a){return e===a||e!==e&&a!==a}var cl=tt(Ld),fl=tt(function(e,a){return e>=a}),La=P4(function(){return arguments}())?P4:function(e){return f1(e)&&i1.call(e,"callee")&&!_4.call(e,"callee")},j=b.isArray,hl=Q6?ee(Q6):N8;function Z1(e){return e!=null&&ut(e.length)&&!Qe(e)}function g1(e){return f1(e)&&Z1(e)}function vl(e){return e===!0||e===!1||f1(e)&&q1(e)==Op}var _a=C5||b0,_l=X6?ee(X6):T8;function wl(e){return f1(e)&&e.nodeType===1&&!a2(e)}function gl(e){if(e==null)return!0;if(Z1(e)&&(j(e)||typeof e=="string"||typeof e.splice=="function"||_a(e)||mp(e)||La(e)))return!e.length;var a=U1(e);if(a==ye||a==Se)return!e.size;if(Xp(e))return!Bd(e).length;for(var d in e)if(i1.call(e,d))return!1;return!0}function bl(e,a){return Jp(e,a)}function yl(e,a,d){d=typeof d=="function"?d:p;var s=d?d(e,a):p;return s===p?Jp(e,a,p,d):!!s}function m0(e){if(!f1(e))return!1;var a=q1(e);return a==T2||a==$3||typeof e.message=="string"&&typeof e.name=="string"&&!a2(e)}function Sl(e){return typeof e=="number"&&g4(e)}function Qe(e){if(!u1(e))return!1;var a=q1(e);return a==x2||a==D6||a==z3||a==j3}function Ui(e){return typeof e=="number"&&e==G(e)}function ut(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=G1}function u1(e){var a=typeof e;return e!=null&&(a=="object"||a=="function")}function f1(e){return e!=null&&typeof e=="object"}var Mi=e4?ee(e4):D8;function Nl(e,a){return e===a||Fd(e,a,e0(a))}function Tl(e,a,d){return d=typeof d=="function"?d:p,Fd(e,a,e0(a),d)}function xl(e){return qi(e)&&e!=+e}function Dl(e){if(l7(e))throw new W(r);return k4(e)}function Al(e){return e===null}function El(e){return e==null}function qi(e){return typeof e=="number"||f1(e)&&q1(e)==Ip}function a2(e){if(!f1(e)||q1(e)!=je)return!1;var a=U2(e);if(a===null)return!0;var d=i1.call(a,"constructor")&&a.constructor;return typeof d=="function"&&d instanceof d&&L2.call(d)==T5}var o0=a4?ee(a4):A8;function Vl(e){return Ui(e)&&e>=-G1&&e<=G1}var zi=p4?ee(p4):E8;function ct(e){return typeof e=="string"||!j(e)&&f1(e)&&q1(e)==Bp}function pe(e){return typeof e=="symbol"||f1(e)&&q1(e)==D2}var mp=t4?ee(t4):V8;function Cl(e){return e===p}function Pl(e){return f1(e)&&U1(e)==Up}function kl(e){return f1(e)&&q1(e)==G3}var Rl=tt(Ud),Ol=tt(function(e,a){return e<=a});function $i(e){if(!e)return[];if(Z1(e))return ct(e)?Ne(e):K1(e);if(zp&&e[zp])return u5(e[zp]());var a=U1(e),d=a==ye?Dd:a==Se?k2:op;return d(e)}function Xe(e){if(!e)return e===0?e:0;if(e=he(e),e===w1||e===-w1){var a=e<0?-1:1;return a*Rp}return e===e?e:0}function G(e){var a=Xe(e),d=a%1;return a===a?d?a-d:a:0}function Wi(e){return e?Pa(G(e),0,Pe):0}function he(e){if(typeof e=="number")return e;if(pe(e))return Ja;if(u1(e)){var a=typeof e.valueOf=="function"?e.valueOf():e;e=u1(a)?a+"":a}if(typeof e!="string")return e===0?e:+e;e=l4(e);var d=hn.test(e);return d||_n.test(e)?Zn(e.slice(2),d?2:8):fn.test(e)?Ja:+e}function ji(e){return Re(e,J1(e))}function Ll(e){return e?Pa(G(e),-G1,G1):e===0?e:0}function d1(e){return e==null?"":ae(e)}var Il=np(function(e,a){if(Xp(a)||Z1(a)){Re(a,V1(a),e);return}for(var d in a)i1.call(a,d)&&Gp(e,d,a[d])}),Hi=np(function(e,a){Re(a,J1(a),e)}),ft=np(function(e,a,d,s){Re(a,J1(a),e,s)}),Fl=np(function(e,a,d,s){Re(a,V1(a),e,s)}),Bl=Je(kd);function Ul(e,a){var d=rp(e);return a==null?d:T4(d,a)}var Ml=Z(function(e,a){e=r1(e);var d=-1,s=a.length,l=s>2?a[2]:p;for(l&&z1(a[0],a[1],l)&&(s=1);++d<s;)for(var o=a[d],c=J1(o),h=-1,g=c.length;++h<g;){var S=c[h],T=e[S];(T===p||xe(T,tp[S])&&!i1.call(e,S))&&(e[S]=o[S])}return e}),ql=Z(function(e){return e.push(p,li),X1(Gi,p,e)});function zl(e,a){return i4(e,M(a,3),ke)}function $l(e,a){return i4(e,M(a,3),Od)}function Wl(e,a){return e==null?e:Rd(e,M(a,3),J1)}function jl(e,a){return e==null?e:V4(e,M(a,3),J1)}function Hl(e,a){return e&&ke(e,M(a,3))}function Gl(e,a){return e&&Od(e,M(a,3))}function Kl(e){return e==null?[]:Z2(e,V1(e))}function Zl(e){return e==null?[]:Z2(e,J1(e))}function u0(e,a,d){var s=e==null?p:ka(e,a);return s===p?d:s}function Jl(e,a){return e!=null&&ui(e,a,g8)}function c0(e,a){return e!=null&&ui(e,a,b8)}var Yl=di(function(e,a,d){a!=null&&typeof a.toString!="function"&&(a=I2.call(a)),e[a]=d},h0(Y1)),Ql=di(function(e,a,d){a!=null&&typeof a.toString!="function"&&(a=I2.call(a)),i1.call(e,a)?e[a].push(d):e[a]=[d]},M),Xl=Z(Zp);function V1(e){return Z1(e)?S4(e):Bd(e)}function J1(e){return Z1(e)?S4(e,!0):C8(e)}function em(e,a){var d={};return a=M(a,3),ke(e,function(s,l,o){Ke(d,a(s,l,o),s)}),d}function am(e,a){var d={};return a=M(a,3),ke(e,function(s,l,o){Ke(d,l,a(s,l,o))}),d}var pm=np(function(e,a,d){J2(e,a,d)}),Gi=np(function(e,a,d,s){J2(e,a,d,s)}),tm=Je(function(e,a){var d={};if(e==null)return d;var s=!1;a=m1(a,function(o){return o=ha(o,e),s||(s=o.length>1),o}),Re(e,Qd(e),d),s&&(d=ue(d,N|A|D,Y8));for(var l=a.length;l--;)Wd(d,a[l]);return d});function dm(e,a){return Ki(e,ot(M(a)))}var im=Je(function(e,a){return e==null?{}:k8(e,a)});function Ki(e,a){if(e==null)return{};var d=m1(Qd(e),function(s){return[s]});return a=M(a),U4(e,d,function(s,l){return a(s,l[0])})}function rm(e,a,d){a=ha(a,e);var s=-1,l=a.length;for(l||(l=1,e=p);++s<l;){var o=e==null?p:e[Oe(a[s])];o===p&&(s=l,o=d),e=Qe(o)?o.call(e):o}return e}function nm(e,a,d){return e==null?e:Yp(e,a,d)}function sm(e,a,d,s){return s=typeof s=="function"?s:p,e==null?e:Yp(e,a,d,s)}var Zi=ni(V1),Ji=ni(J1);function lm(e,a,d){var s=j(e),l=s||_a(e)||mp(e);if(a=M(a,4),d==null){var o=e&&e.constructor;l?d=s?new o:[]:u1(e)?d=Qe(o)?rp(U2(e)):{}:d={}}return(l?le:ke)(e,function(c,h,g){return a(d,c,h,g)}),d}function mm(e,a){return e==null?!0:Wd(e,a)}function om(e,a,d){return e==null?e:W4(e,a,Gd(d))}function um(e,a,d,s){return s=typeof s=="function"?s:p,e==null?e:W4(e,a,Gd(d),s)}function op(e){return e==null?[]:xd(e,V1(e))}function cm(e){return e==null?[]:xd(e,J1(e))}function fm(e,a,d){return d===p&&(d=a,a=p),d!==p&&(d=he(d),d=d===d?d:0),a!==p&&(a=he(a),a=a===a?a:0),Pa(he(e),a,d)}function hm(e,a,d){return a=Xe(a),d===p?(d=a,a=0):d=Xe(d),e=he(e),y8(e,a,d)}function vm(e,a,d){if(d&&typeof d!="boolean"&&z1(e,a,d)&&(a=d=p),d===p&&(typeof a=="boolean"?(d=a,a=p):typeof e=="boolean"&&(d=e,e=p)),e===p&&a===p?(e=0,a=1):(e=Xe(e),a===p?(a=e,e=0):a=Xe(a)),e>a){var s=e;e=a,a=s}if(d||e%1||a%1){var l=b4();return B1(e+l*(a-e+Kn("1e-"+((l+"").length-1))),a)}return qd(e,a)}var _m=sp(function(e,a,d){return a=a.toLowerCase(),e+(d?Yi(a):a)});function Yi(e){return f0(d1(e).toLowerCase())}function Qi(e){return e=d1(e),e&&e.replace(gn,n5).replace(Bn,"")}function wm(e,a,d){e=d1(e),a=ae(a);var s=e.length;d=d===p?s:Pa(G(d),0,s);var l=d;return d-=a.length,d>=0&&e.slice(d,l)==a}function gm(e){return e=d1(e),e&&Q3.test(e)?e.replace(V6,s5):e}function bm(e){return e=d1(e),e&&dn.test(e)?e.replace(ld,"\\$&"):e}var ym=sp(function(e,a,d){return e+(d?"-":"")+a.toLowerCase()}),Sm=sp(function(e,a,d){return e+(d?" ":"")+a.toLowerCase()}),Nm=ai("toLowerCase");function Tm(e,a,d){e=d1(e),a=G(a);var s=a?pp(e):0;if(!a||s>=a)return e;var l=(a-s)/2;return pt($2(l),d)+e+pt(z2(l),d)}function xm(e,a,d){e=d1(e),a=G(a);var s=a?pp(e):0;return a&&s<a?e+pt(a-s,d):e}function Dm(e,a,d){e=d1(e),a=G(a);var s=a?pp(e):0;return a&&s<a?pt(a-s,d)+e:e}function Am(e,a,d){return d||a==null?a=0:a&&(a=+a),O5(d1(e).replace(md,""),a||0)}function Em(e,a,d){return(d?z1(e,a,d):a===p)?a=1:a=G(a),zd(d1(e),a)}function Vm(){var e=arguments,a=d1(e[0]);return e.length<3?a:a.replace(e[1],e[2])}var Cm=sp(function(e,a,d){return e+(d?"_":"")+a.toLowerCase()});function Pm(e,a,d){return d&&typeof d!="number"&&z1(e,a,d)&&(a=d=p),d=d===p?Pe:d>>>0,d?(e=d1(e),e&&(typeof a=="string"||a!=null&&!o0(a))&&(a=ae(a),!a&&ap(e))?va(Ne(e),0,d):e.split(a,d)):[]}var km=sp(function(e,a,d){return e+(d?" ":"")+f0(a)});function Rm(e,a,d){return e=d1(e),d=d==null?0:Pa(G(d),0,e.length),a=ae(a),e.slice(d,d+a.length)==a}function Om(e,a,d){var s=m.templateSettings;d&&z1(e,a,d)&&(a=p),e=d1(e),a=ft({},a,s,si);var l=ft({},a.imports,s.imports,si),o=V1(l),c=xd(l,o),h,g,S=0,T=a.interpolate||A2,x="__p += '",E=Ad((a.escape||A2).source+"|"+T.source+"|"+(T===C6?cn:A2).source+"|"+(a.evaluate||A2).source+"|$","g"),O="//# sourceURL="+(i1.call(a,"sourceURL")?(a.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++$n+"]")+`
`;e.replace(E,function($,Y,p1,te,$1,de){return p1||(p1=te),x+=e.slice(S,de).replace(bn,l5),Y&&(h=!0,x+=`' +
__e(`+Y+`) +
'`),$1&&(g=!0,x+=`';
`+$1+`;
__p += '`),p1&&(x+=`' +
((__t = (`+p1+`)) == null ? '' : __t) +
'`),S=de+$.length,$}),x+=`';
`;var z=i1.call(a,"variable")&&a.variable;if(!z)x=`with (obj) {
`+x+`
}
`;else if(on.test(z))throw new W(u);x=(g?x.replace(K3,""):x).replace(Z3,"$1").replace(J3,"$1;"),x="function("+(z||"obj")+`) {
`+(z?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(h?", __e = _.escape":"")+(g?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+x+`return __p
}`;var K=er(function(){return t1(o,O+"return "+x).apply(p,c)});if(K.source=x,m0(K))throw K;return K}function Lm(e){return d1(e).toLowerCase()}function Im(e){return d1(e).toUpperCase()}function Fm(e,a,d){if(e=d1(e),e&&(d||a===p))return l4(e);if(!e||!(a=ae(a)))return e;var s=Ne(e),l=Ne(a),o=m4(s,l),c=o4(s,l)+1;return va(s,o,c).join("")}function Bm(e,a,d){if(e=d1(e),e&&(d||a===p))return e.slice(0,c4(e)+1);if(!e||!(a=ae(a)))return e;var s=Ne(e),l=o4(s,Ne(a))+1;return va(s,0,l).join("")}function Um(e,a,d){if(e=d1(e),e&&(d||a===p))return e.replace(md,"");if(!e||!(a=ae(a)))return e;var s=Ne(e),l=m4(s,Ne(a));return va(s,l).join("")}function Mm(e,a){var d=F1,s=Q1;if(u1(a)){var l="separator"in a?a.separator:l;d="length"in a?G(a.length):d,s="omission"in a?ae(a.omission):s}e=d1(e);var o=e.length;if(ap(e)){var c=Ne(e);o=c.length}if(d>=o)return e;var h=d-pp(s);if(h<1)return s;var g=c?va(c,0,h).join(""):e.slice(0,h);if(l===p)return g+s;if(c&&(h+=g.length-h),o0(l)){if(e.slice(h).search(l)){var S,T=g;for(l.global||(l=Ad(l.source,d1(P6.exec(l))+"g")),l.lastIndex=0;S=l.exec(T);)var x=S.index;g=g.slice(0,x===p?h:x)}}else if(e.indexOf(ae(l),h)!=h){var E=g.lastIndexOf(l);E>-1&&(g=g.slice(0,E))}return g+s}function qm(e){return e=d1(e),e&&Y3.test(e)?e.replace(E6,v5):e}var zm=sp(function(e,a,d){return e+(d?" ":"")+a.toUpperCase()}),f0=ai("toUpperCase");function Xi(e,a,d){return e=d1(e),a=d?p:a,a===p?o5(e)?g5(e):p5(e):e.match(a)||[]}var er=Z(function(e,a){try{return X1(e,p,a)}catch(d){return m0(d)?d:new W(d)}}),$m=Je(function(e,a){return le(a,function(d){d=Oe(d),Ke(e,d,s0(e[d],e))}),e});function Wm(e){var a=e==null?0:e.length,d=M();return e=a?m1(e,function(s){if(typeof s[1]!="function")throw new me(n);return[d(s[0]),s[1]]}):[],Z(function(s){for(var l=-1;++l<a;){var o=e[l];if(X1(o[0],this,s))return X1(o[1],this,s)}})}function jm(e){return v8(ue(e,N))}function h0(e){return function(){return e}}function Hm(e,a){return e==null||e!==e?a:e}var Gm=ti(),Km=ti(!0);function Y1(e){return e}function v0(e){return R4(typeof e=="function"?e:ue(e,N))}function Zm(e){return L4(ue(e,N))}function Jm(e,a){return I4(e,ue(a,N))}var Ym=Z(function(e,a){return function(d){return Zp(d,e,a)}}),Qm=Z(function(e,a){return function(d){return Zp(e,d,a)}});function _0(e,a,d){var s=V1(a),l=Z2(a,s);d==null&&!(u1(a)&&(l.length||!s.length))&&(d=a,a=e,e=this,l=Z2(a,V1(a)));var o=!(u1(d)&&"chain"in d)||!!d.chain,c=Qe(e);return le(l,function(h){var g=a[h];e[h]=g,c&&(e.prototype[h]=function(){var S=this.__chain__;if(o||S){var T=e(this.__wrapped__),x=T.__actions__=K1(this.__actions__);return x.push({func:g,args:arguments,thisArg:e}),T.__chain__=S,T}return g.apply(e,la([this.value()],arguments))})}),e}function Xm(){return E1._===this&&(E1._=x5),this}function w0(){}function eo(e){return e=G(e),Z(function(a){return F4(a,e)})}var ao=Zd(m1),po=Zd(d4),to=Zd(bd);function ar(e){return p0(e)?yd(Oe(e)):R8(e)}function io(e){return function(a){return e==null?p:ka(e,a)}}var ro=ii(),no=ii(!0);function g0(){return[]}function b0(){return!1}function so(){return{}}function lo(){return""}function mo(){return!0}function oo(e,a){if(e=G(e),e<1||e>G1)return[];var d=Pe,s=B1(e,Pe);a=M(a),e-=Pe;for(var l=Td(s,a);++d<e;)a(d);return l}function uo(e){return j(e)?m1(e,Oe):pe(e)?[e]:K1(yi(d1(e)))}function co(e){var a=++N5;return d1(e)+a}var fo=at(function(e,a){return e+a},0),ho=Jd("ceil"),vo=at(function(e,a){return e/a},1),_o=Jd("floor");function wo(e){return e&&e.length?K2(e,Y1,Ld):p}function go(e,a){return e&&e.length?K2(e,M(a,2),Ld):p}function bo(e){return n4(e,Y1)}function yo(e,a){return n4(e,M(a,2))}function So(e){return e&&e.length?K2(e,Y1,Ud):p}function No(e,a){return e&&e.length?K2(e,M(a,2),Ud):p}var To=at(function(e,a){return e*a},1),xo=Jd("round"),Do=at(function(e,a){return e-a},0);function Ao(e){return e&&e.length?Nd(e,Y1):0}function Eo(e,a){return e&&e.length?Nd(e,M(a,2)):0}return m.after=Zs,m.ary=ki,m.assign=Il,m.assignIn=Hi,m.assignInWith=ft,m.assignWith=Fl,m.at=Bl,m.before=Ri,m.bind=s0,m.bindAll=$m,m.bindKey=Oi,m.castArray=nl,m.chain=Vi,m.chunk=v7,m.compact=_7,m.concat=w7,m.cond=Wm,m.conforms=jm,m.constant=h0,m.countBy=Ds,m.create=Ul,m.curry=Li,m.curryRight=Ii,m.debounce=Fi,m.defaults=Ml,m.defaultsDeep=ql,m.defer=Js,m.delay=Ys,m.difference=g7,m.differenceBy=b7,m.differenceWith=y7,m.drop=S7,m.dropRight=N7,m.dropRightWhile=T7,m.dropWhile=x7,m.fill=D7,m.filter=Es,m.flatMap=Ps,m.flatMapDeep=ks,m.flatMapDepth=Rs,m.flatten=xi,m.flattenDeep=A7,m.flattenDepth=E7,m.flip=Qs,m.flow=Gm,m.flowRight=Km,m.fromPairs=V7,m.functions=Kl,m.functionsIn=Zl,m.groupBy=Os,m.initial=P7,m.intersection=k7,m.intersectionBy=R7,m.intersectionWith=O7,m.invert=Yl,m.invertBy=Ql,m.invokeMap=Is,m.iteratee=v0,m.keyBy=Fs,m.keys=V1,m.keysIn=J1,m.map=st,m.mapKeys=em,m.mapValues=am,m.matches=Zm,m.matchesProperty=Jm,m.memoize=mt,m.merge=pm,m.mergeWith=Gi,m.method=Ym,m.methodOf=Qm,m.mixin=_0,m.negate=ot,m.nthArg=eo,m.omit=tm,m.omitBy=dm,m.once=Xs,m.orderBy=Bs,m.over=ao,m.overArgs=el,m.overEvery=po,m.overSome=to,m.partial=l0,m.partialRight=Bi,m.partition=Us,m.pick=im,m.pickBy=Ki,m.property=ar,m.propertyOf=io,m.pull=B7,m.pullAll=Ai,m.pullAllBy=U7,m.pullAllWith=M7,m.pullAt=q7,m.range=ro,m.rangeRight=no,m.rearg=al,m.reject=zs,m.remove=z7,m.rest=pl,m.reverse=r0,m.sampleSize=Ws,m.set=nm,m.setWith=sm,m.shuffle=js,m.slice=$7,m.sortBy=Ks,m.sortedUniq=J7,m.sortedUniqBy=Y7,m.split=Pm,m.spread=tl,m.tail=Q7,m.take=X7,m.takeRight=es,m.takeRightWhile=as,m.takeWhile=ps,m.tap=_s,m.throttle=dl,m.thru=nt,m.toArray=$i,m.toPairs=Zi,m.toPairsIn=Ji,m.toPath=uo,m.toPlainObject=ji,m.transform=lm,m.unary=il,m.union=ts,m.unionBy=ds,m.unionWith=is,m.uniq=rs,m.uniqBy=ns,m.uniqWith=ss,m.unset=mm,m.unzip=n0,m.unzipWith=Ei,m.update=om,m.updateWith=um,m.values=op,m.valuesIn=cm,m.without=ls,m.words=Xi,m.wrap=rl,m.xor=ms,m.xorBy=os,m.xorWith=us,m.zip=cs,m.zipObject=fs,m.zipObjectDeep=hs,m.zipWith=vs,m.entries=Zi,m.entriesIn=Ji,m.extend=Hi,m.extendWith=ft,_0(m,m),m.add=fo,m.attempt=er,m.camelCase=_m,m.capitalize=Yi,m.ceil=ho,m.clamp=fm,m.clone=sl,m.cloneDeep=ml,m.cloneDeepWith=ol,m.cloneWith=ll,m.conformsTo=ul,m.deburr=Qi,m.defaultTo=Hm,m.divide=vo,m.endsWith=wm,m.eq=xe,m.escape=gm,m.escapeRegExp=bm,m.every=As,m.find=Vs,m.findIndex=Ni,m.findKey=zl,m.findLast=Cs,m.findLastIndex=Ti,m.findLastKey=$l,m.floor=_o,m.forEach=Ci,m.forEachRight=Pi,m.forIn=Wl,m.forInRight=jl,m.forOwn=Hl,m.forOwnRight=Gl,m.get=u0,m.gt=cl,m.gte=fl,m.has=Jl,m.hasIn=c0,m.head=Di,m.identity=Y1,m.includes=Ls,m.indexOf=C7,m.inRange=hm,m.invoke=Xl,m.isArguments=La,m.isArray=j,m.isArrayBuffer=hl,m.isArrayLike=Z1,m.isArrayLikeObject=g1,m.isBoolean=vl,m.isBuffer=_a,m.isDate=_l,m.isElement=wl,m.isEmpty=gl,m.isEqual=bl,m.isEqualWith=yl,m.isError=m0,m.isFinite=Sl,m.isFunction=Qe,m.isInteger=Ui,m.isLength=ut,m.isMap=Mi,m.isMatch=Nl,m.isMatchWith=Tl,m.isNaN=xl,m.isNative=Dl,m.isNil=El,m.isNull=Al,m.isNumber=qi,m.isObject=u1,m.isObjectLike=f1,m.isPlainObject=a2,m.isRegExp=o0,m.isSafeInteger=Vl,m.isSet=zi,m.isString=ct,m.isSymbol=pe,m.isTypedArray=mp,m.isUndefined=Cl,m.isWeakMap=Pl,m.isWeakSet=kl,m.join=L7,m.kebabCase=ym,m.last=fe,m.lastIndexOf=I7,m.lowerCase=Sm,m.lowerFirst=Nm,m.lt=Rl,m.lte=Ol,m.max=wo,m.maxBy=go,m.mean=bo,m.meanBy=yo,m.min=So,m.minBy=No,m.stubArray=g0,m.stubFalse=b0,m.stubObject=so,m.stubString=lo,m.stubTrue=mo,m.multiply=To,m.nth=F7,m.noConflict=Xm,m.noop=w0,m.now=lt,m.pad=Tm,m.padEnd=xm,m.padStart=Dm,m.parseInt=Am,m.random=vm,m.reduce=Ms,m.reduceRight=qs,m.repeat=Em,m.replace=Vm,m.result=rm,m.round=xo,m.runInContext=w,m.sample=$s,m.size=Hs,m.snakeCase=Cm,m.some=Gs,m.sortedIndex=W7,m.sortedIndexBy=j7,m.sortedIndexOf=H7,m.sortedLastIndex=G7,m.sortedLastIndexBy=K7,m.sortedLastIndexOf=Z7,m.startCase=km,m.startsWith=Rm,m.subtract=Do,m.sum=Ao,m.sumBy=Eo,m.template=Om,m.times=oo,m.toFinite=Xe,m.toInteger=G,m.toLength=Wi,m.toLower=Lm,m.toNumber=he,m.toSafeInteger=Ll,m.toString=d1,m.toUpper=Im,m.trim=Fm,m.trimEnd=Bm,m.trimStart=Um,m.truncate=Mm,m.unescape=qm,m.uniqueId=co,m.upperCase=zm,m.upperFirst=f0,m.each=Ci,m.eachRight=Pi,m.first=Di,_0(m,function(){var e={};return ke(m,function(a,d){i1.call(m.prototype,d)||(e[d]=a)}),e}(),{chain:!1}),m.VERSION=t,le(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){m[e].placeholder=m}),le(["drop","take"],function(e,a){X.prototype[e]=function(d){d=d===p?1:D1(G(d),0);var s=this.__filtered__&&!a?new X(this):this.clone();return s.__filtered__?s.__takeCount__=B1(d,s.__takeCount__):s.__views__.push({size:B1(d,Pe),type:e+(s.__dir__<0?"Right":"")}),s},X.prototype[e+"Right"]=function(d){return this.reverse()[e](d).reverse()}}),le(["filter","map","takeWhile"],function(e,a){var d=a+1,s=d==H1||d==a1;X.prototype[e]=function(l){var o=this.clone();return o.__iteratees__.push({iteratee:M(l,3),type:d}),o.__filtered__=o.__filtered__||s,o}}),le(["head","last"],function(e,a){var d="take"+(a?"Right":"");X.prototype[e]=function(){return this[d](1).value()[0]}}),le(["initial","tail"],function(e,a){var d="drop"+(a?"":"Right");X.prototype[e]=function(){return this.__filtered__?new X(this):this[d](1)}}),X.prototype.compact=function(){return this.filter(Y1)},X.prototype.find=function(e){return this.filter(e).head()},X.prototype.findLast=function(e){return this.reverse().find(e)},X.prototype.invokeMap=Z(function(e,a){return typeof e=="function"?new X(this):this.map(function(d){return Zp(d,e,a)})}),X.prototype.reject=function(e){return this.filter(ot(M(e)))},X.prototype.slice=function(e,a){e=G(e);var d=this;return d.__filtered__&&(e>0||a<0)?new X(d):(e<0?d=d.takeRight(-e):e&&(d=d.drop(e)),a!==p&&(a=G(a),d=a<0?d.dropRight(-a):d.take(a-e)),d)},X.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},X.prototype.toArray=function(){return this.take(Pe)},ke(X.prototype,function(e,a){var d=/^(?:filter|find|map|reject)|While$/.test(a),s=/^(?:head|last)$/.test(a),l=m[s?"take"+(a=="last"?"Right":""):a],o=s||/^find/.test(a);l&&(m.prototype[a]=function(){var c=this.__wrapped__,h=s?[1]:arguments,g=c instanceof X,S=h[0],T=g||j(c),x=function(Y){var p1=l.apply(m,la([Y],h));return s&&E?p1[0]:p1};T&&d&&typeof S=="function"&&S.length!=1&&(g=T=!1);var E=this.__chain__,O=!!this.__actions__.length,z=o&&!E,K=g&&!O;if(!o&&T){c=K?c:new X(this);var $=e.apply(c,h);return $.__actions__.push({func:nt,args:[x],thisArg:p}),new oe($,E)}return z&&K?e.apply(this,h):($=this.thru(x),z?s?$.value()[0]:$.value():$)})}),le(["pop","push","shift","sort","splice","unshift"],function(e){var a=R2[e],d=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",s=/^(?:pop|shift)$/.test(e);m.prototype[e]=function(){var l=arguments;if(s&&!this.__chain__){var o=this.value();return a.apply(j(o)?o:[],l)}return this[d](function(c){return a.apply(j(c)?c:[],l)})}}),ke(X.prototype,function(e,a){var d=m[a];if(d){var s=d.name+"";i1.call(ip,s)||(ip[s]=[]),ip[s].push({name:a,func:d})}}),ip[et(p,R).name]=[{name:"wrapper",func:p}],X.prototype.clone=q5,X.prototype.reverse=z5,X.prototype.value=$5,m.prototype.at=ws,m.prototype.chain=gs,m.prototype.commit=bs,m.prototype.next=ys,m.prototype.plant=Ns,m.prototype.reverse=Ts,m.prototype.toJSON=m.prototype.valueOf=m.prototype.value=xs,m.prototype.first=m.prototype.head,zp&&(m.prototype[zp]=Ss),m},oa=b5();typeof define=="function"&&typeof define.amd=="object"&&define.amd?(E1._=oa,define(function(){return oa})):Aa?((Aa.exports=oa)._=oa,vd._=oa):E1._=oa}).call(kp)});var Oh={};Oo(Oh,{default:()=>F3});module.exports=Lo(Oh);var o1=require("@raycast/api"),Za=p2(Yt());var N6=p2(Yt());var V3=p2(require("os"));function Qt(p){return p.replace(/"/g,'\\"')}function C3(p){return`${parseInt(V3.default.release().split(".")[0])>=23?"applenotes":"notes"}://showNote?identifier=${p}`}async function P3(p){return(0,N6.runAppleScript)(`
    tell application "Notes"
      set theNote to note id "${Qt(p)}"
      return body of theNote
    end tell
    `)}async function k3(p,t){return(0,N6.runAppleScript)(`
    tell application "Notes"
      set theNote to note id "${Qt(p)}"
      set body of theNote to "${Qt(t)}"
    end tell
    `)}var O3=require("os"),L3=require("path"),S2=p2(Yt()),T6=p2(R3());var Xt=(0,L3.resolve)((0,O3.homedir)(),"Library/Group Containers/group.com.apple.notes/NoteStore.sqlite"),Ch=`
    SELECT
        'x-coredata://' || zmd.z_uuid || '/ICNote/p' || note.z_pk AS id,
        note.z_pk AS pk,
        note.ztitle1 AS title,
        folder.ztitle2 AS folder,
        datetime(note.zmodificationdate1 + *********, 'unixepoch') AS modifiedAt,
        note.zsnippet AS snippet,
        acc.zname AS account,
        note.zidentifier AS UUID,
        (note.zispasswordprotected = 1) as locked,
        (note.zispinned = 1) as pinned,
        (note.zhaschecklist = 1) as checklist,
        (note.zhaschecklistinprogress = 1) as checklistInProgress
    FROM 
        ziccloudsyncingobject AS note
    INNER JOIN ziccloudsyncingobject AS folder 
        ON note.zfolder = folder.z_pk
    LEFT JOIN ziccloudsyncingobject AS acc 
        ON note.zaccount4 = acc.z_pk
    LEFT JOIN z_metadata AS zmd ON 1=1
    WHERE
        note.ztitle1 IS NOT NULL AND
        note.zmodificationdate1 IS NOT NULL AND
        note.z_pk IS NOT NULL AND
        note.zmarkedfordeletion != 1 AND
        folder.zmarkedfordeletion != 1
    ORDER BY
        note.zmodificationdate1 DESC
`,Ph=`
    SELECT
        inv.zshareurl AS invitationLink,
        'x-coredata://' || zmd.z_uuid || '/ICNote/p' || note.z_pk AS noteId
    FROM
        ziccloudsyncingobject AS note
    LEFT JOIN zicinvitation AS inv 
        ON note.zinvitation = inv.z_pk
    LEFT JOIN z_metadata AS zmd ON 1=1
    WHERE
        note.zmarkedfordeletion != 1
`,kh=`
    SELECT
      note.z_pk AS notePk,
      link.zidentifier AS id,
      link.ZALTTEXT as text,
      link.ZTOKENCONTENTIDENTIFIER as url
    FROM
      ziccloudsyncingobject AS note
    JOIN ziccloudsyncingobject AS link ON note.z_pk = link.ZNOTE1
    WHERE
      link.ZTYPEUTI1 = 'com.apple.notes.inlinetextattachment.link'
`,Rh=`
    SELECT
      note.z_pk AS notePk,
      link.zidentifier AS id,
      link.ZALTTEXT as text
    FROM
      ziccloudsyncingobject AS note
    JOIN ziccloudsyncingobject AS link ON note.z_pk = link.ZNOTE1
    WHERE
      link.ZTYPEUTI1 = 'com.apple.notes.inlinetextattachment.hashtag'
`,I3=()=>{let{data:p,...t}=(0,S2.useSQL)(Xt,Ch,{permissionPriming:"This is required to search your Apple Notes."}),{data:i}=(0,S2.useSQL)(Xt,Ph,{execute:p&&p.length>0,onError(){}}),{data:r}=(0,S2.useSQL)(Xt,kh,{execute:p&&p.length>0}),{data:n}=(0,S2.useSQL)(Xt,Rh,{execute:p&&p.length>0}),u={},v=p?.filter(V=>{let F=u[V.id];return F||(u[V.id]=!0),!F}).sort((V,F)=>V.modifiedAt&&F.modifiedAt&&V.modifiedAt<F.modifiedAt?1:-1)??[],_=v.map(V=>{let F=i?.find(P=>P.noteId===V.id),C=r?.filter(P=>P.notePk==V.pk),R=[];r?.forEach(P=>{if(P.url?.includes(V.UUID.toLowerCase())){let L=v.find(B=>B.pk===P.notePk);if(!L)return;R.push({id:P.id,title:L.title,url:C3(L.UUID)})}});let q=n?.filter(P=>P.notePk==V.pk);return{...V,invitationLink:F?.invitationLink??null,links:C??[],backlinks:R??[],tags:q??[]}}),[f,N]=(0,T6.partition)(_,V=>V.folder!="Recently Deleted"),[A,D]=(0,T6.partition)(f,V=>V.pinned);return{data:{pinnedNotes:A,unpinnedNotes:D,deletedNotes:N,allNotes:[...A,...D,...N]},...t}};var Ce=require("react/jsx-runtime");function x6({draftValues:p,noteId:t}){let{data:i,isLoading:r,permissionView:n}=I3(),{pop:u}=(0,o1.useNavigation)(),{itemProps:v,handleSubmit:_,reset:f}=(0,Za.useForm)({async onSubmit(N){let A=[...i.pinnedNotes,...i.unpinnedNotes].find(D=>D.id===N.note)?.title||"Note";try{await(0,o1.showToast)({style:o1.Toast.Style.Animated,title:`Adding text to "${A}"`});let D=await P3(N.note),V=N.prepend?`${N.text}

${D}`:`${D}

${N.text}`;await k3(N.note,V),t?await u():await(0,o1.closeMainWindow)(),await(0,o1.showToast)({style:o1.Toast.Style.Success,title:`Added text to "${A}"`}),f({text:""})}catch(D){await(0,Za.showFailureToast)(D,{title:`Failed adding text to "${A}"`})}},initialValues:{note:t??p?.note??"",text:p?.text??"",prepend:p?.prepend??!1},validation:{note:Za.FormValidation.Required,text:Za.FormValidation.Required}});return n||(0,Ce.jsxs)(o1.Form,{actions:(0,Ce.jsx)(o1.ActionPanel,{children:(0,Ce.jsx)(o1.Action.SubmitForm,{onSubmit:_,title:"Add Text to Note",icon:o1.Icon.Plus})}),isLoading:r,enableDrafts:!t,children:[(0,Ce.jsx)(o1.Form.Dropdown,{...v.note,title:"Note",isLoading:r,storeValue:!0,children:(0,Ce.jsxs)(o1.Form.Dropdown.Section,{children:[i.pinnedNotes.map(N=>(0,Ce.jsx)(o1.Form.Dropdown.Item,{title:N.title,value:N.id,icon:"notes-icon.png"},N.id)),i.unpinnedNotes.map(N=>(0,Ce.jsx)(o1.Form.Dropdown.Item,{title:N.title,value:N.id,icon:"notes-icon.png"},N.id))]})}),(0,Ce.jsx)(o1.Form.TextArea,{enableMarkdown:!0,title:"Text",...v.text}),(0,Ce.jsx)(o1.Form.Checkbox,{...v.prepend,label:"Add text at the top",info:"If checked, the text will be added at the top of the note instead of the bottom",storeValue:!0})]})}var B3=require("react/jsx-runtime");function F3(p){return(0,B3.jsx)(x6,{draftValues:p.draftValues})}
/*! Bundled license information:

lodash/lodash.js:
  (**
   * @license
   * Lodash <https://lodash.com/>
   * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
   * Released under MIT license <https://lodash.com/license>
   * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
   * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
   *)
*/
