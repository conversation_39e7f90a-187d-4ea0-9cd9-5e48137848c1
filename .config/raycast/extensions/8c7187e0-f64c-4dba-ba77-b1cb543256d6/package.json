{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "apple-notes", "title": "Apple Notes", "description": "Search and create notes within the Apple Notes application.", "icon": "command-icon.png", "owner": "raycast", "access": "public", "author": "tumtum", "contributors": ["ron-myers", "<PERSON><PERSON><PERSON><PERSON>", "StevenRCE0", "HelloImSteven", "tyler<PERSON>ce", "thoma<PERSON><PERSON><PERSON><PERSON>", "xilopaint", "ridemountainpig", "teemu_suvinen", "juan_saldana"], "license": "MIT", "commands": [{"name": "index", "title": "Search Notes", "subtitle": "Apple Notes", "description": "Search through your Apple notes.", "mode": "view"}, {"name": "new", "title": "New Note", "subtitle": "Apple Notes", "description": "Create a new note in your Apple Notes.", "mode": "no-view", "keywords": ["write note", "add note", "make note", "create note"], "arguments": [{"name": "text", "type": "text", "placeholder": "Text"}]}, {"name": "ai", "title": "AI Note", "subtitle": "Apple Notes", "description": "Create a new note filled with AI in your Apple notes.", "mode": "no-view", "arguments": [{"name": "text", "type": "text", "placeholder": "Text", "required": true}, {"name": "instructions", "type": "text", "placeholder": "Additional instructions"}]}, {"name": "menu-bar", "title": "<PERSON>u Bar <PERSON>", "subtitle": "Apple Notes", "description": "View your recent and pinned Apple notes in the menu bar.", "interval": "5m", "mode": "menu-bar", "preferences": [{"name": "maxResults", "title": "Maximum Results", "type": "textfield", "default": "25", "description": "The maximum number of notes to display in the menu bar", "required": false}]}, {"name": "add-text", "title": "Add Text to Note", "subtitle": "Apple Notes", "description": "Add some text to an Apple note.", "mode": "view"}, {"name": "view-selected-note", "title": "View Selected Note", "subtitle": "Apple Notes", "description": "View the currently selected note in Raycast.", "mode": "view"}], "tools": [{"name": "create-note", "title": "Create Note", "description": "Create a new note in Apple Notes.", "input": {"type": "object", "properties": {"content": {"type": "string", "description": "The content of the note to create, formatted as HTML, so that it can be pasted into Apple Notes.\n\n- The note should be clear and concise.\n- The title should be short and descriptive and wrapped in an <h1> tag.\n- Don't directly address the reader. Write the note from an objective point of view.\n- Use the same language as the original text.\n- Break the content into paragraphs with line breaks.\n- Don't use Markdown links (e.g. [Link](https://example.com)), use HTML links (e.g. <a href=\"https://example.com\">Link</a>).\n\nExample:\n```html\n<h1>Title</h1>\n<br/>\n<p>Paragraph 1</p>\n<br/>\n<p>Paragraph 2</p>\n```"}, "raw_content": {"type": "string", "description": "The unformatted content of the note to create, without any HTML tags."}}, "required": ["content", "raw_content"]}, "confirmation": true}, {"name": "search-notes", "title": "Search Notes", "description": "Search for notes in Apple Notes.", "confirmation": false}, {"name": "get-note-content", "title": "Get Note Content", "description": "Get the content of a specific note in Apple Notes.", "input": {"type": "object", "properties": {"noteId": {"type": "string", "description": "The ID of the note to get the content of"}}, "required": ["noteId"]}, "confirmation": false}, {"name": "update-note", "title": "Update Note", "description": "Update the content of a specific note in Apple Notes.", "input": {"type": "object", "properties": {"noteId": {"type": "string", "description": "The ID of the note to get the content of"}, "content": {"type": "string", "description": "The content of the note to create, formatted as HTML, so that it can be pasted into Apple Notes.\n\n- The note should be clear and concise.\n- The title should be short and descriptive and wrapped in an <h1> tag.\n- Don't directly address the reader. Write the note from an objective point of view.\n- Use the same language as the original text.\n- Break the content into paragraphs with line breaks.\n- Don't use Markdown links (e.g. [Link](https://example.com)), use HTML links (e.g. <a href=\"https://example.com\">Link</a>).\n\nExample:\n```html\n<h1>Title</h1>\n<br/>\n<p>Paragraph 1</p>\n<br/>\n<p>Paragraph 2</p>\n```"}}, "required": ["noteId", "content"]}, "confirmation": false}], "ai": {"instructions": "- Always format note titles as Markdown links using the note's URL.\\n- If you can't access the Apple Notes database, tell the user he needs to give <PERSON><PERSON> the full disk access permission in the system settings. Be detailed about the steps.", "evals": [{"input": "@apple-notes what's on my grocery list", "expected": [{"callsTool": "search-notes"}, {"callsTool": {"arguments": {"noteId": "grocery-list"}, "name": "get-note-content"}}], "mocks": {"get-note-content": "<li>Bananas</li><li>Carrots</li><li>Potatoes</li><li>Yogurt</li><li>Cheese</li><li>Milk</li><li>Bread</li><li>Butter</li><li>Jam</li><li>Coffee</li>", "search-notes": [{"id": "grocery-list", "folder": "Home", "modifiedAt": "2025-01-21 13:15:50", "snippet": "- Bananas - Carrots", "title": "Grocery list"}, {"id": "carbonara-recipe", "folder": "Notes", "modifiedAt": "2025-01-21 13:12:46", "snippet": "Ingredients:", "title": "Carbonara Recipe"}, {"id": "weekly-todos", "folder": "Work", "modifiedAt": "2025-01-21 13:11:27", "snippet": "Fix bugs, Implement user profile page", "title": "Weekly to-dos"}, {"id": "budget", "folder": "Notes", "modifiedAt": "2025-01-21 13:09:52", "snippet": "Food: $100, Rent: $1000, Bills: $100", "title": "Budget"}]}}, {"input": "@apple-notes Create a new note of a carbonara recipe", "mocks": {"create-note": "Successfully called \"create-note\""}, "expected": [{"callsTool": "create-note"}]}, {"input": "@apple-notes Find me all my note ideas", "mocks": {"search-notes": [{"id": "app-ideas", "folder": "Ideas", "modifiedAt": "2025-01-21 15:30:22", "snippet": "1. Recipe manager with AI suggestions 2. Plant care reminder app", "title": "App Ideas 2025"}, {"id": "business-ideas", "folder": "Ideas", "modifiedAt": "2025-01-19 18:45:12", "snippet": "Subscription box for sustainable products, Online workshop platform", "title": "Startup Ideas"}, {"id": "shopping-list", "folder": "Personal", "modifiedAt": "2025-01-21 16:10:05", "snippet": "Milk, Bread, Eggs", "title": "Grocery List"}, {"id": "meeting-notes", "folder": "Work", "modifiedAt": "2025-01-21 14:25:18", "snippet": "Q1 goals discussion, team updates", "title": "Team Meeting 01/21"}, {"id": "gift-ideas", "folder": "Ideas", "modifiedAt": "2025-01-17 20:30:15", "snippet": "Dad: vintage vinyl records", "title": "Birthday Gift Ideas"}, {"id": "workout", "folder": "Health", "modifiedAt": "2025-01-21 07:00:00", "snippet": "Monday: Upper body, Tuesday: Lower body", "title": "Workout Schedule"}]}, "expected": [{"callsTool": "search-notes"}, {"meetsCriteria": "Returns a list of 3 notes, each with a link to the note."}]}, {"input": "@apple-notes which notes have a checklist?", "mocks": {"search-notes": [{"id": "grocery-list", "folder": "Home", "checklist": true, "modifiedAt": "2025-01-21 13:15:50", "snippet": "- Bananas - Carrots", "title": "Grocery list"}, {"id": "moving-tasks", "folder": "Personal", "checklist": true, "modifiedAt": "2025-01-20 09:30:00", "snippet": "- Pack kitchen - Call movers - Update address", "title": "Moving Checklist"}, {"id": "project-milestones", "folder": "Work", "checklist": true, "modifiedAt": "2025-01-19 16:45:22", "snippet": "- Design review - User testing - Launch prep", "title": "Q1 Project Milestones"}, {"id": "random-note", "folder": "Notes", "checklist": false, "modifiedAt": "2025-01-21 10:20:15", "snippet": "Just some regular text without any checklist items", "title": "Random Thoughts"}, {"id": "travel-packing", "folder": "Travel", "checklist": true, "modifiedAt": "2025-01-18 20:15:30", "snippet": "- Passport - Chargers - Toiletries", "title": "Paris Trip Packing"}]}, "expected": [{"callsTool": "search-notes"}, {"meetsCriteria": "Returns a list of 4 notes, each with a link to the note."}]}, {"input": "@apple-notes Add gift ideas for my dad in birthday gifts note", "mocks": {"search-notes": [{"id": "birthday-gifts", "folder": "Notes", "modifiedAt": "2025-01-21 13:15:50", "snippet": "Some gift ideas", "title": "Birthday gifts"}], "update-note": "Successfully updated the note", "get-note-content": "<div><h1>Birthday gifts</h1></div>\n<div><br></div>\n<div><h2>Dad</h2></div>\n<ul>\n<li>Vintage vinyl records</li>\n</ul>\n<div><br></div>\n<div><h2>Mom</h2></div>\n<ul>\n<li>Rock concert</li>\n<li>Go to the restaurant together</li>\n</ul>\n"}, "expected": [{"callsTool": "search-notes"}, {"callsTool": {"name": "get-note-content", "arguments": {"noteId": "birthday-gifts"}}}, {"callsTool": {"arguments": {"noteId": "birthday-gifts"}, "name": "update-note"}}]}]}, "preferences": [{"name": "accounts", "title": "Note accessories", "type": "checkbox", "default": false, "label": "Show account name", "description": "Show the account name next to a note", "required": false}, {"name": "folders", "type": "checkbox", "default": true, "label": "Show folder name", "description": "Show the folder name next to a note", "required": false}, {"name": "modificationDate", "type": "checkbox", "default": false, "label": "Show modification date", "description": "Show the modification date next to each note", "required": false}, {"name": "checklist", "type": "checkbox", "default": true, "label": "Show checklist status", "description": "Show a checklist icon with its status next to each note", "required": false}, {"name": "links", "type": "checkbox", "default": true, "label": "Show links", "description": "Show a link icon next to each note that references other notes.", "required": false}, {"name": "backlinks", "type": "checkbox", "default": true, "label": "Show backlinks", "description": "Show a backlink icon next to each note that has been referenced by other notes.", "required": false}, {"name": "tags", "type": "checkbox", "default": true, "label": "Show tags", "description": "Show a tag icon next to each note if it has any tags.", "required": false}, {"name": "locked", "type": "checkbox", "default": true, "label": "Show if a note is password-protected", "description": "Show a lock icon next to each note if it is password-protected", "required": false}, {"name": "shared", "type": "checkbox", "default": true, "label": "Show if a note is shared with someone else", "description": "Show a person icon next to each note if it is shared with someone else", "required": false}, {"name": "openSeparately", "title": "Note actions", "type": "checkbox", "default": false, "label": "Open notes in separate windows by default", "description": "Pressing ⏎ will open notes in separate windows by default", "required": false}], "dependencies": {"@raycast/api": "^1.89.0", "@raycast/utils": "^1.18.1", "date-fns": "^3.6.0", "lodash": "^4.17.21", "node-html-markdown": "^1.3.0"}, "devDependencies": {"@raycast/eslint-config": "^1.0.8", "@types/lodash": "^4.17.4", "@types/node": "^20.12.12", "@types/react": "^18.3.2", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "prettier": "3.2.5", "react": "^18.3.1", "typescript": "^5.4.5"}, "scripts": {"build": "ray build -e dist", "dev": "ray develop", "lint": "ray lint", "fix-lint": "ray lint --fix", "publish": "npx @raycast/api@latest publish"}}