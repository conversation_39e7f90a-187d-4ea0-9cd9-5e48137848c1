{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "obsidian", "title": "Obsidian", "description": "Control Obsidian with Raycast", "icon": "extension_icon.png", "author": "<PERSON><PERSON><PERSON><PERSON>", "categories": ["Productivity"], "contributors": ["Kevin<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ofalva<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "psychomane"], "license": "MIT", "preferences": [{"name": "vaultPath", "type": "textfield", "placeholder": "path/to/vault", "title": "Path to Vault", "required": false, "description": "Specify the path or multiple paths (comma separated) to your vault/vaults"}, {"name": "configFileName", "type": "textfield", "placeholder": ".obsidian", "title": "Config filename", "required": false, "default": ".obsidian", "description": "Override the vault config filename (default: .obsidian)"}, {"name": "excludedFolders", "type": "textfield", "placeholder": "folder1, folder2, ...", "title": "Exclude following folders", "required": false, "description": "Specify which folders to exclude (comma separated)"}, {"name": "removeYAML", "title": "Remove content", "label": "Hide YAML frontmatter", "type": "checkbox", "required": false, "description": "Hide YAML frontmatter for copying and viewing notes"}, {"name": "removeLatex", "label": "Hide LaTeX", "type": "checkbox", "required": false, "description": "Hide LaTeX (surrounded by $$ or $) for copying and viewing notes"}, {"name": "removeLinks", "label": "Hide Links", "type": "checkbox", "required": false, "description": "Hide links for copying and viewing notes"}], "commands": [{"name": "searchNoteCommand", "title": "Search Note", "subtitle": "Obsidian", "description": "Search, open, view, copy, paste and edit any note in Obsidian.", "mode": "view", "arguments": [{"name": "searchArgument", "placeholder": "Note", "type": "text", "required": false}, {"name": "tagArgument", "placeholder": "Tag", "type": "text", "required": false}], "preferences": [{"name": "appendTemplate", "type": "textfield", "title": "Template for Append action", "required": false, "description": "Specify a template for Append action (e.g. '- {content}')"}, {"name": "appendSelectedTemplate", "type": "textfield", "title": "Template for Append Selected Text action", "required": false, "description": "Specify a template for Append Selected Text action (e.g. '- {content}')"}, {"name": "showDetail", "type": "checkbox", "label": "Show Detail", "title": "Show Detail", "required": false, "description": "Show the notes content in a detail view", "default": true}, {"name": "showMetadata", "type": "checkbox", "label": "Show Metadata", "title": "Show Metadata", "required": false, "description": "Show the notes metadata in a detail view (only works when Show Detail is enabled)", "default": false}, {"name": "searchContent", "type": "checkbox", "label": "Search Content", "title": "Search Content", "required": false, "description": "Use the content of notes for searching", "default": false}, {"name": "fuzzySearch", "type": "checkbox", "label": "Use Fuzzy Search", "title": "Use Fuzzy Search", "required": false, "description": "If fuzzy search should be used", "default": false}, {"name": "primaryAction", "type": "dropdown", "title": "Primary Action", "required": false, "description": "Select a primary action to be executed on enter", "defaultValue": "quicklook", "data": [{"title": "Quick Look", "value": "quicklook"}, {"title": "Open in Obsidian", "value": "obsidian"}, {"title": "Open in new Obsidian tab", "value": "newpane"}, {"title": "Open in default app", "value": "defaultapp"}]}]}, {"name": "starredNotesCommand", "title": "Bookmarked Notes", "subtitle": "Obsidian", "description": "Search, open, view, copy, paste and edit bookmarked notes in Obsidian.", "mode": "view", "arguments": [{"name": "searchArgument", "placeholder": "Note", "type": "text", "required": false}, {"name": "tagArgument", "placeholder": "Tag", "type": "text", "required": false}], "preferences": [{"name": "appendTemplate", "type": "textfield", "title": "Template for Append action", "required": false, "description": "Specify a template for Append action (e.g. '- {content}')"}, {"name": "appendSelectedTemplate", "type": "textfield", "title": "Template for Append Selected Text action", "required": false, "description": "Specify a template for Append Selected Text action (e.g. '- {content}')"}, {"name": "showDetail", "type": "checkbox", "label": "Show Detail", "title": "Show Detail", "required": false, "description": "Show the notes content in a detail view", "default": true}, {"name": "showMetadata", "type": "checkbox", "label": "Show Metadata", "title": "Show Metadata", "required": false, "description": "Show the notes metadata in a detail view (only works when Show Detail is enabled)", "default": false}, {"name": "searchContent", "type": "checkbox", "label": "Search Content", "title": "Search Content", "required": false, "description": "Use the content of notes for searching", "default": false}, {"name": "primaryAction", "type": "dropdown", "title": "Primary Action", "required": false, "description": "Select a primary action to be executed on enter", "defaultValue": "quicklook", "data": [{"title": "Quick Look", "value": "quicklook"}, {"title": "Open in Obsidian", "value": "obsidian"}, {"title": "Open in new Obsidian tab", "value": "newpane"}, {"title": "Open in default app", "value": "defaultapp"}]}]}, {"name": "openVaultCommand", "title": "Open Vault", "subtitle": "Obsidian", "description": "Open a vault in Obsidian.", "mode": "view"}, {"name": "dailyNoteAppendCommand", "title": "Append to Daily Note", "subtitle": "Obsidian", "description": "Append text to your daily note", "mode": "view", "arguments": [{"name": "text", "placeholder": "Take out the trash", "type": "text", "required": true}], "preferences": [{"name": "appendTemplate", "type": "textfield", "title": "Template for Daily Note Append action", "required": false, "description": "Specify a template for Daily Note Append action (e.g. '- {content}')"}, {"name": "<PERSON><PERSON><PERSON>", "type": "textfield", "title": "Name of Obsidian vault where note is", "required": false, "description": "Name of Obsidian vault where note is"}, {"name": "heading", "type": "textfield", "title": "Name of heading in note in which to append", "required": false, "description": "If no heading is set, text will be appended to the end of the daily note"}, {"name": "prepend", "type": "checkbox", "label": "Prepend", "title": "Prepend", "required": false, "description": "Prepend the text instead of appending", "default": false}, {"name": "silent", "type": "checkbox", "label": "Silent Mode", "title": "Silent Mode", "required": false, "description": "Don't open daily note when appending to the daily note.", "default": true}]}, {"name": "dailyNoteCommand", "title": "Daily Note", "subtitle": "Obsidian", "description": "Open daily note in Obsidian. Will create new daily note when it doesn't exist yet.", "mode": "view"}, {"name": "createNoteCommand", "title": "Create Note", "subtitle": "Obsidian", "description": "Create new note", "mode": "view", "preferences": [{"name": "blankNote", "type": "checkbox", "label": "Blank Note", "title": "Blank Note", "required": false, "description": "Create a blank note", "default": false}, {"name": "openOnCreate", "type": "checkbox", "title": "Open Note on Creation", "label": "Open Note on Creation", "default": true, "required": false, "description": "Open the created note in Obsidian"}, {"name": "prefPath", "type": "textfield", "placeholder": "path/to/note", "title": "Default Path", "required": false, "description": "The default path where a new note will be created", "default": ""}, {"name": "prefNoteName", "type": "textfield", "title": "Default Note Name", "required": false, "description": "The default note name if no name is specified", "default": "Untitled"}, {"name": "prefNote<PERSON><PERSON>nt", "type": "textfield", "title": "Default Note Content", "required": false, "description": "The default note content (supports templates)", "default": ""}, {"name": "fillFormWithDefaults", "type": "checkbox", "label": "Fill form with defaults", "title": "Fill form with defaults", "required": false, "default": false, "description": "Fill form with default values"}, {"name": "prefTag", "type": "textfield", "placeholder": "untagged", "title": "Default <PERSON>", "required": false, "description": "The default selected tag", "default": ""}, {"name": "tags", "type": "textfield", "placeholder": "tag1, tag2, tag3, ...", "title": "Tags", "required": false, "description": "The tags which will be suggested on note creation", "default": ""}, {"name": "folderActions", "type": "textfield", "placeholder": "folder1, folder2, folder3, ...", "title": "Folder Actions", "required": false, "description": "Add actions to folders (comma separated)", "default": ""}]}, {"name": "randomNoteCommand", "title": "Random Note", "subtitle": "Obsidian", "description": "Open random note", "mode": "view", "preferences": [{"name": "appendTemplate", "type": "textfield", "title": "Template for Append action", "required": false, "description": "Specify a template for Append action (e.g. '- {content}')"}, {"name": "appendSelectedTemplate", "type": "textfield", "title": "Template for Append Selected Text action", "required": false, "description": "Specify a template for Append Selected Text action (e.g. '- {content}')"}, {"name": "primaryAction", "type": "dropdown", "title": "Primary Action", "required": false, "description": "Select a primary action to be executed on enter", "defaultValue": "quicklook", "data": [{"title": "Quick Look", "value": "quicklook"}, {"title": "Open in Obsidian", "value": "obsidian"}, {"title": "Open in new Obsidian tab", "value": "newpane"}, {"title": "Open in default app", "value": "defaultapp"}]}]}, {"name": "searchMedia", "title": "Search Media", "subtitle": "Obsidian", "description": "Search for media in your vault", "mode": "view", "arguments": [{"name": "searchArgument", "placeholder": "Name", "type": "text", "required": false}, {"name": "typeArgument", "placeholder": "Type", "type": "text", "required": false}], "preferences": [{"name": "excludedFolders", "type": "textfield", "placeholder": "folder1, folder2, ...", "title": "Exclude following folders", "required": false, "description": "Specify which folders to exclude (comma separated)"}, {"name": "imageSize", "type": "dropdown", "title": "Image Size", "required": false, "description": "Select the image size to display", "defaultValue": "medium", "data": [{"title": "Small", "value": "small"}, {"title": "Medium", "value": "medium"}, {"title": "Large", "value": "large"}]}]}, {"name": "obsidianMenuBar", "title": "Obsidian Menu Bar", "subtitle": "Obsidian", "description": "See your pinned notes at a glance and control actions from the menu bar.", "mode": "menu-bar"}, {"name": "appendTaskCommand", "title": "Append Task", "subtitle": "Obsidian", "description": "Append a task to a note of your choice", "mode": "view", "arguments": [{"name": "text", "placeholder": "Your task", "type": "text", "required": true}, {"name": "dueDate", "placeholder": "YYYY-MM-DD", "type": "text", "required": false}], "preferences": [{"description": "Path of note", "name": "notePath", "required": true, "title": "Path of the note you wish to append the task to", "type": "textfield"}, {"name": "noteTag", "required": false, "title": "Tag to append to the beginning of the task.", "description": "Defaults to #task for compatibility with Obsidian Tasks.", "type": "textfield", "default": "#task"}, {"name": "creationDate", "type": "checkbox", "title": "Creation Date", "label": "Append creation date to the task", "required": false, "description": "➕ YYYY-MM-DD (Current date)", "default": false}, {"description": "Name of Obsidian vault where note is", "name": "<PERSON><PERSON><PERSON>", "required": false, "title": "Name of Obsidian vault where note is", "type": "textfield"}, {"description": "If no heading is not set, text will be appended to the end of the note", "name": "heading", "required": false, "title": "Name of heading in note in which to append", "type": "textfield"}, {"default": true, "description": "Don't open note when appending.", "label": "Silent Mode", "name": "silent", "required": false, "title": "Silent Mode", "type": "checkbox"}]}], "dependencies": {"@raycast/api": "^1.94.0", "@types/uuid": "^8.3.4", "fuse.js": "^7.0.0", "luxon": "^3.4.4", "uuid": "^8.3.2", "yaml": "^2.1.1"}, "devDependencies": {"@raycast/eslint-config": "^1.0.6", "@types/luxon": "^3.4.2", "@types/node": "~16.10.0", "@types/react": "^17.0.52", "eslint": "^8.42.0", "prettier": "^2.8.8", "typescript": "^4.5.4"}, "scripts": {"build": "ray build -e dist", "dev": "ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "publish": "ray publish"}}