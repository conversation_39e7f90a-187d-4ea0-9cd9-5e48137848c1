"use strict";var Ed=Object.create;var wn=Object.defineProperty;var vd=Object.getOwnPropertyDescriptor;var Ad=Object.getOwnPropertyNames;var Od=Object.getPrototypeOf,Id=Object.prototype.hasOwnProperty;var S=(s,e)=>()=>(e||s((e={exports:{}}).exports,e),e.exports),Dd=(s,e)=>{for(var t in e)wn(s,t,{get:e[t],enumerable:!0})},ol=(s,e,t,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of Ad(e))!Id.call(s,r)&&r!==t&&wn(s,r,{get:()=>e[r],enumerable:!(n=vd(e,r))||n.enumerable});return s};var se=(s,e,t)=>(t=s!=null?Ed(Od(s)):{},ol(e||!s||!s.__esModule?wn(t,"default",{value:s,enumerable:!0}):t,s)),Cd=s=>ol(wn({},"__esModule",{value:!0}),s);var L=S(J=>{"use strict";var li=Symbol.for("yaml.alias"),kl=Symbol.for("yaml.document"),bn=Symbol.for("yaml.map"),Tl=Symbol.for("yaml.pair"),ci=Symbol.for("yaml.scalar"),Nn=Symbol.for("yaml.seq"),Le=Symbol.for("yaml.node.type"),Md=s=>!!s&&typeof s=="object"&&s[Le]===li,Ld=s=>!!s&&typeof s=="object"&&s[Le]===kl,Fd=s=>!!s&&typeof s=="object"&&s[Le]===bn,xd=s=>!!s&&typeof s=="object"&&s[Le]===Tl,bl=s=>!!s&&typeof s=="object"&&s[Le]===ci,_d=s=>!!s&&typeof s=="object"&&s[Le]===Nn;function Nl(s){if(s&&typeof s=="object")switch(s[Le]){case bn:case Nn:return!0}return!1}function qd(s){if(s&&typeof s=="object")switch(s[Le]){case li:case bn:case ci:case Nn:return!0}return!1}var Pd=s=>(bl(s)||Nl(s))&&!!s.anchor;J.ALIAS=li;J.DOC=kl;J.MAP=bn;J.NODE_TYPE=Le;J.PAIR=Tl;J.SCALAR=ci;J.SEQ=Nn;J.hasAnchor=Pd;J.isAlias=Md;J.isCollection=Nl;J.isDocument=Ld;J.isMap=Fd;J.isNode=qd;J.isPair=xd;J.isScalar=bl;J.isSeq=_d});var ss=S(ui=>{"use strict";var Y=L(),ne=Symbol("break visit"),El=Symbol("skip children"),Oe=Symbol("remove node");function En(s,e){let t=vl(e);Y.isDocument(s)?At(null,s.contents,t,Object.freeze([s]))===Oe&&(s.contents=null):At(null,s,t,Object.freeze([]))}En.BREAK=ne;En.SKIP=El;En.REMOVE=Oe;function At(s,e,t,n){let r=Al(s,e,t,n);if(Y.isNode(r)||Y.isPair(r))return Ol(s,n,r),At(s,r,t,n);if(typeof r!="symbol"){if(Y.isCollection(e)){n=Object.freeze(n.concat(e));for(let i=0;i<e.items.length;++i){let o=At(i,e.items[i],t,n);if(typeof o=="number")i=o-1;else{if(o===ne)return ne;o===Oe&&(e.items.splice(i,1),i-=1)}}}else if(Y.isPair(e)){n=Object.freeze(n.concat(e));let i=At("key",e.key,t,n);if(i===ne)return ne;i===Oe&&(e.key=null);let o=At("value",e.value,t,n);if(o===ne)return ne;o===Oe&&(e.value=null)}}return r}async function vn(s,e){let t=vl(e);Y.isDocument(s)?await Ot(null,s.contents,t,Object.freeze([s]))===Oe&&(s.contents=null):await Ot(null,s,t,Object.freeze([]))}vn.BREAK=ne;vn.SKIP=El;vn.REMOVE=Oe;async function Ot(s,e,t,n){let r=await Al(s,e,t,n);if(Y.isNode(r)||Y.isPair(r))return Ol(s,n,r),Ot(s,r,t,n);if(typeof r!="symbol"){if(Y.isCollection(e)){n=Object.freeze(n.concat(e));for(let i=0;i<e.items.length;++i){let o=await Ot(i,e.items[i],t,n);if(typeof o=="number")i=o-1;else{if(o===ne)return ne;o===Oe&&(e.items.splice(i,1),i-=1)}}}else if(Y.isPair(e)){n=Object.freeze(n.concat(e));let i=await Ot("key",e.key,t,n);if(i===ne)return ne;i===Oe&&(e.key=null);let o=await Ot("value",e.value,t,n);if(o===ne)return ne;o===Oe&&(e.value=null)}}return r}function vl(s){return typeof s=="object"&&(s.Collection||s.Node||s.Value)?Object.assign({Alias:s.Node,Map:s.Node,Scalar:s.Node,Seq:s.Node},s.Value&&{Map:s.Value,Scalar:s.Value,Seq:s.Value},s.Collection&&{Map:s.Collection,Seq:s.Collection},s):s}function Al(s,e,t,n){if(typeof t=="function")return t(s,e,n);if(Y.isMap(e))return t.Map?.(s,e,n);if(Y.isSeq(e))return t.Seq?.(s,e,n);if(Y.isPair(e))return t.Pair?.(s,e,n);if(Y.isScalar(e))return t.Scalar?.(s,e,n);if(Y.isAlias(e))return t.Alias?.(s,e,n)}function Ol(s,e,t){let n=e[e.length-1];if(Y.isCollection(n))n.items[s]=t;else if(Y.isPair(n))s==="key"?n.key=t:n.value=t;else if(Y.isDocument(n))n.contents=t;else{let r=Y.isAlias(n)?"alias":"scalar";throw new Error(`Cannot replace node with ${r} parent`)}}ui.visit=En;ui.visitAsync=vn});var fi=S(Dl=>{"use strict";var Il=L(),$d=ss(),Rd={"!":"%21",",":"%2C","[":"%5B","]":"%5D","{":"%7B","}":"%7D"},Vd=s=>s.replace(/[!,[\]{}]/g,e=>Rd[e]),ns=class s{constructor(e,t){this.docStart=null,this.docEnd=!1,this.yaml=Object.assign({},s.defaultYaml,e),this.tags=Object.assign({},s.defaultTags,t)}clone(){let e=new s(this.yaml,this.tags);return e.docStart=this.docStart,e}atDocument(){let e=new s(this.yaml,this.tags);switch(this.yaml.version){case"1.1":this.atNextDocument=!0;break;case"1.2":this.atNextDocument=!1,this.yaml={explicit:s.defaultYaml.explicit,version:"1.2"},this.tags=Object.assign({},s.defaultTags);break}return e}add(e,t){this.atNextDocument&&(this.yaml={explicit:s.defaultYaml.explicit,version:"1.1"},this.tags=Object.assign({},s.defaultTags),this.atNextDocument=!1);let n=e.trim().split(/[ \t]+/),r=n.shift();switch(r){case"%TAG":{if(n.length!==2&&(t(0,"%TAG directive should contain exactly two parts"),n.length<2))return!1;let[i,o]=n;return this.tags[i]=o,!0}case"%YAML":{if(this.yaml.explicit=!0,n.length!==1)return t(0,"%YAML directive should contain exactly one part"),!1;let[i]=n;if(i==="1.1"||i==="1.2")return this.yaml.version=i,!0;{let o=/^\d+\.\d+$/.test(i);return t(6,`Unsupported YAML version ${i}`,o),!1}}default:return t(0,`Unknown directive ${r}`,!0),!1}}tagName(e,t){if(e==="!")return"!";if(e[0]!=="!")return t(`Not a valid tag: ${e}`),null;if(e[1]==="<"){let o=e.slice(2,-1);return o==="!"||o==="!!"?(t(`Verbatim tags aren't resolved, so ${e} is invalid.`),null):(e[e.length-1]!==">"&&t("Verbatim tags must end with a >"),o)}let[,n,r]=e.match(/^(.*!)([^!]*)$/s);r||t(`The ${e} tag has no suffix`);let i=this.tags[n];if(i)try{return i+decodeURIComponent(r)}catch(o){return t(String(o)),null}return n==="!"?e:(t(`Could not resolve tag: ${e}`),null)}tagString(e){for(let[t,n]of Object.entries(this.tags))if(e.startsWith(n))return t+Vd(e.substring(n.length));return e[0]==="!"?e:`!<${e}>`}toString(e){let t=this.yaml.explicit?[`%YAML ${this.yaml.version||"1.2"}`]:[],n=Object.entries(this.tags),r;if(e&&n.length>0&&Il.isNode(e.contents)){let i={};$d.visit(e.contents,(o,a)=>{Il.isNode(a)&&a.tag&&(i[a.tag]=!0)}),r=Object.keys(i)}else r=[];for(let[i,o]of n)i==="!!"&&o==="tag:yaml.org,2002:"||(!e||r.some(a=>a.startsWith(o)))&&t.push(`%TAG ${i} ${o}`);return t.join(`
`)}};ns.defaultYaml={explicit:!1,version:"1.2"};ns.defaultTags={"!!":"tag:yaml.org,2002:"};Dl.Directives=ns});var An=S(rs=>{"use strict";var Cl=L(),Bd=ss();function Ud(s){if(/[\x00-\x19\s,[\]{}]/.test(s)){let t=`Anchor must not contain whitespace or control characters: ${JSON.stringify(s)}`;throw new Error(t)}return!0}function Ml(s){let e=new Set;return Bd.visit(s,{Value(t,n){n.anchor&&e.add(n.anchor)}}),e}function Ll(s,e){for(let t=1;;++t){let n=`${s}${t}`;if(!e.has(n))return n}}function Wd(s,e){let t=[],n=new Map,r=null;return{onAnchor:i=>{t.push(i),r||(r=Ml(s));let o=Ll(e,r);return r.add(o),o},setAnchors:()=>{for(let i of t){let o=n.get(i);if(typeof o=="object"&&o.anchor&&(Cl.isScalar(o.node)||Cl.isCollection(o.node)))o.node.anchor=o.anchor;else{let a=new Error("Failed to resolve repeated object (this should not happen)");throw a.source=i,a}}},sourceObjects:n}}rs.anchorIsValid=Ud;rs.anchorNames=Ml;rs.createNodeAnchors=Wd;rs.findNewAnchor=Ll});var hi=S(Fl=>{"use strict";function is(s,e,t,n){if(n&&typeof n=="object")if(Array.isArray(n))for(let r=0,i=n.length;r<i;++r){let o=n[r],a=is(s,n,String(r),o);a===void 0?delete n[r]:a!==o&&(n[r]=a)}else if(n instanceof Map)for(let r of Array.from(n.keys())){let i=n.get(r),o=is(s,n,r,i);o===void 0?n.delete(r):o!==i&&n.set(r,o)}else if(n instanceof Set)for(let r of Array.from(n)){let i=is(s,n,r,r);i===void 0?n.delete(r):i!==r&&(n.delete(r),n.add(i))}else for(let[r,i]of Object.entries(n)){let o=is(s,n,r,i);o===void 0?delete n[r]:o!==i&&(n[r]=o)}return s.call(e,t,n)}Fl.applyReviver=is});var Be=S(_l=>{"use strict";var Yd=L();function xl(s,e,t){if(Array.isArray(s))return s.map((n,r)=>xl(n,String(r),t));if(s&&typeof s.toJSON=="function"){if(!t||!Yd.hasAnchor(s))return s.toJSON(e,t);let n={aliasCount:0,count:1,res:void 0};t.anchors.set(s,n),t.onCreate=i=>{n.res=i,delete t.onCreate};let r=s.toJSON(e,t);return t.onCreate&&t.onCreate(r),r}return typeof s=="bigint"&&!t?.keep?Number(s):s}_l.toJS=xl});var On=S(Pl=>{"use strict";var Hd=hi(),ql=L(),Kd=Be(),di=class{constructor(e){Object.defineProperty(this,ql.NODE_TYPE,{value:e})}clone(){let e=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return this.range&&(e.range=this.range.slice()),e}toJS(e,{mapAsMap:t,maxAliasCount:n,onAnchor:r,reviver:i}={}){if(!ql.isDocument(e))throw new TypeError("A document argument is required");let o={anchors:new Map,doc:e,keep:!0,mapAsMap:t===!0,mapKeyWarned:!1,maxAliasCount:typeof n=="number"?n:100},a=Kd.toJS(this,"",o);if(typeof r=="function")for(let{count:l,res:c}of o.anchors.values())r(c,l);return typeof i=="function"?Hd.applyReviver(i,{"":a},"",a):a}};Pl.NodeBase=di});var os=S(Rl=>{"use strict";var jd=An(),$l=ss(),In=L(),Zd=On(),Jd=Be(),mi=class extends Zd.NodeBase{constructor(e){super(In.ALIAS),this.source=e,Object.defineProperty(this,"tag",{set(){throw new Error("Alias nodes cannot have tags")}})}resolve(e){let t;return $l.visit(e,{Node:(n,r)=>{if(r===this)return $l.visit.BREAK;r.anchor===this.source&&(t=r)}}),t}toJSON(e,t){if(!t)return{source:this.source};let{anchors:n,doc:r,maxAliasCount:i}=t,o=this.resolve(r);if(!o){let l=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new ReferenceError(l)}let a=n.get(o);if(a||(Jd.toJS(o,null,t),a=n.get(o)),!a||a.res===void 0){let l="This should not happen: Alias anchor was not resolved?";throw new ReferenceError(l)}if(i>=0&&(a.count+=1,a.aliasCount===0&&(a.aliasCount=Dn(r,o,n)),a.count*a.aliasCount>i)){let l="Excessive alias count indicates a resource exhaustion attack";throw new ReferenceError(l)}return a.res}toString(e,t,n){let r=`*${this.source}`;if(e){if(jd.anchorIsValid(this.source),e.options.verifyAliasOrder&&!e.anchors.has(this.source)){let i=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new Error(i)}if(e.implicitKey)return`${r} `}return r}};function Dn(s,e,t){if(In.isAlias(e)){let n=e.resolve(s),r=t&&n&&t.get(n);return r?r.count*r.aliasCount:0}else if(In.isCollection(e)){let n=0;for(let r of e.items){let i=Dn(s,r,t);i>n&&(n=i)}return n}else if(In.isPair(e)){let n=Dn(s,e.key,t),r=Dn(s,e.value,t);return Math.max(n,r)}return 1}Rl.Alias=mi});var U=S(pi=>{"use strict";var Gd=L(),zd=On(),Qd=Be(),Xd=s=>!s||typeof s!="function"&&typeof s!="object",Ue=class extends zd.NodeBase{constructor(e){super(Gd.SCALAR),this.value=e}toJSON(e,t){return t?.keep?this.value:Qd.toJS(this.value,e,t)}toString(){return String(this.value)}};Ue.BLOCK_FOLDED="BLOCK_FOLDED";Ue.BLOCK_LITERAL="BLOCK_LITERAL";Ue.PLAIN="PLAIN";Ue.QUOTE_DOUBLE="QUOTE_DOUBLE";Ue.QUOTE_SINGLE="QUOTE_SINGLE";pi.Scalar=Ue;pi.isScalarValue=Xd});var as=S(Bl=>{"use strict";var em=os(),at=L(),Vl=U(),tm="tag:yaml.org,2002:";function sm(s,e,t){if(e){let n=t.filter(i=>i.tag===e),r=n.find(i=>!i.format)??n[0];if(!r)throw new Error(`Tag ${e} not found`);return r}return t.find(n=>n.identify?.(s)&&!n.format)}function nm(s,e,t){if(at.isDocument(s)&&(s=s.contents),at.isNode(s))return s;if(at.isPair(s)){let f=t.schema[at.MAP].createNode?.(t.schema,null,t);return f.items.push(s),f}(s instanceof String||s instanceof Number||s instanceof Boolean||typeof BigInt<"u"&&s instanceof BigInt)&&(s=s.valueOf());let{aliasDuplicateObjects:n,onAnchor:r,onTagObj:i,schema:o,sourceObjects:a}=t,l;if(n&&s&&typeof s=="object"){if(l=a.get(s),l)return l.anchor||(l.anchor=r(s)),new em.Alias(l.anchor);l={anchor:null,node:null},a.set(s,l)}e?.startsWith("!!")&&(e=tm+e.slice(2));let c=sm(s,e,o.tags);if(!c){if(s&&typeof s.toJSON=="function"&&(s=s.toJSON()),!s||typeof s!="object"){let f=new Vl.Scalar(s);return l&&(l.node=f),f}c=s instanceof Map?o[at.MAP]:Symbol.iterator in Object(s)?o[at.SEQ]:o[at.MAP]}i&&(i(c),delete t.onTagObj);let u=c?.createNode?c.createNode(t.schema,s,t):typeof c?.nodeClass?.from=="function"?c.nodeClass.from(t.schema,s,t):new Vl.Scalar(s);return e?u.tag=e:c.default||(u.tag=c.tag),l&&(l.node=u),u}Bl.createNode=nm});var Mn=S(Cn=>{"use strict";var rm=as(),Ie=L(),im=On();function gi(s,e,t){let n=t;for(let r=e.length-1;r>=0;--r){let i=e[r];if(typeof i=="number"&&Number.isInteger(i)&&i>=0){let o=[];o[i]=n,n=o}else n=new Map([[i,n]])}return rm.createNode(n,void 0,{aliasDuplicateObjects:!1,keepUndefined:!1,onAnchor:()=>{throw new Error("This should not happen, please report a bug.")},schema:s,sourceObjects:new Map})}var Ul=s=>s==null||typeof s=="object"&&!!s[Symbol.iterator]().next().done,yi=class extends im.NodeBase{constructor(e,t){super(e),Object.defineProperty(this,"schema",{value:t,configurable:!0,enumerable:!1,writable:!0})}clone(e){let t=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return e&&(t.schema=e),t.items=t.items.map(n=>Ie.isNode(n)||Ie.isPair(n)?n.clone(e):n),this.range&&(t.range=this.range.slice()),t}addIn(e,t){if(Ul(e))this.add(t);else{let[n,...r]=e,i=this.get(n,!0);if(Ie.isCollection(i))i.addIn(r,t);else if(i===void 0&&this.schema)this.set(n,gi(this.schema,r,t));else throw new Error(`Expected YAML collection at ${n}. Remaining path: ${r}`)}}deleteIn(e){let[t,...n]=e;if(n.length===0)return this.delete(t);let r=this.get(t,!0);if(Ie.isCollection(r))return r.deleteIn(n);throw new Error(`Expected YAML collection at ${t}. Remaining path: ${n}`)}getIn(e,t){let[n,...r]=e,i=this.get(n,!0);return r.length===0?!t&&Ie.isScalar(i)?i.value:i:Ie.isCollection(i)?i.getIn(r,t):void 0}hasAllNullValues(e){return this.items.every(t=>{if(!Ie.isPair(t))return!1;let n=t.value;return n==null||e&&Ie.isScalar(n)&&n.value==null&&!n.commentBefore&&!n.comment&&!n.tag})}hasIn(e){let[t,...n]=e;if(n.length===0)return this.has(t);let r=this.get(t,!0);return Ie.isCollection(r)?r.hasIn(n):!1}setIn(e,t){let[n,...r]=e;if(r.length===0)this.set(n,t);else{let i=this.get(n,!0);if(Ie.isCollection(i))i.setIn(r,t);else if(i===void 0&&this.schema)this.set(n,gi(this.schema,r,t));else throw new Error(`Expected YAML collection at ${n}. Remaining path: ${r}`)}}};Cn.Collection=yi;Cn.collectionFromPath=gi;Cn.isEmptyPath=Ul});var ls=S(Ln=>{"use strict";var om=s=>s.replace(/^(?!$)(?: $)?/gm,"#");function Si(s,e){return/^\n+$/.test(s)?s.substring(1):e?s.replace(/^(?! *$)/gm,e):s}var am=(s,e,t)=>s.endsWith(`
`)?Si(t,e):t.includes(`
`)?`
`+Si(t,e):(s.endsWith(" ")?"":" ")+t;Ln.indentComment=Si;Ln.lineComment=am;Ln.stringifyComment=om});var Yl=S(cs=>{"use strict";var lm="flow",wi="block",Fn="quoted";function cm(s,e,t="flow",{indentAtStart:n,lineWidth:r=80,minContentWidth:i=20,onFold:o,onOverflow:a}={}){if(!r||r<0)return s;r<i&&(i=0);let l=Math.max(1+i,1+r-e.length);if(s.length<=l)return s;let c=[],u={},f=r-e.length;typeof n=="number"&&(n>r-Math.max(2,i)?c.push(0):f=r-n);let h,d,g=!1,m=-1,p=-1,w=-1;t===wi&&(m=Wl(s,m,e.length),m!==-1&&(f=m+l));for(let E;E=s[m+=1];){if(t===Fn&&E==="\\"){switch(p=m,s[m+1]){case"x":m+=3;break;case"u":m+=5;break;case"U":m+=9;break;default:m+=1}w=m}if(E===`
`)t===wi&&(m=Wl(s,m,e.length)),f=m+e.length+l,h=void 0;else{if(E===" "&&d&&d!==" "&&d!==`
`&&d!=="	"){let v=s[m+1];v&&v!==" "&&v!==`
`&&v!=="	"&&(h=m)}if(m>=f)if(h)c.push(h),f=h+l,h=void 0;else if(t===Fn){for(;d===" "||d==="	";)d=E,E=s[m+=1],g=!0;let v=m>w+1?m-2:p-1;if(u[v])return s;c.push(v),u[v]=!0,f=v+l,h=void 0}else g=!0}d=E}if(g&&a&&a(),c.length===0)return s;o&&o();let k=s.slice(0,c[0]);for(let E=0;E<c.length;++E){let v=c[E],O=c[E+1]||s.length;v===0?k=`
${e}${s.slice(0,O)}`:(t===Fn&&u[v]&&(k+=`${s[v]}\\`),k+=`
${e}${s.slice(v+1,O)}`)}return k}function Wl(s,e,t){let n=e,r=e+1,i=s[r];for(;i===" "||i==="	";)if(e<r+t)i=s[++e];else{do i=s[++e];while(i&&i!==`
`);n=e,r=e+1,i=s[r]}return n}cs.FOLD_BLOCK=wi;cs.FOLD_FLOW=lm;cs.FOLD_QUOTED=Fn;cs.foldFlowLines=cm});var fs=S(Hl=>{"use strict";var ye=U(),We=Yl(),_n=(s,e)=>({indentAtStart:e?s.indent.length:s.indentAtStart,lineWidth:s.options.lineWidth,minContentWidth:s.options.minContentWidth}),qn=s=>/^(%|---|\.\.\.)/m.test(s);function um(s,e,t){if(!e||e<0)return!1;let n=e-t,r=s.length;if(r<=n)return!1;for(let i=0,o=0;i<r;++i)if(s[i]===`
`){if(i-o>n)return!0;if(o=i+1,r-o<=n)return!1}return!0}function us(s,e){let t=JSON.stringify(s);if(e.options.doubleQuotedAsJSON)return t;let{implicitKey:n}=e,r=e.options.doubleQuotedMinMultiLineLength,i=e.indent||(qn(s)?"  ":""),o="",a=0;for(let l=0,c=t[l];c;c=t[++l])if(c===" "&&t[l+1]==="\\"&&t[l+2]==="n"&&(o+=t.slice(a,l)+"\\ ",l+=1,a=l,c="\\"),c==="\\")switch(t[l+1]){case"u":{o+=t.slice(a,l);let u=t.substr(l+2,4);switch(u){case"0000":o+="\\0";break;case"0007":o+="\\a";break;case"000b":o+="\\v";break;case"001b":o+="\\e";break;case"0085":o+="\\N";break;case"00a0":o+="\\_";break;case"2028":o+="\\L";break;case"2029":o+="\\P";break;default:u.substr(0,2)==="00"?o+="\\x"+u.substr(2):o+=t.substr(l,6)}l+=5,a=l+1}break;case"n":if(n||t[l+2]==='"'||t.length<r)l+=1;else{for(o+=t.slice(a,l)+`

`;t[l+2]==="\\"&&t[l+3]==="n"&&t[l+4]!=='"';)o+=`
`,l+=2;o+=i,t[l+2]===" "&&(o+="\\"),l+=1,a=l+1}break;default:l+=1}return o=a?o+t.slice(a):t,n?o:We.foldFlowLines(o,i,We.FOLD_QUOTED,_n(e,!1))}function ki(s,e){if(e.options.singleQuote===!1||e.implicitKey&&s.includes(`
`)||/[ \t]\n|\n[ \t]/.test(s))return us(s,e);let t=e.indent||(qn(s)?"  ":""),n="'"+s.replace(/'/g,"''").replace(/\n+/g,`$&
${t}`)+"'";return e.implicitKey?n:We.foldFlowLines(n,t,We.FOLD_FLOW,_n(e,!1))}function It(s,e){let{singleQuote:t}=e.options,n;if(t===!1)n=us;else{let r=s.includes('"'),i=s.includes("'");r&&!i?n=ki:i&&!r?n=us:n=t?ki:us}return n(s,e)}var Ti;try{Ti=new RegExp(`(^|(?<!
))
+(?!
|$)`,"g")}catch{Ti=/\n+(?!\n|$)/g}function xn({comment:s,type:e,value:t},n,r,i){let{blockQuote:o,commentString:a,lineWidth:l}=n.options;if(!o||/\n[\t ]+$/.test(t)||/^\s*$/.test(t))return It(t,n);let c=n.indent||(n.forceBlockIndent||qn(t)?"  ":""),u=o==="literal"?!0:o==="folded"||e===ye.Scalar.BLOCK_FOLDED?!1:e===ye.Scalar.BLOCK_LITERAL?!0:!um(t,l,c.length);if(!t)return u?`|
`:`>
`;let f,h;for(h=t.length;h>0;--h){let O=t[h-1];if(O!==`
`&&O!=="	"&&O!==" ")break}let d=t.substring(h),g=d.indexOf(`
`);g===-1?f="-":t===d||g!==d.length-1?(f="+",i&&i()):f="",d&&(t=t.slice(0,-d.length),d[d.length-1]===`
`&&(d=d.slice(0,-1)),d=d.replace(Ti,`$&${c}`));let m=!1,p,w=-1;for(p=0;p<t.length;++p){let O=t[p];if(O===" ")m=!0;else if(O===`
`)w=p;else break}let k=t.substring(0,w<p?w+1:p);k&&(t=t.substring(k.length),k=k.replace(/\n+/g,`$&${c}`));let v=(m?c?"2":"1":"")+f;if(s&&(v+=" "+a(s.replace(/ ?[\r\n]+/g," ")),r&&r()),!u){let O=t.replace(/\n+/g,`
$&`).replace(/(?:^|\n)([\t ].*)(?:([\n\t ]*)\n(?![\n\t ]))?/g,"$1$2").replace(/\n+/g,`$&${c}`),I=!1,C=_n(n,!0);o!=="folded"&&e!==ye.Scalar.BLOCK_FOLDED&&(C.onOverflow=()=>{I=!0});let T=We.foldFlowLines(`${k}${O}${d}`,c,We.FOLD_BLOCK,C);if(!I)return`>${v}
${c}${T}`}return t=t.replace(/\n+/g,`$&${c}`),`|${v}
${c}${k}${t}${d}`}function fm(s,e,t,n){let{type:r,value:i}=s,{actualString:o,implicitKey:a,indent:l,indentStep:c,inFlow:u}=e;if(a&&i.includes(`
`)||u&&/[[\]{},]/.test(i))return It(i,e);if(!i||/^[\n\t ,[\]{}#&*!|>'"%@`]|^[?-]$|^[?-][ \t]|[\n:][ \t]|[ \t]\n|[\n\t ]#|[\n\t :]$/.test(i))return a||u||!i.includes(`
`)?It(i,e):xn(s,e,t,n);if(!a&&!u&&r!==ye.Scalar.PLAIN&&i.includes(`
`))return xn(s,e,t,n);if(qn(i)){if(l==="")return e.forceBlockIndent=!0,xn(s,e,t,n);if(a&&l===c)return It(i,e)}let f=i.replace(/\n+/g,`$&
${l}`);if(o){let h=m=>m.default&&m.tag!=="tag:yaml.org,2002:str"&&m.test?.test(f),{compat:d,tags:g}=e.doc.schema;if(g.some(h)||d?.some(h))return It(i,e)}return a?f:We.foldFlowLines(f,l,We.FOLD_FLOW,_n(e,!1))}function hm(s,e,t,n){let{implicitKey:r,inFlow:i}=e,o=typeof s.value=="string"?s:Object.assign({},s,{value:String(s.value)}),{type:a}=s;a!==ye.Scalar.QUOTE_DOUBLE&&/[\x00-\x08\x0b-\x1f\x7f-\x9f\u{D800}-\u{DFFF}]/u.test(o.value)&&(a=ye.Scalar.QUOTE_DOUBLE);let l=u=>{switch(u){case ye.Scalar.BLOCK_FOLDED:case ye.Scalar.BLOCK_LITERAL:return r||i?It(o.value,e):xn(o,e,t,n);case ye.Scalar.QUOTE_DOUBLE:return us(o.value,e);case ye.Scalar.QUOTE_SINGLE:return ki(o.value,e);case ye.Scalar.PLAIN:return fm(o,e,t,n);default:return null}},c=l(a);if(c===null){let{defaultKeyType:u,defaultStringType:f}=e.options,h=r&&u||f;if(c=l(h),c===null)throw new Error(`Unsupported default string type ${h}`)}return c}Hl.stringifyString=hm});var hs=S(bi=>{"use strict";var dm=An(),Ye=L(),mm=ls(),pm=fs();function gm(s,e){let t=Object.assign({blockQuote:!0,commentString:mm.stringifyComment,defaultKeyType:null,defaultStringType:"PLAIN",directives:null,doubleQuotedAsJSON:!1,doubleQuotedMinMultiLineLength:40,falseStr:"false",flowCollectionPadding:!0,indentSeq:!0,lineWidth:80,minContentWidth:20,nullStr:"null",simpleKeys:!1,singleQuote:null,trueStr:"true",verifyAliasOrder:!0},s.schema.toStringOptions,e),n;switch(t.collectionStyle){case"block":n=!1;break;case"flow":n=!0;break;default:n=null}return{anchors:new Set,doc:s,flowCollectionPadding:t.flowCollectionPadding?" ":"",indent:"",indentStep:typeof t.indent=="number"?" ".repeat(t.indent):"  ",inFlow:n,options:t}}function ym(s,e){if(e.tag){let r=s.filter(i=>i.tag===e.tag);if(r.length>0)return r.find(i=>i.format===e.format)??r[0]}let t,n;if(Ye.isScalar(e)){n=e.value;let r=s.filter(i=>i.identify?.(n));if(r.length>1){let i=r.filter(o=>o.test);i.length>0&&(r=i)}t=r.find(i=>i.format===e.format)??r.find(i=>!i.format)}else n=e,t=s.find(r=>r.nodeClass&&n instanceof r.nodeClass);if(!t){let r=n?.constructor?.name??typeof n;throw new Error(`Tag not resolved for ${r} value`)}return t}function Sm(s,e,{anchors:t,doc:n}){if(!n.directives)return"";let r=[],i=(Ye.isScalar(s)||Ye.isCollection(s))&&s.anchor;i&&dm.anchorIsValid(i)&&(t.add(i),r.push(`&${i}`));let o=s.tag?s.tag:e.default?null:e.tag;return o&&r.push(n.directives.tagString(o)),r.join(" ")}function wm(s,e,t,n){if(Ye.isPair(s))return s.toString(e,t,n);if(Ye.isAlias(s)){if(e.doc.directives)return s.toString(e);if(e.resolvedAliases?.has(s))throw new TypeError("Cannot stringify circular structure without alias nodes");e.resolvedAliases?e.resolvedAliases.add(s):e.resolvedAliases=new Set([s]),s=s.resolve(e.doc)}let r,i=Ye.isNode(s)?s:e.doc.createNode(s,{onTagObj:l=>r=l});r||(r=ym(e.doc.schema.tags,i));let o=Sm(i,r,e);o.length>0&&(e.indentAtStart=(e.indentAtStart??0)+o.length+1);let a=typeof r.stringify=="function"?r.stringify(i,e,t,n):Ye.isScalar(i)?pm.stringifyString(i,e,t,n):i.toString(e,t,n);return o?Ye.isScalar(i)||a[0]==="{"||a[0]==="["?`${o} ${a}`:`${o}
${e.indent}${a}`:a}bi.createStringifyContext=gm;bi.stringify=wm});var Jl=S(Zl=>{"use strict";var Fe=L(),Kl=U(),jl=hs(),ds=ls();function km({key:s,value:e},t,n,r){let{allNullValues:i,doc:o,indent:a,indentStep:l,options:{commentString:c,indentSeq:u,simpleKeys:f}}=t,h=Fe.isNode(s)&&s.comment||null;if(f){if(h)throw new Error("With simple keys, key nodes cannot have comments");if(Fe.isCollection(s)||!Fe.isNode(s)&&typeof s=="object"){let C="With simple keys, collection cannot be used as a key value";throw new Error(C)}}let d=!f&&(!s||h&&e==null&&!t.inFlow||Fe.isCollection(s)||(Fe.isScalar(s)?s.type===Kl.Scalar.BLOCK_FOLDED||s.type===Kl.Scalar.BLOCK_LITERAL:typeof s=="object"));t=Object.assign({},t,{allNullValues:!1,implicitKey:!d&&(f||!i),indent:a+l});let g=!1,m=!1,p=jl.stringify(s,t,()=>g=!0,()=>m=!0);if(!d&&!t.inFlow&&p.length>1024){if(f)throw new Error("With simple keys, single line scalar must not span more than 1024 characters");d=!0}if(t.inFlow){if(i||e==null)return g&&n&&n(),p===""?"?":d?`? ${p}`:p}else if(i&&!f||e==null&&d)return p=`? ${p}`,h&&!g?p+=ds.lineComment(p,t.indent,c(h)):m&&r&&r(),p;g&&(h=null),d?(h&&(p+=ds.lineComment(p,t.indent,c(h))),p=`? ${p}
${a}:`):(p=`${p}:`,h&&(p+=ds.lineComment(p,t.indent,c(h))));let w,k,E;Fe.isNode(e)?(w=!!e.spaceBefore,k=e.commentBefore,E=e.comment):(w=!1,k=null,E=null,e&&typeof e=="object"&&(e=o.createNode(e))),t.implicitKey=!1,!d&&!h&&Fe.isScalar(e)&&(t.indentAtStart=p.length+1),m=!1,!u&&l.length>=2&&!t.inFlow&&!d&&Fe.isSeq(e)&&!e.flow&&!e.tag&&!e.anchor&&(t.indent=t.indent.substring(2));let v=!1,O=jl.stringify(e,t,()=>v=!0,()=>m=!0),I=" ";if(h||w||k){if(I=w?`
`:"",k){let C=c(k);I+=`
${ds.indentComment(C,t.indent)}`}O===""&&!t.inFlow?I===`
`&&(I=`

`):I+=`
${t.indent}`}else if(!d&&Fe.isCollection(e)){let C=O[0],T=O.indexOf(`
`),M=T!==-1,Z=t.inFlow??e.flow??e.items.length===0;if(M||!Z){let ue=!1;if(M&&(C==="&"||C==="!")){let $=O.indexOf(" ");C==="&"&&$!==-1&&$<T&&O[$+1]==="!"&&($=O.indexOf(" ",$+1)),($===-1||T<$)&&(ue=!0)}ue||(I=`
${t.indent}`)}}else(O===""||O[0]===`
`)&&(I="");return p+=I+O,t.inFlow?v&&n&&n():E&&!v?p+=ds.lineComment(p,t.indent,c(E)):m&&r&&r(),p}Zl.stringifyPair=km});var Ei=S(Ni=>{"use strict";var Gl=require("node:process");function Tm(s,...e){s==="debug"&&console.log(...e)}function bm(s,e){(s==="debug"||s==="warn")&&(typeof Gl.emitWarning=="function"?Gl.emitWarning(e):console.warn(e))}Ni.debug=Tm;Ni.warn=bm});var Vn=S(Rn=>{"use strict";var ms=L(),zl=U(),Pn="<<",$n={identify:s=>s===Pn||typeof s=="symbol"&&s.description===Pn,default:"key",tag:"tag:yaml.org,2002:merge",test:/^<<$/,resolve:()=>Object.assign(new zl.Scalar(Symbol(Pn)),{addToJSMap:Ql}),stringify:()=>Pn},Nm=(s,e)=>($n.identify(e)||ms.isScalar(e)&&(!e.type||e.type===zl.Scalar.PLAIN)&&$n.identify(e.value))&&s?.doc.schema.tags.some(t=>t.tag===$n.tag&&t.default);function Ql(s,e,t){if(t=s&&ms.isAlias(t)?t.resolve(s.doc):t,ms.isSeq(t))for(let n of t.items)vi(s,e,n);else if(Array.isArray(t))for(let n of t)vi(s,e,n);else vi(s,e,t)}function vi(s,e,t){let n=s&&ms.isAlias(t)?t.resolve(s.doc):t;if(!ms.isMap(n))throw new Error("Merge sources must be maps or map aliases");let r=n.toJSON(null,s,Map);for(let[i,o]of r)e instanceof Map?e.has(i)||e.set(i,o):e instanceof Set?e.add(i):Object.prototype.hasOwnProperty.call(e,i)||Object.defineProperty(e,i,{value:o,writable:!0,enumerable:!0,configurable:!0});return e}Rn.addMergeToJSMap=Ql;Rn.isMergeKey=Nm;Rn.merge=$n});var Oi=S(tc=>{"use strict";var Em=Ei(),Xl=Vn(),vm=hs(),ec=L(),Ai=Be();function Am(s,e,{key:t,value:n}){if(ec.isNode(t)&&t.addToJSMap)t.addToJSMap(s,e,n);else if(Xl.isMergeKey(s,t))Xl.addMergeToJSMap(s,e,n);else{let r=Ai.toJS(t,"",s);if(e instanceof Map)e.set(r,Ai.toJS(n,r,s));else if(e instanceof Set)e.add(r);else{let i=Om(t,r,s),o=Ai.toJS(n,i,s);i in e?Object.defineProperty(e,i,{value:o,writable:!0,enumerable:!0,configurable:!0}):e[i]=o}}return e}function Om(s,e,t){if(e===null)return"";if(typeof e!="object")return String(e);if(ec.isNode(s)&&t?.doc){let n=vm.createStringifyContext(t.doc,{});n.anchors=new Set;for(let i of t.anchors.keys())n.anchors.add(i.anchor);n.inFlow=!0,n.inStringifyKey=!0;let r=s.toString(n);if(!t.mapKeyWarned){let i=JSON.stringify(r);i.length>40&&(i=i.substring(0,36)+'..."'),Em.warn(t.doc.options.logLevel,`Keys with collection values will be stringified due to JS Object restrictions: ${i}. Set mapAsMap: true to use object keys.`),t.mapKeyWarned=!0}return r}return JSON.stringify(e)}tc.addPairToJSMap=Am});var He=S(Ii=>{"use strict";var sc=as(),Im=Jl(),Dm=Oi(),Bn=L();function Cm(s,e,t){let n=sc.createNode(s,void 0,t),r=sc.createNode(e,void 0,t);return new Un(n,r)}var Un=class s{constructor(e,t=null){Object.defineProperty(this,Bn.NODE_TYPE,{value:Bn.PAIR}),this.key=e,this.value=t}clone(e){let{key:t,value:n}=this;return Bn.isNode(t)&&(t=t.clone(e)),Bn.isNode(n)&&(n=n.clone(e)),new s(t,n)}toJSON(e,t){let n=t?.mapAsMap?new Map:{};return Dm.addPairToJSMap(t,n,this)}toString(e,t,n){return e?.doc?Im.stringifyPair(this,e,t,n):JSON.stringify(this)}};Ii.Pair=Un;Ii.createPair=Cm});var Di=S(rc=>{"use strict";var lt=L(),nc=hs(),Wn=ls();function Mm(s,e,t){return(e.inFlow??s.flow?Fm:Lm)(s,e,t)}function Lm({comment:s,items:e},t,{blockItemPrefix:n,flowChars:r,itemIndent:i,onChompKeep:o,onComment:a}){let{indent:l,options:{commentString:c}}=t,u=Object.assign({},t,{indent:i,type:null}),f=!1,h=[];for(let g=0;g<e.length;++g){let m=e[g],p=null;if(lt.isNode(m))!f&&m.spaceBefore&&h.push(""),Yn(t,h,m.commentBefore,f),m.comment&&(p=m.comment);else if(lt.isPair(m)){let k=lt.isNode(m.key)?m.key:null;k&&(!f&&k.spaceBefore&&h.push(""),Yn(t,h,k.commentBefore,f))}f=!1;let w=nc.stringify(m,u,()=>p=null,()=>f=!0);p&&(w+=Wn.lineComment(w,i,c(p))),f&&p&&(f=!1),h.push(n+w)}let d;if(h.length===0)d=r.start+r.end;else{d=h[0];for(let g=1;g<h.length;++g){let m=h[g];d+=m?`
${l}${m}`:`
`}}return s?(d+=`
`+Wn.indentComment(c(s),l),a&&a()):f&&o&&o(),d}function Fm({items:s},e,{flowChars:t,itemIndent:n}){let{indent:r,indentStep:i,flowCollectionPadding:o,options:{commentString:a}}=e;n+=i;let l=Object.assign({},e,{indent:n,inFlow:!0,type:null}),c=!1,u=0,f=[];for(let g=0;g<s.length;++g){let m=s[g],p=null;if(lt.isNode(m))m.spaceBefore&&f.push(""),Yn(e,f,m.commentBefore,!1),m.comment&&(p=m.comment);else if(lt.isPair(m)){let k=lt.isNode(m.key)?m.key:null;k&&(k.spaceBefore&&f.push(""),Yn(e,f,k.commentBefore,!1),k.comment&&(c=!0));let E=lt.isNode(m.value)?m.value:null;E?(E.comment&&(p=E.comment),E.commentBefore&&(c=!0)):m.value==null&&k?.comment&&(p=k.comment)}p&&(c=!0);let w=nc.stringify(m,l,()=>p=null);g<s.length-1&&(w+=","),p&&(w+=Wn.lineComment(w,n,a(p))),!c&&(f.length>u||w.includes(`
`))&&(c=!0),f.push(w),u=f.length}let{start:h,end:d}=t;if(f.length===0)return h+d;if(!c){let g=f.reduce((m,p)=>m+p.length+2,2);c=e.options.lineWidth>0&&g>e.options.lineWidth}if(c){let g=h;for(let m of f)g+=m?`
${i}${r}${m}`:`
`;return`${g}
${r}${d}`}else return`${h}${o}${f.join(" ")}${o}${d}`}function Yn({indent:s,options:{commentString:e}},t,n,r){if(n&&r&&(n=n.replace(/^\n+/,"")),n){let i=Wn.indentComment(e(n),s);t.push(i.trimStart())}}rc.stringifyCollection=Mm});var je=S(Mi=>{"use strict";var xm=Di(),_m=Oi(),qm=Mn(),Ke=L(),Hn=He(),Pm=U();function ps(s,e){let t=Ke.isScalar(e)?e.value:e;for(let n of s)if(Ke.isPair(n)&&(n.key===e||n.key===t||Ke.isScalar(n.key)&&n.key.value===t))return n}var Ci=class extends qm.Collection{static get tagName(){return"tag:yaml.org,2002:map"}constructor(e){super(Ke.MAP,e),this.items=[]}static from(e,t,n){let{keepUndefined:r,replacer:i}=n,o=new this(e),a=(l,c)=>{if(typeof i=="function")c=i.call(t,l,c);else if(Array.isArray(i)&&!i.includes(l))return;(c!==void 0||r)&&o.items.push(Hn.createPair(l,c,n))};if(t instanceof Map)for(let[l,c]of t)a(l,c);else if(t&&typeof t=="object")for(let l of Object.keys(t))a(l,t[l]);return typeof e.sortMapEntries=="function"&&o.items.sort(e.sortMapEntries),o}add(e,t){let n;Ke.isPair(e)?n=e:!e||typeof e!="object"||!("key"in e)?n=new Hn.Pair(e,e?.value):n=new Hn.Pair(e.key,e.value);let r=ps(this.items,n.key),i=this.schema?.sortMapEntries;if(r){if(!t)throw new Error(`Key ${n.key} already set`);Ke.isScalar(r.value)&&Pm.isScalarValue(n.value)?r.value.value=n.value:r.value=n.value}else if(i){let o=this.items.findIndex(a=>i(n,a)<0);o===-1?this.items.push(n):this.items.splice(o,0,n)}else this.items.push(n)}delete(e){let t=ps(this.items,e);return t?this.items.splice(this.items.indexOf(t),1).length>0:!1}get(e,t){let r=ps(this.items,e)?.value;return(!t&&Ke.isScalar(r)?r.value:r)??void 0}has(e){return!!ps(this.items,e)}set(e,t){this.add(new Hn.Pair(e,t),!0)}toJSON(e,t,n){let r=n?new n:t?.mapAsMap?new Map:{};t?.onCreate&&t.onCreate(r);for(let i of this.items)_m.addPairToJSMap(t,r,i);return r}toString(e,t,n){if(!e)return JSON.stringify(this);for(let r of this.items)if(!Ke.isPair(r))throw new Error(`Map items must all be pairs; found ${JSON.stringify(r)} instead`);return!e.allNullValues&&this.hasAllNullValues(!1)&&(e=Object.assign({},e,{allNullValues:!0})),xm.stringifyCollection(this,e,{blockItemPrefix:"",flowChars:{start:"{",end:"}"},itemIndent:e.indent||"",onChompKeep:n,onComment:t})}};Mi.YAMLMap=Ci;Mi.findPair=ps});var Dt=S(oc=>{"use strict";var $m=L(),ic=je(),Rm={collection:"map",default:!0,nodeClass:ic.YAMLMap,tag:"tag:yaml.org,2002:map",resolve(s,e){return $m.isMap(s)||e("Expected a mapping for this tag"),s},createNode:(s,e,t)=>ic.YAMLMap.from(s,e,t)};oc.map=Rm});var Ze=S(ac=>{"use strict";var Vm=as(),Bm=Di(),Um=Mn(),jn=L(),Wm=U(),Ym=Be(),Li=class extends Um.Collection{static get tagName(){return"tag:yaml.org,2002:seq"}constructor(e){super(jn.SEQ,e),this.items=[]}add(e){this.items.push(e)}delete(e){let t=Kn(e);return typeof t!="number"?!1:this.items.splice(t,1).length>0}get(e,t){let n=Kn(e);if(typeof n!="number")return;let r=this.items[n];return!t&&jn.isScalar(r)?r.value:r}has(e){let t=Kn(e);return typeof t=="number"&&t<this.items.length}set(e,t){let n=Kn(e);if(typeof n!="number")throw new Error(`Expected a valid index, not ${e}.`);let r=this.items[n];jn.isScalar(r)&&Wm.isScalarValue(t)?r.value=t:this.items[n]=t}toJSON(e,t){let n=[];t?.onCreate&&t.onCreate(n);let r=0;for(let i of this.items)n.push(Ym.toJS(i,String(r++),t));return n}toString(e,t,n){return e?Bm.stringifyCollection(this,e,{blockItemPrefix:"- ",flowChars:{start:"[",end:"]"},itemIndent:(e.indent||"")+"  ",onChompKeep:n,onComment:t}):JSON.stringify(this)}static from(e,t,n){let{replacer:r}=n,i=new this(e);if(t&&Symbol.iterator in Object(t)){let o=0;for(let a of t){if(typeof r=="function"){let l=t instanceof Set?a:String(o++);a=r.call(t,l,a)}i.items.push(Vm.createNode(a,void 0,n))}}return i}};function Kn(s){let e=jn.isScalar(s)?s.value:s;return e&&typeof e=="string"&&(e=Number(e)),typeof e=="number"&&Number.isInteger(e)&&e>=0?e:null}ac.YAMLSeq=Li});var Ct=S(cc=>{"use strict";var Hm=L(),lc=Ze(),Km={collection:"seq",default:!0,nodeClass:lc.YAMLSeq,tag:"tag:yaml.org,2002:seq",resolve(s,e){return Hm.isSeq(s)||e("Expected a sequence for this tag"),s},createNode:(s,e,t)=>lc.YAMLSeq.from(s,e,t)};cc.seq=Km});var gs=S(uc=>{"use strict";var jm=fs(),Zm={identify:s=>typeof s=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:s=>s,stringify(s,e,t,n){return e=Object.assign({actualString:!0},e),jm.stringifyString(s,e,t,n)}};uc.string=Zm});var Zn=S(dc=>{"use strict";var fc=U(),hc={identify:s=>s==null,createNode:()=>new fc.Scalar(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:()=>new fc.Scalar(null),stringify:({source:s},e)=>typeof s=="string"&&hc.test.test(s)?s:e.options.nullStr};dc.nullTag=hc});var Fi=S(pc=>{"use strict";var Jm=U(),mc={identify:s=>typeof s=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,resolve:s=>new Jm.Scalar(s[0]==="t"||s[0]==="T"),stringify({source:s,value:e},t){if(s&&mc.test.test(s)){let n=s[0]==="t"||s[0]==="T";if(e===n)return s}return e?t.options.trueStr:t.options.falseStr}};pc.boolTag=mc});var Mt=S(gc=>{"use strict";function Gm({format:s,minFractionDigits:e,tag:t,value:n}){if(typeof n=="bigint")return String(n);let r=typeof n=="number"?n:Number(n);if(!isFinite(r))return isNaN(r)?".nan":r<0?"-.inf":".inf";let i=JSON.stringify(n);if(!s&&e&&(!t||t==="tag:yaml.org,2002:float")&&/^\d/.test(i)){let o=i.indexOf(".");o<0&&(o=i.length,i+=".");let a=e-(i.length-o-1);for(;a-- >0;)i+="0"}return i}gc.stringifyNumber=Gm});var _i=S(Jn=>{"use strict";var zm=U(),xi=Mt(),Qm={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:s=>s.slice(-3).toLowerCase()==="nan"?NaN:s[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:xi.stringifyNumber},Xm={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:\.[0-9]+|[0-9]+(?:\.[0-9]*)?)[eE][-+]?[0-9]+$/,resolve:s=>parseFloat(s),stringify(s){let e=Number(s.value);return isFinite(e)?e.toExponential():xi.stringifyNumber(s)}},ep={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:\.[0-9]+|[0-9]+\.[0-9]*)$/,resolve(s){let e=new zm.Scalar(parseFloat(s)),t=s.indexOf(".");return t!==-1&&s[s.length-1]==="0"&&(e.minFractionDigits=s.length-t-1),e},stringify:xi.stringifyNumber};Jn.float=ep;Jn.floatExp=Xm;Jn.floatNaN=Qm});var Pi=S(zn=>{"use strict";var yc=Mt(),Gn=s=>typeof s=="bigint"||Number.isInteger(s),qi=(s,e,t,{intAsBigInt:n})=>n?BigInt(s):parseInt(s.substring(e),t);function Sc(s,e,t){let{value:n}=s;return Gn(n)&&n>=0?t+n.toString(e):yc.stringifyNumber(s)}var tp={identify:s=>Gn(s)&&s>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^0o[0-7]+$/,resolve:(s,e,t)=>qi(s,2,8,t),stringify:s=>Sc(s,8,"0o")},sp={identify:Gn,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9]+$/,resolve:(s,e,t)=>qi(s,0,10,t),stringify:yc.stringifyNumber},np={identify:s=>Gn(s)&&s>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x[0-9a-fA-F]+$/,resolve:(s,e,t)=>qi(s,2,16,t),stringify:s=>Sc(s,16,"0x")};zn.int=sp;zn.intHex=np;zn.intOct=tp});var kc=S(wc=>{"use strict";var rp=Dt(),ip=Zn(),op=Ct(),ap=gs(),lp=Fi(),$i=_i(),Ri=Pi(),cp=[rp.map,op.seq,ap.string,ip.nullTag,lp.boolTag,Ri.intOct,Ri.int,Ri.intHex,$i.floatNaN,$i.floatExp,$i.float];wc.schema=cp});var Nc=S(bc=>{"use strict";var up=U(),fp=Dt(),hp=Ct();function Tc(s){return typeof s=="bigint"||Number.isInteger(s)}var Qn=({value:s})=>JSON.stringify(s),dp=[{identify:s=>typeof s=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:s=>s,stringify:Qn},{identify:s=>s==null,createNode:()=>new up.Scalar(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^null$/,resolve:()=>null,stringify:Qn},{identify:s=>typeof s=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^true$|^false$/,resolve:s=>s==="true",stringify:Qn},{identify:Tc,default:!0,tag:"tag:yaml.org,2002:int",test:/^-?(?:0|[1-9][0-9]*)$/,resolve:(s,e,{intAsBigInt:t})=>t?BigInt(s):parseInt(s,10),stringify:({value:s})=>Tc(s)?s.toString():JSON.stringify(s)},{identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^-?(?:0|[1-9][0-9]*)(?:\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,resolve:s=>parseFloat(s),stringify:Qn}],mp={default:!0,tag:"",test:/^/,resolve(s,e){return e(`Unresolved plain scalar ${JSON.stringify(s)}`),s}},pp=[fp.map,hp.seq].concat(dp,mp);bc.schema=pp});var Bi=S(Ec=>{"use strict";var ys=require("node:buffer"),Vi=U(),gp=fs(),yp={identify:s=>s instanceof Uint8Array,default:!1,tag:"tag:yaml.org,2002:binary",resolve(s,e){if(typeof ys.Buffer=="function")return ys.Buffer.from(s,"base64");if(typeof atob=="function"){let t=atob(s.replace(/[\n\r]/g,"")),n=new Uint8Array(t.length);for(let r=0;r<t.length;++r)n[r]=t.charCodeAt(r);return n}else return e("This environment does not support reading binary tags; either Buffer or atob is required"),s},stringify({comment:s,type:e,value:t},n,r,i){let o=t,a;if(typeof ys.Buffer=="function")a=o instanceof ys.Buffer?o.toString("base64"):ys.Buffer.from(o.buffer).toString("base64");else if(typeof btoa=="function"){let l="";for(let c=0;c<o.length;++c)l+=String.fromCharCode(o[c]);a=btoa(l)}else throw new Error("This environment does not support writing binary tags; either Buffer or btoa is required");if(e||(e=Vi.Scalar.BLOCK_LITERAL),e!==Vi.Scalar.QUOTE_DOUBLE){let l=Math.max(n.options.lineWidth-n.indent.length,n.options.minContentWidth),c=Math.ceil(a.length/l),u=new Array(c);for(let f=0,h=0;f<c;++f,h+=l)u[f]=a.substr(h,l);a=u.join(e===Vi.Scalar.BLOCK_LITERAL?`
`:" ")}return gp.stringifyString({comment:s,type:e,value:a},n,r,i)}};Ec.binary=yp});var tr=S(er=>{"use strict";var Xn=L(),Ui=He(),Sp=U(),wp=Ze();function vc(s,e){if(Xn.isSeq(s))for(let t=0;t<s.items.length;++t){let n=s.items[t];if(!Xn.isPair(n)){if(Xn.isMap(n)){n.items.length>1&&e("Each pair must have its own sequence indicator");let r=n.items[0]||new Ui.Pair(new Sp.Scalar(null));if(n.commentBefore&&(r.key.commentBefore=r.key.commentBefore?`${n.commentBefore}
${r.key.commentBefore}`:n.commentBefore),n.comment){let i=r.value??r.key;i.comment=i.comment?`${n.comment}
${i.comment}`:n.comment}n=r}s.items[t]=Xn.isPair(n)?n:new Ui.Pair(n)}}else e("Expected a sequence for this tag");return s}function Ac(s,e,t){let{replacer:n}=t,r=new wp.YAMLSeq(s);r.tag="tag:yaml.org,2002:pairs";let i=0;if(e&&Symbol.iterator in Object(e))for(let o of e){typeof n=="function"&&(o=n.call(e,String(i++),o));let a,l;if(Array.isArray(o))if(o.length===2)a=o[0],l=o[1];else throw new TypeError(`Expected [key, value] tuple: ${o}`);else if(o&&o instanceof Object){let c=Object.keys(o);if(c.length===1)a=c[0],l=o[a];else throw new TypeError(`Expected tuple with one key, not ${c.length} keys`)}else a=o;r.items.push(Ui.createPair(a,l,t))}return r}var kp={collection:"seq",default:!1,tag:"tag:yaml.org,2002:pairs",resolve:vc,createNode:Ac};er.createPairs=Ac;er.pairs=kp;er.resolvePairs=vc});var Hi=S(Yi=>{"use strict";var Oc=L(),Wi=Be(),Ss=je(),Tp=Ze(),Ic=tr(),ct=class s extends Tp.YAMLSeq{constructor(){super(),this.add=Ss.YAMLMap.prototype.add.bind(this),this.delete=Ss.YAMLMap.prototype.delete.bind(this),this.get=Ss.YAMLMap.prototype.get.bind(this),this.has=Ss.YAMLMap.prototype.has.bind(this),this.set=Ss.YAMLMap.prototype.set.bind(this),this.tag=s.tag}toJSON(e,t){if(!t)return super.toJSON(e);let n=new Map;t?.onCreate&&t.onCreate(n);for(let r of this.items){let i,o;if(Oc.isPair(r)?(i=Wi.toJS(r.key,"",t),o=Wi.toJS(r.value,i,t)):i=Wi.toJS(r,"",t),n.has(i))throw new Error("Ordered maps must not include duplicate keys");n.set(i,o)}return n}static from(e,t,n){let r=Ic.createPairs(e,t,n),i=new this;return i.items=r.items,i}};ct.tag="tag:yaml.org,2002:omap";var bp={collection:"seq",identify:s=>s instanceof Map,nodeClass:ct,default:!1,tag:"tag:yaml.org,2002:omap",resolve(s,e){let t=Ic.resolvePairs(s,e),n=[];for(let{key:r}of t.items)Oc.isScalar(r)&&(n.includes(r.value)?e(`Ordered maps must not include duplicate keys: ${r.value}`):n.push(r.value));return Object.assign(new ct,t)},createNode:(s,e,t)=>ct.from(s,e,t)};Yi.YAMLOMap=ct;Yi.omap=bp});var Fc=S(Ki=>{"use strict";var Dc=U();function Cc({value:s,source:e},t){return e&&(s?Mc:Lc).test.test(e)?e:s?t.options.trueStr:t.options.falseStr}var Mc={identify:s=>s===!0,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,resolve:()=>new Dc.Scalar(!0),stringify:Cc},Lc={identify:s=>s===!1,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,resolve:()=>new Dc.Scalar(!1),stringify:Cc};Ki.falseTag=Lc;Ki.trueTag=Mc});var xc=S(sr=>{"use strict";var Np=U(),ji=Mt(),Ep={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:s=>s.slice(-3).toLowerCase()==="nan"?NaN:s[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:ji.stringifyNumber},vp={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:[0-9][0-9_]*)?(?:\.[0-9_]*)?[eE][-+]?[0-9]+$/,resolve:s=>parseFloat(s.replace(/_/g,"")),stringify(s){let e=Number(s.value);return isFinite(e)?e.toExponential():ji.stringifyNumber(s)}},Ap={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:[0-9][0-9_]*)?\.[0-9_]*$/,resolve(s){let e=new Np.Scalar(parseFloat(s.replace(/_/g,""))),t=s.indexOf(".");if(t!==-1){let n=s.substring(t+1).replace(/_/g,"");n[n.length-1]==="0"&&(e.minFractionDigits=n.length)}return e},stringify:ji.stringifyNumber};sr.float=Ap;sr.floatExp=vp;sr.floatNaN=Ep});var qc=S(ks=>{"use strict";var _c=Mt(),ws=s=>typeof s=="bigint"||Number.isInteger(s);function nr(s,e,t,{intAsBigInt:n}){let r=s[0];if((r==="-"||r==="+")&&(e+=1),s=s.substring(e).replace(/_/g,""),n){switch(t){case 2:s=`0b${s}`;break;case 8:s=`0o${s}`;break;case 16:s=`0x${s}`;break}let o=BigInt(s);return r==="-"?BigInt(-1)*o:o}let i=parseInt(s,t);return r==="-"?-1*i:i}function Zi(s,e,t){let{value:n}=s;if(ws(n)){let r=n.toString(e);return n<0?"-"+t+r.substr(1):t+r}return _c.stringifyNumber(s)}var Op={identify:ws,default:!0,tag:"tag:yaml.org,2002:int",format:"BIN",test:/^[-+]?0b[0-1_]+$/,resolve:(s,e,t)=>nr(s,2,2,t),stringify:s=>Zi(s,2,"0b")},Ip={identify:ws,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^[-+]?0[0-7_]+$/,resolve:(s,e,t)=>nr(s,1,8,t),stringify:s=>Zi(s,8,"0")},Dp={identify:ws,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9][0-9_]*$/,resolve:(s,e,t)=>nr(s,0,10,t),stringify:_c.stringifyNumber},Cp={identify:ws,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^[-+]?0x[0-9a-fA-F_]+$/,resolve:(s,e,t)=>nr(s,2,16,t),stringify:s=>Zi(s,16,"0x")};ks.int=Dp;ks.intBin=Op;ks.intHex=Cp;ks.intOct=Ip});var Gi=S(Ji=>{"use strict";var or=L(),rr=He(),ir=je(),ut=class s extends ir.YAMLMap{constructor(e){super(e),this.tag=s.tag}add(e){let t;or.isPair(e)?t=e:e&&typeof e=="object"&&"key"in e&&"value"in e&&e.value===null?t=new rr.Pair(e.key,null):t=new rr.Pair(e,null),ir.findPair(this.items,t.key)||this.items.push(t)}get(e,t){let n=ir.findPair(this.items,e);return!t&&or.isPair(n)?or.isScalar(n.key)?n.key.value:n.key:n}set(e,t){if(typeof t!="boolean")throw new Error(`Expected boolean value for set(key, value) in a YAML set, not ${typeof t}`);let n=ir.findPair(this.items,e);n&&!t?this.items.splice(this.items.indexOf(n),1):!n&&t&&this.items.push(new rr.Pair(e))}toJSON(e,t){return super.toJSON(e,t,Set)}toString(e,t,n){if(!e)return JSON.stringify(this);if(this.hasAllNullValues(!0))return super.toString(Object.assign({},e,{allNullValues:!0}),t,n);throw new Error("Set items must all have null values")}static from(e,t,n){let{replacer:r}=n,i=new this(e);if(t&&Symbol.iterator in Object(t))for(let o of t)typeof r=="function"&&(o=r.call(t,o,o)),i.items.push(rr.createPair(o,null,n));return i}};ut.tag="tag:yaml.org,2002:set";var Mp={collection:"map",identify:s=>s instanceof Set,nodeClass:ut,default:!1,tag:"tag:yaml.org,2002:set",createNode:(s,e,t)=>ut.from(s,e,t),resolve(s,e){if(or.isMap(s)){if(s.hasAllNullValues(!0))return Object.assign(new ut,s);e("Set items must all have null values")}else e("Expected a mapping for this tag");return s}};Ji.YAMLSet=ut;Ji.set=Mp});var Qi=S(ar=>{"use strict";var Lp=Mt();function zi(s,e){let t=s[0],n=t==="-"||t==="+"?s.substring(1):s,r=o=>e?BigInt(o):Number(o),i=n.replace(/_/g,"").split(":").reduce((o,a)=>o*r(60)+r(a),r(0));return t==="-"?r(-1)*i:i}function Pc(s){let{value:e}=s,t=o=>o;if(typeof e=="bigint")t=o=>BigInt(o);else if(isNaN(e)||!isFinite(e))return Lp.stringifyNumber(s);let n="";e<0&&(n="-",e*=t(-1));let r=t(60),i=[e%r];return e<60?i.unshift(0):(e=(e-i[0])/r,i.unshift(e%r),e>=60&&(e=(e-i[0])/r,i.unshift(e))),n+i.map(o=>String(o).padStart(2,"0")).join(":").replace(/000000\d*$/,"")}var Fp={identify:s=>typeof s=="bigint"||Number.isInteger(s),default:!0,tag:"tag:yaml.org,2002:int",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,resolve:(s,e,{intAsBigInt:t})=>zi(s,t),stringify:Pc},xp={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\.[0-9_]*$/,resolve:s=>zi(s,!1),stringify:Pc},$c={identify:s=>s instanceof Date,default:!0,tag:"tag:yaml.org,2002:timestamp",test:RegExp("^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})(?:(?:t|T|[ \\t]+)([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\.[0-9]+)?)(?:[ \\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?)?$"),resolve(s){let e=s.match($c.test);if(!e)throw new Error("!!timestamp expects a date, starting with yyyy-mm-dd");let[,t,n,r,i,o,a]=e.map(Number),l=e[7]?Number((e[7]+"00").substr(1,3)):0,c=Date.UTC(t,n-1,r,i||0,o||0,a||0,l),u=e[8];if(u&&u!=="Z"){let f=zi(u,!1);Math.abs(f)<30&&(f*=60),c-=6e4*f}return new Date(c)},stringify:({value:s})=>s.toISOString().replace(/(T00:00:00)?\.000Z$/,"")};ar.floatTime=xp;ar.intTime=Fp;ar.timestamp=$c});var Bc=S(Vc=>{"use strict";var _p=Dt(),qp=Zn(),Pp=Ct(),$p=gs(),Rp=Bi(),Rc=Fc(),Xi=xc(),lr=qc(),Vp=Vn(),Bp=Hi(),Up=tr(),Wp=Gi(),eo=Qi(),Yp=[_p.map,Pp.seq,$p.string,qp.nullTag,Rc.trueTag,Rc.falseTag,lr.intBin,lr.intOct,lr.int,lr.intHex,Xi.floatNaN,Xi.floatExp,Xi.float,Rp.binary,Vp.merge,Bp.omap,Up.pairs,Wp.set,eo.intTime,eo.floatTime,eo.timestamp];Vc.schema=Yp});var zc=S(no=>{"use strict";var Hc=Dt(),Hp=Zn(),Kc=Ct(),Kp=gs(),jp=Fi(),to=_i(),so=Pi(),Zp=kc(),Jp=Nc(),jc=Bi(),Ts=Vn(),Zc=Hi(),Jc=tr(),Uc=Bc(),Gc=Gi(),cr=Qi(),Wc=new Map([["core",Zp.schema],["failsafe",[Hc.map,Kc.seq,Kp.string]],["json",Jp.schema],["yaml11",Uc.schema],["yaml-1.1",Uc.schema]]),Yc={binary:jc.binary,bool:jp.boolTag,float:to.float,floatExp:to.floatExp,floatNaN:to.floatNaN,floatTime:cr.floatTime,int:so.int,intHex:so.intHex,intOct:so.intOct,intTime:cr.intTime,map:Hc.map,merge:Ts.merge,null:Hp.nullTag,omap:Zc.omap,pairs:Jc.pairs,seq:Kc.seq,set:Gc.set,timestamp:cr.timestamp},Gp={"tag:yaml.org,2002:binary":jc.binary,"tag:yaml.org,2002:merge":Ts.merge,"tag:yaml.org,2002:omap":Zc.omap,"tag:yaml.org,2002:pairs":Jc.pairs,"tag:yaml.org,2002:set":Gc.set,"tag:yaml.org,2002:timestamp":cr.timestamp};function zp(s,e,t){let n=Wc.get(e);if(n&&!s)return t&&!n.includes(Ts.merge)?n.concat(Ts.merge):n.slice();let r=n;if(!r)if(Array.isArray(s))r=[];else{let i=Array.from(Wc.keys()).filter(o=>o!=="yaml11").map(o=>JSON.stringify(o)).join(", ");throw new Error(`Unknown schema "${e}"; use one of ${i} or define customTags array`)}if(Array.isArray(s))for(let i of s)r=r.concat(i);else typeof s=="function"&&(r=s(r.slice()));return t&&(r=r.concat(Ts.merge)),r.reduce((i,o)=>{let a=typeof o=="string"?Yc[o]:o;if(!a){let l=JSON.stringify(o),c=Object.keys(Yc).map(u=>JSON.stringify(u)).join(", ");throw new Error(`Unknown custom tag ${l}; use one of ${c}`)}return i.includes(a)||i.push(a),i},[])}no.coreKnownTags=Gp;no.getTags=zp});var oo=S(Qc=>{"use strict";var ro=L(),Qp=Dt(),Xp=Ct(),eg=gs(),ur=zc(),tg=(s,e)=>s.key<e.key?-1:s.key>e.key?1:0,io=class s{constructor({compat:e,customTags:t,merge:n,resolveKnownTags:r,schema:i,sortMapEntries:o,toStringDefaults:a}){this.compat=Array.isArray(e)?ur.getTags(e,"compat"):e?ur.getTags(null,e):null,this.name=typeof i=="string"&&i||"core",this.knownTags=r?ur.coreKnownTags:{},this.tags=ur.getTags(t,this.name,n),this.toStringOptions=a??null,Object.defineProperty(this,ro.MAP,{value:Qp.map}),Object.defineProperty(this,ro.SCALAR,{value:eg.string}),Object.defineProperty(this,ro.SEQ,{value:Xp.seq}),this.sortMapEntries=typeof o=="function"?o:o===!0?tg:null}clone(){let e=Object.create(s.prototype,Object.getOwnPropertyDescriptors(this));return e.tags=this.tags.slice(),e}};Qc.Schema=io});var eu=S(Xc=>{"use strict";var sg=L(),ao=hs(),bs=ls();function ng(s,e){let t=[],n=e.directives===!0;if(e.directives!==!1&&s.directives){let l=s.directives.toString(s);l?(t.push(l),n=!0):s.directives.docStart&&(n=!0)}n&&t.push("---");let r=ao.createStringifyContext(s,e),{commentString:i}=r.options;if(s.commentBefore){t.length!==1&&t.unshift("");let l=i(s.commentBefore);t.unshift(bs.indentComment(l,""))}let o=!1,a=null;if(s.contents){if(sg.isNode(s.contents)){if(s.contents.spaceBefore&&n&&t.push(""),s.contents.commentBefore){let u=i(s.contents.commentBefore);t.push(bs.indentComment(u,""))}r.forceBlockIndent=!!s.comment,a=s.contents.comment}let l=a?void 0:()=>o=!0,c=ao.stringify(s.contents,r,()=>a=null,l);a&&(c+=bs.lineComment(c,"",i(a))),(c[0]==="|"||c[0]===">")&&t[t.length-1]==="---"?t[t.length-1]=`--- ${c}`:t.push(c)}else t.push(ao.stringify(s.contents,r));if(s.directives?.docEnd)if(s.comment){let l=i(s.comment);l.includes(`
`)?(t.push("..."),t.push(bs.indentComment(l,""))):t.push(`... ${l}`)}else t.push("...");else{let l=s.comment;l&&o&&(l=l.replace(/^\n+/,"")),l&&((!o||a)&&t[t.length-1]!==""&&t.push(""),t.push(bs.indentComment(i(l),"")))}return t.join(`
`)+`
`}Xc.stringifyDocument=ng});var Ns=S(tu=>{"use strict";var rg=os(),Lt=Mn(),fe=L(),ig=He(),og=Be(),ag=oo(),lg=eu(),lo=An(),cg=hi(),ug=as(),co=fi(),uo=class s{constructor(e,t,n){this.commentBefore=null,this.comment=null,this.errors=[],this.warnings=[],Object.defineProperty(this,fe.NODE_TYPE,{value:fe.DOC});let r=null;typeof t=="function"||Array.isArray(t)?r=t:n===void 0&&t&&(n=t,t=void 0);let i=Object.assign({intAsBigInt:!1,keepSourceTokens:!1,logLevel:"warn",prettyErrors:!0,strict:!0,stringKeys:!1,uniqueKeys:!0,version:"1.2"},n);this.options=i;let{version:o}=i;n?._directives?(this.directives=n._directives.atDocument(),this.directives.yaml.explicit&&(o=this.directives.yaml.version)):this.directives=new co.Directives({version:o}),this.setSchema(o,n),this.contents=e===void 0?null:this.createNode(e,r,n)}clone(){let e=Object.create(s.prototype,{[fe.NODE_TYPE]:{value:fe.DOC}});return e.commentBefore=this.commentBefore,e.comment=this.comment,e.errors=this.errors.slice(),e.warnings=this.warnings.slice(),e.options=Object.assign({},this.options),this.directives&&(e.directives=this.directives.clone()),e.schema=this.schema.clone(),e.contents=fe.isNode(this.contents)?this.contents.clone(e.schema):this.contents,this.range&&(e.range=this.range.slice()),e}add(e){Ft(this.contents)&&this.contents.add(e)}addIn(e,t){Ft(this.contents)&&this.contents.addIn(e,t)}createAlias(e,t){if(!e.anchor){let n=lo.anchorNames(this);e.anchor=!t||n.has(t)?lo.findNewAnchor(t||"a",n):t}return new rg.Alias(e.anchor)}createNode(e,t,n){let r;if(typeof t=="function")e=t.call({"":e},"",e),r=t;else if(Array.isArray(t)){let p=k=>typeof k=="number"||k instanceof String||k instanceof Number,w=t.filter(p).map(String);w.length>0&&(t=t.concat(w)),r=t}else n===void 0&&t&&(n=t,t=void 0);let{aliasDuplicateObjects:i,anchorPrefix:o,flow:a,keepUndefined:l,onTagObj:c,tag:u}=n??{},{onAnchor:f,setAnchors:h,sourceObjects:d}=lo.createNodeAnchors(this,o||"a"),g={aliasDuplicateObjects:i??!0,keepUndefined:l??!1,onAnchor:f,onTagObj:c,replacer:r,schema:this.schema,sourceObjects:d},m=ug.createNode(e,u,g);return a&&fe.isCollection(m)&&(m.flow=!0),h(),m}createPair(e,t,n={}){let r=this.createNode(e,null,n),i=this.createNode(t,null,n);return new ig.Pair(r,i)}delete(e){return Ft(this.contents)?this.contents.delete(e):!1}deleteIn(e){return Lt.isEmptyPath(e)?this.contents==null?!1:(this.contents=null,!0):Ft(this.contents)?this.contents.deleteIn(e):!1}get(e,t){return fe.isCollection(this.contents)?this.contents.get(e,t):void 0}getIn(e,t){return Lt.isEmptyPath(e)?!t&&fe.isScalar(this.contents)?this.contents.value:this.contents:fe.isCollection(this.contents)?this.contents.getIn(e,t):void 0}has(e){return fe.isCollection(this.contents)?this.contents.has(e):!1}hasIn(e){return Lt.isEmptyPath(e)?this.contents!==void 0:fe.isCollection(this.contents)?this.contents.hasIn(e):!1}set(e,t){this.contents==null?this.contents=Lt.collectionFromPath(this.schema,[e],t):Ft(this.contents)&&this.contents.set(e,t)}setIn(e,t){Lt.isEmptyPath(e)?this.contents=t:this.contents==null?this.contents=Lt.collectionFromPath(this.schema,Array.from(e),t):Ft(this.contents)&&this.contents.setIn(e,t)}setSchema(e,t={}){typeof e=="number"&&(e=String(e));let n;switch(e){case"1.1":this.directives?this.directives.yaml.version="1.1":this.directives=new co.Directives({version:"1.1"}),n={resolveKnownTags:!1,schema:"yaml-1.1"};break;case"1.2":case"next":this.directives?this.directives.yaml.version=e:this.directives=new co.Directives({version:e}),n={resolveKnownTags:!0,schema:"core"};break;case null:this.directives&&delete this.directives,n=null;break;default:{let r=JSON.stringify(e);throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${r}`)}}if(t.schema instanceof Object)this.schema=t.schema;else if(n)this.schema=new ag.Schema(Object.assign(n,t));else throw new Error("With a null YAML version, the { schema: Schema } option is required")}toJS({json:e,jsonArg:t,mapAsMap:n,maxAliasCount:r,onAnchor:i,reviver:o}={}){let a={anchors:new Map,doc:this,keep:!e,mapAsMap:n===!0,mapKeyWarned:!1,maxAliasCount:typeof r=="number"?r:100},l=og.toJS(this.contents,t??"",a);if(typeof i=="function")for(let{count:c,res:u}of a.anchors.values())i(u,c);return typeof o=="function"?cg.applyReviver(o,{"":l},"",l):l}toJSON(e,t){return this.toJS({json:!0,jsonArg:e,mapAsMap:!1,onAnchor:t})}toString(e={}){if(this.errors.length>0)throw new Error("Document with errors cannot be stringified");if("indent"in e&&(!Number.isInteger(e.indent)||Number(e.indent)<=0)){let t=JSON.stringify(e.indent);throw new Error(`"indent" option must be a positive integer, not ${t}`)}return lg.stringifyDocument(this,e)}};function Ft(s){if(fe.isCollection(s))return!0;throw new Error("Expected a YAML collection as document contents")}tu.Document=uo});var As=S(vs=>{"use strict";var Es=class extends Error{constructor(e,t,n,r){super(),this.name=e,this.code=n,this.message=r,this.pos=t}},fo=class extends Es{constructor(e,t,n){super("YAMLParseError",e,t,n)}},ho=class extends Es{constructor(e,t,n){super("YAMLWarning",e,t,n)}},fg=(s,e)=>t=>{if(t.pos[0]===-1)return;t.linePos=t.pos.map(a=>e.linePos(a));let{line:n,col:r}=t.linePos[0];t.message+=` at line ${n}, column ${r}`;let i=r-1,o=s.substring(e.lineStarts[n-1],e.lineStarts[n]).replace(/[\n\r]+$/,"");if(i>=60&&o.length>80){let a=Math.min(i-39,o.length-79);o="\u2026"+o.substring(a),i-=a-1}if(o.length>80&&(o=o.substring(0,79)+"\u2026"),n>1&&/^ *$/.test(o.substring(0,i))){let a=s.substring(e.lineStarts[n-2],e.lineStarts[n-1]);a.length>80&&(a=a.substring(0,79)+`\u2026
`),o=a+o}if(/[^ ]/.test(o)){let a=1,l=t.linePos[1];l&&l.line===n&&l.col>r&&(a=Math.max(1,Math.min(l.col-r,80-i)));let c=" ".repeat(i)+"^".repeat(a);t.message+=`:

${o}
${c}
`}};vs.YAMLError=Es;vs.YAMLParseError=fo;vs.YAMLWarning=ho;vs.prettifyError=fg});var Os=S(su=>{"use strict";function hg(s,{flow:e,indicator:t,next:n,offset:r,onError:i,parentIndent:o,startOnNewline:a}){let l=!1,c=a,u=a,f="",h="",d=!1,g=!1,m=null,p=null,w=null,k=null,E=null,v=null,O=null;for(let T of s)switch(g&&(T.type!=="space"&&T.type!=="newline"&&T.type!=="comma"&&i(T.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),g=!1),m&&(c&&T.type!=="comment"&&T.type!=="newline"&&i(m,"TAB_AS_INDENT","Tabs are not allowed as indentation"),m=null),T.type){case"space":!e&&(t!=="doc-start"||n?.type!=="flow-collection")&&T.source.includes("	")&&(m=T),u=!0;break;case"comment":{u||i(T,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");let M=T.source.substring(1)||" ";f?f+=h+M:f=M,h="",c=!1;break}case"newline":c?f?f+=T.source:(!v||t!=="seq-item-ind")&&(l=!0):h+=T.source,c=!0,d=!0,(p||w)&&(k=T),u=!0;break;case"anchor":p&&i(T,"MULTIPLE_ANCHORS","A node can have at most one anchor"),T.source.endsWith(":")&&i(T.offset+T.source.length-1,"BAD_ALIAS","Anchor ending in : is ambiguous",!0),p=T,O===null&&(O=T.offset),c=!1,u=!1,g=!0;break;case"tag":{w&&i(T,"MULTIPLE_TAGS","A node can have at most one tag"),w=T,O===null&&(O=T.offset),c=!1,u=!1,g=!0;break}case t:(p||w)&&i(T,"BAD_PROP_ORDER",`Anchors and tags must be after the ${T.source} indicator`),v&&i(T,"UNEXPECTED_TOKEN",`Unexpected ${T.source} in ${e??"collection"}`),v=T,c=t==="seq-item-ind"||t==="explicit-key-ind",u=!1;break;case"comma":if(e){E&&i(T,"UNEXPECTED_TOKEN",`Unexpected , in ${e}`),E=T,c=!1,u=!1;break}default:i(T,"UNEXPECTED_TOKEN",`Unexpected ${T.type} token`),c=!1,u=!1}let I=s[s.length-1],C=I?I.offset+I.source.length:r;return g&&n&&n.type!=="space"&&n.type!=="newline"&&n.type!=="comma"&&(n.type!=="scalar"||n.source!=="")&&i(n.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),m&&(c&&m.indent<=o||n?.type==="block-map"||n?.type==="block-seq")&&i(m,"TAB_AS_INDENT","Tabs are not allowed as indentation"),{comma:E,found:v,spaceBefore:l,comment:f,hasNewline:d,anchor:p,tag:w,newlineAfterProp:k,end:C,start:O??C}}su.resolveProps=hg});var fr=S(nu=>{"use strict";function mo(s){if(!s)return null;switch(s.type){case"alias":case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":if(s.source.includes(`
`))return!0;if(s.end){for(let e of s.end)if(e.type==="newline")return!0}return!1;case"flow-collection":for(let e of s.items){for(let t of e.start)if(t.type==="newline")return!0;if(e.sep){for(let t of e.sep)if(t.type==="newline")return!0}if(mo(e.key)||mo(e.value))return!0}return!1;default:return!0}}nu.containsNewline=mo});var po=S(ru=>{"use strict";var dg=fr();function mg(s,e,t){if(e?.type==="flow-collection"){let n=e.end[0];n.indent===s&&(n.source==="]"||n.source==="}")&&dg.containsNewline(e)&&t(n,"BAD_INDENT","Flow end indicator should be more indented than parent",!0)}}ru.flowIndentCheck=mg});var go=S(ou=>{"use strict";var iu=L();function pg(s,e,t){let{uniqueKeys:n}=s.options;if(n===!1)return!1;let r=typeof n=="function"?n:(i,o)=>i===o||iu.isScalar(i)&&iu.isScalar(o)&&i.value===o.value;return e.some(i=>r(i.key,t))}ou.mapIncludes=pg});var hu=S(fu=>{"use strict";var au=He(),gg=je(),lu=Os(),yg=fr(),cu=po(),Sg=go(),uu="All mapping items must start at the same column";function wg({composeNode:s,composeEmptyNode:e},t,n,r,i){let o=i?.nodeClass??gg.YAMLMap,a=new o(t.schema);t.atRoot&&(t.atRoot=!1);let l=n.offset,c=null;for(let u of n.items){let{start:f,key:h,sep:d,value:g}=u,m=lu.resolveProps(f,{indicator:"explicit-key-ind",next:h??d?.[0],offset:l,onError:r,parentIndent:n.indent,startOnNewline:!0}),p=!m.found;if(p){if(h&&(h.type==="block-seq"?r(l,"BLOCK_AS_IMPLICIT_KEY","A block sequence may not be used as an implicit map key"):"indent"in h&&h.indent!==n.indent&&r(l,"BAD_INDENT",uu)),!m.anchor&&!m.tag&&!d){c=m.end,m.comment&&(a.comment?a.comment+=`
`+m.comment:a.comment=m.comment);continue}(m.newlineAfterProp||yg.containsNewline(h))&&r(h??f[f.length-1],"MULTILINE_IMPLICIT_KEY","Implicit keys need to be on a single line")}else m.found?.indent!==n.indent&&r(l,"BAD_INDENT",uu);t.atKey=!0;let w=m.end,k=h?s(t,h,m,r):e(t,w,f,null,m,r);t.schema.compat&&cu.flowIndentCheck(n.indent,h,r),t.atKey=!1,Sg.mapIncludes(t,a.items,k)&&r(w,"DUPLICATE_KEY","Map keys must be unique");let E=lu.resolveProps(d??[],{indicator:"map-value-ind",next:g,offset:k.range[2],onError:r,parentIndent:n.indent,startOnNewline:!h||h.type==="block-scalar"});if(l=E.end,E.found){p&&(g?.type==="block-map"&&!E.hasNewline&&r(l,"BLOCK_AS_IMPLICIT_KEY","Nested mappings are not allowed in compact mappings"),t.options.strict&&m.start<E.found.offset-1024&&r(k.range,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit block mapping key"));let v=g?s(t,g,E,r):e(t,l,d,null,E,r);t.schema.compat&&cu.flowIndentCheck(n.indent,g,r),l=v.range[2];let O=new au.Pair(k,v);t.options.keepSourceTokens&&(O.srcToken=u),a.items.push(O)}else{p&&r(k.range,"MISSING_CHAR","Implicit map keys need to be followed by map values"),E.comment&&(k.comment?k.comment+=`
`+E.comment:k.comment=E.comment);let v=new au.Pair(k);t.options.keepSourceTokens&&(v.srcToken=u),a.items.push(v)}}return c&&c<l&&r(c,"IMPOSSIBLE","Map comment with trailing content"),a.range=[n.offset,l,c??l],a}fu.resolveBlockMap=wg});var mu=S(du=>{"use strict";var kg=Ze(),Tg=Os(),bg=po();function Ng({composeNode:s,composeEmptyNode:e},t,n,r,i){let o=i?.nodeClass??kg.YAMLSeq,a=new o(t.schema);t.atRoot&&(t.atRoot=!1),t.atKey&&(t.atKey=!1);let l=n.offset,c=null;for(let{start:u,value:f}of n.items){let h=Tg.resolveProps(u,{indicator:"seq-item-ind",next:f,offset:l,onError:r,parentIndent:n.indent,startOnNewline:!0});if(!h.found)if(h.anchor||h.tag||f)f&&f.type==="block-seq"?r(h.end,"BAD_INDENT","All sequence items must start at the same column"):r(l,"MISSING_CHAR","Sequence item without - indicator");else{c=h.end,h.comment&&(a.comment=h.comment);continue}let d=f?s(t,f,h,r):e(t,h.end,u,null,h,r);t.schema.compat&&bg.flowIndentCheck(n.indent,f,r),l=d.range[2],a.items.push(d)}return a.range=[n.offset,l,c??l],a}du.resolveBlockSeq=Ng});var xt=S(pu=>{"use strict";function Eg(s,e,t,n){let r="";if(s){let i=!1,o="";for(let a of s){let{source:l,type:c}=a;switch(c){case"space":i=!0;break;case"comment":{t&&!i&&n(a,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");let u=l.substring(1)||" ";r?r+=o+u:r=u,o="";break}case"newline":r&&(o+=l),i=!0;break;default:n(a,"UNEXPECTED_TOKEN",`Unexpected ${c} at node end`)}e+=l.length}}return{comment:r,offset:e}}pu.resolveEnd=Eg});var wu=S(Su=>{"use strict";var vg=L(),Ag=He(),gu=je(),Og=Ze(),Ig=xt(),yu=Os(),Dg=fr(),Cg=go(),yo="Block collections are not allowed within flow collections",So=s=>s&&(s.type==="block-map"||s.type==="block-seq");function Mg({composeNode:s,composeEmptyNode:e},t,n,r,i){let o=n.start.source==="{",a=o?"flow map":"flow sequence",l=i?.nodeClass??(o?gu.YAMLMap:Og.YAMLSeq),c=new l(t.schema);c.flow=!0;let u=t.atRoot;u&&(t.atRoot=!1),t.atKey&&(t.atKey=!1);let f=n.offset+n.start.source.length;for(let p=0;p<n.items.length;++p){let w=n.items[p],{start:k,key:E,sep:v,value:O}=w,I=yu.resolveProps(k,{flow:a,indicator:"explicit-key-ind",next:E??v?.[0],offset:f,onError:r,parentIndent:n.indent,startOnNewline:!1});if(!I.found){if(!I.anchor&&!I.tag&&!v&&!O){p===0&&I.comma?r(I.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`):p<n.items.length-1&&r(I.start,"UNEXPECTED_TOKEN",`Unexpected empty item in ${a}`),I.comment&&(c.comment?c.comment+=`
`+I.comment:c.comment=I.comment),f=I.end;continue}!o&&t.options.strict&&Dg.containsNewline(E)&&r(E,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line")}if(p===0)I.comma&&r(I.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`);else if(I.comma||r(I.start,"MISSING_CHAR",`Missing , between ${a} items`),I.comment){let C="";e:for(let T of k)switch(T.type){case"comma":case"space":break;case"comment":C=T.source.substring(1);break e;default:break e}if(C){let T=c.items[c.items.length-1];vg.isPair(T)&&(T=T.value??T.key),T.comment?T.comment+=`
`+C:T.comment=C,I.comment=I.comment.substring(C.length+1)}}if(!o&&!v&&!I.found){let C=O?s(t,O,I,r):e(t,I.end,v,null,I,r);c.items.push(C),f=C.range[2],So(O)&&r(C.range,"BLOCK_IN_FLOW",yo)}else{t.atKey=!0;let C=I.end,T=E?s(t,E,I,r):e(t,C,k,null,I,r);So(E)&&r(T.range,"BLOCK_IN_FLOW",yo),t.atKey=!1;let M=yu.resolveProps(v??[],{flow:a,indicator:"map-value-ind",next:O,offset:T.range[2],onError:r,parentIndent:n.indent,startOnNewline:!1});if(M.found){if(!o&&!I.found&&t.options.strict){if(v)for(let $ of v){if($===M.found)break;if($.type==="newline"){r($,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line");break}}I.start<M.found.offset-1024&&r(M.found,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit flow sequence key")}}else O&&("source"in O&&O.source&&O.source[0]===":"?r(O,"MISSING_CHAR",`Missing space after : in ${a}`):r(M.start,"MISSING_CHAR",`Missing , or : between ${a} items`));let Z=O?s(t,O,M,r):M.found?e(t,M.end,v,null,M,r):null;Z?So(O)&&r(Z.range,"BLOCK_IN_FLOW",yo):M.comment&&(T.comment?T.comment+=`
`+M.comment:T.comment=M.comment);let ue=new Ag.Pair(T,Z);if(t.options.keepSourceTokens&&(ue.srcToken=w),o){let $=c;Cg.mapIncludes(t,$.items,T)&&r(C,"DUPLICATE_KEY","Map keys must be unique"),$.items.push(ue)}else{let $=new gu.YAMLMap(t.schema);$.flow=!0,$.items.push(ue);let ti=(Z??T).range;$.range=[T.range[0],ti[1],ti[2]],c.items.push($)}f=Z?Z.range[2]:M.end}}let h=o?"}":"]",[d,...g]=n.end,m=f;if(d&&d.source===h)m=d.offset+d.source.length;else{let p=a[0].toUpperCase()+a.substring(1),w=u?`${p} must end with a ${h}`:`${p} in block collection must be sufficiently indented and end with a ${h}`;r(f,u?"MISSING_CHAR":"BAD_INDENT",w),d&&d.source.length!==1&&g.unshift(d)}if(g.length>0){let p=Ig.resolveEnd(g,m,t.options.strict,r);p.comment&&(c.comment?c.comment+=`
`+p.comment:c.comment=p.comment),c.range=[n.offset,m,p.offset]}else c.range=[n.offset,m,m];return c}Su.resolveFlowCollection=Mg});var Tu=S(ku=>{"use strict";var Lg=L(),Fg=U(),xg=je(),_g=Ze(),qg=hu(),Pg=mu(),$g=wu();function wo(s,e,t,n,r,i){let o=t.type==="block-map"?qg.resolveBlockMap(s,e,t,n,i):t.type==="block-seq"?Pg.resolveBlockSeq(s,e,t,n,i):$g.resolveFlowCollection(s,e,t,n,i),a=o.constructor;return r==="!"||r===a.tagName?(o.tag=a.tagName,o):(r&&(o.tag=r),o)}function Rg(s,e,t,n,r){let i=n.tag,o=i?e.directives.tagName(i.source,h=>r(i,"TAG_RESOLVE_FAILED",h)):null;if(t.type==="block-seq"){let{anchor:h,newlineAfterProp:d}=n,g=h&&i?h.offset>i.offset?h:i:h??i;g&&(!d||d.offset<g.offset)&&r(g,"MISSING_CHAR","Missing newline after block sequence props")}let a=t.type==="block-map"?"map":t.type==="block-seq"?"seq":t.start.source==="{"?"map":"seq";if(!i||!o||o==="!"||o===xg.YAMLMap.tagName&&a==="map"||o===_g.YAMLSeq.tagName&&a==="seq")return wo(s,e,t,r,o);let l=e.schema.tags.find(h=>h.tag===o&&h.collection===a);if(!l){let h=e.schema.knownTags[o];if(h&&h.collection===a)e.schema.tags.push(Object.assign({},h,{default:!1})),l=h;else return h?.collection?r(i,"BAD_COLLECTION_TYPE",`${h.tag} used for ${a} collection, but expects ${h.collection}`,!0):r(i,"TAG_RESOLVE_FAILED",`Unresolved tag: ${o}`,!0),wo(s,e,t,r,o)}let c=wo(s,e,t,r,o,l),u=l.resolve?.(c,h=>r(i,"TAG_RESOLVE_FAILED",h),e.options)??c,f=Lg.isNode(u)?u:new Fg.Scalar(u);return f.range=c.range,f.tag=o,l?.format&&(f.format=l.format),f}ku.composeCollection=Rg});var To=S(bu=>{"use strict";var ko=U();function Vg(s,e,t){let n=e.offset,r=Bg(e,s.options.strict,t);if(!r)return{value:"",type:null,comment:"",range:[n,n,n]};let i=r.mode===">"?ko.Scalar.BLOCK_FOLDED:ko.Scalar.BLOCK_LITERAL,o=e.source?Ug(e.source):[],a=o.length;for(let m=o.length-1;m>=0;--m){let p=o[m][1];if(p===""||p==="\r")a=m;else break}if(a===0){let m=r.chomp==="+"&&o.length>0?`
`.repeat(Math.max(1,o.length-1)):"",p=n+r.length;return e.source&&(p+=e.source.length),{value:m,type:i,comment:r.comment,range:[n,p,p]}}let l=e.indent+r.indent,c=e.offset+r.length,u=0;for(let m=0;m<a;++m){let[p,w]=o[m];if(w===""||w==="\r")r.indent===0&&p.length>l&&(l=p.length);else{p.length<l&&t(c+p.length,"MISSING_CHAR","Block scalars with more-indented leading empty lines must use an explicit indentation indicator"),r.indent===0&&(l=p.length),u=m,l===0&&!s.atRoot&&t(c,"BAD_INDENT","Block scalar values in collections must be indented");break}c+=p.length+w.length+1}for(let m=o.length-1;m>=a;--m)o[m][0].length>l&&(a=m+1);let f="",h="",d=!1;for(let m=0;m<u;++m)f+=o[m][0].slice(l)+`
`;for(let m=u;m<a;++m){let[p,w]=o[m];c+=p.length+w.length+1;let k=w[w.length-1]==="\r";if(k&&(w=w.slice(0,-1)),w&&p.length<l){let v=`Block scalar lines must not be less indented than their ${r.indent?"explicit indentation indicator":"first line"}`;t(c-w.length-(k?2:1),"BAD_INDENT",v),p=""}i===ko.Scalar.BLOCK_LITERAL?(f+=h+p.slice(l)+w,h=`
`):p.length>l||w[0]==="	"?(h===" "?h=`
`:!d&&h===`
`&&(h=`

`),f+=h+p.slice(l)+w,h=`
`,d=!0):w===""?h===`
`?f+=`
`:h=`
`:(f+=h+w,h=" ",d=!1)}switch(r.chomp){case"-":break;case"+":for(let m=a;m<o.length;++m)f+=`
`+o[m][0].slice(l);f[f.length-1]!==`
`&&(f+=`
`);break;default:f+=`
`}let g=n+r.length+e.source.length;return{value:f,type:i,comment:r.comment,range:[n,g,g]}}function Bg({offset:s,props:e},t,n){if(e[0].type!=="block-scalar-header")return n(e[0],"IMPOSSIBLE","Block scalar header not found"),null;let{source:r}=e[0],i=r[0],o=0,a="",l=-1;for(let h=1;h<r.length;++h){let d=r[h];if(!a&&(d==="-"||d==="+"))a=d;else{let g=Number(d);!o&&g?o=g:l===-1&&(l=s+h)}}l!==-1&&n(l,"UNEXPECTED_TOKEN",`Block scalar header includes extra characters: ${r}`);let c=!1,u="",f=r.length;for(let h=1;h<e.length;++h){let d=e[h];switch(d.type){case"space":c=!0;case"newline":f+=d.source.length;break;case"comment":t&&!c&&n(d,"MISSING_CHAR","Comments must be separated from other tokens by white space characters"),f+=d.source.length,u=d.source.substring(1);break;case"error":n(d,"UNEXPECTED_TOKEN",d.message),f+=d.source.length;break;default:{let g=`Unexpected token in block scalar header: ${d.type}`;n(d,"UNEXPECTED_TOKEN",g);let m=d.source;m&&typeof m=="string"&&(f+=m.length)}}}return{mode:i,indent:o,chomp:a,comment:u,length:f}}function Ug(s){let e=s.split(/\n( *)/),t=e[0],n=t.match(/^( *)/),i=[n?.[1]?[n[1],t.slice(n[1].length)]:["",t]];for(let o=1;o<e.length;o+=2)i.push([e[o],e[o+1]]);return i}bu.resolveBlockScalar=Vg});var No=S(Eu=>{"use strict";var bo=U(),Wg=xt();function Yg(s,e,t){let{offset:n,type:r,source:i,end:o}=s,a,l,c=(h,d,g)=>t(n+h,d,g);switch(r){case"scalar":a=bo.Scalar.PLAIN,l=Hg(i,c);break;case"single-quoted-scalar":a=bo.Scalar.QUOTE_SINGLE,l=Kg(i,c);break;case"double-quoted-scalar":a=bo.Scalar.QUOTE_DOUBLE,l=jg(i,c);break;default:return t(s,"UNEXPECTED_TOKEN",`Expected a flow scalar value, but found: ${r}`),{value:"",type:null,comment:"",range:[n,n+i.length,n+i.length]}}let u=n+i.length,f=Wg.resolveEnd(o,u,e,t);return{value:l,type:a,comment:f.comment,range:[n,u,f.offset]}}function Hg(s,e){let t="";switch(s[0]){case"	":t="a tab character";break;case",":t="flow indicator character ,";break;case"%":t="directive indicator character %";break;case"|":case">":{t=`block scalar indicator ${s[0]}`;break}case"@":case"`":{t=`reserved character ${s[0]}`;break}}return t&&e(0,"BAD_SCALAR_START",`Plain value cannot start with ${t}`),Nu(s)}function Kg(s,e){return(s[s.length-1]!=="'"||s.length===1)&&e(s.length,"MISSING_CHAR","Missing closing 'quote"),Nu(s.slice(1,-1)).replace(/''/g,"'")}function Nu(s){let e,t;try{e=new RegExp(`(.*?)(?<![ 	])[ 	]*\r?
`,"sy"),t=new RegExp(`[ 	]*(.*?)(?:(?<![ 	])[ 	]*)?\r?
`,"sy")}catch{e=/(.*?)[ \t]*\r?\n/sy,t=/[ \t]*(.*?)[ \t]*\r?\n/sy}let n=e.exec(s);if(!n)return s;let r=n[1],i=" ",o=e.lastIndex;for(t.lastIndex=o;n=t.exec(s);)n[1]===""?i===`
`?r+=i:i=`
`:(r+=i+n[1],i=" "),o=t.lastIndex;let a=/[ \t]*(.*)/sy;return a.lastIndex=o,n=a.exec(s),r+i+(n?.[1]??"")}function jg(s,e){let t="";for(let n=1;n<s.length-1;++n){let r=s[n];if(!(r==="\r"&&s[n+1]===`
`))if(r===`
`){let{fold:i,offset:o}=Zg(s,n);t+=i,n=o}else if(r==="\\"){let i=s[++n],o=Jg[i];if(o)t+=o;else if(i===`
`)for(i=s[n+1];i===" "||i==="	";)i=s[++n+1];else if(i==="\r"&&s[n+1]===`
`)for(i=s[++n+1];i===" "||i==="	";)i=s[++n+1];else if(i==="x"||i==="u"||i==="U"){let a={x:2,u:4,U:8}[i];t+=Gg(s,n+1,a,e),n+=a}else{let a=s.substr(n-1,2);e(n-1,"BAD_DQ_ESCAPE",`Invalid escape sequence ${a}`),t+=a}}else if(r===" "||r==="	"){let i=n,o=s[n+1];for(;o===" "||o==="	";)o=s[++n+1];o!==`
`&&!(o==="\r"&&s[n+2]===`
`)&&(t+=n>i?s.slice(i,n+1):r)}else t+=r}return(s[s.length-1]!=='"'||s.length===1)&&e(s.length,"MISSING_CHAR",'Missing closing "quote'),t}function Zg(s,e){let t="",n=s[e+1];for(;(n===" "||n==="	"||n===`
`||n==="\r")&&!(n==="\r"&&s[e+2]!==`
`);)n===`
`&&(t+=`
`),e+=1,n=s[e+1];return t||(t=" "),{fold:t,offset:e}}var Jg={0:"\0",a:"\x07",b:"\b",e:"\x1B",f:"\f",n:`
`,r:"\r",t:"	",v:"\v",N:"\x85",_:"\xA0",L:"\u2028",P:"\u2029"," ":" ",'"':'"',"/":"/","\\":"\\","	":"	"};function Gg(s,e,t,n){let r=s.substr(e,t),o=r.length===t&&/^[0-9a-fA-F]+$/.test(r)?parseInt(r,16):NaN;if(isNaN(o)){let a=s.substr(e-2,t+2);return n(e-2,"BAD_DQ_ESCAPE",`Invalid escape sequence ${a}`),a}return String.fromCodePoint(o)}Eu.resolveFlowScalar=Yg});var Ou=S(Au=>{"use strict";var ft=L(),vu=U(),zg=To(),Qg=No();function Xg(s,e,t,n){let{value:r,type:i,comment:o,range:a}=e.type==="block-scalar"?zg.resolveBlockScalar(s,e,n):Qg.resolveFlowScalar(e,s.options.strict,n),l=t?s.directives.tagName(t.source,f=>n(t,"TAG_RESOLVE_FAILED",f)):null,c;s.options.stringKeys&&s.atKey?c=s.schema[ft.SCALAR]:l?c=ey(s.schema,r,l,t,n):e.type==="scalar"?c=ty(s,r,e,n):c=s.schema[ft.SCALAR];let u;try{let f=c.resolve(r,h=>n(t??e,"TAG_RESOLVE_FAILED",h),s.options);u=ft.isScalar(f)?f:new vu.Scalar(f)}catch(f){let h=f instanceof Error?f.message:String(f);n(t??e,"TAG_RESOLVE_FAILED",h),u=new vu.Scalar(r)}return u.range=a,u.source=r,i&&(u.type=i),l&&(u.tag=l),c.format&&(u.format=c.format),o&&(u.comment=o),u}function ey(s,e,t,n,r){if(t==="!")return s[ft.SCALAR];let i=[];for(let a of s.tags)if(!a.collection&&a.tag===t)if(a.default&&a.test)i.push(a);else return a;for(let a of i)if(a.test?.test(e))return a;let o=s.knownTags[t];return o&&!o.collection?(s.tags.push(Object.assign({},o,{default:!1,test:void 0})),o):(r(n,"TAG_RESOLVE_FAILED",`Unresolved tag: ${t}`,t!=="tag:yaml.org,2002:str"),s[ft.SCALAR])}function ty({atKey:s,directives:e,schema:t},n,r,i){let o=t.tags.find(a=>(a.default===!0||s&&a.default==="key")&&a.test?.test(n))||t[ft.SCALAR];if(t.compat){let a=t.compat.find(l=>l.default&&l.test?.test(n))??t[ft.SCALAR];if(o.tag!==a.tag){let l=e.tagString(o.tag),c=e.tagString(a.tag),u=`Value may be parsed as either ${l} or ${c}`;i(r,"TAG_RESOLVE_FAILED",u,!0)}}return o}Au.composeScalar=Xg});var Du=S(Iu=>{"use strict";function sy(s,e,t){if(e){t===null&&(t=e.length);for(let n=t-1;n>=0;--n){let r=e[n];switch(r.type){case"space":case"comment":case"newline":s-=r.source.length;continue}for(r=e[++n];r?.type==="space";)s+=r.source.length,r=e[++n];break}}return s}Iu.emptyScalarPosition=sy});var Lu=S(vo=>{"use strict";var ny=os(),ry=L(),iy=Tu(),Cu=Ou(),oy=xt(),ay=Du(),ly={composeNode:Mu,composeEmptyNode:Eo};function Mu(s,e,t,n){let r=s.atKey,{spaceBefore:i,comment:o,anchor:a,tag:l}=t,c,u=!0;switch(e.type){case"alias":c=cy(s,e,n),(a||l)&&n(e,"ALIAS_PROPS","An alias node must not specify any properties");break;case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"block-scalar":c=Cu.composeScalar(s,e,l,n),a&&(c.anchor=a.source.substring(1));break;case"block-map":case"block-seq":case"flow-collection":c=iy.composeCollection(ly,s,e,t,n),a&&(c.anchor=a.source.substring(1));break;default:{let f=e.type==="error"?e.message:`Unsupported token (type: ${e.type})`;n(e,"UNEXPECTED_TOKEN",f),c=Eo(s,e.offset,void 0,null,t,n),u=!1}}return a&&c.anchor===""&&n(a,"BAD_ALIAS","Anchor cannot be an empty string"),r&&s.options.stringKeys&&(!ry.isScalar(c)||typeof c.value!="string"||c.tag&&c.tag!=="tag:yaml.org,2002:str")&&n(l??e,"NON_STRING_KEY","With stringKeys, all keys must be strings"),i&&(c.spaceBefore=!0),o&&(e.type==="scalar"&&e.source===""?c.comment=o:c.commentBefore=o),s.options.keepSourceTokens&&u&&(c.srcToken=e),c}function Eo(s,e,t,n,{spaceBefore:r,comment:i,anchor:o,tag:a,end:l},c){let u={type:"scalar",offset:ay.emptyScalarPosition(e,t,n),indent:-1,source:""},f=Cu.composeScalar(s,u,a,c);return o&&(f.anchor=o.source.substring(1),f.anchor===""&&c(o,"BAD_ALIAS","Anchor cannot be an empty string")),r&&(f.spaceBefore=!0),i&&(f.comment=i,f.range[2]=l),f}function cy({options:s},{offset:e,source:t,end:n},r){let i=new ny.Alias(t.substring(1));i.source===""&&r(e,"BAD_ALIAS","Alias cannot be an empty string"),i.source.endsWith(":")&&r(e+t.length-1,"BAD_ALIAS","Alias ending in : is ambiguous",!0);let o=e+t.length,a=oy.resolveEnd(n,o,s.strict,r);return i.range=[e,o,a.offset],a.comment&&(i.comment=a.comment),i}vo.composeEmptyNode=Eo;vo.composeNode=Mu});var _u=S(xu=>{"use strict";var uy=Ns(),Fu=Lu(),fy=xt(),hy=Os();function dy(s,e,{offset:t,start:n,value:r,end:i},o){let a=Object.assign({_directives:e},s),l=new uy.Document(void 0,a),c={atKey:!1,atRoot:!0,directives:l.directives,options:l.options,schema:l.schema},u=hy.resolveProps(n,{indicator:"doc-start",next:r??i?.[0],offset:t,onError:o,parentIndent:0,startOnNewline:!0});u.found&&(l.directives.docStart=!0,r&&(r.type==="block-map"||r.type==="block-seq")&&!u.hasNewline&&o(u.end,"MISSING_CHAR","Block collection cannot start on same line with directives-end marker")),l.contents=r?Fu.composeNode(c,r,u,o):Fu.composeEmptyNode(c,u.end,n,null,u,o);let f=l.contents.range[2],h=fy.resolveEnd(i,f,!1,o);return h.comment&&(l.comment=h.comment),l.range=[t,f,h.offset],l}xu.composeDoc=dy});var Oo=S($u=>{"use strict";var my=require("node:process"),py=fi(),gy=Ns(),Is=As(),qu=L(),yy=_u(),Sy=xt();function Ds(s){if(typeof s=="number")return[s,s+1];if(Array.isArray(s))return s.length===2?s:[s[0],s[1]];let{offset:e,source:t}=s;return[e,e+(typeof t=="string"?t.length:1)]}function Pu(s){let e="",t=!1,n=!1;for(let r=0;r<s.length;++r){let i=s[r];switch(i[0]){case"#":e+=(e===""?"":n?`

`:`
`)+(i.substring(1)||" "),t=!0,n=!1;break;case"%":s[r+1]?.[0]!=="#"&&(r+=1),t=!1;break;default:t||(n=!0),t=!1}}return{comment:e,afterEmptyLine:n}}var Ao=class{constructor(e={}){this.doc=null,this.atDirectives=!1,this.prelude=[],this.errors=[],this.warnings=[],this.onError=(t,n,r,i)=>{let o=Ds(t);i?this.warnings.push(new Is.YAMLWarning(o,n,r)):this.errors.push(new Is.YAMLParseError(o,n,r))},this.directives=new py.Directives({version:e.version||"1.2"}),this.options=e}decorate(e,t){let{comment:n,afterEmptyLine:r}=Pu(this.prelude);if(n){let i=e.contents;if(t)e.comment=e.comment?`${e.comment}
${n}`:n;else if(r||e.directives.docStart||!i)e.commentBefore=n;else if(qu.isCollection(i)&&!i.flow&&i.items.length>0){let o=i.items[0];qu.isPair(o)&&(o=o.key);let a=o.commentBefore;o.commentBefore=a?`${n}
${a}`:n}else{let o=i.commentBefore;i.commentBefore=o?`${n}
${o}`:n}}t?(Array.prototype.push.apply(e.errors,this.errors),Array.prototype.push.apply(e.warnings,this.warnings)):(e.errors=this.errors,e.warnings=this.warnings),this.prelude=[],this.errors=[],this.warnings=[]}streamInfo(){return{comment:Pu(this.prelude).comment,directives:this.directives,errors:this.errors,warnings:this.warnings}}*compose(e,t=!1,n=-1){for(let r of e)yield*this.next(r);yield*this.end(t,n)}*next(e){switch(my.env.LOG_STREAM&&console.dir(e,{depth:null}),e.type){case"directive":this.directives.add(e.source,(t,n,r)=>{let i=Ds(e);i[0]+=t,this.onError(i,"BAD_DIRECTIVE",n,r)}),this.prelude.push(e.source),this.atDirectives=!0;break;case"document":{let t=yy.composeDoc(this.options,this.directives,e,this.onError);this.atDirectives&&!t.directives.docStart&&this.onError(e,"MISSING_CHAR","Missing directives-end/doc-start indicator line"),this.decorate(t,!1),this.doc&&(yield this.doc),this.doc=t,this.atDirectives=!1;break}case"byte-order-mark":case"space":break;case"comment":case"newline":this.prelude.push(e.source);break;case"error":{let t=e.source?`${e.message}: ${JSON.stringify(e.source)}`:e.message,n=new Is.YAMLParseError(Ds(e),"UNEXPECTED_TOKEN",t);this.atDirectives||!this.doc?this.errors.push(n):this.doc.errors.push(n);break}case"doc-end":{if(!this.doc){let n="Unexpected doc-end without preceding document";this.errors.push(new Is.YAMLParseError(Ds(e),"UNEXPECTED_TOKEN",n));break}this.doc.directives.docEnd=!0;let t=Sy.resolveEnd(e.end,e.offset+e.source.length,this.doc.options.strict,this.onError);if(this.decorate(this.doc,!0),t.comment){let n=this.doc.comment;this.doc.comment=n?`${n}
${t.comment}`:t.comment}this.doc.range[2]=t.offset;break}default:this.errors.push(new Is.YAMLParseError(Ds(e),"UNEXPECTED_TOKEN",`Unsupported token ${e.type}`))}}*end(e=!1,t=-1){if(this.doc)this.decorate(this.doc,!0),yield this.doc,this.doc=null;else if(e){let n=Object.assign({_directives:this.directives},this.options),r=new gy.Document(void 0,n);this.atDirectives&&this.onError(t,"MISSING_CHAR","Missing directives-end indicator line"),r.range=[0,t,t],this.decorate(r,!1),yield r}}};$u.Composer=Ao});var Bu=S(hr=>{"use strict";var wy=To(),ky=No(),Ty=As(),Ru=fs();function by(s,e=!0,t){if(s){let n=(r,i,o)=>{let a=typeof r=="number"?r:Array.isArray(r)?r[0]:r.offset;if(t)t(a,i,o);else throw new Ty.YAMLParseError([a,a+1],i,o)};switch(s.type){case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return ky.resolveFlowScalar(s,e,n);case"block-scalar":return wy.resolveBlockScalar({options:{strict:e}},s,n)}}return null}function Ny(s,e){let{implicitKey:t=!1,indent:n,inFlow:r=!1,offset:i=-1,type:o="PLAIN"}=e,a=Ru.stringifyString({type:o,value:s},{implicitKey:t,indent:n>0?" ".repeat(n):"",inFlow:r,options:{blockQuote:!0,lineWidth:-1}}),l=e.end??[{type:"newline",offset:-1,indent:n,source:`
`}];switch(a[0]){case"|":case">":{let c=a.indexOf(`
`),u=a.substring(0,c),f=a.substring(c+1)+`
`,h=[{type:"block-scalar-header",offset:i,indent:n,source:u}];return Vu(h,l)||h.push({type:"newline",offset:-1,indent:n,source:`
`}),{type:"block-scalar",offset:i,indent:n,props:h,source:f}}case'"':return{type:"double-quoted-scalar",offset:i,indent:n,source:a,end:l};case"'":return{type:"single-quoted-scalar",offset:i,indent:n,source:a,end:l};default:return{type:"scalar",offset:i,indent:n,source:a,end:l}}}function Ey(s,e,t={}){let{afterKey:n=!1,implicitKey:r=!1,inFlow:i=!1,type:o}=t,a="indent"in s?s.indent:null;if(n&&typeof a=="number"&&(a+=2),!o)switch(s.type){case"single-quoted-scalar":o="QUOTE_SINGLE";break;case"double-quoted-scalar":o="QUOTE_DOUBLE";break;case"block-scalar":{let c=s.props[0];if(c.type!=="block-scalar-header")throw new Error("Invalid block scalar header");o=c.source[0]===">"?"BLOCK_FOLDED":"BLOCK_LITERAL";break}default:o="PLAIN"}let l=Ru.stringifyString({type:o,value:e},{implicitKey:r||a===null,indent:a!==null&&a>0?" ".repeat(a):"",inFlow:i,options:{blockQuote:!0,lineWidth:-1}});switch(l[0]){case"|":case">":vy(s,l);break;case'"':Io(s,l,"double-quoted-scalar");break;case"'":Io(s,l,"single-quoted-scalar");break;default:Io(s,l,"scalar")}}function vy(s,e){let t=e.indexOf(`
`),n=e.substring(0,t),r=e.substring(t+1)+`
`;if(s.type==="block-scalar"){let i=s.props[0];if(i.type!=="block-scalar-header")throw new Error("Invalid block scalar header");i.source=n,s.source=r}else{let{offset:i}=s,o="indent"in s?s.indent:-1,a=[{type:"block-scalar-header",offset:i,indent:o,source:n}];Vu(a,"end"in s?s.end:void 0)||a.push({type:"newline",offset:-1,indent:o,source:`
`});for(let l of Object.keys(s))l!=="type"&&l!=="offset"&&delete s[l];Object.assign(s,{type:"block-scalar",indent:o,props:a,source:r})}}function Vu(s,e){if(e)for(let t of e)switch(t.type){case"space":case"comment":s.push(t);break;case"newline":return s.push(t),!0}return!1}function Io(s,e,t){switch(s.type){case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":s.type=t,s.source=e;break;case"block-scalar":{let n=s.props.slice(1),r=e.length;s.props[0].type==="block-scalar-header"&&(r-=s.props[0].source.length);for(let i of n)i.offset+=r;delete s.props,Object.assign(s,{type:t,source:e,end:n});break}case"block-map":case"block-seq":{let r={type:"newline",offset:s.offset+e.length,indent:s.indent,source:`
`};delete s.items,Object.assign(s,{type:t,source:e,end:[r]});break}default:{let n="indent"in s?s.indent:-1,r="end"in s&&Array.isArray(s.end)?s.end.filter(i=>i.type==="space"||i.type==="comment"||i.type==="newline"):[];for(let i of Object.keys(s))i!=="type"&&i!=="offset"&&delete s[i];Object.assign(s,{type:t,indent:n,source:e,end:r})}}}hr.createScalarToken=Ny;hr.resolveAsScalar=by;hr.setScalarValue=Ey});var Wu=S(Uu=>{"use strict";var Ay=s=>"type"in s?mr(s):dr(s);function mr(s){switch(s.type){case"block-scalar":{let e="";for(let t of s.props)e+=mr(t);return e+s.source}case"block-map":case"block-seq":{let e="";for(let t of s.items)e+=dr(t);return e}case"flow-collection":{let e=s.start.source;for(let t of s.items)e+=dr(t);for(let t of s.end)e+=t.source;return e}case"document":{let e=dr(s);if(s.end)for(let t of s.end)e+=t.source;return e}default:{let e=s.source;if("end"in s&&s.end)for(let t of s.end)e+=t.source;return e}}}function dr({start:s,key:e,sep:t,value:n}){let r="";for(let i of s)r+=i.source;if(e&&(r+=mr(e)),t)for(let i of t)r+=i.source;return n&&(r+=mr(n)),r}Uu.stringify=Ay});var ju=S(Ku=>{"use strict";var Do=Symbol("break visit"),Oy=Symbol("skip children"),Yu=Symbol("remove item");function ht(s,e){"type"in s&&s.type==="document"&&(s={start:s.start,value:s.value}),Hu(Object.freeze([]),s,e)}ht.BREAK=Do;ht.SKIP=Oy;ht.REMOVE=Yu;ht.itemAtPath=(s,e)=>{let t=s;for(let[n,r]of e){let i=t?.[n];if(i&&"items"in i)t=i.items[r];else return}return t};ht.parentCollection=(s,e)=>{let t=ht.itemAtPath(s,e.slice(0,-1)),n=e[e.length-1][0],r=t?.[n];if(r&&"items"in r)return r;throw new Error("Parent collection not found")};function Hu(s,e,t){let n=t(e,s);if(typeof n=="symbol")return n;for(let r of["key","value"]){let i=e[r];if(i&&"items"in i){for(let o=0;o<i.items.length;++o){let a=Hu(Object.freeze(s.concat([[r,o]])),i.items[o],t);if(typeof a=="number")o=a-1;else{if(a===Do)return Do;a===Yu&&(i.items.splice(o,1),o-=1)}}typeof n=="function"&&r==="key"&&(n=n(e,s))}}return typeof n=="function"?n(e,s):n}Ku.visit=ht});var pr=S(re=>{"use strict";var Co=Bu(),Iy=Wu(),Dy=ju(),Mo="\uFEFF",Lo="",Fo="",xo="",Cy=s=>!!s&&"items"in s,My=s=>!!s&&(s.type==="scalar"||s.type==="single-quoted-scalar"||s.type==="double-quoted-scalar"||s.type==="block-scalar");function Ly(s){switch(s){case Mo:return"<BOM>";case Lo:return"<DOC>";case Fo:return"<FLOW_END>";case xo:return"<SCALAR>";default:return JSON.stringify(s)}}function Fy(s){switch(s){case Mo:return"byte-order-mark";case Lo:return"doc-mode";case Fo:return"flow-error-end";case xo:return"scalar";case"---":return"doc-start";case"...":return"doc-end";case"":case`
`:case`\r
`:return"newline";case"-":return"seq-item-ind";case"?":return"explicit-key-ind";case":":return"map-value-ind";case"{":return"flow-map-start";case"}":return"flow-map-end";case"[":return"flow-seq-start";case"]":return"flow-seq-end";case",":return"comma"}switch(s[0]){case" ":case"	":return"space";case"#":return"comment";case"%":return"directive-line";case"*":return"alias";case"&":return"anchor";case"!":return"tag";case"'":return"single-quoted-scalar";case'"':return"double-quoted-scalar";case"|":case">":return"block-scalar-header"}return null}re.createScalarToken=Co.createScalarToken;re.resolveAsScalar=Co.resolveAsScalar;re.setScalarValue=Co.setScalarValue;re.stringify=Iy.stringify;re.visit=Dy.visit;re.BOM=Mo;re.DOCUMENT=Lo;re.FLOW_END=Fo;re.SCALAR=xo;re.isCollection=Cy;re.isScalar=My;re.prettyToken=Ly;re.tokenType=Fy});var Po=S(Ju=>{"use strict";var Cs=pr();function Se(s){switch(s){case void 0:case" ":case`
`:case"\r":case"	":return!0;default:return!1}}var Zu=new Set("0123456789ABCDEFabcdef"),xy=new Set("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-#;/?:@&=+$_.!~*'()"),gr=new Set(",[]{}"),_y=new Set(` ,[]{}
\r	`),_o=s=>!s||_y.has(s),qo=class{constructor(){this.atEnd=!1,this.blockScalarIndent=-1,this.blockScalarKeep=!1,this.buffer="",this.flowKey=!1,this.flowLevel=0,this.indentNext=0,this.indentValue=0,this.lineEndPos=null,this.next=null,this.pos=0}*lex(e,t=!1){if(e){if(typeof e!="string")throw TypeError("source is not a string");this.buffer=this.buffer?this.buffer+e:e,this.lineEndPos=null}this.atEnd=!t;let n=this.next??"stream";for(;n&&(t||this.hasChars(1));)n=yield*this.parseNext(n)}atLineEnd(){let e=this.pos,t=this.buffer[e];for(;t===" "||t==="	";)t=this.buffer[++e];return!t||t==="#"||t===`
`?!0:t==="\r"?this.buffer[e+1]===`
`:!1}charAt(e){return this.buffer[this.pos+e]}continueScalar(e){let t=this.buffer[e];if(this.indentNext>0){let n=0;for(;t===" ";)t=this.buffer[++n+e];if(t==="\r"){let r=this.buffer[n+e+1];if(r===`
`||!r&&!this.atEnd)return e+n+1}return t===`
`||n>=this.indentNext||!t&&!this.atEnd?e+n:-1}if(t==="-"||t==="."){let n=this.buffer.substr(e,3);if((n==="---"||n==="...")&&Se(this.buffer[e+3]))return-1}return e}getLine(){let e=this.lineEndPos;return(typeof e!="number"||e!==-1&&e<this.pos)&&(e=this.buffer.indexOf(`
`,this.pos),this.lineEndPos=e),e===-1?this.atEnd?this.buffer.substring(this.pos):null:(this.buffer[e-1]==="\r"&&(e-=1),this.buffer.substring(this.pos,e))}hasChars(e){return this.pos+e<=this.buffer.length}setNext(e){return this.buffer=this.buffer.substring(this.pos),this.pos=0,this.lineEndPos=null,this.next=e,null}peek(e){return this.buffer.substr(this.pos,e)}*parseNext(e){switch(e){case"stream":return yield*this.parseStream();case"line-start":return yield*this.parseLineStart();case"block-start":return yield*this.parseBlockStart();case"doc":return yield*this.parseDocument();case"flow":return yield*this.parseFlowCollection();case"quoted-scalar":return yield*this.parseQuotedScalar();case"block-scalar":return yield*this.parseBlockScalar();case"plain-scalar":return yield*this.parsePlainScalar()}}*parseStream(){let e=this.getLine();if(e===null)return this.setNext("stream");if(e[0]===Cs.BOM&&(yield*this.pushCount(1),e=e.substring(1)),e[0]==="%"){let t=e.length,n=e.indexOf("#");for(;n!==-1;){let i=e[n-1];if(i===" "||i==="	"){t=n-1;break}else n=e.indexOf("#",n+1)}for(;;){let i=e[t-1];if(i===" "||i==="	")t-=1;else break}let r=(yield*this.pushCount(t))+(yield*this.pushSpaces(!0));return yield*this.pushCount(e.length-r),this.pushNewline(),"stream"}if(this.atLineEnd()){let t=yield*this.pushSpaces(!0);return yield*this.pushCount(e.length-t),yield*this.pushNewline(),"stream"}return yield Cs.DOCUMENT,yield*this.parseLineStart()}*parseLineStart(){let e=this.charAt(0);if(!e&&!this.atEnd)return this.setNext("line-start");if(e==="-"||e==="."){if(!this.atEnd&&!this.hasChars(4))return this.setNext("line-start");let t=this.peek(3);if((t==="---"||t==="...")&&Se(this.charAt(3)))return yield*this.pushCount(3),this.indentValue=0,this.indentNext=0,t==="---"?"doc":"stream"}return this.indentValue=yield*this.pushSpaces(!1),this.indentNext>this.indentValue&&!Se(this.charAt(1))&&(this.indentNext=this.indentValue),yield*this.parseBlockStart()}*parseBlockStart(){let[e,t]=this.peek(2);if(!t&&!this.atEnd)return this.setNext("block-start");if((e==="-"||e==="?"||e===":")&&Se(t)){let n=(yield*this.pushCount(1))+(yield*this.pushSpaces(!0));return this.indentNext=this.indentValue+1,this.indentValue+=n,yield*this.parseBlockStart()}return"doc"}*parseDocument(){yield*this.pushSpaces(!0);let e=this.getLine();if(e===null)return this.setNext("doc");let t=yield*this.pushIndicators();switch(e[t]){case"#":yield*this.pushCount(e.length-t);case void 0:return yield*this.pushNewline(),yield*this.parseLineStart();case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel=1,"flow";case"}":case"]":return yield*this.pushCount(1),"doc";case"*":return yield*this.pushUntil(_o),"doc";case'"':case"'":return yield*this.parseQuotedScalar();case"|":case">":return t+=yield*this.parseBlockScalarHeader(),t+=yield*this.pushSpaces(!0),yield*this.pushCount(e.length-t),yield*this.pushNewline(),yield*this.parseBlockScalar();default:return yield*this.parsePlainScalar()}}*parseFlowCollection(){let e,t,n=-1;do e=yield*this.pushNewline(),e>0?(t=yield*this.pushSpaces(!1),this.indentValue=n=t):t=0,t+=yield*this.pushSpaces(!0);while(e+t>0);let r=this.getLine();if(r===null)return this.setNext("flow");if((n!==-1&&n<this.indentNext&&r[0]!=="#"||n===0&&(r.startsWith("---")||r.startsWith("..."))&&Se(r[3]))&&!(n===this.indentNext-1&&this.flowLevel===1&&(r[0]==="]"||r[0]==="}")))return this.flowLevel=0,yield Cs.FLOW_END,yield*this.parseLineStart();let i=0;for(;r[i]===",";)i+=yield*this.pushCount(1),i+=yield*this.pushSpaces(!0),this.flowKey=!1;switch(i+=yield*this.pushIndicators(),r[i]){case void 0:return"flow";case"#":return yield*this.pushCount(r.length-i),"flow";case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel+=1,"flow";case"}":case"]":return yield*this.pushCount(1),this.flowKey=!0,this.flowLevel-=1,this.flowLevel?"flow":"doc";case"*":return yield*this.pushUntil(_o),"flow";case'"':case"'":return this.flowKey=!0,yield*this.parseQuotedScalar();case":":{let o=this.charAt(1);if(this.flowKey||Se(o)||o===",")return this.flowKey=!1,yield*this.pushCount(1),yield*this.pushSpaces(!0),"flow"}default:return this.flowKey=!1,yield*this.parsePlainScalar()}}*parseQuotedScalar(){let e=this.charAt(0),t=this.buffer.indexOf(e,this.pos+1);if(e==="'")for(;t!==-1&&this.buffer[t+1]==="'";)t=this.buffer.indexOf("'",t+2);else for(;t!==-1;){let i=0;for(;this.buffer[t-1-i]==="\\";)i+=1;if(i%2===0)break;t=this.buffer.indexOf('"',t+1)}let n=this.buffer.substring(0,t),r=n.indexOf(`
`,this.pos);if(r!==-1){for(;r!==-1;){let i=this.continueScalar(r+1);if(i===-1)break;r=n.indexOf(`
`,i)}r!==-1&&(t=r-(n[r-1]==="\r"?2:1))}if(t===-1){if(!this.atEnd)return this.setNext("quoted-scalar");t=this.buffer.length}return yield*this.pushToIndex(t+1,!1),this.flowLevel?"flow":"doc"}*parseBlockScalarHeader(){this.blockScalarIndent=-1,this.blockScalarKeep=!1;let e=this.pos;for(;;){let t=this.buffer[++e];if(t==="+")this.blockScalarKeep=!0;else if(t>"0"&&t<="9")this.blockScalarIndent=Number(t)-1;else if(t!=="-")break}return yield*this.pushUntil(t=>Se(t)||t==="#")}*parseBlockScalar(){let e=this.pos-1,t=0,n;e:for(let i=this.pos;n=this.buffer[i];++i)switch(n){case" ":t+=1;break;case`
`:e=i,t=0;break;case"\r":{let o=this.buffer[i+1];if(!o&&!this.atEnd)return this.setNext("block-scalar");if(o===`
`)break}default:break e}if(!n&&!this.atEnd)return this.setNext("block-scalar");if(t>=this.indentNext){this.blockScalarIndent===-1?this.indentNext=t:this.indentNext=this.blockScalarIndent+(this.indentNext===0?1:this.indentNext);do{let i=this.continueScalar(e+1);if(i===-1)break;e=this.buffer.indexOf(`
`,i)}while(e!==-1);if(e===-1){if(!this.atEnd)return this.setNext("block-scalar");e=this.buffer.length}}let r=e+1;for(n=this.buffer[r];n===" ";)n=this.buffer[++r];if(n==="	"){for(;n==="	"||n===" "||n==="\r"||n===`
`;)n=this.buffer[++r];e=r-1}else if(!this.blockScalarKeep)do{let i=e-1,o=this.buffer[i];o==="\r"&&(o=this.buffer[--i]);let a=i;for(;o===" ";)o=this.buffer[--i];if(o===`
`&&i>=this.pos&&i+1+t>a)e=i;else break}while(!0);return yield Cs.SCALAR,yield*this.pushToIndex(e+1,!0),yield*this.parseLineStart()}*parsePlainScalar(){let e=this.flowLevel>0,t=this.pos-1,n=this.pos-1,r;for(;r=this.buffer[++n];)if(r===":"){let i=this.buffer[n+1];if(Se(i)||e&&gr.has(i))break;t=n}else if(Se(r)){let i=this.buffer[n+1];if(r==="\r"&&(i===`
`?(n+=1,r=`
`,i=this.buffer[n+1]):t=n),i==="#"||e&&gr.has(i))break;if(r===`
`){let o=this.continueScalar(n+1);if(o===-1)break;n=Math.max(n,o-2)}}else{if(e&&gr.has(r))break;t=n}return!r&&!this.atEnd?this.setNext("plain-scalar"):(yield Cs.SCALAR,yield*this.pushToIndex(t+1,!0),e?"flow":"doc")}*pushCount(e){return e>0?(yield this.buffer.substr(this.pos,e),this.pos+=e,e):0}*pushToIndex(e,t){let n=this.buffer.slice(this.pos,e);return n?(yield n,this.pos+=n.length,n.length):(t&&(yield""),0)}*pushIndicators(){switch(this.charAt(0)){case"!":return(yield*this.pushTag())+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"&":return(yield*this.pushUntil(_o))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"-":case"?":case":":{let e=this.flowLevel>0,t=this.charAt(1);if(Se(t)||e&&gr.has(t))return e?this.flowKey&&(this.flowKey=!1):this.indentNext=this.indentValue+1,(yield*this.pushCount(1))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators())}}return 0}*pushTag(){if(this.charAt(1)==="<"){let e=this.pos+2,t=this.buffer[e];for(;!Se(t)&&t!==">";)t=this.buffer[++e];return yield*this.pushToIndex(t===">"?e+1:e,!1)}else{let e=this.pos+1,t=this.buffer[e];for(;t;)if(xy.has(t))t=this.buffer[++e];else if(t==="%"&&Zu.has(this.buffer[e+1])&&Zu.has(this.buffer[e+2]))t=this.buffer[e+=3];else break;return yield*this.pushToIndex(e,!1)}}*pushNewline(){let e=this.buffer[this.pos];return e===`
`?yield*this.pushCount(1):e==="\r"&&this.charAt(1)===`
`?yield*this.pushCount(2):0}*pushSpaces(e){let t=this.pos-1,n;do n=this.buffer[++t];while(n===" "||e&&n==="	");let r=t-this.pos;return r>0&&(yield this.buffer.substr(this.pos,r),this.pos=t),r}*pushUntil(e){let t=this.pos,n=this.buffer[t];for(;!e(n);)n=this.buffer[++t];return yield*this.pushToIndex(t,!1)}};Ju.Lexer=qo});var Ro=S(Gu=>{"use strict";var $o=class{constructor(){this.lineStarts=[],this.addNewLine=e=>this.lineStarts.push(e),this.linePos=e=>{let t=0,n=this.lineStarts.length;for(;t<n;){let i=t+n>>1;this.lineStarts[i]<e?t=i+1:n=i}if(this.lineStarts[t]===e)return{line:t+1,col:1};if(t===0)return{line:0,col:e};let r=this.lineStarts[t-1];return{line:t,col:e-r+1}}}};Gu.LineCounter=$o});var Bo=S(tf=>{"use strict";var qy=require("node:process"),zu=pr(),Py=Po();function dt(s,e){for(let t=0;t<s.length;++t)if(s[t].type===e)return!0;return!1}function Qu(s){for(let e=0;e<s.length;++e)switch(s[e].type){case"space":case"comment":case"newline":break;default:return e}return-1}function ef(s){switch(s?.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"flow-collection":return!0;default:return!1}}function yr(s){switch(s.type){case"document":return s.start;case"block-map":{let e=s.items[s.items.length-1];return e.sep??e.start}case"block-seq":return s.items[s.items.length-1].start;default:return[]}}function _t(s){if(s.length===0)return[];let e=s.length;e:for(;--e>=0;)switch(s[e].type){case"doc-start":case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":case"newline":break e}for(;s[++e]?.type==="space";);return s.splice(e,s.length)}function Xu(s){if(s.start.type==="flow-seq-start")for(let e of s.items)e.sep&&!e.value&&!dt(e.start,"explicit-key-ind")&&!dt(e.sep,"map-value-ind")&&(e.key&&(e.value=e.key),delete e.key,ef(e.value)?e.value.end?Array.prototype.push.apply(e.value.end,e.sep):e.value.end=e.sep:Array.prototype.push.apply(e.start,e.sep),delete e.sep)}var Vo=class{constructor(e){this.atNewLine=!0,this.atScalar=!1,this.indent=0,this.offset=0,this.onKeyLine=!1,this.stack=[],this.source="",this.type="",this.lexer=new Py.Lexer,this.onNewLine=e}*parse(e,t=!1){this.onNewLine&&this.offset===0&&this.onNewLine(0);for(let n of this.lexer.lex(e,t))yield*this.next(n);t||(yield*this.end())}*next(e){if(this.source=e,qy.env.LOG_TOKENS&&console.log("|",zu.prettyToken(e)),this.atScalar){this.atScalar=!1,yield*this.step(),this.offset+=e.length;return}let t=zu.tokenType(e);if(t)if(t==="scalar")this.atNewLine=!1,this.atScalar=!0,this.type="scalar";else{switch(this.type=t,yield*this.step(),t){case"newline":this.atNewLine=!0,this.indent=0,this.onNewLine&&this.onNewLine(this.offset+e.length);break;case"space":this.atNewLine&&e[0]===" "&&(this.indent+=e.length);break;case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":this.atNewLine&&(this.indent+=e.length);break;case"doc-mode":case"flow-error-end":return;default:this.atNewLine=!1}this.offset+=e.length}else{let n=`Not a YAML token: ${e}`;yield*this.pop({type:"error",offset:this.offset,message:n,source:e}),this.offset+=e.length}}*end(){for(;this.stack.length>0;)yield*this.pop()}get sourceToken(){return{type:this.type,offset:this.offset,indent:this.indent,source:this.source}}*step(){let e=this.peek(1);if(this.type==="doc-end"&&(!e||e.type!=="doc-end")){for(;this.stack.length>0;)yield*this.pop();this.stack.push({type:"doc-end",offset:this.offset,source:this.source});return}if(!e)return yield*this.stream();switch(e.type){case"document":return yield*this.document(e);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return yield*this.scalar(e);case"block-scalar":return yield*this.blockScalar(e);case"block-map":return yield*this.blockMap(e);case"block-seq":return yield*this.blockSequence(e);case"flow-collection":return yield*this.flowCollection(e);case"doc-end":return yield*this.documentEnd(e)}yield*this.pop()}peek(e){return this.stack[this.stack.length-e]}*pop(e){let t=e??this.stack.pop();if(!t)yield{type:"error",offset:this.offset,source:"",message:"Tried to pop an empty stack"};else if(this.stack.length===0)yield t;else{let n=this.peek(1);switch(t.type==="block-scalar"?t.indent="indent"in n?n.indent:0:t.type==="flow-collection"&&n.type==="document"&&(t.indent=0),t.type==="flow-collection"&&Xu(t),n.type){case"document":n.value=t;break;case"block-scalar":n.props.push(t);break;case"block-map":{let r=n.items[n.items.length-1];if(r.value){n.items.push({start:[],key:t,sep:[]}),this.onKeyLine=!0;return}else if(r.sep)r.value=t;else{Object.assign(r,{key:t,sep:[]}),this.onKeyLine=!r.explicitKey;return}break}case"block-seq":{let r=n.items[n.items.length-1];r.value?n.items.push({start:[],value:t}):r.value=t;break}case"flow-collection":{let r=n.items[n.items.length-1];!r||r.value?n.items.push({start:[],key:t,sep:[]}):r.sep?r.value=t:Object.assign(r,{key:t,sep:[]});return}default:yield*this.pop(),yield*this.pop(t)}if((n.type==="document"||n.type==="block-map"||n.type==="block-seq")&&(t.type==="block-map"||t.type==="block-seq")){let r=t.items[t.items.length-1];r&&!r.sep&&!r.value&&r.start.length>0&&Qu(r.start)===-1&&(t.indent===0||r.start.every(i=>i.type!=="comment"||i.indent<t.indent))&&(n.type==="document"?n.end=r.start:n.items.push({start:r.start}),t.items.splice(-1,1))}}}*stream(){switch(this.type){case"directive-line":yield{type:"directive",offset:this.offset,source:this.source};return;case"byte-order-mark":case"space":case"comment":case"newline":yield this.sourceToken;return;case"doc-mode":case"doc-start":{let e={type:"document",offset:this.offset,start:[]};this.type==="doc-start"&&e.start.push(this.sourceToken),this.stack.push(e);return}}yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML stream`,source:this.source}}*document(e){if(e.value)return yield*this.lineEnd(e);switch(this.type){case"doc-start":{Qu(e.start)!==-1?(yield*this.pop(),yield*this.step()):e.start.push(this.sourceToken);return}case"anchor":case"tag":case"space":case"comment":case"newline":e.start.push(this.sourceToken);return}let t=this.startBlockValue(e);t?this.stack.push(t):yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML document`,source:this.source}}*scalar(e){if(this.type==="map-value-ind"){let t=yr(this.peek(2)),n=_t(t),r;e.end?(r=e.end,r.push(this.sourceToken),delete e.end):r=[this.sourceToken];let i={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:n,key:e,sep:r}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=i}else yield*this.lineEnd(e)}*blockScalar(e){switch(this.type){case"space":case"comment":case"newline":e.props.push(this.sourceToken);return;case"scalar":if(e.source=this.source,this.atNewLine=!0,this.indent=0,this.onNewLine){let t=this.source.indexOf(`
`)+1;for(;t!==0;)this.onNewLine(this.offset+t),t=this.source.indexOf(`
`,t)+1}yield*this.pop();break;default:yield*this.pop(),yield*this.step()}}*blockMap(e){let t=e.items[e.items.length-1];switch(this.type){case"newline":if(this.onKeyLine=!1,t.value){let n="end"in t.value?t.value.end:void 0;(Array.isArray(n)?n[n.length-1]:void 0)?.type==="comment"?n?.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"space":case"comment":if(t.value)e.items.push({start:[this.sourceToken]});else if(t.sep)t.sep.push(this.sourceToken);else{if(this.atIndentedComment(t.start,e.indent)){let r=e.items[e.items.length-2]?.value?.end;if(Array.isArray(r)){Array.prototype.push.apply(r,t.start),r.push(this.sourceToken),e.items.pop();return}}t.start.push(this.sourceToken)}return}if(this.indent>=e.indent){let n=!this.onKeyLine&&this.indent===e.indent,r=n&&(t.sep||t.explicitKey)&&this.type!=="seq-item-ind",i=[];if(r&&t.sep&&!t.value){let o=[];for(let a=0;a<t.sep.length;++a){let l=t.sep[a];switch(l.type){case"newline":o.push(a);break;case"space":break;case"comment":l.indent>e.indent&&(o.length=0);break;default:o.length=0}}o.length>=2&&(i=t.sep.splice(o[1]))}switch(this.type){case"anchor":case"tag":r||t.value?(i.push(this.sourceToken),e.items.push({start:i}),this.onKeyLine=!0):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"explicit-key-ind":!t.sep&&!t.explicitKey?(t.start.push(this.sourceToken),t.explicitKey=!0):r||t.value?(i.push(this.sourceToken),e.items.push({start:i,explicitKey:!0})):this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken],explicitKey:!0}]}),this.onKeyLine=!0;return;case"map-value-ind":if(t.explicitKey)if(t.sep)if(t.value)e.items.push({start:[],key:null,sep:[this.sourceToken]});else if(dt(t.sep,"map-value-ind"))this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:i,key:null,sep:[this.sourceToken]}]});else if(ef(t.key)&&!dt(t.sep,"newline")){let o=_t(t.start),a=t.key,l=t.sep;l.push(this.sourceToken),delete t.key,delete t.sep,this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:o,key:a,sep:l}]})}else i.length>0?t.sep=t.sep.concat(i,this.sourceToken):t.sep.push(this.sourceToken);else if(dt(t.start,"newline"))Object.assign(t,{key:null,sep:[this.sourceToken]});else{let o=_t(t.start);this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:o,key:null,sep:[this.sourceToken]}]})}else t.sep?t.value||r?e.items.push({start:i,key:null,sep:[this.sourceToken]}):dt(t.sep,"map-value-ind")?this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[],key:null,sep:[this.sourceToken]}]}):t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]});this.onKeyLine=!0;return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{let o=this.flowScalar(this.type);r||t.value?(e.items.push({start:i,key:o,sep:[]}),this.onKeyLine=!0):t.sep?this.stack.push(o):(Object.assign(t,{key:o,sep:[]}),this.onKeyLine=!0);return}default:{let o=this.startBlockValue(e);if(o){n&&o.type!=="block-seq"&&e.items.push({start:i}),this.stack.push(o);return}}}}yield*this.pop(),yield*this.step()}*blockSequence(e){let t=e.items[e.items.length-1];switch(this.type){case"newline":if(t.value){let n="end"in t.value?t.value.end:void 0;(Array.isArray(n)?n[n.length-1]:void 0)?.type==="comment"?n?.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else t.start.push(this.sourceToken);return;case"space":case"comment":if(t.value)e.items.push({start:[this.sourceToken]});else{if(this.atIndentedComment(t.start,e.indent)){let r=e.items[e.items.length-2]?.value?.end;if(Array.isArray(r)){Array.prototype.push.apply(r,t.start),r.push(this.sourceToken),e.items.pop();return}}t.start.push(this.sourceToken)}return;case"anchor":case"tag":if(t.value||this.indent<=e.indent)break;t.start.push(this.sourceToken);return;case"seq-item-ind":if(this.indent!==e.indent)break;t.value||dt(t.start,"seq-item-ind")?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken);return}if(this.indent>e.indent){let n=this.startBlockValue(e);if(n){this.stack.push(n);return}}yield*this.pop(),yield*this.step()}*flowCollection(e){let t=e.items[e.items.length-1];if(this.type==="flow-error-end"){let n;do yield*this.pop(),n=this.peek(1);while(n&&n.type==="flow-collection")}else if(e.end.length===0){switch(this.type){case"comma":case"explicit-key-ind":!t||t.sep?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken);return;case"map-value-ind":!t||t.value?e.items.push({start:[],key:null,sep:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]});return;case"space":case"comment":case"newline":case"anchor":case"tag":!t||t.value?e.items.push({start:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{let r=this.flowScalar(this.type);!t||t.value?e.items.push({start:[],key:r,sep:[]}):t.sep?this.stack.push(r):Object.assign(t,{key:r,sep:[]});return}case"flow-map-end":case"flow-seq-end":e.end.push(this.sourceToken);return}let n=this.startBlockValue(e);n?this.stack.push(n):(yield*this.pop(),yield*this.step())}else{let n=this.peek(2);if(n.type==="block-map"&&(this.type==="map-value-ind"&&n.indent===e.indent||this.type==="newline"&&!n.items[n.items.length-1].sep))yield*this.pop(),yield*this.step();else if(this.type==="map-value-ind"&&n.type!=="flow-collection"){let r=yr(n),i=_t(r);Xu(e);let o=e.end.splice(1,e.end.length);o.push(this.sourceToken);let a={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:i,key:e,sep:o}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=a}else yield*this.lineEnd(e)}}flowScalar(e){if(this.onNewLine){let t=this.source.indexOf(`
`)+1;for(;t!==0;)this.onNewLine(this.offset+t),t=this.source.indexOf(`
`,t)+1}return{type:e,offset:this.offset,indent:this.indent,source:this.source}}startBlockValue(e){switch(this.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return this.flowScalar(this.type);case"block-scalar-header":return{type:"block-scalar",offset:this.offset,indent:this.indent,props:[this.sourceToken],source:""};case"flow-map-start":case"flow-seq-start":return{type:"flow-collection",offset:this.offset,indent:this.indent,start:this.sourceToken,items:[],end:[]};case"seq-item-ind":return{type:"block-seq",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken]}]};case"explicit-key-ind":{this.onKeyLine=!0;let t=yr(e),n=_t(t);return n.push(this.sourceToken),{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:n,explicitKey:!0}]}}case"map-value-ind":{this.onKeyLine=!0;let t=yr(e),n=_t(t);return{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:n,key:null,sep:[this.sourceToken]}]}}}return null}atIndentedComment(e,t){return this.type!=="comment"||this.indent<=t?!1:e.every(n=>n.type==="newline"||n.type==="space")}*documentEnd(e){this.type!=="doc-mode"&&(e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop()))}*lineEnd(e){switch(this.type){case"comma":case"doc-start":case"doc-end":case"flow-seq-end":case"flow-map-end":case"map-value-ind":yield*this.pop(),yield*this.step();break;case"newline":this.onKeyLine=!1;case"space":case"comment":default:e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop())}}};tf.Parser=Vo});var af=S(Ls=>{"use strict";var sf=Oo(),$y=Ns(),Ms=As(),Ry=Ei(),Vy=L(),By=Ro(),nf=Bo();function rf(s){let e=s.prettyErrors!==!1;return{lineCounter:s.lineCounter||e&&new By.LineCounter||null,prettyErrors:e}}function Uy(s,e={}){let{lineCounter:t,prettyErrors:n}=rf(e),r=new nf.Parser(t?.addNewLine),i=new sf.Composer(e),o=Array.from(i.compose(r.parse(s)));if(n&&t)for(let a of o)a.errors.forEach(Ms.prettifyError(s,t)),a.warnings.forEach(Ms.prettifyError(s,t));return o.length>0?o:Object.assign([],{empty:!0},i.streamInfo())}function of(s,e={}){let{lineCounter:t,prettyErrors:n}=rf(e),r=new nf.Parser(t?.addNewLine),i=new sf.Composer(e),o=null;for(let a of i.compose(r.parse(s),!0,s.length))if(!o)o=a;else if(o.options.logLevel!=="silent"){o.errors.push(new Ms.YAMLParseError(a.range.slice(0,2),"MULTIPLE_DOCS","Source contains multiple documents; please use YAML.parseAllDocuments()"));break}return n&&t&&(o.errors.forEach(Ms.prettifyError(s,t)),o.warnings.forEach(Ms.prettifyError(s,t))),o}function Wy(s,e,t){let n;typeof e=="function"?n=e:t===void 0&&e&&typeof e=="object"&&(t=e);let r=of(s,t);if(!r)return null;if(r.warnings.forEach(i=>Ry.warn(r.options.logLevel,i)),r.errors.length>0){if(r.options.logLevel!=="silent")throw r.errors[0];r.errors=[]}return r.toJS(Object.assign({reviver:n},t))}function Yy(s,e,t){let n=null;if(typeof e=="function"||Array.isArray(e)?n=e:t===void 0&&e&&(t=e),typeof t=="string"&&(t=t.length),typeof t=="number"){let r=Math.round(t);t=r<1?void 0:r>8?{indent:8}:{indent:r}}if(s===void 0){let{keepUndefined:r}=t??e??{};if(!r)return}return Vy.isDocument(s)&&!n?s.toString(t):new $y.Document(s,n,t).toString(t)}Ls.parse=Wy;Ls.parseAllDocuments=Uy;Ls.parseDocument=of;Ls.stringify=Yy});var cf=S(P=>{"use strict";var Hy=Oo(),Ky=Ns(),jy=oo(),Uo=As(),Zy=os(),Je=L(),Jy=He(),Gy=U(),zy=je(),Qy=Ze(),Xy=pr(),e0=Po(),t0=Ro(),s0=Bo(),Sr=af(),lf=ss();P.Composer=Hy.Composer;P.Document=Ky.Document;P.Schema=jy.Schema;P.YAMLError=Uo.YAMLError;P.YAMLParseError=Uo.YAMLParseError;P.YAMLWarning=Uo.YAMLWarning;P.Alias=Zy.Alias;P.isAlias=Je.isAlias;P.isCollection=Je.isCollection;P.isDocument=Je.isDocument;P.isMap=Je.isMap;P.isNode=Je.isNode;P.isPair=Je.isPair;P.isScalar=Je.isScalar;P.isSeq=Je.isSeq;P.Pair=Jy.Pair;P.Scalar=Gy.Scalar;P.YAMLMap=zy.YAMLMap;P.YAMLSeq=Qy.YAMLSeq;P.CST=Xy;P.Lexer=e0.Lexer;P.LineCounter=t0.LineCounter;P.Parser=s0.Parser;P.parse=Sr.parse;P.parseAllDocuments=Sr.parseAllDocuments;P.parseDocument=Sr.parseDocument;P.stringify=Sr.stringify;P.visit=lf.visit;P.visitAsync=lf.visitAsync});var xw={};Dd(xw,{default:()=>Nd});module.exports=Cd(xw);var bd=require("@raycast/api");var Xr=se(require("react"));var Tr=require("@raycast/api"),oe=require("react");var oi=se(require("fs"));var al=1e3,si=1024,ni=si**2,qw=ni**2;var ri=/(#[a-zA-Z_0-9/-]+)/g,ll=/---\s([\s\S]*)---/g,cl=/\$\$(.|\n)*?\$\$/gm,ul=/\$(.|\n)*?\$/gm,kn=/```(.*)\n([\s\S]*?)```/gm,fl={0:"Sun",1:"Mon",2:"Tue",3:"Wed",4:"Thu",5:"Fri",6:"Sat"},hl={0:"Jan",1:"Feb",2:"Mar",3:"Apr",4:"May",5:"Jun",6:"Jul",7:"Aug",8:"Sep",9:"Oct",10:"Nov",11:"Dec"};var ii={source:"obsidian_icon.svg",tintColor:{dark:"#E6E6E6",light:"#262626",adjustContrast:!1}};var dl=require("@raycast/api");function Tn(s,e){let t=s,n=e;return t>n?1:t<n?-1:0}function ai(s){return s.split(" ").length}function ml(s){return Math.ceil(ai(s)/200)}function pl(s){let{birthtime:e}=oi.default.statSync(s.path);return e}function gl(s){let{size:e}=oi.default.statSync(s.path);return e/si}function yl(s,e){return s.length>e?"..."+s.slice(s.length-e).slice(1):s.slice(1)}async function Sl(s){let e=new Date(s.getTime()),t=(s.getDay()+6)%7;e.setDate(e.getDate()-t+3);let n=e.getTime();return e.setMonth(0,1),e.getDay()!==4&&e.setMonth(0,1+(4-e.getDay()+7)%7),1+Math.ceil((n-e.getTime())/6048e5)}async function wl(){let s;try{s=await(0,dl.getSelectedText)()}catch(e){console.warn("Could not get selected text",e)}return s}function vt(s){switch(s.type){case"obsidian://open?vault=":return"obsidian://open?vault="+encodeURIComponent(s.vault.name);case"obsidian://open?path=":return"obsidian://open?path="+encodeURIComponent(s.path);case"obsidian://advanced-uri?daily=true&vault=":return"obsidian://advanced-uri?daily=true&vault="+encodeURIComponent(s.vault.name);case"obsidian://advanced-uri?daily=true":{let e=s.heading?"&heading="+encodeURIComponent(s.heading):"";return"obsidian://advanced-uri?daily=true"+(s.prepend?"&mode=prepend":"&mode=append")+"&data="+encodeURIComponent(s.text)+"&vault="+encodeURIComponent(s.vault.name)+e+(s.silent?"&openmode=silent":"")}case"obsidian://new?vault=":return"obsidian://new?vault="+encodeURIComponent(s.vault.name)+"&name="+encodeURIComponent(s.name)+"&content="+encodeURIComponent(s.content||"");case"obsidian://advanced-uri?mode=append&filepath=":{let e=s.heading?"&heading="+encodeURIComponent(s.heading):"";return"obsidian://advanced-uri?mode=append&filepath="+encodeURIComponent(s.path)+"&data="+encodeURIComponent(s.text)+"&vault="+encodeURIComponent(s.vault.name)+e+(s.silent?"&openmode=silent":"")}default:return""}}var mt=require("@raycast/api"),ke=se(require("fs")),kf=require("fs/promises"),Tf=require("os"),we=se(require("path")),Jo=require("perf_hooks");var uf=se(cf());function ff(s){let e=s.match(ll);if(e)try{return uf.default.parse(e[0].replaceAll("---",""),{logLevel:"error"})}catch{}}function Wo(s,e){return!!(Object.prototype.hasOwnProperty.call(s,e)&&s[e])}function hf(s,e){let t=ff(s);if(t&&Wo(t,e))return t[e]}function n0(s){let e=[];for(let t of s){let r=[...t.content.replaceAll(kn,"").matchAll(ri)];for(let i of r)e.includes(i[1])||e.push(i[1])}return e}function r0(s){let e=[];for(let t of s){let n=df(t.content);for(let r of n)e.includes(r)||e.push(r)}return e}function wr(s){let e=n0(s),t=r0(s);for(let n of t)e.includes(n)||e.push(n);return e.sort(Tn)}function i0(s){let e=[],t=[...s.matchAll(ri)];for(let n of t)e.includes(n[1])||e.push(n[1]);return e}function df(s){let e=[],t=ff(s);return t&&(Wo(t,"tag")?Array.isArray(t.tag)?e=[...t.tag]:typeof t.tag=="string"&&(e=[...t.tag.split(",").map(n=>n.trim())]):Wo(t,"tags")&&(Array.isArray(t.tags)?e=[...t.tags]:typeof t.tags=="string"&&(e=[...t.tags.split(",").map(n=>n.trim())]))),e=e.filter(n=>n!=""),e.map(n=>"#"+n)}function mf(s){let e=i0(s),t=df(s);for(let n of t)e.includes(n)||e.push(n);return e.sort(Tn)}var Ho=require("@raycast/api"),kr=se(require("fs")),Ko=se(require("path"));var Ge=class{constructor(e){this.name=e||"Logger"}timestamp(){return new Date().toISOString()}formatMessage(e){if(typeof e=="string")return e;if(e instanceof Error)return`${e.message}
${e.stack}`;if(typeof e=="object"&&e!==null)try{return JSON.stringify(e,null,2)}catch{return String(e)}return String(e)}info(e){console.log(`[${this.timestamp()}] [${this.name}] [INFO] ${this.formatMessage(e)}`)}success(e){console.log(`[${this.timestamp()}] [${this.name}] [SUCCESS] ${this.formatMessage(e)}`)}warning(e){console.log(`[${this.timestamp()}] [${this.name}] [WARNING] ${this.formatMessage(e)}`)}error(e){console.log(`[${this.timestamp()}] [${this.name}] [ERROR] ${this.formatMessage(e)}`)}debug(e){console.log(`[${this.timestamp()}] [${this.name}] [DEBUG] ${this.formatMessage(e)}`)}trace(e){console.log(`[${this.timestamp()}] [${this.name}] [TRACE] ${this.formatMessage(e)}`)}};var ze=new Ge("Bookmarks");function*pf(s){for(let e of s)e.type==="file"&&(yield e),e.type==="group"&&e.items&&(yield*pf(e.items))}function jo(s){let{configFileName:e}=(0,Ho.getPreferenceValues)(),t=`${s.path}/${e||".obsidian"}/bookmarks.json`;if(!kr.default.existsSync(t)){ze.warning("No bookmarks JSON found");return}let n=kr.default.readFileSync(t,"utf-8"),r=JSON.parse(n);return ze.info(r),r}function Yo(s,e){let{configFileName:t}=(0,Ho.getPreferenceValues)(),n=`${s.path}/${t||".obsidian"}/bookmarks.json`;kr.default.writeFileSync(n,JSON.stringify(e,null,2))}function gf(s){let e=jo(s);return e?Array.from(pf(e.items)):[]}function yf(s){let t=gf(s).map(n=>n.path);return ze.info(t),t}function Sf(s,e){let t=jo(s),n=Ko.default.relative(s.path,e.path);if(gf(s).some(o=>o.path===n)){ze.info(`Note ${e.title} is already bookmarked`);return}let i={type:"file",title:e.title,path:n};if(!t){Yo(s,{items:[i]});return}t.items.push(i),Yo(s,t),ze.info(`Bookmarked note: ${e.title}`)}function wf(s,e){let t=jo(s);if(!t){ze.warning("No bookmarks JSON found, can't unbookmark note.");return}let n=Ko.default.relative(s.path,e.path),r=!1,i=o=>{for(let a=0;a<o.length;a++){let l=o[a];if(l.type==="file"&&l.path===n)return o.splice(a,1),!0;if(l.type==="group"&&l.items&&i(l.items))return!0}return!1};r=i(t.items),r?(Yo(s,t),ze.info(`Removed bookmark for note: ${e.title}`)):ze.warning(`Note not found in bookmarks: ${e.title}`)}function bf(s){let e=s.split(we.default.sep).filter(t=>{if(t!="")return t}).pop();return e||"Default Vault Name (check your path preferences)"}function Go(){return(0,mt.getPreferenceValues)().vaultPath.split(",").filter(t=>t.trim()!=="").filter(t=>ke.existsSync(t)).map(t=>({name:bf(t.trim()),key:t.trim(),path:t.trim()}))}async function Nf(){let s=we.default.resolve(`${(0,Tf.homedir)()}/Library/Application Support/obsidian/obsidian.json`);try{let e=JSON.parse(await(0,kf.readFile)(s,"utf8"));return Object.values(e.vaults).map(({path:t})=>({name:bf(t),key:t,path:t}))}catch{return[]}}function Zo(s,e){let t=we.default.normalize(s);return e.some(n=>{if(!n)return!1;let r=we.default.normalize(n);return t===r||t.startsWith(r+we.default.sep)})}var o0=[".git",".obsidian",".trash",".excalidraw",".mobile"];function Ef(s,e,t,n){let r=ke.readdirSync(s),{configFileName:i}=(0,mt.getPreferenceValues)();for(let o of r){let a=we.default.join(s,o);if(ke.statSync(a).isDirectory()){if(o===i||o0.includes(o)||Zo(a,e))continue;Ef(a,e,t,n)}else{let c=we.default.extname(o);t.includes(c)&&o!==".md"&&!o.includes(".excalidraw")&&!Zo(s,[".obsidian",i])&&!Zo(s,e)&&n.push(a)}}return n}function a0(){let e=(0,mt.getPreferenceValues)().excludedFolders;return e?e.split(",").map(n=>n.trim()):[]}function l0(s){let e=a0(),t=c0(s);return e.push(...t),Ef(s.path,e,[".md"],[])}function c0(s){let{configFileName:e}=(0,mt.getPreferenceValues)(),t=`${s.path}/${e||".obsidian"}/app.json`;return ke.existsSync(t)?JSON.parse(ke.readFileSync(t,"utf-8")).userIgnoreFilters||[]:[]}function Fs(s){let e=(0,mt.getPreferenceValues)();if(e.removeYAML){let t=s.match(/---(.|\n)*?---/gm);t&&(s=s.replace(t[0],""))}if(e.removeLatex){let t=s.matchAll(cl);for(let r of t)s=s.replace(r[0],"");let n=s.matchAll(ul);for(let r of n)s=s.replace(r[0],"")}return e.removeLinks&&(s=s.replaceAll("![[",""),s=s.replaceAll("[[",""),s=s.replaceAll("]]","")),s}function zo(s,e=!1){let t="";return t=ke.readFileSync(s,"utf8"),e?Fs(t):t}function vf(s){console.log("Loading Notes for vault: "+s.path);let e=Jo.performance.now(),t=[],n=l0(s),r=yf(s);for(let o of n){let l=we.default.basename(o).replace(/\.md$/,"")||"default",c=zo(o,!1),u=we.default.relative(s.path,o),f={title:l,path:o,lastModified:ke.statSync(o).mtime,tags:mf(c),content:c,bookmarked:r.includes(u)};t.push(f)}let i=Jo.performance.now();return console.log(`Finished loading ${t.length} notes in ${i-e} ms.`),t.sort((o,a)=>a.lastModified.getTime()-o.lastModified.getTime())}var Af=require("@raycast/api");var u0=new Ge("Cache"),pt=new Af.Cache({capacity:ni*500});function Of(s){let e=vf(s);return pt.set(s.name,JSON.stringify({lastCached:Date.now(),notes:e})),e}function qt(s){console.log("Renew Cache"),Of(s)}function Qo(s){if(pt.has(s.name))return!0;console.log("Cache does not exist for vault: "+s.name)}function xs(s,e){if(Qo(s)){let t=JSON.parse(pt.get(s.name)??"{}");t.notes=t.notes.map(n=>n.path===e.path?e:n),pt.set(s.name,JSON.stringify(t))}}function If(s,e){if(Qo(s)){let t=JSON.parse(pt.get(s.name)??"{}");t.notes=t.notes.filter(n=>n.path!==e.path),pt.set(s.name,JSON.stringify(t))}}function Df(s){if(Qo(s)){let e=JSON.parse(pt.get(s.name)??"{}");if(e.notes?.length>0&&e.lastCached>Date.now()-1e3*60*5){let t=e.notes;return u0.info("Using cached notes."),t}}return Of(s)}var Cf=new Ge("Hooks"),Xo=(0,oe.createContext)([]),ea=(0,oe.createContext)(()=>{});function Mf(s,e=!1){let t=Df(s),[n]=(0,oe.useState)(t);return Cf.info("useNotes hook called"),e?[n.filter(r=>r.bookmarked)]:[n]}function br(){return(0,oe.useContext)(Xo)}function De(){return(0,oe.useContext)(ea)}function Lf(){let s=(0,oe.useMemo)(()=>(0,Tr.getPreferenceValues)(),[]),[e,t]=(0,oe.useState)(s.vaultPath?{ready:!0,vaults:Go()}:{ready:!1,vaults:[]});return Cf.info("useObsidianVaults hook called"),(0,oe.useEffect)(()=>{e.ready||Nf().then(n=>{t({vaults:n,ready:!0})}).catch(()=>t({vaults:Go(),ready:!0}))},[]),e}var pe=require("@raycast/api"),zr=require("react");var Q=require("@raycast/api"),ta=se(require("react")),Ff=se(require("fs"));var W=require("react/jsx-runtime");function xf(s){let{note:e,vault:t,pref:n,action:r}=s,i=!Ff.default.existsSync(e.path);i&&qt(t);function o(){return e.tags.length>0?(0,W.jsx)(Q.List.Item.Detail.Metadata.TagList,{title:"Tags",children:e.tags.map(l=>(0,W.jsx)(Q.List.Item.Detail.Metadata.TagList.Item,{text:l},l))}):null}function a(){let l=hf(e.content,"url");return l?(0,W.jsx)(Q.List.Item.Detail.Metadata.Link,{target:l,text:"View",title:"URL"}):null}return i?null:(0,W.jsx)(Q.List.Item,{title:e.title,accessories:[{icon:e.bookmarked?{source:"bookmark.svg"}:null}],detail:(0,W.jsx)(Q.List.Item.Detail,{markdown:Fs(e.content),metadata:n.showMetadata?(0,W.jsxs)(Q.List.Item.Detail.Metadata,{children:[(0,W.jsx)(Q.List.Item.Detail.Metadata.Label,{title:"Character Count",text:e.content.length.toString()}),(0,W.jsx)(Q.List.Item.Detail.Metadata.Label,{title:"Word Count",text:ai(e.content).toString()}),(0,W.jsx)(Q.List.Item.Detail.Metadata.Label,{title:"Reading Time",text:ml(e.content).toString()+" min read"}),(0,W.jsx)(o,{}),(0,W.jsx)(a,{}),(0,W.jsx)(Q.List.Item.Detail.Metadata.Separator,{}),(0,W.jsx)(Q.List.Item.Detail.Metadata.Label,{title:"Creation Date",text:pl(e).toLocaleDateString()}),(0,W.jsx)(Q.List.Item.Detail.Metadata.Label,{title:"File Size",text:gl(e).toFixed(2)+" KB"}),(0,W.jsx)(Q.List.Item.Detail.Metadata.Label,{title:"Note Path",text:yl(e.path.split(t.path)[1],55)})]}):(0,W.jsx)(ta.default.Fragment,{})}),actions:(0,W.jsx)(Q.ActionPanel,{children:(0,W.jsx)(ta.default.Fragment,{children:r&&r(e,t)})})})}var hn=require("@raycast/api"),Yr=require("react");var G=require("@raycast/api"),Wr=se(require("fs"));var Nr=require("@raycast/api");function _f(){(0,Nr.showToast)({title:"Path Error",message:"Something went wrong with your vault path. There are no paths to select from.",style:Nr.Toast.Style.Failure})}var xe=class extends Error{},Er=class extends xe{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}},vr=class extends xe{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}},Ar=class extends xe{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}},Te=class extends xe{},Pt=class extends xe{constructor(e){super(`Invalid unit ${e}`)}},B=class extends xe{},be=class extends xe{constructor(){super("Zone is an abstract class")}};var y="numeric",Ne="short",ae="long",Qe={year:y,month:y,day:y},_s={year:y,month:Ne,day:y},sa={year:y,month:Ne,day:y,weekday:Ne},qs={year:y,month:ae,day:y},Ps={year:y,month:ae,day:y,weekday:ae},$s={hour:y,minute:y},Rs={hour:y,minute:y,second:y},Vs={hour:y,minute:y,second:y,timeZoneName:Ne},Bs={hour:y,minute:y,second:y,timeZoneName:ae},Us={hour:y,minute:y,hourCycle:"h23"},Ws={hour:y,minute:y,second:y,hourCycle:"h23"},Ys={hour:y,minute:y,second:y,hourCycle:"h23",timeZoneName:Ne},Hs={hour:y,minute:y,second:y,hourCycle:"h23",timeZoneName:ae},Ks={year:y,month:y,day:y,hour:y,minute:y},js={year:y,month:y,day:y,hour:y,minute:y,second:y},Zs={year:y,month:Ne,day:y,hour:y,minute:y},Js={year:y,month:Ne,day:y,hour:y,minute:y,second:y},na={year:y,month:Ne,day:y,weekday:Ne,hour:y,minute:y},Gs={year:y,month:ae,day:y,hour:y,minute:y,timeZoneName:Ne},zs={year:y,month:ae,day:y,hour:y,minute:y,second:y,timeZoneName:Ne},Qs={year:y,month:ae,day:y,weekday:ae,hour:y,minute:y,timeZoneName:ae},Xs={year:y,month:ae,day:y,weekday:ae,hour:y,minute:y,second:y,timeZoneName:ae};var ie=class{get type(){throw new be}get name(){throw new be}get ianaName(){return this.name}get isUniversal(){throw new be}offsetName(e,t){throw new be}formatOffset(e,t){throw new be}offset(e){throw new be}equals(e){throw new be}get isValid(){throw new be}};var ra=null,Xe=class s extends ie{static get instance(){return ra===null&&(ra=new s),ra}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:t,locale:n}){return Ir(e,t,n)}formatOffset(e,t){return et(this.offset(e),t)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return e.type==="system"}get isValid(){return!0}};var Cr={};function f0(s){return Cr[s]||(Cr[s]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:s,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),Cr[s]}var h0={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function d0(s,e){let t=s.format(e).replace(/\u200E/g,""),n=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(t),[,r,i,o,a,l,c,u]=n;return[o,r,i,a,l,c,u]}function m0(s,e){let t=s.formatToParts(e),n=[];for(let r=0;r<t.length;r++){let{type:i,value:o}=t[r],a=h0[i];i==="era"?n[a]=o:A(a)||(n[a]=parseInt(o,10))}return n}var Dr={},X=class s extends ie{static create(e){return Dr[e]||(Dr[e]=new s(e)),Dr[e]}static resetCache(){Dr={},Cr={}}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch{return!1}}constructor(e){super(),this.zoneName=e,this.valid=s.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:t,locale:n}){return Ir(e,t,n,this.name)}formatOffset(e,t){return et(this.offset(e),t)}offset(e){let t=new Date(e);if(isNaN(t))return NaN;let n=f0(this.name),[r,i,o,a,l,c,u]=n.formatToParts?m0(n,t):d0(n,t);a==="BC"&&(r=-Math.abs(r)+1);let h=$t({year:r,month:i,day:o,hour:l===24?0:l,minute:c,second:u,millisecond:0}),d=+t,g=d%1e3;return d-=g>=0?g:1e3+g,(h-d)/(60*1e3)}equals(e){return e.type==="iana"&&e.name===this.name}get isValid(){return this.valid}};var qf={};function p0(s,e={}){let t=JSON.stringify([s,e]),n=qf[t];return n||(n=new Intl.ListFormat(s,e),qf[t]=n),n}var ia={};function oa(s,e={}){let t=JSON.stringify([s,e]),n=ia[t];return n||(n=new Intl.DateTimeFormat(s,e),ia[t]=n),n}var aa={};function g0(s,e={}){let t=JSON.stringify([s,e]),n=aa[t];return n||(n=new Intl.NumberFormat(s,e),aa[t]=n),n}var la={};function y0(s,e={}){let{base:t,...n}=e,r=JSON.stringify([s,n]),i=la[r];return i||(i=new Intl.RelativeTimeFormat(s,e),la[r]=i),i}var en=null;function S0(){return en||(en=new Intl.DateTimeFormat().resolvedOptions().locale,en)}var Pf={};function w0(s){let e=Pf[s];if(!e){let t=new Intl.Locale(s);e="getWeekInfo"in t?t.getWeekInfo():t.weekInfo,Pf[s]=e}return e}function k0(s){let e=s.indexOf("-x-");e!==-1&&(s=s.substring(0,e));let t=s.indexOf("-u-");if(t===-1)return[s];{let n,r;try{n=oa(s).resolvedOptions(),r=s}catch{let l=s.substring(0,t);n=oa(l).resolvedOptions(),r=l}let{numberingSystem:i,calendar:o}=n;return[r,i,o]}}function T0(s,e,t){return(t||e)&&(s.includes("-u-")||(s+="-u"),t&&(s+=`-ca-${t}`),e&&(s+=`-nu-${e}`)),s}function b0(s){let e=[];for(let t=1;t<=12;t++){let n=F.utc(2009,t,1);e.push(s(n))}return e}function N0(s){let e=[];for(let t=1;t<=7;t++){let n=F.utc(2016,11,13+t);e.push(s(n))}return e}function Mr(s,e,t,n){let r=s.listingMode();return r==="error"?null:r==="en"?t(e):n(e)}function E0(s){return s.numberingSystem&&s.numberingSystem!=="latn"?!1:s.numberingSystem==="latn"||!s.locale||s.locale.startsWith("en")||new Intl.DateTimeFormat(s.intl).resolvedOptions().numberingSystem==="latn"}var ca=class{constructor(e,t,n){this.padTo=n.padTo||0,this.floor=n.floor||!1;let{padTo:r,floor:i,...o}=n;if(!t||Object.keys(o).length>0){let a={useGrouping:!1,...n};n.padTo>0&&(a.minimumIntegerDigits=n.padTo),this.inf=g0(e,a)}}format(e){if(this.inf){let t=this.floor?Math.floor(e):e;return this.inf.format(t)}else{let t=this.floor?Math.floor(e):Rt(e,3);return R(t,this.padTo)}}},ua=class{constructor(e,t,n){this.opts=n,this.originalZone=void 0;let r;if(this.opts.timeZone)this.dt=e;else if(e.zone.type==="fixed"){let o=-1*(e.offset/60),a=o>=0?`Etc/GMT+${o}`:`Etc/GMT${o}`;e.offset!==0&&X.create(a).valid?(r=a,this.dt=e):(r="UTC",this.dt=e.offset===0?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)}else e.zone.type==="system"?this.dt=e:e.zone.type==="iana"?(this.dt=e,r=e.zone.name):(r="UTC",this.dt=e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone);let i={...this.opts};i.timeZone=i.timeZone||r,this.dtf=oa(t,i)}format(){return this.originalZone?this.formatToParts().map(({value:e})=>e).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){let e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map(t=>{if(t.type==="timeZoneName"){let n=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...t,value:n}}else return t}):e}resolvedOptions(){return this.dtf.resolvedOptions()}},fa=class{constructor(e,t,n){this.opts={style:"long",...n},!t&&Lr()&&(this.rtf=y0(e,n))}format(e,t){return this.rtf?this.rtf.format(e,t):$f(t,e,this.opts.numeric,this.opts.style!=="long")}formatToParts(e,t){return this.rtf?this.rtf.formatToParts(e,t):[]}},v0={firstDay:1,minimalDays:4,weekend:[6,7]},_=class s{static fromOpts(e){return s.create(e.locale,e.numberingSystem,e.outputCalendar,e.weekSettings,e.defaultToEN)}static create(e,t,n,r,i=!1){let o=e||x.defaultLocale,a=o||(i?"en-US":S0()),l=t||x.defaultNumberingSystem,c=n||x.defaultOutputCalendar,u=tn(r)||x.defaultWeekSettings;return new s(a,l,c,u,o)}static resetCache(){en=null,ia={},aa={},la={}}static fromObject({locale:e,numberingSystem:t,outputCalendar:n,weekSettings:r}={}){return s.create(e,t,n,r)}constructor(e,t,n,r,i){let[o,a,l]=k0(e);this.locale=o,this.numberingSystem=t||a||null,this.outputCalendar=n||l||null,this.weekSettings=r,this.intl=T0(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=i,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=E0(this)),this.fastNumbersCached}listingMode(){let e=this.isEnglish(),t=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return e&&t?"en":"intl"}clone(e){return!e||Object.getOwnPropertyNames(e).length===0?this:s.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,tn(e.weekSettings)||this.weekSettings,e.defaultToEN||!1)}redefaultToEN(e={}){return this.clone({...e,defaultToEN:!0})}redefaultToSystem(e={}){return this.clone({...e,defaultToEN:!1})}months(e,t=!1){return Mr(this,e,ha,()=>{let n=t?{month:e,day:"numeric"}:{month:e},r=t?"format":"standalone";return this.monthsCache[r][e]||(this.monthsCache[r][e]=b0(i=>this.extract(i,n,"month"))),this.monthsCache[r][e]})}weekdays(e,t=!1){return Mr(this,e,da,()=>{let n=t?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},r=t?"format":"standalone";return this.weekdaysCache[r][e]||(this.weekdaysCache[r][e]=N0(i=>this.extract(i,n,"weekday"))),this.weekdaysCache[r][e]})}meridiems(){return Mr(this,void 0,()=>ma,()=>{if(!this.meridiemCache){let e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[F.utc(2016,11,13,9),F.utc(2016,11,13,19)].map(t=>this.extract(t,e,"dayperiod"))}return this.meridiemCache})}eras(e){return Mr(this,e,pa,()=>{let t={era:e};return this.eraCache[e]||(this.eraCache[e]=[F.utc(-40,1,1),F.utc(2017,1,1)].map(n=>this.extract(n,t,"era"))),this.eraCache[e]})}extract(e,t,n){let r=this.dtFormatter(e,t),i=r.formatToParts(),o=i.find(a=>a.type.toLowerCase()===n);return o?o.value:null}numberFormatter(e={}){return new ca(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,t={}){return new ua(e,this.intl,t)}relFormatter(e={}){return new fa(this.intl,this.isEnglish(),e)}listFormatter(e={}){return p0(this.intl,e)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}getWeekSettings(){return this.weekSettings?this.weekSettings:Fr()?w0(this.locale):v0}getStartOfWeek(){return this.getWeekSettings().firstDay}getMinDaysInFirstWeek(){return this.getWeekSettings().minimalDays}getWeekendDays(){return this.getWeekSettings().weekend}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}toString(){return`Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`}};var ya=null,H=class s extends ie{static get utcInstance(){return ya===null&&(ya=new s(0)),ya}static instance(e){return e===0?s.utcInstance:new s(e)}static parseSpecifier(e){if(e){let t=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(t)return new s(gt(t[1],t[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${et(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${et(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,t){return et(this.fixed,t)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return e.type==="fixed"&&e.fixed===this.fixed}get isValid(){return!0}};var Vt=class extends ie{constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}};function Ee(s,e){let t;if(A(s)||s===null)return e;if(s instanceof ie)return s;if(Rf(s)){let n=s.toLowerCase();return n==="default"?e:n==="local"||n==="system"?Xe.instance:n==="utc"||n==="gmt"?H.utcInstance:H.parseSpecifier(n)||X.create(s)}else return ve(s)?H.instance(s):typeof s=="object"&&"offset"in s&&typeof s.offset=="function"?s:new Vt(s)}var Sa={arab:"[\u0660-\u0669]",arabext:"[\u06F0-\u06F9]",bali:"[\u1B50-\u1B59]",beng:"[\u09E6-\u09EF]",deva:"[\u0966-\u096F]",fullwide:"[\uFF10-\uFF19]",gujr:"[\u0AE6-\u0AEF]",hanidec:"[\u3007|\u4E00|\u4E8C|\u4E09|\u56DB|\u4E94|\u516D|\u4E03|\u516B|\u4E5D]",khmr:"[\u17E0-\u17E9]",knda:"[\u0CE6-\u0CEF]",laoo:"[\u0ED0-\u0ED9]",limb:"[\u1946-\u194F]",mlym:"[\u0D66-\u0D6F]",mong:"[\u1810-\u1819]",mymr:"[\u1040-\u1049]",orya:"[\u0B66-\u0B6F]",tamldec:"[\u0BE6-\u0BEF]",telu:"[\u0C66-\u0C6F]",thai:"[\u0E50-\u0E59]",tibt:"[\u0F20-\u0F29]",latn:"\\d"},Vf={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},A0=Sa.hanidec.replace(/[\[|\]]/g,"").split("");function Bf(s){let e=parseInt(s,10);if(isNaN(e)){e="";for(let t=0;t<s.length;t++){let n=s.charCodeAt(t);if(s[t].search(Sa.hanidec)!==-1)e+=A0.indexOf(s[t]);else for(let r in Vf){let[i,o]=Vf[r];n>=i&&n<=o&&(e+=n-i)}}return parseInt(e,10)}else return e}var Bt={};function Uf(){Bt={}}function he({numberingSystem:s},e=""){let t=s||"latn";return Bt[t]||(Bt[t]={}),Bt[t][e]||(Bt[t][e]=new RegExp(`${Sa[t]}${e}`)),Bt[t][e]}var Wf=()=>Date.now(),Yf="system",Hf=null,Kf=null,jf=null,Zf=60,Jf,Gf=null,x=class{static get now(){return Wf}static set now(e){Wf=e}static set defaultZone(e){Yf=e}static get defaultZone(){return Ee(Yf,Xe.instance)}static get defaultLocale(){return Hf}static set defaultLocale(e){Hf=e}static get defaultNumberingSystem(){return Kf}static set defaultNumberingSystem(e){Kf=e}static get defaultOutputCalendar(){return jf}static set defaultOutputCalendar(e){jf=e}static get defaultWeekSettings(){return Gf}static set defaultWeekSettings(e){Gf=tn(e)}static get twoDigitCutoffYear(){return Zf}static set twoDigitCutoffYear(e){Zf=e%100}static get throwOnInvalid(){return Jf}static set throwOnInvalid(e){Jf=e}static resetCaches(){_.resetCache(),X.resetCache(),F.resetCache(),Uf()}};var K=class{constructor(e,t){this.reason=e,this.explanation=t}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}};var zf=[0,31,59,90,120,151,181,212,243,273,304,334],Qf=[0,31,60,91,121,152,182,213,244,274,305,335];function de(s,e){return new K("unit out of range",`you specified ${e} (of type ${typeof e}) as a ${s}, which is invalid`)}function xr(s,e,t){let n=new Date(Date.UTC(s,e-1,t));s<100&&s>=0&&n.setUTCFullYear(n.getUTCFullYear()-1900);let r=n.getUTCDay();return r===0?7:r}function Xf(s,e,t){return t+(St(s)?Qf:zf)[e-1]}function eh(s,e){let t=St(s)?Qf:zf,n=t.findIndex(i=>i<e),r=e-t[n];return{month:n+1,day:r}}function _r(s,e){return(s-e+7)%7+1}function sn(s,e=4,t=1){let{year:n,month:r,day:i}=s,o=Xf(n,r,i),a=_r(xr(n,r,i),t),l=Math.floor((o-a+14-e)/7),c;return l<1?(c=n-1,l=yt(c,e,t)):l>yt(n,e,t)?(c=n+1,l=1):c=n,{weekYear:c,weekNumber:l,weekday:a,...rn(s)}}function wa(s,e=4,t=1){let{weekYear:n,weekNumber:r,weekday:i}=s,o=_r(xr(n,1,e),t),a=tt(n),l=r*7+i-o-7+e,c;l<1?(c=n-1,l+=tt(c)):l>a?(c=n+1,l-=tt(n)):c=n;let{month:u,day:f}=eh(c,l);return{year:c,month:u,day:f,...rn(s)}}function qr(s){let{year:e,month:t,day:n}=s,r=Xf(e,t,n);return{year:e,ordinal:r,...rn(s)}}function ka(s){let{year:e,ordinal:t}=s,{month:n,day:r}=eh(e,t);return{year:e,month:n,day:r,...rn(s)}}function Ta(s,e){if(!A(s.localWeekday)||!A(s.localWeekNumber)||!A(s.localWeekYear)){if(!A(s.weekday)||!A(s.weekNumber)||!A(s.weekYear))throw new Te("Cannot mix locale-based week fields with ISO-based week fields");return A(s.localWeekday)||(s.weekday=s.localWeekday),A(s.localWeekNumber)||(s.weekNumber=s.localWeekNumber),A(s.localWeekYear)||(s.weekYear=s.localWeekYear),delete s.localWeekday,delete s.localWeekNumber,delete s.localWeekYear,{minDaysInFirstWeek:e.getMinDaysInFirstWeek(),startOfWeek:e.getStartOfWeek()}}else return{minDaysInFirstWeek:4,startOfWeek:1}}function th(s,e=4,t=1){let n=nn(s.weekYear),r=le(s.weekNumber,1,yt(s.weekYear,e,t)),i=le(s.weekday,1,7);return n?r?i?!1:de("weekday",s.weekday):de("week",s.weekNumber):de("weekYear",s.weekYear)}function sh(s){let e=nn(s.year),t=le(s.ordinal,1,tt(s.year));return e?t?!1:de("ordinal",s.ordinal):de("year",s.year)}function ba(s){let e=nn(s.year),t=le(s.month,1,12),n=le(s.day,1,Ut(s.year,s.month));return e?t?n?!1:de("day",s.day):de("month",s.month):de("year",s.year)}function Na(s){let{hour:e,minute:t,second:n,millisecond:r}=s,i=le(e,0,23)||e===24&&t===0&&n===0&&r===0,o=le(t,0,59),a=le(n,0,59),l=le(r,0,999);return i?o?a?l?!1:de("millisecond",r):de("second",n):de("minute",t):de("hour",e)}function A(s){return typeof s>"u"}function ve(s){return typeof s=="number"}function nn(s){return typeof s=="number"&&s%1===0}function Rf(s){return typeof s=="string"}function rh(s){return Object.prototype.toString.call(s)==="[object Date]"}function Lr(){try{return typeof Intl<"u"&&!!Intl.RelativeTimeFormat}catch{return!1}}function Fr(){try{return typeof Intl<"u"&&!!Intl.Locale&&("weekInfo"in Intl.Locale.prototype||"getWeekInfo"in Intl.Locale.prototype)}catch{return!1}}function ih(s){return Array.isArray(s)?s:[s]}function Ea(s,e,t){if(s.length!==0)return s.reduce((n,r)=>{let i=[e(r),r];return n&&t(n[0],i[0])===n[0]?n:i},null)[1]}function oh(s,e){return e.reduce((t,n)=>(t[n]=s[n],t),{})}function st(s,e){return Object.prototype.hasOwnProperty.call(s,e)}function tn(s){if(s==null)return null;if(typeof s!="object")throw new B("Week settings must be an object");if(!le(s.firstDay,1,7)||!le(s.minimalDays,1,7)||!Array.isArray(s.weekend)||s.weekend.some(e=>!le(e,1,7)))throw new B("Invalid week settings");return{firstDay:s.firstDay,minimalDays:s.minimalDays,weekend:Array.from(s.weekend)}}function le(s,e,t){return nn(s)&&s>=e&&s<=t}function O0(s,e){return s-e*Math.floor(s/e)}function R(s,e=2){let t=s<0,n;return t?n="-"+(""+-s).padStart(e,"0"):n=(""+s).padStart(e,"0"),n}function _e(s){if(!(A(s)||s===null||s===""))return parseInt(s,10)}function nt(s){if(!(A(s)||s===null||s===""))return parseFloat(s)}function on(s){if(!(A(s)||s===null||s==="")){let e=parseFloat("0."+s)*1e3;return Math.floor(e)}}function Rt(s,e,t=!1){let n=10**e;return(t?Math.trunc:Math.round)(s*n)/n}function St(s){return s%4===0&&(s%100!==0||s%400===0)}function tt(s){return St(s)?366:365}function Ut(s,e){let t=O0(e-1,12)+1,n=s+(e-t)/12;return t===2?St(n)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][t-1]}function $t(s){let e=Date.UTC(s.year,s.month-1,s.day,s.hour,s.minute,s.second,s.millisecond);return s.year<100&&s.year>=0&&(e=new Date(e),e.setUTCFullYear(s.year,s.month-1,s.day)),+e}function nh(s,e,t){return-_r(xr(s,1,e),t)+e-1}function yt(s,e=4,t=1){let n=nh(s,e,t),r=nh(s+1,e,t);return(tt(s)-n+r)/7}function an(s){return s>99?s:s>x.twoDigitCutoffYear?1900+s:2e3+s}function Ir(s,e,t,n=null){let r=new Date(s),i={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};n&&(i.timeZone=n);let o={timeZoneName:e,...i},a=new Intl.DateTimeFormat(t,o).formatToParts(r).find(l=>l.type.toLowerCase()==="timezonename");return a?a.value:null}function gt(s,e){let t=parseInt(s,10);Number.isNaN(t)&&(t=0);let n=parseInt(e,10)||0,r=t<0||Object.is(t,-0)?-n:n;return t*60+r}function va(s){let e=Number(s);if(typeof s=="boolean"||s===""||Number.isNaN(e))throw new B(`Invalid unit value ${s}`);return e}function Wt(s,e){let t={};for(let n in s)if(st(s,n)){let r=s[n];if(r==null)continue;t[e(n)]=va(r)}return t}function et(s,e){let t=Math.trunc(Math.abs(s/60)),n=Math.trunc(Math.abs(s%60)),r=s>=0?"+":"-";switch(e){case"short":return`${r}${R(t,2)}:${R(n,2)}`;case"narrow":return`${r}${t}${n>0?`:${n}`:""}`;case"techie":return`${r}${R(t,2)}${R(n,2)}`;default:throw new RangeError(`Value format ${e} is out of range for property format`)}}function rn(s){return oh(s,["hour","minute","second","millisecond"])}var I0=["January","February","March","April","May","June","July","August","September","October","November","December"],Aa=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],D0=["J","F","M","A","M","J","J","A","S","O","N","D"];function ha(s){switch(s){case"narrow":return[...D0];case"short":return[...Aa];case"long":return[...I0];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}var Oa=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Ia=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],C0=["M","T","W","T","F","S","S"];function da(s){switch(s){case"narrow":return[...C0];case"short":return[...Ia];case"long":return[...Oa];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}var ma=["AM","PM"],M0=["Before Christ","Anno Domini"],L0=["BC","AD"],F0=["B","A"];function pa(s){switch(s){case"narrow":return[...F0];case"short":return[...L0];case"long":return[...M0];default:return null}}function ah(s){return ma[s.hour<12?0:1]}function lh(s,e){return da(e)[s.weekday-1]}function ch(s,e){return ha(e)[s.month-1]}function uh(s,e){return pa(e)[s.year<0?0:1]}function $f(s,e,t="always",n=!1){let r={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},i=["hours","minutes","seconds"].indexOf(s)===-1;if(t==="auto"&&i){let f=s==="days";switch(e){case 1:return f?"tomorrow":`next ${r[s][0]}`;case-1:return f?"yesterday":`last ${r[s][0]}`;case 0:return f?"today":`this ${r[s][0]}`;default:}}let o=Object.is(e,-0)||e<0,a=Math.abs(e),l=a===1,c=r[s],u=n?l?c[1]:c[2]||c[1]:l?r[s][0]:s;return o?`${a} ${u} ago`:`in ${a} ${u}`}function fh(s,e){let t="";for(let n of s)n.literal?t+=n.val:t+=e(n.val);return t}var x0={D:Qe,DD:_s,DDD:qs,DDDD:Ps,t:$s,tt:Rs,ttt:Vs,tttt:Bs,T:Us,TT:Ws,TTT:Ys,TTTT:Hs,f:Ks,ff:Zs,fff:Gs,ffff:Qs,F:js,FF:Js,FFF:zs,FFFF:Xs},j=class s{static create(e,t={}){return new s(e,t)}static parseFormat(e){let t=null,n="",r=!1,i=[];for(let o=0;o<e.length;o++){let a=e.charAt(o);a==="'"?(n.length>0&&i.push({literal:r||/^\s+$/.test(n),val:n}),t=null,n="",r=!r):r||a===t?n+=a:(n.length>0&&i.push({literal:/^\s+$/.test(n),val:n}),n=a,t=a)}return n.length>0&&i.push({literal:r||/^\s+$/.test(n),val:n}),i}static macroTokenToFormatOpts(e){return x0[e]}constructor(e,t){this.opts=t,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,t){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,{...this.opts,...t}).format()}dtFormatter(e,t={}){return this.loc.dtFormatter(e,{...this.opts,...t})}formatDateTime(e,t){return this.dtFormatter(e,t).format()}formatDateTimeParts(e,t){return this.dtFormatter(e,t).formatToParts()}formatInterval(e,t){return this.dtFormatter(e.start,t).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())}resolvedOptions(e,t){return this.dtFormatter(e,t).resolvedOptions()}num(e,t=0){if(this.opts.forceSimple)return R(e,t);let n={...this.opts};return t>0&&(n.padTo=t),this.loc.numberFormatter(n).format(e)}formatDateTimeFromString(e,t){let n=this.loc.listingMode()==="en",r=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",i=(d,g)=>this.loc.extract(e,d,g),o=d=>e.isOffsetFixed&&e.offset===0&&d.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,d.format):"",a=()=>n?ah(e):i({hour:"numeric",hourCycle:"h12"},"dayperiod"),l=(d,g)=>n?ch(e,d):i(g?{month:d}:{month:d,day:"numeric"},"month"),c=(d,g)=>n?lh(e,d):i(g?{weekday:d}:{weekday:d,month:"long",day:"numeric"},"weekday"),u=d=>{let g=s.macroTokenToFormatOpts(d);return g?this.formatWithSystemDefault(e,g):d},f=d=>n?uh(e,d):i({era:d},"era"),h=d=>{switch(d){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12===0?12:e.hour%12);case"hh":return this.num(e.hour%12===0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return o({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return o({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return o({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return a();case"d":return r?i({day:"numeric"},"day"):this.num(e.day);case"dd":return r?i({day:"2-digit"},"day"):this.num(e.day,2);case"c":return this.num(e.weekday);case"ccc":return c("short",!0);case"cccc":return c("long",!0);case"ccccc":return c("narrow",!0);case"E":return this.num(e.weekday);case"EEE":return c("short",!1);case"EEEE":return c("long",!1);case"EEEEE":return c("narrow",!1);case"L":return r?i({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return r?i({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return l("short",!0);case"LLLL":return l("long",!0);case"LLLLL":return l("narrow",!0);case"M":return r?i({month:"numeric"},"month"):this.num(e.month);case"MM":return r?i({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return l("short",!1);case"MMMM":return l("long",!1);case"MMMMM":return l("narrow",!1);case"y":return r?i({year:"numeric"},"year"):this.num(e.year);case"yy":return r?i({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return r?i({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return r?i({year:"numeric"},"year"):this.num(e.year,6);case"G":return f("short");case"GG":return f("long");case"GGGGG":return f("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"n":return this.num(e.localWeekNumber);case"nn":return this.num(e.localWeekNumber,2);case"ii":return this.num(e.localWeekYear.toString().slice(-2),2);case"iiii":return this.num(e.localWeekYear,4);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return u(d)}};return fh(s.parseFormat(t),h)}formatDurationFromString(e,t){let n=l=>{switch(l[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},r=l=>c=>{let u=n(c);return u?this.num(l.get(u),c.length):c},i=s.parseFormat(t),o=i.reduce((l,{literal:c,val:u})=>c?l:l.concat(u),[]),a=e.shiftTo(...o.map(n).filter(l=>l));return fh(i,r(a))}};var dh=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function Ht(...s){let e=s.reduce((t,n)=>t+n.source,"");return RegExp(`^${e}$`)}function Kt(...s){return e=>s.reduce(([t,n,r],i)=>{let[o,a,l]=i(e,r);return[{...t,...o},a||n,l]},[{},null,1]).slice(0,2)}function jt(s,...e){if(s==null)return[null,null];for(let[t,n]of e){let r=t.exec(s);if(r)return n(r)}return[null,null]}function mh(...s){return(e,t)=>{let n={},r;for(r=0;r<s.length;r++)n[s[r]]=_e(e[t+r]);return[n,null,t+r]}}var ph=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,_0=`(?:${ph.source}?(?:\\[(${dh.source})\\])?)?`,Da=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,gh=RegExp(`${Da.source}${_0}`),Ca=RegExp(`(?:T${gh.source})?`),q0=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,P0=/(\d{4})-?W(\d\d)(?:-?(\d))?/,$0=/(\d{4})-?(\d{3})/,R0=mh("weekYear","weekNumber","weekDay"),V0=mh("year","ordinal"),B0=/(\d{4})-(\d\d)-(\d\d)/,yh=RegExp(`${Da.source} ?(?:${ph.source}|(${dh.source}))?`),U0=RegExp(`(?: ${yh.source})?`);function Yt(s,e,t){let n=s[e];return A(n)?t:_e(n)}function W0(s,e){return[{year:Yt(s,e),month:Yt(s,e+1,1),day:Yt(s,e+2,1)},null,e+3]}function Zt(s,e){return[{hours:Yt(s,e,0),minutes:Yt(s,e+1,0),seconds:Yt(s,e+2,0),milliseconds:on(s[e+3])},null,e+4]}function ln(s,e){let t=!s[e]&&!s[e+1],n=gt(s[e+1],s[e+2]),r=t?null:H.instance(n);return[{},r,e+3]}function cn(s,e){let t=s[e]?X.create(s[e]):null;return[{},t,e+1]}var Y0=RegExp(`^T?${Da.source}$`),H0=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function K0(s){let[e,t,n,r,i,o,a,l,c]=s,u=e[0]==="-",f=l&&l[0]==="-",h=(d,g=!1)=>d!==void 0&&(g||d&&u)?-d:d;return[{years:h(nt(t)),months:h(nt(n)),weeks:h(nt(r)),days:h(nt(i)),hours:h(nt(o)),minutes:h(nt(a)),seconds:h(nt(l),l==="-0"),milliseconds:h(on(c),f)}]}var j0={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function Ma(s,e,t,n,r,i,o){let a={year:e.length===2?an(_e(e)):_e(e),month:Aa.indexOf(t)+1,day:_e(n),hour:_e(r),minute:_e(i)};return o&&(a.second=_e(o)),s&&(a.weekday=s.length>3?Oa.indexOf(s)+1:Ia.indexOf(s)+1),a}var Z0=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function J0(s){let[,e,t,n,r,i,o,a,l,c,u,f]=s,h=Ma(e,r,n,t,i,o,a),d;return l?d=j0[l]:c?d=0:d=gt(u,f),[h,new H(d)]}function G0(s){return s.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}var z0=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,Q0=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,X0=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function hh(s){let[,e,t,n,r,i,o,a]=s;return[Ma(e,r,n,t,i,o,a),H.utcInstance]}function eS(s){let[,e,t,n,r,i,o,a]=s;return[Ma(e,a,t,n,r,i,o),H.utcInstance]}var tS=Ht(q0,Ca),sS=Ht(P0,Ca),nS=Ht($0,Ca),rS=Ht(gh),Sh=Kt(W0,Zt,ln,cn),iS=Kt(R0,Zt,ln,cn),oS=Kt(V0,Zt,ln,cn),aS=Kt(Zt,ln,cn);function wh(s){return jt(s,[tS,Sh],[sS,iS],[nS,oS],[rS,aS])}function kh(s){return jt(G0(s),[Z0,J0])}function Th(s){return jt(s,[z0,hh],[Q0,hh],[X0,eS])}function bh(s){return jt(s,[H0,K0])}var lS=Kt(Zt);function Nh(s){return jt(s,[Y0,lS])}var cS=Ht(B0,U0),uS=Ht(yh),fS=Kt(Zt,ln,cn);function Eh(s){return jt(s,[cS,Sh],[uS,fS])}var vh="Invalid Duration",Oh={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},hS={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...Oh},me=146097/400,Jt=146097/4800,dS={years:{quarters:4,months:12,weeks:me/7,days:me,hours:me*24,minutes:me*24*60,seconds:me*24*60*60,milliseconds:me*24*60*60*1e3},quarters:{months:3,weeks:me/28,days:me/4,hours:me*24/4,minutes:me*24*60/4,seconds:me*24*60*60/4,milliseconds:me*24*60*60*1e3/4},months:{weeks:Jt/7,days:Jt,hours:Jt*24,minutes:Jt*24*60,seconds:Jt*24*60*60,milliseconds:Jt*24*60*60*1e3},...Oh},wt=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],mS=wt.slice(0).reverse();function rt(s,e,t=!1){let n={values:t?e.values:{...s.values,...e.values||{}},loc:s.loc.clone(e.loc),conversionAccuracy:e.conversionAccuracy||s.conversionAccuracy,matrix:e.matrix||s.matrix};return new V(n)}function Ih(s,e){let t=e.milliseconds??0;for(let n of mS.slice(1))e[n]&&(t+=e[n]*s[n].milliseconds);return t}function Ah(s,e){let t=Ih(s,e)<0?-1:1;wt.reduceRight((n,r)=>{if(A(e[r]))return n;if(n){let i=e[n]*t,o=s[r][n],a=Math.floor(i/o);e[r]+=a*t,e[n]-=a*o*t}return r},null),wt.reduce((n,r)=>{if(A(e[r]))return n;if(n){let i=e[n]%1;e[n]-=i,e[r]+=i*s[n][r]}return r},null)}function pS(s){let e={};for(let[t,n]of Object.entries(s))n!==0&&(e[t]=n);return e}var V=class s{constructor(e){let t=e.conversionAccuracy==="longterm"||!1,n=t?dS:hS;e.matrix&&(n=e.matrix),this.values=e.values,this.loc=e.loc||_.create(),this.conversionAccuracy=t?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=n,this.isLuxonDuration=!0}static fromMillis(e,t){return s.fromObject({milliseconds:e},t)}static fromObject(e,t={}){if(e==null||typeof e!="object")throw new B(`Duration.fromObject: argument expected to be an object, got ${e===null?"null":typeof e}`);return new s({values:Wt(e,s.normalizeUnit),loc:_.fromObject(t),conversionAccuracy:t.conversionAccuracy,matrix:t.matrix})}static fromDurationLike(e){if(ve(e))return s.fromMillis(e);if(s.isDuration(e))return e;if(typeof e=="object")return s.fromObject(e);throw new B(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,t){let[n]=bh(e);return n?s.fromObject(n,t):s.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,t){let[n]=Nh(e);return n?s.fromObject(n,t):s.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,t=null){if(!e)throw new B("need to specify a reason the Duration is invalid");let n=e instanceof K?e:new K(e,t);if(x.throwOnInvalid)throw new Ar(n);return new s({invalid:n})}static normalizeUnit(e){let t={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e&&e.toLowerCase()];if(!t)throw new Pt(e);return t}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,t={}){let n={...t,floor:t.round!==!1&&t.floor!==!1};return this.isValid?j.create(this.loc,n).formatDurationFromString(this,e):vh}toHuman(e={}){if(!this.isValid)return vh;let t=wt.map(n=>{let r=this.values[n];return A(r)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...e,unit:n.slice(0,-1)}).format(r)}).filter(n=>n);return this.loc.listFormatter({type:"conjunction",style:e.listStyle||"narrow",...e}).format(t)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let e="P";return this.years!==0&&(e+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(e+=this.months+this.quarters*3+"M"),this.weeks!==0&&(e+=this.weeks+"W"),this.days!==0&&(e+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(e+="T"),this.hours!==0&&(e+=this.hours+"H"),this.minutes!==0&&(e+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(e+=Rt(this.seconds+this.milliseconds/1e3,3)+"S"),e==="P"&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;let t=this.toMillis();return t<0||t>=864e5?null:(e={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...e,includeOffset:!1},F.fromMillis(t,{zone:"UTC"}).toISOTime(e))}toJSON(){return this.toISO()}toString(){return this.toISO()}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Duration { values: ${JSON.stringify(this.values)} }`:`Duration { Invalid, reason: ${this.invalidReason} }`}toMillis(){return this.isValid?Ih(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;let t=s.fromDurationLike(e),n={};for(let r of wt)(st(t.values,r)||st(this.values,r))&&(n[r]=t.get(r)+this.get(r));return rt(this,{values:n},!0)}minus(e){if(!this.isValid)return this;let t=s.fromDurationLike(e);return this.plus(t.negate())}mapUnits(e){if(!this.isValid)return this;let t={};for(let n of Object.keys(this.values))t[n]=va(e(this.values[n],n));return rt(this,{values:t},!0)}get(e){return this[s.normalizeUnit(e)]}set(e){if(!this.isValid)return this;let t={...this.values,...Wt(e,s.normalizeUnit)};return rt(this,{values:t})}reconfigure({locale:e,numberingSystem:t,conversionAccuracy:n,matrix:r}={}){let o={loc:this.loc.clone({locale:e,numberingSystem:t}),matrix:r,conversionAccuracy:n};return rt(this,o)}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;let e=this.toObject();return Ah(this.matrix,e),rt(this,{values:e},!0)}rescale(){if(!this.isValid)return this;let e=pS(this.normalize().shiftToAll().toObject());return rt(this,{values:e},!0)}shiftTo(...e){if(!this.isValid)return this;if(e.length===0)return this;e=e.map(o=>s.normalizeUnit(o));let t={},n={},r=this.toObject(),i;for(let o of wt)if(e.indexOf(o)>=0){i=o;let a=0;for(let c in n)a+=this.matrix[c][o]*n[c],n[c]=0;ve(r[o])&&(a+=r[o]);let l=Math.trunc(a);t[o]=l,n[o]=(a*1e3-l*1e3)/1e3}else ve(r[o])&&(n[o]=r[o]);for(let o in n)n[o]!==0&&(t[i]+=o===i?n[o]:n[o]/this.matrix[i][o]);return Ah(this.matrix,t),rt(this,{values:t},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;let e={};for(let t of Object.keys(this.values))e[t]=this.values[t]===0?0:-this.values[t];return rt(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid||!this.loc.equals(e.loc))return!1;function t(n,r){return n===void 0||n===0?r===void 0||r===0:n===r}for(let n of wt)if(!t(this.values[n],e.values[n]))return!1;return!0}};var Gt="Invalid Interval";function gS(s,e){return!s||!s.isValid?qe.invalid("missing or invalid start"):!e||!e.isValid?qe.invalid("missing or invalid end"):e<s?qe.invalid("end before start",`The end of an interval must be after its start, but you had start=${s.toISO()} and end=${e.toISO()}`):null}var qe=class s{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,t=null){if(!e)throw new B("need to specify a reason the Interval is invalid");let n=e instanceof K?e:new K(e,t);if(x.throwOnInvalid)throw new vr(n);return new s({invalid:n})}static fromDateTimes(e,t){let n=zt(e),r=zt(t),i=gS(n,r);return i??new s({start:n,end:r})}static after(e,t){let n=V.fromDurationLike(t),r=zt(e);return s.fromDateTimes(r,r.plus(n))}static before(e,t){let n=V.fromDurationLike(t),r=zt(e);return s.fromDateTimes(r.minus(n),r)}static fromISO(e,t){let[n,r]=(e||"").split("/",2);if(n&&r){let i,o;try{i=F.fromISO(n,t),o=i.isValid}catch{o=!1}let a,l;try{a=F.fromISO(r,t),l=a.isValid}catch{l=!1}if(o&&l)return s.fromDateTimes(i,a);if(o){let c=V.fromISO(r,t);if(c.isValid)return s.after(i,c)}else if(l){let c=V.fromISO(n,t);if(c.isValid)return s.before(a,c)}}return s.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds",t){if(!this.isValid)return NaN;let n=this.start.startOf(e,t),r;return t?.useLocaleWeeks?r=this.end.reconfigure({locale:n.locale}):r=this.end,r=r.startOf(e,t),Math.floor(r.diff(n,e).get(e))+(r.valueOf()!==this.end.valueOf())}hasSame(e){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,e):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return this.isValid?this.s>e:!1}isBefore(e){return this.isValid?this.e<=e:!1}contains(e){return this.isValid?this.s<=e&&this.e>e:!1}set({start:e,end:t}={}){return this.isValid?s.fromDateTimes(e||this.s,t||this.e):this}splitAt(...e){if(!this.isValid)return[];let t=e.map(zt).filter(o=>this.contains(o)).sort((o,a)=>o.toMillis()-a.toMillis()),n=[],{s:r}=this,i=0;for(;r<this.e;){let o=t[i]||this.e,a=+o>+this.e?this.e:o;n.push(s.fromDateTimes(r,a)),r=a,i+=1}return n}splitBy(e){let t=V.fromDurationLike(e);if(!this.isValid||!t.isValid||t.as("milliseconds")===0)return[];let{s:n}=this,r=1,i,o=[];for(;n<this.e;){let a=this.start.plus(t.mapUnits(l=>l*r));i=+a>+this.e?this.e:a,o.push(s.fromDateTimes(n,i)),n=i,r+=1}return o}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return this.isValid?+this.e==+e.s:!1}abutsEnd(e){return this.isValid?+e.e==+this.s:!1}engulfs(e){return this.isValid?this.s<=e.s&&this.e>=e.e:!1}equals(e){return!this.isValid||!e.isValid?!1:this.s.equals(e.s)&&this.e.equals(e.e)}intersection(e){if(!this.isValid)return this;let t=this.s>e.s?this.s:e.s,n=this.e<e.e?this.e:e.e;return t>=n?null:s.fromDateTimes(t,n)}union(e){if(!this.isValid)return this;let t=this.s<e.s?this.s:e.s,n=this.e>e.e?this.e:e.e;return s.fromDateTimes(t,n)}static merge(e){let[t,n]=e.sort((r,i)=>r.s-i.s).reduce(([r,i],o)=>i?i.overlaps(o)||i.abutsStart(o)?[r,i.union(o)]:[r.concat([i]),o]:[r,o],[[],null]);return n&&t.push(n),t}static xor(e){let t=null,n=0,r=[],i=e.map(l=>[{time:l.s,type:"s"},{time:l.e,type:"e"}]),o=Array.prototype.concat(...i),a=o.sort((l,c)=>l.time-c.time);for(let l of a)n+=l.type==="s"?1:-1,n===1?t=l.time:(t&&+t!=+l.time&&r.push(s.fromDateTimes(t,l.time)),t=null);return s.merge(r)}difference(...e){return s.xor([this].concat(e)).map(t=>this.intersection(t)).filter(t=>t&&!t.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} \u2013 ${this.e.toISO()})`:Gt}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`:`Interval { Invalid, reason: ${this.invalidReason} }`}toLocaleString(e=Qe,t={}){return this.isValid?j.create(this.s.loc.clone(t),e).formatInterval(this):Gt}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:Gt}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:Gt}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:Gt}toFormat(e,{separator:t=" \u2013 "}={}){return this.isValid?`${this.s.toFormat(e)}${t}${this.e.toFormat(e)}`:Gt}toDuration(e,t){return this.isValid?this.e.diff(this.s,e,t):V.invalid(this.invalidReason)}mapEndpoints(e){return s.fromDateTimes(e(this.s),e(this.e))}};var Pe=class{static hasDST(e=x.defaultZone){let t=F.now().setZone(e).set({month:12});return!e.isUniversal&&t.offset!==t.set({month:6}).offset}static isValidIANAZone(e){return X.isValidZone(e)}static normalizeZone(e){return Ee(e,x.defaultZone)}static getStartOfWeek({locale:e=null,locObj:t=null}={}){return(t||_.create(e)).getStartOfWeek()}static getMinimumDaysInFirstWeek({locale:e=null,locObj:t=null}={}){return(t||_.create(e)).getMinDaysInFirstWeek()}static getWeekendWeekdays({locale:e=null,locObj:t=null}={}){return(t||_.create(e)).getWeekendDays().slice()}static months(e="long",{locale:t=null,numberingSystem:n=null,locObj:r=null,outputCalendar:i="gregory"}={}){return(r||_.create(t,n,i)).months(e)}static monthsFormat(e="long",{locale:t=null,numberingSystem:n=null,locObj:r=null,outputCalendar:i="gregory"}={}){return(r||_.create(t,n,i)).months(e,!0)}static weekdays(e="long",{locale:t=null,numberingSystem:n=null,locObj:r=null}={}){return(r||_.create(t,n,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:t=null,numberingSystem:n=null,locObj:r=null}={}){return(r||_.create(t,n,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return _.create(e).meridiems()}static eras(e="short",{locale:t=null}={}){return _.create(t,null,"gregory").eras(e)}static features(){return{relative:Lr(),localeWeek:Fr()}}};function Dh(s,e){let t=r=>r.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),n=t(e)-t(s);return Math.floor(V.fromMillis(n).as("days"))}function yS(s,e,t){let n=[["years",(l,c)=>c.year-l.year],["quarters",(l,c)=>c.quarter-l.quarter+(c.year-l.year)*4],["months",(l,c)=>c.month-l.month+(c.year-l.year)*12],["weeks",(l,c)=>{let u=Dh(l,c);return(u-u%7)/7}],["days",Dh]],r={},i=s,o,a;for(let[l,c]of n)t.indexOf(l)>=0&&(o=l,r[l]=c(s,e),a=i.plus(r),a>e?(r[l]--,s=i.plus(r),s>e&&(a=s,r[l]--,s=i.plus(r))):s=a);return[s,r,a,o]}function Ch(s,e,t,n){let[r,i,o,a]=yS(s,e,t),l=e-r,c=t.filter(f=>["hours","minutes","seconds","milliseconds"].indexOf(f)>=0);c.length===0&&(o<e&&(o=r.plus({[a]:1})),o!==r&&(i[a]=(i[a]||0)+l/(o-r)));let u=V.fromObject(i,n);return c.length>0?V.fromMillis(l,n).shiftTo(...c).plus(u):u}var SS="missing Intl.DateTimeFormat.formatToParts support";function q(s,e=t=>t){return{regex:s,deser:([t])=>e(Bf(t))}}var wS="\xA0",Fh=`[ ${wS}]`,xh=new RegExp(Fh,"g");function kS(s){return s.replace(/\./g,"\\.?").replace(xh,Fh)}function Mh(s){return s.replace(/\./g,"").replace(xh," ").toLowerCase()}function Ae(s,e){return s===null?null:{regex:RegExp(s.map(kS).join("|")),deser:([t])=>s.findIndex(n=>Mh(t)===Mh(n))+e}}function Lh(s,e){return{regex:s,deser:([,t,n])=>gt(t,n),groups:e}}function Pr(s){return{regex:s,deser:([e])=>e}}function TS(s){return s.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function bS(s,e){let t=he(e),n=he(e,"{2}"),r=he(e,"{3}"),i=he(e,"{4}"),o=he(e,"{6}"),a=he(e,"{1,2}"),l=he(e,"{1,3}"),c=he(e,"{1,6}"),u=he(e,"{1,9}"),f=he(e,"{2,4}"),h=he(e,"{4,6}"),d=p=>({regex:RegExp(TS(p.val)),deser:([w])=>w,literal:!0}),m=(p=>{if(s.literal)return d(p);switch(p.val){case"G":return Ae(e.eras("short"),0);case"GG":return Ae(e.eras("long"),0);case"y":return q(c);case"yy":return q(f,an);case"yyyy":return q(i);case"yyyyy":return q(h);case"yyyyyy":return q(o);case"M":return q(a);case"MM":return q(n);case"MMM":return Ae(e.months("short",!0),1);case"MMMM":return Ae(e.months("long",!0),1);case"L":return q(a);case"LL":return q(n);case"LLL":return Ae(e.months("short",!1),1);case"LLLL":return Ae(e.months("long",!1),1);case"d":return q(a);case"dd":return q(n);case"o":return q(l);case"ooo":return q(r);case"HH":return q(n);case"H":return q(a);case"hh":return q(n);case"h":return q(a);case"mm":return q(n);case"m":return q(a);case"q":return q(a);case"qq":return q(n);case"s":return q(a);case"ss":return q(n);case"S":return q(l);case"SSS":return q(r);case"u":return Pr(u);case"uu":return Pr(a);case"uuu":return q(t);case"a":return Ae(e.meridiems(),0);case"kkkk":return q(i);case"kk":return q(f,an);case"W":return q(a);case"WW":return q(n);case"E":case"c":return q(t);case"EEE":return Ae(e.weekdays("short",!1),1);case"EEEE":return Ae(e.weekdays("long",!1),1);case"ccc":return Ae(e.weekdays("short",!0),1);case"cccc":return Ae(e.weekdays("long",!0),1);case"Z":case"ZZ":return Lh(new RegExp(`([+-]${a.source})(?::(${n.source}))?`),2);case"ZZZ":return Lh(new RegExp(`([+-]${a.source})(${n.source})?`),2);case"z":return Pr(/[a-z_+-/]{1,256}?/i);case" ":return Pr(/[^\S\n\r]/);default:return d(p)}})(s)||{invalidReason:SS};return m.token=s,m}var NS={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function ES(s,e,t){let{type:n,value:r}=s;if(n==="literal"){let l=/^\s+$/.test(r);return{literal:!l,val:l?" ":r}}let i=e[n],o=n;n==="hour"&&(e.hour12!=null?o=e.hour12?"hour12":"hour24":e.hourCycle!=null?e.hourCycle==="h11"||e.hourCycle==="h12"?o="hour12":o="hour24":o=t.hour12?"hour12":"hour24");let a=NS[o];if(typeof a=="object"&&(a=a[i]),a)return{literal:!1,val:a}}function vS(s){return[`^${s.map(t=>t.regex).reduce((t,n)=>`${t}(${n.source})`,"")}$`,s]}function AS(s,e,t){let n=s.match(e);if(n){let r={},i=1;for(let o in t)if(st(t,o)){let a=t[o],l=a.groups?a.groups+1:1;!a.literal&&a.token&&(r[a.token.val[0]]=a.deser(n.slice(i,i+l))),i+=l}return[n,r]}else return[n,{}]}function OS(s){let e=i=>{switch(i){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}},t=null,n;return A(s.z)||(t=X.create(s.z)),A(s.Z)||(t||(t=new H(s.Z)),n=s.Z),A(s.q)||(s.M=(s.q-1)*3+1),A(s.h)||(s.h<12&&s.a===1?s.h+=12:s.h===12&&s.a===0&&(s.h=0)),s.G===0&&s.y&&(s.y=-s.y),A(s.u)||(s.S=on(s.u)),[Object.keys(s).reduce((i,o)=>{let a=e(o);return a&&(i[a]=s[o]),i},{}),t,n]}var La=null;function IS(){return La||(La=F.fromMillis(1555555555555)),La}function DS(s,e){if(s.literal)return s;let t=j.macroTokenToFormatOpts(s.val),n=_a(t,e);return n==null||n.includes(void 0)?s:n}function Fa(s,e){return Array.prototype.concat(...s.map(t=>DS(t,e)))}var un=class{constructor(e,t){if(this.locale=e,this.format=t,this.tokens=Fa(j.parseFormat(t),e),this.units=this.tokens.map(n=>bS(n,e)),this.disqualifyingUnit=this.units.find(n=>n.invalidReason),!this.disqualifyingUnit){let[n,r]=vS(this.units);this.regex=RegExp(n,"i"),this.handlers=r}}explainFromTokens(e){if(this.isValid){let[t,n]=AS(e,this.regex,this.handlers),[r,i,o]=n?OS(n):[null,null,void 0];if(st(n,"a")&&st(n,"H"))throw new Te("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:this.tokens,regex:this.regex,rawMatches:t,matches:n,result:r,zone:i,specificOffset:o}}else return{input:e,tokens:this.tokens,invalidReason:this.invalidReason}}get isValid(){return!this.disqualifyingUnit}get invalidReason(){return this.disqualifyingUnit?this.disqualifyingUnit.invalidReason:null}};function xa(s,e,t){return new un(s,t).explainFromTokens(e)}function _h(s,e,t){let{result:n,zone:r,specificOffset:i,invalidReason:o}=xa(s,e,t);return[n,r,i,o]}function _a(s,e){if(!s)return null;let n=j.create(e,s).dtFormatter(IS()),r=n.formatToParts(),i=n.resolvedOptions();return r.map(o=>ES(o,s,i))}var qa="Invalid DateTime",qh=864e13;function fn(s){return new K("unsupported zone",`the zone "${s.name}" is not supported`)}function Pa(s){return s.weekData===null&&(s.weekData=sn(s.c)),s.weekData}function $a(s){return s.localWeekData===null&&(s.localWeekData=sn(s.c,s.loc.getMinDaysInFirstWeek(),s.loc.getStartOfWeek())),s.localWeekData}function kt(s,e){let t={ts:s.ts,zone:s.zone,c:s.c,o:s.o,loc:s.loc,invalid:s.invalid};return new F({...t,...e,old:t})}function Wh(s,e,t){let n=s-e*60*1e3,r=t.offset(n);if(e===r)return[n,e];n-=(r-e)*60*1e3;let i=t.offset(n);return r===i?[n,r]:[s-Math.min(r,i)*60*1e3,Math.max(r,i)]}function $r(s,e){s+=e*60*1e3;let t=new Date(s);return{year:t.getUTCFullYear(),month:t.getUTCMonth()+1,day:t.getUTCDate(),hour:t.getUTCHours(),minute:t.getUTCMinutes(),second:t.getUTCSeconds(),millisecond:t.getUTCMilliseconds()}}function Vr(s,e,t){return Wh($t(s),e,t)}function Ph(s,e){let t=s.o,n=s.c.year+Math.trunc(e.years),r=s.c.month+Math.trunc(e.months)+Math.trunc(e.quarters)*3,i={...s.c,year:n,month:r,day:Math.min(s.c.day,Ut(n,r))+Math.trunc(e.days)+Math.trunc(e.weeks)*7},o=V.fromObject({years:e.years-Math.trunc(e.years),quarters:e.quarters-Math.trunc(e.quarters),months:e.months-Math.trunc(e.months),weeks:e.weeks-Math.trunc(e.weeks),days:e.days-Math.trunc(e.days),hours:e.hours,minutes:e.minutes,seconds:e.seconds,milliseconds:e.milliseconds}).as("milliseconds"),a=$t(i),[l,c]=Wh(a,t,s.zone);return o!==0&&(l+=o,c=s.zone.offset(l)),{ts:l,o:c}}function Qt(s,e,t,n,r,i){let{setZone:o,zone:a}=t;if(s&&Object.keys(s).length!==0||e){let l=e||a,c=F.fromObject(s,{...t,zone:l,specificOffset:i});return o?c:c.setZone(a)}else return F.invalid(new K("unparsable",`the input "${r}" can't be parsed as ${n}`))}function Rr(s,e,t=!0){return s.isValid?j.create(_.create("en-US"),{allowZ:t,forceSimple:!0}).formatDateTimeFromString(s,e):null}function Ra(s,e){let t=s.c.year>9999||s.c.year<0,n="";return t&&s.c.year>=0&&(n+="+"),n+=R(s.c.year,t?6:4),e?(n+="-",n+=R(s.c.month),n+="-",n+=R(s.c.day)):(n+=R(s.c.month),n+=R(s.c.day)),n}function $h(s,e,t,n,r,i){let o=R(s.c.hour);return e?(o+=":",o+=R(s.c.minute),(s.c.millisecond!==0||s.c.second!==0||!t)&&(o+=":")):o+=R(s.c.minute),(s.c.millisecond!==0||s.c.second!==0||!t)&&(o+=R(s.c.second),(s.c.millisecond!==0||!n)&&(o+=".",o+=R(s.c.millisecond,3))),r&&(s.isOffsetFixed&&s.offset===0&&!i?o+="Z":s.o<0?(o+="-",o+=R(Math.trunc(-s.o/60)),o+=":",o+=R(Math.trunc(-s.o%60))):(o+="+",o+=R(Math.trunc(s.o/60)),o+=":",o+=R(Math.trunc(s.o%60)))),i&&(o+="["+s.zone.ianaName+"]"),o}var Yh={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},CS={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},MS={ordinal:1,hour:0,minute:0,second:0,millisecond:0},Hh=["year","month","day","hour","minute","second","millisecond"],LS=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],FS=["year","ordinal","hour","minute","second","millisecond"];function xS(s){let e={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[s.toLowerCase()];if(!e)throw new Pt(s);return e}function Rh(s){switch(s.toLowerCase()){case"localweekday":case"localweekdays":return"localWeekday";case"localweeknumber":case"localweeknumbers":return"localWeekNumber";case"localweekyear":case"localweekyears":return"localWeekYear";default:return xS(s)}}function _S(s){return Ur[s]||(Br===void 0&&(Br=x.now()),Ur[s]=s.offset(Br)),Ur[s]}function Vh(s,e){let t=Ee(e.zone,x.defaultZone);if(!t.isValid)return F.invalid(fn(t));let n=_.fromObject(e),r,i;if(A(s.year))r=x.now();else{for(let l of Hh)A(s[l])&&(s[l]=Yh[l]);let o=ba(s)||Na(s);if(o)return F.invalid(o);let a=_S(t);[r,i]=Vr(s,a,t)}return new F({ts:r,zone:t,loc:n,o:i})}function Bh(s,e,t){let n=A(t.round)?!0:t.round,r=(o,a)=>(o=Rt(o,n||t.calendary?0:2,!0),e.loc.clone(t).relFormatter(t).format(o,a)),i=o=>t.calendary?e.hasSame(s,o)?0:e.startOf(o).diff(s.startOf(o),o).get(o):e.diff(s,o).get(o);if(t.unit)return r(i(t.unit),t.unit);for(let o of t.units){let a=i(o);if(Math.abs(a)>=1)return r(a,o)}return r(s>e?-0:0,t.units[t.units.length-1])}function Uh(s){let e={},t;return s.length>0&&typeof s[s.length-1]=="object"?(e=s[s.length-1],t=Array.from(s).slice(0,s.length-1)):t=Array.from(s),[e,t]}var Br,Ur={},F=class s{constructor(e){let t=e.zone||x.defaultZone,n=e.invalid||(Number.isNaN(e.ts)?new K("invalid input"):null)||(t.isValid?null:fn(t));this.ts=A(e.ts)?x.now():e.ts;let r=null,i=null;if(!n)if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(t))[r,i]=[e.old.c,e.old.o];else{let a=ve(e.o)&&!e.old?e.o:t.offset(this.ts);r=$r(this.ts,a),n=Number.isNaN(r.year)?new K("invalid input"):null,r=n?null:r,i=n?null:a}this._zone=t,this.loc=e.loc||_.create(),this.invalid=n,this.weekData=null,this.localWeekData=null,this.c=r,this.o=i,this.isLuxonDateTime=!0}static now(){return new s({})}static local(){let[e,t]=Uh(arguments),[n,r,i,o,a,l,c]=t;return Vh({year:n,month:r,day:i,hour:o,minute:a,second:l,millisecond:c},e)}static utc(){let[e,t]=Uh(arguments),[n,r,i,o,a,l,c]=t;return e.zone=H.utcInstance,Vh({year:n,month:r,day:i,hour:o,minute:a,second:l,millisecond:c},e)}static fromJSDate(e,t={}){let n=rh(e)?e.valueOf():NaN;if(Number.isNaN(n))return s.invalid("invalid input");let r=Ee(t.zone,x.defaultZone);return r.isValid?new s({ts:n,zone:r,loc:_.fromObject(t)}):s.invalid(fn(r))}static fromMillis(e,t={}){if(ve(e))return e<-qh||e>qh?s.invalid("Timestamp out of range"):new s({ts:e,zone:Ee(t.zone,x.defaultZone),loc:_.fromObject(t)});throw new B(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,t={}){if(ve(e))return new s({ts:e*1e3,zone:Ee(t.zone,x.defaultZone),loc:_.fromObject(t)});throw new B("fromSeconds requires a numerical input")}static fromObject(e,t={}){e=e||{};let n=Ee(t.zone,x.defaultZone);if(!n.isValid)return s.invalid(fn(n));let r=_.fromObject(t),i=Wt(e,Rh),{minDaysInFirstWeek:o,startOfWeek:a}=Ta(i,r),l=x.now(),c=A(t.specificOffset)?n.offset(l):t.specificOffset,u=!A(i.ordinal),f=!A(i.year),h=!A(i.month)||!A(i.day),d=f||h,g=i.weekYear||i.weekNumber;if((d||u)&&g)throw new Te("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(h&&u)throw new Te("Can't mix ordinal dates with month/day");let m=g||i.weekday&&!d,p,w,k=$r(l,c);m?(p=LS,w=CS,k=sn(k,o,a)):u?(p=FS,w=MS,k=qr(k)):(p=Hh,w=Yh);let E=!1;for(let Z of p){let ue=i[Z];A(ue)?E?i[Z]=w[Z]:i[Z]=k[Z]:E=!0}let v=m?th(i,o,a):u?sh(i):ba(i),O=v||Na(i);if(O)return s.invalid(O);let I=m?wa(i,o,a):u?ka(i):i,[C,T]=Vr(I,c,n),M=new s({ts:C,zone:n,o:T,loc:r});return i.weekday&&d&&e.weekday!==M.weekday?s.invalid("mismatched weekday",`you can't specify both a weekday of ${i.weekday} and a date of ${M.toISO()}`):M.isValid?M:s.invalid(M.invalid)}static fromISO(e,t={}){let[n,r]=wh(e);return Qt(n,r,t,"ISO 8601",e)}static fromRFC2822(e,t={}){let[n,r]=kh(e);return Qt(n,r,t,"RFC 2822",e)}static fromHTTP(e,t={}){let[n,r]=Th(e);return Qt(n,r,t,"HTTP",t)}static fromFormat(e,t,n={}){if(A(e)||A(t))throw new B("fromFormat requires an input string and a format");let{locale:r=null,numberingSystem:i=null}=n,o=_.fromOpts({locale:r,numberingSystem:i,defaultToEN:!0}),[a,l,c,u]=_h(o,e,t);return u?s.invalid(u):Qt(a,l,n,`format ${t}`,e,c)}static fromString(e,t,n={}){return s.fromFormat(e,t,n)}static fromSQL(e,t={}){let[n,r]=Eh(e);return Qt(n,r,t,"SQL",e)}static invalid(e,t=null){if(!e)throw new B("need to specify a reason the DateTime is invalid");let n=e instanceof K?e:new K(e,t);if(x.throwOnInvalid)throw new Er(n);return new s({invalid:n})}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,t={}){let n=_a(e,_.fromObject(t));return n?n.map(r=>r?r.val:null).join(""):null}static expandFormat(e,t={}){return Fa(j.parseFormat(e),_.fromObject(t)).map(r=>r.val).join("")}static resetCache(){Br=void 0,Ur={}}get(e){return this[e]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?Pa(this).weekYear:NaN}get weekNumber(){return this.isValid?Pa(this).weekNumber:NaN}get weekday(){return this.isValid?Pa(this).weekday:NaN}get isWeekend(){return this.isValid&&this.loc.getWeekendDays().includes(this.weekday)}get localWeekday(){return this.isValid?$a(this).weekday:NaN}get localWeekNumber(){return this.isValid?$a(this).weekNumber:NaN}get localWeekYear(){return this.isValid?$a(this).weekYear:NaN}get ordinal(){return this.isValid?qr(this.c).ordinal:NaN}get monthShort(){return this.isValid?Pe.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?Pe.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?Pe.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?Pe.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];let e=864e5,t=6e4,n=$t(this.c),r=this.zone.offset(n-e),i=this.zone.offset(n+e),o=this.zone.offset(n-r*t),a=this.zone.offset(n-i*t);if(o===a)return[this];let l=n-o*t,c=n-a*t,u=$r(l,o),f=$r(c,a);return u.hour===f.hour&&u.minute===f.minute&&u.second===f.second&&u.millisecond===f.millisecond?[kt(this,{ts:l}),kt(this,{ts:c})]:[this]}get isInLeapYear(){return St(this.year)}get daysInMonth(){return Ut(this.year,this.month)}get daysInYear(){return this.isValid?tt(this.year):NaN}get weeksInWeekYear(){return this.isValid?yt(this.weekYear):NaN}get weeksInLocalWeekYear(){return this.isValid?yt(this.localWeekYear,this.loc.getMinDaysInFirstWeek(),this.loc.getStartOfWeek()):NaN}resolvedLocaleOptions(e={}){let{locale:t,numberingSystem:n,calendar:r}=j.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:t,numberingSystem:n,outputCalendar:r}}toUTC(e=0,t={}){return this.setZone(H.instance(e),t)}toLocal(){return this.setZone(x.defaultZone)}setZone(e,{keepLocalTime:t=!1,keepCalendarTime:n=!1}={}){if(e=Ee(e,x.defaultZone),e.equals(this.zone))return this;if(e.isValid){let r=this.ts;if(t||n){let i=e.offset(this.ts),o=this.toObject();[r]=Vr(o,i,e)}return kt(this,{ts:r,zone:e})}else return s.invalid(fn(e))}reconfigure({locale:e,numberingSystem:t,outputCalendar:n}={}){let r=this.loc.clone({locale:e,numberingSystem:t,outputCalendar:n});return kt(this,{loc:r})}setLocale(e){return this.reconfigure({locale:e})}set(e){if(!this.isValid)return this;let t=Wt(e,Rh),{minDaysInFirstWeek:n,startOfWeek:r}=Ta(t,this.loc),i=!A(t.weekYear)||!A(t.weekNumber)||!A(t.weekday),o=!A(t.ordinal),a=!A(t.year),l=!A(t.month)||!A(t.day),c=a||l,u=t.weekYear||t.weekNumber;if((c||o)&&u)throw new Te("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(l&&o)throw new Te("Can't mix ordinal dates with month/day");let f;i?f=wa({...sn(this.c,n,r),...t},n,r):A(t.ordinal)?(f={...this.toObject(),...t},A(t.day)&&(f.day=Math.min(Ut(f.year,f.month),f.day))):f=ka({...qr(this.c),...t});let[h,d]=Vr(f,this.o,this.zone);return kt(this,{ts:h,o:d})}plus(e){if(!this.isValid)return this;let t=V.fromDurationLike(e);return kt(this,Ph(this,t))}minus(e){if(!this.isValid)return this;let t=V.fromDurationLike(e).negate();return kt(this,Ph(this,t))}startOf(e,{useLocaleWeeks:t=!1}={}){if(!this.isValid)return this;let n={},r=V.normalizeUnit(e);switch(r){case"years":n.month=1;case"quarters":case"months":n.day=1;case"weeks":case"days":n.hour=0;case"hours":n.minute=0;case"minutes":n.second=0;case"seconds":n.millisecond=0;break;case"milliseconds":break}if(r==="weeks")if(t){let i=this.loc.getStartOfWeek(),{weekday:o}=this;o<i&&(n.weekNumber=this.weekNumber-1),n.weekday=i}else n.weekday=1;if(r==="quarters"){let i=Math.ceil(this.month/3);n.month=(i-1)*3+1}return this.set(n)}endOf(e,t){return this.isValid?this.plus({[e]:1}).startOf(e,t).minus(1):this}toFormat(e,t={}){return this.isValid?j.create(this.loc.redefaultToEN(t)).formatDateTimeFromString(this,e):qa}toLocaleString(e=Qe,t={}){return this.isValid?j.create(this.loc.clone(t),e).formatDateTime(this):qa}toLocaleParts(e={}){return this.isValid?j.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:t=!1,suppressMilliseconds:n=!1,includeOffset:r=!0,extendedZone:i=!1}={}){if(!this.isValid)return null;let o=e==="extended",a=Ra(this,o);return a+="T",a+=$h(this,o,t,n,r,i),a}toISODate({format:e="extended"}={}){return this.isValid?Ra(this,e==="extended"):null}toISOWeekDate(){return Rr(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:t=!1,includeOffset:n=!0,includePrefix:r=!1,extendedZone:i=!1,format:o="extended"}={}){return this.isValid?(r?"T":"")+$h(this,o==="extended",t,e,n,i):null}toRFC2822(){return Rr(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return Rr(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?Ra(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:t=!1,includeOffsetSpace:n=!0}={}){let r="HH:mm:ss.SSS";return(t||e)&&(n&&(r+=" "),t?r+="z":e&&(r+="ZZ")),Rr(this,r,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():qa}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`:`DateTime { Invalid, reason: ${this.invalidReason} }`}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};let t={...this.c};return e.includeConfig&&(t.outputCalendar=this.outputCalendar,t.numberingSystem=this.loc.numberingSystem,t.locale=this.loc.locale),t}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,t="milliseconds",n={}){if(!this.isValid||!e.isValid)return V.invalid("created by diffing an invalid DateTime");let r={locale:this.locale,numberingSystem:this.numberingSystem,...n},i=ih(t).map(V.normalizeUnit),o=e.valueOf()>this.valueOf(),a=o?this:e,l=o?e:this,c=Ch(a,l,i,r);return o?c.negate():c}diffNow(e="milliseconds",t={}){return this.diff(s.now(),e,t)}until(e){return this.isValid?qe.fromDateTimes(this,e):this}hasSame(e,t,n){if(!this.isValid)return!1;let r=e.valueOf(),i=this.setZone(e.zone,{keepLocalTime:!0});return i.startOf(t,n)<=r&&r<=i.endOf(t,n)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;let t=e.base||s.fromObject({},{zone:this.zone}),n=e.padding?this<t?-e.padding:e.padding:0,r=["years","months","days","hours","minutes","seconds"],i=e.unit;return Array.isArray(e.unit)&&(r=e.unit,i=void 0),Bh(t,this.plus(n),{...e,numeric:"always",units:r,unit:i})}toRelativeCalendar(e={}){return this.isValid?Bh(e.base||s.fromObject({},{zone:this.zone}),this,{...e,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...e){if(!e.every(s.isDateTime))throw new B("min requires all arguments be DateTimes");return Ea(e,t=>t.valueOf(),Math.min)}static max(...e){if(!e.every(s.isDateTime))throw new B("max requires all arguments be DateTimes");return Ea(e,t=>t.valueOf(),Math.max)}static fromFormatExplain(e,t,n={}){let{locale:r=null,numberingSystem:i=null}=n,o=_.fromOpts({locale:r,numberingSystem:i,defaultToEN:!0});return xa(o,e,t)}static fromStringExplain(e,t,n={}){return s.fromFormatExplain(e,t,n)}static buildFormatParser(e,t={}){let{locale:n=null,numberingSystem:r=null}=t,i=_.fromOpts({locale:n,numberingSystem:r,defaultToEN:!0});return new un(i,e)}static fromFormatParser(e,t,n={}){if(A(e)||A(t))throw new B("fromFormatParser requires an input string and a format parser");let{locale:r=null,numberingSystem:i=null}=n,o=_.fromOpts({locale:r,numberingSystem:i,defaultToEN:!0});if(!o.equals(t.locale))throw new B(`fromFormatParser called with a locale of ${o}, but the format parser was created for ${t.locale}`);let{result:a,zone:l,specificOffset:c,invalidReason:u}=t.explainFromTokens(e);return u?s.invalid(u):Qt(a,l,n,`format ${t.format}`,e,c)}static get DATE_SHORT(){return Qe}static get DATE_MED(){return _s}static get DATE_MED_WITH_WEEKDAY(){return sa}static get DATE_FULL(){return qs}static get DATE_HUGE(){return Ps}static get TIME_SIMPLE(){return $s}static get TIME_WITH_SECONDS(){return Rs}static get TIME_WITH_SHORT_OFFSET(){return Vs}static get TIME_WITH_LONG_OFFSET(){return Bs}static get TIME_24_SIMPLE(){return Us}static get TIME_24_WITH_SECONDS(){return Ws}static get TIME_24_WITH_SHORT_OFFSET(){return Ys}static get TIME_24_WITH_LONG_OFFSET(){return Hs}static get DATETIME_SHORT(){return Ks}static get DATETIME_SHORT_WITH_SECONDS(){return js}static get DATETIME_MED(){return Zs}static get DATETIME_MED_WITH_SECONDS(){return Js}static get DATETIME_MED_WITH_WEEKDAY(){return na}static get DATETIME_FULL(){return Gs}static get DATETIME_FULL_WITH_SECONDS(){return zs}static get DATETIME_HUGE(){return Qs}static get DATETIME_HUGE_WITH_SECONDS(){return Xs}};function zt(s){if(F.isDateTime(s))return s;if(s&&s.valueOf&&ve(s.valueOf()))return F.fromJSDate(s);if(s&&typeof s=="object")return F.fromObject(s);throw new B(`Unknown datetime argument: ${s}, of type ${typeof s}`)}var Kh=require("@raycast/api");async function Xt(s,e=""){let t=new Date,n=F.now(),r=await Sl(t),i=t.getHours().toString().padStart(2,"0"),o=t.getMinutes().toString().padStart(2,"0"),a=t.getSeconds().toString().padStart(2,"0"),l=Date.now().toString(),c=await Kh.Clipboard.readText()||"",u=await wl()||"";return(e.includes("{content}")?e:e+s).replaceAll("{content}",s).replaceAll(/{.*?}/g,h=>{let d=h.slice(1,-1);switch(d){case"S":case"u":case"SSS":case"s":case"ss":case"uu":case"uuu":case"m":case"mm":case"h":case"hh":case"H":case"HH":case"Z":case"ZZ":case"ZZZ":case"ZZZZ":case"ZZZZZ":case"z":case"a":case"d":case"dd":case"c":case"ccc":case"cccc":case"ccccc":case"E":case"EEE":case"EEEE":case"EEEEE":case"L":case"LL":case"LLL":case"LLLL":case"LLLLL":case"M":case"MM":case"MMM":case"MMMM":case"MMMMM":case"y":case"yy":case"yyyy":case"yyyyyy":case"G":case"GG":case"GGGGG":case"kk":case"kkkk":case"W":case"WW":case"n":case"nn":case"ii":case"iiii":case"o":case"ooo":case"q":case"qq":case"X":case"x":return n.toFormat(d);case"content":return s;case"time":return t.toLocaleTimeString();case"date":return t.toLocaleDateString();case"week":return r.toString().padStart(2,"0");case"year":return t.getFullYear().toString();case"month":return hl[t.getMonth()];case"day":return fl[t.getDay()];case"hour":return i;case"minute":return o;case"second":return a;case"millisecond":return t.getMilliseconds().toString();case"timestamp":return l;case"zettelkastenID":return l;case"clipboard":return c;case"clip":return c;case"selection":return u;case"selected":return u;case`
`:return`
`;case"newline":return`
`;case"nl":return`
`;default:return h}})}async function jh(s){let{appendSelectedTemplate:e}=(0,G.getPreferenceValues)();e=e||"{content}";try{let t=await(0,G.getSelectedText)();if(t.trim()=="")(0,G.showToast)({title:"No text selected",message:"Make sure to select some text.",style:G.Toast.Style.Failure});else{let n=e.replaceAll("{content}",t);return n=await Xt(n),Wr.default.appendFileSync(s.path,`
`+n),(0,G.showToast)({title:"Added selected text to note",style:G.Toast.Style.Success}),!0}}catch{(0,G.showToast)({title:"Couldn't copy selected text",message:"Maybe you didn't select anything.",style:G.Toast.Style.Failure})}}function Zh(s){let e=s.matchAll(kn),t=[];for(let n of e){let[,r,i]=n;t.push({language:r,code:i})}return t}function Jh(s){if(Wr.default.existsSync(s.path))return Wr.default.unlinkSync(s.path),(0,G.showToast)({title:"Deleted Note",style:G.Toast.Style.Success}),!0}function Gh(s,e){switch(e.type){case 0:return console.log("REDUCER SET"),e.payload;case 1:{console.log("REDUCER DELETE");let t=s.filter(n=>n.path!==e.payload.note.path);return Jh(e.payload.note),If(e.payload.vault,e.payload.note),t}case 2:return console.log("REDUCER BOOKMARK"),Sf(e.payload.vault,e.payload.note),s.map(t=>(t.path===e.payload.note.path&&(t.bookmarked=!0,xs(e.payload.vault,t)),t));case 3:return console.log("REDUCER UNBOOKMARK"),wf(e.payload.vault,e.payload.note),s.map(t=>(t.path===e.payload.note.path&&(t.bookmarked=!1,xs(e.payload.vault,t)),t));case 4:{console.log("REDUCER UPDATE");let t=zo(e.payload.note.path);console.log(t),e.payload.note.content=t;let n=wr([e.payload.note]);return e.payload.note.tags=n,xs(e.payload.vault,e.payload.note),s.map(r=>r.path===e.payload.note.path?e.payload.note:r)}case 5:return console.log("REDUCER ADD"),xs(e.payload.vault,e.payload.note),[...s,e.payload.note];default:return s}}var es=require("react/jsx-runtime");function zh(s){let e=br(),t=De(),{tags:n,searchArguments:r}=s,[i,o]=(0,Yr.useState)(()=>r.tagArgument?r.tagArgument.startsWith("#")?r.tagArgument:"#"+r.tagArgument:"all");(0,Yr.useEffect)(()=>{e&&t(i!=="all"?{type:0,payload:e.filter(l=>l.tags.includes(i))}:{type:0,payload:e})},[i,e,t]);function a(l){o(l)}return(0,es.jsxs)(hn.List.Dropdown,{tooltip:"Search For",value:i,onChange:a,children:[(0,es.jsx)(hn.List.Dropdown.Item,{title:"All",value:"all"}),(0,es.jsx)(hn.List.Dropdown.Section,{title:"Tags"}),n.map(l=>(0,es.jsx)(hn.List.Dropdown.Item,{title:l,value:l},l))]})}function $e(s){return Array.isArray?Array.isArray(s):id(s)==="[object Array]"}var qS=1/0;function PS(s){if(typeof s=="string")return s;let e=s+"";return e=="0"&&1/s==-qS?"-0":e}function $S(s){return s==null?"":PS(s)}function Ce(s){return typeof s=="string"}function nd(s){return typeof s=="number"}function RS(s){return s===!0||s===!1||VS(s)&&id(s)=="[object Boolean]"}function rd(s){return typeof s=="object"}function VS(s){return rd(s)&&s!==null}function ce(s){return s!=null}function Ba(s){return!s.trim().length}function id(s){return s==null?s===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(s)}var BS="Incorrect 'index' type",US=s=>`Invalid value for key ${s}`,WS=s=>`Pattern length exceeds max of ${s}.`,YS=s=>`Missing ${s} property in key`,HS=s=>`Property 'weight' in key '${s}' must be a positive integer`,Qh=Object.prototype.hasOwnProperty,Ua=class{constructor(e){this._keys=[],this._keyMap={};let t=0;e.forEach(n=>{let r=od(n);this._keys.push(r),this._keyMap[r.id]=r,t+=r.weight}),this._keys.forEach(n=>{n.weight/=t})}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}};function od(s){let e=null,t=null,n=null,r=1,i=null;if(Ce(s)||$e(s))n=s,e=Xh(s),t=Wa(s);else{if(!Qh.call(s,"name"))throw new Error(YS("name"));let o=s.name;if(n=o,Qh.call(s,"weight")&&(r=s.weight,r<=0))throw new Error(HS(o));e=Xh(o),t=Wa(o),i=s.getFn}return{path:e,id:t,weight:r,src:n,getFn:i}}function Xh(s){return $e(s)?s:s.split(".")}function Wa(s){return $e(s)?s.join("."):s}function KS(s,e){let t=[],n=!1,r=(i,o,a)=>{if(ce(i))if(!o[a])t.push(i);else{let l=o[a],c=i[l];if(!ce(c))return;if(a===o.length-1&&(Ce(c)||nd(c)||RS(c)))t.push($S(c));else if($e(c)){n=!0;for(let u=0,f=c.length;u<f;u+=1)r(c[u],o,a+1)}else o.length&&r(c,o,a+1)}};return r(s,Ce(e)?e.split("."):e,0),n?t:t[0]}var jS={includeMatches:!1,findAllMatches:!1,minMatchCharLength:1},ZS={isCaseSensitive:!1,ignoreDiacritics:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(s,e)=>s.score===e.score?s.idx<e.idx?-1:1:s.score<e.score?-1:1},JS={location:0,threshold:.6,distance:100},GS={useExtendedSearch:!1,getFn:KS,ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1},D={...ZS,...jS,...JS,...GS},zS=/[^ ]+/g;function QS(s=1,e=3){let t=new Map,n=Math.pow(10,e);return{get(r){let i=r.match(zS).length;if(t.has(i))return t.get(i);let o=1/Math.pow(i,.5*s),a=parseFloat(Math.round(o*n)/n);return t.set(i,a),a},clear(){t.clear()}}}var dn=class{constructor({getFn:e=D.getFn,fieldNormWeight:t=D.fieldNormWeight}={}){this.norm=QS(t,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach((t,n)=>{this._keysMap[t.id]=n})}create(){this.isCreated||!this.docs.length||(this.isCreated=!0,Ce(this.docs[0])?this.docs.forEach((e,t)=>{this._addString(e,t)}):this.docs.forEach((e,t)=>{this._addObject(e,t)}),this.norm.clear())}add(e){let t=this.size();Ce(e)?this._addString(e,t):this._addObject(e,t)}removeAt(e){this.records.splice(e,1);for(let t=e,n=this.size();t<n;t+=1)this.records[t].i-=1}getValueForItemAtKeyId(e,t){return e[this._keysMap[t]]}size(){return this.records.length}_addString(e,t){if(!ce(e)||Ba(e))return;let n={v:e,i:t,n:this.norm.get(e)};this.records.push(n)}_addObject(e,t){let n={i:t,$:{}};this.keys.forEach((r,i)=>{let o=r.getFn?r.getFn(e):this.getFn(e,r.path);if(ce(o)){if($e(o)){let a=[],l=[{nestedArrIndex:-1,value:o}];for(;l.length;){let{nestedArrIndex:c,value:u}=l.pop();if(ce(u))if(Ce(u)&&!Ba(u)){let f={v:u,i:c,n:this.norm.get(u)};a.push(f)}else $e(u)&&u.forEach((f,h)=>{l.push({nestedArrIndex:h,value:f})})}n.$[i]=a}else if(Ce(o)&&!Ba(o)){let a={v:o,n:this.norm.get(o)};n.$[i]=a}}}),this.records.push(n)}toJSON(){return{keys:this.keys,records:this.records}}};function ad(s,e,{getFn:t=D.getFn,fieldNormWeight:n=D.fieldNormWeight}={}){let r=new dn({getFn:t,fieldNormWeight:n});return r.setKeys(s.map(od)),r.setSources(e),r.create(),r}function XS(s,{getFn:e=D.getFn,fieldNormWeight:t=D.fieldNormWeight}={}){let{keys:n,records:r}=s,i=new dn({getFn:e,fieldNormWeight:t});return i.setKeys(n),i.setIndexRecords(r),i}function Hr(s,{errors:e=0,currentLocation:t=0,expectedLocation:n=0,distance:r=D.distance,ignoreLocation:i=D.ignoreLocation}={}){let o=e/s.length;if(i)return o;let a=Math.abs(n-t);return r?o+a/r:a?1:o}function ew(s=[],e=D.minMatchCharLength){let t=[],n=-1,r=-1,i=0;for(let o=s.length;i<o;i+=1){let a=s[i];a&&n===-1?n=i:!a&&n!==-1&&(r=i-1,r-n+1>=e&&t.push([n,r]),n=-1)}return s[i-1]&&i-n>=e&&t.push([n,i-1]),t}var Tt=32;function tw(s,e,t,{location:n=D.location,distance:r=D.distance,threshold:i=D.threshold,findAllMatches:o=D.findAllMatches,minMatchCharLength:a=D.minMatchCharLength,includeMatches:l=D.includeMatches,ignoreLocation:c=D.ignoreLocation}={}){if(e.length>Tt)throw new Error(WS(Tt));let u=e.length,f=s.length,h=Math.max(0,Math.min(n,f)),d=i,g=h,m=a>1||l,p=m?Array(f):[],w;for(;(w=s.indexOf(e,g))>-1;){let C=Hr(e,{currentLocation:w,expectedLocation:h,distance:r,ignoreLocation:c});if(d=Math.min(C,d),g=w+u,m){let T=0;for(;T<u;)p[w+T]=1,T+=1}}g=-1;let k=[],E=1,v=u+f,O=1<<u-1;for(let C=0;C<u;C+=1){let T=0,M=v;for(;T<M;)Hr(e,{errors:C,currentLocation:h+M,expectedLocation:h,distance:r,ignoreLocation:c})<=d?T=M:v=M,M=Math.floor((v-T)/2+T);v=M;let Z=Math.max(1,h-M+1),ue=o?f:Math.min(h+M,f)+u,$=Array(ue+2);$[ue+1]=(1<<C)-1;for(let ge=ue;ge>=Z;ge-=1){let Sn=ge-1,il=t[s.charAt(Sn)];if(m&&(p[Sn]=+!!il),$[ge]=($[ge+1]<<1|1)&il,C&&($[ge]|=(k[ge+1]|k[ge])<<1|1|k[ge+1]),$[ge]&O&&(E=Hr(e,{errors:C,currentLocation:Sn,expectedLocation:h,distance:r,ignoreLocation:c}),E<=d)){if(d=E,g=Sn,g<=h)break;Z=Math.max(1,2*h-g)}}if(Hr(e,{errors:C+1,currentLocation:h,expectedLocation:h,distance:r,ignoreLocation:c})>d)break;k=$}let I={isMatch:g>=0,score:Math.max(.001,E)};if(m){let C=ew(p,a);C.length?l&&(I.indices=C):I.isMatch=!1}return I}function sw(s){let e={};for(let t=0,n=s.length;t<n;t+=1){let r=s.charAt(t);e[r]=(e[r]||0)|1<<n-t-1}return e}var Kr=String.prototype.normalize?s=>s.normalize("NFD").replace(/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C04\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F]/g,""):s=>s,jr=class{constructor(e,{location:t=D.location,threshold:n=D.threshold,distance:r=D.distance,includeMatches:i=D.includeMatches,findAllMatches:o=D.findAllMatches,minMatchCharLength:a=D.minMatchCharLength,isCaseSensitive:l=D.isCaseSensitive,ignoreDiacritics:c=D.ignoreDiacritics,ignoreLocation:u=D.ignoreLocation}={}){if(this.options={location:t,threshold:n,distance:r,includeMatches:i,findAllMatches:o,minMatchCharLength:a,isCaseSensitive:l,ignoreDiacritics:c,ignoreLocation:u},e=l?e:e.toLowerCase(),e=c?Kr(e):e,this.pattern=e,this.chunks=[],!this.pattern.length)return;let f=(d,g)=>{this.chunks.push({pattern:d,alphabet:sw(d),startIndex:g})},h=this.pattern.length;if(h>Tt){let d=0,g=h%Tt,m=h-g;for(;d<m;)f(this.pattern.substr(d,Tt),d),d+=Tt;if(g){let p=h-Tt;f(this.pattern.substr(p),p)}}else f(this.pattern,0)}searchIn(e){let{isCaseSensitive:t,ignoreDiacritics:n,includeMatches:r}=this.options;if(e=t?e:e.toLowerCase(),e=n?Kr(e):e,this.pattern===e){let m={isMatch:!0,score:0};return r&&(m.indices=[[0,e.length-1]]),m}let{location:i,distance:o,threshold:a,findAllMatches:l,minMatchCharLength:c,ignoreLocation:u}=this.options,f=[],h=0,d=!1;this.chunks.forEach(({pattern:m,alphabet:p,startIndex:w})=>{let{isMatch:k,score:E,indices:v}=tw(e,m,p,{location:i+w,distance:o,threshold:a,findAllMatches:l,minMatchCharLength:c,includeMatches:r,ignoreLocation:u});k&&(d=!0),h+=E,k&&v&&(f=[...f,...v])});let g={isMatch:d,score:d?h/this.chunks.length:1};return d&&r&&(g.indices=f),g}},Me=class{constructor(e){this.pattern=e}static isMultiMatch(e){return ed(e,this.multiRegex)}static isSingleMatch(e){return ed(e,this.singleRegex)}search(){}};function ed(s,e){let t=s.match(e);return t?t[1]:null}var Ya=class extends Me{constructor(e){super(e)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(e){let t=e===this.pattern;return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},Ha=class extends Me{constructor(e){super(e)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(e){let n=e.indexOf(this.pattern)===-1;return{isMatch:n,score:n?0:1,indices:[0,e.length-1]}}},Ka=class extends Me{constructor(e){super(e)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(e){let t=e.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},ja=class extends Me{constructor(e){super(e)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(e){let t=!e.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},Za=class extends Me{constructor(e){super(e)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(e){let t=e.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[e.length-this.pattern.length,e.length-1]}}},Ja=class extends Me{constructor(e){super(e)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(e){let t=!e.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},Zr=class extends Me{constructor(e,{location:t=D.location,threshold:n=D.threshold,distance:r=D.distance,includeMatches:i=D.includeMatches,findAllMatches:o=D.findAllMatches,minMatchCharLength:a=D.minMatchCharLength,isCaseSensitive:l=D.isCaseSensitive,ignoreDiacritics:c=D.ignoreDiacritics,ignoreLocation:u=D.ignoreLocation}={}){super(e),this._bitapSearch=new jr(e,{location:t,threshold:n,distance:r,includeMatches:i,findAllMatches:o,minMatchCharLength:a,isCaseSensitive:l,ignoreDiacritics:c,ignoreLocation:u})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}},Jr=class extends Me{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let t=0,n,r=[],i=this.pattern.length;for(;(n=e.indexOf(this.pattern,t))>-1;)t=n+i,r.push([n,t-1]);let o=!!r.length;return{isMatch:o,score:o?0:1,indices:r}}},Ga=[Ya,Jr,Ka,ja,Ja,Za,Ha,Zr],td=Ga.length,nw=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,rw="|";function iw(s,e={}){return s.split(rw).map(t=>{let n=t.trim().split(nw).filter(i=>i&&!!i.trim()),r=[];for(let i=0,o=n.length;i<o;i+=1){let a=n[i],l=!1,c=-1;for(;!l&&++c<td;){let u=Ga[c],f=u.isMultiMatch(a);f&&(r.push(new u(f,e)),l=!0)}if(!l)for(c=-1;++c<td;){let u=Ga[c],f=u.isSingleMatch(a);if(f){r.push(new u(f,e));break}}}return r})}var ow=new Set([Zr.type,Jr.type]),za=class{constructor(e,{isCaseSensitive:t=D.isCaseSensitive,ignoreDiacritics:n=D.ignoreDiacritics,includeMatches:r=D.includeMatches,minMatchCharLength:i=D.minMatchCharLength,ignoreLocation:o=D.ignoreLocation,findAllMatches:a=D.findAllMatches,location:l=D.location,threshold:c=D.threshold,distance:u=D.distance}={}){this.query=null,this.options={isCaseSensitive:t,ignoreDiacritics:n,includeMatches:r,minMatchCharLength:i,findAllMatches:a,ignoreLocation:o,location:l,threshold:c,distance:u},e=t?e:e.toLowerCase(),e=n?Kr(e):e,this.pattern=e,this.query=iw(this.pattern,this.options)}static condition(e,t){return t.useExtendedSearch}searchIn(e){let t=this.query;if(!t)return{isMatch:!1,score:1};let{includeMatches:n,isCaseSensitive:r,ignoreDiacritics:i}=this.options;e=r?e:e.toLowerCase(),e=i?Kr(e):e;let o=0,a=[],l=0;for(let c=0,u=t.length;c<u;c+=1){let f=t[c];a.length=0,o=0;for(let h=0,d=f.length;h<d;h+=1){let g=f[h],{isMatch:m,indices:p,score:w}=g.search(e);if(m){if(o+=1,l+=w,n){let k=g.constructor.type;ow.has(k)?a=[...a,...p]:a.push(p)}}else{l=0,o=0,a.length=0;break}}if(o){let h={isMatch:!0,score:l/o};return n&&(h.indices=a),h}}return{isMatch:!1,score:1}}},Qa=[];function aw(...s){Qa.push(...s)}function Xa(s,e){for(let t=0,n=Qa.length;t<n;t+=1){let r=Qa[t];if(r.condition(s,e))return new r(s,e)}return new jr(s,e)}var Gr={AND:"$and",OR:"$or"},el={PATH:"$path",PATTERN:"$val"},tl=s=>!!(s[Gr.AND]||s[Gr.OR]),lw=s=>!!s[el.PATH],cw=s=>!$e(s)&&rd(s)&&!tl(s),sd=s=>({[Gr.AND]:Object.keys(s).map(e=>({[e]:s[e]}))});function ld(s,e,{auto:t=!0}={}){let n=r=>{let i=Object.keys(r),o=lw(r);if(!o&&i.length>1&&!tl(r))return n(sd(r));if(cw(r)){let l=o?r[el.PATH]:i[0],c=o?r[el.PATTERN]:r[l];if(!Ce(c))throw new Error(US(l));let u={keyId:Wa(l),pattern:c};return t&&(u.searcher=Xa(c,e)),u}let a={children:[],operator:i[0]};return i.forEach(l=>{let c=r[l];$e(c)&&c.forEach(u=>{a.children.push(n(u))})}),a};return tl(s)||(s=sd(s)),n(s)}function uw(s,{ignoreFieldNorm:e=D.ignoreFieldNorm}){s.forEach(t=>{let n=1;t.matches.forEach(({key:r,norm:i,score:o})=>{let a=r?r.weight:null;n*=Math.pow(o===0&&a?Number.EPSILON:o,(a||1)*(e?1:i))}),t.score=n})}function fw(s,e){let t=s.matches;e.matches=[],ce(t)&&t.forEach(n=>{if(!ce(n.indices)||!n.indices.length)return;let{indices:r,value:i}=n,o={indices:r,value:i};n.key&&(o.key=n.key.src),n.idx>-1&&(o.refIndex=n.idx),e.matches.push(o)})}function hw(s,e){e.score=s.score}function dw(s,e,{includeMatches:t=D.includeMatches,includeScore:n=D.includeScore}={}){let r=[];return t&&r.push(fw),n&&r.push(hw),s.map(i=>{let{idx:o}=i,a={item:e[o],refIndex:o};return r.length&&r.forEach(l=>{l(i,a)}),a})}var Re=class{constructor(e,t={},n){this.options={...D,...t},this.options.useExtendedSearch,this._keyStore=new Ua(this.options.keys),this.setCollection(e,n)}setCollection(e,t){if(this._docs=e,t&&!(t instanceof dn))throw new Error(BS);this._myIndex=t||ad(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){ce(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){let t=[];for(let n=0,r=this._docs.length;n<r;n+=1){let i=this._docs[n];e(i,n)&&(this.removeAt(n),n-=1,r-=1,t.push(i))}return t}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:t=-1}={}){let{includeMatches:n,includeScore:r,shouldSort:i,sortFn:o,ignoreFieldNorm:a}=this.options,l=Ce(e)?Ce(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return uw(l,{ignoreFieldNorm:a}),i&&l.sort(o),nd(t)&&t>-1&&(l=l.slice(0,t)),dw(l,this._docs,{includeMatches:n,includeScore:r})}_searchStringList(e){let t=Xa(e,this.options),{records:n}=this._myIndex,r=[];return n.forEach(({v:i,i:o,n:a})=>{if(!ce(i))return;let{isMatch:l,score:c,indices:u}=t.searchIn(i);l&&r.push({item:i,idx:o,matches:[{score:c,value:i,norm:a,indices:u}]})}),r}_searchLogical(e){let t=ld(e,this.options),n=(a,l,c)=>{if(!a.children){let{keyId:f,searcher:h}=a,d=this._findMatches({key:this._keyStore.get(f),value:this._myIndex.getValueForItemAtKeyId(l,f),searcher:h});return d&&d.length?[{idx:c,item:l,matches:d}]:[]}let u=[];for(let f=0,h=a.children.length;f<h;f+=1){let d=a.children[f],g=n(d,l,c);if(g.length)u.push(...g);else if(a.operator===Gr.AND)return[]}return u},r=this._myIndex.records,i={},o=[];return r.forEach(({$:a,i:l})=>{if(ce(a)){let c=n(t,a,l);c.length&&(i[l]||(i[l]={idx:l,item:a,matches:[]},o.push(i[l])),c.forEach(({matches:u})=>{i[l].matches.push(...u)}))}}),o}_searchObjectList(e){let t=Xa(e,this.options),{keys:n,records:r}=this._myIndex,i=[];return r.forEach(({$:o,i:a})=>{if(!ce(o))return;let l=[];n.forEach((c,u)=>{l.push(...this._findMatches({key:c,value:o[u],searcher:t}))}),l.length&&i.push({idx:a,item:o,matches:l})}),i}_findMatches({key:e,value:t,searcher:n}){if(!ce(t))return[];let r=[];if($e(t))t.forEach(({v:i,i:o,n:a})=>{if(!ce(i))return;let{isMatch:l,score:c,indices:u}=n.searchIn(i);l&&r.push({score:c,key:e,value:i,idx:o,norm:a,indices:u})});else{let{v:i,n:o}=t,{isMatch:a,score:l,indices:c}=n.searchIn(i);a&&r.push({score:l,key:e,value:i,norm:o,indices:c})}return r}};Re.version="7.1.0";Re.createIndex=ad;Re.parseIndex=XS;Re.config=D;Re.parseQuery=ld;aw(za);function cd(s,e,t){return e.length===0?s:(e=e.toLowerCase(),t?s.filter(n=>n.content.toLowerCase().includes(e)||n.title.toLowerCase().includes(e)||n.path.toLowerCase().includes(e)):s.filter(n=>n.title.toLowerCase().includes(e)))}function ud(s,e,t){if(e.length===0)return s;let n={keys:["title","path"],fieldNormWeight:2,ignoreLocation:!0,threshold:.3};t&&n.keys.push("content");let r=e.trim().split(/\s+/),i=s,o=new Re(s,n);for(let a of r)i=o.search(a).map(l=>l.item),o.setCollection(i);return i}var it=require("react/jsx-runtime");function Qr(s){let{notes:e,vault:t,title:n,searchArguments:r,action:i}=s,o=(0,pe.getPreferenceValues)(),a=br(),[l,c]=(0,zr.useState)(r.searchArgument??""),u=o.fuzzySearch?ud:cd,h=(0,zr.useMemo)(()=>u(e??[],l,o.searchContent),[e,l]).slice(0,al),d=wr(a);function g(){let p=vt({type:"obsidian://new?vault=",vault:t,name:l});(0,pe.open)(p)}let m=e===void 0;return h.length==0?(0,it.jsx)(pe.List,{navigationTitle:n,onSearchTextChange:p=>{c(p)},children:(0,it.jsx)(pe.List.Item,{title:`\u{1F5D2}\uFE0F Create Note "${l}"`,actions:(0,it.jsx)(pe.ActionPanel,{children:(0,it.jsx)(pe.Action,{title:"Create Note",onAction:g})})})}):(0,it.jsx)(pe.List,{throttle:!0,isLoading:m,isShowingDetail:o.showDetail,onSearchTextChange:p=>{c(p)},navigationTitle:n,searchText:l,searchBarAccessory:(0,it.jsx)(zh,{tags:d,searchArguments:r}),children:h?.map(p=>(0,it.jsx)(xf,{note:p,vault:t,pref:o,action:i},p.path))})}var N=require("@raycast/api"),te=se(require("react"));var fd=require("@raycast/api"),sl=se(require("fs"));function hd(s,e){let{configFileName:t}=(0,fd.getPreferenceValues)(),n=[];return[s.filter(i=>{let o=`${i.path}/${t||".obsidian"}/community-plugins.json`;if(!sl.default.existsSync(o))return n.push(i),!1;let l=JSON.parse(sl.default.readFileSync(o,"utf-8")).includes(e);return l||n.push(i),l}),n]}var ee=require("@raycast/api"),dd=se(require("fs"));var mn=require("react/jsx-runtime");function nl(s){let{note:e,vault:t,dispatch:n}=s,{pop:r}=(0,ee.useNavigation)(),{appendTemplate:i}=(0,ee.getPreferenceValues)();async function o(a){let l=await Xt(a.content);dd.default.appendFileSync(e.path,`
`+l),(0,ee.showToast)({title:"Added text to note",style:ee.Toast.Style.Success}),n({type:4,payload:{note:e,vault:t}}),r()}return(0,mn.jsx)(ee.Form,{navigationTitle:"Add text to: "+e.title,actions:(0,mn.jsx)(ee.ActionPanel,{children:(0,mn.jsx)(ee.Action.SubmitForm,{title:"Submit",onSubmit:o})}),children:(0,mn.jsx)(ee.Form.TextArea,{title:`Add text to:
`+e.title,id:"content",placeholder:"Text",defaultValue:i})})}var z=require("@raycast/api"),md=se(require("fs"));var pn=require("react/jsx-runtime");function pd(s){let{note:e,vault:t}=s,{pop:n}=(0,z.useNavigation)();async function r(i){let o=i.content;o=await Xt(o);let a={title:"Override note",message:'Are you sure you want to override the note: "'+e.title+'"?',icon:z.Icon.ExclamationMark};await(0,z.confirmAlert)(a)&&(md.default.writeFileSync(e.path,o),(0,z.showToast)({title:"Edited note",style:z.Toast.Style.Success}),s.dispatch({type:4,payload:{note:e,vault:t}}),n())}return(0,pn.jsx)(z.Form,{navigationTitle:"Edit: "+e.title,actions:(0,pn.jsx)(z.ActionPanel,{children:(0,pn.jsx)(z.Action.SubmitForm,{title:"Submit",onSubmit:r})}),children:(0,pn.jsx)(z.Form.TextArea,{title:`Edit:
`+e.title,id:"content",placeholder:"Text",enableMarkdown:!0,defaultValue:e.content})})}var ot=require("@raycast/api");var bt=require("react/jsx-runtime");function gd(s){let{note:e,showTitle:t,allNotes:n,vault:r}=s;return(0,bt.jsx)(ot.Detail,{isLoading:e===void 0,navigationTitle:t?e.title:"",markdown:Fs(e.content),actions:(0,bt.jsxs)(ot.ActionPanel,{children:[(0,bt.jsx)(yn,{note:e,notes:n,vault:r}),(0,bt.jsx)(gn,{notes:n,note:e,vault:r}),(0,bt.jsx)(ot.Action,{title:"Reload Notes",icon:ot.Icon.ArrowClockwise,onAction:()=>qt(r)})]})})}var b=require("react/jsx-runtime");function pw(s){let{path:e}=s;return(0,b.jsx)(N.Action.ShowInFinder,{title:"Show in Finder",icon:N.Icon.Finder,path:e,shortcut:{modifiers:["opt"],key:"enter"}})}function gw(s){let{note:e,vault:t}=s,n=De();return(0,b.jsx)(N.Action.Push,{title:"Edit Note",target:(0,b.jsx)(pd,{note:e,vault:t,dispatch:n}),shortcut:{modifiers:["opt"],key:"e"},icon:N.Icon.Pencil})}function yw(s){let{note:e,vault:t}=s,n=De();return(0,b.jsx)(N.Action.Push,{title:"Append to Note",target:(0,b.jsx)(nl,{note:e,vault:t,dispatch:n}),shortcut:{modifiers:["opt"],key:"a"},icon:N.Icon.Pencil})}function Sw(s){let{note:e,vault:t}=s,n=De();return(0,b.jsx)(N.Action,{title:"Append Selected Text to Note",shortcut:{modifiers:["opt"],key:"s"},onAction:async()=>{await jh(e)&&n({type:4,payload:{note:e,vault:t}})},icon:N.Icon.Pencil})}function ww(s){let{note:e}=s;return(0,b.jsx)(N.Action.CopyToClipboard,{title:"Copy Note Content",content:e.content,shortcut:{modifiers:["opt"],key:"c"}})}function kw(s){let{note:e}=s;return(0,b.jsx)(N.Action.CopyToClipboard,{title:"Copy Note Title",content:e.title,shortcut:{modifiers:["opt"],key:"t"}})}function Tw(s){let{note:e}=s;return(0,b.jsx)(N.Action.Paste,{title:"Paste Note Content",content:e.content,shortcut:{modifiers:["opt"],key:"v"}})}function bw(s){let{note:e}=s,t=vt({type:"obsidian://open?path=",path:e.path});return(0,b.jsx)(N.Action.CopyToClipboard,{title:"Copy Markdown Link",icon:N.Icon.Link,content:`[${e.title}](${t})`,shortcut:{modifiers:["opt"],key:"l"}})}function Nw(s){let{note:e}=s,t=vt({type:"obsidian://open?path=",path:e.path});return(0,b.jsx)(N.Action.CopyToClipboard,{title:"Copy Obsidian Link",icon:N.Icon.Link,content:t,shortcut:{modifiers:["opt"],key:"u"}})}function Ew(s){let{note:e,vault:t}=s,n=De();return(0,b.jsx)(N.Action,{title:"Delete Note",shortcut:{modifiers:["opt"],key:"d"},onAction:async()=>{let r={title:"Delete Note",message:'Are you sure you want to delete the note: "'+e.title+'"?',icon:N.Icon.ExclamationMark};await(0,N.confirmAlert)(r)&&n({type:1,payload:{note:e,vault:t}})},icon:{source:N.Icon.Trash,tintColor:N.Color.Red}})}function vw(s){let{note:e,notes:t,vault:n}=s;return(0,b.jsx)(N.Action.Push,{title:"Quick Look",target:(0,b.jsx)(gd,{note:e,showTitle:!0,allNotes:t,vault:n}),icon:N.Icon.Eye})}function Aw(s){let{note:e}=s,[t,n]=(0,te.useState)("Default App");return(0,te.useEffect)(()=>{(0,N.getDefaultApplication)(e.path).then(r=>n(r.name)).catch(r=>{console.error(r),n("")})},[e.path]),t?(0,b.jsx)(N.Action.Open,{title:`Open in ${t}`,target:e.path,icon:N.Icon.AppWindow}):null}function Ow(s){let{note:e,vault:t}=s,n=De();return(0,b.jsx)(N.Action,{title:"Bookmark Note",shortcut:{modifiers:["opt"],key:"p"},onAction:()=>{n({type:2,payload:{note:e,vault:t}})},icon:N.Icon.Bookmark})}function Iw(s){let{note:e,vault:t}=s,n=De();return(0,b.jsx)(N.Action,{title:"Unbookmark Note",shortcut:{modifiers:["opt"],key:"p"},onAction:()=>{n({type:3,payload:{note:e,vault:t}})},icon:N.Icon.Bookmark})}function Dw(s){let{path:e}=s,t=vt({type:"obsidian://open?path=",path:e});return(0,b.jsx)(N.Action.Open,{title:"Open in Obsidian",target:t,icon:ii})}function Cw(s){let{note:e,vault:t}=s;return(0,b.jsx)(N.Action.Open,{title:"Open in New Obsidian Tab",target:"obsidian://advanced-uri?vault="+encodeURIComponent(t.name)+"&filepath="+encodeURIComponent(e.path.replace(t.path,""))+"&newpane=true",icon:ii})}function yd(s){let{vault:e}=s;return(0,b.jsx)(N.Action.ShowInFinder,{title:"Show in Finder",icon:N.Icon.Finder,path:e.path})}function Mw(s){let{vault:e,str:t,notes:n}=s,r=n.filter(o=>o.content.includes(t)),i=r.length;if(i>0){let o=(0,b.jsx)(Qr,{vault:e,notes:r,searchArguments:{searchArgument:"",tagArgument:""},title:`${i} notes mentioning "${t}"`,action:(a,l)=>(0,b.jsxs)(te.default.Fragment,{children:[(0,b.jsx)(yn,{note:a,notes:n,vault:l}),(0,b.jsx)(gn,{note:a,notes:n,vault:l})]})});return(0,b.jsx)(N.Action.Push,{title:`Show Mentioning Notes (${i})`,target:o,icon:N.Icon.Megaphone})}else return(0,b.jsx)(te.default.Fragment,{})}function Lw(s){let{note:e}=s,t=Zh(e.content);if(t.length===1){let n=t[0];return(0,b.jsxs)(te.default.Fragment,{children:[(0,b.jsx)(N.Action.Paste,{title:"Paste Code",icon:N.Icon.Code,content:n.code}),(0,b.jsx)(N.Action.CopyToClipboard,{title:"Copy Code",icon:N.Icon.Code,content:n.code})]})}else return t.length>1?(0,b.jsx)(N.Action.Push,{title:"Copy Code",icon:N.Icon.Code,target:(0,b.jsx)(N.List,{isShowingDetail:!0,children:t?.map(n=>(0,b.jsx)(N.List.Item,{title:n.code,detail:(0,b.jsx)(N.List.Item.Detail,{markdown:"```\n"+n.code+"```"}),subtitle:n.language,actions:(0,b.jsxs)(N.ActionPanel,{children:[(0,b.jsx)(N.Action.Paste,{title:"Paste Code",icon:N.Icon.Code,content:n.code}),(0,b.jsx)(N.Action.CopyToClipboard,{title:"Copy Code",icon:N.Icon.Code,content:n.code})]})},n.code))})}):(0,b.jsx)(te.default.Fragment,{})}function gn(s){let{notes:e,note:t,vault:n}=s;return(0,b.jsxs)(te.default.Fragment,{children:[(0,b.jsx)(pw,{path:t.path}),(0,b.jsx)(Mw,{vault:n,str:t.title,notes:e}),t.bookmarked?(0,b.jsx)(Iw,{note:t,vault:n}):(0,b.jsx)(Ow,{note:t,vault:n}),(0,b.jsx)(Lw,{note:t}),(0,b.jsx)(gw,{note:t,vault:n}),(0,b.jsx)(yw,{note:t,vault:n}),(0,b.jsx)(Sw,{note:t,vault:n}),(0,b.jsx)(ww,{note:t}),(0,b.jsx)(kw,{note:t}),(0,b.jsx)(Tw,{note:t}),(0,b.jsx)(bw,{note:t}),(0,b.jsx)(Nw,{note:t}),(0,b.jsx)(Ew,{note:t,vault:n}),(0,b.jsx)(Fw,{note:t,vault:n})]})}function yn(s){let{note:e,notes:t,vault:n}=s,{primaryAction:r}=(0,N.getPreferenceValues)(),[i]=hd([n],"obsidian-advanced-uri"),o=(0,b.jsx)(vw,{note:e,notes:t,vault:n}),a=(0,b.jsx)(Aw,{note:e,notes:t,vault:n}),l=(0,b.jsx)(Dw,{path:e.path}),c=i.includes(n)?(0,b.jsx)(Cw,{note:e,vault:n}):null;return r=="quicklook"?(0,b.jsxs)(te.default.Fragment,{children:[o,l,c,a]}):r=="obsidian"?(0,b.jsxs)(te.default.Fragment,{children:[l,c,a,o]}):r=="defaultapp"?(0,b.jsxs)(te.default.Fragment,{children:[a,l,c,o]}):r=="newpane"?(0,b.jsxs)(te.default.Fragment,{children:[c,l,o,a]}):(0,b.jsxs)(te.default.Fragment,{children:[l,c,o,a]})}function Fw(s){let{note:e,vault:t}=s,n=De();return(0,b.jsx)(N.Action.Push,{title:"Append Task",target:(0,b.jsx)(nl,{note:e,vault:t,dispatch:n}),shortcut:{modifiers:["opt"],key:"a"},icon:N.Icon.Pencil})}var ei=require("@raycast/api");var Ve=require("react/jsx-runtime");function rl(s){let{showTitle:e,vault:t,searchArguments:n}=s,[r]=Mf(t,s.bookmarked),[i,o]=(0,Xr.useReducer)(Gh,r);return(0,Ve.jsx)(Xo.Provider,{value:r,children:(0,Ve.jsx)(ea.Provider,{value:o,children:(0,Ve.jsx)(Qr,{title:e?`Search Note in ${t.name}`:"",notes:i,vault:t,searchArguments:n,action:(a,l)=>(0,Ve.jsxs)(Xr.default.Fragment,{children:[(0,Ve.jsx)(yn,{note:a,notes:r,vault:l}),(0,Ve.jsx)(gn,{notes:r,note:a,vault:l}),(0,Ve.jsx)(ei.Action,{title:"Reload Notes",icon:ei.Icon.ArrowClockwise,onAction:()=>qt(l)})]})})})})}var Et=require("@raycast/api");var Nt=require("react/jsx-runtime");function Sd(s){let{vaults:e,target:t}=s;return(0,Nt.jsx)(Et.List,{children:e?.map(n=>(0,Nt.jsx)(Et.List.Item,{title:n.name,actions:(0,Nt.jsxs)(Et.ActionPanel,{children:[(0,Nt.jsx)(Et.Action.Push,{title:"Select Vault",target:t(n)}),(0,Nt.jsx)(yd,{vault:n})]})},n.key))})}var wd=require("@raycast/api"),Td=require("react/jsx-runtime");function kd(){return(0,Td.jsx)(wd.Detail,{markdown:`# No vaults found

 Please use Obsidian to create a vault, or set a vault path in the extension's preferences before using this command.`})}var ts=require("react/jsx-runtime");function Nd(s){let{ready:e,vaults:t}=Lf();if(e){if(t.length===0)return(0,ts.jsx)(kd,{});if(t.length>1)return(0,ts.jsx)(Sd,{vaults:t,target:n=>(0,ts.jsx)(rl,{vault:n,showTitle:!0,bookmarked:!0,searchArguments:s.arguments})});if(t.length==1)return(0,ts.jsx)(rl,{vault:t[0],showTitle:!1,bookmarked:!0,searchArguments:s.arguments});_f()}else return(0,ts.jsx)(bd.List,{isLoading:!0})}
