{"version": 3, "sources": ["../node_modules/yaml/dist/nodes/identity.js", "../node_modules/yaml/dist/visit.js", "../node_modules/yaml/dist/doc/directives.js", "../node_modules/yaml/dist/doc/anchors.js", "../node_modules/yaml/dist/doc/applyReviver.js", "../node_modules/yaml/dist/nodes/toJS.js", "../node_modules/yaml/dist/nodes/Node.js", "../node_modules/yaml/dist/nodes/Alias.js", "../node_modules/yaml/dist/nodes/Scalar.js", "../node_modules/yaml/dist/doc/createNode.js", "../node_modules/yaml/dist/nodes/Collection.js", "../node_modules/yaml/dist/stringify/stringifyComment.js", "../node_modules/yaml/dist/stringify/foldFlowLines.js", "../node_modules/yaml/dist/stringify/stringifyString.js", "../node_modules/yaml/dist/stringify/stringify.js", "../node_modules/yaml/dist/stringify/stringifyPair.js", "../node_modules/yaml/dist/log.js", "../node_modules/yaml/dist/schema/yaml-1.1/merge.js", "../node_modules/yaml/dist/nodes/addPairToJSMap.js", "../node_modules/yaml/dist/nodes/Pair.js", "../node_modules/yaml/dist/stringify/stringifyCollection.js", "../node_modules/yaml/dist/nodes/YAMLMap.js", "../node_modules/yaml/dist/schema/common/map.js", "../node_modules/yaml/dist/nodes/YAMLSeq.js", "../node_modules/yaml/dist/schema/common/seq.js", "../node_modules/yaml/dist/schema/common/string.js", "../node_modules/yaml/dist/schema/common/null.js", "../node_modules/yaml/dist/schema/core/bool.js", "../node_modules/yaml/dist/stringify/stringifyNumber.js", "../node_modules/yaml/dist/schema/core/float.js", "../node_modules/yaml/dist/schema/core/int.js", "../node_modules/yaml/dist/schema/core/schema.js", "../node_modules/yaml/dist/schema/json/schema.js", "../node_modules/yaml/dist/schema/yaml-1.1/binary.js", "../node_modules/yaml/dist/schema/yaml-1.1/pairs.js", "../node_modules/yaml/dist/schema/yaml-1.1/omap.js", "../node_modules/yaml/dist/schema/yaml-1.1/bool.js", "../node_modules/yaml/dist/schema/yaml-1.1/float.js", "../node_modules/yaml/dist/schema/yaml-1.1/int.js", "../node_modules/yaml/dist/schema/yaml-1.1/set.js", "../node_modules/yaml/dist/schema/yaml-1.1/timestamp.js", "../node_modules/yaml/dist/schema/yaml-1.1/schema.js", "../node_modules/yaml/dist/schema/tags.js", "../node_modules/yaml/dist/schema/Schema.js", "../node_modules/yaml/dist/stringify/stringifyDocument.js", "../node_modules/yaml/dist/doc/Document.js", "../node_modules/yaml/dist/errors.js", "../node_modules/yaml/dist/compose/resolve-props.js", "../node_modules/yaml/dist/compose/util-contains-newline.js", "../node_modules/yaml/dist/compose/util-flow-indent-check.js", "../node_modules/yaml/dist/compose/util-map-includes.js", "../node_modules/yaml/dist/compose/resolve-block-map.js", "../node_modules/yaml/dist/compose/resolve-block-seq.js", "../node_modules/yaml/dist/compose/resolve-end.js", "../node_modules/yaml/dist/compose/resolve-flow-collection.js", "../node_modules/yaml/dist/compose/compose-collection.js", "../node_modules/yaml/dist/compose/resolve-block-scalar.js", "../node_modules/yaml/dist/compose/resolve-flow-scalar.js", "../node_modules/yaml/dist/compose/compose-scalar.js", "../node_modules/yaml/dist/compose/util-empty-scalar-position.js", "../node_modules/yaml/dist/compose/compose-node.js", "../node_modules/yaml/dist/compose/compose-doc.js", "../node_modules/yaml/dist/compose/composer.js", "../node_modules/yaml/dist/parse/cst-scalar.js", "../node_modules/yaml/dist/parse/cst-stringify.js", "../node_modules/yaml/dist/parse/cst-visit.js", "../node_modules/yaml/dist/parse/cst.js", "../node_modules/yaml/dist/parse/lexer.js", "../node_modules/yaml/dist/parse/line-counter.js", "../node_modules/yaml/dist/parse/parser.js", "../node_modules/yaml/dist/public-api.js", "../node_modules/yaml/dist/index.js", "../src/dailyNoteCommand.tsx", "../src/utils/constants.tsx", "../src/utils/utils.tsx", "../src/components/Notifications/NoVaultFoundMessage.tsx", "../src/components/Toasts.tsx", "../src/components/Notifications/AdvancedURIPluginNotInstalled.tsx", "../src/utils/hooks.tsx", "../src/api/vault/vault.service.ts", "../src/utils/yaml.tsx", "../src/api/vault/notes/bookmarks/bookmarks.service.ts", "../src/api/logger/logger.service.ts", "../src/api/cache/cache.service.ts", "../src/api/vault/plugins/plugins.service.ts"], "sourcesContent": ["'use strict';\n\nconst ALIAS = Symbol.for('yaml.alias');\nconst DOC = Symbol.for('yaml.document');\nconst MAP = Symbol.for('yaml.map');\nconst PAIR = Symbol.for('yaml.pair');\nconst SCALAR = Symbol.for('yaml.scalar');\nconst SEQ = Symbol.for('yaml.seq');\nconst NODE_TYPE = Symbol.for('yaml.node.type');\nconst isAlias = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === ALIAS;\nconst isDocument = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === DOC;\nconst isMap = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === MAP;\nconst isPair = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === PAIR;\nconst isScalar = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === SCALAR;\nconst isSeq = (node) => !!node && typeof node === 'object' && node[NODE_TYPE] === SEQ;\nfunction isCollection(node) {\n    if (node && typeof node === 'object')\n        switch (node[NODE_TYPE]) {\n            case MAP:\n            case SEQ:\n                return true;\n        }\n    return false;\n}\nfunction isNode(node) {\n    if (node && typeof node === 'object')\n        switch (node[NODE_TYPE]) {\n            case ALIAS:\n            case MAP:\n            case SCALAR:\n            case SEQ:\n                return true;\n        }\n    return false;\n}\nconst hasAnchor = (node) => (isScalar(node) || isCollection(node)) && !!node.anchor;\n\nexports.ALIAS = ALIAS;\nexports.DOC = DOC;\nexports.MAP = MAP;\nexports.NODE_TYPE = NODE_TYPE;\nexports.PAIR = PAIR;\nexports.SCALAR = SCALAR;\nexports.SEQ = SEQ;\nexports.hasAnchor = hasAnchor;\nexports.isAlias = isAlias;\nexports.isCollection = isCollection;\nexports.isDocument = isDocument;\nexports.isMap = isMap;\nexports.isNode = isNode;\nexports.isPair = isPair;\nexports.isScalar = isScalar;\nexports.isSeq = isSeq;\n", "'use strict';\n\nvar identity = require('./nodes/identity.js');\n\nconst BREAK = Symbol('break visit');\nconst SKIP = Symbol('skip children');\nconst REMOVE = Symbol('remove node');\n/**\n * Apply a visitor to an AST node or document.\n *\n * Walks through the tree (depth-first) starting from `node`, calling a\n * `visitor` function with three arguments:\n *   - `key`: For sequence values and map `Pair`, the node's index in the\n *     collection. Within a `Pair`, `'key'` or `'value'`, correspondingly.\n *     `null` for the root node.\n *   - `node`: The current node.\n *   - `path`: The ancestry of the current node.\n *\n * The return value of the visitor may be used to control the traversal:\n *   - `undefined` (default): Do nothing and continue\n *   - `visit.SKIP`: Do not visit the children of this node, continue with next\n *     sibling\n *   - `visit.BREAK`: Terminate traversal completely\n *   - `visit.REMOVE`: Remove the current node, then continue with the next one\n *   - `Node`: Replace the current node, then continue by visiting it\n *   - `number`: While iterating the items of a sequence or map, set the index\n *     of the next step. This is useful especially if the index of the current\n *     node has changed.\n *\n * If `visitor` is a single function, it will be called with all values\n * encountered in the tree, including e.g. `null` values. Alternatively,\n * separate visitor functions may be defined for each `Map`, `Pair`, `Seq`,\n * `Alias` and `Scalar` node. To define the same visitor function for more than\n * one node type, use the `Collection` (map and seq), `Value` (map, seq & scalar)\n * and `Node` (alias, map, seq & scalar) targets. Of all these, only the most\n * specific defined one will be used for each node.\n */\nfunction visit(node, visitor) {\n    const visitor_ = initVisitor(visitor);\n    if (identity.isDocument(node)) {\n        const cd = visit_(null, node.contents, visitor_, Object.freeze([node]));\n        if (cd === REMOVE)\n            node.contents = null;\n    }\n    else\n        visit_(null, node, visitor_, Object.freeze([]));\n}\n// Without the `as symbol` casts, TS declares these in the `visit`\n// namespace using `var`, but then complains about that because\n// `unique symbol` must be `const`.\n/** Terminate visit traversal completely */\nvisit.BREAK = BREAK;\n/** Do not visit the children of the current node */\nvisit.SKIP = SKIP;\n/** Remove the current node */\nvisit.REMOVE = REMOVE;\nfunction visit_(key, node, visitor, path) {\n    const ctrl = callVisitor(key, node, visitor, path);\n    if (identity.isNode(ctrl) || identity.isPair(ctrl)) {\n        replaceNode(key, path, ctrl);\n        return visit_(key, ctrl, visitor, path);\n    }\n    if (typeof ctrl !== 'symbol') {\n        if (identity.isCollection(node)) {\n            path = Object.freeze(path.concat(node));\n            for (let i = 0; i < node.items.length; ++i) {\n                const ci = visit_(i, node.items[i], visitor, path);\n                if (typeof ci === 'number')\n                    i = ci - 1;\n                else if (ci === BREAK)\n                    return BREAK;\n                else if (ci === REMOVE) {\n                    node.items.splice(i, 1);\n                    i -= 1;\n                }\n            }\n        }\n        else if (identity.isPair(node)) {\n            path = Object.freeze(path.concat(node));\n            const ck = visit_('key', node.key, visitor, path);\n            if (ck === BREAK)\n                return BREAK;\n            else if (ck === REMOVE)\n                node.key = null;\n            const cv = visit_('value', node.value, visitor, path);\n            if (cv === BREAK)\n                return BREAK;\n            else if (cv === REMOVE)\n                node.value = null;\n        }\n    }\n    return ctrl;\n}\n/**\n * Apply an async visitor to an AST node or document.\n *\n * Walks through the tree (depth-first) starting from `node`, calling a\n * `visitor` function with three arguments:\n *   - `key`: For sequence values and map `Pair`, the node's index in the\n *     collection. Within a `Pair`, `'key'` or `'value'`, correspondingly.\n *     `null` for the root node.\n *   - `node`: The current node.\n *   - `path`: The ancestry of the current node.\n *\n * The return value of the visitor may be used to control the traversal:\n *   - `Promise`: Must resolve to one of the following values\n *   - `undefined` (default): Do nothing and continue\n *   - `visit.SKIP`: Do not visit the children of this node, continue with next\n *     sibling\n *   - `visit.BREAK`: Terminate traversal completely\n *   - `visit.REMOVE`: Remove the current node, then continue with the next one\n *   - `Node`: Replace the current node, then continue by visiting it\n *   - `number`: While iterating the items of a sequence or map, set the index\n *     of the next step. This is useful especially if the index of the current\n *     node has changed.\n *\n * If `visitor` is a single function, it will be called with all values\n * encountered in the tree, including e.g. `null` values. Alternatively,\n * separate visitor functions may be defined for each `Map`, `Pair`, `Seq`,\n * `Alias` and `Scalar` node. To define the same visitor function for more than\n * one node type, use the `Collection` (map and seq), `Value` (map, seq & scalar)\n * and `Node` (alias, map, seq & scalar) targets. Of all these, only the most\n * specific defined one will be used for each node.\n */\nasync function visitAsync(node, visitor) {\n    const visitor_ = initVisitor(visitor);\n    if (identity.isDocument(node)) {\n        const cd = await visitAsync_(null, node.contents, visitor_, Object.freeze([node]));\n        if (cd === REMOVE)\n            node.contents = null;\n    }\n    else\n        await visitAsync_(null, node, visitor_, Object.freeze([]));\n}\n// Without the `as symbol` casts, TS declares these in the `visit`\n// namespace using `var`, but then complains about that because\n// `unique symbol` must be `const`.\n/** Terminate visit traversal completely */\nvisitAsync.BREAK = BREAK;\n/** Do not visit the children of the current node */\nvisitAsync.SKIP = SKIP;\n/** Remove the current node */\nvisitAsync.REMOVE = REMOVE;\nasync function visitAsync_(key, node, visitor, path) {\n    const ctrl = await callVisitor(key, node, visitor, path);\n    if (identity.isNode(ctrl) || identity.isPair(ctrl)) {\n        replaceNode(key, path, ctrl);\n        return visitAsync_(key, ctrl, visitor, path);\n    }\n    if (typeof ctrl !== 'symbol') {\n        if (identity.isCollection(node)) {\n            path = Object.freeze(path.concat(node));\n            for (let i = 0; i < node.items.length; ++i) {\n                const ci = await visitAsync_(i, node.items[i], visitor, path);\n                if (typeof ci === 'number')\n                    i = ci - 1;\n                else if (ci === BREAK)\n                    return BREAK;\n                else if (ci === REMOVE) {\n                    node.items.splice(i, 1);\n                    i -= 1;\n                }\n            }\n        }\n        else if (identity.isPair(node)) {\n            path = Object.freeze(path.concat(node));\n            const ck = await visitAsync_('key', node.key, visitor, path);\n            if (ck === BREAK)\n                return BREAK;\n            else if (ck === REMOVE)\n                node.key = null;\n            const cv = await visitAsync_('value', node.value, visitor, path);\n            if (cv === BREAK)\n                return BREAK;\n            else if (cv === REMOVE)\n                node.value = null;\n        }\n    }\n    return ctrl;\n}\nfunction initVisitor(visitor) {\n    if (typeof visitor === 'object' &&\n        (visitor.Collection || visitor.Node || visitor.Value)) {\n        return Object.assign({\n            Alias: visitor.Node,\n            Map: visitor.Node,\n            Scalar: visitor.Node,\n            Seq: visitor.Node\n        }, visitor.Value && {\n            Map: visitor.Value,\n            Scalar: visitor.Value,\n            Seq: visitor.Value\n        }, visitor.Collection && {\n            Map: visitor.Collection,\n            Seq: visitor.Collection\n        }, visitor);\n    }\n    return visitor;\n}\nfunction callVisitor(key, node, visitor, path) {\n    if (typeof visitor === 'function')\n        return visitor(key, node, path);\n    if (identity.isMap(node))\n        return visitor.Map?.(key, node, path);\n    if (identity.isSeq(node))\n        return visitor.Seq?.(key, node, path);\n    if (identity.isPair(node))\n        return visitor.Pair?.(key, node, path);\n    if (identity.isScalar(node))\n        return visitor.Scalar?.(key, node, path);\n    if (identity.isAlias(node))\n        return visitor.Alias?.(key, node, path);\n    return undefined;\n}\nfunction replaceNode(key, path, node) {\n    const parent = path[path.length - 1];\n    if (identity.isCollection(parent)) {\n        parent.items[key] = node;\n    }\n    else if (identity.isPair(parent)) {\n        if (key === 'key')\n            parent.key = node;\n        else\n            parent.value = node;\n    }\n    else if (identity.isDocument(parent)) {\n        parent.contents = node;\n    }\n    else {\n        const pt = identity.isAlias(parent) ? 'alias' : 'scalar';\n        throw new Error(`Cannot replace node with ${pt} parent`);\n    }\n}\n\nexports.visit = visit;\nexports.visitAsync = visitAsync;\n", "'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar visit = require('../visit.js');\n\nconst escapeChars = {\n    '!': '%21',\n    ',': '%2C',\n    '[': '%5B',\n    ']': '%5D',\n    '{': '%7B',\n    '}': '%7D'\n};\nconst escapeTagName = (tn) => tn.replace(/[!,[\\]{}]/g, ch => escapeChars[ch]);\nclass Directives {\n    constructor(yaml, tags) {\n        /**\n         * The directives-end/doc-start marker `---`. If `null`, a marker may still be\n         * included in the document's stringified representation.\n         */\n        this.docStart = null;\n        /** The doc-end marker `...`.  */\n        this.docEnd = false;\n        this.yaml = Object.assign({}, Directives.defaultYaml, yaml);\n        this.tags = Object.assign({}, Directives.defaultTags, tags);\n    }\n    clone() {\n        const copy = new Directives(this.yaml, this.tags);\n        copy.docStart = this.docStart;\n        return copy;\n    }\n    /**\n     * During parsing, get a Directives instance for the current document and\n     * update the stream state according to the current version's spec.\n     */\n    atDocument() {\n        const res = new Directives(this.yaml, this.tags);\n        switch (this.yaml.version) {\n            case '1.1':\n                this.atNextDocument = true;\n                break;\n            case '1.2':\n                this.atNextDocument = false;\n                this.yaml = {\n                    explicit: Directives.defaultYaml.explicit,\n                    version: '1.2'\n                };\n                this.tags = Object.assign({}, Directives.defaultTags);\n                break;\n        }\n        return res;\n    }\n    /**\n     * @param onError - May be called even if the action was successful\n     * @returns `true` on success\n     */\n    add(line, onError) {\n        if (this.atNextDocument) {\n            this.yaml = { explicit: Directives.defaultYaml.explicit, version: '1.1' };\n            this.tags = Object.assign({}, Directives.defaultTags);\n            this.atNextDocument = false;\n        }\n        const parts = line.trim().split(/[ \\t]+/);\n        const name = parts.shift();\n        switch (name) {\n            case '%TAG': {\n                if (parts.length !== 2) {\n                    onError(0, '%TAG directive should contain exactly two parts');\n                    if (parts.length < 2)\n                        return false;\n                }\n                const [handle, prefix] = parts;\n                this.tags[handle] = prefix;\n                return true;\n            }\n            case '%YAML': {\n                this.yaml.explicit = true;\n                if (parts.length !== 1) {\n                    onError(0, '%YAML directive should contain exactly one part');\n                    return false;\n                }\n                const [version] = parts;\n                if (version === '1.1' || version === '1.2') {\n                    this.yaml.version = version;\n                    return true;\n                }\n                else {\n                    const isValid = /^\\d+\\.\\d+$/.test(version);\n                    onError(6, `Unsupported YAML version ${version}`, isValid);\n                    return false;\n                }\n            }\n            default:\n                onError(0, `Unknown directive ${name}`, true);\n                return false;\n        }\n    }\n    /**\n     * Resolves a tag, matching handles to those defined in %TAG directives.\n     *\n     * @returns Resolved tag, which may also be the non-specific tag `'!'` or a\n     *   `'!local'` tag, or `null` if unresolvable.\n     */\n    tagName(source, onError) {\n        if (source === '!')\n            return '!'; // non-specific tag\n        if (source[0] !== '!') {\n            onError(`Not a valid tag: ${source}`);\n            return null;\n        }\n        if (source[1] === '<') {\n            const verbatim = source.slice(2, -1);\n            if (verbatim === '!' || verbatim === '!!') {\n                onError(`Verbatim tags aren't resolved, so ${source} is invalid.`);\n                return null;\n            }\n            if (source[source.length - 1] !== '>')\n                onError('Verbatim tags must end with a >');\n            return verbatim;\n        }\n        const [, handle, suffix] = source.match(/^(.*!)([^!]*)$/s);\n        if (!suffix)\n            onError(`The ${source} tag has no suffix`);\n        const prefix = this.tags[handle];\n        if (prefix) {\n            try {\n                return prefix + decodeURIComponent(suffix);\n            }\n            catch (error) {\n                onError(String(error));\n                return null;\n            }\n        }\n        if (handle === '!')\n            return source; // local tag\n        onError(`Could not resolve tag: ${source}`);\n        return null;\n    }\n    /**\n     * Given a fully resolved tag, returns its printable string form,\n     * taking into account current tag prefixes and defaults.\n     */\n    tagString(tag) {\n        for (const [handle, prefix] of Object.entries(this.tags)) {\n            if (tag.startsWith(prefix))\n                return handle + escapeTagName(tag.substring(prefix.length));\n        }\n        return tag[0] === '!' ? tag : `!<${tag}>`;\n    }\n    toString(doc) {\n        const lines = this.yaml.explicit\n            ? [`%YAML ${this.yaml.version || '1.2'}`]\n            : [];\n        const tagEntries = Object.entries(this.tags);\n        let tagNames;\n        if (doc && tagEntries.length > 0 && identity.isNode(doc.contents)) {\n            const tags = {};\n            visit.visit(doc.contents, (_key, node) => {\n                if (identity.isNode(node) && node.tag)\n                    tags[node.tag] = true;\n            });\n            tagNames = Object.keys(tags);\n        }\n        else\n            tagNames = [];\n        for (const [handle, prefix] of tagEntries) {\n            if (handle === '!!' && prefix === 'tag:yaml.org,2002:')\n                continue;\n            if (!doc || tagNames.some(tn => tn.startsWith(prefix)))\n                lines.push(`%TAG ${handle} ${prefix}`);\n        }\n        return lines.join('\\n');\n    }\n}\nDirectives.defaultYaml = { explicit: false, version: '1.2' };\nDirectives.defaultTags = { '!!': 'tag:yaml.org,2002:' };\n\nexports.Directives = Directives;\n", "'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar visit = require('../visit.js');\n\n/**\n * Verify that the input string is a valid anchor.\n *\n * Will throw on errors.\n */\nfunction anchorIsValid(anchor) {\n    if (/[\\x00-\\x19\\s,[\\]{}]/.test(anchor)) {\n        const sa = JSON.stringify(anchor);\n        const msg = `Anchor must not contain whitespace or control characters: ${sa}`;\n        throw new Error(msg);\n    }\n    return true;\n}\nfunction anchorNames(root) {\n    const anchors = new Set();\n    visit.visit(root, {\n        Value(_key, node) {\n            if (node.anchor)\n                anchors.add(node.anchor);\n        }\n    });\n    return anchors;\n}\n/** Find a new anchor name with the given `prefix` and a one-indexed suffix. */\nfunction findNewAnchor(prefix, exclude) {\n    for (let i = 1; true; ++i) {\n        const name = `${prefix}${i}`;\n        if (!exclude.has(name))\n            return name;\n    }\n}\nfunction createNodeAnchors(doc, prefix) {\n    const aliasObjects = [];\n    const sourceObjects = new Map();\n    let prevAnchors = null;\n    return {\n        onAnchor: (source) => {\n            aliasObjects.push(source);\n            if (!prevAnchors)\n                prevAnchors = anchorNames(doc);\n            const anchor = findNewAnchor(prefix, prevAnchors);\n            prevAnchors.add(anchor);\n            return anchor;\n        },\n        /**\n         * With circular references, the source node is only resolved after all\n         * of its child nodes are. This is why anchors are set only after all of\n         * the nodes have been created.\n         */\n        setAnchors: () => {\n            for (const source of aliasObjects) {\n                const ref = sourceObjects.get(source);\n                if (typeof ref === 'object' &&\n                    ref.anchor &&\n                    (identity.isScalar(ref.node) || identity.isCollection(ref.node))) {\n                    ref.node.anchor = ref.anchor;\n                }\n                else {\n                    const error = new Error('Failed to resolve repeated object (this should not happen)');\n                    error.source = source;\n                    throw error;\n                }\n            }\n        },\n        sourceObjects\n    };\n}\n\nexports.anchorIsValid = anchorIsValid;\nexports.anchorNames = anchorNames;\nexports.createNodeAnchors = createNodeAnchors;\nexports.findNewAnchor = findNewAnchor;\n", "'use strict';\n\n/**\n * Applies the JSON.parse reviver algorithm as defined in the ECMA-262 spec,\n * in section 24.5.1.1 \"Runtime Semantics: InternalizeJSONProperty\" of the\n * 2021 edition: https://tc39.es/ecma262/#sec-json.parse\n *\n * Includes extensions for handling Map and Set objects.\n */\nfunction applyReviver(reviver, obj, key, val) {\n    if (val && typeof val === 'object') {\n        if (Array.isArray(val)) {\n            for (let i = 0, len = val.length; i < len; ++i) {\n                const v0 = val[i];\n                const v1 = applyReviver(reviver, val, String(i), v0);\n                // eslint-disable-next-line @typescript-eslint/no-array-delete\n                if (v1 === undefined)\n                    delete val[i];\n                else if (v1 !== v0)\n                    val[i] = v1;\n            }\n        }\n        else if (val instanceof Map) {\n            for (const k of Array.from(val.keys())) {\n                const v0 = val.get(k);\n                const v1 = applyReviver(reviver, val, k, v0);\n                if (v1 === undefined)\n                    val.delete(k);\n                else if (v1 !== v0)\n                    val.set(k, v1);\n            }\n        }\n        else if (val instanceof Set) {\n            for (const v0 of Array.from(val)) {\n                const v1 = applyReviver(reviver, val, v0, v0);\n                if (v1 === undefined)\n                    val.delete(v0);\n                else if (v1 !== v0) {\n                    val.delete(v0);\n                    val.add(v1);\n                }\n            }\n        }\n        else {\n            for (const [k, v0] of Object.entries(val)) {\n                const v1 = applyReviver(reviver, val, k, v0);\n                if (v1 === undefined)\n                    delete val[k];\n                else if (v1 !== v0)\n                    val[k] = v1;\n            }\n        }\n    }\n    return reviver.call(obj, key, val);\n}\n\nexports.applyReviver = applyReviver;\n", "'use strict';\n\nvar identity = require('./identity.js');\n\n/**\n * Recursively convert any node or its contents to native JavaScript\n *\n * @param value - The input value\n * @param arg - If `value` defines a `toJSON()` method, use this\n *   as its first argument\n * @param ctx - Conversion context, originally set in Document#toJS(). If\n *   `{ keep: true }` is not set, output should be suitable for JSON\n *   stringification.\n */\nfunction toJS(value, arg, ctx) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n    if (Array.isArray(value))\n        return value.map((v, i) => toJS(v, String(i), ctx));\n    if (value && typeof value.toJSON === 'function') {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n        if (!ctx || !identity.hasAnchor(value))\n            return value.toJSON(arg, ctx);\n        const data = { aliasCount: 0, count: 1, res: undefined };\n        ctx.anchors.set(value, data);\n        ctx.onCreate = res => {\n            data.res = res;\n            delete ctx.onCreate;\n        };\n        const res = value.toJSON(arg, ctx);\n        if (ctx.onCreate)\n            ctx.onCreate(res);\n        return res;\n    }\n    if (typeof value === 'bigint' && !ctx?.keep)\n        return Number(value);\n    return value;\n}\n\nexports.toJS = toJS;\n", "'use strict';\n\nvar applyReviver = require('../doc/applyReviver.js');\nvar identity = require('./identity.js');\nvar toJS = require('./toJS.js');\n\nclass NodeBase {\n    constructor(type) {\n        Object.defineProperty(this, identity.NODE_TYPE, { value: type });\n    }\n    /** Create a copy of this node.  */\n    clone() {\n        const copy = Object.create(Object.getPrototypeOf(this), Object.getOwnPropertyDescriptors(this));\n        if (this.range)\n            copy.range = this.range.slice();\n        return copy;\n    }\n    /** A plain JavaScript representation of this node. */\n    toJS(doc, { mapAsMap, maxAliasCount, onAnchor, reviver } = {}) {\n        if (!identity.isDocument(doc))\n            throw new TypeError('A document argument is required');\n        const ctx = {\n            anchors: new Map(),\n            doc,\n            keep: true,\n            mapAsMap: mapAsMap === true,\n            mapKeyWarned: false,\n            maxAliasCount: typeof maxAliasCount === 'number' ? maxAliasCount : 100\n        };\n        const res = toJS.toJS(this, '', ctx);\n        if (typeof onAnchor === 'function')\n            for (const { count, res } of ctx.anchors.values())\n                onAnchor(res, count);\n        return typeof reviver === 'function'\n            ? applyReviver.applyReviver(reviver, { '': res }, '', res)\n            : res;\n    }\n}\n\nexports.NodeBase = NodeBase;\n", "'use strict';\n\nvar anchors = require('../doc/anchors.js');\nvar visit = require('../visit.js');\nvar identity = require('./identity.js');\nvar Node = require('./Node.js');\nvar toJS = require('./toJS.js');\n\nclass Alias extends Node.NodeBase {\n    constructor(source) {\n        super(identity.ALIAS);\n        this.source = source;\n        Object.defineProperty(this, 'tag', {\n            set() {\n                throw new Error('Alias nodes cannot have tags');\n            }\n        });\n    }\n    /**\n     * Resolve the value of this alias within `doc`, finding the last\n     * instance of the `source` anchor before this node.\n     */\n    resolve(doc) {\n        let found = undefined;\n        visit.visit(doc, {\n            Node: (_key, node) => {\n                if (node === this)\n                    return visit.visit.BREAK;\n                if (node.anchor === this.source)\n                    found = node;\n            }\n        });\n        return found;\n    }\n    toJSON(_arg, ctx) {\n        if (!ctx)\n            return { source: this.source };\n        const { anchors, doc, maxAliasCount } = ctx;\n        const source = this.resolve(doc);\n        if (!source) {\n            const msg = `Unresolved alias (the anchor must be set before the alias): ${this.source}`;\n            throw new ReferenceError(msg);\n        }\n        let data = anchors.get(source);\n        if (!data) {\n            // Resolve anchors for Node.prototype.toJS()\n            toJS.toJS(source, null, ctx);\n            data = anchors.get(source);\n        }\n        /* istanbul ignore if */\n        if (!data || data.res === undefined) {\n            const msg = 'This should not happen: Alias anchor was not resolved?';\n            throw new ReferenceError(msg);\n        }\n        if (maxAliasCount >= 0) {\n            data.count += 1;\n            if (data.aliasCount === 0)\n                data.aliasCount = getAliasCount(doc, source, anchors);\n            if (data.count * data.aliasCount > maxAliasCount) {\n                const msg = 'Excessive alias count indicates a resource exhaustion attack';\n                throw new ReferenceError(msg);\n            }\n        }\n        return data.res;\n    }\n    toString(ctx, _onComment, _onChompKeep) {\n        const src = `*${this.source}`;\n        if (ctx) {\n            anchors.anchorIsValid(this.source);\n            if (ctx.options.verifyAliasOrder && !ctx.anchors.has(this.source)) {\n                const msg = `Unresolved alias (the anchor must be set before the alias): ${this.source}`;\n                throw new Error(msg);\n            }\n            if (ctx.implicitKey)\n                return `${src} `;\n        }\n        return src;\n    }\n}\nfunction getAliasCount(doc, node, anchors) {\n    if (identity.isAlias(node)) {\n        const source = node.resolve(doc);\n        const anchor = anchors && source && anchors.get(source);\n        return anchor ? anchor.count * anchor.aliasCount : 0;\n    }\n    else if (identity.isCollection(node)) {\n        let count = 0;\n        for (const item of node.items) {\n            const c = getAliasCount(doc, item, anchors);\n            if (c > count)\n                count = c;\n        }\n        return count;\n    }\n    else if (identity.isPair(node)) {\n        const kc = getAliasCount(doc, node.key, anchors);\n        const vc = getAliasCount(doc, node.value, anchors);\n        return Math.max(kc, vc);\n    }\n    return 1;\n}\n\nexports.Alias = Alias;\n", "'use strict';\n\nvar identity = require('./identity.js');\nvar Node = require('./Node.js');\nvar toJS = require('./toJS.js');\n\nconst isScalarValue = (value) => !value || (typeof value !== 'function' && typeof value !== 'object');\nclass Scalar extends Node.NodeBase {\n    constructor(value) {\n        super(identity.SCALAR);\n        this.value = value;\n    }\n    toJSON(arg, ctx) {\n        return ctx?.keep ? this.value : toJS.toJS(this.value, arg, ctx);\n    }\n    toString() {\n        return String(this.value);\n    }\n}\nScalar.BLOCK_FOLDED = 'BLOCK_FOLDED';\nScalar.BLOCK_LITERAL = 'BLOCK_LITERAL';\nScalar.PLAIN = 'PLAIN';\nScalar.QUOTE_DOUBLE = 'QUOTE_DOUBLE';\nScalar.QUOTE_SINGLE = 'QUOTE_SINGLE';\n\nexports.Scalar = Scalar;\nexports.isScalarValue = isScalarValue;\n", "'use strict';\n\nvar Alias = require('../nodes/Alias.js');\nvar identity = require('../nodes/identity.js');\nvar Scalar = require('../nodes/Scalar.js');\n\nconst defaultTagPrefix = 'tag:yaml.org,2002:';\nfunction findTagObject(value, tagName, tags) {\n    if (tagName) {\n        const match = tags.filter(t => t.tag === tagName);\n        const tagObj = match.find(t => !t.format) ?? match[0];\n        if (!tagObj)\n            throw new Error(`Tag ${tagName} not found`);\n        return tagObj;\n    }\n    return tags.find(t => t.identify?.(value) && !t.format);\n}\nfunction createNode(value, tagName, ctx) {\n    if (identity.isDocument(value))\n        value = value.contents;\n    if (identity.isNode(value))\n        return value;\n    if (identity.isPair(value)) {\n        const map = ctx.schema[identity.MAP].createNode?.(ctx.schema, null, ctx);\n        map.items.push(value);\n        return map;\n    }\n    if (value instanceof String ||\n        value instanceof Number ||\n        value instanceof Boolean ||\n        (typeof BigInt !== 'undefined' && value instanceof BigInt) // not supported everywhere\n    ) {\n        // https://tc39.es/ecma262/#sec-serializejsonproperty\n        value = value.valueOf();\n    }\n    const { aliasDuplicateObjects, onAnchor, onTagObj, schema, sourceObjects } = ctx;\n    // Detect duplicate references to the same object & use Alias nodes for all\n    // after first. The `ref` wrapper allows for circular references to resolve.\n    let ref = undefined;\n    if (aliasDuplicateObjects && value && typeof value === 'object') {\n        ref = sourceObjects.get(value);\n        if (ref) {\n            if (!ref.anchor)\n                ref.anchor = onAnchor(value);\n            return new Alias.Alias(ref.anchor);\n        }\n        else {\n            ref = { anchor: null, node: null };\n            sourceObjects.set(value, ref);\n        }\n    }\n    if (tagName?.startsWith('!!'))\n        tagName = defaultTagPrefix + tagName.slice(2);\n    let tagObj = findTagObject(value, tagName, schema.tags);\n    if (!tagObj) {\n        if (value && typeof value.toJSON === 'function') {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n            value = value.toJSON();\n        }\n        if (!value || typeof value !== 'object') {\n            const node = new Scalar.Scalar(value);\n            if (ref)\n                ref.node = node;\n            return node;\n        }\n        tagObj =\n            value instanceof Map\n                ? schema[identity.MAP]\n                : Symbol.iterator in Object(value)\n                    ? schema[identity.SEQ]\n                    : schema[identity.MAP];\n    }\n    if (onTagObj) {\n        onTagObj(tagObj);\n        delete ctx.onTagObj;\n    }\n    const node = tagObj?.createNode\n        ? tagObj.createNode(ctx.schema, value, ctx)\n        : typeof tagObj?.nodeClass?.from === 'function'\n            ? tagObj.nodeClass.from(ctx.schema, value, ctx)\n            : new Scalar.Scalar(value);\n    if (tagName)\n        node.tag = tagName;\n    else if (!tagObj.default)\n        node.tag = tagObj.tag;\n    if (ref)\n        ref.node = node;\n    return node;\n}\n\nexports.createNode = createNode;\n", "'use strict';\n\nvar createNode = require('../doc/createNode.js');\nvar identity = require('./identity.js');\nvar Node = require('./Node.js');\n\nfunction collectionFromPath(schema, path, value) {\n    let v = value;\n    for (let i = path.length - 1; i >= 0; --i) {\n        const k = path[i];\n        if (typeof k === 'number' && Number.isInteger(k) && k >= 0) {\n            const a = [];\n            a[k] = v;\n            v = a;\n        }\n        else {\n            v = new Map([[k, v]]);\n        }\n    }\n    return createNode.createNode(v, undefined, {\n        aliasDuplicateObjects: false,\n        keepUndefined: false,\n        onAnchor: () => {\n            throw new Error('This should not happen, please report a bug.');\n        },\n        schema,\n        sourceObjects: new Map()\n    });\n}\n// Type guard is intentionally a little wrong so as to be more useful,\n// as it does not cover untypable empty non-string iterables (e.g. []).\nconst isEmptyPath = (path) => path == null ||\n    (typeof path === 'object' && !!path[Symbol.iterator]().next().done);\nclass Collection extends Node.NodeBase {\n    constructor(type, schema) {\n        super(type);\n        Object.defineProperty(this, 'schema', {\n            value: schema,\n            configurable: true,\n            enumerable: false,\n            writable: true\n        });\n    }\n    /**\n     * Create a copy of this collection.\n     *\n     * @param schema - If defined, overwrites the original's schema\n     */\n    clone(schema) {\n        const copy = Object.create(Object.getPrototypeOf(this), Object.getOwnPropertyDescriptors(this));\n        if (schema)\n            copy.schema = schema;\n        copy.items = copy.items.map(it => identity.isNode(it) || identity.isPair(it) ? it.clone(schema) : it);\n        if (this.range)\n            copy.range = this.range.slice();\n        return copy;\n    }\n    /**\n     * Adds a value to the collection. For `!!map` and `!!omap` the value must\n     * be a Pair instance or a `{ key, value }` object, which may not have a key\n     * that already exists in the map.\n     */\n    addIn(path, value) {\n        if (isEmptyPath(path))\n            this.add(value);\n        else {\n            const [key, ...rest] = path;\n            const node = this.get(key, true);\n            if (identity.isCollection(node))\n                node.addIn(rest, value);\n            else if (node === undefined && this.schema)\n                this.set(key, collectionFromPath(this.schema, rest, value));\n            else\n                throw new Error(`Expected YAML collection at ${key}. Remaining path: ${rest}`);\n        }\n    }\n    /**\n     * Removes a value from the collection.\n     * @returns `true` if the item was found and removed.\n     */\n    deleteIn(path) {\n        const [key, ...rest] = path;\n        if (rest.length === 0)\n            return this.delete(key);\n        const node = this.get(key, true);\n        if (identity.isCollection(node))\n            return node.deleteIn(rest);\n        else\n            throw new Error(`Expected YAML collection at ${key}. Remaining path: ${rest}`);\n    }\n    /**\n     * Returns item at `key`, or `undefined` if not found. By default unwraps\n     * scalar values from their surrounding node; to disable set `keepScalar` to\n     * `true` (collections are always returned intact).\n     */\n    getIn(path, keepScalar) {\n        const [key, ...rest] = path;\n        const node = this.get(key, true);\n        if (rest.length === 0)\n            return !keepScalar && identity.isScalar(node) ? node.value : node;\n        else\n            return identity.isCollection(node) ? node.getIn(rest, keepScalar) : undefined;\n    }\n    hasAllNullValues(allowScalar) {\n        return this.items.every(node => {\n            if (!identity.isPair(node))\n                return false;\n            const n = node.value;\n            return (n == null ||\n                (allowScalar &&\n                    identity.isScalar(n) &&\n                    n.value == null &&\n                    !n.commentBefore &&\n                    !n.comment &&\n                    !n.tag));\n        });\n    }\n    /**\n     * Checks if the collection includes a value with the key `key`.\n     */\n    hasIn(path) {\n        const [key, ...rest] = path;\n        if (rest.length === 0)\n            return this.has(key);\n        const node = this.get(key, true);\n        return identity.isCollection(node) ? node.hasIn(rest) : false;\n    }\n    /**\n     * Sets a value in this collection. For `!!set`, `value` needs to be a\n     * boolean to add/remove the item from the set.\n     */\n    setIn(path, value) {\n        const [key, ...rest] = path;\n        if (rest.length === 0) {\n            this.set(key, value);\n        }\n        else {\n            const node = this.get(key, true);\n            if (identity.isCollection(node))\n                node.setIn(rest, value);\n            else if (node === undefined && this.schema)\n                this.set(key, collectionFromPath(this.schema, rest, value));\n            else\n                throw new Error(`Expected YAML collection at ${key}. Remaining path: ${rest}`);\n        }\n    }\n}\n\nexports.Collection = Collection;\nexports.collectionFromPath = collectionFromPath;\nexports.isEmptyPath = isEmptyPath;\n", "'use strict';\n\n/**\n * Stringifies a comment.\n *\n * Empty comment lines are left empty,\n * lines consisting of a single space are replaced by `#`,\n * and all other lines are prefixed with a `#`.\n */\nconst stringifyComment = (str) => str.replace(/^(?!$)(?: $)?/gm, '#');\nfunction indentComment(comment, indent) {\n    if (/^\\n+$/.test(comment))\n        return comment.substring(1);\n    return indent ? comment.replace(/^(?! *$)/gm, indent) : comment;\n}\nconst lineComment = (str, indent, comment) => str.endsWith('\\n')\n    ? indentComment(comment, indent)\n    : comment.includes('\\n')\n        ? '\\n' + indentComment(comment, indent)\n        : (str.endsWith(' ') ? '' : ' ') + comment;\n\nexports.indentComment = indentComment;\nexports.lineComment = lineComment;\nexports.stringifyComment = stringifyComment;\n", "'use strict';\n\nconst FOLD_FLOW = 'flow';\nconst FOLD_BLOCK = 'block';\nconst FOLD_QUOTED = 'quoted';\n/**\n * Tries to keep input at up to `lineWidth` characters, splitting only on spaces\n * not followed by newlines or spaces unless `mode` is `'quoted'`. Lines are\n * terminated with `\\n` and started with `indent`.\n */\nfunction foldFlowLines(text, indent, mode = 'flow', { indentAtStart, lineWidth = 80, minContentWidth = 20, onFold, onOverflow } = {}) {\n    if (!lineWidth || lineWidth < 0)\n        return text;\n    if (lineWidth < minContentWidth)\n        minContentWidth = 0;\n    const endStep = Math.max(1 + minContentWidth, 1 + lineWidth - indent.length);\n    if (text.length <= endStep)\n        return text;\n    const folds = [];\n    const escapedFolds = {};\n    let end = lineWidth - indent.length;\n    if (typeof indentAtStart === 'number') {\n        if (indentAtStart > lineWidth - Math.max(2, minContentWidth))\n            folds.push(0);\n        else\n            end = lineWidth - indentAtStart;\n    }\n    let split = undefined;\n    let prev = undefined;\n    let overflow = false;\n    let i = -1;\n    let escStart = -1;\n    let escEnd = -1;\n    if (mode === FOLD_BLOCK) {\n        i = consumeMoreIndentedLines(text, i, indent.length);\n        if (i !== -1)\n            end = i + endStep;\n    }\n    for (let ch; (ch = text[(i += 1)]);) {\n        if (mode === FOLD_QUOTED && ch === '\\\\') {\n            escStart = i;\n            switch (text[i + 1]) {\n                case 'x':\n                    i += 3;\n                    break;\n                case 'u':\n                    i += 5;\n                    break;\n                case 'U':\n                    i += 9;\n                    break;\n                default:\n                    i += 1;\n            }\n            escEnd = i;\n        }\n        if (ch === '\\n') {\n            if (mode === FOLD_BLOCK)\n                i = consumeMoreIndentedLines(text, i, indent.length);\n            end = i + indent.length + endStep;\n            split = undefined;\n        }\n        else {\n            if (ch === ' ' &&\n                prev &&\n                prev !== ' ' &&\n                prev !== '\\n' &&\n                prev !== '\\t') {\n                // space surrounded by non-space can be replaced with newline + indent\n                const next = text[i + 1];\n                if (next && next !== ' ' && next !== '\\n' && next !== '\\t')\n                    split = i;\n            }\n            if (i >= end) {\n                if (split) {\n                    folds.push(split);\n                    end = split + endStep;\n                    split = undefined;\n                }\n                else if (mode === FOLD_QUOTED) {\n                    // white-space collected at end may stretch past lineWidth\n                    while (prev === ' ' || prev === '\\t') {\n                        prev = ch;\n                        ch = text[(i += 1)];\n                        overflow = true;\n                    }\n                    // Account for newline escape, but don't break preceding escape\n                    const j = i > escEnd + 1 ? i - 2 : escStart - 1;\n                    // Bail out if lineWidth & minContentWidth are shorter than an escape string\n                    if (escapedFolds[j])\n                        return text;\n                    folds.push(j);\n                    escapedFolds[j] = true;\n                    end = j + endStep;\n                    split = undefined;\n                }\n                else {\n                    overflow = true;\n                }\n            }\n        }\n        prev = ch;\n    }\n    if (overflow && onOverflow)\n        onOverflow();\n    if (folds.length === 0)\n        return text;\n    if (onFold)\n        onFold();\n    let res = text.slice(0, folds[0]);\n    for (let i = 0; i < folds.length; ++i) {\n        const fold = folds[i];\n        const end = folds[i + 1] || text.length;\n        if (fold === 0)\n            res = `\\n${indent}${text.slice(0, end)}`;\n        else {\n            if (mode === FOLD_QUOTED && escapedFolds[fold])\n                res += `${text[fold]}\\\\`;\n            res += `\\n${indent}${text.slice(fold + 1, end)}`;\n        }\n    }\n    return res;\n}\n/**\n * Presumes `i + 1` is at the start of a line\n * @returns index of last newline in more-indented block\n */\nfunction consumeMoreIndentedLines(text, i, indent) {\n    let end = i;\n    let start = i + 1;\n    let ch = text[start];\n    while (ch === ' ' || ch === '\\t') {\n        if (i < start + indent) {\n            ch = text[++i];\n        }\n        else {\n            do {\n                ch = text[++i];\n            } while (ch && ch !== '\\n');\n            end = i;\n            start = i + 1;\n            ch = text[start];\n        }\n    }\n    return end;\n}\n\nexports.FOLD_BLOCK = FOLD_BLOCK;\nexports.FOLD_FLOW = FOLD_FLOW;\nexports.FOLD_QUOTED = FOLD_QUOTED;\nexports.foldFlowLines = foldFlowLines;\n", "'use strict';\n\nvar Scalar = require('../nodes/Scalar.js');\nvar foldFlowLines = require('./foldFlowLines.js');\n\nconst getFoldOptions = (ctx, isBlock) => ({\n    indentAtStart: isBlock ? ctx.indent.length : ctx.indentAtStart,\n    lineWidth: ctx.options.lineWidth,\n    minContentWidth: ctx.options.minContentWidth\n});\n// Also checks for lines starting with %, as parsing the output as YAML 1.1 will\n// presume that's starting a new document.\nconst containsDocumentMarker = (str) => /^(%|---|\\.\\.\\.)/m.test(str);\nfunction lineLengthOverLimit(str, lineWidth, indentLength) {\n    if (!lineWidth || lineWidth < 0)\n        return false;\n    const limit = lineWidth - indentLength;\n    const strLen = str.length;\n    if (strLen <= limit)\n        return false;\n    for (let i = 0, start = 0; i < strLen; ++i) {\n        if (str[i] === '\\n') {\n            if (i - start > limit)\n                return true;\n            start = i + 1;\n            if (strLen - start <= limit)\n                return false;\n        }\n    }\n    return true;\n}\nfunction doubleQuotedString(value, ctx) {\n    const json = JSON.stringify(value);\n    if (ctx.options.doubleQuotedAsJSON)\n        return json;\n    const { implicitKey } = ctx;\n    const minMultiLineLength = ctx.options.doubleQuotedMinMultiLineLength;\n    const indent = ctx.indent || (containsDocumentMarker(value) ? '  ' : '');\n    let str = '';\n    let start = 0;\n    for (let i = 0, ch = json[i]; ch; ch = json[++i]) {\n        if (ch === ' ' && json[i + 1] === '\\\\' && json[i + 2] === 'n') {\n            // space before newline needs to be escaped to not be folded\n            str += json.slice(start, i) + '\\\\ ';\n            i += 1;\n            start = i;\n            ch = '\\\\';\n        }\n        if (ch === '\\\\')\n            switch (json[i + 1]) {\n                case 'u':\n                    {\n                        str += json.slice(start, i);\n                        const code = json.substr(i + 2, 4);\n                        switch (code) {\n                            case '0000':\n                                str += '\\\\0';\n                                break;\n                            case '0007':\n                                str += '\\\\a';\n                                break;\n                            case '000b':\n                                str += '\\\\v';\n                                break;\n                            case '001b':\n                                str += '\\\\e';\n                                break;\n                            case '0085':\n                                str += '\\\\N';\n                                break;\n                            case '00a0':\n                                str += '\\\\_';\n                                break;\n                            case '2028':\n                                str += '\\\\L';\n                                break;\n                            case '2029':\n                                str += '\\\\P';\n                                break;\n                            default:\n                                if (code.substr(0, 2) === '00')\n                                    str += '\\\\x' + code.substr(2);\n                                else\n                                    str += json.substr(i, 6);\n                        }\n                        i += 5;\n                        start = i + 1;\n                    }\n                    break;\n                case 'n':\n                    if (implicitKey ||\n                        json[i + 2] === '\"' ||\n                        json.length < minMultiLineLength) {\n                        i += 1;\n                    }\n                    else {\n                        // folding will eat first newline\n                        str += json.slice(start, i) + '\\n\\n';\n                        while (json[i + 2] === '\\\\' &&\n                            json[i + 3] === 'n' &&\n                            json[i + 4] !== '\"') {\n                            str += '\\n';\n                            i += 2;\n                        }\n                        str += indent;\n                        // space after newline needs to be escaped to not be folded\n                        if (json[i + 2] === ' ')\n                            str += '\\\\';\n                        i += 1;\n                        start = i + 1;\n                    }\n                    break;\n                default:\n                    i += 1;\n            }\n    }\n    str = start ? str + json.slice(start) : json;\n    return implicitKey\n        ? str\n        : foldFlowLines.foldFlowLines(str, indent, foldFlowLines.FOLD_QUOTED, getFoldOptions(ctx, false));\n}\nfunction singleQuotedString(value, ctx) {\n    if (ctx.options.singleQuote === false ||\n        (ctx.implicitKey && value.includes('\\n')) ||\n        /[ \\t]\\n|\\n[ \\t]/.test(value) // single quoted string can't have leading or trailing whitespace around newline\n    )\n        return doubleQuotedString(value, ctx);\n    const indent = ctx.indent || (containsDocumentMarker(value) ? '  ' : '');\n    const res = \"'\" + value.replace(/'/g, \"''\").replace(/\\n+/g, `$&\\n${indent}`) + \"'\";\n    return ctx.implicitKey\n        ? res\n        : foldFlowLines.foldFlowLines(res, indent, foldFlowLines.FOLD_FLOW, getFoldOptions(ctx, false));\n}\nfunction quotedString(value, ctx) {\n    const { singleQuote } = ctx.options;\n    let qs;\n    if (singleQuote === false)\n        qs = doubleQuotedString;\n    else {\n        const hasDouble = value.includes('\"');\n        const hasSingle = value.includes(\"'\");\n        if (hasDouble && !hasSingle)\n            qs = singleQuotedString;\n        else if (hasSingle && !hasDouble)\n            qs = doubleQuotedString;\n        else\n            qs = singleQuote ? singleQuotedString : doubleQuotedString;\n    }\n    return qs(value, ctx);\n}\n// The negative lookbehind avoids a polynomial search,\n// but isn't supported yet on Safari: https://caniuse.com/js-regexp-lookbehind\nlet blockEndNewlines;\ntry {\n    blockEndNewlines = new RegExp('(^|(?<!\\n))\\n+(?!\\n|$)', 'g');\n}\ncatch {\n    blockEndNewlines = /\\n+(?!\\n|$)/g;\n}\nfunction blockString({ comment, type, value }, ctx, onComment, onChompKeep) {\n    const { blockQuote, commentString, lineWidth } = ctx.options;\n    // 1. Block can't end in whitespace unless the last line is non-empty.\n    // 2. Strings consisting of only whitespace are best rendered explicitly.\n    if (!blockQuote || /\\n[\\t ]+$/.test(value) || /^\\s*$/.test(value)) {\n        return quotedString(value, ctx);\n    }\n    const indent = ctx.indent ||\n        (ctx.forceBlockIndent || containsDocumentMarker(value) ? '  ' : '');\n    const literal = blockQuote === 'literal'\n        ? true\n        : blockQuote === 'folded' || type === Scalar.Scalar.BLOCK_FOLDED\n            ? false\n            : type === Scalar.Scalar.BLOCK_LITERAL\n                ? true\n                : !lineLengthOverLimit(value, lineWidth, indent.length);\n    if (!value)\n        return literal ? '|\\n' : '>\\n';\n    // determine chomping from whitespace at value end\n    let chomp;\n    let endStart;\n    for (endStart = value.length; endStart > 0; --endStart) {\n        const ch = value[endStart - 1];\n        if (ch !== '\\n' && ch !== '\\t' && ch !== ' ')\n            break;\n    }\n    let end = value.substring(endStart);\n    const endNlPos = end.indexOf('\\n');\n    if (endNlPos === -1) {\n        chomp = '-'; // strip\n    }\n    else if (value === end || endNlPos !== end.length - 1) {\n        chomp = '+'; // keep\n        if (onChompKeep)\n            onChompKeep();\n    }\n    else {\n        chomp = ''; // clip\n    }\n    if (end) {\n        value = value.slice(0, -end.length);\n        if (end[end.length - 1] === '\\n')\n            end = end.slice(0, -1);\n        end = end.replace(blockEndNewlines, `$&${indent}`);\n    }\n    // determine indent indicator from whitespace at value start\n    let startWithSpace = false;\n    let startEnd;\n    let startNlPos = -1;\n    for (startEnd = 0; startEnd < value.length; ++startEnd) {\n        const ch = value[startEnd];\n        if (ch === ' ')\n            startWithSpace = true;\n        else if (ch === '\\n')\n            startNlPos = startEnd;\n        else\n            break;\n    }\n    let start = value.substring(0, startNlPos < startEnd ? startNlPos + 1 : startEnd);\n    if (start) {\n        value = value.substring(start.length);\n        start = start.replace(/\\n+/g, `$&${indent}`);\n    }\n    const indentSize = indent ? '2' : '1'; // root is at -1\n    // Leading | or > is added later\n    let header = (startWithSpace ? indentSize : '') + chomp;\n    if (comment) {\n        header += ' ' + commentString(comment.replace(/ ?[\\r\\n]+/g, ' '));\n        if (onComment)\n            onComment();\n    }\n    if (!literal) {\n        const foldedValue = value\n            .replace(/\\n+/g, '\\n$&')\n            .replace(/(?:^|\\n)([\\t ].*)(?:([\\n\\t ]*)\\n(?![\\n\\t ]))?/g, '$1$2') // more-indented lines aren't folded\n            //                ^ more-ind. ^ empty     ^ capture next empty lines only at end of indent\n            .replace(/\\n+/g, `$&${indent}`);\n        let literalFallback = false;\n        const foldOptions = getFoldOptions(ctx, true);\n        if (blockQuote !== 'folded' && type !== Scalar.Scalar.BLOCK_FOLDED) {\n            foldOptions.onOverflow = () => {\n                literalFallback = true;\n            };\n        }\n        const body = foldFlowLines.foldFlowLines(`${start}${foldedValue}${end}`, indent, foldFlowLines.FOLD_BLOCK, foldOptions);\n        if (!literalFallback)\n            return `>${header}\\n${indent}${body}`;\n    }\n    value = value.replace(/\\n+/g, `$&${indent}`);\n    return `|${header}\\n${indent}${start}${value}${end}`;\n}\nfunction plainString(item, ctx, onComment, onChompKeep) {\n    const { type, value } = item;\n    const { actualString, implicitKey, indent, indentStep, inFlow } = ctx;\n    if ((implicitKey && value.includes('\\n')) ||\n        (inFlow && /[[\\]{},]/.test(value))) {\n        return quotedString(value, ctx);\n    }\n    if (!value ||\n        /^[\\n\\t ,[\\]{}#&*!|>'\"%@`]|^[?-]$|^[?-][ \\t]|[\\n:][ \\t]|[ \\t]\\n|[\\n\\t ]#|[\\n\\t :]$/.test(value)) {\n        // not allowed:\n        // - empty string, '-' or '?'\n        // - start with an indicator character (except [?:-]) or /[?-] /\n        // - '\\n ', ': ' or ' \\n' anywhere\n        // - '#' not preceded by a non-space char\n        // - end with ' ' or ':'\n        return implicitKey || inFlow || !value.includes('\\n')\n            ? quotedString(value, ctx)\n            : blockString(item, ctx, onComment, onChompKeep);\n    }\n    if (!implicitKey &&\n        !inFlow &&\n        type !== Scalar.Scalar.PLAIN &&\n        value.includes('\\n')) {\n        // Where allowed & type not set explicitly, prefer block style for multiline strings\n        return blockString(item, ctx, onComment, onChompKeep);\n    }\n    if (containsDocumentMarker(value)) {\n        if (indent === '') {\n            ctx.forceBlockIndent = true;\n            return blockString(item, ctx, onComment, onChompKeep);\n        }\n        else if (implicitKey && indent === indentStep) {\n            return quotedString(value, ctx);\n        }\n    }\n    const str = value.replace(/\\n+/g, `$&\\n${indent}`);\n    // Verify that output will be parsed as a string, as e.g. plain numbers and\n    // booleans get parsed with those types in v1.2 (e.g. '42', 'true' & '0.9e-3'),\n    // and others in v1.1.\n    if (actualString) {\n        const test = (tag) => tag.default && tag.tag !== 'tag:yaml.org,2002:str' && tag.test?.test(str);\n        const { compat, tags } = ctx.doc.schema;\n        if (tags.some(test) || compat?.some(test))\n            return quotedString(value, ctx);\n    }\n    return implicitKey\n        ? str\n        : foldFlowLines.foldFlowLines(str, indent, foldFlowLines.FOLD_FLOW, getFoldOptions(ctx, false));\n}\nfunction stringifyString(item, ctx, onComment, onChompKeep) {\n    const { implicitKey, inFlow } = ctx;\n    const ss = typeof item.value === 'string'\n        ? item\n        : Object.assign({}, item, { value: String(item.value) });\n    let { type } = item;\n    if (type !== Scalar.Scalar.QUOTE_DOUBLE) {\n        // force double quotes on control characters & unpaired surrogates\n        if (/[\\x00-\\x08\\x0b-\\x1f\\x7f-\\x9f\\u{D800}-\\u{DFFF}]/u.test(ss.value))\n            type = Scalar.Scalar.QUOTE_DOUBLE;\n    }\n    const _stringify = (_type) => {\n        switch (_type) {\n            case Scalar.Scalar.BLOCK_FOLDED:\n            case Scalar.Scalar.BLOCK_LITERAL:\n                return implicitKey || inFlow\n                    ? quotedString(ss.value, ctx) // blocks are not valid inside flow containers\n                    : blockString(ss, ctx, onComment, onChompKeep);\n            case Scalar.Scalar.QUOTE_DOUBLE:\n                return doubleQuotedString(ss.value, ctx);\n            case Scalar.Scalar.QUOTE_SINGLE:\n                return singleQuotedString(ss.value, ctx);\n            case Scalar.Scalar.PLAIN:\n                return plainString(ss, ctx, onComment, onChompKeep);\n            default:\n                return null;\n        }\n    };\n    let res = _stringify(type);\n    if (res === null) {\n        const { defaultKeyType, defaultStringType } = ctx.options;\n        const t = (implicitKey && defaultKeyType) || defaultStringType;\n        res = _stringify(t);\n        if (res === null)\n            throw new Error(`Unsupported default string type ${t}`);\n    }\n    return res;\n}\n\nexports.stringifyString = stringifyString;\n", "'use strict';\n\nvar anchors = require('../doc/anchors.js');\nvar identity = require('../nodes/identity.js');\nvar stringifyComment = require('./stringifyComment.js');\nvar stringifyString = require('./stringifyString.js');\n\nfunction createStringifyContext(doc, options) {\n    const opt = Object.assign({\n        blockQuote: true,\n        commentString: stringifyComment.stringifyComment,\n        defaultKeyType: null,\n        defaultStringType: 'PLAIN',\n        directives: null,\n        doubleQuotedAsJSON: false,\n        doubleQuotedMinMultiLineLength: 40,\n        falseStr: 'false',\n        flowCollectionPadding: true,\n        indentSeq: true,\n        lineWidth: 80,\n        minContentWidth: 20,\n        nullStr: 'null',\n        simpleKeys: false,\n        singleQuote: null,\n        trueStr: 'true',\n        verifyAliasOrder: true\n    }, doc.schema.toStringOptions, options);\n    let inFlow;\n    switch (opt.collectionStyle) {\n        case 'block':\n            inFlow = false;\n            break;\n        case 'flow':\n            inFlow = true;\n            break;\n        default:\n            inFlow = null;\n    }\n    return {\n        anchors: new Set(),\n        doc,\n        flowCollectionPadding: opt.flowCollectionPadding ? ' ' : '',\n        indent: '',\n        indentStep: typeof opt.indent === 'number' ? ' '.repeat(opt.indent) : '  ',\n        inFlow,\n        options: opt\n    };\n}\nfunction getTagObject(tags, item) {\n    if (item.tag) {\n        const match = tags.filter(t => t.tag === item.tag);\n        if (match.length > 0)\n            return match.find(t => t.format === item.format) ?? match[0];\n    }\n    let tagObj = undefined;\n    let obj;\n    if (identity.isScalar(item)) {\n        obj = item.value;\n        let match = tags.filter(t => t.identify?.(obj));\n        if (match.length > 1) {\n            const testMatch = match.filter(t => t.test);\n            if (testMatch.length > 0)\n                match = testMatch;\n        }\n        tagObj =\n            match.find(t => t.format === item.format) ?? match.find(t => !t.format);\n    }\n    else {\n        obj = item;\n        tagObj = tags.find(t => t.nodeClass && obj instanceof t.nodeClass);\n    }\n    if (!tagObj) {\n        const name = obj?.constructor?.name ?? typeof obj;\n        throw new Error(`Tag not resolved for ${name} value`);\n    }\n    return tagObj;\n}\n// needs to be called before value stringifier to allow for circular anchor refs\nfunction stringifyProps(node, tagObj, { anchors: anchors$1, doc }) {\n    if (!doc.directives)\n        return '';\n    const props = [];\n    const anchor = (identity.isScalar(node) || identity.isCollection(node)) && node.anchor;\n    if (anchor && anchors.anchorIsValid(anchor)) {\n        anchors$1.add(anchor);\n        props.push(`&${anchor}`);\n    }\n    const tag = node.tag ? node.tag : tagObj.default ? null : tagObj.tag;\n    if (tag)\n        props.push(doc.directives.tagString(tag));\n    return props.join(' ');\n}\nfunction stringify(item, ctx, onComment, onChompKeep) {\n    if (identity.isPair(item))\n        return item.toString(ctx, onComment, onChompKeep);\n    if (identity.isAlias(item)) {\n        if (ctx.doc.directives)\n            return item.toString(ctx);\n        if (ctx.resolvedAliases?.has(item)) {\n            throw new TypeError(`Cannot stringify circular structure without alias nodes`);\n        }\n        else {\n            if (ctx.resolvedAliases)\n                ctx.resolvedAliases.add(item);\n            else\n                ctx.resolvedAliases = new Set([item]);\n            item = item.resolve(ctx.doc);\n        }\n    }\n    let tagObj = undefined;\n    const node = identity.isNode(item)\n        ? item\n        : ctx.doc.createNode(item, { onTagObj: o => (tagObj = o) });\n    if (!tagObj)\n        tagObj = getTagObject(ctx.doc.schema.tags, node);\n    const props = stringifyProps(node, tagObj, ctx);\n    if (props.length > 0)\n        ctx.indentAtStart = (ctx.indentAtStart ?? 0) + props.length + 1;\n    const str = typeof tagObj.stringify === 'function'\n        ? tagObj.stringify(node, ctx, onComment, onChompKeep)\n        : identity.isScalar(node)\n            ? stringifyString.stringifyString(node, ctx, onComment, onChompKeep)\n            : node.toString(ctx, onComment, onChompKeep);\n    if (!props)\n        return str;\n    return identity.isScalar(node) || str[0] === '{' || str[0] === '['\n        ? `${props} ${str}`\n        : `${props}\\n${ctx.indent}${str}`;\n}\n\nexports.createStringifyContext = createStringifyContext;\nexports.stringify = stringify;\n", "'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar Scalar = require('../nodes/Scalar.js');\nvar stringify = require('./stringify.js');\nvar stringifyComment = require('./stringifyComment.js');\n\nfunction stringifyPair({ key, value }, ctx, onComment, onChompKeep) {\n    const { allNullValues, doc, indent, indentStep, options: { commentString, indentSeq, simpleKeys } } = ctx;\n    let keyComment = (identity.isNode(key) && key.comment) || null;\n    if (simpleKeys) {\n        if (keyComment) {\n            throw new Error('With simple keys, key nodes cannot have comments');\n        }\n        if (identity.isCollection(key) || (!identity.isNode(key) && typeof key === 'object')) {\n            const msg = 'With simple keys, collection cannot be used as a key value';\n            throw new Error(msg);\n        }\n    }\n    let explicitKey = !simpleKeys &&\n        (!key ||\n            (keyComment && value == null && !ctx.inFlow) ||\n            identity.isCollection(key) ||\n            (identity.isScalar(key)\n                ? key.type === Scalar.Scalar.BLOCK_FOLDED || key.type === Scalar.Scalar.BLOCK_LITERAL\n                : typeof key === 'object'));\n    ctx = Object.assign({}, ctx, {\n        allNullValues: false,\n        implicitKey: !explicitKey && (simpleKeys || !allNullValues),\n        indent: indent + indentStep\n    });\n    let keyCommentDone = false;\n    let chompKeep = false;\n    let str = stringify.stringify(key, ctx, () => (keyCommentDone = true), () => (chompKeep = true));\n    if (!explicitKey && !ctx.inFlow && str.length > 1024) {\n        if (simpleKeys)\n            throw new Error('With simple keys, single line scalar must not span more than 1024 characters');\n        explicitKey = true;\n    }\n    if (ctx.inFlow) {\n        if (allNullValues || value == null) {\n            if (keyCommentDone && onComment)\n                onComment();\n            return str === '' ? '?' : explicitKey ? `? ${str}` : str;\n        }\n    }\n    else if ((allNullValues && !simpleKeys) || (value == null && explicitKey)) {\n        str = `? ${str}`;\n        if (keyComment && !keyCommentDone) {\n            str += stringifyComment.lineComment(str, ctx.indent, commentString(keyComment));\n        }\n        else if (chompKeep && onChompKeep)\n            onChompKeep();\n        return str;\n    }\n    if (keyCommentDone)\n        keyComment = null;\n    if (explicitKey) {\n        if (keyComment)\n            str += stringifyComment.lineComment(str, ctx.indent, commentString(keyComment));\n        str = `? ${str}\\n${indent}:`;\n    }\n    else {\n        str = `${str}:`;\n        if (keyComment)\n            str += stringifyComment.lineComment(str, ctx.indent, commentString(keyComment));\n    }\n    let vsb, vcb, valueComment;\n    if (identity.isNode(value)) {\n        vsb = !!value.spaceBefore;\n        vcb = value.commentBefore;\n        valueComment = value.comment;\n    }\n    else {\n        vsb = false;\n        vcb = null;\n        valueComment = null;\n        if (value && typeof value === 'object')\n            value = doc.createNode(value);\n    }\n    ctx.implicitKey = false;\n    if (!explicitKey && !keyComment && identity.isScalar(value))\n        ctx.indentAtStart = str.length + 1;\n    chompKeep = false;\n    if (!indentSeq &&\n        indentStep.length >= 2 &&\n        !ctx.inFlow &&\n        !explicitKey &&\n        identity.isSeq(value) &&\n        !value.flow &&\n        !value.tag &&\n        !value.anchor) {\n        // If indentSeq === false, consider '- ' as part of indentation where possible\n        ctx.indent = ctx.indent.substring(2);\n    }\n    let valueCommentDone = false;\n    const valueStr = stringify.stringify(value, ctx, () => (valueCommentDone = true), () => (chompKeep = true));\n    let ws = ' ';\n    if (keyComment || vsb || vcb) {\n        ws = vsb ? '\\n' : '';\n        if (vcb) {\n            const cs = commentString(vcb);\n            ws += `\\n${stringifyComment.indentComment(cs, ctx.indent)}`;\n        }\n        if (valueStr === '' && !ctx.inFlow) {\n            if (ws === '\\n')\n                ws = '\\n\\n';\n        }\n        else {\n            ws += `\\n${ctx.indent}`;\n        }\n    }\n    else if (!explicitKey && identity.isCollection(value)) {\n        const vs0 = valueStr[0];\n        const nl0 = valueStr.indexOf('\\n');\n        const hasNewline = nl0 !== -1;\n        const flow = ctx.inFlow ?? value.flow ?? value.items.length === 0;\n        if (hasNewline || !flow) {\n            let hasPropsLine = false;\n            if (hasNewline && (vs0 === '&' || vs0 === '!')) {\n                let sp0 = valueStr.indexOf(' ');\n                if (vs0 === '&' &&\n                    sp0 !== -1 &&\n                    sp0 < nl0 &&\n                    valueStr[sp0 + 1] === '!') {\n                    sp0 = valueStr.indexOf(' ', sp0 + 1);\n                }\n                if (sp0 === -1 || nl0 < sp0)\n                    hasPropsLine = true;\n            }\n            if (!hasPropsLine)\n                ws = `\\n${ctx.indent}`;\n        }\n    }\n    else if (valueStr === '' || valueStr[0] === '\\n') {\n        ws = '';\n    }\n    str += ws + valueStr;\n    if (ctx.inFlow) {\n        if (valueCommentDone && onComment)\n            onComment();\n    }\n    else if (valueComment && !valueCommentDone) {\n        str += stringifyComment.lineComment(str, ctx.indent, commentString(valueComment));\n    }\n    else if (chompKeep && onChompKeep) {\n        onChompKeep();\n    }\n    return str;\n}\n\nexports.stringifyPair = stringifyPair;\n", "'use strict';\n\nvar node_process = require('node:process');\n\nfunction debug(logLevel, ...messages) {\n    if (logLevel === 'debug')\n        console.log(...messages);\n}\nfunction warn(logLevel, warning) {\n    if (logLevel === 'debug' || logLevel === 'warn') {\n        if (typeof node_process.emitWarning === 'function')\n            node_process.emitWarning(warning);\n        else\n            console.warn(warning);\n    }\n}\n\nexports.debug = debug;\nexports.warn = warn;\n", "'use strict';\n\nvar identity = require('../../nodes/identity.js');\nvar Scalar = require('../../nodes/Scalar.js');\n\n// If the value associated with a merge key is a single mapping node, each of\n// its key/value pairs is inserted into the current mapping, unless the key\n// already exists in it. If the value associated with the merge key is a\n// sequence, then this sequence is expected to contain mapping nodes and each\n// of these nodes is merged in turn according to its order in the sequence.\n// Keys in mapping nodes earlier in the sequence override keys specified in\n// later mapping nodes. -- http://yaml.org/type/merge.html\nconst MERGE_KEY = '<<';\nconst merge = {\n    identify: value => value === MERGE_KEY ||\n        (typeof value === 'symbol' && value.description === MERGE_KEY),\n    default: 'key',\n    tag: 'tag:yaml.org,2002:merge',\n    test: /^<<$/,\n    resolve: () => Object.assign(new Scalar.Scalar(Symbol(MERGE_KEY)), {\n        addToJSMap: addMergeToJSMap\n    }),\n    stringify: () => MERGE_KEY\n};\nconst isMergeKey = (ctx, key) => (merge.identify(key) ||\n    (identity.isScalar(key) &&\n        (!key.type || key.type === Scalar.Scalar.PLAIN) &&\n        merge.identify(key.value))) &&\n    ctx?.doc.schema.tags.some(tag => tag.tag === merge.tag && tag.default);\nfunction addMergeToJSMap(ctx, map, value) {\n    value = ctx && identity.isAlias(value) ? value.resolve(ctx.doc) : value;\n    if (identity.isSeq(value))\n        for (const it of value.items)\n            mergeValue(ctx, map, it);\n    else if (Array.isArray(value))\n        for (const it of value)\n            mergeValue(ctx, map, it);\n    else\n        mergeValue(ctx, map, value);\n}\nfunction mergeValue(ctx, map, value) {\n    const source = ctx && identity.isAlias(value) ? value.resolve(ctx.doc) : value;\n    if (!identity.isMap(source))\n        throw new Error('Merge sources must be maps or map aliases');\n    const srcMap = source.toJSON(null, ctx, Map);\n    for (const [key, value] of srcMap) {\n        if (map instanceof Map) {\n            if (!map.has(key))\n                map.set(key, value);\n        }\n        else if (map instanceof Set) {\n            map.add(key);\n        }\n        else if (!Object.prototype.hasOwnProperty.call(map, key)) {\n            Object.defineProperty(map, key, {\n                value,\n                writable: true,\n                enumerable: true,\n                configurable: true\n            });\n        }\n    }\n    return map;\n}\n\nexports.addMergeToJSMap = addMergeToJSMap;\nexports.isMergeKey = isMergeKey;\nexports.merge = merge;\n", "'use strict';\n\nvar log = require('../log.js');\nvar merge = require('../schema/yaml-1.1/merge.js');\nvar stringify = require('../stringify/stringify.js');\nvar identity = require('./identity.js');\nvar toJS = require('./toJS.js');\n\nfunction addPairToJSMap(ctx, map, { key, value }) {\n    if (identity.isNode(key) && key.addToJSMap)\n        key.addToJSMap(ctx, map, value);\n    // TODO: Should drop this special case for bare << handling\n    else if (merge.isMergeKey(ctx, key))\n        merge.addMergeToJSMap(ctx, map, value);\n    else {\n        const jsKey = toJS.toJS(key, '', ctx);\n        if (map instanceof Map) {\n            map.set(jsKey, toJS.toJS(value, jsKey, ctx));\n        }\n        else if (map instanceof Set) {\n            map.add(jsKey);\n        }\n        else {\n            const stringKey = stringifyKey(key, jsKey, ctx);\n            const jsValue = toJS.toJS(value, stringKey, ctx);\n            if (stringKey in map)\n                Object.defineProperty(map, stringKey, {\n                    value: jsValue,\n                    writable: true,\n                    enumerable: true,\n                    configurable: true\n                });\n            else\n                map[stringKey] = jsValue;\n        }\n    }\n    return map;\n}\nfunction stringifyKey(key, jsKey, ctx) {\n    if (jsKey === null)\n        return '';\n    if (typeof jsKey !== 'object')\n        return String(jsKey);\n    if (identity.isNode(key) && ctx?.doc) {\n        const strCtx = stringify.createStringifyContext(ctx.doc, {});\n        strCtx.anchors = new Set();\n        for (const node of ctx.anchors.keys())\n            strCtx.anchors.add(node.anchor);\n        strCtx.inFlow = true;\n        strCtx.inStringifyKey = true;\n        const strKey = key.toString(strCtx);\n        if (!ctx.mapKeyWarned) {\n            let jsonStr = JSON.stringify(strKey);\n            if (jsonStr.length > 40)\n                jsonStr = jsonStr.substring(0, 36) + '...\"';\n            log.warn(ctx.doc.options.logLevel, `Keys with collection values will be stringified due to JS Object restrictions: ${jsonStr}. Set mapAsMap: true to use object keys.`);\n            ctx.mapKeyWarned = true;\n        }\n        return strKey;\n    }\n    return JSON.stringify(jsKey);\n}\n\nexports.addPairToJSMap = addPairToJSMap;\n", "'use strict';\n\nvar createNode = require('../doc/createNode.js');\nvar stringifyPair = require('../stringify/stringifyPair.js');\nvar addPairToJSMap = require('./addPairToJSMap.js');\nvar identity = require('./identity.js');\n\nfunction createPair(key, value, ctx) {\n    const k = createNode.createNode(key, undefined, ctx);\n    const v = createNode.createNode(value, undefined, ctx);\n    return new Pair(k, v);\n}\nclass Pair {\n    constructor(key, value = null) {\n        Object.defineProperty(this, identity.NODE_TYPE, { value: identity.PAIR });\n        this.key = key;\n        this.value = value;\n    }\n    clone(schema) {\n        let { key, value } = this;\n        if (identity.isNode(key))\n            key = key.clone(schema);\n        if (identity.isNode(value))\n            value = value.clone(schema);\n        return new Pair(key, value);\n    }\n    toJSON(_, ctx) {\n        const pair = ctx?.mapAsMap ? new Map() : {};\n        return addPairToJSMap.addPairToJSMap(ctx, pair, this);\n    }\n    toString(ctx, onComment, onChompKeep) {\n        return ctx?.doc\n            ? stringifyPair.stringifyPair(this, ctx, onComment, onChompKeep)\n            : JSON.stringify(this);\n    }\n}\n\nexports.Pair = Pair;\nexports.createPair = createPair;\n", "'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar stringify = require('./stringify.js');\nvar stringifyComment = require('./stringifyComment.js');\n\nfunction stringifyCollection(collection, ctx, options) {\n    const flow = ctx.inFlow ?? collection.flow;\n    const stringify = flow ? stringifyFlowCollection : stringifyBlockCollection;\n    return stringify(collection, ctx, options);\n}\nfunction stringifyBlockCollection({ comment, items }, ctx, { blockItemPrefix, flowChars, itemIndent, onChompKeep, onComment }) {\n    const { indent, options: { commentString } } = ctx;\n    const itemCtx = Object.assign({}, ctx, { indent: itemIndent, type: null });\n    let chompKeep = false; // flag for the preceding node's status\n    const lines = [];\n    for (let i = 0; i < items.length; ++i) {\n        const item = items[i];\n        let comment = null;\n        if (identity.isNode(item)) {\n            if (!chompKeep && item.spaceBefore)\n                lines.push('');\n            addCommentBefore(ctx, lines, item.commentBefore, chompKeep);\n            if (item.comment)\n                comment = item.comment;\n        }\n        else if (identity.isPair(item)) {\n            const ik = identity.isNode(item.key) ? item.key : null;\n            if (ik) {\n                if (!chompKeep && ik.spaceBefore)\n                    lines.push('');\n                addCommentBefore(ctx, lines, ik.commentBefore, chompKeep);\n            }\n        }\n        chompKeep = false;\n        let str = stringify.stringify(item, itemCtx, () => (comment = null), () => (chompKeep = true));\n        if (comment)\n            str += stringifyComment.lineComment(str, itemIndent, commentString(comment));\n        if (chompKeep && comment)\n            chompKeep = false;\n        lines.push(blockItemPrefix + str);\n    }\n    let str;\n    if (lines.length === 0) {\n        str = flowChars.start + flowChars.end;\n    }\n    else {\n        str = lines[0];\n        for (let i = 1; i < lines.length; ++i) {\n            const line = lines[i];\n            str += line ? `\\n${indent}${line}` : '\\n';\n        }\n    }\n    if (comment) {\n        str += '\\n' + stringifyComment.indentComment(commentString(comment), indent);\n        if (onComment)\n            onComment();\n    }\n    else if (chompKeep && onChompKeep)\n        onChompKeep();\n    return str;\n}\nfunction stringifyFlowCollection({ items }, ctx, { flowChars, itemIndent }) {\n    const { indent, indentStep, flowCollectionPadding: fcPadding, options: { commentString } } = ctx;\n    itemIndent += indentStep;\n    const itemCtx = Object.assign({}, ctx, {\n        indent: itemIndent,\n        inFlow: true,\n        type: null\n    });\n    let reqNewline = false;\n    let linesAtValue = 0;\n    const lines = [];\n    for (let i = 0; i < items.length; ++i) {\n        const item = items[i];\n        let comment = null;\n        if (identity.isNode(item)) {\n            if (item.spaceBefore)\n                lines.push('');\n            addCommentBefore(ctx, lines, item.commentBefore, false);\n            if (item.comment)\n                comment = item.comment;\n        }\n        else if (identity.isPair(item)) {\n            const ik = identity.isNode(item.key) ? item.key : null;\n            if (ik) {\n                if (ik.spaceBefore)\n                    lines.push('');\n                addCommentBefore(ctx, lines, ik.commentBefore, false);\n                if (ik.comment)\n                    reqNewline = true;\n            }\n            const iv = identity.isNode(item.value) ? item.value : null;\n            if (iv) {\n                if (iv.comment)\n                    comment = iv.comment;\n                if (iv.commentBefore)\n                    reqNewline = true;\n            }\n            else if (item.value == null && ik?.comment) {\n                comment = ik.comment;\n            }\n        }\n        if (comment)\n            reqNewline = true;\n        let str = stringify.stringify(item, itemCtx, () => (comment = null));\n        if (i < items.length - 1)\n            str += ',';\n        if (comment)\n            str += stringifyComment.lineComment(str, itemIndent, commentString(comment));\n        if (!reqNewline && (lines.length > linesAtValue || str.includes('\\n')))\n            reqNewline = true;\n        lines.push(str);\n        linesAtValue = lines.length;\n    }\n    const { start, end } = flowChars;\n    if (lines.length === 0) {\n        return start + end;\n    }\n    else {\n        if (!reqNewline) {\n            const len = lines.reduce((sum, line) => sum + line.length + 2, 2);\n            reqNewline = ctx.options.lineWidth > 0 && len > ctx.options.lineWidth;\n        }\n        if (reqNewline) {\n            let str = start;\n            for (const line of lines)\n                str += line ? `\\n${indentStep}${indent}${line}` : '\\n';\n            return `${str}\\n${indent}${end}`;\n        }\n        else {\n            return `${start}${fcPadding}${lines.join(' ')}${fcPadding}${end}`;\n        }\n    }\n}\nfunction addCommentBefore({ indent, options: { commentString } }, lines, comment, chompKeep) {\n    if (comment && chompKeep)\n        comment = comment.replace(/^\\n+/, '');\n    if (comment) {\n        const ic = stringifyComment.indentComment(commentString(comment), indent);\n        lines.push(ic.trimStart()); // Avoid double indent on first line\n    }\n}\n\nexports.stringifyCollection = stringifyCollection;\n", "'use strict';\n\nvar stringifyCollection = require('../stringify/stringifyCollection.js');\nvar addPairToJSMap = require('./addPairToJSMap.js');\nvar Collection = require('./Collection.js');\nvar identity = require('./identity.js');\nvar Pair = require('./Pair.js');\nvar Scalar = require('./Scalar.js');\n\nfunction findPair(items, key) {\n    const k = identity.isScalar(key) ? key.value : key;\n    for (const it of items) {\n        if (identity.isPair(it)) {\n            if (it.key === key || it.key === k)\n                return it;\n            if (identity.isScalar(it.key) && it.key.value === k)\n                return it;\n        }\n    }\n    return undefined;\n}\nclass YAMLMap extends Collection.Collection {\n    static get tagName() {\n        return 'tag:yaml.org,2002:map';\n    }\n    constructor(schema) {\n        super(identity.MAP, schema);\n        this.items = [];\n    }\n    /**\n     * A generic collection parsing method that can be extended\n     * to other node classes that inherit from YAMLMap\n     */\n    static from(schema, obj, ctx) {\n        const { keepUndefined, replacer } = ctx;\n        const map = new this(schema);\n        const add = (key, value) => {\n            if (typeof replacer === 'function')\n                value = replacer.call(obj, key, value);\n            else if (Array.isArray(replacer) && !replacer.includes(key))\n                return;\n            if (value !== undefined || keepUndefined)\n                map.items.push(Pair.createPair(key, value, ctx));\n        };\n        if (obj instanceof Map) {\n            for (const [key, value] of obj)\n                add(key, value);\n        }\n        else if (obj && typeof obj === 'object') {\n            for (const key of Object.keys(obj))\n                add(key, obj[key]);\n        }\n        if (typeof schema.sortMapEntries === 'function') {\n            map.items.sort(schema.sortMapEntries);\n        }\n        return map;\n    }\n    /**\n     * Adds a value to the collection.\n     *\n     * @param overwrite - If not set `true`, using a key that is already in the\n     *   collection will throw. Otherwise, overwrites the previous value.\n     */\n    add(pair, overwrite) {\n        let _pair;\n        if (identity.isPair(pair))\n            _pair = pair;\n        else if (!pair || typeof pair !== 'object' || !('key' in pair)) {\n            // In TypeScript, this never happens.\n            _pair = new Pair.Pair(pair, pair?.value);\n        }\n        else\n            _pair = new Pair.Pair(pair.key, pair.value);\n        const prev = findPair(this.items, _pair.key);\n        const sortEntries = this.schema?.sortMapEntries;\n        if (prev) {\n            if (!overwrite)\n                throw new Error(`Key ${_pair.key} already set`);\n            // For scalars, keep the old node & its comments and anchors\n            if (identity.isScalar(prev.value) && Scalar.isScalarValue(_pair.value))\n                prev.value.value = _pair.value;\n            else\n                prev.value = _pair.value;\n        }\n        else if (sortEntries) {\n            const i = this.items.findIndex(item => sortEntries(_pair, item) < 0);\n            if (i === -1)\n                this.items.push(_pair);\n            else\n                this.items.splice(i, 0, _pair);\n        }\n        else {\n            this.items.push(_pair);\n        }\n    }\n    delete(key) {\n        const it = findPair(this.items, key);\n        if (!it)\n            return false;\n        const del = this.items.splice(this.items.indexOf(it), 1);\n        return del.length > 0;\n    }\n    get(key, keepScalar) {\n        const it = findPair(this.items, key);\n        const node = it?.value;\n        return (!keepScalar && identity.isScalar(node) ? node.value : node) ?? undefined;\n    }\n    has(key) {\n        return !!findPair(this.items, key);\n    }\n    set(key, value) {\n        this.add(new Pair.Pair(key, value), true);\n    }\n    /**\n     * @param ctx - Conversion context, originally set in Document#toJS()\n     * @param {Class} Type - If set, forces the returned collection type\n     * @returns Instance of Type, Map, or Object\n     */\n    toJSON(_, ctx, Type) {\n        const map = Type ? new Type() : ctx?.mapAsMap ? new Map() : {};\n        if (ctx?.onCreate)\n            ctx.onCreate(map);\n        for (const item of this.items)\n            addPairToJSMap.addPairToJSMap(ctx, map, item);\n        return map;\n    }\n    toString(ctx, onComment, onChompKeep) {\n        if (!ctx)\n            return JSON.stringify(this);\n        for (const item of this.items) {\n            if (!identity.isPair(item))\n                throw new Error(`Map items must all be pairs; found ${JSON.stringify(item)} instead`);\n        }\n        if (!ctx.allNullValues && this.hasAllNullValues(false))\n            ctx = Object.assign({}, ctx, { allNullValues: true });\n        return stringifyCollection.stringifyCollection(this, ctx, {\n            blockItemPrefix: '',\n            flowChars: { start: '{', end: '}' },\n            itemIndent: ctx.indent || '',\n            onChompKeep,\n            onComment\n        });\n    }\n}\n\nexports.YAMLMap = YAMLMap;\nexports.findPair = findPair;\n", "'use strict';\n\nvar identity = require('../../nodes/identity.js');\nvar YAMLMap = require('../../nodes/YAMLMap.js');\n\nconst map = {\n    collection: 'map',\n    default: true,\n    nodeClass: YAMLMap.YAMLMap,\n    tag: 'tag:yaml.org,2002:map',\n    resolve(map, onError) {\n        if (!identity.isMap(map))\n            onError('Expected a mapping for this tag');\n        return map;\n    },\n    createNode: (schema, obj, ctx) => YAMLMap.YAMLMap.from(schema, obj, ctx)\n};\n\nexports.map = map;\n", "'use strict';\n\nvar createNode = require('../doc/createNode.js');\nvar stringifyCollection = require('../stringify/stringifyCollection.js');\nvar Collection = require('./Collection.js');\nvar identity = require('./identity.js');\nvar Scalar = require('./Scalar.js');\nvar toJS = require('./toJS.js');\n\nclass YAMLSeq extends Collection.Collection {\n    static get tagName() {\n        return 'tag:yaml.org,2002:seq';\n    }\n    constructor(schema) {\n        super(identity.SEQ, schema);\n        this.items = [];\n    }\n    add(value) {\n        this.items.push(value);\n    }\n    /**\n     * Removes a value from the collection.\n     *\n     * `key` must contain a representation of an integer for this to succeed.\n     * It may be wrapped in a `Scalar`.\n     *\n     * @returns `true` if the item was found and removed.\n     */\n    delete(key) {\n        const idx = asItemIndex(key);\n        if (typeof idx !== 'number')\n            return false;\n        const del = this.items.splice(idx, 1);\n        return del.length > 0;\n    }\n    get(key, keepScalar) {\n        const idx = asItemIndex(key);\n        if (typeof idx !== 'number')\n            return undefined;\n        const it = this.items[idx];\n        return !keepScalar && identity.isScalar(it) ? it.value : it;\n    }\n    /**\n     * Checks if the collection includes a value with the key `key`.\n     *\n     * `key` must contain a representation of an integer for this to succeed.\n     * It may be wrapped in a `Scalar`.\n     */\n    has(key) {\n        const idx = asItemIndex(key);\n        return typeof idx === 'number' && idx < this.items.length;\n    }\n    /**\n     * Sets a value in this collection. For `!!set`, `value` needs to be a\n     * boolean to add/remove the item from the set.\n     *\n     * If `key` does not contain a representation of an integer, this will throw.\n     * It may be wrapped in a `Scalar`.\n     */\n    set(key, value) {\n        const idx = asItemIndex(key);\n        if (typeof idx !== 'number')\n            throw new Error(`Expected a valid index, not ${key}.`);\n        const prev = this.items[idx];\n        if (identity.isScalar(prev) && Scalar.isScalarValue(value))\n            prev.value = value;\n        else\n            this.items[idx] = value;\n    }\n    toJSON(_, ctx) {\n        const seq = [];\n        if (ctx?.onCreate)\n            ctx.onCreate(seq);\n        let i = 0;\n        for (const item of this.items)\n            seq.push(toJS.toJS(item, String(i++), ctx));\n        return seq;\n    }\n    toString(ctx, onComment, onChompKeep) {\n        if (!ctx)\n            return JSON.stringify(this);\n        return stringifyCollection.stringifyCollection(this, ctx, {\n            blockItemPrefix: '- ',\n            flowChars: { start: '[', end: ']' },\n            itemIndent: (ctx.indent || '') + '  ',\n            onChompKeep,\n            onComment\n        });\n    }\n    static from(schema, obj, ctx) {\n        const { replacer } = ctx;\n        const seq = new this(schema);\n        if (obj && Symbol.iterator in Object(obj)) {\n            let i = 0;\n            for (let it of obj) {\n                if (typeof replacer === 'function') {\n                    const key = obj instanceof Set ? it : String(i++);\n                    it = replacer.call(obj, key, it);\n                }\n                seq.items.push(createNode.createNode(it, undefined, ctx));\n            }\n        }\n        return seq;\n    }\n}\nfunction asItemIndex(key) {\n    let idx = identity.isScalar(key) ? key.value : key;\n    if (idx && typeof idx === 'string')\n        idx = Number(idx);\n    return typeof idx === 'number' && Number.isInteger(idx) && idx >= 0\n        ? idx\n        : null;\n}\n\nexports.YAMLSeq = YAMLSeq;\n", "'use strict';\n\nvar identity = require('../../nodes/identity.js');\nvar YAMLSeq = require('../../nodes/YAMLSeq.js');\n\nconst seq = {\n    collection: 'seq',\n    default: true,\n    nodeClass: YAMLSeq.YAMLSeq,\n    tag: 'tag:yaml.org,2002:seq',\n    resolve(seq, onError) {\n        if (!identity.isSeq(seq))\n            onError('Expected a sequence for this tag');\n        return seq;\n    },\n    createNode: (schema, obj, ctx) => YAMLSeq.YAMLSeq.from(schema, obj, ctx)\n};\n\nexports.seq = seq;\n", "'use strict';\n\nvar stringifyString = require('../../stringify/stringifyString.js');\n\nconst string = {\n    identify: value => typeof value === 'string',\n    default: true,\n    tag: 'tag:yaml.org,2002:str',\n    resolve: str => str,\n    stringify(item, ctx, onComment, onChompKeep) {\n        ctx = Object.assign({ actualString: true }, ctx);\n        return stringifyString.stringifyString(item, ctx, onComment, onChompKeep);\n    }\n};\n\nexports.string = string;\n", "'use strict';\n\nvar Scalar = require('../../nodes/Scalar.js');\n\nconst nullTag = {\n    identify: value => value == null,\n    createNode: () => new Scalar.Scalar(null),\n    default: true,\n    tag: 'tag:yaml.org,2002:null',\n    test: /^(?:~|[Nn]ull|NULL)?$/,\n    resolve: () => new Scalar.Scalar(null),\n    stringify: ({ source }, ctx) => typeof source === 'string' && nullTag.test.test(source)\n        ? source\n        : ctx.options.nullStr\n};\n\nexports.nullTag = nullTag;\n", "'use strict';\n\nvar Scalar = require('../../nodes/Scalar.js');\n\nconst boolTag = {\n    identify: value => typeof value === 'boolean',\n    default: true,\n    tag: 'tag:yaml.org,2002:bool',\n    test: /^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,\n    resolve: str => new Scalar.Scalar(str[0] === 't' || str[0] === 'T'),\n    stringify({ source, value }, ctx) {\n        if (source && boolTag.test.test(source)) {\n            const sv = source[0] === 't' || source[0] === 'T';\n            if (value === sv)\n                return source;\n        }\n        return value ? ctx.options.trueStr : ctx.options.falseStr;\n    }\n};\n\nexports.boolTag = boolTag;\n", "'use strict';\n\nfunction stringifyNumber({ format, minFractionDigits, tag, value }) {\n    if (typeof value === 'bigint')\n        return String(value);\n    const num = typeof value === 'number' ? value : Number(value);\n    if (!isFinite(num))\n        return isNaN(num) ? '.nan' : num < 0 ? '-.inf' : '.inf';\n    let n = JSON.stringify(value);\n    if (!format &&\n        minFractionDigits &&\n        (!tag || tag === 'tag:yaml.org,2002:float') &&\n        /^\\d/.test(n)) {\n        let i = n.indexOf('.');\n        if (i < 0) {\n            i = n.length;\n            n += '.';\n        }\n        let d = minFractionDigits - (n.length - i - 1);\n        while (d-- > 0)\n            n += '0';\n    }\n    return n;\n}\n\nexports.stringifyNumber = stringifyNumber;\n", "'use strict';\n\nvar Scalar = require('../../nodes/Scalar.js');\nvar stringifyNumber = require('../../stringify/stringifyNumber.js');\n\nconst floatNaN = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    test: /^(?:[-+]?\\.(?:inf|Inf|INF)|\\.nan|\\.NaN|\\.NAN)$/,\n    resolve: str => str.slice(-3).toLowerCase() === 'nan'\n        ? NaN\n        : str[0] === '-'\n            ? Number.NEGATIVE_INFINITY\n            : Number.POSITIVE_INFINITY,\n    stringify: stringifyNumber.stringifyNumber\n};\nconst floatExp = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    format: 'EXP',\n    test: /^[-+]?(?:\\.[0-9]+|[0-9]+(?:\\.[0-9]*)?)[eE][-+]?[0-9]+$/,\n    resolve: str => parseFloat(str),\n    stringify(node) {\n        const num = Number(node.value);\n        return isFinite(num) ? num.toExponential() : stringifyNumber.stringifyNumber(node);\n    }\n};\nconst float = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    test: /^[-+]?(?:\\.[0-9]+|[0-9]+\\.[0-9]*)$/,\n    resolve(str) {\n        const node = new Scalar.Scalar(parseFloat(str));\n        const dot = str.indexOf('.');\n        if (dot !== -1 && str[str.length - 1] === '0')\n            node.minFractionDigits = str.length - dot - 1;\n        return node;\n    },\n    stringify: stringifyNumber.stringifyNumber\n};\n\nexports.float = float;\nexports.floatExp = floatExp;\nexports.floatNaN = floatNaN;\n", "'use strict';\n\nvar stringifyNumber = require('../../stringify/stringifyNumber.js');\n\nconst intIdentify = (value) => typeof value === 'bigint' || Number.isInteger(value);\nconst intResolve = (str, offset, radix, { intAsBigInt }) => (intAsBigInt ? BigInt(str) : parseInt(str.substring(offset), radix));\nfunction intStringify(node, radix, prefix) {\n    const { value } = node;\n    if (intIdentify(value) && value >= 0)\n        return prefix + value.toString(radix);\n    return stringifyNumber.stringifyNumber(node);\n}\nconst intOct = {\n    identify: value => intIdentify(value) && value >= 0,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'OCT',\n    test: /^0o[0-7]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 2, 8, opt),\n    stringify: node => intStringify(node, 8, '0o')\n};\nconst int = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    test: /^[-+]?[0-9]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 0, 10, opt),\n    stringify: stringifyNumber.stringifyNumber\n};\nconst intHex = {\n    identify: value => intIdentify(value) && value >= 0,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'HEX',\n    test: /^0x[0-9a-fA-F]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 2, 16, opt),\n    stringify: node => intStringify(node, 16, '0x')\n};\n\nexports.int = int;\nexports.intHex = intHex;\nexports.intOct = intOct;\n", "'use strict';\n\nvar map = require('../common/map.js');\nvar _null = require('../common/null.js');\nvar seq = require('../common/seq.js');\nvar string = require('../common/string.js');\nvar bool = require('./bool.js');\nvar float = require('./float.js');\nvar int = require('./int.js');\n\nconst schema = [\n    map.map,\n    seq.seq,\n    string.string,\n    _null.nullTag,\n    bool.boolTag,\n    int.intOct,\n    int.int,\n    int.intHex,\n    float.floatNaN,\n    float.floatExp,\n    float.float\n];\n\nexports.schema = schema;\n", "'use strict';\n\nvar Scalar = require('../../nodes/Scalar.js');\nvar map = require('../common/map.js');\nvar seq = require('../common/seq.js');\n\nfunction intIdentify(value) {\n    return typeof value === 'bigint' || Number.isInteger(value);\n}\nconst stringifyJSON = ({ value }) => JSON.stringify(value);\nconst jsonScalars = [\n    {\n        identify: value => typeof value === 'string',\n        default: true,\n        tag: 'tag:yaml.org,2002:str',\n        resolve: str => str,\n        stringify: stringifyJSON\n    },\n    {\n        identify: value => value == null,\n        createNode: () => new Scalar.Scalar(null),\n        default: true,\n        tag: 'tag:yaml.org,2002:null',\n        test: /^null$/,\n        resolve: () => null,\n        stringify: stringifyJSON\n    },\n    {\n        identify: value => typeof value === 'boolean',\n        default: true,\n        tag: 'tag:yaml.org,2002:bool',\n        test: /^true$|^false$/,\n        resolve: str => str === 'true',\n        stringify: stringifyJSON\n    },\n    {\n        identify: intIdentify,\n        default: true,\n        tag: 'tag:yaml.org,2002:int',\n        test: /^-?(?:0|[1-9][0-9]*)$/,\n        resolve: (str, _onError, { intAsBigInt }) => intAsBigInt ? BigInt(str) : parseInt(str, 10),\n        stringify: ({ value }) => intIdentify(value) ? value.toString() : JSON.stringify(value)\n    },\n    {\n        identify: value => typeof value === 'number',\n        default: true,\n        tag: 'tag:yaml.org,2002:float',\n        test: /^-?(?:0|[1-9][0-9]*)(?:\\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,\n        resolve: str => parseFloat(str),\n        stringify: stringifyJSON\n    }\n];\nconst jsonError = {\n    default: true,\n    tag: '',\n    test: /^/,\n    resolve(str, onError) {\n        onError(`Unresolved plain scalar ${JSON.stringify(str)}`);\n        return str;\n    }\n};\nconst schema = [map.map, seq.seq].concat(jsonScalars, jsonError);\n\nexports.schema = schema;\n", "'use strict';\n\nvar node_buffer = require('node:buffer');\nvar Scalar = require('../../nodes/Scalar.js');\nvar stringifyString = require('../../stringify/stringifyString.js');\n\nconst binary = {\n    identify: value => value instanceof Uint8Array, // <PERSON><PERSON><PERSON> inherits from Uint8Array\n    default: false,\n    tag: 'tag:yaml.org,2002:binary',\n    /**\n     * Returns a Buffer in node and an Uint8Array in browsers\n     *\n     * To use the resulting buffer as an image, you'll want to do something like:\n     *\n     *   const blob = new Blob([buffer], { type: 'image/jpeg' })\n     *   document.querySelector('#photo').src = URL.createObjectURL(blob)\n     */\n    resolve(src, onError) {\n        if (typeof node_buffer.Buffer === 'function') {\n            return node_buffer.Buffer.from(src, 'base64');\n        }\n        else if (typeof atob === 'function') {\n            // On IE 11, atob() can't handle newlines\n            const str = atob(src.replace(/[\\n\\r]/g, ''));\n            const buffer = new Uint8Array(str.length);\n            for (let i = 0; i < str.length; ++i)\n                buffer[i] = str.charCodeAt(i);\n            return buffer;\n        }\n        else {\n            onError('This environment does not support reading binary tags; either Buffer or atob is required');\n            return src;\n        }\n    },\n    stringify({ comment, type, value }, ctx, onComment, onChompKeep) {\n        const buf = value; // checked earlier by binary.identify()\n        let str;\n        if (typeof node_buffer.Buffer === 'function') {\n            str =\n                buf instanceof node_buffer.Buffer\n                    ? buf.toString('base64')\n                    : node_buffer.Buffer.from(buf.buffer).toString('base64');\n        }\n        else if (typeof btoa === 'function') {\n            let s = '';\n            for (let i = 0; i < buf.length; ++i)\n                s += String.fromCharCode(buf[i]);\n            str = btoa(s);\n        }\n        else {\n            throw new Error('This environment does not support writing binary tags; either Buffer or btoa is required');\n        }\n        if (!type)\n            type = Scalar.Scalar.BLOCK_LITERAL;\n        if (type !== Scalar.Scalar.QUOTE_DOUBLE) {\n            const lineWidth = Math.max(ctx.options.lineWidth - ctx.indent.length, ctx.options.minContentWidth);\n            const n = Math.ceil(str.length / lineWidth);\n            const lines = new Array(n);\n            for (let i = 0, o = 0; i < n; ++i, o += lineWidth) {\n                lines[i] = str.substr(o, lineWidth);\n            }\n            str = lines.join(type === Scalar.Scalar.BLOCK_LITERAL ? '\\n' : ' ');\n        }\n        return stringifyString.stringifyString({ comment, type, value: str }, ctx, onComment, onChompKeep);\n    }\n};\n\nexports.binary = binary;\n", "'use strict';\n\nvar identity = require('../../nodes/identity.js');\nvar Pair = require('../../nodes/Pair.js');\nvar Scalar = require('../../nodes/Scalar.js');\nvar YAMLSeq = require('../../nodes/YAMLSeq.js');\n\nfunction resolvePairs(seq, onError) {\n    if (identity.isSeq(seq)) {\n        for (let i = 0; i < seq.items.length; ++i) {\n            let item = seq.items[i];\n            if (identity.isPair(item))\n                continue;\n            else if (identity.isMap(item)) {\n                if (item.items.length > 1)\n                    onError('Each pair must have its own sequence indicator');\n                const pair = item.items[0] || new Pair.Pair(new Scalar.Scalar(null));\n                if (item.commentBefore)\n                    pair.key.commentBefore = pair.key.commentBefore\n                        ? `${item.commentBefore}\\n${pair.key.commentBefore}`\n                        : item.commentBefore;\n                if (item.comment) {\n                    const cn = pair.value ?? pair.key;\n                    cn.comment = cn.comment\n                        ? `${item.comment}\\n${cn.comment}`\n                        : item.comment;\n                }\n                item = pair;\n            }\n            seq.items[i] = identity.isPair(item) ? item : new Pair.Pair(item);\n        }\n    }\n    else\n        onError('Expected a sequence for this tag');\n    return seq;\n}\nfunction createPairs(schema, iterable, ctx) {\n    const { replacer } = ctx;\n    const pairs = new YAMLSeq.YAMLSeq(schema);\n    pairs.tag = 'tag:yaml.org,2002:pairs';\n    let i = 0;\n    if (iterable && Symbol.iterator in Object(iterable))\n        for (let it of iterable) {\n            if (typeof replacer === 'function')\n                it = replacer.call(iterable, String(i++), it);\n            let key, value;\n            if (Array.isArray(it)) {\n                if (it.length === 2) {\n                    key = it[0];\n                    value = it[1];\n                }\n                else\n                    throw new TypeError(`Expected [key, value] tuple: ${it}`);\n            }\n            else if (it && it instanceof Object) {\n                const keys = Object.keys(it);\n                if (keys.length === 1) {\n                    key = keys[0];\n                    value = it[key];\n                }\n                else {\n                    throw new TypeError(`Expected tuple with one key, not ${keys.length} keys`);\n                }\n            }\n            else {\n                key = it;\n            }\n            pairs.items.push(Pair.createPair(key, value, ctx));\n        }\n    return pairs;\n}\nconst pairs = {\n    collection: 'seq',\n    default: false,\n    tag: 'tag:yaml.org,2002:pairs',\n    resolve: resolvePairs,\n    createNode: createPairs\n};\n\nexports.createPairs = createPairs;\nexports.pairs = pairs;\nexports.resolvePairs = resolvePairs;\n", "'use strict';\n\nvar identity = require('../../nodes/identity.js');\nvar toJS = require('../../nodes/toJS.js');\nvar YAMLMap = require('../../nodes/YAMLMap.js');\nvar YAMLSeq = require('../../nodes/YAMLSeq.js');\nvar pairs = require('./pairs.js');\n\nclass YAMLOMap extends YAMLSeq.YAMLSeq {\n    constructor() {\n        super();\n        this.add = YAMLMap.YAMLMap.prototype.add.bind(this);\n        this.delete = YAMLMap.YAMLMap.prototype.delete.bind(this);\n        this.get = YAMLMap.YAMLMap.prototype.get.bind(this);\n        this.has = YAMLMap.YAMLMap.prototype.has.bind(this);\n        this.set = YAMLMap.YAMLMap.prototype.set.bind(this);\n        this.tag = YAMLOMap.tag;\n    }\n    /**\n     * If `ctx` is given, the return type is actually `Map<unknown, unknown>`,\n     * but TypeScript won't allow widening the signature of a child method.\n     */\n    toJSON(_, ctx) {\n        if (!ctx)\n            return super.toJSON(_);\n        const map = new Map();\n        if (ctx?.onCreate)\n            ctx.onCreate(map);\n        for (const pair of this.items) {\n            let key, value;\n            if (identity.isPair(pair)) {\n                key = toJS.toJS(pair.key, '', ctx);\n                value = toJS.toJS(pair.value, key, ctx);\n            }\n            else {\n                key = toJS.toJS(pair, '', ctx);\n            }\n            if (map.has(key))\n                throw new Error('Ordered maps must not include duplicate keys');\n            map.set(key, value);\n        }\n        return map;\n    }\n    static from(schema, iterable, ctx) {\n        const pairs$1 = pairs.createPairs(schema, iterable, ctx);\n        const omap = new this();\n        omap.items = pairs$1.items;\n        return omap;\n    }\n}\nYAMLOMap.tag = 'tag:yaml.org,2002:omap';\nconst omap = {\n    collection: 'seq',\n    identify: value => value instanceof Map,\n    nodeClass: YAMLOMap,\n    default: false,\n    tag: 'tag:yaml.org,2002:omap',\n    resolve(seq, onError) {\n        const pairs$1 = pairs.resolvePairs(seq, onError);\n        const seenKeys = [];\n        for (const { key } of pairs$1.items) {\n            if (identity.isScalar(key)) {\n                if (seenKeys.includes(key.value)) {\n                    onError(`Ordered maps must not include duplicate keys: ${key.value}`);\n                }\n                else {\n                    seenKeys.push(key.value);\n                }\n            }\n        }\n        return Object.assign(new YAMLOMap(), pairs$1);\n    },\n    createNode: (schema, iterable, ctx) => YAMLOMap.from(schema, iterable, ctx)\n};\n\nexports.YAMLOMap = YAMLOMap;\nexports.omap = omap;\n", "'use strict';\n\nvar Scalar = require('../../nodes/Scalar.js');\n\nfunction boolStringify({ value, source }, ctx) {\n    const boolObj = value ? trueTag : falseTag;\n    if (source && boolObj.test.test(source))\n        return source;\n    return value ? ctx.options.trueStr : ctx.options.falseStr;\n}\nconst trueTag = {\n    identify: value => value === true,\n    default: true,\n    tag: 'tag:yaml.org,2002:bool',\n    test: /^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,\n    resolve: () => new Scalar.Scalar(true),\n    stringify: boolStringify\n};\nconst falseTag = {\n    identify: value => value === false,\n    default: true,\n    tag: 'tag:yaml.org,2002:bool',\n    test: /^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,\n    resolve: () => new Scalar.Scalar(false),\n    stringify: boolStringify\n};\n\nexports.falseTag = falseTag;\nexports.trueTag = trueTag;\n", "'use strict';\n\nvar Scalar = require('../../nodes/Scalar.js');\nvar stringifyNumber = require('../../stringify/stringifyNumber.js');\n\nconst floatNaN = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    test: /^(?:[-+]?\\.(?:inf|Inf|INF)|\\.nan|\\.NaN|\\.NAN)$/,\n    resolve: (str) => str.slice(-3).toLowerCase() === 'nan'\n        ? NaN\n        : str[0] === '-'\n            ? Number.NEGATIVE_INFINITY\n            : Number.POSITIVE_INFINITY,\n    stringify: stringifyNumber.stringifyNumber\n};\nconst floatExp = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    format: 'EXP',\n    test: /^[-+]?(?:[0-9][0-9_]*)?(?:\\.[0-9_]*)?[eE][-+]?[0-9]+$/,\n    resolve: (str) => parseFloat(str.replace(/_/g, '')),\n    stringify(node) {\n        const num = Number(node.value);\n        return isFinite(num) ? num.toExponential() : stringifyNumber.stringifyNumber(node);\n    }\n};\nconst float = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    test: /^[-+]?(?:[0-9][0-9_]*)?\\.[0-9_]*$/,\n    resolve(str) {\n        const node = new Scalar.Scalar(parseFloat(str.replace(/_/g, '')));\n        const dot = str.indexOf('.');\n        if (dot !== -1) {\n            const f = str.substring(dot + 1).replace(/_/g, '');\n            if (f[f.length - 1] === '0')\n                node.minFractionDigits = f.length;\n        }\n        return node;\n    },\n    stringify: stringifyNumber.stringifyNumber\n};\n\nexports.float = float;\nexports.floatExp = floatExp;\nexports.floatNaN = floatNaN;\n", "'use strict';\n\nvar stringifyNumber = require('../../stringify/stringifyNumber.js');\n\nconst intIdentify = (value) => typeof value === 'bigint' || Number.isInteger(value);\nfunction intResolve(str, offset, radix, { intAsBigInt }) {\n    const sign = str[0];\n    if (sign === '-' || sign === '+')\n        offset += 1;\n    str = str.substring(offset).replace(/_/g, '');\n    if (intAsBigInt) {\n        switch (radix) {\n            case 2:\n                str = `0b${str}`;\n                break;\n            case 8:\n                str = `0o${str}`;\n                break;\n            case 16:\n                str = `0x${str}`;\n                break;\n        }\n        const n = BigInt(str);\n        return sign === '-' ? BigInt(-1) * n : n;\n    }\n    const n = parseInt(str, radix);\n    return sign === '-' ? -1 * n : n;\n}\nfunction intStringify(node, radix, prefix) {\n    const { value } = node;\n    if (intIdentify(value)) {\n        const str = value.toString(radix);\n        return value < 0 ? '-' + prefix + str.substr(1) : prefix + str;\n    }\n    return stringifyNumber.stringifyNumber(node);\n}\nconst intBin = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'BIN',\n    test: /^[-+]?0b[0-1_]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 2, 2, opt),\n    stringify: node => intStringify(node, 2, '0b')\n};\nconst intOct = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'OCT',\n    test: /^[-+]?0[0-7_]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 1, 8, opt),\n    stringify: node => intStringify(node, 8, '0')\n};\nconst int = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    test: /^[-+]?[0-9][0-9_]*$/,\n    resolve: (str, _onError, opt) => intResolve(str, 0, 10, opt),\n    stringify: stringifyNumber.stringifyNumber\n};\nconst intHex = {\n    identify: intIdentify,\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'HEX',\n    test: /^[-+]?0x[0-9a-fA-F_]+$/,\n    resolve: (str, _onError, opt) => intResolve(str, 2, 16, opt),\n    stringify: node => intStringify(node, 16, '0x')\n};\n\nexports.int = int;\nexports.intBin = intBin;\nexports.intHex = intHex;\nexports.intOct = intOct;\n", "'use strict';\n\nvar identity = require('../../nodes/identity.js');\nvar Pair = require('../../nodes/Pair.js');\nvar YAMLMap = require('../../nodes/YAMLMap.js');\n\nclass YAMLSet extends YAMLMap.YAMLMap {\n    constructor(schema) {\n        super(schema);\n        this.tag = YAMLSet.tag;\n    }\n    add(key) {\n        let pair;\n        if (identity.isPair(key))\n            pair = key;\n        else if (key &&\n            typeof key === 'object' &&\n            'key' in key &&\n            'value' in key &&\n            key.value === null)\n            pair = new Pair.Pair(key.key, null);\n        else\n            pair = new Pair.Pair(key, null);\n        const prev = YAMLMap.findPair(this.items, pair.key);\n        if (!prev)\n            this.items.push(pair);\n    }\n    /**\n     * If `keepPair` is `true`, returns the Pair matching `key`.\n     * Otherwise, returns the value of that Pair's key.\n     */\n    get(key, keepPair) {\n        const pair = YAMLMap.findPair(this.items, key);\n        return !keepPair && identity.isPair(pair)\n            ? identity.isScalar(pair.key)\n                ? pair.key.value\n                : pair.key\n            : pair;\n    }\n    set(key, value) {\n        if (typeof value !== 'boolean')\n            throw new Error(`Expected boolean value for set(key, value) in a YAML set, not ${typeof value}`);\n        const prev = YAMLMap.findPair(this.items, key);\n        if (prev && !value) {\n            this.items.splice(this.items.indexOf(prev), 1);\n        }\n        else if (!prev && value) {\n            this.items.push(new Pair.Pair(key));\n        }\n    }\n    toJSON(_, ctx) {\n        return super.toJSON(_, ctx, Set);\n    }\n    toString(ctx, onComment, onChompKeep) {\n        if (!ctx)\n            return JSON.stringify(this);\n        if (this.hasAllNullValues(true))\n            return super.toString(Object.assign({}, ctx, { allNullValues: true }), onComment, onChompKeep);\n        else\n            throw new Error('Set items must all have null values');\n    }\n    static from(schema, iterable, ctx) {\n        const { replacer } = ctx;\n        const set = new this(schema);\n        if (iterable && Symbol.iterator in Object(iterable))\n            for (let value of iterable) {\n                if (typeof replacer === 'function')\n                    value = replacer.call(iterable, value, value);\n                set.items.push(Pair.createPair(value, null, ctx));\n            }\n        return set;\n    }\n}\nYAMLSet.tag = 'tag:yaml.org,2002:set';\nconst set = {\n    collection: 'map',\n    identify: value => value instanceof Set,\n    nodeClass: YAMLSet,\n    default: false,\n    tag: 'tag:yaml.org,2002:set',\n    createNode: (schema, iterable, ctx) => YAMLSet.from(schema, iterable, ctx),\n    resolve(map, onError) {\n        if (identity.isMap(map)) {\n            if (map.hasAllNullValues(true))\n                return Object.assign(new YAMLSet(), map);\n            else\n                onError('Set items must all have null values');\n        }\n        else\n            onError('Expected a mapping for this tag');\n        return map;\n    }\n};\n\nexports.YAMLSet = YAMLSet;\nexports.set = set;\n", "'use strict';\n\nvar stringifyNumber = require('../../stringify/stringifyNumber.js');\n\n/** Internal types handle bigint as number, because TS can't figure it out. */\nfunction parseSexagesimal(str, asBigInt) {\n    const sign = str[0];\n    const parts = sign === '-' || sign === '+' ? str.substring(1) : str;\n    const num = (n) => asBigInt ? BigInt(n) : Number(n);\n    const res = parts\n        .replace(/_/g, '')\n        .split(':')\n        .reduce((res, p) => res * num(60) + num(p), num(0));\n    return (sign === '-' ? num(-1) * res : res);\n}\n/**\n * hhhh:mm:ss.sss\n *\n * Internal types handle bigint as number, because TS can't figure it out.\n */\nfunction stringifySexagesimal(node) {\n    let { value } = node;\n    let num = (n) => n;\n    if (typeof value === 'bigint')\n        num = n => BigInt(n);\n    else if (isNaN(value) || !isFinite(value))\n        return stringifyNumber.stringifyNumber(node);\n    let sign = '';\n    if (value < 0) {\n        sign = '-';\n        value *= num(-1);\n    }\n    const _60 = num(60);\n    const parts = [value % _60]; // seconds, including ms\n    if (value < 60) {\n        parts.unshift(0); // at least one : is required\n    }\n    else {\n        value = (value - parts[0]) / _60;\n        parts.unshift(value % _60); // minutes\n        if (value >= 60) {\n            value = (value - parts[0]) / _60;\n            parts.unshift(value); // hours\n        }\n    }\n    return (sign +\n        parts\n            .map(n => String(n).padStart(2, '0'))\n            .join(':')\n            .replace(/000000\\d*$/, '') // % 60 may introduce error\n    );\n}\nconst intTime = {\n    identify: value => typeof value === 'bigint' || Number.isInteger(value),\n    default: true,\n    tag: 'tag:yaml.org,2002:int',\n    format: 'TIME',\n    test: /^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,\n    resolve: (str, _onError, { intAsBigInt }) => parseSexagesimal(str, intAsBigInt),\n    stringify: stringifySexagesimal\n};\nconst floatTime = {\n    identify: value => typeof value === 'number',\n    default: true,\n    tag: 'tag:yaml.org,2002:float',\n    format: 'TIME',\n    test: /^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\.[0-9_]*$/,\n    resolve: str => parseSexagesimal(str, false),\n    stringify: stringifySexagesimal\n};\nconst timestamp = {\n    identify: value => value instanceof Date,\n    default: true,\n    tag: 'tag:yaml.org,2002:timestamp',\n    // If the time zone is omitted, the timestamp is assumed to be specified in UTC. The time part\n    // may be omitted altogether, resulting in a date format. In such a case, the time part is\n    // assumed to be 00:00:00Z (start of day, UTC).\n    test: RegExp('^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})' + // YYYY-Mm-Dd\n        '(?:' + // time is optional\n        '(?:t|T|[ \\\\t]+)' + // t | T | whitespace\n        '([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\\\.[0-9]+)?)' + // Hh:Mm:Ss(.ss)?\n        '(?:[ \\\\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?' + // Z | +5 | -03:30\n        ')?$'),\n    resolve(str) {\n        const match = str.match(timestamp.test);\n        if (!match)\n            throw new Error('!!timestamp expects a date, starting with yyyy-mm-dd');\n        const [, year, month, day, hour, minute, second] = match.map(Number);\n        const millisec = match[7] ? Number((match[7] + '00').substr(1, 3)) : 0;\n        let date = Date.UTC(year, month - 1, day, hour || 0, minute || 0, second || 0, millisec);\n        const tz = match[8];\n        if (tz && tz !== 'Z') {\n            let d = parseSexagesimal(tz, false);\n            if (Math.abs(d) < 30)\n                d *= 60;\n            date -= 60000 * d;\n        }\n        return new Date(date);\n    },\n    stringify: ({ value }) => value.toISOString().replace(/(T00:00:00)?\\.000Z$/, '')\n};\n\nexports.floatTime = floatTime;\nexports.intTime = intTime;\nexports.timestamp = timestamp;\n", "'use strict';\n\nvar map = require('../common/map.js');\nvar _null = require('../common/null.js');\nvar seq = require('../common/seq.js');\nvar string = require('../common/string.js');\nvar binary = require('./binary.js');\nvar bool = require('./bool.js');\nvar float = require('./float.js');\nvar int = require('./int.js');\nvar merge = require('./merge.js');\nvar omap = require('./omap.js');\nvar pairs = require('./pairs.js');\nvar set = require('./set.js');\nvar timestamp = require('./timestamp.js');\n\nconst schema = [\n    map.map,\n    seq.seq,\n    string.string,\n    _null.nullTag,\n    bool.trueTag,\n    bool.falseTag,\n    int.intBin,\n    int.intOct,\n    int.int,\n    int.intHex,\n    float.floatNaN,\n    float.floatExp,\n    float.float,\n    binary.binary,\n    merge.merge,\n    omap.omap,\n    pairs.pairs,\n    set.set,\n    timestamp.intTime,\n    timestamp.floatTime,\n    timestamp.timestamp\n];\n\nexports.schema = schema;\n", "'use strict';\n\nvar map = require('./common/map.js');\nvar _null = require('./common/null.js');\nvar seq = require('./common/seq.js');\nvar string = require('./common/string.js');\nvar bool = require('./core/bool.js');\nvar float = require('./core/float.js');\nvar int = require('./core/int.js');\nvar schema = require('./core/schema.js');\nvar schema$1 = require('./json/schema.js');\nvar binary = require('./yaml-1.1/binary.js');\nvar merge = require('./yaml-1.1/merge.js');\nvar omap = require('./yaml-1.1/omap.js');\nvar pairs = require('./yaml-1.1/pairs.js');\nvar schema$2 = require('./yaml-1.1/schema.js');\nvar set = require('./yaml-1.1/set.js');\nvar timestamp = require('./yaml-1.1/timestamp.js');\n\nconst schemas = new Map([\n    ['core', schema.schema],\n    ['failsafe', [map.map, seq.seq, string.string]],\n    ['json', schema$1.schema],\n    ['yaml11', schema$2.schema],\n    ['yaml-1.1', schema$2.schema]\n]);\nconst tagsByName = {\n    binary: binary.binary,\n    bool: bool.boolTag,\n    float: float.float,\n    floatExp: float.floatExp,\n    floatNaN: float.floatNaN,\n    floatTime: timestamp.floatTime,\n    int: int.int,\n    intHex: int.intHex,\n    intOct: int.intOct,\n    intTime: timestamp.intTime,\n    map: map.map,\n    merge: merge.merge,\n    null: _null.nullTag,\n    omap: omap.omap,\n    pairs: pairs.pairs,\n    seq: seq.seq,\n    set: set.set,\n    timestamp: timestamp.timestamp\n};\nconst coreKnownTags = {\n    'tag:yaml.org,2002:binary': binary.binary,\n    'tag:yaml.org,2002:merge': merge.merge,\n    'tag:yaml.org,2002:omap': omap.omap,\n    'tag:yaml.org,2002:pairs': pairs.pairs,\n    'tag:yaml.org,2002:set': set.set,\n    'tag:yaml.org,2002:timestamp': timestamp.timestamp\n};\nfunction getTags(customTags, schemaName, addMergeTag) {\n    const schemaTags = schemas.get(schemaName);\n    if (schemaTags && !customTags) {\n        return addMergeTag && !schemaTags.includes(merge.merge)\n            ? schemaTags.concat(merge.merge)\n            : schemaTags.slice();\n    }\n    let tags = schemaTags;\n    if (!tags) {\n        if (Array.isArray(customTags))\n            tags = [];\n        else {\n            const keys = Array.from(schemas.keys())\n                .filter(key => key !== 'yaml11')\n                .map(key => JSON.stringify(key))\n                .join(', ');\n            throw new Error(`Unknown schema \"${schemaName}\"; use one of ${keys} or define customTags array`);\n        }\n    }\n    if (Array.isArray(customTags)) {\n        for (const tag of customTags)\n            tags = tags.concat(tag);\n    }\n    else if (typeof customTags === 'function') {\n        tags = customTags(tags.slice());\n    }\n    if (addMergeTag)\n        tags = tags.concat(merge.merge);\n    return tags.reduce((tags, tag) => {\n        const tagObj = typeof tag === 'string' ? tagsByName[tag] : tag;\n        if (!tagObj) {\n            const tagName = JSON.stringify(tag);\n            const keys = Object.keys(tagsByName)\n                .map(key => JSON.stringify(key))\n                .join(', ');\n            throw new Error(`Unknown custom tag ${tagName}; use one of ${keys}`);\n        }\n        if (!tags.includes(tagObj))\n            tags.push(tagObj);\n        return tags;\n    }, []);\n}\n\nexports.coreKnownTags = coreKnownTags;\nexports.getTags = getTags;\n", "'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar map = require('./common/map.js');\nvar seq = require('./common/seq.js');\nvar string = require('./common/string.js');\nvar tags = require('./tags.js');\n\nconst sortMapEntriesByKey = (a, b) => a.key < b.key ? -1 : a.key > b.key ? 1 : 0;\nclass Schema {\n    constructor({ compat, customTags, merge, resolveKnownTags, schema, sortMapEntries, toStringDefaults }) {\n        this.compat = Array.isArray(compat)\n            ? tags.getTags(compat, 'compat')\n            : compat\n                ? tags.getTags(null, compat)\n                : null;\n        this.name = (typeof schema === 'string' && schema) || 'core';\n        this.knownTags = resolveKnownTags ? tags.coreKnownTags : {};\n        this.tags = tags.getTags(customTags, this.name, merge);\n        this.toStringOptions = toStringDefaults ?? null;\n        Object.defineProperty(this, identity.MAP, { value: map.map });\n        Object.defineProperty(this, identity.SCALAR, { value: string.string });\n        Object.defineProperty(this, identity.SEQ, { value: seq.seq });\n        // Used by createMap()\n        this.sortMapEntries =\n            typeof sortMapEntries === 'function'\n                ? sortMapEntries\n                : sortMapEntries === true\n                    ? sortMapEntriesByKey\n                    : null;\n    }\n    clone() {\n        const copy = Object.create(Schema.prototype, Object.getOwnPropertyDescriptors(this));\n        copy.tags = this.tags.slice();\n        return copy;\n    }\n}\n\nexports.Schema = Schema;\n", "'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar stringify = require('./stringify.js');\nvar stringifyComment = require('./stringifyComment.js');\n\nfunction stringifyDocument(doc, options) {\n    const lines = [];\n    let hasDirectives = options.directives === true;\n    if (options.directives !== false && doc.directives) {\n        const dir = doc.directives.toString(doc);\n        if (dir) {\n            lines.push(dir);\n            hasDirectives = true;\n        }\n        else if (doc.directives.docStart)\n            hasDirectives = true;\n    }\n    if (hasDirectives)\n        lines.push('---');\n    const ctx = stringify.createStringifyContext(doc, options);\n    const { commentString } = ctx.options;\n    if (doc.commentBefore) {\n        if (lines.length !== 1)\n            lines.unshift('');\n        const cs = commentString(doc.commentBefore);\n        lines.unshift(stringifyComment.indentComment(cs, ''));\n    }\n    let chompKeep = false;\n    let contentComment = null;\n    if (doc.contents) {\n        if (identity.isNode(doc.contents)) {\n            if (doc.contents.spaceBefore && hasDirectives)\n                lines.push('');\n            if (doc.contents.commentBefore) {\n                const cs = commentString(doc.contents.commentBefore);\n                lines.push(stringifyComment.indentComment(cs, ''));\n            }\n            // top-level block scalars need to be indented if followed by a comment\n            ctx.forceBlockIndent = !!doc.comment;\n            contentComment = doc.contents.comment;\n        }\n        const onChompKeep = contentComment ? undefined : () => (chompKeep = true);\n        let body = stringify.stringify(doc.contents, ctx, () => (contentComment = null), onChompKeep);\n        if (contentComment)\n            body += stringifyComment.lineComment(body, '', commentString(contentComment));\n        if ((body[0] === '|' || body[0] === '>') &&\n            lines[lines.length - 1] === '---') {\n            // Top-level block scalars with a preceding doc marker ought to use the\n            // same line for their header.\n            lines[lines.length - 1] = `--- ${body}`;\n        }\n        else\n            lines.push(body);\n    }\n    else {\n        lines.push(stringify.stringify(doc.contents, ctx));\n    }\n    if (doc.directives?.docEnd) {\n        if (doc.comment) {\n            const cs = commentString(doc.comment);\n            if (cs.includes('\\n')) {\n                lines.push('...');\n                lines.push(stringifyComment.indentComment(cs, ''));\n            }\n            else {\n                lines.push(`... ${cs}`);\n            }\n        }\n        else {\n            lines.push('...');\n        }\n    }\n    else {\n        let dc = doc.comment;\n        if (dc && chompKeep)\n            dc = dc.replace(/^\\n+/, '');\n        if (dc) {\n            if ((!chompKeep || contentComment) && lines[lines.length - 1] !== '')\n                lines.push('');\n            lines.push(stringifyComment.indentComment(commentString(dc), ''));\n        }\n    }\n    return lines.join('\\n') + '\\n';\n}\n\nexports.stringifyDocument = stringifyDocument;\n", "'use strict';\n\nvar Alias = require('../nodes/Alias.js');\nvar Collection = require('../nodes/Collection.js');\nvar identity = require('../nodes/identity.js');\nvar Pair = require('../nodes/Pair.js');\nvar toJS = require('../nodes/toJS.js');\nvar Schema = require('../schema/Schema.js');\nvar stringifyDocument = require('../stringify/stringifyDocument.js');\nvar anchors = require('./anchors.js');\nvar applyReviver = require('./applyReviver.js');\nvar createNode = require('./createNode.js');\nvar directives = require('./directives.js');\n\nclass Document {\n    constructor(value, replacer, options) {\n        /** A comment before this Document */\n        this.commentBefore = null;\n        /** A comment immediately after this Document */\n        this.comment = null;\n        /** Errors encountered during parsing. */\n        this.errors = [];\n        /** Warnings encountered during parsing. */\n        this.warnings = [];\n        Object.defineProperty(this, identity.NODE_TYPE, { value: identity.DOC });\n        let _replacer = null;\n        if (typeof replacer === 'function' || Array.isArray(replacer)) {\n            _replacer = replacer;\n        }\n        else if (options === undefined && replacer) {\n            options = replacer;\n            replacer = undefined;\n        }\n        const opt = Object.assign({\n            intAsBigInt: false,\n            keepSourceTokens: false,\n            logLevel: 'warn',\n            prettyErrors: true,\n            strict: true,\n            stringKeys: false,\n            uniqueKeys: true,\n            version: '1.2'\n        }, options);\n        this.options = opt;\n        let { version } = opt;\n        if (options?._directives) {\n            this.directives = options._directives.atDocument();\n            if (this.directives.yaml.explicit)\n                version = this.directives.yaml.version;\n        }\n        else\n            this.directives = new directives.Directives({ version });\n        this.setSchema(version, options);\n        // @ts-expect-error We can't really know that this matches Contents.\n        this.contents =\n            value === undefined ? null : this.createNode(value, _replacer, options);\n    }\n    /**\n     * Create a deep copy of this Document and its contents.\n     *\n     * Custom Node values that inherit from `Object` still refer to their original instances.\n     */\n    clone() {\n        const copy = Object.create(Document.prototype, {\n            [identity.NODE_TYPE]: { value: identity.DOC }\n        });\n        copy.commentBefore = this.commentBefore;\n        copy.comment = this.comment;\n        copy.errors = this.errors.slice();\n        copy.warnings = this.warnings.slice();\n        copy.options = Object.assign({}, this.options);\n        if (this.directives)\n            copy.directives = this.directives.clone();\n        copy.schema = this.schema.clone();\n        // @ts-expect-error We can't really know that this matches Contents.\n        copy.contents = identity.isNode(this.contents)\n            ? this.contents.clone(copy.schema)\n            : this.contents;\n        if (this.range)\n            copy.range = this.range.slice();\n        return copy;\n    }\n    /** Adds a value to the document. */\n    add(value) {\n        if (assertCollection(this.contents))\n            this.contents.add(value);\n    }\n    /** Adds a value to the document. */\n    addIn(path, value) {\n        if (assertCollection(this.contents))\n            this.contents.addIn(path, value);\n    }\n    /**\n     * Create a new `Alias` node, ensuring that the target `node` has the required anchor.\n     *\n     * If `node` already has an anchor, `name` is ignored.\n     * Otherwise, the `node.anchor` value will be set to `name`,\n     * or if an anchor with that name is already present in the document,\n     * `name` will be used as a prefix for a new unique anchor.\n     * If `name` is undefined, the generated anchor will use 'a' as a prefix.\n     */\n    createAlias(node, name) {\n        if (!node.anchor) {\n            const prev = anchors.anchorNames(this);\n            node.anchor =\n                // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n                !name || prev.has(name) ? anchors.findNewAnchor(name || 'a', prev) : name;\n        }\n        return new Alias.Alias(node.anchor);\n    }\n    createNode(value, replacer, options) {\n        let _replacer = undefined;\n        if (typeof replacer === 'function') {\n            value = replacer.call({ '': value }, '', value);\n            _replacer = replacer;\n        }\n        else if (Array.isArray(replacer)) {\n            const keyToStr = (v) => typeof v === 'number' || v instanceof String || v instanceof Number;\n            const asStr = replacer.filter(keyToStr).map(String);\n            if (asStr.length > 0)\n                replacer = replacer.concat(asStr);\n            _replacer = replacer;\n        }\n        else if (options === undefined && replacer) {\n            options = replacer;\n            replacer = undefined;\n        }\n        const { aliasDuplicateObjects, anchorPrefix, flow, keepUndefined, onTagObj, tag } = options ?? {};\n        const { onAnchor, setAnchors, sourceObjects } = anchors.createNodeAnchors(this, \n        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n        anchorPrefix || 'a');\n        const ctx = {\n            aliasDuplicateObjects: aliasDuplicateObjects ?? true,\n            keepUndefined: keepUndefined ?? false,\n            onAnchor,\n            onTagObj,\n            replacer: _replacer,\n            schema: this.schema,\n            sourceObjects\n        };\n        const node = createNode.createNode(value, tag, ctx);\n        if (flow && identity.isCollection(node))\n            node.flow = true;\n        setAnchors();\n        return node;\n    }\n    /**\n     * Convert a key and a value into a `Pair` using the current schema,\n     * recursively wrapping all values as `Scalar` or `Collection` nodes.\n     */\n    createPair(key, value, options = {}) {\n        const k = this.createNode(key, null, options);\n        const v = this.createNode(value, null, options);\n        return new Pair.Pair(k, v);\n    }\n    /**\n     * Removes a value from the document.\n     * @returns `true` if the item was found and removed.\n     */\n    delete(key) {\n        return assertCollection(this.contents) ? this.contents.delete(key) : false;\n    }\n    /**\n     * Removes a value from the document.\n     * @returns `true` if the item was found and removed.\n     */\n    deleteIn(path) {\n        if (Collection.isEmptyPath(path)) {\n            if (this.contents == null)\n                return false;\n            // @ts-expect-error Presumed impossible if Strict extends false\n            this.contents = null;\n            return true;\n        }\n        return assertCollection(this.contents)\n            ? this.contents.deleteIn(path)\n            : false;\n    }\n    /**\n     * Returns item at `key`, or `undefined` if not found. By default unwraps\n     * scalar values from their surrounding node; to disable set `keepScalar` to\n     * `true` (collections are always returned intact).\n     */\n    get(key, keepScalar) {\n        return identity.isCollection(this.contents)\n            ? this.contents.get(key, keepScalar)\n            : undefined;\n    }\n    /**\n     * Returns item at `path`, or `undefined` if not found. By default unwraps\n     * scalar values from their surrounding node; to disable set `keepScalar` to\n     * `true` (collections are always returned intact).\n     */\n    getIn(path, keepScalar) {\n        if (Collection.isEmptyPath(path))\n            return !keepScalar && identity.isScalar(this.contents)\n                ? this.contents.value\n                : this.contents;\n        return identity.isCollection(this.contents)\n            ? this.contents.getIn(path, keepScalar)\n            : undefined;\n    }\n    /**\n     * Checks if the document includes a value with the key `key`.\n     */\n    has(key) {\n        return identity.isCollection(this.contents) ? this.contents.has(key) : false;\n    }\n    /**\n     * Checks if the document includes a value at `path`.\n     */\n    hasIn(path) {\n        if (Collection.isEmptyPath(path))\n            return this.contents !== undefined;\n        return identity.isCollection(this.contents) ? this.contents.hasIn(path) : false;\n    }\n    /**\n     * Sets a value in this document. For `!!set`, `value` needs to be a\n     * boolean to add/remove the item from the set.\n     */\n    set(key, value) {\n        if (this.contents == null) {\n            // @ts-expect-error We can't really know that this matches Contents.\n            this.contents = Collection.collectionFromPath(this.schema, [key], value);\n        }\n        else if (assertCollection(this.contents)) {\n            this.contents.set(key, value);\n        }\n    }\n    /**\n     * Sets a value in this document. For `!!set`, `value` needs to be a\n     * boolean to add/remove the item from the set.\n     */\n    setIn(path, value) {\n        if (Collection.isEmptyPath(path)) {\n            // @ts-expect-error We can't really know that this matches Contents.\n            this.contents = value;\n        }\n        else if (this.contents == null) {\n            // @ts-expect-error We can't really know that this matches Contents.\n            this.contents = Collection.collectionFromPath(this.schema, Array.from(path), value);\n        }\n        else if (assertCollection(this.contents)) {\n            this.contents.setIn(path, value);\n        }\n    }\n    /**\n     * Change the YAML version and schema used by the document.\n     * A `null` version disables support for directives, explicit tags, anchors, and aliases.\n     * It also requires the `schema` option to be given as a `Schema` instance value.\n     *\n     * Overrides all previously set schema options.\n     */\n    setSchema(version, options = {}) {\n        if (typeof version === 'number')\n            version = String(version);\n        let opt;\n        switch (version) {\n            case '1.1':\n                if (this.directives)\n                    this.directives.yaml.version = '1.1';\n                else\n                    this.directives = new directives.Directives({ version: '1.1' });\n                opt = { resolveKnownTags: false, schema: 'yaml-1.1' };\n                break;\n            case '1.2':\n            case 'next':\n                if (this.directives)\n                    this.directives.yaml.version = version;\n                else\n                    this.directives = new directives.Directives({ version });\n                opt = { resolveKnownTags: true, schema: 'core' };\n                break;\n            case null:\n                if (this.directives)\n                    delete this.directives;\n                opt = null;\n                break;\n            default: {\n                const sv = JSON.stringify(version);\n                throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${sv}`);\n            }\n        }\n        // Not using `instanceof Schema` to allow for duck typing\n        if (options.schema instanceof Object)\n            this.schema = options.schema;\n        else if (opt)\n            this.schema = new Schema.Schema(Object.assign(opt, options));\n        else\n            throw new Error(`With a null YAML version, the { schema: Schema } option is required`);\n    }\n    // json & jsonArg are only used from toJSON()\n    toJS({ json, jsonArg, mapAsMap, maxAliasCount, onAnchor, reviver } = {}) {\n        const ctx = {\n            anchors: new Map(),\n            doc: this,\n            keep: !json,\n            mapAsMap: mapAsMap === true,\n            mapKeyWarned: false,\n            maxAliasCount: typeof maxAliasCount === 'number' ? maxAliasCount : 100\n        };\n        const res = toJS.toJS(this.contents, jsonArg ?? '', ctx);\n        if (typeof onAnchor === 'function')\n            for (const { count, res } of ctx.anchors.values())\n                onAnchor(res, count);\n        return typeof reviver === 'function'\n            ? applyReviver.applyReviver(reviver, { '': res }, '', res)\n            : res;\n    }\n    /**\n     * A JSON representation of the document `contents`.\n     *\n     * @param jsonArg Used by `JSON.stringify` to indicate the array index or\n     *   property name.\n     */\n    toJSON(jsonArg, onAnchor) {\n        return this.toJS({ json: true, jsonArg, mapAsMap: false, onAnchor });\n    }\n    /** A YAML representation of the document. */\n    toString(options = {}) {\n        if (this.errors.length > 0)\n            throw new Error('Document with errors cannot be stringified');\n        if ('indent' in options &&\n            (!Number.isInteger(options.indent) || Number(options.indent) <= 0)) {\n            const s = JSON.stringify(options.indent);\n            throw new Error(`\"indent\" option must be a positive integer, not ${s}`);\n        }\n        return stringifyDocument.stringifyDocument(this, options);\n    }\n}\nfunction assertCollection(contents) {\n    if (identity.isCollection(contents))\n        return true;\n    throw new Error('Expected a YAML collection as document contents');\n}\n\nexports.Document = Document;\n", "'use strict';\n\nclass YAM<PERSON>rror extends <PERSON>rror {\n    constructor(name, pos, code, message) {\n        super();\n        this.name = name;\n        this.code = code;\n        this.message = message;\n        this.pos = pos;\n    }\n}\nclass YAMLParseError extends YAMLError {\n    constructor(pos, code, message) {\n        super('YAMLParseError', pos, code, message);\n    }\n}\nclass YAMLWarning extends YAM<PERSON>rror {\n    constructor(pos, code, message) {\n        super('YAMLWarning', pos, code, message);\n    }\n}\nconst prettifyError = (src, lc) => (error) => {\n    if (error.pos[0] === -1)\n        return;\n    error.linePos = error.pos.map(pos => lc.linePos(pos));\n    const { line, col } = error.linePos[0];\n    error.message += ` at line ${line}, column ${col}`;\n    let ci = col - 1;\n    let lineStr = src\n        .substring(lc.lineStarts[line - 1], lc.lineStarts[line])\n        .replace(/[\\n\\r]+$/, '');\n    // Trim to max 80 chars, keeping col position near the middle\n    if (ci >= 60 && lineStr.length > 80) {\n        const trimStart = Math.min(ci - 39, lineStr.length - 79);\n        lineStr = '…' + lineStr.substring(trimStart);\n        ci -= trimStart - 1;\n    }\n    if (lineStr.length > 80)\n        lineStr = lineStr.substring(0, 79) + '…';\n    // Include previous line in context if pointing at line start\n    if (line > 1 && /^ *$/.test(lineStr.substring(0, ci))) {\n        // Regexp won't match if start is trimmed\n        let prev = src.substring(lc.lineStarts[line - 2], lc.lineStarts[line - 1]);\n        if (prev.length > 80)\n            prev = prev.substring(0, 79) + '…\\n';\n        lineStr = prev + lineStr;\n    }\n    if (/[^ ]/.test(lineStr)) {\n        let count = 1;\n        const end = error.linePos[1];\n        if (end && end.line === line && end.col > col) {\n            count = Math.max(1, Math.min(end.col - col, 80 - ci));\n        }\n        const pointer = ' '.repeat(ci) + '^'.repeat(count);\n        error.message += `:\\n\\n${lineStr}\\n${pointer}\\n`;\n    }\n};\n\nexports.YAMLError = YAMLError;\nexports.YAMLParseError = YAMLParseError;\nexports.YAMLWarning = YAMLWarning;\nexports.prettifyError = prettifyError;\n", "'use strict';\n\nfunction resolveProps(tokens, { flow, indicator, next, offset, onError, parentIndent, startOnNewline }) {\n    let spaceBefore = false;\n    let atNewline = startOnNewline;\n    let hasSpace = startOnNewline;\n    let comment = '';\n    let commentSep = '';\n    let hasNewline = false;\n    let reqSpace = false;\n    let tab = null;\n    let anchor = null;\n    let tag = null;\n    let newlineAfterProp = null;\n    let comma = null;\n    let found = null;\n    let start = null;\n    for (const token of tokens) {\n        if (reqSpace) {\n            if (token.type !== 'space' &&\n                token.type !== 'newline' &&\n                token.type !== 'comma')\n                onError(token.offset, 'MISSING_CHAR', 'Tags and anchors must be separated from the next token by white space');\n            reqSpace = false;\n        }\n        if (tab) {\n            if (atNewline && token.type !== 'comment' && token.type !== 'newline') {\n                onError(tab, 'TAB_AS_INDENT', 'Tabs are not allowed as indentation');\n            }\n            tab = null;\n        }\n        switch (token.type) {\n            case 'space':\n                // At the doc level, tabs at line start may be parsed\n                // as leading white space rather than indentation.\n                // In a flow collection, only the parser handles indent.\n                if (!flow &&\n                    (indicator !== 'doc-start' || next?.type !== 'flow-collection') &&\n                    token.source.includes('\\t')) {\n                    tab = token;\n                }\n                hasSpace = true;\n                break;\n            case 'comment': {\n                if (!hasSpace)\n                    onError(token, 'MISSING_CHAR', 'Comments must be separated from other tokens by white space characters');\n                const cb = token.source.substring(1) || ' ';\n                if (!comment)\n                    comment = cb;\n                else\n                    comment += commentSep + cb;\n                commentSep = '';\n                atNewline = false;\n                break;\n            }\n            case 'newline':\n                if (atNewline) {\n                    if (comment)\n                        comment += token.source;\n                    else if (!found || indicator !== 'seq-item-ind')\n                        spaceBefore = true;\n                }\n                else\n                    commentSep += token.source;\n                atNewline = true;\n                hasNewline = true;\n                if (anchor || tag)\n                    newlineAfterProp = token;\n                hasSpace = true;\n                break;\n            case 'anchor':\n                if (anchor)\n                    onError(token, 'MULTIPLE_ANCHORS', 'A node can have at most one anchor');\n                if (token.source.endsWith(':'))\n                    onError(token.offset + token.source.length - 1, 'BAD_ALIAS', 'Anchor ending in : is ambiguous', true);\n                anchor = token;\n                if (start === null)\n                    start = token.offset;\n                atNewline = false;\n                hasSpace = false;\n                reqSpace = true;\n                break;\n            case 'tag': {\n                if (tag)\n                    onError(token, 'MULTIPLE_TAGS', 'A node can have at most one tag');\n                tag = token;\n                if (start === null)\n                    start = token.offset;\n                atNewline = false;\n                hasSpace = false;\n                reqSpace = true;\n                break;\n            }\n            case indicator:\n                // Could here handle preceding comments differently\n                if (anchor || tag)\n                    onError(token, 'BAD_PROP_ORDER', `Anchors and tags must be after the ${token.source} indicator`);\n                if (found)\n                    onError(token, 'UNEXPECTED_TOKEN', `Unexpected ${token.source} in ${flow ?? 'collection'}`);\n                found = token;\n                atNewline =\n                    indicator === 'seq-item-ind' || indicator === 'explicit-key-ind';\n                hasSpace = false;\n                break;\n            case 'comma':\n                if (flow) {\n                    if (comma)\n                        onError(token, 'UNEXPECTED_TOKEN', `Unexpected , in ${flow}`);\n                    comma = token;\n                    atNewline = false;\n                    hasSpace = false;\n                    break;\n                }\n            // else fallthrough\n            default:\n                onError(token, 'UNEXPECTED_TOKEN', `Unexpected ${token.type} token`);\n                atNewline = false;\n                hasSpace = false;\n        }\n    }\n    const last = tokens[tokens.length - 1];\n    const end = last ? last.offset + last.source.length : offset;\n    if (reqSpace &&\n        next &&\n        next.type !== 'space' &&\n        next.type !== 'newline' &&\n        next.type !== 'comma' &&\n        (next.type !== 'scalar' || next.source !== '')) {\n        onError(next.offset, 'MISSING_CHAR', 'Tags and anchors must be separated from the next token by white space');\n    }\n    if (tab &&\n        ((atNewline && tab.indent <= parentIndent) ||\n            next?.type === 'block-map' ||\n            next?.type === 'block-seq'))\n        onError(tab, 'TAB_AS_INDENT', 'Tabs are not allowed as indentation');\n    return {\n        comma,\n        found,\n        spaceBefore,\n        comment,\n        hasNewline,\n        anchor,\n        tag,\n        newlineAfterProp,\n        end,\n        start: start ?? end\n    };\n}\n\nexports.resolveProps = resolveProps;\n", "'use strict';\n\nfunction containsNewline(key) {\n    if (!key)\n        return null;\n    switch (key.type) {\n        case 'alias':\n        case 'scalar':\n        case 'double-quoted-scalar':\n        case 'single-quoted-scalar':\n            if (key.source.includes('\\n'))\n                return true;\n            if (key.end)\n                for (const st of key.end)\n                    if (st.type === 'newline')\n                        return true;\n            return false;\n        case 'flow-collection':\n            for (const it of key.items) {\n                for (const st of it.start)\n                    if (st.type === 'newline')\n                        return true;\n                if (it.sep)\n                    for (const st of it.sep)\n                        if (st.type === 'newline')\n                            return true;\n                if (containsNewline(it.key) || containsNewline(it.value))\n                    return true;\n            }\n            return false;\n        default:\n            return true;\n    }\n}\n\nexports.containsNewline = containsNewline;\n", "'use strict';\n\nvar utilContainsNewline = require('./util-contains-newline.js');\n\nfunction flowIndentCheck(indent, fc, onError) {\n    if (fc?.type === 'flow-collection') {\n        const end = fc.end[0];\n        if (end.indent === indent &&\n            (end.source === ']' || end.source === '}') &&\n            utilContainsNewline.containsNewline(fc)) {\n            const msg = 'Flow end indicator should be more indented than parent';\n            onError(end, 'BAD_INDENT', msg, true);\n        }\n    }\n}\n\nexports.flowIndentCheck = flowIndentCheck;\n", "'use strict';\n\nvar identity = require('../nodes/identity.js');\n\nfunction mapIncludes(ctx, items, search) {\n    const { uniqueKeys } = ctx.options;\n    if (uniqueKeys === false)\n        return false;\n    const isEqual = typeof uniqueKeys === 'function'\n        ? uniqueKeys\n        : (a, b) => a === b || (identity.isScalar(a) && identity.isScalar(b) && a.value === b.value);\n    return items.some(pair => isEqual(pair.key, search));\n}\n\nexports.mapIncludes = mapIncludes;\n", "'use strict';\n\nvar Pair = require('../nodes/Pair.js');\nvar YAMLMap = require('../nodes/YAMLMap.js');\nvar resolveProps = require('./resolve-props.js');\nvar utilContainsNewline = require('./util-contains-newline.js');\nvar utilFlowIndentCheck = require('./util-flow-indent-check.js');\nvar utilMapIncludes = require('./util-map-includes.js');\n\nconst startColMsg = 'All mapping items must start at the same column';\nfunction resolveBlockMap({ composeNode, composeEmptyNode }, ctx, bm, onError, tag) {\n    const NodeClass = tag?.nodeClass ?? YAMLMap.YAMLMap;\n    const map = new NodeClass(ctx.schema);\n    if (ctx.atRoot)\n        ctx.atRoot = false;\n    let offset = bm.offset;\n    let commentEnd = null;\n    for (const collItem of bm.items) {\n        const { start, key, sep, value } = collItem;\n        // key properties\n        const keyProps = resolveProps.resolveProps(start, {\n            indicator: 'explicit-key-ind',\n            next: key ?? sep?.[0],\n            offset,\n            onError,\n            parentIndent: bm.indent,\n            startOnNewline: true\n        });\n        const implicitKey = !keyProps.found;\n        if (implicitKey) {\n            if (key) {\n                if (key.type === 'block-seq')\n                    onError(offset, 'BLOCK_AS_IMPLICIT_KEY', 'A block sequence may not be used as an implicit map key');\n                else if ('indent' in key && key.indent !== bm.indent)\n                    onError(offset, 'BAD_INDENT', startColMsg);\n            }\n            if (!keyProps.anchor && !keyProps.tag && !sep) {\n                commentEnd = keyProps.end;\n                if (keyProps.comment) {\n                    if (map.comment)\n                        map.comment += '\\n' + keyProps.comment;\n                    else\n                        map.comment = keyProps.comment;\n                }\n                continue;\n            }\n            if (keyProps.newlineAfterProp || utilContainsNewline.containsNewline(key)) {\n                onError(key ?? start[start.length - 1], 'MULTILINE_IMPLICIT_KEY', 'Implicit keys need to be on a single line');\n            }\n        }\n        else if (keyProps.found?.indent !== bm.indent) {\n            onError(offset, 'BAD_INDENT', startColMsg);\n        }\n        // key value\n        ctx.atKey = true;\n        const keyStart = keyProps.end;\n        const keyNode = key\n            ? composeNode(ctx, key, keyProps, onError)\n            : composeEmptyNode(ctx, keyStart, start, null, keyProps, onError);\n        if (ctx.schema.compat)\n            utilFlowIndentCheck.flowIndentCheck(bm.indent, key, onError);\n        ctx.atKey = false;\n        if (utilMapIncludes.mapIncludes(ctx, map.items, keyNode))\n            onError(keyStart, 'DUPLICATE_KEY', 'Map keys must be unique');\n        // value properties\n        const valueProps = resolveProps.resolveProps(sep ?? [], {\n            indicator: 'map-value-ind',\n            next: value,\n            offset: keyNode.range[2],\n            onError,\n            parentIndent: bm.indent,\n            startOnNewline: !key || key.type === 'block-scalar'\n        });\n        offset = valueProps.end;\n        if (valueProps.found) {\n            if (implicitKey) {\n                if (value?.type === 'block-map' && !valueProps.hasNewline)\n                    onError(offset, 'BLOCK_AS_IMPLICIT_KEY', 'Nested mappings are not allowed in compact mappings');\n                if (ctx.options.strict &&\n                    keyProps.start < valueProps.found.offset - 1024)\n                    onError(keyNode.range, 'KEY_OVER_1024_CHARS', 'The : indicator must be at most 1024 chars after the start of an implicit block mapping key');\n            }\n            // value value\n            const valueNode = value\n                ? composeNode(ctx, value, valueProps, onError)\n                : composeEmptyNode(ctx, offset, sep, null, valueProps, onError);\n            if (ctx.schema.compat)\n                utilFlowIndentCheck.flowIndentCheck(bm.indent, value, onError);\n            offset = valueNode.range[2];\n            const pair = new Pair.Pair(keyNode, valueNode);\n            if (ctx.options.keepSourceTokens)\n                pair.srcToken = collItem;\n            map.items.push(pair);\n        }\n        else {\n            // key with no value\n            if (implicitKey)\n                onError(keyNode.range, 'MISSING_CHAR', 'Implicit map keys need to be followed by map values');\n            if (valueProps.comment) {\n                if (keyNode.comment)\n                    keyNode.comment += '\\n' + valueProps.comment;\n                else\n                    keyNode.comment = valueProps.comment;\n            }\n            const pair = new Pair.Pair(keyNode);\n            if (ctx.options.keepSourceTokens)\n                pair.srcToken = collItem;\n            map.items.push(pair);\n        }\n    }\n    if (commentEnd && commentEnd < offset)\n        onError(commentEnd, 'IMPOSSIBLE', 'Map comment with trailing content');\n    map.range = [bm.offset, offset, commentEnd ?? offset];\n    return map;\n}\n\nexports.resolveBlockMap = resolveBlockMap;\n", "'use strict';\n\nvar YAMLSeq = require('../nodes/YAMLSeq.js');\nvar resolveProps = require('./resolve-props.js');\nvar utilFlowIndentCheck = require('./util-flow-indent-check.js');\n\nfunction resolveBlockSeq({ composeNode, composeEmptyNode }, ctx, bs, onError, tag) {\n    const NodeClass = tag?.nodeClass ?? YAMLSeq.YAMLSeq;\n    const seq = new NodeClass(ctx.schema);\n    if (ctx.atRoot)\n        ctx.atRoot = false;\n    if (ctx.atKey)\n        ctx.atKey = false;\n    let offset = bs.offset;\n    let commentEnd = null;\n    for (const { start, value } of bs.items) {\n        const props = resolveProps.resolveProps(start, {\n            indicator: 'seq-item-ind',\n            next: value,\n            offset,\n            onError,\n            parentIndent: bs.indent,\n            startOnNewline: true\n        });\n        if (!props.found) {\n            if (props.anchor || props.tag || value) {\n                if (value && value.type === 'block-seq')\n                    onError(props.end, 'BAD_INDENT', 'All sequence items must start at the same column');\n                else\n                    onError(offset, 'MISSING_CHAR', 'Sequence item without - indicator');\n            }\n            else {\n                commentEnd = props.end;\n                if (props.comment)\n                    seq.comment = props.comment;\n                continue;\n            }\n        }\n        const node = value\n            ? composeNode(ctx, value, props, onError)\n            : composeEmptyNode(ctx, props.end, start, null, props, onError);\n        if (ctx.schema.compat)\n            utilFlowIndentCheck.flowIndentCheck(bs.indent, value, onError);\n        offset = node.range[2];\n        seq.items.push(node);\n    }\n    seq.range = [bs.offset, offset, commentEnd ?? offset];\n    return seq;\n}\n\nexports.resolveBlockSeq = resolveBlockSeq;\n", "'use strict';\n\nfunction resolveEnd(end, offset, reqSpace, onError) {\n    let comment = '';\n    if (end) {\n        let hasSpace = false;\n        let sep = '';\n        for (const token of end) {\n            const { source, type } = token;\n            switch (type) {\n                case 'space':\n                    hasSpace = true;\n                    break;\n                case 'comment': {\n                    if (reqSpace && !hasSpace)\n                        onError(token, 'MISSING_CHAR', 'Comments must be separated from other tokens by white space characters');\n                    const cb = source.substring(1) || ' ';\n                    if (!comment)\n                        comment = cb;\n                    else\n                        comment += sep + cb;\n                    sep = '';\n                    break;\n                }\n                case 'newline':\n                    if (comment)\n                        sep += source;\n                    hasSpace = true;\n                    break;\n                default:\n                    onError(token, 'UNEXPECTED_TOKEN', `Unexpected ${type} at node end`);\n            }\n            offset += source.length;\n        }\n    }\n    return { comment, offset };\n}\n\nexports.resolveEnd = resolveEnd;\n", "'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar Pair = require('../nodes/Pair.js');\nvar YAMLMap = require('../nodes/YAMLMap.js');\nvar YAMLSeq = require('../nodes/YAMLSeq.js');\nvar resolveEnd = require('./resolve-end.js');\nvar resolveProps = require('./resolve-props.js');\nvar utilContainsNewline = require('./util-contains-newline.js');\nvar utilMapIncludes = require('./util-map-includes.js');\n\nconst blockMsg = 'Block collections are not allowed within flow collections';\nconst isBlock = (token) => token && (token.type === 'block-map' || token.type === 'block-seq');\nfunction resolveFlowCollection({ composeNode, composeEmptyNode }, ctx, fc, onError, tag) {\n    const isMap = fc.start.source === '{';\n    const fcName = isMap ? 'flow map' : 'flow sequence';\n    const NodeClass = (tag?.nodeClass ?? (isMap ? YAMLMap.YAMLMap : YAMLSeq.YAMLSeq));\n    const coll = new NodeClass(ctx.schema);\n    coll.flow = true;\n    const atRoot = ctx.atRoot;\n    if (atRoot)\n        ctx.atRoot = false;\n    if (ctx.atKey)\n        ctx.atKey = false;\n    let offset = fc.offset + fc.start.source.length;\n    for (let i = 0; i < fc.items.length; ++i) {\n        const collItem = fc.items[i];\n        const { start, key, sep, value } = collItem;\n        const props = resolveProps.resolveProps(start, {\n            flow: fcName,\n            indicator: 'explicit-key-ind',\n            next: key ?? sep?.[0],\n            offset,\n            onError,\n            parentIndent: fc.indent,\n            startOnNewline: false\n        });\n        if (!props.found) {\n            if (!props.anchor && !props.tag && !sep && !value) {\n                if (i === 0 && props.comma)\n                    onError(props.comma, 'UNEXPECTED_TOKEN', `Unexpected , in ${fcName}`);\n                else if (i < fc.items.length - 1)\n                    onError(props.start, 'UNEXPECTED_TOKEN', `Unexpected empty item in ${fcName}`);\n                if (props.comment) {\n                    if (coll.comment)\n                        coll.comment += '\\n' + props.comment;\n                    else\n                        coll.comment = props.comment;\n                }\n                offset = props.end;\n                continue;\n            }\n            if (!isMap && ctx.options.strict && utilContainsNewline.containsNewline(key))\n                onError(key, // checked by containsNewline()\n                'MULTILINE_IMPLICIT_KEY', 'Implicit keys of flow sequence pairs need to be on a single line');\n        }\n        if (i === 0) {\n            if (props.comma)\n                onError(props.comma, 'UNEXPECTED_TOKEN', `Unexpected , in ${fcName}`);\n        }\n        else {\n            if (!props.comma)\n                onError(props.start, 'MISSING_CHAR', `Missing , between ${fcName} items`);\n            if (props.comment) {\n                let prevItemComment = '';\n                loop: for (const st of start) {\n                    switch (st.type) {\n                        case 'comma':\n                        case 'space':\n                            break;\n                        case 'comment':\n                            prevItemComment = st.source.substring(1);\n                            break loop;\n                        default:\n                            break loop;\n                    }\n                }\n                if (prevItemComment) {\n                    let prev = coll.items[coll.items.length - 1];\n                    if (identity.isPair(prev))\n                        prev = prev.value ?? prev.key;\n                    if (prev.comment)\n                        prev.comment += '\\n' + prevItemComment;\n                    else\n                        prev.comment = prevItemComment;\n                    props.comment = props.comment.substring(prevItemComment.length + 1);\n                }\n            }\n        }\n        if (!isMap && !sep && !props.found) {\n            // item is a value in a seq\n            // → key & sep are empty, start does not include ? or :\n            const valueNode = value\n                ? composeNode(ctx, value, props, onError)\n                : composeEmptyNode(ctx, props.end, sep, null, props, onError);\n            coll.items.push(valueNode);\n            offset = valueNode.range[2];\n            if (isBlock(value))\n                onError(valueNode.range, 'BLOCK_IN_FLOW', blockMsg);\n        }\n        else {\n            // item is a key+value pair\n            // key value\n            ctx.atKey = true;\n            const keyStart = props.end;\n            const keyNode = key\n                ? composeNode(ctx, key, props, onError)\n                : composeEmptyNode(ctx, keyStart, start, null, props, onError);\n            if (isBlock(key))\n                onError(keyNode.range, 'BLOCK_IN_FLOW', blockMsg);\n            ctx.atKey = false;\n            // value properties\n            const valueProps = resolveProps.resolveProps(sep ?? [], {\n                flow: fcName,\n                indicator: 'map-value-ind',\n                next: value,\n                offset: keyNode.range[2],\n                onError,\n                parentIndent: fc.indent,\n                startOnNewline: false\n            });\n            if (valueProps.found) {\n                if (!isMap && !props.found && ctx.options.strict) {\n                    if (sep)\n                        for (const st of sep) {\n                            if (st === valueProps.found)\n                                break;\n                            if (st.type === 'newline') {\n                                onError(st, 'MULTILINE_IMPLICIT_KEY', 'Implicit keys of flow sequence pairs need to be on a single line');\n                                break;\n                            }\n                        }\n                    if (props.start < valueProps.found.offset - 1024)\n                        onError(valueProps.found, 'KEY_OVER_1024_CHARS', 'The : indicator must be at most 1024 chars after the start of an implicit flow sequence key');\n                }\n            }\n            else if (value) {\n                if ('source' in value && value.source && value.source[0] === ':')\n                    onError(value, 'MISSING_CHAR', `Missing space after : in ${fcName}`);\n                else\n                    onError(valueProps.start, 'MISSING_CHAR', `Missing , or : between ${fcName} items`);\n            }\n            // value value\n            const valueNode = value\n                ? composeNode(ctx, value, valueProps, onError)\n                : valueProps.found\n                    ? composeEmptyNode(ctx, valueProps.end, sep, null, valueProps, onError)\n                    : null;\n            if (valueNode) {\n                if (isBlock(value))\n                    onError(valueNode.range, 'BLOCK_IN_FLOW', blockMsg);\n            }\n            else if (valueProps.comment) {\n                if (keyNode.comment)\n                    keyNode.comment += '\\n' + valueProps.comment;\n                else\n                    keyNode.comment = valueProps.comment;\n            }\n            const pair = new Pair.Pair(keyNode, valueNode);\n            if (ctx.options.keepSourceTokens)\n                pair.srcToken = collItem;\n            if (isMap) {\n                const map = coll;\n                if (utilMapIncludes.mapIncludes(ctx, map.items, keyNode))\n                    onError(keyStart, 'DUPLICATE_KEY', 'Map keys must be unique');\n                map.items.push(pair);\n            }\n            else {\n                const map = new YAMLMap.YAMLMap(ctx.schema);\n                map.flow = true;\n                map.items.push(pair);\n                const endRange = (valueNode ?? keyNode).range;\n                map.range = [keyNode.range[0], endRange[1], endRange[2]];\n                coll.items.push(map);\n            }\n            offset = valueNode ? valueNode.range[2] : valueProps.end;\n        }\n    }\n    const expectedEnd = isMap ? '}' : ']';\n    const [ce, ...ee] = fc.end;\n    let cePos = offset;\n    if (ce && ce.source === expectedEnd)\n        cePos = ce.offset + ce.source.length;\n    else {\n        const name = fcName[0].toUpperCase() + fcName.substring(1);\n        const msg = atRoot\n            ? `${name} must end with a ${expectedEnd}`\n            : `${name} in block collection must be sufficiently indented and end with a ${expectedEnd}`;\n        onError(offset, atRoot ? 'MISSING_CHAR' : 'BAD_INDENT', msg);\n        if (ce && ce.source.length !== 1)\n            ee.unshift(ce);\n    }\n    if (ee.length > 0) {\n        const end = resolveEnd.resolveEnd(ee, cePos, ctx.options.strict, onError);\n        if (end.comment) {\n            if (coll.comment)\n                coll.comment += '\\n' + end.comment;\n            else\n                coll.comment = end.comment;\n        }\n        coll.range = [fc.offset, cePos, end.offset];\n    }\n    else {\n        coll.range = [fc.offset, cePos, cePos];\n    }\n    return coll;\n}\n\nexports.resolveFlowCollection = resolveFlowCollection;\n", "'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar Scalar = require('../nodes/Scalar.js');\nvar YAMLMap = require('../nodes/YAMLMap.js');\nvar YAMLSeq = require('../nodes/YAMLSeq.js');\nvar resolveBlockMap = require('./resolve-block-map.js');\nvar resolveBlockSeq = require('./resolve-block-seq.js');\nvar resolveFlowCollection = require('./resolve-flow-collection.js');\n\nfunction resolveCollection(CN, ctx, token, onError, tagName, tag) {\n    const coll = token.type === 'block-map'\n        ? resolveBlockMap.resolveBlockMap(CN, ctx, token, onError, tag)\n        : token.type === 'block-seq'\n            ? resolveBlockSeq.resolveBlockSeq(CN, ctx, token, onError, tag)\n            : resolveFlowCollection.resolveFlowCollection(CN, ctx, token, onError, tag);\n    const Coll = coll.constructor;\n    // If we got a tagName matching the class, or the tag name is '!',\n    // then use the tagName from the node class used to create it.\n    if (tagName === '!' || tagName === Coll.tagName) {\n        coll.tag = Coll.tagName;\n        return coll;\n    }\n    if (tagName)\n        coll.tag = tagName;\n    return coll;\n}\nfunction composeCollection(CN, ctx, token, props, onError) {\n    const tagToken = props.tag;\n    const tagName = !tagToken\n        ? null\n        : ctx.directives.tagName(tagToken.source, msg => onError(tagToken, 'TAG_RESOLVE_FAILED', msg));\n    if (token.type === 'block-seq') {\n        const { anchor, newlineAfterProp: nl } = props;\n        const lastProp = anchor && tagToken\n            ? anchor.offset > tagToken.offset\n                ? anchor\n                : tagToken\n            : (anchor ?? tagToken);\n        if (lastProp && (!nl || nl.offset < lastProp.offset)) {\n            const message = 'Missing newline after block sequence props';\n            onError(lastProp, 'MISSING_CHAR', message);\n        }\n    }\n    const expType = token.type === 'block-map'\n        ? 'map'\n        : token.type === 'block-seq'\n            ? 'seq'\n            : token.start.source === '{'\n                ? 'map'\n                : 'seq';\n    // shortcut: check if it's a generic YAMLMap or YAMLSeq\n    // before jumping into the custom tag logic.\n    if (!tagToken ||\n        !tagName ||\n        tagName === '!' ||\n        (tagName === YAMLMap.YAMLMap.tagName && expType === 'map') ||\n        (tagName === YAMLSeq.YAMLSeq.tagName && expType === 'seq')) {\n        return resolveCollection(CN, ctx, token, onError, tagName);\n    }\n    let tag = ctx.schema.tags.find(t => t.tag === tagName && t.collection === expType);\n    if (!tag) {\n        const kt = ctx.schema.knownTags[tagName];\n        if (kt && kt.collection === expType) {\n            ctx.schema.tags.push(Object.assign({}, kt, { default: false }));\n            tag = kt;\n        }\n        else {\n            if (kt?.collection) {\n                onError(tagToken, 'BAD_COLLECTION_TYPE', `${kt.tag} used for ${expType} collection, but expects ${kt.collection}`, true);\n            }\n            else {\n                onError(tagToken, 'TAG_RESOLVE_FAILED', `Unresolved tag: ${tagName}`, true);\n            }\n            return resolveCollection(CN, ctx, token, onError, tagName);\n        }\n    }\n    const coll = resolveCollection(CN, ctx, token, onError, tagName, tag);\n    const res = tag.resolve?.(coll, msg => onError(tagToken, 'TAG_RESOLVE_FAILED', msg), ctx.options) ?? coll;\n    const node = identity.isNode(res)\n        ? res\n        : new Scalar.Scalar(res);\n    node.range = coll.range;\n    node.tag = tagName;\n    if (tag?.format)\n        node.format = tag.format;\n    return node;\n}\n\nexports.composeCollection = composeCollection;\n", "'use strict';\n\nvar Scalar = require('../nodes/Scalar.js');\n\nfunction resolveBlockScalar(ctx, scalar, onError) {\n    const start = scalar.offset;\n    const header = parseBlockScalarHeader(scalar, ctx.options.strict, onError);\n    if (!header)\n        return { value: '', type: null, comment: '', range: [start, start, start] };\n    const type = header.mode === '>' ? Scalar.Scalar.BLOCK_FOLDED : Scalar.Scalar.BLOCK_LITERAL;\n    const lines = scalar.source ? splitLines(scalar.source) : [];\n    // determine the end of content & start of chomping\n    let chompStart = lines.length;\n    for (let i = lines.length - 1; i >= 0; --i) {\n        const content = lines[i][1];\n        if (content === '' || content === '\\r')\n            chompStart = i;\n        else\n            break;\n    }\n    // shortcut for empty contents\n    if (chompStart === 0) {\n        const value = header.chomp === '+' && lines.length > 0\n            ? '\\n'.repeat(Math.max(1, lines.length - 1))\n            : '';\n        let end = start + header.length;\n        if (scalar.source)\n            end += scalar.source.length;\n        return { value, type, comment: header.comment, range: [start, end, end] };\n    }\n    // find the indentation level to trim from start\n    let trimIndent = scalar.indent + header.indent;\n    let offset = scalar.offset + header.length;\n    let contentStart = 0;\n    for (let i = 0; i < chompStart; ++i) {\n        const [indent, content] = lines[i];\n        if (content === '' || content === '\\r') {\n            if (header.indent === 0 && indent.length > trimIndent)\n                trimIndent = indent.length;\n        }\n        else {\n            if (indent.length < trimIndent) {\n                const message = 'Block scalars with more-indented leading empty lines must use an explicit indentation indicator';\n                onError(offset + indent.length, 'MISSING_CHAR', message);\n            }\n            if (header.indent === 0)\n                trimIndent = indent.length;\n            contentStart = i;\n            if (trimIndent === 0 && !ctx.atRoot) {\n                const message = 'Block scalar values in collections must be indented';\n                onError(offset, 'BAD_INDENT', message);\n            }\n            break;\n        }\n        offset += indent.length + content.length + 1;\n    }\n    // include trailing more-indented empty lines in content\n    for (let i = lines.length - 1; i >= chompStart; --i) {\n        if (lines[i][0].length > trimIndent)\n            chompStart = i + 1;\n    }\n    let value = '';\n    let sep = '';\n    let prevMoreIndented = false;\n    // leading whitespace is kept intact\n    for (let i = 0; i < contentStart; ++i)\n        value += lines[i][0].slice(trimIndent) + '\\n';\n    for (let i = contentStart; i < chompStart; ++i) {\n        let [indent, content] = lines[i];\n        offset += indent.length + content.length + 1;\n        const crlf = content[content.length - 1] === '\\r';\n        if (crlf)\n            content = content.slice(0, -1);\n        /* istanbul ignore if already caught in lexer */\n        if (content && indent.length < trimIndent) {\n            const src = header.indent\n                ? 'explicit indentation indicator'\n                : 'first line';\n            const message = `Block scalar lines must not be less indented than their ${src}`;\n            onError(offset - content.length - (crlf ? 2 : 1), 'BAD_INDENT', message);\n            indent = '';\n        }\n        if (type === Scalar.Scalar.BLOCK_LITERAL) {\n            value += sep + indent.slice(trimIndent) + content;\n            sep = '\\n';\n        }\n        else if (indent.length > trimIndent || content[0] === '\\t') {\n            // more-indented content within a folded block\n            if (sep === ' ')\n                sep = '\\n';\n            else if (!prevMoreIndented && sep === '\\n')\n                sep = '\\n\\n';\n            value += sep + indent.slice(trimIndent) + content;\n            sep = '\\n';\n            prevMoreIndented = true;\n        }\n        else if (content === '') {\n            // empty line\n            if (sep === '\\n')\n                value += '\\n';\n            else\n                sep = '\\n';\n        }\n        else {\n            value += sep + content;\n            sep = ' ';\n            prevMoreIndented = false;\n        }\n    }\n    switch (header.chomp) {\n        case '-':\n            break;\n        case '+':\n            for (let i = chompStart; i < lines.length; ++i)\n                value += '\\n' + lines[i][0].slice(trimIndent);\n            if (value[value.length - 1] !== '\\n')\n                value += '\\n';\n            break;\n        default:\n            value += '\\n';\n    }\n    const end = start + header.length + scalar.source.length;\n    return { value, type, comment: header.comment, range: [start, end, end] };\n}\nfunction parseBlockScalarHeader({ offset, props }, strict, onError) {\n    /* istanbul ignore if should not happen */\n    if (props[0].type !== 'block-scalar-header') {\n        onError(props[0], 'IMPOSSIBLE', 'Block scalar header not found');\n        return null;\n    }\n    const { source } = props[0];\n    const mode = source[0];\n    let indent = 0;\n    let chomp = '';\n    let error = -1;\n    for (let i = 1; i < source.length; ++i) {\n        const ch = source[i];\n        if (!chomp && (ch === '-' || ch === '+'))\n            chomp = ch;\n        else {\n            const n = Number(ch);\n            if (!indent && n)\n                indent = n;\n            else if (error === -1)\n                error = offset + i;\n        }\n    }\n    if (error !== -1)\n        onError(error, 'UNEXPECTED_TOKEN', `Block scalar header includes extra characters: ${source}`);\n    let hasSpace = false;\n    let comment = '';\n    let length = source.length;\n    for (let i = 1; i < props.length; ++i) {\n        const token = props[i];\n        switch (token.type) {\n            case 'space':\n                hasSpace = true;\n            // fallthrough\n            case 'newline':\n                length += token.source.length;\n                break;\n            case 'comment':\n                if (strict && !hasSpace) {\n                    const message = 'Comments must be separated from other tokens by white space characters';\n                    onError(token, 'MISSING_CHAR', message);\n                }\n                length += token.source.length;\n                comment = token.source.substring(1);\n                break;\n            case 'error':\n                onError(token, 'UNEXPECTED_TOKEN', token.message);\n                length += token.source.length;\n                break;\n            /* istanbul ignore next should not happen */\n            default: {\n                const message = `Unexpected token in block scalar header: ${token.type}`;\n                onError(token, 'UNEXPECTED_TOKEN', message);\n                const ts = token.source;\n                if (ts && typeof ts === 'string')\n                    length += ts.length;\n            }\n        }\n    }\n    return { mode, indent, chomp, comment, length };\n}\n/** @returns Array of lines split up as `[indent, content]` */\nfunction splitLines(source) {\n    const split = source.split(/\\n( *)/);\n    const first = split[0];\n    const m = first.match(/^( *)/);\n    const line0 = m?.[1]\n        ? [m[1], first.slice(m[1].length)]\n        : ['', first];\n    const lines = [line0];\n    for (let i = 1; i < split.length; i += 2)\n        lines.push([split[i], split[i + 1]]);\n    return lines;\n}\n\nexports.resolveBlockScalar = resolveBlockScalar;\n", "'use strict';\n\nvar Scalar = require('../nodes/Scalar.js');\nvar resolveEnd = require('./resolve-end.js');\n\nfunction resolveFlowScalar(scalar, strict, onError) {\n    const { offset, type, source, end } = scalar;\n    let _type;\n    let value;\n    const _onError = (rel, code, msg) => onError(offset + rel, code, msg);\n    switch (type) {\n        case 'scalar':\n            _type = Scalar.Scalar.PLAIN;\n            value = plainValue(source, _onError);\n            break;\n        case 'single-quoted-scalar':\n            _type = Scalar.Scalar.QUOTE_SINGLE;\n            value = singleQuotedValue(source, _onError);\n            break;\n        case 'double-quoted-scalar':\n            _type = Scalar.Scalar.QUOTE_DOUBLE;\n            value = doubleQuotedValue(source, _onError);\n            break;\n        /* istanbul ignore next should not happen */\n        default:\n            onError(scalar, 'UNEXPECTED_TOKEN', `Expected a flow scalar value, but found: ${type}`);\n            return {\n                value: '',\n                type: null,\n                comment: '',\n                range: [offset, offset + source.length, offset + source.length]\n            };\n    }\n    const valueEnd = offset + source.length;\n    const re = resolveEnd.resolveEnd(end, valueEnd, strict, onError);\n    return {\n        value,\n        type: _type,\n        comment: re.comment,\n        range: [offset, valueEnd, re.offset]\n    };\n}\nfunction plainValue(source, onError) {\n    let badChar = '';\n    switch (source[0]) {\n        /* istanbul ignore next should not happen */\n        case '\\t':\n            badChar = 'a tab character';\n            break;\n        case ',':\n            badChar = 'flow indicator character ,';\n            break;\n        case '%':\n            badChar = 'directive indicator character %';\n            break;\n        case '|':\n        case '>': {\n            badChar = `block scalar indicator ${source[0]}`;\n            break;\n        }\n        case '@':\n        case '`': {\n            badChar = `reserved character ${source[0]}`;\n            break;\n        }\n    }\n    if (badChar)\n        onError(0, 'BAD_SCALAR_START', `Plain value cannot start with ${badChar}`);\n    return foldLines(source);\n}\nfunction singleQuotedValue(source, onError) {\n    if (source[source.length - 1] !== \"'\" || source.length === 1)\n        onError(source.length, 'MISSING_CHAR', \"Missing closing 'quote\");\n    return foldLines(source.slice(1, -1)).replace(/''/g, \"'\");\n}\nfunction foldLines(source) {\n    /**\n     * The negative lookbehind here and in the `re` RegExp is to\n     * prevent causing a polynomial search time in certain cases.\n     *\n     * The try-catch is for Safari, which doesn't support this yet:\n     * https://caniuse.com/js-regexp-lookbehind\n     */\n    let first, line;\n    try {\n        first = new RegExp('(.*?)(?<![ \\t])[ \\t]*\\r?\\n', 'sy');\n        line = new RegExp('[ \\t]*(.*?)(?:(?<![ \\t])[ \\t]*)?\\r?\\n', 'sy');\n    }\n    catch {\n        first = /(.*?)[ \\t]*\\r?\\n/sy;\n        line = /[ \\t]*(.*?)[ \\t]*\\r?\\n/sy;\n    }\n    let match = first.exec(source);\n    if (!match)\n        return source;\n    let res = match[1];\n    let sep = ' ';\n    let pos = first.lastIndex;\n    line.lastIndex = pos;\n    while ((match = line.exec(source))) {\n        if (match[1] === '') {\n            if (sep === '\\n')\n                res += sep;\n            else\n                sep = '\\n';\n        }\n        else {\n            res += sep + match[1];\n            sep = ' ';\n        }\n        pos = line.lastIndex;\n    }\n    const last = /[ \\t]*(.*)/sy;\n    last.lastIndex = pos;\n    match = last.exec(source);\n    return res + sep + (match?.[1] ?? '');\n}\nfunction doubleQuotedValue(source, onError) {\n    let res = '';\n    for (let i = 1; i < source.length - 1; ++i) {\n        const ch = source[i];\n        if (ch === '\\r' && source[i + 1] === '\\n')\n            continue;\n        if (ch === '\\n') {\n            const { fold, offset } = foldNewline(source, i);\n            res += fold;\n            i = offset;\n        }\n        else if (ch === '\\\\') {\n            let next = source[++i];\n            const cc = escapeCodes[next];\n            if (cc)\n                res += cc;\n            else if (next === '\\n') {\n                // skip escaped newlines, but still trim the following line\n                next = source[i + 1];\n                while (next === ' ' || next === '\\t')\n                    next = source[++i + 1];\n            }\n            else if (next === '\\r' && source[i + 1] === '\\n') {\n                // skip escaped CRLF newlines, but still trim the following line\n                next = source[++i + 1];\n                while (next === ' ' || next === '\\t')\n                    next = source[++i + 1];\n            }\n            else if (next === 'x' || next === 'u' || next === 'U') {\n                const length = { x: 2, u: 4, U: 8 }[next];\n                res += parseCharCode(source, i + 1, length, onError);\n                i += length;\n            }\n            else {\n                const raw = source.substr(i - 1, 2);\n                onError(i - 1, 'BAD_DQ_ESCAPE', `Invalid escape sequence ${raw}`);\n                res += raw;\n            }\n        }\n        else if (ch === ' ' || ch === '\\t') {\n            // trim trailing whitespace\n            const wsStart = i;\n            let next = source[i + 1];\n            while (next === ' ' || next === '\\t')\n                next = source[++i + 1];\n            if (next !== '\\n' && !(next === '\\r' && source[i + 2] === '\\n'))\n                res += i > wsStart ? source.slice(wsStart, i + 1) : ch;\n        }\n        else {\n            res += ch;\n        }\n    }\n    if (source[source.length - 1] !== '\"' || source.length === 1)\n        onError(source.length, 'MISSING_CHAR', 'Missing closing \"quote');\n    return res;\n}\n/**\n * Fold a single newline into a space, multiple newlines to N - 1 newlines.\n * Presumes `source[offset] === '\\n'`\n */\nfunction foldNewline(source, offset) {\n    let fold = '';\n    let ch = source[offset + 1];\n    while (ch === ' ' || ch === '\\t' || ch === '\\n' || ch === '\\r') {\n        if (ch === '\\r' && source[offset + 2] !== '\\n')\n            break;\n        if (ch === '\\n')\n            fold += '\\n';\n        offset += 1;\n        ch = source[offset + 1];\n    }\n    if (!fold)\n        fold = ' ';\n    return { fold, offset };\n}\nconst escapeCodes = {\n    '0': '\\0', // null character\n    a: '\\x07', // bell character\n    b: '\\b', // backspace\n    e: '\\x1b', // escape character\n    f: '\\f', // form feed\n    n: '\\n', // line feed\n    r: '\\r', // carriage return\n    t: '\\t', // horizontal tab\n    v: '\\v', // vertical tab\n    N: '\\u0085', // Unicode next line\n    _: '\\u00a0', // Unicode non-breaking space\n    L: '\\u2028', // Unicode line separator\n    P: '\\u2029', // Unicode paragraph separator\n    ' ': ' ',\n    '\"': '\"',\n    '/': '/',\n    '\\\\': '\\\\',\n    '\\t': '\\t'\n};\nfunction parseCharCode(source, offset, length, onError) {\n    const cc = source.substr(offset, length);\n    const ok = cc.length === length && /^[0-9a-fA-F]+$/.test(cc);\n    const code = ok ? parseInt(cc, 16) : NaN;\n    if (isNaN(code)) {\n        const raw = source.substr(offset - 2, length + 2);\n        onError(offset - 2, 'BAD_DQ_ESCAPE', `Invalid escape sequence ${raw}`);\n        return raw;\n    }\n    return String.fromCodePoint(code);\n}\n\nexports.resolveFlowScalar = resolveFlowScalar;\n", "'use strict';\n\nvar identity = require('../nodes/identity.js');\nvar Scalar = require('../nodes/Scalar.js');\nvar resolveBlockScalar = require('./resolve-block-scalar.js');\nvar resolveFlowScalar = require('./resolve-flow-scalar.js');\n\nfunction composeScalar(ctx, token, tagToken, onError) {\n    const { value, type, comment, range } = token.type === 'block-scalar'\n        ? resolveBlockScalar.resolveBlockScalar(ctx, token, onError)\n        : resolveFlowScalar.resolveFlowScalar(token, ctx.options.strict, onError);\n    const tagName = tagToken\n        ? ctx.directives.tagName(tagToken.source, msg => onError(tagToken, 'TAG_RESOLVE_FAILED', msg))\n        : null;\n    let tag;\n    if (ctx.options.stringKeys && ctx.atKey) {\n        tag = ctx.schema[identity.SCALAR];\n    }\n    else if (tagName)\n        tag = findScalarTagByName(ctx.schema, value, tagName, tagToken, onError);\n    else if (token.type === 'scalar')\n        tag = findScalarTagByTest(ctx, value, token, onError);\n    else\n        tag = ctx.schema[identity.SCALAR];\n    let scalar;\n    try {\n        const res = tag.resolve(value, msg => onError(tagToken ?? token, 'TAG_RESOLVE_FAILED', msg), ctx.options);\n        scalar = identity.isScalar(res) ? res : new Scalar.Scalar(res);\n    }\n    catch (error) {\n        const msg = error instanceof Error ? error.message : String(error);\n        onError(tagToken ?? token, 'TAG_RESOLVE_FAILED', msg);\n        scalar = new Scalar.Scalar(value);\n    }\n    scalar.range = range;\n    scalar.source = value;\n    if (type)\n        scalar.type = type;\n    if (tagName)\n        scalar.tag = tagName;\n    if (tag.format)\n        scalar.format = tag.format;\n    if (comment)\n        scalar.comment = comment;\n    return scalar;\n}\nfunction findScalarTagByName(schema, value, tagName, tagToken, onError) {\n    if (tagName === '!')\n        return schema[identity.SCALAR]; // non-specific tag\n    const matchWithTest = [];\n    for (const tag of schema.tags) {\n        if (!tag.collection && tag.tag === tagName) {\n            if (tag.default && tag.test)\n                matchWithTest.push(tag);\n            else\n                return tag;\n        }\n    }\n    for (const tag of matchWithTest)\n        if (tag.test?.test(value))\n            return tag;\n    const kt = schema.knownTags[tagName];\n    if (kt && !kt.collection) {\n        // Ensure that the known tag is available for stringifying,\n        // but does not get used by default.\n        schema.tags.push(Object.assign({}, kt, { default: false, test: undefined }));\n        return kt;\n    }\n    onError(tagToken, 'TAG_RESOLVE_FAILED', `Unresolved tag: ${tagName}`, tagName !== 'tag:yaml.org,2002:str');\n    return schema[identity.SCALAR];\n}\nfunction findScalarTagByTest({ atKey, directives, schema }, value, token, onError) {\n    const tag = schema.tags.find(tag => (tag.default === true || (atKey && tag.default === 'key')) &&\n        tag.test?.test(value)) || schema[identity.SCALAR];\n    if (schema.compat) {\n        const compat = schema.compat.find(tag => tag.default && tag.test?.test(value)) ??\n            schema[identity.SCALAR];\n        if (tag.tag !== compat.tag) {\n            const ts = directives.tagString(tag.tag);\n            const cs = directives.tagString(compat.tag);\n            const msg = `Value may be parsed as either ${ts} or ${cs}`;\n            onError(token, 'TAG_RESOLVE_FAILED', msg, true);\n        }\n    }\n    return tag;\n}\n\nexports.composeScalar = composeScalar;\n", "'use strict';\n\nfunction emptyScalarPosition(offset, before, pos) {\n    if (before) {\n        if (pos === null)\n            pos = before.length;\n        for (let i = pos - 1; i >= 0; --i) {\n            let st = before[i];\n            switch (st.type) {\n                case 'space':\n                case 'comment':\n                case 'newline':\n                    offset -= st.source.length;\n                    continue;\n            }\n            // Technically, an empty scalar is immediately after the last non-empty\n            // node, but it's more useful to place it after any whitespace.\n            st = before[++i];\n            while (st?.type === 'space') {\n                offset += st.source.length;\n                st = before[++i];\n            }\n            break;\n        }\n    }\n    return offset;\n}\n\nexports.emptyScalarPosition = emptyScalarPosition;\n", "'use strict';\n\nvar Alias = require('../nodes/Alias.js');\nvar identity = require('../nodes/identity.js');\nvar composeCollection = require('./compose-collection.js');\nvar composeScalar = require('./compose-scalar.js');\nvar resolveEnd = require('./resolve-end.js');\nvar utilEmptyScalarPosition = require('./util-empty-scalar-position.js');\n\nconst CN = { composeNode, composeEmptyNode };\nfunction composeNode(ctx, token, props, onError) {\n    const atKey = ctx.atKey;\n    const { spaceBefore, comment, anchor, tag } = props;\n    let node;\n    let isSrcToken = true;\n    switch (token.type) {\n        case 'alias':\n            node = composeAlias(ctx, token, onError);\n            if (anchor || tag)\n                onError(token, 'ALIAS_PROPS', 'An alias node must not specify any properties');\n            break;\n        case 'scalar':\n        case 'single-quoted-scalar':\n        case 'double-quoted-scalar':\n        case 'block-scalar':\n            node = composeScalar.composeScalar(ctx, token, tag, onError);\n            if (anchor)\n                node.anchor = anchor.source.substring(1);\n            break;\n        case 'block-map':\n        case 'block-seq':\n        case 'flow-collection':\n            node = composeCollection.composeCollection(CN, ctx, token, props, onError);\n            if (anchor)\n                node.anchor = anchor.source.substring(1);\n            break;\n        default: {\n            const message = token.type === 'error'\n                ? token.message\n                : `Unsupported token (type: ${token.type})`;\n            onError(token, 'UNEXPECTED_TOKEN', message);\n            node = composeEmptyNode(ctx, token.offset, undefined, null, props, onError);\n            isSrcToken = false;\n        }\n    }\n    if (anchor && node.anchor === '')\n        onError(anchor, 'BAD_ALIAS', 'Anchor cannot be an empty string');\n    if (atKey &&\n        ctx.options.stringKeys &&\n        (!identity.isScalar(node) ||\n            typeof node.value !== 'string' ||\n            (node.tag && node.tag !== 'tag:yaml.org,2002:str'))) {\n        const msg = 'With stringKeys, all keys must be strings';\n        onError(tag ?? token, 'NON_STRING_KEY', msg);\n    }\n    if (spaceBefore)\n        node.spaceBefore = true;\n    if (comment) {\n        if (token.type === 'scalar' && token.source === '')\n            node.comment = comment;\n        else\n            node.commentBefore = comment;\n    }\n    // @ts-expect-error Type checking misses meaning of isSrcToken\n    if (ctx.options.keepSourceTokens && isSrcToken)\n        node.srcToken = token;\n    return node;\n}\nfunction composeEmptyNode(ctx, offset, before, pos, { spaceBefore, comment, anchor, tag, end }, onError) {\n    const token = {\n        type: 'scalar',\n        offset: utilEmptyScalarPosition.emptyScalarPosition(offset, before, pos),\n        indent: -1,\n        source: ''\n    };\n    const node = composeScalar.composeScalar(ctx, token, tag, onError);\n    if (anchor) {\n        node.anchor = anchor.source.substring(1);\n        if (node.anchor === '')\n            onError(anchor, 'BAD_ALIAS', 'Anchor cannot be an empty string');\n    }\n    if (spaceBefore)\n        node.spaceBefore = true;\n    if (comment) {\n        node.comment = comment;\n        node.range[2] = end;\n    }\n    return node;\n}\nfunction composeAlias({ options }, { offset, source, end }, onError) {\n    const alias = new Alias.Alias(source.substring(1));\n    if (alias.source === '')\n        onError(offset, 'BAD_ALIAS', 'Alias cannot be an empty string');\n    if (alias.source.endsWith(':'))\n        onError(offset + source.length - 1, 'BAD_ALIAS', 'Alias ending in : is ambiguous', true);\n    const valueEnd = offset + source.length;\n    const re = resolveEnd.resolveEnd(end, valueEnd, options.strict, onError);\n    alias.range = [offset, valueEnd, re.offset];\n    if (re.comment)\n        alias.comment = re.comment;\n    return alias;\n}\n\nexports.composeEmptyNode = composeEmptyNode;\nexports.composeNode = composeNode;\n", "'use strict';\n\nvar Document = require('../doc/Document.js');\nvar composeNode = require('./compose-node.js');\nvar resolveEnd = require('./resolve-end.js');\nvar resolveProps = require('./resolve-props.js');\n\nfunction composeDoc(options, directives, { offset, start, value, end }, onError) {\n    const opts = Object.assign({ _directives: directives }, options);\n    const doc = new Document.Document(undefined, opts);\n    const ctx = {\n        atKey: false,\n        atRoot: true,\n        directives: doc.directives,\n        options: doc.options,\n        schema: doc.schema\n    };\n    const props = resolveProps.resolveProps(start, {\n        indicator: 'doc-start',\n        next: value ?? end?.[0],\n        offset,\n        onError,\n        parentIndent: 0,\n        startOnNewline: true\n    });\n    if (props.found) {\n        doc.directives.docStart = true;\n        if (value &&\n            (value.type === 'block-map' || value.type === 'block-seq') &&\n            !props.hasNewline)\n            onError(props.end, 'MISSING_CHAR', 'Block collection cannot start on same line with directives-end marker');\n    }\n    // @ts-expect-error If Contents is set, let's trust the user\n    doc.contents = value\n        ? composeNode.composeNode(ctx, value, props, onError)\n        : composeNode.composeEmptyNode(ctx, props.end, start, null, props, onError);\n    const contentEnd = doc.contents.range[2];\n    const re = resolveEnd.resolveEnd(end, contentEnd, false, onError);\n    if (re.comment)\n        doc.comment = re.comment;\n    doc.range = [offset, contentEnd, re.offset];\n    return doc;\n}\n\nexports.composeDoc = composeDoc;\n", "'use strict';\n\nvar node_process = require('node:process');\nvar directives = require('../doc/directives.js');\nvar Document = require('../doc/Document.js');\nvar errors = require('../errors.js');\nvar identity = require('../nodes/identity.js');\nvar composeDoc = require('./compose-doc.js');\nvar resolveEnd = require('./resolve-end.js');\n\nfunction getErrorPos(src) {\n    if (typeof src === 'number')\n        return [src, src + 1];\n    if (Array.isArray(src))\n        return src.length === 2 ? src : [src[0], src[1]];\n    const { offset, source } = src;\n    return [offset, offset + (typeof source === 'string' ? source.length : 1)];\n}\nfunction parsePrelude(prelude) {\n    let comment = '';\n    let atComment = false;\n    let afterEmptyLine = false;\n    for (let i = 0; i < prelude.length; ++i) {\n        const source = prelude[i];\n        switch (source[0]) {\n            case '#':\n                comment +=\n                    (comment === '' ? '' : afterEmptyLine ? '\\n\\n' : '\\n') +\n                        (source.substring(1) || ' ');\n                atComment = true;\n                afterEmptyLine = false;\n                break;\n            case '%':\n                if (prelude[i + 1]?.[0] !== '#')\n                    i += 1;\n                atComment = false;\n                break;\n            default:\n                // This may be wrong after doc-end, but in that case it doesn't matter\n                if (!atComment)\n                    afterEmptyLine = true;\n                atComment = false;\n        }\n    }\n    return { comment, afterEmptyLine };\n}\n/**\n * Compose a stream of CST nodes into a stream of YAML Documents.\n *\n * ```ts\n * import { Composer, Parser } from 'yaml'\n *\n * const src: string = ...\n * const tokens = new Parser().parse(src)\n * const docs = new Composer().compose(tokens)\n * ```\n */\nclass Composer {\n    constructor(options = {}) {\n        this.doc = null;\n        this.atDirectives = false;\n        this.prelude = [];\n        this.errors = [];\n        this.warnings = [];\n        this.onError = (source, code, message, warning) => {\n            const pos = getErrorPos(source);\n            if (warning)\n                this.warnings.push(new errors.YAMLWarning(pos, code, message));\n            else\n                this.errors.push(new errors.YAMLParseError(pos, code, message));\n        };\n        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n        this.directives = new directives.Directives({ version: options.version || '1.2' });\n        this.options = options;\n    }\n    decorate(doc, afterDoc) {\n        const { comment, afterEmptyLine } = parsePrelude(this.prelude);\n        //console.log({ dc: doc.comment, prelude, comment })\n        if (comment) {\n            const dc = doc.contents;\n            if (afterDoc) {\n                doc.comment = doc.comment ? `${doc.comment}\\n${comment}` : comment;\n            }\n            else if (afterEmptyLine || doc.directives.docStart || !dc) {\n                doc.commentBefore = comment;\n            }\n            else if (identity.isCollection(dc) && !dc.flow && dc.items.length > 0) {\n                let it = dc.items[0];\n                if (identity.isPair(it))\n                    it = it.key;\n                const cb = it.commentBefore;\n                it.commentBefore = cb ? `${comment}\\n${cb}` : comment;\n            }\n            else {\n                const cb = dc.commentBefore;\n                dc.commentBefore = cb ? `${comment}\\n${cb}` : comment;\n            }\n        }\n        if (afterDoc) {\n            Array.prototype.push.apply(doc.errors, this.errors);\n            Array.prototype.push.apply(doc.warnings, this.warnings);\n        }\n        else {\n            doc.errors = this.errors;\n            doc.warnings = this.warnings;\n        }\n        this.prelude = [];\n        this.errors = [];\n        this.warnings = [];\n    }\n    /**\n     * Current stream status information.\n     *\n     * Mostly useful at the end of input for an empty stream.\n     */\n    streamInfo() {\n        return {\n            comment: parsePrelude(this.prelude).comment,\n            directives: this.directives,\n            errors: this.errors,\n            warnings: this.warnings\n        };\n    }\n    /**\n     * Compose tokens into documents.\n     *\n     * @param forceDoc - If the stream contains no document, still emit a final document including any comments and directives that would be applied to a subsequent document.\n     * @param endOffset - Should be set if `forceDoc` is also set, to set the document range end and to indicate errors correctly.\n     */\n    *compose(tokens, forceDoc = false, endOffset = -1) {\n        for (const token of tokens)\n            yield* this.next(token);\n        yield* this.end(forceDoc, endOffset);\n    }\n    /** Advance the composer by one CST token. */\n    *next(token) {\n        if (node_process.env.LOG_STREAM)\n            console.dir(token, { depth: null });\n        switch (token.type) {\n            case 'directive':\n                this.directives.add(token.source, (offset, message, warning) => {\n                    const pos = getErrorPos(token);\n                    pos[0] += offset;\n                    this.onError(pos, 'BAD_DIRECTIVE', message, warning);\n                });\n                this.prelude.push(token.source);\n                this.atDirectives = true;\n                break;\n            case 'document': {\n                const doc = composeDoc.composeDoc(this.options, this.directives, token, this.onError);\n                if (this.atDirectives && !doc.directives.docStart)\n                    this.onError(token, 'MISSING_CHAR', 'Missing directives-end/doc-start indicator line');\n                this.decorate(doc, false);\n                if (this.doc)\n                    yield this.doc;\n                this.doc = doc;\n                this.atDirectives = false;\n                break;\n            }\n            case 'byte-order-mark':\n            case 'space':\n                break;\n            case 'comment':\n            case 'newline':\n                this.prelude.push(token.source);\n                break;\n            case 'error': {\n                const msg = token.source\n                    ? `${token.message}: ${JSON.stringify(token.source)}`\n                    : token.message;\n                const error = new errors.YAMLParseError(getErrorPos(token), 'UNEXPECTED_TOKEN', msg);\n                if (this.atDirectives || !this.doc)\n                    this.errors.push(error);\n                else\n                    this.doc.errors.push(error);\n                break;\n            }\n            case 'doc-end': {\n                if (!this.doc) {\n                    const msg = 'Unexpected doc-end without preceding document';\n                    this.errors.push(new errors.YAMLParseError(getErrorPos(token), 'UNEXPECTED_TOKEN', msg));\n                    break;\n                }\n                this.doc.directives.docEnd = true;\n                const end = resolveEnd.resolveEnd(token.end, token.offset + token.source.length, this.doc.options.strict, this.onError);\n                this.decorate(this.doc, true);\n                if (end.comment) {\n                    const dc = this.doc.comment;\n                    this.doc.comment = dc ? `${dc}\\n${end.comment}` : end.comment;\n                }\n                this.doc.range[2] = end.offset;\n                break;\n            }\n            default:\n                this.errors.push(new errors.YAMLParseError(getErrorPos(token), 'UNEXPECTED_TOKEN', `Unsupported token ${token.type}`));\n        }\n    }\n    /**\n     * Call at end of input to yield any remaining document.\n     *\n     * @param forceDoc - If the stream contains no document, still emit a final document including any comments and directives that would be applied to a subsequent document.\n     * @param endOffset - Should be set if `forceDoc` is also set, to set the document range end and to indicate errors correctly.\n     */\n    *end(forceDoc = false, endOffset = -1) {\n        if (this.doc) {\n            this.decorate(this.doc, true);\n            yield this.doc;\n            this.doc = null;\n        }\n        else if (forceDoc) {\n            const opts = Object.assign({ _directives: this.directives }, this.options);\n            const doc = new Document.Document(undefined, opts);\n            if (this.atDirectives)\n                this.onError(endOffset, 'MISSING_CHAR', 'Missing directives-end indicator line');\n            doc.range = [0, endOffset, endOffset];\n            this.decorate(doc, false);\n            yield doc;\n        }\n    }\n}\n\nexports.Composer = Composer;\n", "'use strict';\n\nvar resolveBlockScalar = require('../compose/resolve-block-scalar.js');\nvar resolveFlowScalar = require('../compose/resolve-flow-scalar.js');\nvar errors = require('../errors.js');\nvar stringifyString = require('../stringify/stringifyString.js');\n\nfunction resolveAsScalar(token, strict = true, onError) {\n    if (token) {\n        const _onError = (pos, code, message) => {\n            const offset = typeof pos === 'number' ? pos : Array.isArray(pos) ? pos[0] : pos.offset;\n            if (onError)\n                onError(offset, code, message);\n            else\n                throw new errors.YAMLParseError([offset, offset + 1], code, message);\n        };\n        switch (token.type) {\n            case 'scalar':\n            case 'single-quoted-scalar':\n            case 'double-quoted-scalar':\n                return resolveFlowScalar.resolveFlowScalar(token, strict, _onError);\n            case 'block-scalar':\n                return resolveBlockScalar.resolveBlockScalar({ options: { strict } }, token, _onError);\n        }\n    }\n    return null;\n}\n/**\n * Create a new scalar token with `value`\n *\n * Values that represent an actual string but may be parsed as a different type should use a `type` other than `'PLAIN'`,\n * as this function does not support any schema operations and won't check for such conflicts.\n *\n * @param value The string representation of the value, which will have its content properly indented.\n * @param context.end Comments and whitespace after the end of the value, or after the block scalar header. If undefined, a newline will be added.\n * @param context.implicitKey Being within an implicit key may affect the resolved type of the token's value.\n * @param context.indent The indent level of the token.\n * @param context.inFlow Is this scalar within a flow collection? This may affect the resolved type of the token's value.\n * @param context.offset The offset position of the token.\n * @param context.type The preferred type of the scalar token. If undefined, the previous type of the `token` will be used, defaulting to `'PLAIN'`.\n */\nfunction createScalarToken(value, context) {\n    const { implicitKey = false, indent, inFlow = false, offset = -1, type = 'PLAIN' } = context;\n    const source = stringifyString.stringifyString({ type, value }, {\n        implicitKey,\n        indent: indent > 0 ? ' '.repeat(indent) : '',\n        inFlow,\n        options: { blockQuote: true, lineWidth: -1 }\n    });\n    const end = context.end ?? [\n        { type: 'newline', offset: -1, indent, source: '\\n' }\n    ];\n    switch (source[0]) {\n        case '|':\n        case '>': {\n            const he = source.indexOf('\\n');\n            const head = source.substring(0, he);\n            const body = source.substring(he + 1) + '\\n';\n            const props = [\n                { type: 'block-scalar-header', offset, indent, source: head }\n            ];\n            if (!addEndtoBlockProps(props, end))\n                props.push({ type: 'newline', offset: -1, indent, source: '\\n' });\n            return { type: 'block-scalar', offset, indent, props, source: body };\n        }\n        case '\"':\n            return { type: 'double-quoted-scalar', offset, indent, source, end };\n        case \"'\":\n            return { type: 'single-quoted-scalar', offset, indent, source, end };\n        default:\n            return { type: 'scalar', offset, indent, source, end };\n    }\n}\n/**\n * Set the value of `token` to the given string `value`, overwriting any previous contents and type that it may have.\n *\n * Best efforts are made to retain any comments previously associated with the `token`,\n * though all contents within a collection's `items` will be overwritten.\n *\n * Values that represent an actual string but may be parsed as a different type should use a `type` other than `'PLAIN'`,\n * as this function does not support any schema operations and won't check for such conflicts.\n *\n * @param token Any token. If it does not include an `indent` value, the value will be stringified as if it were an implicit key.\n * @param value The string representation of the value, which will have its content properly indented.\n * @param context.afterKey In most cases, values after a key should have an additional level of indentation.\n * @param context.implicitKey Being within an implicit key may affect the resolved type of the token's value.\n * @param context.inFlow Being within a flow collection may affect the resolved type of the token's value.\n * @param context.type The preferred type of the scalar token. If undefined, the previous type of the `token` will be used, defaulting to `'PLAIN'`.\n */\nfunction setScalarValue(token, value, context = {}) {\n    let { afterKey = false, implicitKey = false, inFlow = false, type } = context;\n    let indent = 'indent' in token ? token.indent : null;\n    if (afterKey && typeof indent === 'number')\n        indent += 2;\n    if (!type)\n        switch (token.type) {\n            case 'single-quoted-scalar':\n                type = 'QUOTE_SINGLE';\n                break;\n            case 'double-quoted-scalar':\n                type = 'QUOTE_DOUBLE';\n                break;\n            case 'block-scalar': {\n                const header = token.props[0];\n                if (header.type !== 'block-scalar-header')\n                    throw new Error('Invalid block scalar header');\n                type = header.source[0] === '>' ? 'BLOCK_FOLDED' : 'BLOCK_LITERAL';\n                break;\n            }\n            default:\n                type = 'PLAIN';\n        }\n    const source = stringifyString.stringifyString({ type, value }, {\n        implicitKey: implicitKey || indent === null,\n        indent: indent !== null && indent > 0 ? ' '.repeat(indent) : '',\n        inFlow,\n        options: { blockQuote: true, lineWidth: -1 }\n    });\n    switch (source[0]) {\n        case '|':\n        case '>':\n            setBlockScalarValue(token, source);\n            break;\n        case '\"':\n            setFlowScalarValue(token, source, 'double-quoted-scalar');\n            break;\n        case \"'\":\n            setFlowScalarValue(token, source, 'single-quoted-scalar');\n            break;\n        default:\n            setFlowScalarValue(token, source, 'scalar');\n    }\n}\nfunction setBlockScalarValue(token, source) {\n    const he = source.indexOf('\\n');\n    const head = source.substring(0, he);\n    const body = source.substring(he + 1) + '\\n';\n    if (token.type === 'block-scalar') {\n        const header = token.props[0];\n        if (header.type !== 'block-scalar-header')\n            throw new Error('Invalid block scalar header');\n        header.source = head;\n        token.source = body;\n    }\n    else {\n        const { offset } = token;\n        const indent = 'indent' in token ? token.indent : -1;\n        const props = [\n            { type: 'block-scalar-header', offset, indent, source: head }\n        ];\n        if (!addEndtoBlockProps(props, 'end' in token ? token.end : undefined))\n            props.push({ type: 'newline', offset: -1, indent, source: '\\n' });\n        for (const key of Object.keys(token))\n            if (key !== 'type' && key !== 'offset')\n                delete token[key];\n        Object.assign(token, { type: 'block-scalar', indent, props, source: body });\n    }\n}\n/** @returns `true` if last token is a newline */\nfunction addEndtoBlockProps(props, end) {\n    if (end)\n        for (const st of end)\n            switch (st.type) {\n                case 'space':\n                case 'comment':\n                    props.push(st);\n                    break;\n                case 'newline':\n                    props.push(st);\n                    return true;\n            }\n    return false;\n}\nfunction setFlowScalarValue(token, source, type) {\n    switch (token.type) {\n        case 'scalar':\n        case 'double-quoted-scalar':\n        case 'single-quoted-scalar':\n            token.type = type;\n            token.source = source;\n            break;\n        case 'block-scalar': {\n            const end = token.props.slice(1);\n            let oa = source.length;\n            if (token.props[0].type === 'block-scalar-header')\n                oa -= token.props[0].source.length;\n            for (const tok of end)\n                tok.offset += oa;\n            delete token.props;\n            Object.assign(token, { type, source, end });\n            break;\n        }\n        case 'block-map':\n        case 'block-seq': {\n            const offset = token.offset + source.length;\n            const nl = { type: 'newline', offset, indent: token.indent, source: '\\n' };\n            delete token.items;\n            Object.assign(token, { type, source, end: [nl] });\n            break;\n        }\n        default: {\n            const indent = 'indent' in token ? token.indent : -1;\n            const end = 'end' in token && Array.isArray(token.end)\n                ? token.end.filter(st => st.type === 'space' ||\n                    st.type === 'comment' ||\n                    st.type === 'newline')\n                : [];\n            for (const key of Object.keys(token))\n                if (key !== 'type' && key !== 'offset')\n                    delete token[key];\n            Object.assign(token, { type, indent, source, end });\n        }\n    }\n}\n\nexports.createScalarToken = createScalarToken;\nexports.resolveAsScalar = resolveAsScalar;\nexports.setScalarValue = setScalarValue;\n", "'use strict';\n\n/**\n * Stringify a CST document, token, or collection item\n *\n * Fair warning: This applies no validation whatsoever, and\n * simply concatenates the sources in their logical order.\n */\nconst stringify = (cst) => 'type' in cst ? stringifyToken(cst) : stringifyItem(cst);\nfunction stringifyToken(token) {\n    switch (token.type) {\n        case 'block-scalar': {\n            let res = '';\n            for (const tok of token.props)\n                res += stringifyToken(tok);\n            return res + token.source;\n        }\n        case 'block-map':\n        case 'block-seq': {\n            let res = '';\n            for (const item of token.items)\n                res += stringifyItem(item);\n            return res;\n        }\n        case 'flow-collection': {\n            let res = token.start.source;\n            for (const item of token.items)\n                res += stringifyItem(item);\n            for (const st of token.end)\n                res += st.source;\n            return res;\n        }\n        case 'document': {\n            let res = stringifyItem(token);\n            if (token.end)\n                for (const st of token.end)\n                    res += st.source;\n            return res;\n        }\n        default: {\n            let res = token.source;\n            if ('end' in token && token.end)\n                for (const st of token.end)\n                    res += st.source;\n            return res;\n        }\n    }\n}\nfunction stringifyItem({ start, key, sep, value }) {\n    let res = '';\n    for (const st of start)\n        res += st.source;\n    if (key)\n        res += stringifyToken(key);\n    if (sep)\n        for (const st of sep)\n            res += st.source;\n    if (value)\n        res += stringifyToken(value);\n    return res;\n}\n\nexports.stringify = stringify;\n", "'use strict';\n\nconst BREAK = Symbol('break visit');\nconst SKIP = Symbol('skip children');\nconst REMOVE = Symbol('remove item');\n/**\n * Apply a visitor to a CST document or item.\n *\n * Walks through the tree (depth-first) starting from the root, calling a\n * `visitor` function with two arguments when entering each item:\n *   - `item`: The current item, which included the following members:\n *     - `start: SourceToken[]` – Source tokens before the key or value,\n *       possibly including its anchor or tag.\n *     - `key?: Token | null` – Set for pair values. May then be `null`, if\n *       the key before the `:` separator is empty.\n *     - `sep?: SourceToken[]` – Source tokens between the key and the value,\n *       which should include the `:` map value indicator if `value` is set.\n *     - `value?: Token` – The value of a sequence item, or of a map pair.\n *   - `path`: The steps from the root to the current node, as an array of\n *     `['key' | 'value', number]` tuples.\n *\n * The return value of the visitor may be used to control the traversal:\n *   - `undefined` (default): Do nothing and continue\n *   - `visit.SKIP`: Do not visit the children of this token, continue with\n *      next sibling\n *   - `visit.BREAK`: Terminate traversal completely\n *   - `visit.REMOVE`: Remove the current item, then continue with the next one\n *   - `number`: Set the index of the next step. This is useful especially if\n *     the index of the current token has changed.\n *   - `function`: Define the next visitor for this item. After the original\n *     visitor is called on item entry, next visitors are called after handling\n *     a non-empty `key` and when exiting the item.\n */\nfunction visit(cst, visitor) {\n    if ('type' in cst && cst.type === 'document')\n        cst = { start: cst.start, value: cst.value };\n    _visit(Object.freeze([]), cst, visitor);\n}\n// Without the `as symbol` casts, TS declares these in the `visit`\n// namespace using `var`, but then complains about that because\n// `unique symbol` must be `const`.\n/** Terminate visit traversal completely */\nvisit.BREAK = BREAK;\n/** Do not visit the children of the current item */\nvisit.SKIP = SKIP;\n/** Remove the current item */\nvisit.REMOVE = REMOVE;\n/** Find the item at `path` from `cst` as the root */\nvisit.itemAtPath = (cst, path) => {\n    let item = cst;\n    for (const [field, index] of path) {\n        const tok = item?.[field];\n        if (tok && 'items' in tok) {\n            item = tok.items[index];\n        }\n        else\n            return undefined;\n    }\n    return item;\n};\n/**\n * Get the immediate parent collection of the item at `path` from `cst` as the root.\n *\n * Throws an error if the collection is not found, which should never happen if the item itself exists.\n */\nvisit.parentCollection = (cst, path) => {\n    const parent = visit.itemAtPath(cst, path.slice(0, -1));\n    const field = path[path.length - 1][0];\n    const coll = parent?.[field];\n    if (coll && 'items' in coll)\n        return coll;\n    throw new Error('Parent collection not found');\n};\nfunction _visit(path, item, visitor) {\n    let ctrl = visitor(item, path);\n    if (typeof ctrl === 'symbol')\n        return ctrl;\n    for (const field of ['key', 'value']) {\n        const token = item[field];\n        if (token && 'items' in token) {\n            for (let i = 0; i < token.items.length; ++i) {\n                const ci = _visit(Object.freeze(path.concat([[field, i]])), token.items[i], visitor);\n                if (typeof ci === 'number')\n                    i = ci - 1;\n                else if (ci === BREAK)\n                    return BREAK;\n                else if (ci === REMOVE) {\n                    token.items.splice(i, 1);\n                    i -= 1;\n                }\n            }\n            if (typeof ctrl === 'function' && field === 'key')\n                ctrl = ctrl(item, path);\n        }\n    }\n    return typeof ctrl === 'function' ? ctrl(item, path) : ctrl;\n}\n\nexports.visit = visit;\n", "'use strict';\n\nvar cstScalar = require('./cst-scalar.js');\nvar cstStringify = require('./cst-stringify.js');\nvar cstVisit = require('./cst-visit.js');\n\n/** The byte order mark */\nconst BOM = '\\u{FEFF}';\n/** Start of doc-mode */\nconst DOCUMENT = '\\x02'; // C0: Start of Text\n/** Unexpected end of flow-mode */\nconst FLOW_END = '\\x18'; // C0: Cancel\n/** Next token is a scalar value */\nconst SCALAR = '\\x1f'; // C0: Unit Separator\n/** @returns `true` if `token` is a flow or block collection */\nconst isCollection = (token) => !!token && 'items' in token;\n/** @returns `true` if `token` is a flow or block scalar; not an alias */\nconst isScalar = (token) => !!token &&\n    (token.type === 'scalar' ||\n        token.type === 'single-quoted-scalar' ||\n        token.type === 'double-quoted-scalar' ||\n        token.type === 'block-scalar');\n/* istanbul ignore next */\n/** Get a printable representation of a lexer token */\nfunction prettyToken(token) {\n    switch (token) {\n        case BOM:\n            return '<BOM>';\n        case DOCUMENT:\n            return '<DOC>';\n        case FLOW_END:\n            return '<FLOW_END>';\n        case SCALAR:\n            return '<SCALAR>';\n        default:\n            return JSON.stringify(token);\n    }\n}\n/** Identify the type of a lexer token. May return `null` for unknown tokens. */\nfunction tokenType(source) {\n    switch (source) {\n        case BOM:\n            return 'byte-order-mark';\n        case DOCUMENT:\n            return 'doc-mode';\n        case FLOW_END:\n            return 'flow-error-end';\n        case SCALAR:\n            return 'scalar';\n        case '---':\n            return 'doc-start';\n        case '...':\n            return 'doc-end';\n        case '':\n        case '\\n':\n        case '\\r\\n':\n            return 'newline';\n        case '-':\n            return 'seq-item-ind';\n        case '?':\n            return 'explicit-key-ind';\n        case ':':\n            return 'map-value-ind';\n        case '{':\n            return 'flow-map-start';\n        case '}':\n            return 'flow-map-end';\n        case '[':\n            return 'flow-seq-start';\n        case ']':\n            return 'flow-seq-end';\n        case ',':\n            return 'comma';\n    }\n    switch (source[0]) {\n        case ' ':\n        case '\\t':\n            return 'space';\n        case '#':\n            return 'comment';\n        case '%':\n            return 'directive-line';\n        case '*':\n            return 'alias';\n        case '&':\n            return 'anchor';\n        case '!':\n            return 'tag';\n        case \"'\":\n            return 'single-quoted-scalar';\n        case '\"':\n            return 'double-quoted-scalar';\n        case '|':\n        case '>':\n            return 'block-scalar-header';\n    }\n    return null;\n}\n\nexports.createScalarToken = cstScalar.createScalarToken;\nexports.resolveAsScalar = cstScalar.resolveAsScalar;\nexports.setScalarValue = cstScalar.setScalarValue;\nexports.stringify = cstStringify.stringify;\nexports.visit = cstVisit.visit;\nexports.BOM = BOM;\nexports.DOCUMENT = DOCUMENT;\nexports.FLOW_END = FLOW_END;\nexports.SCALAR = SCALAR;\nexports.isCollection = isCollection;\nexports.isScalar = isScalar;\nexports.prettyToken = prettyToken;\nexports.tokenType = tokenType;\n", "'use strict';\n\nvar cst = require('./cst.js');\n\n/*\nSTART -> stream\n\nstream\n  directive -> line-end -> stream\n  indent + line-end -> stream\n  [else] -> line-start\n\nline-end\n  comment -> line-end\n  newline -> .\n  input-end -> END\n\nline-start\n  doc-start -> doc\n  doc-end -> stream\n  [else] -> indent -> block-start\n\nblock-start\n  seq-item-start -> block-start\n  explicit-key-start -> block-start\n  map-value-start -> block-start\n  [else] -> doc\n\ndoc\n  line-end -> line-start\n  spaces -> doc\n  anchor -> doc\n  tag -> doc\n  flow-start -> flow -> doc\n  flow-end -> error -> doc\n  seq-item-start -> error -> doc\n  explicit-key-start -> error -> doc\n  map-value-start -> doc\n  alias -> doc\n  quote-start -> quoted-scalar -> doc\n  block-scalar-header -> line-end -> block-scalar(min) -> line-start\n  [else] -> plain-scalar(false, min) -> doc\n\nflow\n  line-end -> flow\n  spaces -> flow\n  anchor -> flow\n  tag -> flow\n  flow-start -> flow -> flow\n  flow-end -> .\n  seq-item-start -> error -> flow\n  explicit-key-start -> flow\n  map-value-start -> flow\n  alias -> flow\n  quote-start -> quoted-scalar -> flow\n  comma -> flow\n  [else] -> plain-scalar(true, 0) -> flow\n\nquoted-scalar\n  quote-end -> .\n  [else] -> quoted-scalar\n\nblock-scalar(min)\n  newline + peek(indent < min) -> .\n  [else] -> block-scalar(min)\n\nplain-scalar(is-flow, min)\n  scalar-end(is-flow) -> .\n  peek(newline + (indent < min)) -> .\n  [else] -> plain-scalar(min)\n*/\nfunction isEmpty(ch) {\n    switch (ch) {\n        case undefined:\n        case ' ':\n        case '\\n':\n        case '\\r':\n        case '\\t':\n            return true;\n        default:\n            return false;\n    }\n}\nconst hexDigits = new Set('0123456789ABCDEFabcdef');\nconst tagChars = new Set(\"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-#;/?:@&=+$_.!~*'()\");\nconst flowIndicatorChars = new Set(',[]{}');\nconst invalidAnchorChars = new Set(' ,[]{}\\n\\r\\t');\nconst isNotAnchorChar = (ch) => !ch || invalidAnchorChars.has(ch);\n/**\n * Splits an input string into lexical tokens, i.e. smaller strings that are\n * easily identifiable by `tokens.tokenType()`.\n *\n * Lexing starts always in a \"stream\" context. Incomplete input may be buffered\n * until a complete token can be emitted.\n *\n * In addition to slices of the original input, the following control characters\n * may also be emitted:\n *\n * - `\\x02` (Start of Text): A document starts with the next token\n * - `\\x18` (Cancel): Unexpected end of flow-mode (indicates an error)\n * - `\\x1f` (Unit Separator): Next token is a scalar value\n * - `\\u{FEFF}` (Byte order mark): Emitted separately outside documents\n */\nclass Lexer {\n    constructor() {\n        /**\n         * Flag indicating whether the end of the current buffer marks the end of\n         * all input\n         */\n        this.atEnd = false;\n        /**\n         * Explicit indent set in block scalar header, as an offset from the current\n         * minimum indent, so e.g. set to 1 from a header `|2+`. Set to -1 if not\n         * explicitly set.\n         */\n        this.blockScalarIndent = -1;\n        /**\n         * Block scalars that include a + (keep) chomping indicator in their header\n         * include trailing empty lines, which are otherwise excluded from the\n         * scalar's contents.\n         */\n        this.blockScalarKeep = false;\n        /** Current input */\n        this.buffer = '';\n        /**\n         * Flag noting whether the map value indicator : can immediately follow this\n         * node within a flow context.\n         */\n        this.flowKey = false;\n        /** Count of surrounding flow collection levels. */\n        this.flowLevel = 0;\n        /**\n         * Minimum level of indentation required for next lines to be parsed as a\n         * part of the current scalar value.\n         */\n        this.indentNext = 0;\n        /** Indentation level of the current line. */\n        this.indentValue = 0;\n        /** Position of the next \\n character. */\n        this.lineEndPos = null;\n        /** Stores the state of the lexer if reaching the end of incpomplete input */\n        this.next = null;\n        /** A pointer to `buffer`; the current position of the lexer. */\n        this.pos = 0;\n    }\n    /**\n     * Generate YAML tokens from the `source` string. If `incomplete`,\n     * a part of the last line may be left as a buffer for the next call.\n     *\n     * @returns A generator of lexical tokens\n     */\n    *lex(source, incomplete = false) {\n        if (source) {\n            if (typeof source !== 'string')\n                throw TypeError('source is not a string');\n            this.buffer = this.buffer ? this.buffer + source : source;\n            this.lineEndPos = null;\n        }\n        this.atEnd = !incomplete;\n        let next = this.next ?? 'stream';\n        while (next && (incomplete || this.hasChars(1)))\n            next = yield* this.parseNext(next);\n    }\n    atLineEnd() {\n        let i = this.pos;\n        let ch = this.buffer[i];\n        while (ch === ' ' || ch === '\\t')\n            ch = this.buffer[++i];\n        if (!ch || ch === '#' || ch === '\\n')\n            return true;\n        if (ch === '\\r')\n            return this.buffer[i + 1] === '\\n';\n        return false;\n    }\n    charAt(n) {\n        return this.buffer[this.pos + n];\n    }\n    continueScalar(offset) {\n        let ch = this.buffer[offset];\n        if (this.indentNext > 0) {\n            let indent = 0;\n            while (ch === ' ')\n                ch = this.buffer[++indent + offset];\n            if (ch === '\\r') {\n                const next = this.buffer[indent + offset + 1];\n                if (next === '\\n' || (!next && !this.atEnd))\n                    return offset + indent + 1;\n            }\n            return ch === '\\n' || indent >= this.indentNext || (!ch && !this.atEnd)\n                ? offset + indent\n                : -1;\n        }\n        if (ch === '-' || ch === '.') {\n            const dt = this.buffer.substr(offset, 3);\n            if ((dt === '---' || dt === '...') && isEmpty(this.buffer[offset + 3]))\n                return -1;\n        }\n        return offset;\n    }\n    getLine() {\n        let end = this.lineEndPos;\n        if (typeof end !== 'number' || (end !== -1 && end < this.pos)) {\n            end = this.buffer.indexOf('\\n', this.pos);\n            this.lineEndPos = end;\n        }\n        if (end === -1)\n            return this.atEnd ? this.buffer.substring(this.pos) : null;\n        if (this.buffer[end - 1] === '\\r')\n            end -= 1;\n        return this.buffer.substring(this.pos, end);\n    }\n    hasChars(n) {\n        return this.pos + n <= this.buffer.length;\n    }\n    setNext(state) {\n        this.buffer = this.buffer.substring(this.pos);\n        this.pos = 0;\n        this.lineEndPos = null;\n        this.next = state;\n        return null;\n    }\n    peek(n) {\n        return this.buffer.substr(this.pos, n);\n    }\n    *parseNext(next) {\n        switch (next) {\n            case 'stream':\n                return yield* this.parseStream();\n            case 'line-start':\n                return yield* this.parseLineStart();\n            case 'block-start':\n                return yield* this.parseBlockStart();\n            case 'doc':\n                return yield* this.parseDocument();\n            case 'flow':\n                return yield* this.parseFlowCollection();\n            case 'quoted-scalar':\n                return yield* this.parseQuotedScalar();\n            case 'block-scalar':\n                return yield* this.parseBlockScalar();\n            case 'plain-scalar':\n                return yield* this.parsePlainScalar();\n        }\n    }\n    *parseStream() {\n        let line = this.getLine();\n        if (line === null)\n            return this.setNext('stream');\n        if (line[0] === cst.BOM) {\n            yield* this.pushCount(1);\n            line = line.substring(1);\n        }\n        if (line[0] === '%') {\n            let dirEnd = line.length;\n            let cs = line.indexOf('#');\n            while (cs !== -1) {\n                const ch = line[cs - 1];\n                if (ch === ' ' || ch === '\\t') {\n                    dirEnd = cs - 1;\n                    break;\n                }\n                else {\n                    cs = line.indexOf('#', cs + 1);\n                }\n            }\n            while (true) {\n                const ch = line[dirEnd - 1];\n                if (ch === ' ' || ch === '\\t')\n                    dirEnd -= 1;\n                else\n                    break;\n            }\n            const n = (yield* this.pushCount(dirEnd)) + (yield* this.pushSpaces(true));\n            yield* this.pushCount(line.length - n); // possible comment\n            this.pushNewline();\n            return 'stream';\n        }\n        if (this.atLineEnd()) {\n            const sp = yield* this.pushSpaces(true);\n            yield* this.pushCount(line.length - sp);\n            yield* this.pushNewline();\n            return 'stream';\n        }\n        yield cst.DOCUMENT;\n        return yield* this.parseLineStart();\n    }\n    *parseLineStart() {\n        const ch = this.charAt(0);\n        if (!ch && !this.atEnd)\n            return this.setNext('line-start');\n        if (ch === '-' || ch === '.') {\n            if (!this.atEnd && !this.hasChars(4))\n                return this.setNext('line-start');\n            const s = this.peek(3);\n            if ((s === '---' || s === '...') && isEmpty(this.charAt(3))) {\n                yield* this.pushCount(3);\n                this.indentValue = 0;\n                this.indentNext = 0;\n                return s === '---' ? 'doc' : 'stream';\n            }\n        }\n        this.indentValue = yield* this.pushSpaces(false);\n        if (this.indentNext > this.indentValue && !isEmpty(this.charAt(1)))\n            this.indentNext = this.indentValue;\n        return yield* this.parseBlockStart();\n    }\n    *parseBlockStart() {\n        const [ch0, ch1] = this.peek(2);\n        if (!ch1 && !this.atEnd)\n            return this.setNext('block-start');\n        if ((ch0 === '-' || ch0 === '?' || ch0 === ':') && isEmpty(ch1)) {\n            const n = (yield* this.pushCount(1)) + (yield* this.pushSpaces(true));\n            this.indentNext = this.indentValue + 1;\n            this.indentValue += n;\n            return yield* this.parseBlockStart();\n        }\n        return 'doc';\n    }\n    *parseDocument() {\n        yield* this.pushSpaces(true);\n        const line = this.getLine();\n        if (line === null)\n            return this.setNext('doc');\n        let n = yield* this.pushIndicators();\n        switch (line[n]) {\n            case '#':\n                yield* this.pushCount(line.length - n);\n            // fallthrough\n            case undefined:\n                yield* this.pushNewline();\n                return yield* this.parseLineStart();\n            case '{':\n            case '[':\n                yield* this.pushCount(1);\n                this.flowKey = false;\n                this.flowLevel = 1;\n                return 'flow';\n            case '}':\n            case ']':\n                // this is an error\n                yield* this.pushCount(1);\n                return 'doc';\n            case '*':\n                yield* this.pushUntil(isNotAnchorChar);\n                return 'doc';\n            case '\"':\n            case \"'\":\n                return yield* this.parseQuotedScalar();\n            case '|':\n            case '>':\n                n += yield* this.parseBlockScalarHeader();\n                n += yield* this.pushSpaces(true);\n                yield* this.pushCount(line.length - n);\n                yield* this.pushNewline();\n                return yield* this.parseBlockScalar();\n            default:\n                return yield* this.parsePlainScalar();\n        }\n    }\n    *parseFlowCollection() {\n        let nl, sp;\n        let indent = -1;\n        do {\n            nl = yield* this.pushNewline();\n            if (nl > 0) {\n                sp = yield* this.pushSpaces(false);\n                this.indentValue = indent = sp;\n            }\n            else {\n                sp = 0;\n            }\n            sp += yield* this.pushSpaces(true);\n        } while (nl + sp > 0);\n        const line = this.getLine();\n        if (line === null)\n            return this.setNext('flow');\n        if ((indent !== -1 && indent < this.indentNext && line[0] !== '#') ||\n            (indent === 0 &&\n                (line.startsWith('---') || line.startsWith('...')) &&\n                isEmpty(line[3]))) {\n            // Allowing for the terminal ] or } at the same (rather than greater)\n            // indent level as the initial [ or { is technically invalid, but\n            // failing here would be surprising to users.\n            const atFlowEndMarker = indent === this.indentNext - 1 &&\n                this.flowLevel === 1 &&\n                (line[0] === ']' || line[0] === '}');\n            if (!atFlowEndMarker) {\n                // this is an error\n                this.flowLevel = 0;\n                yield cst.FLOW_END;\n                return yield* this.parseLineStart();\n            }\n        }\n        let n = 0;\n        while (line[n] === ',') {\n            n += yield* this.pushCount(1);\n            n += yield* this.pushSpaces(true);\n            this.flowKey = false;\n        }\n        n += yield* this.pushIndicators();\n        switch (line[n]) {\n            case undefined:\n                return 'flow';\n            case '#':\n                yield* this.pushCount(line.length - n);\n                return 'flow';\n            case '{':\n            case '[':\n                yield* this.pushCount(1);\n                this.flowKey = false;\n                this.flowLevel += 1;\n                return 'flow';\n            case '}':\n            case ']':\n                yield* this.pushCount(1);\n                this.flowKey = true;\n                this.flowLevel -= 1;\n                return this.flowLevel ? 'flow' : 'doc';\n            case '*':\n                yield* this.pushUntil(isNotAnchorChar);\n                return 'flow';\n            case '\"':\n            case \"'\":\n                this.flowKey = true;\n                return yield* this.parseQuotedScalar();\n            case ':': {\n                const next = this.charAt(1);\n                if (this.flowKey || isEmpty(next) || next === ',') {\n                    this.flowKey = false;\n                    yield* this.pushCount(1);\n                    yield* this.pushSpaces(true);\n                    return 'flow';\n                }\n            }\n            // fallthrough\n            default:\n                this.flowKey = false;\n                return yield* this.parsePlainScalar();\n        }\n    }\n    *parseQuotedScalar() {\n        const quote = this.charAt(0);\n        let end = this.buffer.indexOf(quote, this.pos + 1);\n        if (quote === \"'\") {\n            while (end !== -1 && this.buffer[end + 1] === \"'\")\n                end = this.buffer.indexOf(\"'\", end + 2);\n        }\n        else {\n            // double-quote\n            while (end !== -1) {\n                let n = 0;\n                while (this.buffer[end - 1 - n] === '\\\\')\n                    n += 1;\n                if (n % 2 === 0)\n                    break;\n                end = this.buffer.indexOf('\"', end + 1);\n            }\n        }\n        // Only looking for newlines within the quotes\n        const qb = this.buffer.substring(0, end);\n        let nl = qb.indexOf('\\n', this.pos);\n        if (nl !== -1) {\n            while (nl !== -1) {\n                const cs = this.continueScalar(nl + 1);\n                if (cs === -1)\n                    break;\n                nl = qb.indexOf('\\n', cs);\n            }\n            if (nl !== -1) {\n                // this is an error caused by an unexpected unindent\n                end = nl - (qb[nl - 1] === '\\r' ? 2 : 1);\n            }\n        }\n        if (end === -1) {\n            if (!this.atEnd)\n                return this.setNext('quoted-scalar');\n            end = this.buffer.length;\n        }\n        yield* this.pushToIndex(end + 1, false);\n        return this.flowLevel ? 'flow' : 'doc';\n    }\n    *parseBlockScalarHeader() {\n        this.blockScalarIndent = -1;\n        this.blockScalarKeep = false;\n        let i = this.pos;\n        while (true) {\n            const ch = this.buffer[++i];\n            if (ch === '+')\n                this.blockScalarKeep = true;\n            else if (ch > '0' && ch <= '9')\n                this.blockScalarIndent = Number(ch) - 1;\n            else if (ch !== '-')\n                break;\n        }\n        return yield* this.pushUntil(ch => isEmpty(ch) || ch === '#');\n    }\n    *parseBlockScalar() {\n        let nl = this.pos - 1; // may be -1 if this.pos === 0\n        let indent = 0;\n        let ch;\n        loop: for (let i = this.pos; (ch = this.buffer[i]); ++i) {\n            switch (ch) {\n                case ' ':\n                    indent += 1;\n                    break;\n                case '\\n':\n                    nl = i;\n                    indent = 0;\n                    break;\n                case '\\r': {\n                    const next = this.buffer[i + 1];\n                    if (!next && !this.atEnd)\n                        return this.setNext('block-scalar');\n                    if (next === '\\n')\n                        break;\n                } // fallthrough\n                default:\n                    break loop;\n            }\n        }\n        if (!ch && !this.atEnd)\n            return this.setNext('block-scalar');\n        if (indent >= this.indentNext) {\n            if (this.blockScalarIndent === -1)\n                this.indentNext = indent;\n            else {\n                this.indentNext =\n                    this.blockScalarIndent + (this.indentNext === 0 ? 1 : this.indentNext);\n            }\n            do {\n                const cs = this.continueScalar(nl + 1);\n                if (cs === -1)\n                    break;\n                nl = this.buffer.indexOf('\\n', cs);\n            } while (nl !== -1);\n            if (nl === -1) {\n                if (!this.atEnd)\n                    return this.setNext('block-scalar');\n                nl = this.buffer.length;\n            }\n        }\n        // Trailing insufficiently indented tabs are invalid.\n        // To catch that during parsing, we include them in the block scalar value.\n        let i = nl + 1;\n        ch = this.buffer[i];\n        while (ch === ' ')\n            ch = this.buffer[++i];\n        if (ch === '\\t') {\n            while (ch === '\\t' || ch === ' ' || ch === '\\r' || ch === '\\n')\n                ch = this.buffer[++i];\n            nl = i - 1;\n        }\n        else if (!this.blockScalarKeep) {\n            do {\n                let i = nl - 1;\n                let ch = this.buffer[i];\n                if (ch === '\\r')\n                    ch = this.buffer[--i];\n                const lastChar = i; // Drop the line if last char not more indented\n                while (ch === ' ')\n                    ch = this.buffer[--i];\n                if (ch === '\\n' && i >= this.pos && i + 1 + indent > lastChar)\n                    nl = i;\n                else\n                    break;\n            } while (true);\n        }\n        yield cst.SCALAR;\n        yield* this.pushToIndex(nl + 1, true);\n        return yield* this.parseLineStart();\n    }\n    *parsePlainScalar() {\n        const inFlow = this.flowLevel > 0;\n        let end = this.pos - 1;\n        let i = this.pos - 1;\n        let ch;\n        while ((ch = this.buffer[++i])) {\n            if (ch === ':') {\n                const next = this.buffer[i + 1];\n                if (isEmpty(next) || (inFlow && flowIndicatorChars.has(next)))\n                    break;\n                end = i;\n            }\n            else if (isEmpty(ch)) {\n                let next = this.buffer[i + 1];\n                if (ch === '\\r') {\n                    if (next === '\\n') {\n                        i += 1;\n                        ch = '\\n';\n                        next = this.buffer[i + 1];\n                    }\n                    else\n                        end = i;\n                }\n                if (next === '#' || (inFlow && flowIndicatorChars.has(next)))\n                    break;\n                if (ch === '\\n') {\n                    const cs = this.continueScalar(i + 1);\n                    if (cs === -1)\n                        break;\n                    i = Math.max(i, cs - 2); // to advance, but still account for ' #'\n                }\n            }\n            else {\n                if (inFlow && flowIndicatorChars.has(ch))\n                    break;\n                end = i;\n            }\n        }\n        if (!ch && !this.atEnd)\n            return this.setNext('plain-scalar');\n        yield cst.SCALAR;\n        yield* this.pushToIndex(end + 1, true);\n        return inFlow ? 'flow' : 'doc';\n    }\n    *pushCount(n) {\n        if (n > 0) {\n            yield this.buffer.substr(this.pos, n);\n            this.pos += n;\n            return n;\n        }\n        return 0;\n    }\n    *pushToIndex(i, allowEmpty) {\n        const s = this.buffer.slice(this.pos, i);\n        if (s) {\n            yield s;\n            this.pos += s.length;\n            return s.length;\n        }\n        else if (allowEmpty)\n            yield '';\n        return 0;\n    }\n    *pushIndicators() {\n        switch (this.charAt(0)) {\n            case '!':\n                return ((yield* this.pushTag()) +\n                    (yield* this.pushSpaces(true)) +\n                    (yield* this.pushIndicators()));\n            case '&':\n                return ((yield* this.pushUntil(isNotAnchorChar)) +\n                    (yield* this.pushSpaces(true)) +\n                    (yield* this.pushIndicators()));\n            case '-': // this is an error\n            case '?': // this is an error outside flow collections\n            case ':': {\n                const inFlow = this.flowLevel > 0;\n                const ch1 = this.charAt(1);\n                if (isEmpty(ch1) || (inFlow && flowIndicatorChars.has(ch1))) {\n                    if (!inFlow)\n                        this.indentNext = this.indentValue + 1;\n                    else if (this.flowKey)\n                        this.flowKey = false;\n                    return ((yield* this.pushCount(1)) +\n                        (yield* this.pushSpaces(true)) +\n                        (yield* this.pushIndicators()));\n                }\n            }\n        }\n        return 0;\n    }\n    *pushTag() {\n        if (this.charAt(1) === '<') {\n            let i = this.pos + 2;\n            let ch = this.buffer[i];\n            while (!isEmpty(ch) && ch !== '>')\n                ch = this.buffer[++i];\n            return yield* this.pushToIndex(ch === '>' ? i + 1 : i, false);\n        }\n        else {\n            let i = this.pos + 1;\n            let ch = this.buffer[i];\n            while (ch) {\n                if (tagChars.has(ch))\n                    ch = this.buffer[++i];\n                else if (ch === '%' &&\n                    hexDigits.has(this.buffer[i + 1]) &&\n                    hexDigits.has(this.buffer[i + 2])) {\n                    ch = this.buffer[(i += 3)];\n                }\n                else\n                    break;\n            }\n            return yield* this.pushToIndex(i, false);\n        }\n    }\n    *pushNewline() {\n        const ch = this.buffer[this.pos];\n        if (ch === '\\n')\n            return yield* this.pushCount(1);\n        else if (ch === '\\r' && this.charAt(1) === '\\n')\n            return yield* this.pushCount(2);\n        else\n            return 0;\n    }\n    *pushSpaces(allowTabs) {\n        let i = this.pos - 1;\n        let ch;\n        do {\n            ch = this.buffer[++i];\n        } while (ch === ' ' || (allowTabs && ch === '\\t'));\n        const n = i - this.pos;\n        if (n > 0) {\n            yield this.buffer.substr(this.pos, n);\n            this.pos = i;\n        }\n        return n;\n    }\n    *pushUntil(test) {\n        let i = this.pos;\n        let ch = this.buffer[i];\n        while (!test(ch))\n            ch = this.buffer[++i];\n        return yield* this.pushToIndex(i, false);\n    }\n}\n\nexports.Lexer = Lexer;\n", "'use strict';\n\n/**\n * Tracks newlines during parsing in order to provide an efficient API for\n * determining the one-indexed `{ line, col }` position for any offset\n * within the input.\n */\nclass LineCounter {\n    constructor() {\n        this.lineStarts = [];\n        /**\n         * Should be called in ascending order. Otherwise, call\n         * `lineCounter.lineStarts.sort()` before calling `linePos()`.\n         */\n        this.addNewLine = (offset) => this.lineStarts.push(offset);\n        /**\n         * Performs a binary search and returns the 1-indexed { line, col }\n         * position of `offset`. If `line === 0`, `addNewLine` has never been\n         * called or `offset` is before the first known newline.\n         */\n        this.linePos = (offset) => {\n            let low = 0;\n            let high = this.lineStarts.length;\n            while (low < high) {\n                const mid = (low + high) >> 1; // Math.floor((low + high) / 2)\n                if (this.lineStarts[mid] < offset)\n                    low = mid + 1;\n                else\n                    high = mid;\n            }\n            if (this.lineStarts[low] === offset)\n                return { line: low + 1, col: 1 };\n            if (low === 0)\n                return { line: 0, col: offset };\n            const start = this.lineStarts[low - 1];\n            return { line: low, col: offset - start + 1 };\n        };\n    }\n}\n\nexports.LineCounter = LineCounter;\n", "'use strict';\n\nvar node_process = require('node:process');\nvar cst = require('./cst.js');\nvar lexer = require('./lexer.js');\n\nfunction includesToken(list, type) {\n    for (let i = 0; i < list.length; ++i)\n        if (list[i].type === type)\n            return true;\n    return false;\n}\nfunction findNonEmptyIndex(list) {\n    for (let i = 0; i < list.length; ++i) {\n        switch (list[i].type) {\n            case 'space':\n            case 'comment':\n            case 'newline':\n                break;\n            default:\n                return i;\n        }\n    }\n    return -1;\n}\nfunction isFlowToken(token) {\n    switch (token?.type) {\n        case 'alias':\n        case 'scalar':\n        case 'single-quoted-scalar':\n        case 'double-quoted-scalar':\n        case 'flow-collection':\n            return true;\n        default:\n            return false;\n    }\n}\nfunction getPrevProps(parent) {\n    switch (parent.type) {\n        case 'document':\n            return parent.start;\n        case 'block-map': {\n            const it = parent.items[parent.items.length - 1];\n            return it.sep ?? it.start;\n        }\n        case 'block-seq':\n            return parent.items[parent.items.length - 1].start;\n        /* istanbul ignore next should not happen */\n        default:\n            return [];\n    }\n}\n/** Note: May modify input array */\nfunction getFirstKeyStartProps(prev) {\n    if (prev.length === 0)\n        return [];\n    let i = prev.length;\n    loop: while (--i >= 0) {\n        switch (prev[i].type) {\n            case 'doc-start':\n            case 'explicit-key-ind':\n            case 'map-value-ind':\n            case 'seq-item-ind':\n            case 'newline':\n                break loop;\n        }\n    }\n    while (prev[++i]?.type === 'space') {\n        /* loop */\n    }\n    return prev.splice(i, prev.length);\n}\nfunction fixFlowSeqItems(fc) {\n    if (fc.start.type === 'flow-seq-start') {\n        for (const it of fc.items) {\n            if (it.sep &&\n                !it.value &&\n                !includesToken(it.start, 'explicit-key-ind') &&\n                !includesToken(it.sep, 'map-value-ind')) {\n                if (it.key)\n                    it.value = it.key;\n                delete it.key;\n                if (isFlowToken(it.value)) {\n                    if (it.value.end)\n                        Array.prototype.push.apply(it.value.end, it.sep);\n                    else\n                        it.value.end = it.sep;\n                }\n                else\n                    Array.prototype.push.apply(it.start, it.sep);\n                delete it.sep;\n            }\n        }\n    }\n}\n/**\n * A YAML concrete syntax tree (CST) parser\n *\n * ```ts\n * const src: string = ...\n * for (const token of new Parser().parse(src)) {\n *   // token: Token\n * }\n * ```\n *\n * To use the parser with a user-provided lexer:\n *\n * ```ts\n * function* parse(source: string, lexer: Lexer) {\n *   const parser = new Parser()\n *   for (const lexeme of lexer.lex(source))\n *     yield* parser.next(lexeme)\n *   yield* parser.end()\n * }\n *\n * const src: string = ...\n * const lexer = new Lexer()\n * for (const token of parse(src, lexer)) {\n *   // token: Token\n * }\n * ```\n */\nclass Parser {\n    /**\n     * @param onNewLine - If defined, called separately with the start position of\n     *   each new line (in `parse()`, including the start of input).\n     */\n    constructor(onNewLine) {\n        /** If true, space and sequence indicators count as indentation */\n        this.atNewLine = true;\n        /** If true, next token is a scalar value */\n        this.atScalar = false;\n        /** Current indentation level */\n        this.indent = 0;\n        /** Current offset since the start of parsing */\n        this.offset = 0;\n        /** On the same line with a block map key */\n        this.onKeyLine = false;\n        /** Top indicates the node that's currently being built */\n        this.stack = [];\n        /** The source of the current token, set in parse() */\n        this.source = '';\n        /** The type of the current token, set in parse() */\n        this.type = '';\n        // Must be defined after `next()`\n        this.lexer = new lexer.Lexer();\n        this.onNewLine = onNewLine;\n    }\n    /**\n     * Parse `source` as a YAML stream.\n     * If `incomplete`, a part of the last line may be left as a buffer for the next call.\n     *\n     * Errors are not thrown, but yielded as `{ type: 'error', message }` tokens.\n     *\n     * @returns A generator of tokens representing each directive, document, and other structure.\n     */\n    *parse(source, incomplete = false) {\n        if (this.onNewLine && this.offset === 0)\n            this.onNewLine(0);\n        for (const lexeme of this.lexer.lex(source, incomplete))\n            yield* this.next(lexeme);\n        if (!incomplete)\n            yield* this.end();\n    }\n    /**\n     * Advance the parser by the `source` of one lexical token.\n     */\n    *next(source) {\n        this.source = source;\n        if (node_process.env.LOG_TOKENS)\n            console.log('|', cst.prettyToken(source));\n        if (this.atScalar) {\n            this.atScalar = false;\n            yield* this.step();\n            this.offset += source.length;\n            return;\n        }\n        const type = cst.tokenType(source);\n        if (!type) {\n            const message = `Not a YAML token: ${source}`;\n            yield* this.pop({ type: 'error', offset: this.offset, message, source });\n            this.offset += source.length;\n        }\n        else if (type === 'scalar') {\n            this.atNewLine = false;\n            this.atScalar = true;\n            this.type = 'scalar';\n        }\n        else {\n            this.type = type;\n            yield* this.step();\n            switch (type) {\n                case 'newline':\n                    this.atNewLine = true;\n                    this.indent = 0;\n                    if (this.onNewLine)\n                        this.onNewLine(this.offset + source.length);\n                    break;\n                case 'space':\n                    if (this.atNewLine && source[0] === ' ')\n                        this.indent += source.length;\n                    break;\n                case 'explicit-key-ind':\n                case 'map-value-ind':\n                case 'seq-item-ind':\n                    if (this.atNewLine)\n                        this.indent += source.length;\n                    break;\n                case 'doc-mode':\n                case 'flow-error-end':\n                    return;\n                default:\n                    this.atNewLine = false;\n            }\n            this.offset += source.length;\n        }\n    }\n    /** Call at end of input to push out any remaining constructions */\n    *end() {\n        while (this.stack.length > 0)\n            yield* this.pop();\n    }\n    get sourceToken() {\n        const st = {\n            type: this.type,\n            offset: this.offset,\n            indent: this.indent,\n            source: this.source\n        };\n        return st;\n    }\n    *step() {\n        const top = this.peek(1);\n        if (this.type === 'doc-end' && (!top || top.type !== 'doc-end')) {\n            while (this.stack.length > 0)\n                yield* this.pop();\n            this.stack.push({\n                type: 'doc-end',\n                offset: this.offset,\n                source: this.source\n            });\n            return;\n        }\n        if (!top)\n            return yield* this.stream();\n        switch (top.type) {\n            case 'document':\n                return yield* this.document(top);\n            case 'alias':\n            case 'scalar':\n            case 'single-quoted-scalar':\n            case 'double-quoted-scalar':\n                return yield* this.scalar(top);\n            case 'block-scalar':\n                return yield* this.blockScalar(top);\n            case 'block-map':\n                return yield* this.blockMap(top);\n            case 'block-seq':\n                return yield* this.blockSequence(top);\n            case 'flow-collection':\n                return yield* this.flowCollection(top);\n            case 'doc-end':\n                return yield* this.documentEnd(top);\n        }\n        /* istanbul ignore next should not happen */\n        yield* this.pop();\n    }\n    peek(n) {\n        return this.stack[this.stack.length - n];\n    }\n    *pop(error) {\n        const token = error ?? this.stack.pop();\n        /* istanbul ignore if should not happen */\n        if (!token) {\n            const message = 'Tried to pop an empty stack';\n            yield { type: 'error', offset: this.offset, source: '', message };\n        }\n        else if (this.stack.length === 0) {\n            yield token;\n        }\n        else {\n            const top = this.peek(1);\n            if (token.type === 'block-scalar') {\n                // Block scalars use their parent rather than header indent\n                token.indent = 'indent' in top ? top.indent : 0;\n            }\n            else if (token.type === 'flow-collection' && top.type === 'document') {\n                // Ignore all indent for top-level flow collections\n                token.indent = 0;\n            }\n            if (token.type === 'flow-collection')\n                fixFlowSeqItems(token);\n            switch (top.type) {\n                case 'document':\n                    top.value = token;\n                    break;\n                case 'block-scalar':\n                    top.props.push(token); // error\n                    break;\n                case 'block-map': {\n                    const it = top.items[top.items.length - 1];\n                    if (it.value) {\n                        top.items.push({ start: [], key: token, sep: [] });\n                        this.onKeyLine = true;\n                        return;\n                    }\n                    else if (it.sep) {\n                        it.value = token;\n                    }\n                    else {\n                        Object.assign(it, { key: token, sep: [] });\n                        this.onKeyLine = !it.explicitKey;\n                        return;\n                    }\n                    break;\n                }\n                case 'block-seq': {\n                    const it = top.items[top.items.length - 1];\n                    if (it.value)\n                        top.items.push({ start: [], value: token });\n                    else\n                        it.value = token;\n                    break;\n                }\n                case 'flow-collection': {\n                    const it = top.items[top.items.length - 1];\n                    if (!it || it.value)\n                        top.items.push({ start: [], key: token, sep: [] });\n                    else if (it.sep)\n                        it.value = token;\n                    else\n                        Object.assign(it, { key: token, sep: [] });\n                    return;\n                }\n                /* istanbul ignore next should not happen */\n                default:\n                    yield* this.pop();\n                    yield* this.pop(token);\n            }\n            if ((top.type === 'document' ||\n                top.type === 'block-map' ||\n                top.type === 'block-seq') &&\n                (token.type === 'block-map' || token.type === 'block-seq')) {\n                const last = token.items[token.items.length - 1];\n                if (last &&\n                    !last.sep &&\n                    !last.value &&\n                    last.start.length > 0 &&\n                    findNonEmptyIndex(last.start) === -1 &&\n                    (token.indent === 0 ||\n                        last.start.every(st => st.type !== 'comment' || st.indent < token.indent))) {\n                    if (top.type === 'document')\n                        top.end = last.start;\n                    else\n                        top.items.push({ start: last.start });\n                    token.items.splice(-1, 1);\n                }\n            }\n        }\n    }\n    *stream() {\n        switch (this.type) {\n            case 'directive-line':\n                yield { type: 'directive', offset: this.offset, source: this.source };\n                return;\n            case 'byte-order-mark':\n            case 'space':\n            case 'comment':\n            case 'newline':\n                yield this.sourceToken;\n                return;\n            case 'doc-mode':\n            case 'doc-start': {\n                const doc = {\n                    type: 'document',\n                    offset: this.offset,\n                    start: []\n                };\n                if (this.type === 'doc-start')\n                    doc.start.push(this.sourceToken);\n                this.stack.push(doc);\n                return;\n            }\n        }\n        yield {\n            type: 'error',\n            offset: this.offset,\n            message: `Unexpected ${this.type} token in YAML stream`,\n            source: this.source\n        };\n    }\n    *document(doc) {\n        if (doc.value)\n            return yield* this.lineEnd(doc);\n        switch (this.type) {\n            case 'doc-start': {\n                if (findNonEmptyIndex(doc.start) !== -1) {\n                    yield* this.pop();\n                    yield* this.step();\n                }\n                else\n                    doc.start.push(this.sourceToken);\n                return;\n            }\n            case 'anchor':\n            case 'tag':\n            case 'space':\n            case 'comment':\n            case 'newline':\n                doc.start.push(this.sourceToken);\n                return;\n        }\n        const bv = this.startBlockValue(doc);\n        if (bv)\n            this.stack.push(bv);\n        else {\n            yield {\n                type: 'error',\n                offset: this.offset,\n                message: `Unexpected ${this.type} token in YAML document`,\n                source: this.source\n            };\n        }\n    }\n    *scalar(scalar) {\n        if (this.type === 'map-value-ind') {\n            const prev = getPrevProps(this.peek(2));\n            const start = getFirstKeyStartProps(prev);\n            let sep;\n            if (scalar.end) {\n                sep = scalar.end;\n                sep.push(this.sourceToken);\n                delete scalar.end;\n            }\n            else\n                sep = [this.sourceToken];\n            const map = {\n                type: 'block-map',\n                offset: scalar.offset,\n                indent: scalar.indent,\n                items: [{ start, key: scalar, sep }]\n            };\n            this.onKeyLine = true;\n            this.stack[this.stack.length - 1] = map;\n        }\n        else\n            yield* this.lineEnd(scalar);\n    }\n    *blockScalar(scalar) {\n        switch (this.type) {\n            case 'space':\n            case 'comment':\n            case 'newline':\n                scalar.props.push(this.sourceToken);\n                return;\n            case 'scalar':\n                scalar.source = this.source;\n                // block-scalar source includes trailing newline\n                this.atNewLine = true;\n                this.indent = 0;\n                if (this.onNewLine) {\n                    let nl = this.source.indexOf('\\n') + 1;\n                    while (nl !== 0) {\n                        this.onNewLine(this.offset + nl);\n                        nl = this.source.indexOf('\\n', nl) + 1;\n                    }\n                }\n                yield* this.pop();\n                break;\n            /* istanbul ignore next should not happen */\n            default:\n                yield* this.pop();\n                yield* this.step();\n        }\n    }\n    *blockMap(map) {\n        const it = map.items[map.items.length - 1];\n        // it.sep is true-ish if pair already has key or : separator\n        switch (this.type) {\n            case 'newline':\n                this.onKeyLine = false;\n                if (it.value) {\n                    const end = 'end' in it.value ? it.value.end : undefined;\n                    const last = Array.isArray(end) ? end[end.length - 1] : undefined;\n                    if (last?.type === 'comment')\n                        end?.push(this.sourceToken);\n                    else\n                        map.items.push({ start: [this.sourceToken] });\n                }\n                else if (it.sep) {\n                    it.sep.push(this.sourceToken);\n                }\n                else {\n                    it.start.push(this.sourceToken);\n                }\n                return;\n            case 'space':\n            case 'comment':\n                if (it.value) {\n                    map.items.push({ start: [this.sourceToken] });\n                }\n                else if (it.sep) {\n                    it.sep.push(this.sourceToken);\n                }\n                else {\n                    if (this.atIndentedComment(it.start, map.indent)) {\n                        const prev = map.items[map.items.length - 2];\n                        const end = prev?.value?.end;\n                        if (Array.isArray(end)) {\n                            Array.prototype.push.apply(end, it.start);\n                            end.push(this.sourceToken);\n                            map.items.pop();\n                            return;\n                        }\n                    }\n                    it.start.push(this.sourceToken);\n                }\n                return;\n        }\n        if (this.indent >= map.indent) {\n            const atMapIndent = !this.onKeyLine && this.indent === map.indent;\n            const atNextItem = atMapIndent &&\n                (it.sep || it.explicitKey) &&\n                this.type !== 'seq-item-ind';\n            // For empty nodes, assign newline-separated not indented empty tokens to following node\n            let start = [];\n            if (atNextItem && it.sep && !it.value) {\n                const nl = [];\n                for (let i = 0; i < it.sep.length; ++i) {\n                    const st = it.sep[i];\n                    switch (st.type) {\n                        case 'newline':\n                            nl.push(i);\n                            break;\n                        case 'space':\n                            break;\n                        case 'comment':\n                            if (st.indent > map.indent)\n                                nl.length = 0;\n                            break;\n                        default:\n                            nl.length = 0;\n                    }\n                }\n                if (nl.length >= 2)\n                    start = it.sep.splice(nl[1]);\n            }\n            switch (this.type) {\n                case 'anchor':\n                case 'tag':\n                    if (atNextItem || it.value) {\n                        start.push(this.sourceToken);\n                        map.items.push({ start });\n                        this.onKeyLine = true;\n                    }\n                    else if (it.sep) {\n                        it.sep.push(this.sourceToken);\n                    }\n                    else {\n                        it.start.push(this.sourceToken);\n                    }\n                    return;\n                case 'explicit-key-ind':\n                    if (!it.sep && !it.explicitKey) {\n                        it.start.push(this.sourceToken);\n                        it.explicitKey = true;\n                    }\n                    else if (atNextItem || it.value) {\n                        start.push(this.sourceToken);\n                        map.items.push({ start, explicitKey: true });\n                    }\n                    else {\n                        this.stack.push({\n                            type: 'block-map',\n                            offset: this.offset,\n                            indent: this.indent,\n                            items: [{ start: [this.sourceToken], explicitKey: true }]\n                        });\n                    }\n                    this.onKeyLine = true;\n                    return;\n                case 'map-value-ind':\n                    if (it.explicitKey) {\n                        if (!it.sep) {\n                            if (includesToken(it.start, 'newline')) {\n                                Object.assign(it, { key: null, sep: [this.sourceToken] });\n                            }\n                            else {\n                                const start = getFirstKeyStartProps(it.start);\n                                this.stack.push({\n                                    type: 'block-map',\n                                    offset: this.offset,\n                                    indent: this.indent,\n                                    items: [{ start, key: null, sep: [this.sourceToken] }]\n                                });\n                            }\n                        }\n                        else if (it.value) {\n                            map.items.push({ start: [], key: null, sep: [this.sourceToken] });\n                        }\n                        else if (includesToken(it.sep, 'map-value-ind')) {\n                            this.stack.push({\n                                type: 'block-map',\n                                offset: this.offset,\n                                indent: this.indent,\n                                items: [{ start, key: null, sep: [this.sourceToken] }]\n                            });\n                        }\n                        else if (isFlowToken(it.key) &&\n                            !includesToken(it.sep, 'newline')) {\n                            const start = getFirstKeyStartProps(it.start);\n                            const key = it.key;\n                            const sep = it.sep;\n                            sep.push(this.sourceToken);\n                            // @ts-expect-error type guard is wrong here\n                            delete it.key;\n                            // @ts-expect-error type guard is wrong here\n                            delete it.sep;\n                            this.stack.push({\n                                type: 'block-map',\n                                offset: this.offset,\n                                indent: this.indent,\n                                items: [{ start, key, sep }]\n                            });\n                        }\n                        else if (start.length > 0) {\n                            // Not actually at next item\n                            it.sep = it.sep.concat(start, this.sourceToken);\n                        }\n                        else {\n                            it.sep.push(this.sourceToken);\n                        }\n                    }\n                    else {\n                        if (!it.sep) {\n                            Object.assign(it, { key: null, sep: [this.sourceToken] });\n                        }\n                        else if (it.value || atNextItem) {\n                            map.items.push({ start, key: null, sep: [this.sourceToken] });\n                        }\n                        else if (includesToken(it.sep, 'map-value-ind')) {\n                            this.stack.push({\n                                type: 'block-map',\n                                offset: this.offset,\n                                indent: this.indent,\n                                items: [{ start: [], key: null, sep: [this.sourceToken] }]\n                            });\n                        }\n                        else {\n                            it.sep.push(this.sourceToken);\n                        }\n                    }\n                    this.onKeyLine = true;\n                    return;\n                case 'alias':\n                case 'scalar':\n                case 'single-quoted-scalar':\n                case 'double-quoted-scalar': {\n                    const fs = this.flowScalar(this.type);\n                    if (atNextItem || it.value) {\n                        map.items.push({ start, key: fs, sep: [] });\n                        this.onKeyLine = true;\n                    }\n                    else if (it.sep) {\n                        this.stack.push(fs);\n                    }\n                    else {\n                        Object.assign(it, { key: fs, sep: [] });\n                        this.onKeyLine = true;\n                    }\n                    return;\n                }\n                default: {\n                    const bv = this.startBlockValue(map);\n                    if (bv) {\n                        if (atMapIndent && bv.type !== 'block-seq') {\n                            map.items.push({ start });\n                        }\n                        this.stack.push(bv);\n                        return;\n                    }\n                }\n            }\n        }\n        yield* this.pop();\n        yield* this.step();\n    }\n    *blockSequence(seq) {\n        const it = seq.items[seq.items.length - 1];\n        switch (this.type) {\n            case 'newline':\n                if (it.value) {\n                    const end = 'end' in it.value ? it.value.end : undefined;\n                    const last = Array.isArray(end) ? end[end.length - 1] : undefined;\n                    if (last?.type === 'comment')\n                        end?.push(this.sourceToken);\n                    else\n                        seq.items.push({ start: [this.sourceToken] });\n                }\n                else\n                    it.start.push(this.sourceToken);\n                return;\n            case 'space':\n            case 'comment':\n                if (it.value)\n                    seq.items.push({ start: [this.sourceToken] });\n                else {\n                    if (this.atIndentedComment(it.start, seq.indent)) {\n                        const prev = seq.items[seq.items.length - 2];\n                        const end = prev?.value?.end;\n                        if (Array.isArray(end)) {\n                            Array.prototype.push.apply(end, it.start);\n                            end.push(this.sourceToken);\n                            seq.items.pop();\n                            return;\n                        }\n                    }\n                    it.start.push(this.sourceToken);\n                }\n                return;\n            case 'anchor':\n            case 'tag':\n                if (it.value || this.indent <= seq.indent)\n                    break;\n                it.start.push(this.sourceToken);\n                return;\n            case 'seq-item-ind':\n                if (this.indent !== seq.indent)\n                    break;\n                if (it.value || includesToken(it.start, 'seq-item-ind'))\n                    seq.items.push({ start: [this.sourceToken] });\n                else\n                    it.start.push(this.sourceToken);\n                return;\n        }\n        if (this.indent > seq.indent) {\n            const bv = this.startBlockValue(seq);\n            if (bv) {\n                this.stack.push(bv);\n                return;\n            }\n        }\n        yield* this.pop();\n        yield* this.step();\n    }\n    *flowCollection(fc) {\n        const it = fc.items[fc.items.length - 1];\n        if (this.type === 'flow-error-end') {\n            let top;\n            do {\n                yield* this.pop();\n                top = this.peek(1);\n            } while (top && top.type === 'flow-collection');\n        }\n        else if (fc.end.length === 0) {\n            switch (this.type) {\n                case 'comma':\n                case 'explicit-key-ind':\n                    if (!it || it.sep)\n                        fc.items.push({ start: [this.sourceToken] });\n                    else\n                        it.start.push(this.sourceToken);\n                    return;\n                case 'map-value-ind':\n                    if (!it || it.value)\n                        fc.items.push({ start: [], key: null, sep: [this.sourceToken] });\n                    else if (it.sep)\n                        it.sep.push(this.sourceToken);\n                    else\n                        Object.assign(it, { key: null, sep: [this.sourceToken] });\n                    return;\n                case 'space':\n                case 'comment':\n                case 'newline':\n                case 'anchor':\n                case 'tag':\n                    if (!it || it.value)\n                        fc.items.push({ start: [this.sourceToken] });\n                    else if (it.sep)\n                        it.sep.push(this.sourceToken);\n                    else\n                        it.start.push(this.sourceToken);\n                    return;\n                case 'alias':\n                case 'scalar':\n                case 'single-quoted-scalar':\n                case 'double-quoted-scalar': {\n                    const fs = this.flowScalar(this.type);\n                    if (!it || it.value)\n                        fc.items.push({ start: [], key: fs, sep: [] });\n                    else if (it.sep)\n                        this.stack.push(fs);\n                    else\n                        Object.assign(it, { key: fs, sep: [] });\n                    return;\n                }\n                case 'flow-map-end':\n                case 'flow-seq-end':\n                    fc.end.push(this.sourceToken);\n                    return;\n            }\n            const bv = this.startBlockValue(fc);\n            /* istanbul ignore else should not happen */\n            if (bv)\n                this.stack.push(bv);\n            else {\n                yield* this.pop();\n                yield* this.step();\n            }\n        }\n        else {\n            const parent = this.peek(2);\n            if (parent.type === 'block-map' &&\n                ((this.type === 'map-value-ind' && parent.indent === fc.indent) ||\n                    (this.type === 'newline' &&\n                        !parent.items[parent.items.length - 1].sep))) {\n                yield* this.pop();\n                yield* this.step();\n            }\n            else if (this.type === 'map-value-ind' &&\n                parent.type !== 'flow-collection') {\n                const prev = getPrevProps(parent);\n                const start = getFirstKeyStartProps(prev);\n                fixFlowSeqItems(fc);\n                const sep = fc.end.splice(1, fc.end.length);\n                sep.push(this.sourceToken);\n                const map = {\n                    type: 'block-map',\n                    offset: fc.offset,\n                    indent: fc.indent,\n                    items: [{ start, key: fc, sep }]\n                };\n                this.onKeyLine = true;\n                this.stack[this.stack.length - 1] = map;\n            }\n            else {\n                yield* this.lineEnd(fc);\n            }\n        }\n    }\n    flowScalar(type) {\n        if (this.onNewLine) {\n            let nl = this.source.indexOf('\\n') + 1;\n            while (nl !== 0) {\n                this.onNewLine(this.offset + nl);\n                nl = this.source.indexOf('\\n', nl) + 1;\n            }\n        }\n        return {\n            type,\n            offset: this.offset,\n            indent: this.indent,\n            source: this.source\n        };\n    }\n    startBlockValue(parent) {\n        switch (this.type) {\n            case 'alias':\n            case 'scalar':\n            case 'single-quoted-scalar':\n            case 'double-quoted-scalar':\n                return this.flowScalar(this.type);\n            case 'block-scalar-header':\n                return {\n                    type: 'block-scalar',\n                    offset: this.offset,\n                    indent: this.indent,\n                    props: [this.sourceToken],\n                    source: ''\n                };\n            case 'flow-map-start':\n            case 'flow-seq-start':\n                return {\n                    type: 'flow-collection',\n                    offset: this.offset,\n                    indent: this.indent,\n                    start: this.sourceToken,\n                    items: [],\n                    end: []\n                };\n            case 'seq-item-ind':\n                return {\n                    type: 'block-seq',\n                    offset: this.offset,\n                    indent: this.indent,\n                    items: [{ start: [this.sourceToken] }]\n                };\n            case 'explicit-key-ind': {\n                this.onKeyLine = true;\n                const prev = getPrevProps(parent);\n                const start = getFirstKeyStartProps(prev);\n                start.push(this.sourceToken);\n                return {\n                    type: 'block-map',\n                    offset: this.offset,\n                    indent: this.indent,\n                    items: [{ start, explicitKey: true }]\n                };\n            }\n            case 'map-value-ind': {\n                this.onKeyLine = true;\n                const prev = getPrevProps(parent);\n                const start = getFirstKeyStartProps(prev);\n                return {\n                    type: 'block-map',\n                    offset: this.offset,\n                    indent: this.indent,\n                    items: [{ start, key: null, sep: [this.sourceToken] }]\n                };\n            }\n        }\n        return null;\n    }\n    atIndentedComment(start, indent) {\n        if (this.type !== 'comment')\n            return false;\n        if (this.indent <= indent)\n            return false;\n        return start.every(st => st.type === 'newline' || st.type === 'space');\n    }\n    *documentEnd(docEnd) {\n        if (this.type !== 'doc-mode') {\n            if (docEnd.end)\n                docEnd.end.push(this.sourceToken);\n            else\n                docEnd.end = [this.sourceToken];\n            if (this.type === 'newline')\n                yield* this.pop();\n        }\n    }\n    *lineEnd(token) {\n        switch (this.type) {\n            case 'comma':\n            case 'doc-start':\n            case 'doc-end':\n            case 'flow-seq-end':\n            case 'flow-map-end':\n            case 'map-value-ind':\n                yield* this.pop();\n                yield* this.step();\n                break;\n            case 'newline':\n                this.onKeyLine = false;\n            // fallthrough\n            case 'space':\n            case 'comment':\n            default:\n                // all other values are errors\n                if (token.end)\n                    token.end.push(this.sourceToken);\n                else\n                    token.end = [this.sourceToken];\n                if (this.type === 'newline')\n                    yield* this.pop();\n        }\n    }\n}\n\nexports.Parser = Parser;\n", "'use strict';\n\nvar composer = require('./compose/composer.js');\nvar Document = require('./doc/Document.js');\nvar errors = require('./errors.js');\nvar log = require('./log.js');\nvar identity = require('./nodes/identity.js');\nvar lineCounter = require('./parse/line-counter.js');\nvar parser = require('./parse/parser.js');\n\nfunction parseOptions(options) {\n    const prettyErrors = options.prettyErrors !== false;\n    const lineCounter$1 = options.lineCounter || (prettyErrors && new lineCounter.LineCounter()) || null;\n    return { lineCounter: lineCounter$1, prettyErrors };\n}\n/**\n * Parse the input as a stream of YAML documents.\n *\n * Documents should be separated from each other by `...` or `---` marker lines.\n *\n * @returns If an empty `docs` array is returned, it will be of type\n *   EmptyStream and contain additional stream information. In\n *   TypeScript, you should use `'empty' in docs` as a type guard for it.\n */\nfunction parseAllDocuments(source, options = {}) {\n    const { lineCounter, prettyErrors } = parseOptions(options);\n    const parser$1 = new parser.Parser(lineCounter?.addNewLine);\n    const composer$1 = new composer.Composer(options);\n    const docs = Array.from(composer$1.compose(parser$1.parse(source)));\n    if (prettyErrors && lineCounter)\n        for (const doc of docs) {\n            doc.errors.forEach(errors.prettifyError(source, lineCounter));\n            doc.warnings.forEach(errors.prettifyError(source, lineCounter));\n        }\n    if (docs.length > 0)\n        return docs;\n    return Object.assign([], { empty: true }, composer$1.streamInfo());\n}\n/** Parse an input string into a single YAML.Document */\nfunction parseDocument(source, options = {}) {\n    const { lineCounter, prettyErrors } = parseOptions(options);\n    const parser$1 = new parser.Parser(lineCounter?.addNewLine);\n    const composer$1 = new composer.Composer(options);\n    // `doc` is always set by compose.end(true) at the very latest\n    let doc = null;\n    for (const _doc of composer$1.compose(parser$1.parse(source), true, source.length)) {\n        if (!doc)\n            doc = _doc;\n        else if (doc.options.logLevel !== 'silent') {\n            doc.errors.push(new errors.YAMLParseError(_doc.range.slice(0, 2), 'MULTIPLE_DOCS', 'Source contains multiple documents; please use YAML.parseAllDocuments()'));\n            break;\n        }\n    }\n    if (prettyErrors && lineCounter) {\n        doc.errors.forEach(errors.prettifyError(source, lineCounter));\n        doc.warnings.forEach(errors.prettifyError(source, lineCounter));\n    }\n    return doc;\n}\nfunction parse(src, reviver, options) {\n    let _reviver = undefined;\n    if (typeof reviver === 'function') {\n        _reviver = reviver;\n    }\n    else if (options === undefined && reviver && typeof reviver === 'object') {\n        options = reviver;\n    }\n    const doc = parseDocument(src, options);\n    if (!doc)\n        return null;\n    doc.warnings.forEach(warning => log.warn(doc.options.logLevel, warning));\n    if (doc.errors.length > 0) {\n        if (doc.options.logLevel !== 'silent')\n            throw doc.errors[0];\n        else\n            doc.errors = [];\n    }\n    return doc.toJS(Object.assign({ reviver: _reviver }, options));\n}\nfunction stringify(value, replacer, options) {\n    let _replacer = null;\n    if (typeof replacer === 'function' || Array.isArray(replacer)) {\n        _replacer = replacer;\n    }\n    else if (options === undefined && replacer) {\n        options = replacer;\n    }\n    if (typeof options === 'string')\n        options = options.length;\n    if (typeof options === 'number') {\n        const indent = Math.round(options);\n        options = indent < 1 ? undefined : indent > 8 ? { indent: 8 } : { indent };\n    }\n    if (value === undefined) {\n        const { keepUndefined } = options ?? replacer ?? {};\n        if (!keepUndefined)\n            return undefined;\n    }\n    if (identity.isDocument(value) && !_replacer)\n        return value.toString(options);\n    return new Document.Document(value, _replacer, options).toString(options);\n}\n\nexports.parse = parse;\nexports.parseAllDocuments = parseAllDocuments;\nexports.parseDocument = parseDocument;\nexports.stringify = stringify;\n", "'use strict';\n\nvar composer = require('./compose/composer.js');\nvar Document = require('./doc/Document.js');\nvar Schema = require('./schema/Schema.js');\nvar errors = require('./errors.js');\nvar Alias = require('./nodes/Alias.js');\nvar identity = require('./nodes/identity.js');\nvar Pair = require('./nodes/Pair.js');\nvar Scalar = require('./nodes/Scalar.js');\nvar YAMLMap = require('./nodes/YAMLMap.js');\nvar YAMLSeq = require('./nodes/YAMLSeq.js');\nvar cst = require('./parse/cst.js');\nvar lexer = require('./parse/lexer.js');\nvar lineCounter = require('./parse/line-counter.js');\nvar parser = require('./parse/parser.js');\nvar publicApi = require('./public-api.js');\nvar visit = require('./visit.js');\n\n\n\nexports.Composer = composer.Composer;\nexports.Document = Document.Document;\nexports.Schema = Schema.Schema;\nexports.YAMLError = errors.YAMLError;\nexports.YAMLParseError = errors.YAMLParseError;\nexports.YAMLWarning = errors.YAMLWarning;\nexports.Alias = Alias.Alias;\nexports.isAlias = identity.isAlias;\nexports.isCollection = identity.isCollection;\nexports.isDocument = identity.isDocument;\nexports.isMap = identity.isMap;\nexports.isNode = identity.isNode;\nexports.isPair = identity.isPair;\nexports.isScalar = identity.isScalar;\nexports.isSeq = identity.isSeq;\nexports.Pair = Pair.Pair;\nexports.Scalar = Scalar.Scalar;\nexports.YAMLMap = YAMLMap.YAMLMap;\nexports.YAMLSeq = YAMLSeq.YAMLSeq;\nexports.CST = cst;\nexports.Lexer = lexer.Lexer;\nexports.LineCounter = lineCounter.LineCounter;\nexports.Parser = parser.Parser;\nexports.parse = publicApi.parse;\nexports.parseAllDocuments = publicApi.parseAllDocuments;\nexports.parseDocument = publicApi.parseDocument;\nexports.stringify = publicApi.stringify;\nexports.visit = visit.visit;\nexports.visitAsync = visit.visitAsync;\n", "import { Action, ActionPanel, closeMainWindow, List, open, popToRoot } from \"@raycast/api\";\n\nimport { getObsidianTarget, ObsidianTargetType } from \"./utils/utils\";\nimport { NoVaultFoundMessage } from \"./components/Notifications/NoVaultFoundMessage\";\nimport { vaultsWithoutAdvancedURIToast } from \"./components/Toasts\";\nimport AdvancedURIPluginNotInstalled from \"./components/Notifications/AdvancedURIPluginNotInstalled\";\nimport { useObsidianVaults } from \"./utils/hooks\";\nimport { vaultPluginCheck } from \"./api/vault/plugins/plugins.service\";\n\nexport default function Command() {\n  const { vaults, ready } = useObsidianVaults();\n\n  if (!ready) {\n    return <List isLoading={true}></List>;\n  } else if (vaults.length === 0) {\n    return <NoVaultFoundMessage />;\n  }\n\n  const [vaultsWithPlugin, vaultsWithoutPlugin] = vaultPluginCheck(vaults, \"obsidian-advanced-uri\");\n\n  if (vaultsWithoutPlugin.length > 0) {\n    vaultsWithoutAdvancedURIToast(vaultsWithoutPlugin);\n  }\n  if (vaultsWithPlugin.length == 0) {\n    return <AdvancedURIPluginNotInstalled />;\n  }\n\n  if (vaultsWithPlugin.length == 1) {\n    const target = getObsidianTarget({ type: ObsidianTargetType.DailyNote, vault: vaultsWithPlugin[0] });\n    open(target);\n    popToRoot();\n    closeMainWindow();\n  }\n\n  return (\n    <List isLoading={vaultsWithPlugin === undefined}>\n      {vaultsWithPlugin?.map((vault) => (\n        <List.Item\n          title={vault.name}\n          key={vault.key}\n          actions={\n            <ActionPanel>\n              <Action.Open\n                title=\"Daily Note\"\n                target={getObsidianTarget({ type: ObsidianTargetType.DailyNote, vault: vault })}\n              />\n            </ActionPanel>\n          }\n        />\n      ))}\n    </List>\n  );\n}\n", "//--------------------------------------------------------------------------------\n// All important constants for all commands should be defined here.\n//--------------------------------------------------------------------------------\n\nimport { Image } from \"@raycast/api\";\n\nexport const MAX_RENDERED_NOTES = 1000;\nexport const BYTES_PER_KILOBYTE = 1024;\nexport const BYTES_PER_MEGABYTE = BYTES_PER_KILOBYTE ** 2;\nexport const BYTES_PER_GIGABYTE = BYTES_PER_MEGABYTE ** 2;\n\nexport enum NoteAction {\n  Edit,\n  Delete,\n  Append,\n  Bookmark,\n  UnBookmark,\n}\n\nexport enum PrimaryAction {\n  QuickLook = \"quicklook\",\n  OpenInObsidian = \"obsidian\",\n  OpenInObsidianNewPane = \"newpane\",\n  OpenInDefaultApp = \"defaultapp\",\n}\n\nexport const APPLICATION_UUID = \"49acc9ee-69a0-4419-9aad-5c2689ff0119\";\n\nexport const INLINE_TAGS_REGEX = /(#[a-zA-Z_0-9/-]+)/g;\nexport const YAML_FRONTMATTER_REGEX = /---\\s([\\s\\S]*)---/g;\nexport const LATEX_REGEX = /\\$\\$(.|\\n)*?\\$\\$/gm;\nexport const LATEX_INLINE_REGEX = /\\$(.|\\n)*?\\$/gm;\nexport const CODE_BLOCK_REGEX = /```(.*)\\n([\\s\\S]*?)```/gm;\n\nexport const DAY_NUMBER_TO_STRING: Record<number, string> = {\n  0: \"Sun\",\n  1: \"Mon\",\n  2: \"Tue\",\n  3: \"Wed\",\n  4: \"Thu\",\n  5: \"Fri\",\n  6: \"Sat\",\n};\n\nexport const MONTH_NUMBER_TO_STRING: Record<number, string> = {\n  0: \"Jan\",\n  1: \"Feb\",\n  2: \"Mar\",\n  3: \"Apr\",\n  4: \"May\",\n  5: \"Jun\",\n  6: \"Jul\",\n  7: \"Aug\",\n  8: \"Sep\",\n  9: \"Oct\",\n  10: \"Nov\",\n  11: \"Dec\",\n};\n\nexport const VIDEO_FILE_EXTENSIONS = [\n  \".webm\",\n  \".mkv\",\n  \".flv\",\n  \".vob\",\n  \".ogv\",\n  \".ogg\",\n  \".rrc\",\n  \".gifv\",\n  \".mng\",\n  \".mov\",\n  \".avi\",\n  \".qt\",\n  \".wmv\",\n  \".yuv\",\n  \".rm\",\n  \".asf\",\n  \".amv\",\n  \".mp4\",\n  \".m4p\",\n  \".m4v\",\n  \".mpg\",\n  \".mp2\",\n  \".mpeg\",\n  \".mpe\",\n  \".mpv\",\n  \".m4v\",\n  \".svi\",\n  \".3gp\",\n  \".3g2\",\n  \".mxf\",\n  \".roq\",\n  \".nsv\",\n  \".flv\",\n  \".f4v\",\n  \".f4p\",\n  \".f4a\",\n  \".f4b\",\n  \".mod\",\n];\n\nexport const AUDIO_FILE_EXTENSIONS = [\n  \"aac\",\n  \"aiff\",\n  \"ape\",\n  \"au\",\n  \"flac\",\n  \"gsm\",\n  \"it\",\n  \"m3u\",\n  \"m4a\",\n  \"mid\",\n  \"mod\",\n  \"mp3\",\n  \"mpa\",\n  \"pls\",\n  \"ra\",\n  \"s3m\",\n  \"sid\",\n  \"wav\",\n  \"wma\",\n  \"xm\",\n];\n\nexport const IMAGE_SIZE_MAPPING: Map<string, number> = new Map([\n  [\"small\", 8],\n  [\"large\", 3],\n  [\"medium\", 5],\n]);\n\nexport const ObsidianIcon: Image = {\n  source: \"obsidian_icon.svg\",\n  tintColor: { dark: \"#E6E6E6\", light: \"#262626\", adjustContrast: false },\n};\n", "import fs from \"fs\";\nimport path from \"path\";\nimport { BYTES_PER_KILOBYTE } from \"./constants\";\nimport { Media } from \"./interfaces\";\nimport { Vault } from \"../api/vault/vault.types\";\nimport { Note } from \"../api/vault/notes/notes.types\";\nimport { getSelectedText } from \"@raycast/api\";\n\nexport function sortByAlphabet(a: string, b: string) {\n  const aTitle = a;\n  const bTitle = b;\n  if (aTitle > bTitle) {\n    return 1;\n  } else if (aTitle < bTitle) {\n    return -1;\n  } else {\n    return 0;\n  }\n}\n\nexport function sortNoteByAlphabet(a: Note, b: Note) {\n  return sortByAlphabet(a.title, b.title);\n}\n\nexport function wordCount(str: string) {\n  return str.split(\" \").length;\n}\n\nexport function readingTime(str: string) {\n  return Math.ceil(wordCount(str) / 200);\n}\n\nexport function createdDateFor(note: Note) {\n  const { birthtime } = fs.statSync(note.path);\n  return birthtime;\n}\n\nexport function fileSizeFor(note: Note) {\n  const { size } = fs.statSync(note.path);\n  return size / BYTES_PER_KILOBYTE;\n}\n\nexport function trimPathToMaxLength(path: string, maxLength: number) {\n  if (path.length > maxLength) {\n    return \"...\" + path.slice(path.length - maxLength).slice(1);\n  } else {\n    return path.slice(1);\n  }\n}\n\nexport async function ISO8601_week_no(dt: Date) {\n  const tdt = new Date(dt.getTime());\n  const dayn = (dt.getDay() + 6) % 7;\n  tdt.setDate(tdt.getDate() - dayn + 3);\n  const firstThursday = tdt.getTime();\n  tdt.setMonth(0, 1);\n  if (tdt.getDay() !== 4) {\n    tdt.setMonth(0, 1 + ((4 - tdt.getDay() + 7) % 7));\n  }\n  return 1 + Math.ceil((firstThursday - tdt.getTime()) / 604800000);\n}\n\nexport function getListOfMediaFileExtensions(media: Media[]) {\n  const foundExtensions: string[] = [];\n  for (const mediaItem of media) {\n    const extension = path.extname(mediaItem.path);\n    if (!foundExtensions.includes(extension) && extension != \"\") {\n      foundExtensions.push(extension);\n    }\n  }\n  return foundExtensions;\n}\n\n/** Retrieves the currently selected text if available, returns undefined if not found */\nexport async function getSelectedTextContent(): Promise<string | undefined> {\n  let selection;\n  try {\n    selection = await getSelectedText();\n  } catch (error) {\n    console.warn(\"Could not get selected text\", error);\n  }\n  return selection;\n} // OBSIDIAN TARGETS\n\nexport enum ObsidianTargetType {\n  OpenVault = \"obsidian://open?vault=\",\n  OpenPath = \"obsidian://open?path=\",\n  DailyNote = \"obsidian://advanced-uri?daily=true&vault=\",\n  DailyNoteAppend = \"obsidian://advanced-uri?daily=true\",\n  NewNote = \"obsidian://new?vault=\",\n  AppendTask = \"obsidian://advanced-uri?mode=append&filepath=\",\n}\n\nexport type ObsidianTarget =\n  | { type: ObsidianTargetType.OpenVault; vault: Vault }\n  | { type: ObsidianTargetType.OpenPath; path: string }\n  | { type: ObsidianTargetType.DailyNote; vault: Vault }\n  | {\n      type: ObsidianTargetType.DailyNoteAppend;\n      vault: Vault;\n      text: string;\n      heading?: string;\n      prepend?: boolean;\n      silent?: boolean;\n    }\n  | { type: ObsidianTargetType.NewNote; vault: Vault; name: string; content?: string }\n  | {\n      type: ObsidianTargetType.AppendTask;\n      vault: Vault;\n      text: string;\n      path: string;\n      heading?: string;\n      silent?: boolean;\n    };\n\nexport function getObsidianTarget(target: ObsidianTarget) {\n  switch (target.type) {\n    case ObsidianTargetType.OpenVault: {\n      return ObsidianTargetType.OpenVault + encodeURIComponent(target.vault.name);\n    }\n    case ObsidianTargetType.OpenPath: {\n      return ObsidianTargetType.OpenPath + encodeURIComponent(target.path);\n    }\n    case ObsidianTargetType.DailyNote: {\n      return ObsidianTargetType.DailyNote + encodeURIComponent(target.vault.name);\n    }\n    case ObsidianTargetType.DailyNoteAppend: {\n      const headingParam = target.heading ? \"&heading=\" + encodeURIComponent(target.heading) : \"\";\n      return (\n        ObsidianTargetType.DailyNoteAppend +\n        (target.prepend ? \"&mode=prepend\" : \"&mode=append\") +\n        \"&data=\" +\n        encodeURIComponent(target.text) +\n        \"&vault=\" +\n        encodeURIComponent(target.vault.name) +\n        headingParam +\n        (target.silent ? \"&openmode=silent\" : \"\")\n      );\n    }\n    case ObsidianTargetType.NewNote: {\n      return (\n        ObsidianTargetType.NewNote +\n        encodeURIComponent(target.vault.name) +\n        \"&name=\" +\n        encodeURIComponent(target.name) +\n        \"&content=\" +\n        encodeURIComponent(target.content || \"\")\n      );\n    }\n    case ObsidianTargetType.AppendTask: {\n      const headingParam = target.heading ? \"&heading=\" + encodeURIComponent(target.heading) : \"\";\n      return (\n        ObsidianTargetType.AppendTask +\n        encodeURIComponent(target.path) +\n        \"&data=\" +\n        encodeURIComponent(target.text) +\n        \"&vault=\" +\n        encodeURIComponent(target.vault.name) +\n        headingParam +\n        (target.silent ? \"&openmode=silent\" : \"\")\n      );\n    }\n    default: {\n      return \"\";\n    }\n  }\n}\n", "import { Detail } from \"@raycast/api\";\n\nexport function NoVaultFoundMessage() {\n  const text =\n    \"# No vaults found\\n\\n Please use Obsidian to create a vault, or set a vault path in the extension's preferences before using this command.\";\n  return <Detail markdown={text} />;\n}\n", "import { showToast, Toast } from \"@raycast/api\";\nimport { Vault } from \"../api/vault/vault.types\";\n\n//--------------------------------------------------------------------------------\n// All toasts for all commands should be defined here.\n// (makes it easier to manage translations when they are added to Raycast)\n//--------------------------------------------------------------------------------\n\nexport function noVaultPathsToast() {\n  showToast({\n    title: \"Path Error\",\n    message: \"Something went wrong with your vault path. There are no paths to select from.\",\n    style: Toast.Style.Failure,\n  });\n}\n\nexport function directoryCreationErrorToast(path: string) {\n  showToast({\n    title: \"Couldn't create directories for the given path:\",\n    message: path,\n    style: Toast.Style.Failure,\n  });\n}\n\nexport function fileWriteErrorToast(path: string, filename: string) {\n  showToast({\n    title: \"Couldn't write to file:\",\n    message: path + \"/\" + filename + \".md\",\n    style: Toast.Style.Failure,\n  });\n}\n\nexport function vaultsWithoutAdvancedURIToast(vaultsWithoutPlugin: Vault[]) {\n  showToast({\n    title: \"Vaults without Advanced URI plugin:\",\n    message: vaultsWithoutPlugin.map((vault: Vault) => vault.name).join(\", \"),\n    style: Toast.Style.Failure,\n  });\n}\n", "import { Detail } from \"@raycast/api\";\n\nexport default function AdvancedURIPluginNotInstalled({ vaultName }: { vaultName?: string }) {\n  const vaultText = vaultName ? `vault \"${vaultName}\"` : \"any vault\";\n  const text = `# Advanced URI plugin not installed in ${vaultText}.\\nThis command requires the [Advanced URI plugin](https://obsidian.md/plugins?id=obsidian-advanced-uri) for Obsidian.  \\n  \\n Install it through the community plugins list.`;\n\n  return <Detail navigationTitle=\"Advanced URI plugin not installed\" markdown={text} />;\n}\n", "import { getPreferenceValues, showToast, Toast } from \"@raycast/api\";\nimport { createContext, useContext, useEffect, useMemo, useState } from \"react\";\nimport { NoteReducerAction } from \"./reducers\";\nimport { MediaState } from \"./interfaces\";\nimport { sortByAlphabet } from \"./utils\";\nimport fs from \"fs\";\nimport { ObsidianVaultsState, Vault } from \"../api/vault/vault.types\";\nimport { Note } from \"../api/vault/notes/notes.types\";\nimport { loadMedia, loadObsidianJson, parseVaults } from \"../api/vault/vault.service\";\nimport { getNotesFromCache } from \"../api/cache/cache.service\";\nimport { Logger } from \"../api/logger/logger.service\";\n\nconst logger = new Logger(\"Hooks\");\n\nexport const NotesContext = createContext([] as Note[]);\nexport const NotesDispatchContext = createContext((() => {}) as (action: NoteReducerAction) => void);\n\nexport function useNotes(vault: Vault, bookmarked = false) {\n  /**\n   * The preferred way of loading notes inside the extension\n   *\n   * @param vault - The Vault to get the notes from\n   * @returns All notes in the cache for the vault\n   */\n\n  const notes_: Note[] = getNotesFromCache(vault);\n\n  const [notes] = useState<Note[]>(notes_);\n  logger.info(\"useNotes hook called\");\n  if (bookmarked) {\n    return [notes.filter((note: Note) => note.bookmarked)] as const;\n  } else {\n    return [notes] as const;\n  }\n}\n\nexport function useNotesContext() {\n  return useContext(NotesContext);\n}\n\nexport function useNotesDispatchContext() {\n  return useContext(NotesDispatchContext);\n}\n\nexport function useMedia(vault: Vault) {\n  const [media, setMedia] = useState<MediaState>({\n    ready: false,\n    media: [],\n  });\n\n  logger.info(\"useMedia hook called\");\n\n  useEffect(() => {\n    async function fetch() {\n      if (!media.ready) {\n        try {\n          await fs.promises.access(vault.path + \"/.\");\n\n          const media = loadMedia(vault).sort((m1, m2) => sortByAlphabet(m1.title, m2.title));\n\n          setMedia({ ready: true, media });\n        } catch (error) {\n          showToast({\n            title: \"The path set in preferences doesn't exist\",\n            message: \"Please set a valid path in preferences\",\n            style: Toast.Style.Failure,\n          });\n        }\n      }\n    }\n    fetch();\n  }, []);\n\n  return media;\n}\n\nexport function useObsidianVaults(): ObsidianVaultsState {\n  const pref = useMemo(() => getPreferenceValues(), []);\n  const [state, setState] = useState<ObsidianVaultsState>(\n    pref.vaultPath\n      ? {\n          ready: true,\n          vaults: parseVaults(),\n        }\n      : { ready: false, vaults: [] }\n  );\n\n  logger.info(\"useObsidianVaults hook called\");\n\n  useEffect(() => {\n    if (!state.ready) {\n      loadObsidianJson()\n        .then((vaults) => {\n          setState({ vaults, ready: true });\n        })\n        .catch(() => setState({ vaults: parseVaults(), ready: true }));\n    }\n  }, []);\n\n  return state;\n}\n", "import { getPreferenceValues, Icon } from \"@raycast/api\";\nimport * as fs from \"fs\";\nimport { readFile } from \"fs/promises\";\nimport { homedir } from \"os\";\nimport { default as fsPath, default as path } from \"path\";\nimport { performance } from \"perf_hooks\";\nimport { AUDIO_FILE_EXTENSIONS, LATEX_INLINE_REGEX, LATEX_REGEX, VIDEO_FILE_EXTENSIONS } from \"../../utils/constants\";\nimport { Media } from \"../../utils/interfaces\";\nimport { GlobalPreferences, SearchNotePreferences } from \"../../utils/preferences\";\nimport { tagsForString } from \"../../utils/yaml\";\nimport { getBookmarkedNotePaths } from \"./notes/bookmarks/bookmarks.service\";\nimport { Note } from \"./notes/notes.types\";\nimport { ObsidianJSON, Vault } from \"./vault.types\";\n\nfunction getVaultNameFromPath(vaultPath: string): string {\n  const name = vaultPath\n    .split(fsPath.sep)\n    .filter((i) => {\n      if (i != \"\") {\n        return i;\n      }\n    })\n    .pop();\n  if (name) {\n    return name;\n  } else {\n    return \"Default Vault Name (check your path preferences)\";\n  }\n}\n\nexport function parseVaults(): Vault[] {\n  const pref: GlobalPreferences = getPreferenceValues();\n  const vaultString = pref.vaultPath;\n  return vaultString\n    .split(\",\")\n    .filter((vaultPath) => vaultPath.trim() !== \"\")\n    .filter((vaultPath) => fs.existsSync(vaultPath))\n    .map((vault) => ({ name: getVaultNameFromPath(vault.trim()), key: vault.trim(), path: vault.trim() }));\n}\n\nexport async function loadObsidianJson(): Promise<Vault[]> {\n  const obsidianJsonPath = fsPath.resolve(`${homedir()}/Library/Application Support/obsidian/obsidian.json`);\n  try {\n    const obsidianJson = JSON.parse(await readFile(obsidianJsonPath, \"utf8\")) as ObsidianJSON;\n    return Object.values(obsidianJson.vaults).map(({ path }) => ({\n      name: getVaultNameFromPath(path),\n      key: path,\n      path,\n    }));\n  } catch (e) {\n    return [];\n  }\n}\n\n/**\n * Checks if a path should be excluded based on exclusion rules\n */\nfunction isPathExcluded(pathToCheck: string, excludedPaths: string[]) {\n  const normalizedPath = path.normalize(pathToCheck);\n\n  return excludedPaths.some((excluded) => {\n    if (!excluded) return false;\n\n    const normalizedExcluded = path.normalize(excluded);\n\n    // Check if the path is exactly the excluded path or is a subfolder\n    return normalizedPath === normalizedExcluded || normalizedPath.startsWith(normalizedExcluded + path.sep);\n  });\n}\n\nconst DEFAULT_EXCLUDED_PATHS = [\".git\", \".obsidian\", \".trash\", \".excalidraw\", \".mobile\"];\n\nfunction walkFilesHelper(pathToWalk: string, excludedFolders: string[], fileEndings: string[], resultFiles: string[]) {\n  const files = fs.readdirSync(pathToWalk);\n  const { configFileName } = getPreferenceValues();\n\n  for (const file of files) {\n    const fullPath = path.join(pathToWalk, file);\n    const stats = fs.statSync(fullPath);\n\n    if (stats.isDirectory()) {\n      if (file === configFileName) continue;\n      if (DEFAULT_EXCLUDED_PATHS.includes(file)) continue;\n      if (isPathExcluded(fullPath, excludedFolders)) continue;\n      // Recursively process subdirectory\n      walkFilesHelper(fullPath, excludedFolders, fileEndings, resultFiles);\n    } else {\n      const extension = path.extname(file);\n      if (\n        fileEndings.includes(extension) &&\n        file !== \".md\" &&\n        !file.includes(\".excalidraw\") &&\n        !isPathExcluded(pathToWalk, [\".obsidian\", configFileName]) &&\n        !isPathExcluded(pathToWalk, excludedFolders)\n      ) {\n        resultFiles.push(fullPath);\n      }\n    }\n  }\n\n  return resultFiles;\n}\n\n/** Gets a list of folders that are marked as excluded inside of the Raycast preferences */\nfunction getExcludedFolders(): string[] {\n  const preferences = getPreferenceValues<SearchNotePreferences>();\n  const foldersString = preferences.excludedFolders;\n  if (!foldersString) return [];\n\n  const folders = foldersString.split(\",\").map((folder) => folder.trim());\n  return folders;\n}\n\n/** Returns a list of file paths for all notes. */\nfunction getFilePaths(vault: Vault): string[] {\n  const excludedFolders = getExcludedFolders();\n  const userIgnoredFolders = getUserIgnoreFilters(vault);\n  excludedFolders.push(...userIgnoredFolders);\n  const files = walkFilesHelper(vault.path, excludedFolders, [\".md\"], []);\n  return files;\n}\n\n/** Gets a list of folders that are ignored by the user inside of Obsidian */\nfunction getUserIgnoreFilters(vault: Vault): string[] {\n  const { configFileName } = getPreferenceValues<GlobalPreferences>();\n  const appJSONPath = `${vault.path}/${configFileName || \".obsidian\"}/app.json`;\n  if (!fs.existsSync(appJSONPath)) {\n    return [];\n  } else {\n    const appJSON = JSON.parse(fs.readFileSync(appJSONPath, \"utf-8\"));\n    return appJSON[\"userIgnoreFilters\"] || [];\n  }\n}\n\nexport function filterContent(content: string) {\n  const pref: GlobalPreferences = getPreferenceValues();\n\n  if (pref.removeYAML) {\n    const yamlHeader = content.match(/---(.|\\n)*?---/gm);\n    if (yamlHeader) {\n      content = content.replace(yamlHeader[0], \"\");\n    }\n  }\n  if (pref.removeLatex) {\n    const latex = content.matchAll(LATEX_REGEX);\n    for (const match of latex) {\n      content = content.replace(match[0], \"\");\n    }\n    const latexInline = content.matchAll(LATEX_INLINE_REGEX);\n    for (const match of latexInline) {\n      content = content.replace(match[0], \"\");\n    }\n  }\n  if (pref.removeLinks) {\n    content = content.replaceAll(\"![[\", \"\");\n    content = content.replaceAll(\"[[\", \"\");\n    content = content.replaceAll(\"]]\", \"\");\n  }\n  return content;\n}\n\nexport function getNoteFileContent(path: string, filter = false) {\n  let content = \"\";\n  content = fs.readFileSync(path, \"utf8\") as string;\n  return filter ? filterContent(content) : content;\n}\n\n/** Reads a list of notes from the vault path */\nexport function loadNotes(vault: Vault): Note[] {\n  console.log(\"Loading Notes for vault: \" + vault.path);\n  const start = performance.now();\n\n  const notes: Note[] = [];\n  const filePaths = getFilePaths(vault);\n  const bookmarkedFilePaths = getBookmarkedNotePaths(vault);\n\n  for (const filePath of filePaths) {\n    const fileName = path.basename(filePath);\n    const title = fileName.replace(/\\.md$/, \"\") || \"default\";\n    const content = getNoteFileContent(filePath, false);\n    const relativePath = path.relative(vault.path, filePath);\n\n    const note: Note = {\n      title,\n      path: filePath,\n      lastModified: fs.statSync(filePath).mtime,\n      tags: tagsForString(content),\n      content,\n      bookmarked: bookmarkedFilePaths.includes(relativePath),\n    };\n\n    notes.push(note);\n  }\n\n  const end = performance.now();\n  console.log(`Finished loading ${notes.length} notes in ${end - start} ms.`);\n\n  return notes.sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime());\n}\n\n/** Gets a list of file paths for all media. */\nfunction getMediaFilePaths(vault: Vault) {\n  const excludedFolders = getExcludedFolders();\n  const files = walkFilesHelper(\n    vault.path,\n    excludedFolders,\n    [...AUDIO_FILE_EXTENSIONS, ...VIDEO_FILE_EXTENSIONS, \".jpg\", \".png\", \".gif\", \".mp4\", \".pdf\"],\n    []\n  );\n  return files;\n}\n\n/** Loads media (images, pdfs, video, audio, etc.) for a given vault from disk. utils.useMedia() is the preferred way of loading media. */\nexport function loadMedia(vault: Vault): Media[] {\n  const medias: Media[] = [];\n  const filePaths = getMediaFilePaths(vault);\n\n  for (const filePath of filePaths) {\n    const title = path.basename(filePath);\n    const icon = getIconFor(filePath);\n\n    const media: Media = {\n      title,\n      path: filePath,\n      icon: icon,\n    };\n    medias.push(media);\n  }\n  return medias;\n}\n\n/** Gets the icon for a given file path. This is used to determine the icon for a media item where the media itself can't be displayed (e.g. video, audio). */\nfunction getIconFor(filePath: string) {\n  const fileExtension = path.extname(filePath);\n  if (VIDEO_FILE_EXTENSIONS.includes(fileExtension)) {\n    return { source: Icon.Video };\n  } else if (AUDIO_FILE_EXTENSIONS.includes(fileExtension)) {\n    return { source: Icon.Microphone };\n  }\n  return { source: filePath };\n}\n", "import YAM<PERSON> from \"yaml\";\nimport { Note } from \"../api/vault/notes/notes.types\";\n\nimport { CODE_BLOCK_REGEX, INLINE_TAGS_REGEX, YAML_FRONTMATTER_REGEX } from \"./constants\";\nimport { sort<PERSON><PERSON><PERSON><PERSON><PERSON>bet } from \"./utils\";\n\nexport function parsedYAMLFrontmatter(str: string) {\n  const frontmatter = str.match(YAML_FRONTMATTER_REGEX);\n  if (frontmatter) {\n    try {\n      return YAML.parse(frontmatter[0].replaceAll(\"---\", \"\"), { logLevel: \"error\" });\n    } catch {\n      //\n    }\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction yamlHas(yaml: any, property: string) {\n  if (Object.prototype.hasOwnProperty.call(yaml, property)) {\n    if (yaml[property]) {\n      return true;\n    }\n  }\n  return false;\n}\n\n//--------------------------------------------------------------------------------\n// Get certain properties from YAML frontmatter\n//--------------------------------------------------------------------------------\n\nexport function yamlPropertyForString(str: string, property: string): string | undefined {\n  const parsedYAML = parsedYAMLFrontmatter(str);\n  if (parsedYAML) {\n    if (yamlHas(parsedYAML, property)) {\n      return parsedYAML[property];\n    }\n  }\n}\n\n//--------------------------------------------------------------------------------\n// Get Tags for a list of notes from both inline tags and YAML frontmatter\n//--------------------------------------------------------------------------------\n\nfunction inlineTagsForNotes(notes: Note[]) {\n  const foundTags: string[] = [];\n  for (const note of notes) {\n    // Ignoring codeblocks to avoid matching hex color codes\n    const cleanedContent = note.content.replaceAll(CODE_BLOCK_REGEX, \"\");\n    const tags = [...cleanedContent.matchAll(INLINE_TAGS_REGEX)];\n    for (const tag of tags) {\n      if (!foundTags.includes(tag[1])) {\n        foundTags.push(tag[1]);\n      }\n    }\n  }\n  return foundTags;\n}\n\nfunction yamlTagsForNotes(notes: Note[]) {\n  const foundTags: string[] = [];\n  for (const note of notes) {\n    const tags = yamlTagsForString(note.content);\n    for (const tag of tags) {\n      if (!foundTags.includes(tag)) {\n        foundTags.push(tag);\n      }\n    }\n  }\n  return foundTags;\n}\n\nexport function tagsForNotes(notes: Note[]) {\n  const foundTags = inlineTagsForNotes(notes);\n  const foundYAMLTags = yamlTagsForNotes(notes);\n  for (const tag of foundYAMLTags) {\n    if (!foundTags.includes(tag)) {\n      foundTags.push(tag);\n    }\n  }\n  return foundTags.sort(sortByAlphabet);\n}\n\n//--------------------------------------------------------------------------------\n// Get Tags for a string from both inline tags and YAML frontmatter\n//--------------------------------------------------------------------------------\n\nexport function inlineTagsForString(str: string) {\n  const foundTags: string[] = [];\n  const tags = [...str.matchAll(INLINE_TAGS_REGEX)];\n  for (const tag of tags) {\n    if (!foundTags.includes(tag[1])) {\n      foundTags.push(tag[1]);\n    }\n  }\n  return foundTags;\n}\n\nexport function yamlTagsForString(str: string) {\n  let foundTags: string[] = [];\n  const parsedYAML = parsedYAMLFrontmatter(str);\n  if (parsedYAML) {\n    if (yamlHas(parsedYAML, \"tag\")) {\n      if (Array.isArray(parsedYAML.tag)) {\n        foundTags = [...parsedYAML.tag];\n      } else if (typeof parsedYAML.tag === \"string\") {\n        foundTags = [...parsedYAML.tag.split(\",\").map((tag: string) => tag.trim())];\n      }\n    } else if (yamlHas(parsedYAML, \"tags\")) {\n      if (Array.isArray(parsedYAML.tags)) {\n        foundTags = [...parsedYAML.tags];\n      } else if (typeof parsedYAML.tags === \"string\") {\n        foundTags = [...parsedYAML.tags.split(\",\").map((tag: string) => tag.trim())];\n      }\n    }\n  }\n  foundTags = foundTags.filter((tag: string) => tag != \"\");\n  return foundTags.map((tag) => \"#\" + tag);\n}\n\nexport function tagsForString(str: string) {\n  const foundTags = inlineTagsForString(str);\n  const foundYAMLTags = yamlTagsForString(str);\n  for (const tag of foundYAMLTags) {\n    if (!foundTags.includes(tag)) {\n      foundTags.push(tag);\n    }\n  }\n  return foundTags.sort(sortByAlphabet);\n}\n", "import { getPreferenceValues } from \"@raycast/api\";\nimport { Vault } from \"../../vault.types\";\nimport { BookmarkEntry, BookmarkFile, BookMarkJson } from \"./bookmarks.types\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { Note } from \"../notes.types\";\nimport { Logger } from \"../../../logger/logger.service\";\n\nconst logger = new Logger(\"Bookmarks\");\n\n/** Flattens BookmarkEntry groups down to simple bookmark entries without groups */\nfunction* flattenBookmarks(bookmarkEntries: BookmarkEntry[]): Generator<BookmarkFile> {\n  for (const item of bookmarkEntries) {\n    if (item.type === \"file\") yield item;\n    if (item.type === \"group\" && item.items) yield* flattenBookmarks(item.items);\n  }\n}\n\nfunction getBookmarksJson(vault: Vault): BookMarkJson | undefined {\n  const { configFileName } = getPreferenceValues();\n  const bookmarksJsonPath = `${vault.path}/${configFileName || \".obsidian\"}/bookmarks.json`;\n  if (!fs.existsSync(bookmarksJsonPath)) {\n    logger.warning(\"No bookmarks JSON found\");\n    return;\n  }\n  const fileContent = fs.readFileSync(bookmarksJsonPath, \"utf-8\");\n  const bookmarkJson = JSON.parse(fileContent) as BookMarkJson;\n  logger.info(bookmarkJson);\n  return bookmarkJson;\n}\n\nfunction writeBookmarksJson(vault: Vault, bookmarksJson: BookMarkJson) {\n  const { configFileName } = getPreferenceValues();\n  const bookmarksJsonPath = `${vault.path}/${configFileName || \".obsidian\"}/bookmarks.json`;\n  fs.writeFileSync(bookmarksJsonPath, JSON.stringify(bookmarksJson, null, 2));\n}\n\nfunction getAllBookmarkFiles(vault: Vault): BookmarkFile[] {\n  const bookmarkJson = getBookmarksJson(vault);\n  if (!bookmarkJson) return [];\n  return Array.from(flattenBookmarks(bookmarkJson.items));\n}\n\nexport function getBookmarkedNotePaths(vault: Vault): string[] {\n  const bookmarkFiles = getAllBookmarkFiles(vault);\n  const notePaths = bookmarkFiles.map((note) => note.path);\n  logger.info(notePaths);\n  return notePaths;\n}\n\n/** Bookmark a note in a vault if it was not bookmarked yet */\nexport function bookmarkNote(vault: Vault, note: Note) {\n  const bookmarksJson = getBookmarksJson(vault);\n  const relativeNotePath = path.relative(vault.path, note.path);\n\n  // Check if the note is already bookmarked\n  const bookmarkedFiles = getAllBookmarkFiles(vault);\n  if (bookmarkedFiles.some((file) => file.path === relativeNotePath)) {\n    logger.info(`Note ${note.title} is already bookmarked`);\n    return;\n  }\n\n  // Create a new bookmark entry\n  const bookmarkedNote: BookmarkFile = {\n    type: \"file\",\n    title: note.title,\n    path: relativeNotePath,\n  };\n\n  // If no bookmarks.json exists, create a new one with just this note\n  if (!bookmarksJson) {\n    const newBookmarksJson: BookMarkJson = {\n      items: [bookmarkedNote],\n    };\n    writeBookmarksJson(vault, newBookmarksJson);\n    return;\n  }\n\n  // Add the note to the root level of bookmarks, preserving the existing structure\n  bookmarksJson.items.push(bookmarkedNote);\n  writeBookmarksJson(vault, bookmarksJson);\n  logger.info(`Bookmarked note: ${note.title}`);\n}\n\n/** Unbookmark a note in a vault if it was bookmarked */\nexport function unbookmarkNote(vault: Vault, note: Note) {\n  const bookmarksJson = getBookmarksJson(vault);\n  if (!bookmarksJson) {\n    logger.warning(\"No bookmarks JSON found, can't unbookmark note.\");\n    return;\n  }\n\n  const notePath = path.relative(vault.path, note.path);\n  let bookmarkFound = false;\n\n  // Function to filter out the bookmark we want to remove\n  const removeBookmark = (items: BookmarkEntry[]): boolean => {\n    for (let i = 0; i < items.length; i++) {\n      const item = items[i];\n\n      // If this is the file we want to remove\n      if (item.type === \"file\" && item.path === notePath) {\n        items.splice(i, 1);\n        return true;\n      }\n\n      // If this is a group, check its items\n      if (item.type === \"group\" && item.items && removeBookmark(item.items)) {\n        return true;\n      }\n    }\n    return false;\n  };\n\n  // Try to remove the bookmark, preserving the structure\n  bookmarkFound = removeBookmark(bookmarksJson.items);\n\n  if (bookmarkFound) {\n    writeBookmarksJson(vault, bookmarksJson);\n    logger.info(`Removed bookmark for note: ${note.title}`);\n  } else {\n    logger.warning(`Note not found in bookmarks: ${note.title}`);\n  }\n}\n", "export class Logger {\n  private readonly name: string;\n\n  constructor(name?: string) {\n    this.name = name || \"Logger\";\n  }\n\n  private timestamp(): string {\n    return new Date().toISOString();\n  }\n\n  private formatMessage(message: unknown): string {\n    if (typeof message === \"string\") {\n      return message;\n    }\n\n    if (message instanceof Error) {\n      return `${message.message}\\n${message.stack}`;\n    }\n\n    if (typeof message === \"object\" && message !== null) {\n      try {\n        return JSON.stringify(message, null, 2);\n      } catch (e) {\n        return String(message);\n      }\n    }\n\n    return String(message);\n  }\n\n  info(message: unknown): void {\n    console.log(`[${this.timestamp()}] [${this.name}] [INFO] ${this.formatMessage(message)}`);\n  }\n\n  success(message: unknown): void {\n    console.log(`[${this.timestamp()}] [${this.name}] [SUCCESS] ${this.formatMessage(message)}`);\n  }\n\n  warning(message: unknown): void {\n    console.log(`[${this.timestamp()}] [${this.name}] [WARNING] ${this.formatMessage(message)}`);\n  }\n\n  error(message: unknown): void {\n    console.log(`[${this.timestamp()}] [${this.name}] [ERROR] ${this.formatMessage(message)}`);\n  }\n\n  debug(message: unknown): void {\n    console.log(`[${this.timestamp()}] [${this.name}] [DEBUG] ${this.formatMessage(message)}`);\n  }\n\n  trace(message: unknown): void {\n    console.log(`[${this.timestamp()}] [${this.name}] [TRACE] ${this.formatMessage(message)}`);\n  }\n}\n", "import { Cache } from \"@raycast/api\";\nimport { BYTES_PER_MEGABYTE } from \"../../utils/constants\";\nimport { Logger } from \"../logger/logger.service\";\nimport { Note } from \"../vault/notes/notes.types\";\nimport { loadNotes } from \"../vault/vault.service\";\nimport { Vault } from \"../vault/vault.types\";\n\n//--------------------------------------------------------------------------------\n// This cache is shared accross all commands.\n//--------------------------------------------------------------------------------\n\nconst logger = new Logger(\"Cache\");\nconst cache = new Cache({ capacity: BYTES_PER_MEGABYTE * 500 });\n\n/**\n * Cache all notes for a given vault.\n *\n * @param vault - Vault to cache notes for\n * @returns The cached notes for the vault\n */\nexport function cacheNotesFor(vault: Vault) {\n  const notes = loadNotes(vault);\n  cache.set(vault.name, JSON.stringify({ lastCached: Date.now(), notes: notes }));\n  return notes;\n}\n\n/**\n * Renews the cache for a given vault by reloading all notes from disk.\n *\n * @param vault - Vault to renew the cache for\n */\nexport function renewCache(vault: Vault) {\n  console.log(\"Renew Cache\");\n  cacheNotesFor(vault);\n}\n\n/**\n * Test if cache exists for a given vault.\n *\n * @param vault - Vault to test if cache exists for\n * @returns true if cache exists for vault\n */\nexport function cacheExistForVault(vault: Vault) {\n  if (cache.has(vault.name)) {\n    return true;\n  } else {\n    console.log(\"Cache does not exist for vault: \" + vault.name);\n  }\n}\n\n/**\n * Updates a note that has already been cached.\n *\n * @param vault - The Vault to update the note in\n * @param note - The updated note\n */\n\nexport function updateNoteInCache(vault: Vault, note: Note) {\n  if (cacheExistForVault(vault)) {\n    const data = JSON.parse(cache.get(vault.name) ?? \"{}\");\n    data.notes = data.notes.map((n: Note) => (n.path === note.path ? note : n));\n    cache.set(vault.name, JSON.stringify(data));\n  }\n}\n\n/**\n * Deletes a note from the cache.\n *\n * @param vault - The Vault to delete the note from\n * @param note - The note to delete from the cache\n */\nexport function deleteNoteFromCache(vault: Vault, note: Note) {\n  if (cacheExistForVault(vault)) {\n    const data = JSON.parse(cache.get(vault.name) ?? \"{}\");\n    data.notes = data.notes.filter((n: Note) => n.path !== note.path);\n    cache.set(vault.name, JSON.stringify(data));\n  }\n}\n\nexport function getNotesFromCache(vault: Vault) {\n  if (cacheExistForVault(vault)) {\n    const data = JSON.parse(cache.get(vault.name) ?? \"{}\");\n    if (data.notes?.length > 0 && data.lastCached > Date.now() - 1000 * 60 * 5) {\n      const notes_ = data.notes as Note[];\n      logger.info(\"Using cached notes.\");\n      return notes_;\n    }\n  }\n  return cacheNotesFor(vault);\n}\n\nexport function clearCache() {\n  cache.clear();\n}\n", "import { getPreferenceValues } from \"@raycast/api\";\nimport { Vault } from \"../vault.types\";\nimport fs from \"fs\";\n\nexport function vaultPluginCheck(vaults: Vault[], plugin: string) {\n  const { configFileName } = getPreferenceValues();\n\n  const vaultsWithoutPlugin: Vault[] = [];\n  const vaultsWithPlugin = vaults.filter((vault: Vault) => {\n    const communityPluginsPath = `${vault.path}/${configFileName || \".obsidian\"}/community-plugins.json`;\n\n    if (!fs.existsSync(communityPluginsPath)) {\n      vaultsWithoutPlugin.push(vault);\n      return false;\n    }\n\n    const plugins: string[] = JSON.parse(fs.readFileSync(communityPluginsPath, \"utf-8\"));\n    const hasPlugin = plugins.includes(plugin);\n\n    if (!hasPlugin) {\n      vaultsWithoutPlugin.push(vault);\n    }\n    return hasPlugin;\n  });\n  return [vaultsWithPlugin, vaultsWithoutPlugin];\n}\n"], "mappings": "yoBAAA,IAAAA,EAAAC,EAAAC,GAAA,cAEA,IAAMC,GAAQ,OAAO,IAAI,YAAY,EAC/BC,GAAM,OAAO,IAAI,eAAe,EAChCC,GAAM,OAAO,IAAI,UAAU,EAC3BC,GAAO,OAAO,IAAI,WAAW,EAC7BC,GAAS,OAAO,IAAI,aAAa,EACjCC,GAAM,OAAO,IAAI,UAAU,EAC3BC,EAAY,OAAO,IAAI,gBAAgB,EACvCC,GAAWC,GAAS,CAAC,CAACA,GAAQ,OAAOA,GAAS,UAAYA,EAAKF,CAAS,IAAMN,GAC9ES,GAAcD,GAAS,CAAC,CAACA,GAAQ,OAAOA,GAAS,UAAYA,EAAKF,CAAS,IAAML,GACjFS,GAASF,GAAS,CAAC,CAACA,GAAQ,OAAOA,GAAS,UAAYA,EAAKF,CAAS,IAAMJ,GAC5ES,GAAUH,GAAS,CAAC,CAACA,GAAQ,OAAOA,GAAS,UAAYA,EAAKF,CAAS,IAAMH,GAC7ES,GAAYJ,GAAS,CAAC,CAACA,GAAQ,OAAOA,GAAS,UAAYA,EAAKF,CAAS,IAAMF,GAC/ES,GAASL,GAAS,CAAC,CAACA,GAAQ,OAAOA,GAAS,UAAYA,EAAKF,CAAS,IAAMD,GAClF,SAASS,GAAaN,EAAM,CACxB,GAAIA,GAAQ,OAAOA,GAAS,SACxB,OAAQA,EAAKF,CAAS,EAAG,CACrB,KAAKJ,GACL,KAAKG,GACD,MAAO,EACf,CACJ,MAAO,EACX,CACA,SAASU,GAAOP,EAAM,CAClB,GAAIA,GAAQ,OAAOA,GAAS,SACxB,OAAQA,EAAKF,CAAS,EAAG,CACrB,KAAKN,GACL,KAAKE,GACL,KAAKE,GACL,KAAKC,GACD,MAAO,EACf,CACJ,MAAO,EACX,CACA,IAAMW,GAAaR,IAAUI,GAASJ,CAAI,GAAKM,GAAaN,CAAI,IAAM,CAAC,CAACA,EAAK,OAE7ET,EAAQ,MAAQC,GAChBD,EAAQ,IAAME,GACdF,EAAQ,IAAMG,GACdH,EAAQ,UAAYO,EACpBP,EAAQ,KAAOI,GACfJ,EAAQ,OAASK,GACjBL,EAAQ,IAAMM,GACdN,EAAQ,UAAYiB,GACpBjB,EAAQ,QAAUQ,GAClBR,EAAQ,aAAee,GACvBf,EAAQ,WAAaU,GACrBV,EAAQ,MAAQW,GAChBX,EAAQ,OAASgB,GACjBhB,EAAQ,OAASY,GACjBZ,EAAQ,SAAWa,GACnBb,EAAQ,MAAQc,KCpDhB,IAAAI,GAAAC,EAAAC,IAAA,cAEA,IAAIC,EAAW,IAETC,EAAQ,OAAO,aAAa,EAC5BC,GAAO,OAAO,eAAe,EAC7BC,EAAS,OAAO,aAAa,EA+BnC,SAASC,GAAMC,EAAMC,EAAS,CAC1B,IAAMC,EAAWC,GAAYF,CAAO,EAChCN,EAAS,WAAWK,CAAI,EACbI,GAAO,KAAMJ,EAAK,SAAUE,EAAU,OAAO,OAAO,CAACF,CAAI,CAAC,CAAC,IAC3DF,IACPE,EAAK,SAAW,MAGpBI,GAAO,KAAMJ,EAAME,EAAU,OAAO,OAAO,CAAC,CAAC,CAAC,CACtD,CAKAH,GAAM,MAAQH,EAEdG,GAAM,KAAOF,GAEbE,GAAM,OAASD,EACf,SAASM,GAAOC,EAAKL,EAAMC,EAASK,EAAM,CACtC,IAAMC,EAAOC,GAAYH,EAAKL,EAAMC,EAASK,CAAI,EACjD,GAAIX,EAAS,OAAOY,CAAI,GAAKZ,EAAS,OAAOY,CAAI,EAC7C,OAAAE,GAAYJ,EAAKC,EAAMC,CAAI,EACpBH,GAAOC,EAAKE,EAAMN,EAASK,CAAI,EAE1C,GAAI,OAAOC,GAAS,UAChB,GAAIZ,EAAS,aAAaK,CAAI,EAAG,CAC7BM,EAAO,OAAO,OAAOA,EAAK,OAAON,CAAI,CAAC,EACtC,QAASU,EAAI,EAAGA,EAAIV,EAAK,MAAM,OAAQ,EAAEU,EAAG,CACxC,IAAMC,EAAKP,GAAOM,EAAGV,EAAK,MAAMU,CAAC,EAAGT,EAASK,CAAI,EACjD,GAAI,OAAOK,GAAO,SACdD,EAAIC,EAAK,MACR,IAAIA,IAAOf,EACZ,OAAOA,EACFe,IAAOb,IACZE,EAAK,MAAM,OAAOU,EAAG,CAAC,EACtBA,GAAK,GAEb,CACJ,SACSf,EAAS,OAAOK,CAAI,EAAG,CAC5BM,EAAO,OAAO,OAAOA,EAAK,OAAON,CAAI,CAAC,EACtC,IAAMY,EAAKR,GAAO,MAAOJ,EAAK,IAAKC,EAASK,CAAI,EAChD,GAAIM,IAAOhB,EACP,OAAOA,EACFgB,IAAOd,IACZE,EAAK,IAAM,MACf,IAAMa,EAAKT,GAAO,QAASJ,EAAK,MAAOC,EAASK,CAAI,EACpD,GAAIO,IAAOjB,EACP,OAAOA,EACFiB,IAAOf,IACZE,EAAK,MAAQ,KACrB,EAEJ,OAAOO,CACX,CAgCA,eAAeO,GAAWd,EAAMC,EAAS,CACrC,IAAMC,EAAWC,GAAYF,CAAO,EAChCN,EAAS,WAAWK,CAAI,EACb,MAAMe,GAAY,KAAMf,EAAK,SAAUE,EAAU,OAAO,OAAO,CAACF,CAAI,CAAC,CAAC,IACtEF,IACPE,EAAK,SAAW,MAGpB,MAAMe,GAAY,KAAMf,EAAME,EAAU,OAAO,OAAO,CAAC,CAAC,CAAC,CACjE,CAKAY,GAAW,MAAQlB,EAEnBkB,GAAW,KAAOjB,GAElBiB,GAAW,OAAShB,EACpB,eAAeiB,GAAYV,EAAKL,EAAMC,EAASK,EAAM,CACjD,IAAMC,EAAO,MAAMC,GAAYH,EAAKL,EAAMC,EAASK,CAAI,EACvD,GAAIX,EAAS,OAAOY,CAAI,GAAKZ,EAAS,OAAOY,CAAI,EAC7C,OAAAE,GAAYJ,EAAKC,EAAMC,CAAI,EACpBQ,GAAYV,EAAKE,EAAMN,EAASK,CAAI,EAE/C,GAAI,OAAOC,GAAS,UAChB,GAAIZ,EAAS,aAAaK,CAAI,EAAG,CAC7BM,EAAO,OAAO,OAAOA,EAAK,OAAON,CAAI,CAAC,EACtC,QAASU,EAAI,EAAGA,EAAIV,EAAK,MAAM,OAAQ,EAAEU,EAAG,CACxC,IAAMC,EAAK,MAAMI,GAAYL,EAAGV,EAAK,MAAMU,CAAC,EAAGT,EAASK,CAAI,EAC5D,GAAI,OAAOK,GAAO,SACdD,EAAIC,EAAK,MACR,IAAIA,IAAOf,EACZ,OAAOA,EACFe,IAAOb,IACZE,EAAK,MAAM,OAAOU,EAAG,CAAC,EACtBA,GAAK,GAEb,CACJ,SACSf,EAAS,OAAOK,CAAI,EAAG,CAC5BM,EAAO,OAAO,OAAOA,EAAK,OAAON,CAAI,CAAC,EACtC,IAAMY,EAAK,MAAMG,GAAY,MAAOf,EAAK,IAAKC,EAASK,CAAI,EAC3D,GAAIM,IAAOhB,EACP,OAAOA,EACFgB,IAAOd,IACZE,EAAK,IAAM,MACf,IAAMa,EAAK,MAAME,GAAY,QAASf,EAAK,MAAOC,EAASK,CAAI,EAC/D,GAAIO,IAAOjB,EACP,OAAOA,EACFiB,IAAOf,IACZE,EAAK,MAAQ,KACrB,EAEJ,OAAOO,CACX,CACA,SAASJ,GAAYF,EAAS,CAC1B,OAAI,OAAOA,GAAY,WAClBA,EAAQ,YAAcA,EAAQ,MAAQA,EAAQ,OACxC,OAAO,OAAO,CACjB,MAAOA,EAAQ,KACf,IAAKA,EAAQ,KACb,OAAQA,EAAQ,KAChB,IAAKA,EAAQ,IACjB,EAAGA,EAAQ,OAAS,CAChB,IAAKA,EAAQ,MACb,OAAQA,EAAQ,MAChB,IAAKA,EAAQ,KACjB,EAAGA,EAAQ,YAAc,CACrB,IAAKA,EAAQ,WACb,IAAKA,EAAQ,UACjB,EAAGA,CAAO,EAEPA,CACX,CACA,SAASO,GAAYH,EAAKL,EAAMC,EAASK,EAAM,CAC3C,GAAI,OAAOL,GAAY,WACnB,OAAOA,EAAQI,EAAKL,EAAMM,CAAI,EAClC,GAAIX,EAAS,MAAMK,CAAI,EACnB,OAAOC,EAAQ,MAAMI,EAAKL,EAAMM,CAAI,EACxC,GAAIX,EAAS,MAAMK,CAAI,EACnB,OAAOC,EAAQ,MAAMI,EAAKL,EAAMM,CAAI,EACxC,GAAIX,EAAS,OAAOK,CAAI,EACpB,OAAOC,EAAQ,OAAOI,EAAKL,EAAMM,CAAI,EACzC,GAAIX,EAAS,SAASK,CAAI,EACtB,OAAOC,EAAQ,SAASI,EAAKL,EAAMM,CAAI,EAC3C,GAAIX,EAAS,QAAQK,CAAI,EACrB,OAAOC,EAAQ,QAAQI,EAAKL,EAAMM,CAAI,CAE9C,CACA,SAASG,GAAYJ,EAAKC,EAAMN,EAAM,CAClC,IAAMgB,EAASV,EAAKA,EAAK,OAAS,CAAC,EACnC,GAAIX,EAAS,aAAaqB,CAAM,EAC5BA,EAAO,MAAMX,CAAG,EAAIL,UAEfL,EAAS,OAAOqB,CAAM,EACvBX,IAAQ,MACRW,EAAO,IAAMhB,EAEbgB,EAAO,MAAQhB,UAEdL,EAAS,WAAWqB,CAAM,EAC/BA,EAAO,SAAWhB,MAEjB,CACD,IAAMiB,EAAKtB,EAAS,QAAQqB,CAAM,EAAI,QAAU,SAChD,MAAM,IAAI,MAAM,4BAA4BC,CAAE,SAAS,CAC3D,CACJ,CAEAvB,GAAQ,MAAQK,GAChBL,GAAQ,WAAaoB,KC3OrB,IAAAI,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAQ,KAENC,GAAc,CAChB,IAAK,MACL,IAAK,MACL,IAAK,MACL,IAAK,MACL,IAAK,MACL,IAAK,KACT,EACMC,GAAiBC,GAAOA,EAAG,QAAQ,aAAcC,GAAMH,GAAYG,CAAE,CAAC,EACtEC,GAAN,MAAMC,CAAW,CACb,YAAYC,EAAMC,EAAM,CAKpB,KAAK,SAAW,KAEhB,KAAK,OAAS,GACd,KAAK,KAAO,OAAO,OAAO,CAAC,EAAGF,EAAW,YAAaC,CAAI,EAC1D,KAAK,KAAO,OAAO,OAAO,CAAC,EAAGD,EAAW,YAAaE,CAAI,CAC9D,CACA,OAAQ,CACJ,IAAMC,EAAO,IAAIH,EAAW,KAAK,KAAM,KAAK,IAAI,EAChD,OAAAG,EAAK,SAAW,KAAK,SACdA,CACX,CAKA,YAAa,CACT,IAAMC,EAAM,IAAIJ,EAAW,KAAK,KAAM,KAAK,IAAI,EAC/C,OAAQ,KAAK,KAAK,QAAS,CACvB,IAAK,MACD,KAAK,eAAiB,GACtB,MACJ,IAAK,MACD,KAAK,eAAiB,GACtB,KAAK,KAAO,CACR,SAAUA,EAAW,YAAY,SACjC,QAAS,KACb,EACA,KAAK,KAAO,OAAO,OAAO,CAAC,EAAGA,EAAW,WAAW,EACpD,KACR,CACA,OAAOI,CACX,CAKA,IAAIC,EAAMC,EAAS,CACX,KAAK,iBACL,KAAK,KAAO,CAAE,SAAUN,EAAW,YAAY,SAAU,QAAS,KAAM,EACxE,KAAK,KAAO,OAAO,OAAO,CAAC,EAAGA,EAAW,WAAW,EACpD,KAAK,eAAiB,IAE1B,IAAMO,EAAQF,EAAK,KAAK,EAAE,MAAM,QAAQ,EAClCG,EAAOD,EAAM,MAAM,EACzB,OAAQC,EAAM,CACV,IAAK,OAAQ,CACT,GAAID,EAAM,SAAW,IACjBD,EAAQ,EAAG,iDAAiD,EACxDC,EAAM,OAAS,GACf,MAAO,GAEf,GAAM,CAACE,EAAQC,CAAM,EAAIH,EACzB,YAAK,KAAKE,CAAM,EAAIC,EACb,EACX,CACA,IAAK,QAAS,CAEV,GADA,KAAK,KAAK,SAAW,GACjBH,EAAM,SAAW,EACjB,OAAAD,EAAQ,EAAG,iDAAiD,EACrD,GAEX,GAAM,CAACK,CAAO,EAAIJ,EAClB,GAAII,IAAY,OAASA,IAAY,MACjC,YAAK,KAAK,QAAUA,EACb,GAEN,CACD,IAAMC,EAAU,aAAa,KAAKD,CAAO,EACzC,OAAAL,EAAQ,EAAG,4BAA4BK,CAAO,GAAIC,CAAO,EAClD,EACX,CACJ,CACA,QACI,OAAAN,EAAQ,EAAG,qBAAqBE,CAAI,GAAI,EAAI,EACrC,EACf,CACJ,CAOA,QAAQK,EAAQP,EAAS,CACrB,GAAIO,IAAW,IACX,MAAO,IACX,GAAIA,EAAO,CAAC,IAAM,IACd,OAAAP,EAAQ,oBAAoBO,CAAM,EAAE,EAC7B,KAEX,GAAIA,EAAO,CAAC,IAAM,IAAK,CACnB,IAAMC,EAAWD,EAAO,MAAM,EAAG,EAAE,EACnC,OAAIC,IAAa,KAAOA,IAAa,MACjCR,EAAQ,qCAAqCO,CAAM,cAAc,EAC1D,OAEPA,EAAOA,EAAO,OAAS,CAAC,IAAM,KAC9BP,EAAQ,iCAAiC,EACtCQ,EACX,CACA,GAAM,CAAC,CAAEL,EAAQM,CAAM,EAAIF,EAAO,MAAM,iBAAiB,EACpDE,GACDT,EAAQ,OAAOO,CAAM,oBAAoB,EAC7C,IAAMH,EAAS,KAAK,KAAKD,CAAM,EAC/B,GAAIC,EACA,GAAI,CACA,OAAOA,EAAS,mBAAmBK,CAAM,CAC7C,OACOC,EAAO,CACV,OAAAV,EAAQ,OAAOU,CAAK,CAAC,EACd,IACX,CAEJ,OAAIP,IAAW,IACJI,GACXP,EAAQ,0BAA0BO,CAAM,EAAE,EACnC,KACX,CAKA,UAAUI,EAAK,CACX,OAAW,CAACR,EAAQC,CAAM,IAAK,OAAO,QAAQ,KAAK,IAAI,EACnD,GAAIO,EAAI,WAAWP,CAAM,EACrB,OAAOD,EAASb,GAAcqB,EAAI,UAAUP,EAAO,MAAM,CAAC,EAElE,OAAOO,EAAI,CAAC,IAAM,IAAMA,EAAM,KAAKA,CAAG,GAC1C,CACA,SAASC,EAAK,CACV,IAAMC,EAAQ,KAAK,KAAK,SAClB,CAAC,SAAS,KAAK,KAAK,SAAW,KAAK,EAAE,EACtC,CAAC,EACDC,EAAa,OAAO,QAAQ,KAAK,IAAI,EACvCC,EACJ,GAAIH,GAAOE,EAAW,OAAS,GAAK3B,GAAS,OAAOyB,EAAI,QAAQ,EAAG,CAC/D,IAAMhB,EAAO,CAAC,EACdR,GAAM,MAAMwB,EAAI,SAAU,CAACI,EAAMC,IAAS,CAClC9B,GAAS,OAAO8B,CAAI,GAAKA,EAAK,MAC9BrB,EAAKqB,EAAK,GAAG,EAAI,GACzB,CAAC,EACDF,EAAW,OAAO,KAAKnB,CAAI,CAC/B,MAEImB,EAAW,CAAC,EAChB,OAAW,CAACZ,EAAQC,CAAM,IAAKU,EACvBX,IAAW,MAAQC,IAAW,uBAE9B,CAACQ,GAAOG,EAAS,KAAKxB,GAAMA,EAAG,WAAWa,CAAM,CAAC,IACjDS,EAAM,KAAK,QAAQV,CAAM,IAAIC,CAAM,EAAE,EAE7C,OAAOS,EAAM,KAAK;AAAA,CAAI,CAC1B,CACJ,EACApB,GAAW,YAAc,CAAE,SAAU,GAAO,QAAS,KAAM,EAC3DA,GAAW,YAAc,CAAE,KAAM,oBAAqB,EAEtDP,GAAQ,WAAaO,KCjLrB,IAAAyB,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAQ,KAOZ,SAASC,GAAcC,EAAQ,CAC3B,GAAI,sBAAsB,KAAKA,CAAM,EAAG,CAEpC,IAAMC,EAAM,6DADD,KAAK,UAAUD,CAAM,CAC2C,GAC3E,MAAM,IAAI,MAAMC,CAAG,CACvB,CACA,MAAO,EACX,CACA,SAASC,GAAYC,EAAM,CACvB,IAAMC,EAAU,IAAI,IACpB,OAAAN,GAAM,MAAMK,EAAM,CACd,MAAME,EAAMC,EAAM,CACVA,EAAK,QACLF,EAAQ,IAAIE,EAAK,MAAM,CAC/B,CACJ,CAAC,EACMF,CACX,CAEA,SAASG,GAAcC,EAAQC,EAAS,CACpC,QAASC,EAAI,GAAS,EAAEA,EAAG,CACvB,IAAMC,EAAO,GAAGH,CAAM,GAAGE,CAAC,GAC1B,GAAI,CAACD,EAAQ,IAAIE,CAAI,EACjB,OAAOA,CACf,CACJ,CACA,SAASC,GAAkBC,EAAKL,EAAQ,CACpC,IAAMM,EAAe,CAAC,EAChBC,EAAgB,IAAI,IACtBC,EAAc,KAClB,MAAO,CACH,SAAWC,GAAW,CAClBH,EAAa,KAAKG,CAAM,EACnBD,IACDA,EAAcd,GAAYW,CAAG,GACjC,IAAMb,EAASO,GAAcC,EAAQQ,CAAW,EAChD,OAAAA,EAAY,IAAIhB,CAAM,EACfA,CACX,EAMA,WAAY,IAAM,CACd,QAAWiB,KAAUH,EAAc,CAC/B,IAAMI,EAAMH,EAAc,IAAIE,CAAM,EACpC,GAAI,OAAOC,GAAQ,UACfA,EAAI,SACHrB,GAAS,SAASqB,EAAI,IAAI,GAAKrB,GAAS,aAAaqB,EAAI,IAAI,GAC9DA,EAAI,KAAK,OAASA,EAAI,WAErB,CACD,IAAMC,EAAQ,IAAI,MAAM,4DAA4D,EACpF,MAAAA,EAAM,OAASF,EACTE,CACV,CACJ,CACJ,EACA,cAAAJ,CACJ,CACJ,CAEAnB,GAAQ,cAAgBG,GACxBH,GAAQ,YAAcM,GACtBN,GAAQ,kBAAoBgB,GAC5BhB,GAAQ,cAAgBW,KC5ExB,IAAAa,GAAAC,EAAAC,IAAA,cASA,SAASC,GAAaC,EAASC,EAAKC,EAAKC,EAAK,CAC1C,GAAIA,GAAO,OAAOA,GAAQ,SACtB,GAAI,MAAM,QAAQA,CAAG,EACjB,QAAS,EAAI,EAAGC,EAAMD,EAAI,OAAQ,EAAIC,EAAK,EAAE,EAAG,CAC5C,IAAMC,EAAKF,EAAI,CAAC,EACVG,EAAKP,GAAaC,EAASG,EAAK,OAAO,CAAC,EAAGE,CAAE,EAE/CC,IAAO,OACP,OAAOH,EAAI,CAAC,EACPG,IAAOD,IACZF,EAAI,CAAC,EAAIG,EACjB,SAEKH,aAAe,IACpB,QAAWI,KAAK,MAAM,KAAKJ,EAAI,KAAK,CAAC,EAAG,CACpC,IAAME,EAAKF,EAAI,IAAII,CAAC,EACdD,EAAKP,GAAaC,EAASG,EAAKI,EAAGF,CAAE,EACvCC,IAAO,OACPH,EAAI,OAAOI,CAAC,EACPD,IAAOD,GACZF,EAAI,IAAII,EAAGD,CAAE,CACrB,SAEKH,aAAe,IACpB,QAAWE,KAAM,MAAM,KAAKF,CAAG,EAAG,CAC9B,IAAMG,EAAKP,GAAaC,EAASG,EAAKE,EAAIA,CAAE,EACxCC,IAAO,OACPH,EAAI,OAAOE,CAAE,EACRC,IAAOD,IACZF,EAAI,OAAOE,CAAE,EACbF,EAAI,IAAIG,CAAE,EAElB,KAGA,QAAW,CAACC,EAAGF,CAAE,IAAK,OAAO,QAAQF,CAAG,EAAG,CACvC,IAAMG,EAAKP,GAAaC,EAASG,EAAKI,EAAGF,CAAE,EACvCC,IAAO,OACP,OAAOH,EAAII,CAAC,EACPD,IAAOD,IACZF,EAAII,CAAC,EAAID,EACjB,CAGR,OAAON,EAAQ,KAAKC,EAAKC,EAAKC,CAAG,CACrC,CAEAL,GAAQ,aAAeC,KCxDvB,IAAAS,EAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IAYf,SAASC,GAAKC,EAAOC,EAAKC,EAAK,CAE3B,GAAI,MAAM,QAAQF,CAAK,EACnB,OAAOA,EAAM,IAAI,CAACG,EAAG,IAAMJ,GAAKI,EAAG,OAAO,CAAC,EAAGD,CAAG,CAAC,EACtD,GAAIF,GAAS,OAAOA,EAAM,QAAW,WAAY,CAE7C,GAAI,CAACE,GAAO,CAACJ,GAAS,UAAUE,CAAK,EACjC,OAAOA,EAAM,OAAOC,EAAKC,CAAG,EAChC,IAAME,EAAO,CAAE,WAAY,EAAG,MAAO,EAAG,IAAK,MAAU,EACvDF,EAAI,QAAQ,IAAIF,EAAOI,CAAI,EAC3BF,EAAI,SAAWG,GAAO,CAClBD,EAAK,IAAMC,EACX,OAAOH,EAAI,QACf,EACA,IAAMG,EAAML,EAAM,OAAOC,EAAKC,CAAG,EACjC,OAAIA,EAAI,UACJA,EAAI,SAASG,CAAG,EACbA,CACX,CACA,OAAI,OAAOL,GAAU,UAAY,CAACE,GAAK,KAC5B,OAAOF,CAAK,EAChBA,CACX,CAEAH,GAAQ,KAAOE,KCtCf,IAAAO,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAe,KACfC,GAAW,IACXC,GAAO,IAELC,GAAN,KAAe,CACX,YAAYC,EAAM,CACd,OAAO,eAAe,KAAMH,GAAS,UAAW,CAAE,MAAOG,CAAK,CAAC,CACnE,CAEA,OAAQ,CACJ,IAAMC,EAAO,OAAO,OAAO,OAAO,eAAe,IAAI,EAAG,OAAO,0BAA0B,IAAI,CAAC,EAC9F,OAAI,KAAK,QACLA,EAAK,MAAQ,KAAK,MAAM,MAAM,GAC3BA,CACX,CAEA,KAAKC,EAAK,CAAE,SAAAC,EAAU,cAAAC,EAAe,SAAAC,EAAU,QAAAC,CAAQ,EAAI,CAAC,EAAG,CAC3D,GAAI,CAACT,GAAS,WAAWK,CAAG,EACxB,MAAM,IAAI,UAAU,iCAAiC,EACzD,IAAMK,EAAM,CACR,QAAS,IAAI,IACb,IAAAL,EACA,KAAM,GACN,SAAUC,IAAa,GACvB,aAAc,GACd,cAAe,OAAOC,GAAkB,SAAWA,EAAgB,GACvE,EACMI,EAAMV,GAAK,KAAK,KAAM,GAAIS,CAAG,EACnC,GAAI,OAAOF,GAAa,WACpB,OAAW,CAAE,MAAAI,EAAO,IAAAD,CAAI,IAAKD,EAAI,QAAQ,OAAO,EAC5CF,EAASG,EAAKC,CAAK,EAC3B,OAAO,OAAOH,GAAY,WACpBV,GAAa,aAAaU,EAAS,CAAE,GAAIE,CAAI,EAAG,GAAIA,CAAG,EACvDA,CACV,CACJ,EAEAb,GAAQ,SAAWI,KCvCnB,IAAAW,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAU,KACVC,GAAQ,KACRC,GAAW,IACXC,GAAO,KACPC,GAAO,IAELC,GAAN,cAAoBF,GAAK,QAAS,CAC9B,YAAYG,EAAQ,CAChB,MAAMJ,GAAS,KAAK,EACpB,KAAK,OAASI,EACd,OAAO,eAAe,KAAM,MAAO,CAC/B,KAAM,CACF,MAAM,IAAI,MAAM,8BAA8B,CAClD,CACJ,CAAC,CACL,CAKA,QAAQC,EAAK,CACT,IAAIC,EACJ,OAAAP,GAAM,MAAMM,EAAK,CACb,KAAM,CAACE,EAAMC,IAAS,CAClB,GAAIA,IAAS,KACT,OAAOT,GAAM,MAAM,MACnBS,EAAK,SAAW,KAAK,SACrBF,EAAQE,EAChB,CACJ,CAAC,EACMF,CACX,CACA,OAAOG,EAAMC,EAAK,CACd,GAAI,CAACA,EACD,MAAO,CAAE,OAAQ,KAAK,MAAO,EACjC,GAAM,CAAE,QAAAZ,EAAS,IAAAO,EAAK,cAAAM,CAAc,EAAID,EAClCN,EAAS,KAAK,QAAQC,CAAG,EAC/B,GAAI,CAACD,EAAQ,CACT,IAAMQ,EAAM,+DAA+D,KAAK,MAAM,GACtF,MAAM,IAAI,eAAeA,CAAG,CAChC,CACA,IAAIC,EAAOf,EAAQ,IAAIM,CAAM,EAO7B,GANKS,IAEDX,GAAK,KAAKE,EAAQ,KAAMM,CAAG,EAC3BG,EAAOf,EAAQ,IAAIM,CAAM,GAGzB,CAACS,GAAQA,EAAK,MAAQ,OAAW,CACjC,IAAMD,EAAM,yDACZ,MAAM,IAAI,eAAeA,CAAG,CAChC,CACA,GAAID,GAAiB,IACjBE,EAAK,OAAS,EACVA,EAAK,aAAe,IACpBA,EAAK,WAAaC,GAAcT,EAAKD,EAAQN,CAAO,GACpDe,EAAK,MAAQA,EAAK,WAAaF,GAAe,CAC9C,IAAMC,EAAM,+DACZ,MAAM,IAAI,eAAeA,CAAG,CAChC,CAEJ,OAAOC,EAAK,GAChB,CACA,SAASH,EAAKK,EAAYC,EAAc,CACpC,IAAMC,EAAM,IAAI,KAAK,MAAM,GAC3B,GAAIP,EAAK,CAEL,GADAZ,GAAQ,cAAc,KAAK,MAAM,EAC7BY,EAAI,QAAQ,kBAAoB,CAACA,EAAI,QAAQ,IAAI,KAAK,MAAM,EAAG,CAC/D,IAAME,EAAM,+DAA+D,KAAK,MAAM,GACtF,MAAM,IAAI,MAAMA,CAAG,CACvB,CACA,GAAIF,EAAI,YACJ,MAAO,GAAGO,CAAG,GACrB,CACA,OAAOA,CACX,CACJ,EACA,SAASH,GAAcT,EAAKG,EAAMV,EAAS,CACvC,GAAIE,GAAS,QAAQQ,CAAI,EAAG,CACxB,IAAMJ,EAASI,EAAK,QAAQH,CAAG,EACzBa,EAASpB,GAAWM,GAAUN,EAAQ,IAAIM,CAAM,EACtD,OAAOc,EAASA,EAAO,MAAQA,EAAO,WAAa,CACvD,SACSlB,GAAS,aAAaQ,CAAI,EAAG,CAClC,IAAIW,EAAQ,EACZ,QAAWC,KAAQZ,EAAK,MAAO,CAC3B,IAAMa,EAAIP,GAAcT,EAAKe,EAAMtB,CAAO,EACtCuB,EAAIF,IACJA,EAAQE,EAChB,CACA,OAAOF,CACX,SACSnB,GAAS,OAAOQ,CAAI,EAAG,CAC5B,IAAMc,EAAKR,GAAcT,EAAKG,EAAK,IAAKV,CAAO,EACzCyB,EAAKT,GAAcT,EAAKG,EAAK,MAAOV,CAAO,EACjD,OAAO,KAAK,IAAIwB,EAAIC,CAAE,CAC1B,CACA,MAAO,EACX,CAEA1B,GAAQ,MAAQM,KCtGhB,IAAAqB,EAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAO,KACPC,GAAO,IAELC,GAAiBC,GAAU,CAACA,GAAU,OAAOA,GAAU,YAAc,OAAOA,GAAU,SACtFC,EAAN,cAAqBJ,GAAK,QAAS,CAC/B,YAAYG,EAAO,CACf,MAAMJ,GAAS,MAAM,EACrB,KAAK,MAAQI,CACjB,CACA,OAAOE,EAAKC,EAAK,CACb,OAAOA,GAAK,KAAO,KAAK,MAAQL,GAAK,KAAK,KAAK,MAAOI,EAAKC,CAAG,CAClE,CACA,UAAW,CACP,OAAO,OAAO,KAAK,KAAK,CAC5B,CACJ,EACAF,EAAO,aAAe,eACtBA,EAAO,cAAgB,gBACvBA,EAAO,MAAQ,QACfA,EAAO,aAAe,eACtBA,EAAO,aAAe,eAEtBN,GAAQ,OAASM,EACjBN,GAAQ,cAAgBI,KC1BxB,IAAAK,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAQ,KACRC,GAAW,IACXC,GAAS,IAEPC,GAAmB,qBACzB,SAASC,GAAcC,EAAOC,EAASC,EAAM,CACzC,GAAID,EAAS,CACT,IAAME,EAAQD,EAAK,OAAOE,GAAKA,EAAE,MAAQH,CAAO,EAC1CI,EAASF,EAAM,KAAKC,GAAK,CAACA,EAAE,MAAM,GAAKD,EAAM,CAAC,EACpD,GAAI,CAACE,EACD,MAAM,IAAI,MAAM,OAAOJ,CAAO,YAAY,EAC9C,OAAOI,CACX,CACA,OAAOH,EAAK,KAAKE,GAAKA,EAAE,WAAWJ,CAAK,GAAK,CAACI,EAAE,MAAM,CAC1D,CACA,SAASE,GAAWN,EAAOC,EAASM,EAAK,CAGrC,GAFIX,GAAS,WAAWI,CAAK,IACzBA,EAAQA,EAAM,UACdJ,GAAS,OAAOI,CAAK,EACrB,OAAOA,EACX,GAAIJ,GAAS,OAAOI,CAAK,EAAG,CACxB,IAAMQ,EAAMD,EAAI,OAAOX,GAAS,GAAG,EAAE,aAAaW,EAAI,OAAQ,KAAMA,CAAG,EACvE,OAAAC,EAAI,MAAM,KAAKR,CAAK,EACbQ,CACX,EACIR,aAAiB,QACjBA,aAAiB,QACjBA,aAAiB,SAChB,OAAO,OAAW,KAAeA,aAAiB,UAGnDA,EAAQA,EAAM,QAAQ,GAE1B,GAAM,CAAE,sBAAAS,EAAuB,SAAAC,EAAU,SAAAC,EAAU,OAAAC,EAAQ,cAAAC,CAAc,EAAIN,EAGzEO,EACJ,GAAIL,GAAyBT,GAAS,OAAOA,GAAU,SAAU,CAE7D,GADAc,EAAMD,EAAc,IAAIb,CAAK,EACzBc,EACA,OAAKA,EAAI,SACLA,EAAI,OAASJ,EAASV,CAAK,GACxB,IAAIL,GAAM,MAAMmB,EAAI,MAAM,EAGjCA,EAAM,CAAE,OAAQ,KAAM,KAAM,IAAK,EACjCD,EAAc,IAAIb,EAAOc,CAAG,CAEpC,CACIb,GAAS,WAAW,IAAI,IACxBA,EAAUH,GAAmBG,EAAQ,MAAM,CAAC,GAChD,IAAII,EAASN,GAAcC,EAAOC,EAASW,EAAO,IAAI,EACtD,GAAI,CAACP,EAAQ,CAKT,GAJIL,GAAS,OAAOA,EAAM,QAAW,aAEjCA,EAAQA,EAAM,OAAO,GAErB,CAACA,GAAS,OAAOA,GAAU,SAAU,CACrC,IAAMe,EAAO,IAAIlB,GAAO,OAAOG,CAAK,EACpC,OAAIc,IACAA,EAAI,KAAOC,GACRA,CACX,CACAV,EACIL,aAAiB,IACXY,EAAOhB,GAAS,GAAG,EACnB,OAAO,YAAY,OAAOI,CAAK,EAC3BY,EAAOhB,GAAS,GAAG,EACnBgB,EAAOhB,GAAS,GAAG,CACrC,CACIe,IACAA,EAASN,CAAM,EACf,OAAOE,EAAI,UAEf,IAAMQ,EAAOV,GAAQ,WACfA,EAAO,WAAWE,EAAI,OAAQP,EAAOO,CAAG,EACxC,OAAOF,GAAQ,WAAW,MAAS,WAC/BA,EAAO,UAAU,KAAKE,EAAI,OAAQP,EAAOO,CAAG,EAC5C,IAAIV,GAAO,OAAOG,CAAK,EACjC,OAAIC,EACAc,EAAK,IAAMd,EACLI,EAAO,UACbU,EAAK,IAAMV,EAAO,KAClBS,IACAA,EAAI,KAAOC,GACRA,CACX,CAEArB,GAAQ,WAAaY,KC1FrB,IAAAU,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAa,KACbC,EAAW,IACXC,GAAO,KAEX,SAASC,GAAmBC,EAAQC,EAAMC,EAAO,CAC7C,IAAIC,EAAID,EACR,QAAS,EAAID,EAAK,OAAS,EAAG,GAAK,EAAG,EAAE,EAAG,CACvC,IAAMG,EAAIH,EAAK,CAAC,EAChB,GAAI,OAAOG,GAAM,UAAY,OAAO,UAAUA,CAAC,GAAKA,GAAK,EAAG,CACxD,IAAMC,EAAI,CAAC,EACXA,EAAED,CAAC,EAAID,EACPA,EAAIE,CACR,MAEIF,EAAI,IAAI,IAAI,CAAC,CAACC,EAAGD,CAAC,CAAC,CAAC,CAE5B,CACA,OAAOP,GAAW,WAAWO,EAAG,OAAW,CACvC,sBAAuB,GACvB,cAAe,GACf,SAAU,IAAM,CACZ,MAAM,IAAI,MAAM,8CAA8C,CAClE,EACA,OAAAH,EACA,cAAe,IAAI,GACvB,CAAC,CACL,CAGA,IAAMM,GAAeL,GAASA,GAAQ,MACjC,OAAOA,GAAS,UAAY,CAAC,CAACA,EAAK,OAAO,QAAQ,EAAE,EAAE,KAAK,EAAE,KAC5DM,GAAN,cAAyBT,GAAK,QAAS,CACnC,YAAYU,EAAMR,EAAQ,CACtB,MAAMQ,CAAI,EACV,OAAO,eAAe,KAAM,SAAU,CAClC,MAAOR,EACP,aAAc,GACd,WAAY,GACZ,SAAU,EACd,CAAC,CACL,CAMA,MAAMA,EAAQ,CACV,IAAMS,EAAO,OAAO,OAAO,OAAO,eAAe,IAAI,EAAG,OAAO,0BAA0B,IAAI,CAAC,EAC9F,OAAIT,IACAS,EAAK,OAAST,GAClBS,EAAK,MAAQA,EAAK,MAAM,IAAIC,GAAMb,EAAS,OAAOa,CAAE,GAAKb,EAAS,OAAOa,CAAE,EAAIA,EAAG,MAAMV,CAAM,EAAIU,CAAE,EAChG,KAAK,QACLD,EAAK,MAAQ,KAAK,MAAM,MAAM,GAC3BA,CACX,CAMA,MAAMR,EAAMC,EAAO,CACf,GAAII,GAAYL,CAAI,EAChB,KAAK,IAAIC,CAAK,MACb,CACD,GAAM,CAACS,EAAK,GAAGC,CAAI,EAAIX,EACjBY,EAAO,KAAK,IAAIF,EAAK,EAAI,EAC/B,GAAId,EAAS,aAAagB,CAAI,EAC1BA,EAAK,MAAMD,EAAMV,CAAK,UACjBW,IAAS,QAAa,KAAK,OAChC,KAAK,IAAIF,EAAKZ,GAAmB,KAAK,OAAQa,EAAMV,CAAK,CAAC,MAE1D,OAAM,IAAI,MAAM,+BAA+BS,CAAG,qBAAqBC,CAAI,EAAE,CACrF,CACJ,CAKA,SAASX,EAAM,CACX,GAAM,CAACU,EAAK,GAAGC,CAAI,EAAIX,EACvB,GAAIW,EAAK,SAAW,EAChB,OAAO,KAAK,OAAOD,CAAG,EAC1B,IAAME,EAAO,KAAK,IAAIF,EAAK,EAAI,EAC/B,GAAId,EAAS,aAAagB,CAAI,EAC1B,OAAOA,EAAK,SAASD,CAAI,EAEzB,MAAM,IAAI,MAAM,+BAA+BD,CAAG,qBAAqBC,CAAI,EAAE,CACrF,CAMA,MAAMX,EAAMa,EAAY,CACpB,GAAM,CAACH,EAAK,GAAGC,CAAI,EAAIX,EACjBY,EAAO,KAAK,IAAIF,EAAK,EAAI,EAC/B,OAAIC,EAAK,SAAW,EACT,CAACE,GAAcjB,EAAS,SAASgB,CAAI,EAAIA,EAAK,MAAQA,EAEtDhB,EAAS,aAAagB,CAAI,EAAIA,EAAK,MAAMD,EAAME,CAAU,EAAI,MAC5E,CACA,iBAAiBC,EAAa,CAC1B,OAAO,KAAK,MAAM,MAAMF,GAAQ,CAC5B,GAAI,CAAChB,EAAS,OAAOgB,CAAI,EACrB,MAAO,GACX,IAAM,EAAIA,EAAK,MACf,OAAQ,GAAK,MACRE,GACGlB,EAAS,SAAS,CAAC,GACnB,EAAE,OAAS,MACX,CAAC,EAAE,eACH,CAAC,EAAE,SACH,CAAC,EAAE,GACf,CAAC,CACL,CAIA,MAAMI,EAAM,CACR,GAAM,CAACU,EAAK,GAAGC,CAAI,EAAIX,EACvB,GAAIW,EAAK,SAAW,EAChB,OAAO,KAAK,IAAID,CAAG,EACvB,IAAME,EAAO,KAAK,IAAIF,EAAK,EAAI,EAC/B,OAAOd,EAAS,aAAagB,CAAI,EAAIA,EAAK,MAAMD,CAAI,EAAI,EAC5D,CAKA,MAAMX,EAAMC,EAAO,CACf,GAAM,CAACS,EAAK,GAAGC,CAAI,EAAIX,EACvB,GAAIW,EAAK,SAAW,EAChB,KAAK,IAAID,EAAKT,CAAK,MAElB,CACD,IAAMW,EAAO,KAAK,IAAIF,EAAK,EAAI,EAC/B,GAAId,EAAS,aAAagB,CAAI,EAC1BA,EAAK,MAAMD,EAAMV,CAAK,UACjBW,IAAS,QAAa,KAAK,OAChC,KAAK,IAAIF,EAAKZ,GAAmB,KAAK,OAAQa,EAAMV,CAAK,CAAC,MAE1D,OAAM,IAAI,MAAM,+BAA+BS,CAAG,qBAAqBC,CAAI,EAAE,CACrF,CACJ,CACJ,EAEAjB,GAAQ,WAAaY,GACrBZ,GAAQ,mBAAqBI,GAC7BJ,GAAQ,YAAcW,KCtJtB,IAAAU,GAAAC,EAAAC,IAAA,cASA,IAAMC,GAAoBC,GAAQA,EAAI,QAAQ,kBAAmB,GAAG,EACpE,SAASC,GAAcC,EAASC,EAAQ,CACpC,MAAI,QAAQ,KAAKD,CAAO,EACbA,EAAQ,UAAU,CAAC,EACvBC,EAASD,EAAQ,QAAQ,aAAcC,CAAM,EAAID,CAC5D,CACA,IAAME,GAAc,CAACJ,EAAKG,EAAQD,IAAYF,EAAI,SAAS;AAAA,CAAI,EACzDC,GAAcC,EAASC,CAAM,EAC7BD,EAAQ,SAAS;AAAA,CAAI,EACjB;AAAA,EAAOD,GAAcC,EAASC,CAAM,GACnCH,EAAI,SAAS,GAAG,EAAI,GAAK,KAAOE,EAE3CJ,GAAQ,cAAgBG,GACxBH,GAAQ,YAAcM,GACtBN,GAAQ,iBAAmBC,KCvB3B,IAAAM,GAAAC,EAAAC,IAAA,cAEA,IAAMC,GAAY,OACZC,GAAa,QACbC,GAAc,SAMpB,SAASC,GAAcC,EAAMC,EAAQC,EAAO,OAAQ,CAAE,cAAAC,EAAe,UAAAC,EAAY,GAAI,gBAAAC,EAAkB,GAAI,OAAAC,EAAQ,WAAAC,CAAW,EAAI,CAAC,EAAG,CAClI,GAAI,CAACH,GAAaA,EAAY,EAC1B,OAAOJ,EACPI,EAAYC,IACZA,EAAkB,GACtB,IAAMG,EAAU,KAAK,IAAI,EAAIH,EAAiB,EAAID,EAAYH,EAAO,MAAM,EAC3E,GAAID,EAAK,QAAUQ,EACf,OAAOR,EACX,IAAMS,EAAQ,CAAC,EACTC,EAAe,CAAC,EAClBC,EAAMP,EAAYH,EAAO,OACzB,OAAOE,GAAkB,WACrBA,EAAgBC,EAAY,KAAK,IAAI,EAAGC,CAAe,EACvDI,EAAM,KAAK,CAAC,EAEZE,EAAMP,EAAYD,GAE1B,IAAIS,EACAC,EACAC,EAAW,GACXC,EAAI,GACJC,EAAW,GACXC,EAAS,GACTf,IAASL,KACTkB,EAAIG,GAAyBlB,EAAMe,EAAGd,EAAO,MAAM,EAC/Cc,IAAM,KACNJ,EAAMI,EAAIP,IAElB,QAASW,EAAKA,EAAKnB,EAAMe,GAAK,CAAE,GAAK,CACjC,GAAIb,IAASJ,IAAeqB,IAAO,KAAM,CAErC,OADAH,EAAWD,EACHf,EAAKe,EAAI,CAAC,EAAG,CACjB,IAAK,IACDA,GAAK,EACL,MACJ,IAAK,IACDA,GAAK,EACL,MACJ,IAAK,IACDA,GAAK,EACL,MACJ,QACIA,GAAK,CACb,CACAE,EAASF,CACb,CACA,GAAII,IAAO;AAAA,EACHjB,IAASL,KACTkB,EAAIG,GAAyBlB,EAAMe,EAAGd,EAAO,MAAM,GACvDU,EAAMI,EAAId,EAAO,OAASO,EAC1BI,EAAQ,WAEP,CACD,GAAIO,IAAO,KACPN,GACAA,IAAS,KACTA,IAAS;AAAA,GACTA,IAAS,IAAM,CAEf,IAAMO,EAAOpB,EAAKe,EAAI,CAAC,EACnBK,GAAQA,IAAS,KAAOA,IAAS;AAAA,GAAQA,IAAS,MAClDR,EAAQG,EAChB,CACA,GAAIA,GAAKJ,EACL,GAAIC,EACAH,EAAM,KAAKG,CAAK,EAChBD,EAAMC,EAAQJ,EACdI,EAAQ,eAEHV,IAASJ,GAAa,CAE3B,KAAOe,IAAS,KAAOA,IAAS,KAC5BA,EAAOM,EACPA,EAAKnB,EAAMe,GAAK,CAAE,EAClBD,EAAW,GAGf,IAAMO,EAAIN,EAAIE,EAAS,EAAIF,EAAI,EAAIC,EAAW,EAE9C,GAAIN,EAAaW,CAAC,EACd,OAAOrB,EACXS,EAAM,KAAKY,CAAC,EACZX,EAAaW,CAAC,EAAI,GAClBV,EAAMU,EAAIb,EACVI,EAAQ,MACZ,MAEIE,EAAW,EAGvB,CACAD,EAAOM,CACX,CAGA,GAFIL,GAAYP,GACZA,EAAW,EACXE,EAAM,SAAW,EACjB,OAAOT,EACPM,GACAA,EAAO,EACX,IAAIgB,EAAMtB,EAAK,MAAM,EAAGS,EAAM,CAAC,CAAC,EAChC,QAASM,EAAI,EAAGA,EAAIN,EAAM,OAAQ,EAAEM,EAAG,CACnC,IAAMQ,EAAOd,EAAMM,CAAC,EACdJ,EAAMF,EAAMM,EAAI,CAAC,GAAKf,EAAK,OAC7BuB,IAAS,EACTD,EAAM;AAAA,EAAKrB,CAAM,GAAGD,EAAK,MAAM,EAAGW,CAAG,CAAC,IAElCT,IAASJ,IAAeY,EAAaa,CAAI,IACzCD,GAAO,GAAGtB,EAAKuB,CAAI,CAAC,MACxBD,GAAO;AAAA,EAAKrB,CAAM,GAAGD,EAAK,MAAMuB,EAAO,EAAGZ,CAAG,CAAC,GAEtD,CACA,OAAOW,CACX,CAKA,SAASJ,GAAyBlB,EAAMe,EAAGd,EAAQ,CAC/C,IAAIU,EAAMI,EACNS,EAAQT,EAAI,EACZI,EAAKnB,EAAKwB,CAAK,EACnB,KAAOL,IAAO,KAAOA,IAAO,KACxB,GAAIJ,EAAIS,EAAQvB,EACZkB,EAAKnB,EAAK,EAAEe,CAAC,MAEZ,CACD,GACII,EAAKnB,EAAK,EAAEe,CAAC,QACRI,GAAMA,IAAO;AAAA,GACtBR,EAAMI,EACNS,EAAQT,EAAI,EACZI,EAAKnB,EAAKwB,CAAK,CACnB,CAEJ,OAAOb,CACX,CAEAhB,GAAQ,WAAaE,GACrBF,GAAQ,UAAYC,GACpBD,GAAQ,YAAcG,GACtBH,GAAQ,cAAgBI,KCtJxB,IAAA0B,GAAAC,EAAAC,IAAA,cAEA,IAAIC,EAAS,IACTC,EAAgB,KAEdC,GAAiB,CAACC,EAAKC,KAAa,CACtC,cAAeA,EAAUD,EAAI,OAAO,OAASA,EAAI,cACjD,UAAWA,EAAI,QAAQ,UACvB,gBAAiBA,EAAI,QAAQ,eACjC,GAGME,GAA0BC,GAAQ,mBAAmB,KAAKA,CAAG,EACnE,SAASC,GAAoBD,EAAKE,EAAWC,EAAc,CACvD,GAAI,CAACD,GAAaA,EAAY,EAC1B,MAAO,GACX,IAAME,EAAQF,EAAYC,EACpBE,EAASL,EAAI,OACnB,GAAIK,GAAUD,EACV,MAAO,GACX,QAASE,EAAI,EAAGC,EAAQ,EAAGD,EAAID,EAAQ,EAAEC,EACrC,GAAIN,EAAIM,CAAC,IAAM;AAAA,EAAM,CACjB,GAAIA,EAAIC,EAAQH,EACZ,MAAO,GAEX,GADAG,EAAQD,EAAI,EACRD,EAASE,GAASH,EAClB,MAAO,EACf,CAEJ,MAAO,EACX,CACA,SAASI,GAAmBC,EAAOZ,EAAK,CACpC,IAAMa,EAAO,KAAK,UAAUD,CAAK,EACjC,GAAIZ,EAAI,QAAQ,mBACZ,OAAOa,EACX,GAAM,CAAE,YAAAC,CAAY,EAAId,EAClBe,EAAqBf,EAAI,QAAQ,+BACjCgB,EAAShB,EAAI,SAAWE,GAAuBU,CAAK,EAAI,KAAO,IACjET,EAAM,GACNO,EAAQ,EACZ,QAASD,EAAI,EAAGQ,EAAKJ,EAAKJ,CAAC,EAAGQ,EAAIA,EAAKJ,EAAK,EAAEJ,CAAC,EAQ3C,GAPIQ,IAAO,KAAOJ,EAAKJ,EAAI,CAAC,IAAM,MAAQI,EAAKJ,EAAI,CAAC,IAAM,MAEtDN,GAAOU,EAAK,MAAMH,EAAOD,CAAC,EAAI,MAC9BA,GAAK,EACLC,EAAQD,EACRQ,EAAK,MAELA,IAAO,KACP,OAAQJ,EAAKJ,EAAI,CAAC,EAAG,CACjB,IAAK,IACD,CACIN,GAAOU,EAAK,MAAMH,EAAOD,CAAC,EAC1B,IAAMS,EAAOL,EAAK,OAAOJ,EAAI,EAAG,CAAC,EACjC,OAAQS,EAAM,CACV,IAAK,OACDf,GAAO,MACP,MACJ,IAAK,OACDA,GAAO,MACP,MACJ,IAAK,OACDA,GAAO,MACP,MACJ,IAAK,OACDA,GAAO,MACP,MACJ,IAAK,OACDA,GAAO,MACP,MACJ,IAAK,OACDA,GAAO,MACP,MACJ,IAAK,OACDA,GAAO,MACP,MACJ,IAAK,OACDA,GAAO,MACP,MACJ,QACQe,EAAK,OAAO,EAAG,CAAC,IAAM,KACtBf,GAAO,MAAQe,EAAK,OAAO,CAAC,EAE5Bf,GAAOU,EAAK,OAAOJ,EAAG,CAAC,CACnC,CACAA,GAAK,EACLC,EAAQD,EAAI,CAChB,CACA,MACJ,IAAK,IACD,GAAIK,GACAD,EAAKJ,EAAI,CAAC,IAAM,KAChBI,EAAK,OAASE,EACdN,GAAK,MAEJ,CAGD,IADAN,GAAOU,EAAK,MAAMH,EAAOD,CAAC,EAAI;AAAA;AAAA,EACvBI,EAAKJ,EAAI,CAAC,IAAM,MACnBI,EAAKJ,EAAI,CAAC,IAAM,KAChBI,EAAKJ,EAAI,CAAC,IAAM,KAChBN,GAAO;AAAA,EACPM,GAAK,EAETN,GAAOa,EAEHH,EAAKJ,EAAI,CAAC,IAAM,MAChBN,GAAO,MACXM,GAAK,EACLC,EAAQD,EAAI,CAChB,CACA,MACJ,QACIA,GAAK,CACb,CAER,OAAAN,EAAMO,EAAQP,EAAMU,EAAK,MAAMH,CAAK,EAAIG,EACjCC,EACDX,EACAL,EAAc,cAAcK,EAAKa,EAAQlB,EAAc,YAAaC,GAAeC,EAAK,EAAK,CAAC,CACxG,CACA,SAASmB,GAAmBP,EAAOZ,EAAK,CACpC,GAAIA,EAAI,QAAQ,cAAgB,IAC3BA,EAAI,aAAeY,EAAM,SAAS;AAAA,CAAI,GACvC,kBAAkB,KAAKA,CAAK,EAE5B,OAAOD,GAAmBC,EAAOZ,CAAG,EACxC,IAAMgB,EAAShB,EAAI,SAAWE,GAAuBU,CAAK,EAAI,KAAO,IAC/DQ,EAAM,IAAMR,EAAM,QAAQ,KAAM,IAAI,EAAE,QAAQ,OAAQ;AAAA,EAAOI,CAAM,EAAE,EAAI,IAC/E,OAAOhB,EAAI,YACLoB,EACAtB,EAAc,cAAcsB,EAAKJ,EAAQlB,EAAc,UAAWC,GAAeC,EAAK,EAAK,CAAC,CACtG,CACA,SAASqB,GAAaT,EAAOZ,EAAK,CAC9B,GAAM,CAAE,YAAAsB,CAAY,EAAItB,EAAI,QACxBuB,EACJ,GAAID,IAAgB,GAChBC,EAAKZ,OACJ,CACD,IAAMa,EAAYZ,EAAM,SAAS,GAAG,EAC9Ba,EAAYb,EAAM,SAAS,GAAG,EAChCY,GAAa,CAACC,EACdF,EAAKJ,GACAM,GAAa,CAACD,EACnBD,EAAKZ,GAELY,EAAKD,EAAcH,GAAqBR,EAChD,CACA,OAAOY,EAAGX,EAAOZ,CAAG,CACxB,CAGA,IAAI0B,GACJ,GAAI,CACAA,GAAmB,IAAI,OAAO;AAAA;AAAA;AAAA,KAA0B,GAAG,CAC/D,MACM,CACFA,GAAmB,cACvB,CACA,SAASC,GAAY,CAAE,QAAAC,EAAS,KAAAC,EAAM,MAAAjB,CAAM,EAAGZ,EAAK8B,EAAWC,EAAa,CACxE,GAAM,CAAE,WAAAC,EAAY,cAAAC,EAAe,UAAA5B,CAAU,EAAIL,EAAI,QAGrD,GAAI,CAACgC,GAAc,YAAY,KAAKpB,CAAK,GAAK,QAAQ,KAAKA,CAAK,EAC5D,OAAOS,GAAaT,EAAOZ,CAAG,EAElC,IAAMgB,EAAShB,EAAI,SACdA,EAAI,kBAAoBE,GAAuBU,CAAK,EAAI,KAAO,IAC9DsB,EAAUF,IAAe,UACzB,GACAA,IAAe,UAAYH,IAAShC,EAAO,OAAO,aAC9C,GACAgC,IAAShC,EAAO,OAAO,cACnB,GACA,CAACO,GAAoBQ,EAAOP,EAAWW,EAAO,MAAM,EAClE,GAAI,CAACJ,EACD,OAAOsB,EAAU;AAAA,EAAQ;AAAA,EAE7B,IAAIC,EACAC,EACJ,IAAKA,EAAWxB,EAAM,OAAQwB,EAAW,EAAG,EAAEA,EAAU,CACpD,IAAMnB,EAAKL,EAAMwB,EAAW,CAAC,EAC7B,GAAInB,IAAO;AAAA,GAAQA,IAAO,KAAQA,IAAO,IACrC,KACR,CACA,IAAIoB,EAAMzB,EAAM,UAAUwB,CAAQ,EAC5BE,EAAWD,EAAI,QAAQ;AAAA,CAAI,EAC7BC,IAAa,GACbH,EAAQ,IAEHvB,IAAUyB,GAAOC,IAAaD,EAAI,OAAS,GAChDF,EAAQ,IACJJ,GACAA,EAAY,GAGhBI,EAAQ,GAERE,IACAzB,EAAQA,EAAM,MAAM,EAAG,CAACyB,EAAI,MAAM,EAC9BA,EAAIA,EAAI,OAAS,CAAC,IAAM;AAAA,IACxBA,EAAMA,EAAI,MAAM,EAAG,EAAE,GACzBA,EAAMA,EAAI,QAAQX,GAAkB,KAAKV,CAAM,EAAE,GAGrD,IAAIuB,EAAiB,GACjBC,EACAC,EAAa,GACjB,IAAKD,EAAW,EAAGA,EAAW5B,EAAM,OAAQ,EAAE4B,EAAU,CACpD,IAAMvB,EAAKL,EAAM4B,CAAQ,EACzB,GAAIvB,IAAO,IACPsB,EAAiB,WACZtB,IAAO;AAAA,EACZwB,EAAaD,MAEb,MACR,CACA,IAAI9B,EAAQE,EAAM,UAAU,EAAG6B,EAAaD,EAAWC,EAAa,EAAID,CAAQ,EAC5E9B,IACAE,EAAQA,EAAM,UAAUF,EAAM,MAAM,EACpCA,EAAQA,EAAM,QAAQ,OAAQ,KAAKM,CAAM,EAAE,GAI/C,IAAI0B,GAAUH,EAFKvB,EAAS,IAAM,IAEU,IAAMmB,EAMlD,GALIP,IACAc,GAAU,IAAMT,EAAcL,EAAQ,QAAQ,aAAc,GAAG,CAAC,EAC5DE,GACAA,EAAU,GAEd,CAACI,EAAS,CACV,IAAMS,EAAc/B,EACf,QAAQ,OAAQ;AAAA,GAAM,EACtB,QAAQ,iDAAkD,MAAM,EAEhE,QAAQ,OAAQ,KAAKI,CAAM,EAAE,EAC9B4B,EAAkB,GAChBC,EAAc9C,GAAeC,EAAK,EAAI,EACxCgC,IAAe,UAAYH,IAAShC,EAAO,OAAO,eAClDgD,EAAY,WAAa,IAAM,CAC3BD,EAAkB,EACtB,GAEJ,IAAME,EAAOhD,EAAc,cAAc,GAAGY,CAAK,GAAGiC,CAAW,GAAGN,CAAG,GAAIrB,EAAQlB,EAAc,WAAY+C,CAAW,EACtH,GAAI,CAACD,EACD,MAAO,IAAIF,CAAM;AAAA,EAAK1B,CAAM,GAAG8B,CAAI,EAC3C,CACA,OAAAlC,EAAQA,EAAM,QAAQ,OAAQ,KAAKI,CAAM,EAAE,EACpC,IAAI0B,CAAM;AAAA,EAAK1B,CAAM,GAAGN,CAAK,GAAGE,CAAK,GAAGyB,CAAG,EACtD,CACA,SAASU,GAAYC,EAAMhD,EAAK8B,EAAWC,EAAa,CACpD,GAAM,CAAE,KAAAF,EAAM,MAAAjB,CAAM,EAAIoC,EAClB,CAAE,aAAAC,EAAc,YAAAnC,EAAa,OAAAE,EAAQ,WAAAkC,EAAY,OAAAC,CAAO,EAAInD,EAClE,GAAKc,GAAeF,EAAM,SAAS;AAAA,CAAI,GAClCuC,GAAU,WAAW,KAAKvC,CAAK,EAChC,OAAOS,GAAaT,EAAOZ,CAAG,EAElC,GAAI,CAACY,GACD,oFAAoF,KAAKA,CAAK,EAO9F,OAAOE,GAAeqC,GAAU,CAACvC,EAAM,SAAS;AAAA,CAAI,EAC9CS,GAAaT,EAAOZ,CAAG,EACvB2B,GAAYqB,EAAMhD,EAAK8B,EAAWC,CAAW,EAEvD,GAAI,CAACjB,GACD,CAACqC,GACDtB,IAAShC,EAAO,OAAO,OACvBe,EAAM,SAAS;AAAA,CAAI,EAEnB,OAAOe,GAAYqB,EAAMhD,EAAK8B,EAAWC,CAAW,EAExD,GAAI7B,GAAuBU,CAAK,EAAG,CAC/B,GAAII,IAAW,GACX,OAAAhB,EAAI,iBAAmB,GAChB2B,GAAYqB,EAAMhD,EAAK8B,EAAWC,CAAW,EAEnD,GAAIjB,GAAeE,IAAWkC,EAC/B,OAAO7B,GAAaT,EAAOZ,CAAG,CAEtC,CACA,IAAMG,EAAMS,EAAM,QAAQ,OAAQ;AAAA,EAAOI,CAAM,EAAE,EAIjD,GAAIiC,EAAc,CACd,IAAMG,EAAQC,GAAQA,EAAI,SAAWA,EAAI,MAAQ,yBAA2BA,EAAI,MAAM,KAAKlD,CAAG,EACxF,CAAE,OAAAmD,EAAQ,KAAAC,CAAK,EAAIvD,EAAI,IAAI,OACjC,GAAIuD,EAAK,KAAKH,CAAI,GAAKE,GAAQ,KAAKF,CAAI,EACpC,OAAO/B,GAAaT,EAAOZ,CAAG,CACtC,CACA,OAAOc,EACDX,EACAL,EAAc,cAAcK,EAAKa,EAAQlB,EAAc,UAAWC,GAAeC,EAAK,EAAK,CAAC,CACtG,CACA,SAASwD,GAAgBR,EAAMhD,EAAK8B,EAAWC,EAAa,CACxD,GAAM,CAAE,YAAAjB,EAAa,OAAAqC,CAAO,EAAInD,EAC1ByD,EAAK,OAAOT,EAAK,OAAU,SAC3BA,EACA,OAAO,OAAO,CAAC,EAAGA,EAAM,CAAE,MAAO,OAAOA,EAAK,KAAK,CAAE,CAAC,EACvD,CAAE,KAAAnB,CAAK,EAAImB,EACXnB,IAAShC,EAAO,OAAO,cAEnB,kDAAkD,KAAK4D,EAAG,KAAK,IAC/D5B,EAAOhC,EAAO,OAAO,cAE7B,IAAM6D,EAAcC,GAAU,CAC1B,OAAQA,EAAO,CACX,KAAK9D,EAAO,OAAO,aACnB,KAAKA,EAAO,OAAO,cACf,OAAOiB,GAAeqC,EAChB9B,GAAaoC,EAAG,MAAOzD,CAAG,EAC1B2B,GAAY8B,EAAIzD,EAAK8B,EAAWC,CAAW,EACrD,KAAKlC,EAAO,OAAO,aACf,OAAOc,GAAmB8C,EAAG,MAAOzD,CAAG,EAC3C,KAAKH,EAAO,OAAO,aACf,OAAOsB,GAAmBsC,EAAG,MAAOzD,CAAG,EAC3C,KAAKH,EAAO,OAAO,MACf,OAAOkD,GAAYU,EAAIzD,EAAK8B,EAAWC,CAAW,EACtD,QACI,OAAO,IACf,CACJ,EACIX,EAAMsC,EAAW7B,CAAI,EACzB,GAAIT,IAAQ,KAAM,CACd,GAAM,CAAE,eAAAwC,EAAgB,kBAAAC,CAAkB,EAAI7D,EAAI,QAC5C8D,EAAKhD,GAAe8C,GAAmBC,EAE7C,GADAzC,EAAMsC,EAAWI,CAAC,EACd1C,IAAQ,KACR,MAAM,IAAI,MAAM,mCAAmC0C,CAAC,EAAE,CAC9D,CACA,OAAO1C,CACX,CAEAxB,GAAQ,gBAAkB4D,KClV1B,IAAAO,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAU,KACVC,EAAW,IACXC,GAAmB,KACnBC,GAAkB,KAEtB,SAASC,GAAuBC,EAAKC,EAAS,CAC1C,IAAMC,EAAM,OAAO,OAAO,CACtB,WAAY,GACZ,cAAeL,GAAiB,iBAChC,eAAgB,KAChB,kBAAmB,QACnB,WAAY,KACZ,mBAAoB,GACpB,+BAAgC,GAChC,SAAU,QACV,sBAAuB,GACvB,UAAW,GACX,UAAW,GACX,gBAAiB,GACjB,QAAS,OACT,WAAY,GACZ,YAAa,KACb,QAAS,OACT,iBAAkB,EACtB,EAAGG,EAAI,OAAO,gBAAiBC,CAAO,EAClCE,EACJ,OAAQD,EAAI,gBAAiB,CACzB,IAAK,QACDC,EAAS,GACT,MACJ,IAAK,OACDA,EAAS,GACT,MACJ,QACIA,EAAS,IACjB,CACA,MAAO,CACH,QAAS,IAAI,IACb,IAAAH,EACA,sBAAuBE,EAAI,sBAAwB,IAAM,GACzD,OAAQ,GACR,WAAY,OAAOA,EAAI,QAAW,SAAW,IAAI,OAAOA,EAAI,MAAM,EAAI,KACtE,OAAAC,EACA,QAASD,CACb,CACJ,CACA,SAASE,GAAaC,EAAMC,EAAM,CAC9B,GAAIA,EAAK,IAAK,CACV,IAAMC,EAAQF,EAAK,OAAOG,GAAKA,EAAE,MAAQF,EAAK,GAAG,EACjD,GAAIC,EAAM,OAAS,EACf,OAAOA,EAAM,KAAKC,GAAKA,EAAE,SAAWF,EAAK,MAAM,GAAKC,EAAM,CAAC,CACnE,CACA,IAAIE,EACAC,EACJ,GAAId,EAAS,SAASU,CAAI,EAAG,CACzBI,EAAMJ,EAAK,MACX,IAAIC,EAAQF,EAAK,OAAOG,GAAKA,EAAE,WAAWE,CAAG,CAAC,EAC9C,GAAIH,EAAM,OAAS,EAAG,CAClB,IAAMI,EAAYJ,EAAM,OAAOC,GAAKA,EAAE,IAAI,EACtCG,EAAU,OAAS,IACnBJ,EAAQI,EAChB,CACAF,EACIF,EAAM,KAAKC,GAAKA,EAAE,SAAWF,EAAK,MAAM,GAAKC,EAAM,KAAKC,GAAK,CAACA,EAAE,MAAM,CAC9E,MAEIE,EAAMJ,EACNG,EAASJ,EAAK,KAAKG,GAAKA,EAAE,WAAaE,aAAeF,EAAE,SAAS,EAErE,GAAI,CAACC,EAAQ,CACT,IAAMG,EAAOF,GAAK,aAAa,MAAQ,OAAOA,EAC9C,MAAM,IAAI,MAAM,wBAAwBE,CAAI,QAAQ,CACxD,CACA,OAAOH,CACX,CAEA,SAASI,GAAeC,EAAML,EAAQ,CAAE,QAASM,EAAW,IAAAf,CAAI,EAAG,CAC/D,GAAI,CAACA,EAAI,WACL,MAAO,GACX,IAAMgB,EAAQ,CAAC,EACTC,GAAUrB,EAAS,SAASkB,CAAI,GAAKlB,EAAS,aAAakB,CAAI,IAAMA,EAAK,OAC5EG,GAAUtB,GAAQ,cAAcsB,CAAM,IACtCF,EAAU,IAAIE,CAAM,EACpBD,EAAM,KAAK,IAAIC,CAAM,EAAE,GAE3B,IAAMC,EAAMJ,EAAK,IAAMA,EAAK,IAAML,EAAO,QAAU,KAAOA,EAAO,IACjE,OAAIS,GACAF,EAAM,KAAKhB,EAAI,WAAW,UAAUkB,CAAG,CAAC,EACrCF,EAAM,KAAK,GAAG,CACzB,CACA,SAASG,GAAUb,EAAMc,EAAKC,EAAWC,EAAa,CAClD,GAAI1B,EAAS,OAAOU,CAAI,EACpB,OAAOA,EAAK,SAASc,EAAKC,EAAWC,CAAW,EACpD,GAAI1B,EAAS,QAAQU,CAAI,EAAG,CACxB,GAAIc,EAAI,IAAI,WACR,OAAOd,EAAK,SAASc,CAAG,EAC5B,GAAIA,EAAI,iBAAiB,IAAId,CAAI,EAC7B,MAAM,IAAI,UAAU,yDAAyD,EAGzEc,EAAI,gBACJA,EAAI,gBAAgB,IAAId,CAAI,EAE5Bc,EAAI,gBAAkB,IAAI,IAAI,CAACd,CAAI,CAAC,EACxCA,EAAOA,EAAK,QAAQc,EAAI,GAAG,CAEnC,CACA,IAAIX,EACEK,EAAOlB,EAAS,OAAOU,CAAI,EAC3BA,EACAc,EAAI,IAAI,WAAWd,EAAM,CAAE,SAAUiB,GAAMd,EAASc,CAAG,CAAC,EACzDd,IACDA,EAASL,GAAagB,EAAI,IAAI,OAAO,KAAMN,CAAI,GACnD,IAAME,EAAQH,GAAeC,EAAML,EAAQW,CAAG,EAC1CJ,EAAM,OAAS,IACfI,EAAI,eAAiBA,EAAI,eAAiB,GAAKJ,EAAM,OAAS,GAClE,IAAMQ,EAAM,OAAOf,EAAO,WAAc,WAClCA,EAAO,UAAUK,EAAMM,EAAKC,EAAWC,CAAW,EAClD1B,EAAS,SAASkB,CAAI,EAClBhB,GAAgB,gBAAgBgB,EAAMM,EAAKC,EAAWC,CAAW,EACjER,EAAK,SAASM,EAAKC,EAAWC,CAAW,EACnD,OAAKN,EAEEpB,EAAS,SAASkB,CAAI,GAAKU,EAAI,CAAC,IAAM,KAAOA,EAAI,CAAC,IAAM,IACzD,GAAGR,CAAK,IAAIQ,CAAG,GACf,GAAGR,CAAK;AAAA,EAAKI,EAAI,MAAM,GAAGI,CAAG,GAHxBA,CAIf,CAEA9B,GAAQ,uBAAyBK,GACjCL,GAAQ,UAAYyB,KCnIpB,IAAAM,GAAAC,EAAAC,IAAA,cAEA,IAAIC,EAAW,IACXC,GAAS,IACTC,GAAY,KACZC,GAAmB,KAEvB,SAASC,GAAc,CAAE,IAAAC,EAAK,MAAAC,CAAM,EAAGC,EAAKC,EAAWC,EAAa,CAChE,GAAM,CAAE,cAAAC,EAAe,IAAAC,EAAK,OAAAC,EAAQ,WAAAC,EAAY,QAAS,CAAE,cAAAC,EAAe,UAAAC,EAAW,WAAAC,CAAW,CAAE,EAAIT,EAClGU,EAAcjB,EAAS,OAAOK,CAAG,GAAKA,EAAI,SAAY,KAC1D,GAAIW,EAAY,CACZ,GAAIC,EACA,MAAM,IAAI,MAAM,kDAAkD,EAEtE,GAAIjB,EAAS,aAAaK,CAAG,GAAM,CAACL,EAAS,OAAOK,CAAG,GAAK,OAAOA,GAAQ,SAAW,CAClF,IAAMa,EAAM,6DACZ,MAAM,IAAI,MAAMA,CAAG,CACvB,CACJ,CACA,IAAIC,EAAc,CAACH,IACd,CAACX,GACGY,GAAcX,GAAS,MAAQ,CAACC,EAAI,QACrCP,EAAS,aAAaK,CAAG,IACxBL,EAAS,SAASK,CAAG,EAChBA,EAAI,OAASJ,GAAO,OAAO,cAAgBI,EAAI,OAASJ,GAAO,OAAO,cACtE,OAAOI,GAAQ,WAC7BE,EAAM,OAAO,OAAO,CAAC,EAAGA,EAAK,CACzB,cAAe,GACf,YAAa,CAACY,IAAgBH,GAAc,CAACN,GAC7C,OAAQE,EAASC,CACrB,CAAC,EACD,IAAIO,EAAiB,GACjBC,EAAY,GACZC,EAAMpB,GAAU,UAAUG,EAAKE,EAAK,IAAOa,EAAiB,GAAO,IAAOC,EAAY,EAAK,EAC/F,GAAI,CAACF,GAAe,CAACZ,EAAI,QAAUe,EAAI,OAAS,KAAM,CAClD,GAAIN,EACA,MAAM,IAAI,MAAM,8EAA8E,EAClGG,EAAc,EAClB,CACA,GAAIZ,EAAI,QACJ,GAAIG,GAAiBJ,GAAS,KAC1B,OAAIc,GAAkBZ,GAClBA,EAAU,EACPc,IAAQ,GAAK,IAAMH,EAAc,KAAKG,CAAG,GAAKA,UAGnDZ,GAAiB,CAACM,GAAgBV,GAAS,MAAQa,EACzD,OAAAG,EAAM,KAAKA,CAAG,GACVL,GAAc,CAACG,EACfE,GAAOnB,GAAiB,YAAYmB,EAAKf,EAAI,OAAQO,EAAcG,CAAU,CAAC,EAEzEI,GAAaZ,GAClBA,EAAY,EACTa,EAEPF,IACAH,EAAa,MACbE,GACIF,IACAK,GAAOnB,GAAiB,YAAYmB,EAAKf,EAAI,OAAQO,EAAcG,CAAU,CAAC,GAClFK,EAAM,KAAKA,CAAG;AAAA,EAAKV,CAAM,MAGzBU,EAAM,GAAGA,CAAG,IACRL,IACAK,GAAOnB,GAAiB,YAAYmB,EAAKf,EAAI,OAAQO,EAAcG,CAAU,CAAC,IAEtF,IAAIM,EAAKC,EAAKC,EACVzB,EAAS,OAAOM,CAAK,GACrBiB,EAAM,CAAC,CAACjB,EAAM,YACdkB,EAAMlB,EAAM,cACZmB,EAAenB,EAAM,UAGrBiB,EAAM,GACNC,EAAM,KACNC,EAAe,KACXnB,GAAS,OAAOA,GAAU,WAC1BA,EAAQK,EAAI,WAAWL,CAAK,IAEpCC,EAAI,YAAc,GACd,CAACY,GAAe,CAACF,GAAcjB,EAAS,SAASM,CAAK,IACtDC,EAAI,cAAgBe,EAAI,OAAS,GACrCD,EAAY,GACR,CAACN,GACDF,EAAW,QAAU,GACrB,CAACN,EAAI,QACL,CAACY,GACDnB,EAAS,MAAMM,CAAK,GACpB,CAACA,EAAM,MACP,CAACA,EAAM,KACP,CAACA,EAAM,SAEPC,EAAI,OAASA,EAAI,OAAO,UAAU,CAAC,GAEvC,IAAImB,EAAmB,GACjBC,EAAWzB,GAAU,UAAUI,EAAOC,EAAK,IAAOmB,EAAmB,GAAO,IAAOL,EAAY,EAAK,EACtGO,EAAK,IACT,GAAIX,GAAcM,GAAOC,EAAK,CAE1B,GADAI,EAAKL,EAAM;AAAA,EAAO,GACdC,EAAK,CACL,IAAMK,EAAKf,EAAcU,CAAG,EAC5BI,GAAM;AAAA,EAAKzB,GAAiB,cAAc0B,EAAItB,EAAI,MAAM,CAAC,EAC7D,CACIoB,IAAa,IAAM,CAACpB,EAAI,OACpBqB,IAAO;AAAA,IACPA,EAAK;AAAA;AAAA,GAGTA,GAAM;AAAA,EAAKrB,EAAI,MAAM,EAE7B,SACS,CAACY,GAAenB,EAAS,aAAaM,CAAK,EAAG,CACnD,IAAMwB,EAAMH,EAAS,CAAC,EAChBI,EAAMJ,EAAS,QAAQ;AAAA,CAAI,EAC3BK,EAAaD,IAAQ,GACrBE,EAAO1B,EAAI,QAAUD,EAAM,MAAQA,EAAM,MAAM,SAAW,EAChE,GAAI0B,GAAc,CAACC,EAAM,CACrB,IAAIC,GAAe,GACnB,GAAIF,IAAeF,IAAQ,KAAOA,IAAQ,KAAM,CAC5C,IAAIK,EAAMR,EAAS,QAAQ,GAAG,EAC1BG,IAAQ,KACRK,IAAQ,IACRA,EAAMJ,GACNJ,EAASQ,EAAM,CAAC,IAAM,MACtBA,EAAMR,EAAS,QAAQ,IAAKQ,EAAM,CAAC,IAEnCA,IAAQ,IAAMJ,EAAMI,KACpBD,GAAe,GACvB,CACKA,KACDN,EAAK;AAAA,EAAKrB,EAAI,MAAM,GAC5B,CACJ,MACSoB,IAAa,IAAMA,EAAS,CAAC,IAAM;AAAA,KACxCC,EAAK,IAET,OAAAN,GAAOM,EAAKD,EACRpB,EAAI,OACAmB,GAAoBlB,GACpBA,EAAU,EAETiB,GAAgB,CAACC,EACtBJ,GAAOnB,GAAiB,YAAYmB,EAAKf,EAAI,OAAQO,EAAcW,CAAY,CAAC,EAE3EJ,GAAaZ,GAClBA,EAAY,EAETa,CACX,CAEAvB,GAAQ,cAAgBK,KCvJxB,IAAAgC,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAe,QAAQ,cAAc,EAEzC,SAASC,GAAMC,KAAaC,EAAU,CAC9BD,IAAa,SACb,QAAQ,IAAI,GAAGC,CAAQ,CAC/B,CACA,SAASC,GAAKF,EAAUG,EAAS,EACzBH,IAAa,SAAWA,IAAa,UACjC,OAAOF,GAAa,aAAgB,WACpCA,GAAa,YAAYK,CAAO,EAEhC,QAAQ,KAAKA,CAAO,EAEhC,CAEAN,GAAQ,MAAQE,GAChBF,GAAQ,KAAOK,KClBf,IAAAE,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAS,IASPC,GAAY,KACZC,GAAQ,CACV,SAAUC,GAASA,IAAUF,IACxB,OAAOE,GAAU,UAAYA,EAAM,cAAgBF,GACxD,QAAS,MACT,IAAK,0BACL,KAAM,OACN,QAAS,IAAM,OAAO,OAAO,IAAID,GAAO,OAAO,OAAOC,EAAS,CAAC,EAAG,CAC/D,WAAYG,EAChB,CAAC,EACD,UAAW,IAAMH,EACrB,EACMI,GAAa,CAACC,EAAKC,KAASL,GAAM,SAASK,CAAG,GAC/CR,GAAS,SAASQ,CAAG,IACjB,CAACA,EAAI,MAAQA,EAAI,OAASP,GAAO,OAAO,QACzCE,GAAM,SAASK,EAAI,KAAK,IAC5BD,GAAK,IAAI,OAAO,KAAK,KAAKE,GAAOA,EAAI,MAAQN,GAAM,KAAOM,EAAI,OAAO,EACzE,SAASJ,GAAgBE,EAAKG,EAAKN,EAAO,CAEtC,GADAA,EAAQG,GAAOP,GAAS,QAAQI,CAAK,EAAIA,EAAM,QAAQG,EAAI,GAAG,EAAIH,EAC9DJ,GAAS,MAAMI,CAAK,EACpB,QAAWO,KAAMP,EAAM,MACnBQ,GAAWL,EAAKG,EAAKC,CAAE,UACtB,MAAM,QAAQP,CAAK,EACxB,QAAWO,KAAMP,EACbQ,GAAWL,EAAKG,EAAKC,CAAE,OAE3BC,GAAWL,EAAKG,EAAKN,CAAK,CAClC,CACA,SAASQ,GAAWL,EAAKG,EAAKN,EAAO,CACjC,IAAMS,EAASN,GAAOP,GAAS,QAAQI,CAAK,EAAIA,EAAM,QAAQG,EAAI,GAAG,EAAIH,EACzE,GAAI,CAACJ,GAAS,MAAMa,CAAM,EACtB,MAAM,IAAI,MAAM,2CAA2C,EAC/D,IAAMC,EAASD,EAAO,OAAO,KAAMN,EAAK,GAAG,EAC3C,OAAW,CAACC,EAAKJ,CAAK,IAAKU,EACnBJ,aAAe,IACVA,EAAI,IAAIF,CAAG,GACZE,EAAI,IAAIF,EAAKJ,CAAK,EAEjBM,aAAe,IACpBA,EAAI,IAAIF,CAAG,EAEL,OAAO,UAAU,eAAe,KAAKE,EAAKF,CAAG,GACnD,OAAO,eAAeE,EAAKF,EAAK,CAC5B,MAAAJ,EACA,SAAU,GACV,WAAY,GACZ,aAAc,EAClB,CAAC,EAGT,OAAOM,CACX,CAEAX,GAAQ,gBAAkBM,GAC1BN,GAAQ,WAAaO,GACrBP,GAAQ,MAAQI,KCnEhB,IAAAY,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAM,KACNC,GAAQ,KACRC,GAAY,KACZC,GAAW,IACXC,GAAO,IAEX,SAASC,GAAeC,EAAKC,EAAK,CAAE,IAAAC,EAAK,MAAAC,CAAM,EAAG,CAC9C,GAAIN,GAAS,OAAOK,CAAG,GAAKA,EAAI,WAC5BA,EAAI,WAAWF,EAAKC,EAAKE,CAAK,UAEzBR,GAAM,WAAWK,EAAKE,CAAG,EAC9BP,GAAM,gBAAgBK,EAAKC,EAAKE,CAAK,MACpC,CACD,IAAMC,EAAQN,GAAK,KAAKI,EAAK,GAAIF,CAAG,EACpC,GAAIC,aAAe,IACfA,EAAI,IAAIG,EAAON,GAAK,KAAKK,EAAOC,EAAOJ,CAAG,CAAC,UAEtCC,aAAe,IACpBA,EAAI,IAAIG,CAAK,MAEZ,CACD,IAAMC,EAAYC,GAAaJ,EAAKE,EAAOJ,CAAG,EACxCO,EAAUT,GAAK,KAAKK,EAAOE,EAAWL,CAAG,EAC3CK,KAAaJ,EACb,OAAO,eAAeA,EAAKI,EAAW,CAClC,MAAOE,EACP,SAAU,GACV,WAAY,GACZ,aAAc,EAClB,CAAC,EAEDN,EAAII,CAAS,EAAIE,CACzB,CACJ,CACA,OAAON,CACX,CACA,SAASK,GAAaJ,EAAKE,EAAOJ,EAAK,CACnC,GAAII,IAAU,KACV,MAAO,GACX,GAAI,OAAOA,GAAU,SACjB,OAAO,OAAOA,CAAK,EACvB,GAAIP,GAAS,OAAOK,CAAG,GAAKF,GAAK,IAAK,CAClC,IAAMQ,EAASZ,GAAU,uBAAuBI,EAAI,IAAK,CAAC,CAAC,EAC3DQ,EAAO,QAAU,IAAI,IACrB,QAAWC,KAAQT,EAAI,QAAQ,KAAK,EAChCQ,EAAO,QAAQ,IAAIC,EAAK,MAAM,EAClCD,EAAO,OAAS,GAChBA,EAAO,eAAiB,GACxB,IAAME,EAASR,EAAI,SAASM,CAAM,EAClC,GAAI,CAACR,EAAI,aAAc,CACnB,IAAIW,EAAU,KAAK,UAAUD,CAAM,EAC/BC,EAAQ,OAAS,KACjBA,EAAUA,EAAQ,UAAU,EAAG,EAAE,EAAI,QACzCjB,GAAI,KAAKM,EAAI,IAAI,QAAQ,SAAU,kFAAkFW,CAAO,0CAA0C,EACtKX,EAAI,aAAe,EACvB,CACA,OAAOU,CACX,CACA,OAAO,KAAK,UAAUN,CAAK,CAC/B,CAEAX,GAAQ,eAAiBM,KC/DzB,IAAAa,EAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAa,KACbC,GAAgB,KAChBC,GAAiB,KACjBC,GAAW,IAEf,SAASC,GAAWC,EAAKC,EAAOC,EAAK,CACjC,IAAMC,EAAIR,GAAW,WAAWK,EAAK,OAAWE,CAAG,EAC7CE,EAAIT,GAAW,WAAWM,EAAO,OAAWC,CAAG,EACrD,OAAO,IAAIG,GAAKF,EAAGC,CAAC,CACxB,CACA,IAAMC,GAAN,MAAMC,CAAK,CACP,YAAYN,EAAKC,EAAQ,KAAM,CAC3B,OAAO,eAAe,KAAMH,GAAS,UAAW,CAAE,MAAOA,GAAS,IAAK,CAAC,EACxE,KAAK,IAAME,EACX,KAAK,MAAQC,CACjB,CACA,MAAMM,EAAQ,CACV,GAAI,CAAE,IAAAP,EAAK,MAAAC,CAAM,EAAI,KACrB,OAAIH,GAAS,OAAOE,CAAG,IACnBA,EAAMA,EAAI,MAAMO,CAAM,GACtBT,GAAS,OAAOG,CAAK,IACrBA,EAAQA,EAAM,MAAMM,CAAM,GACvB,IAAID,EAAKN,EAAKC,CAAK,CAC9B,CACA,OAAOO,EAAGN,EAAK,CACX,IAAMO,EAAOP,GAAK,SAAW,IAAI,IAAQ,CAAC,EAC1C,OAAOL,GAAe,eAAeK,EAAKO,EAAM,IAAI,CACxD,CACA,SAASP,EAAKQ,EAAWC,EAAa,CAClC,OAAOT,GAAK,IACNN,GAAc,cAAc,KAAMM,EAAKQ,EAAWC,CAAW,EAC7D,KAAK,UAAU,IAAI,CAC7B,CACJ,EAEAjB,GAAQ,KAAOW,GACfX,GAAQ,WAAaK,KCtCrB,IAAAa,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAY,KACZC,GAAmB,KAEvB,SAASC,GAAoBC,EAAYC,EAAKC,EAAS,CAGnD,OAFaD,EAAI,QAAUD,EAAW,KACbG,GAA0BC,IAClCJ,EAAYC,EAAKC,CAAO,CAC7C,CACA,SAASE,GAAyB,CAAE,QAAAC,EAAS,MAAAC,CAAM,EAAGL,EAAK,CAAE,gBAAAM,EAAiB,UAAAC,EAAW,WAAAC,EAAY,YAAAC,EAAa,UAAAC,CAAU,EAAG,CAC3H,GAAM,CAAE,OAAAC,EAAQ,QAAS,CAAE,cAAAC,CAAc,CAAE,EAAIZ,EACzCa,EAAU,OAAO,OAAO,CAAC,EAAGb,EAAK,CAAE,OAAQQ,EAAY,KAAM,IAAK,CAAC,EACrEM,EAAY,GACVC,EAAQ,CAAC,EACf,QAASC,EAAI,EAAGA,EAAIX,EAAM,OAAQ,EAAEW,EAAG,CACnC,IAAMC,EAAOZ,EAAMW,CAAC,EAChBZ,EAAU,KACd,GAAIT,GAAS,OAAOsB,CAAI,EAChB,CAACH,GAAaG,EAAK,aACnBF,EAAM,KAAK,EAAE,EACjBG,GAAiBlB,EAAKe,EAAOE,EAAK,cAAeH,CAAS,EACtDG,EAAK,UACLb,EAAUa,EAAK,iBAEdtB,GAAS,OAAOsB,CAAI,EAAG,CAC5B,IAAME,EAAKxB,GAAS,OAAOsB,EAAK,GAAG,EAAIA,EAAK,IAAM,KAC9CE,IACI,CAACL,GAAaK,EAAG,aACjBJ,EAAM,KAAK,EAAE,EACjBG,GAAiBlB,EAAKe,EAAOI,EAAG,cAAeL,CAAS,EAEhE,CACAA,EAAY,GACZ,IAAIM,EAAMxB,GAAU,UAAUqB,EAAMJ,EAAS,IAAOT,EAAU,KAAO,IAAOU,EAAY,EAAK,EACzFV,IACAgB,GAAOvB,GAAiB,YAAYuB,EAAKZ,EAAYI,EAAcR,CAAO,CAAC,GAC3EU,GAAaV,IACbU,EAAY,IAChBC,EAAM,KAAKT,EAAkBc,CAAG,CACpC,CACA,IAAIA,EACJ,GAAIL,EAAM,SAAW,EACjBK,EAAMb,EAAU,MAAQA,EAAU,QAEjC,CACDa,EAAML,EAAM,CAAC,EACb,QAASC,EAAI,EAAGA,EAAID,EAAM,OAAQ,EAAEC,EAAG,CACnC,IAAMK,EAAON,EAAMC,CAAC,EACpBI,GAAOC,EAAO;AAAA,EAAKV,CAAM,GAAGU,CAAI,GAAK;AAAA,CACzC,CACJ,CACA,OAAIjB,GACAgB,GAAO;AAAA,EAAOvB,GAAiB,cAAce,EAAcR,CAAO,EAAGO,CAAM,EACvED,GACAA,EAAU,GAETI,GAAaL,GAClBA,EAAY,EACTW,CACX,CACA,SAASlB,GAAwB,CAAE,MAAAG,CAAM,EAAGL,EAAK,CAAE,UAAAO,EAAW,WAAAC,CAAW,EAAG,CACxE,GAAM,CAAE,OAAAG,EAAQ,WAAAW,EAAY,sBAAuBC,EAAW,QAAS,CAAE,cAAAX,CAAc,CAAE,EAAIZ,EAC7FQ,GAAcc,EACd,IAAMT,EAAU,OAAO,OAAO,CAAC,EAAGb,EAAK,CACnC,OAAQQ,EACR,OAAQ,GACR,KAAM,IACV,CAAC,EACGgB,EAAa,GACbC,EAAe,EACbV,EAAQ,CAAC,EACf,QAASC,EAAI,EAAGA,EAAIX,EAAM,OAAQ,EAAEW,EAAG,CACnC,IAAMC,EAAOZ,EAAMW,CAAC,EAChBZ,EAAU,KACd,GAAIT,GAAS,OAAOsB,CAAI,EAChBA,EAAK,aACLF,EAAM,KAAK,EAAE,EACjBG,GAAiBlB,EAAKe,EAAOE,EAAK,cAAe,EAAK,EAClDA,EAAK,UACLb,EAAUa,EAAK,iBAEdtB,GAAS,OAAOsB,CAAI,EAAG,CAC5B,IAAME,EAAKxB,GAAS,OAAOsB,EAAK,GAAG,EAAIA,EAAK,IAAM,KAC9CE,IACIA,EAAG,aACHJ,EAAM,KAAK,EAAE,EACjBG,GAAiBlB,EAAKe,EAAOI,EAAG,cAAe,EAAK,EAChDA,EAAG,UACHK,EAAa,KAErB,IAAME,EAAK/B,GAAS,OAAOsB,EAAK,KAAK,EAAIA,EAAK,MAAQ,KAClDS,GACIA,EAAG,UACHtB,EAAUsB,EAAG,SACbA,EAAG,gBACHF,EAAa,KAEZP,EAAK,OAAS,MAAQE,GAAI,UAC/Bf,EAAUe,EAAG,QAErB,CACIf,IACAoB,EAAa,IACjB,IAAIJ,EAAMxB,GAAU,UAAUqB,EAAMJ,EAAS,IAAOT,EAAU,IAAK,EAC/DY,EAAIX,EAAM,OAAS,IACnBe,GAAO,KACPhB,IACAgB,GAAOvB,GAAiB,YAAYuB,EAAKZ,EAAYI,EAAcR,CAAO,CAAC,GAC3E,CAACoB,IAAeT,EAAM,OAASU,GAAgBL,EAAI,SAAS;AAAA,CAAI,KAChEI,EAAa,IACjBT,EAAM,KAAKK,CAAG,EACdK,EAAeV,EAAM,MACzB,CACA,GAAM,CAAE,MAAAY,EAAO,IAAAC,CAAI,EAAIrB,EACvB,GAAIQ,EAAM,SAAW,EACjB,OAAOY,EAAQC,EAGf,GAAI,CAACJ,EAAY,CACb,IAAMK,EAAMd,EAAM,OAAO,CAACe,EAAKT,IAASS,EAAMT,EAAK,OAAS,EAAG,CAAC,EAChEG,EAAaxB,EAAI,QAAQ,UAAY,GAAK6B,EAAM7B,EAAI,QAAQ,SAChE,CACA,GAAIwB,EAAY,CACZ,IAAIJ,EAAMO,EACV,QAAWN,KAAQN,EACfK,GAAOC,EAAO;AAAA,EAAKC,CAAU,GAAGX,CAAM,GAAGU,CAAI,GAAK;AAAA,EACtD,MAAO,GAAGD,CAAG;AAAA,EAAKT,CAAM,GAAGiB,CAAG,EAClC,KAEI,OAAO,GAAGD,CAAK,GAAGJ,CAAS,GAAGR,EAAM,KAAK,GAAG,CAAC,GAAGQ,CAAS,GAAGK,CAAG,EAG3E,CACA,SAASV,GAAiB,CAAE,OAAAP,EAAQ,QAAS,CAAE,cAAAC,CAAc,CAAE,EAAGG,EAAOX,EAASU,EAAW,CAGzF,GAFIV,GAAWU,IACXV,EAAUA,EAAQ,QAAQ,OAAQ,EAAE,GACpCA,EAAS,CACT,IAAM2B,EAAKlC,GAAiB,cAAce,EAAcR,CAAO,EAAGO,CAAM,EACxEI,EAAM,KAAKgB,EAAG,UAAU,CAAC,CAC7B,CACJ,CAEArC,GAAQ,oBAAsBI,KChJ9B,IAAAkC,EAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAsB,KACtBC,GAAiB,KACjBC,GAAa,KACbC,EAAW,IACXC,GAAO,IACPC,GAAS,IAEb,SAASC,GAASC,EAAOC,EAAK,CAC1B,IAAMC,EAAIN,EAAS,SAASK,CAAG,EAAIA,EAAI,MAAQA,EAC/C,QAAWE,KAAMH,EACb,GAAIJ,EAAS,OAAOO,CAAE,IACdA,EAAG,MAAQF,GAAOE,EAAG,MAAQD,GAE7BN,EAAS,SAASO,EAAG,GAAG,GAAKA,EAAG,IAAI,QAAUD,GAC9C,OAAOC,CAIvB,CACA,IAAMC,GAAN,cAAsBT,GAAW,UAAW,CACxC,WAAW,SAAU,CACjB,MAAO,uBACX,CACA,YAAYU,EAAQ,CAChB,MAAMT,EAAS,IAAKS,CAAM,EAC1B,KAAK,MAAQ,CAAC,CAClB,CAKA,OAAO,KAAKA,EAAQC,EAAKC,EAAK,CAC1B,GAAM,CAAE,cAAAC,EAAe,SAAAC,CAAS,EAAIF,EAC9BG,EAAM,IAAI,KAAKL,CAAM,EACrBM,EAAM,CAACV,EAAKW,IAAU,CACxB,GAAI,OAAOH,GAAa,WACpBG,EAAQH,EAAS,KAAKH,EAAKL,EAAKW,CAAK,UAChC,MAAM,QAAQH,CAAQ,GAAK,CAACA,EAAS,SAASR,CAAG,EACtD,QACAW,IAAU,QAAaJ,IACvBE,EAAI,MAAM,KAAKb,GAAK,WAAWI,EAAKW,EAAOL,CAAG,CAAC,CACvD,EACA,GAAID,aAAe,IACf,OAAW,CAACL,EAAKW,CAAK,IAAKN,EACvBK,EAAIV,EAAKW,CAAK,UAEbN,GAAO,OAAOA,GAAQ,SAC3B,QAAWL,KAAO,OAAO,KAAKK,CAAG,EAC7BK,EAAIV,EAAKK,EAAIL,CAAG,CAAC,EAEzB,OAAI,OAAOI,EAAO,gBAAmB,YACjCK,EAAI,MAAM,KAAKL,EAAO,cAAc,EAEjCK,CACX,CAOA,IAAIG,EAAMC,EAAW,CACjB,IAAIC,EACAnB,EAAS,OAAOiB,CAAI,EACpBE,EAAQF,EACH,CAACA,GAAQ,OAAOA,GAAS,UAAY,EAAE,QAASA,GAErDE,EAAQ,IAAIlB,GAAK,KAAKgB,EAAMA,GAAM,KAAK,EAGvCE,EAAQ,IAAIlB,GAAK,KAAKgB,EAAK,IAAKA,EAAK,KAAK,EAC9C,IAAMG,EAAOjB,GAAS,KAAK,MAAOgB,EAAM,GAAG,EACrCE,EAAc,KAAK,QAAQ,eACjC,GAAID,EAAM,CACN,GAAI,CAACF,EACD,MAAM,IAAI,MAAM,OAAOC,EAAM,GAAG,cAAc,EAE9CnB,EAAS,SAASoB,EAAK,KAAK,GAAKlB,GAAO,cAAciB,EAAM,KAAK,EACjEC,EAAK,MAAM,MAAQD,EAAM,MAEzBC,EAAK,MAAQD,EAAM,KAC3B,SACSE,EAAa,CAClB,IAAMC,EAAI,KAAK,MAAM,UAAUC,GAAQF,EAAYF,EAAOI,CAAI,EAAI,CAAC,EAC/DD,IAAM,GACN,KAAK,MAAM,KAAKH,CAAK,EAErB,KAAK,MAAM,OAAOG,EAAG,EAAGH,CAAK,CACrC,MAEI,KAAK,MAAM,KAAKA,CAAK,CAE7B,CACA,OAAOd,EAAK,CACR,IAAME,EAAKJ,GAAS,KAAK,MAAOE,CAAG,EACnC,OAAKE,EAEO,KAAK,MAAM,OAAO,KAAK,MAAM,QAAQA,CAAE,EAAG,CAAC,EAC5C,OAAS,EAFT,EAGf,CACA,IAAIF,EAAKmB,EAAY,CAEjB,IAAMC,EADKtB,GAAS,KAAK,MAAOE,CAAG,GAClB,MACjB,OAAQ,CAACmB,GAAcxB,EAAS,SAASyB,CAAI,EAAIA,EAAK,MAAQA,IAAS,MAC3E,CACA,IAAIpB,EAAK,CACL,MAAO,CAAC,CAACF,GAAS,KAAK,MAAOE,CAAG,CACrC,CACA,IAAIA,EAAKW,EAAO,CACZ,KAAK,IAAI,IAAIf,GAAK,KAAKI,EAAKW,CAAK,EAAG,EAAI,CAC5C,CAMA,OAAOU,EAAGf,EAAKgB,EAAM,CACjB,IAAMb,EAAMa,EAAO,IAAIA,EAAShB,GAAK,SAAW,IAAI,IAAQ,CAAC,EACzDA,GAAK,UACLA,EAAI,SAASG,CAAG,EACpB,QAAWS,KAAQ,KAAK,MACpBzB,GAAe,eAAea,EAAKG,EAAKS,CAAI,EAChD,OAAOT,CACX,CACA,SAASH,EAAKiB,EAAWC,EAAa,CAClC,GAAI,CAAClB,EACD,OAAO,KAAK,UAAU,IAAI,EAC9B,QAAWY,KAAQ,KAAK,MACpB,GAAI,CAACvB,EAAS,OAAOuB,CAAI,EACrB,MAAM,IAAI,MAAM,sCAAsC,KAAK,UAAUA,CAAI,CAAC,UAAU,EAE5F,MAAI,CAACZ,EAAI,eAAiB,KAAK,iBAAiB,EAAK,IACjDA,EAAM,OAAO,OAAO,CAAC,EAAGA,EAAK,CAAE,cAAe,EAAK,CAAC,GACjDd,GAAoB,oBAAoB,KAAMc,EAAK,CACtD,gBAAiB,GACjB,UAAW,CAAE,MAAO,IAAK,IAAK,GAAI,EAClC,WAAYA,EAAI,QAAU,GAC1B,YAAAkB,EACA,UAAAD,CACJ,CAAC,CACL,CACJ,EAEAhC,GAAQ,QAAUY,GAClBZ,GAAQ,SAAWO,KClJnB,IAAA2B,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAU,IAERC,GAAM,CACR,WAAY,MACZ,QAAS,GACT,UAAWD,GAAQ,QACnB,IAAK,wBACL,QAAQC,EAAKC,EAAS,CAClB,OAAKH,GAAS,MAAME,CAAG,GACnBC,EAAQ,iCAAiC,EACtCD,CACX,EACA,WAAY,CAACE,EAAQC,EAAKC,IAAQL,GAAQ,QAAQ,KAAKG,EAAQC,EAAKC,CAAG,CAC3E,EAEAP,GAAQ,IAAMG,KClBd,IAAAK,EAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAa,KACbC,GAAsB,KACtBC,GAAa,KACbC,GAAW,IACXC,GAAS,IACTC,GAAO,IAELC,GAAN,cAAsBJ,GAAW,UAAW,CACxC,WAAW,SAAU,CACjB,MAAO,uBACX,CACA,YAAYK,EAAQ,CAChB,MAAMJ,GAAS,IAAKI,CAAM,EAC1B,KAAK,MAAQ,CAAC,CAClB,CACA,IAAIC,EAAO,CACP,KAAK,MAAM,KAAKA,CAAK,CACzB,CASA,OAAOC,EAAK,CACR,IAAMC,EAAMC,GAAYF,CAAG,EAC3B,OAAI,OAAOC,GAAQ,SACR,GACC,KAAK,MAAM,OAAOA,EAAK,CAAC,EACzB,OAAS,CACxB,CACA,IAAID,EAAKG,EAAY,CACjB,IAAMF,EAAMC,GAAYF,CAAG,EAC3B,GAAI,OAAOC,GAAQ,SACf,OACJ,IAAMG,EAAK,KAAK,MAAMH,CAAG,EACzB,MAAO,CAACE,GAAcT,GAAS,SAASU,CAAE,EAAIA,EAAG,MAAQA,CAC7D,CAOA,IAAIJ,EAAK,CACL,IAAMC,EAAMC,GAAYF,CAAG,EAC3B,OAAO,OAAOC,GAAQ,UAAYA,EAAM,KAAK,MAAM,MACvD,CAQA,IAAID,EAAKD,EAAO,CACZ,IAAME,EAAMC,GAAYF,CAAG,EAC3B,GAAI,OAAOC,GAAQ,SACf,MAAM,IAAI,MAAM,+BAA+BD,CAAG,GAAG,EACzD,IAAMK,EAAO,KAAK,MAAMJ,CAAG,EACvBP,GAAS,SAASW,CAAI,GAAKV,GAAO,cAAcI,CAAK,EACrDM,EAAK,MAAQN,EAEb,KAAK,MAAME,CAAG,EAAIF,CAC1B,CACA,OAAOO,EAAGC,EAAK,CACX,IAAMC,EAAM,CAAC,EACTD,GAAK,UACLA,EAAI,SAASC,CAAG,EACpB,IAAI,EAAI,EACR,QAAWC,KAAQ,KAAK,MACpBD,EAAI,KAAKZ,GAAK,KAAKa,EAAM,OAAO,GAAG,EAAGF,CAAG,CAAC,EAC9C,OAAOC,CACX,CACA,SAASD,EAAKG,EAAWC,EAAa,CAClC,OAAKJ,EAEEf,GAAoB,oBAAoB,KAAMe,EAAK,CACtD,gBAAiB,KACjB,UAAW,CAAE,MAAO,IAAK,IAAK,GAAI,EAClC,YAAaA,EAAI,QAAU,IAAM,KACjC,YAAAI,EACA,UAAAD,CACJ,CAAC,EAPU,KAAK,UAAU,IAAI,CAQlC,CACA,OAAO,KAAKZ,EAAQc,EAAKL,EAAK,CAC1B,GAAM,CAAE,SAAAM,CAAS,EAAIN,EACfC,EAAM,IAAI,KAAKV,CAAM,EAC3B,GAAIc,GAAO,OAAO,YAAY,OAAOA,CAAG,EAAG,CACvC,IAAIE,EAAI,EACR,QAASV,KAAMQ,EAAK,CAChB,GAAI,OAAOC,GAAa,WAAY,CAChC,IAAMb,EAAMY,aAAe,IAAMR,EAAK,OAAOU,GAAG,EAChDV,EAAKS,EAAS,KAAKD,EAAKZ,EAAKI,CAAE,CACnC,CACAI,EAAI,MAAM,KAAKjB,GAAW,WAAWa,EAAI,OAAWG,CAAG,CAAC,CAC5D,CACJ,CACA,OAAOC,CACX,CACJ,EACA,SAASN,GAAYF,EAAK,CACtB,IAAIC,EAAMP,GAAS,SAASM,CAAG,EAAIA,EAAI,MAAQA,EAC/C,OAAIC,GAAO,OAAOA,GAAQ,WACtBA,EAAM,OAAOA,CAAG,GACb,OAAOA,GAAQ,UAAY,OAAO,UAAUA,CAAG,GAAKA,GAAO,EAC5DA,EACA,IACV,CAEAX,GAAQ,QAAUO,KClHlB,IAAAkB,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAU,IAERC,GAAM,CACR,WAAY,MACZ,QAAS,GACT,UAAWD,GAAQ,QACnB,IAAK,wBACL,QAAQC,EAAKC,EAAS,CAClB,OAAKH,GAAS,MAAME,CAAG,GACnBC,EAAQ,kCAAkC,EACvCD,CACX,EACA,WAAY,CAACE,EAAQC,EAAKC,IAAQL,GAAQ,QAAQ,KAAKG,EAAQC,EAAKC,CAAG,CAC3E,EAEAP,GAAQ,IAAMG,KClBd,IAAAK,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAkB,KAEhBC,GAAS,CACX,SAAUC,GAAS,OAAOA,GAAU,SACpC,QAAS,GACT,IAAK,wBACL,QAASC,GAAOA,EAChB,UAAUC,EAAMC,EAAKC,EAAWC,EAAa,CACzC,OAAAF,EAAM,OAAO,OAAO,CAAE,aAAc,EAAK,EAAGA,CAAG,EACxCL,GAAgB,gBAAgBI,EAAMC,EAAKC,EAAWC,CAAW,CAC5E,CACJ,EAEAR,GAAQ,OAASE,KCfjB,IAAAO,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAS,IAEPC,GAAU,CACZ,SAAUC,GAASA,GAAS,KAC5B,WAAY,IAAM,IAAIF,GAAO,OAAO,IAAI,EACxC,QAAS,GACT,IAAK,yBACL,KAAM,wBACN,QAAS,IAAM,IAAIA,GAAO,OAAO,IAAI,EACrC,UAAW,CAAC,CAAE,OAAAG,CAAO,EAAGC,IAAQ,OAAOD,GAAW,UAAYF,GAAQ,KAAK,KAAKE,CAAM,EAChFA,EACAC,EAAI,QAAQ,OACtB,EAEAL,GAAQ,QAAUE,KChBlB,IAAAI,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAS,IAEPC,GAAU,CACZ,SAAUC,GAAS,OAAOA,GAAU,UACpC,QAAS,GACT,IAAK,yBACL,KAAM,oCACN,QAASC,GAAO,IAAIH,GAAO,OAAOG,EAAI,CAAC,IAAM,KAAOA,EAAI,CAAC,IAAM,GAAG,EAClE,UAAU,CAAE,OAAAC,EAAQ,MAAAF,CAAM,EAAGG,EAAK,CAC9B,GAAID,GAAUH,GAAQ,KAAK,KAAKG,CAAM,EAAG,CACrC,IAAME,EAAKF,EAAO,CAAC,IAAM,KAAOA,EAAO,CAAC,IAAM,IAC9C,GAAIF,IAAUI,EACV,OAAOF,CACf,CACA,OAAOF,EAAQG,EAAI,QAAQ,QAAUA,EAAI,QAAQ,QACrD,CACJ,EAEAN,GAAQ,QAAUE,KCpBlB,IAAAM,GAAAC,EAAAC,IAAA,cAEA,SAASC,GAAgB,CAAE,OAAAC,EAAQ,kBAAAC,EAAmB,IAAAC,EAAK,MAAAC,CAAM,EAAG,CAChE,GAAI,OAAOA,GAAU,SACjB,OAAO,OAAOA,CAAK,EACvB,IAAMC,EAAM,OAAOD,GAAU,SAAWA,EAAQ,OAAOA,CAAK,EAC5D,GAAI,CAAC,SAASC,CAAG,EACb,OAAO,MAAMA,CAAG,EAAI,OAASA,EAAM,EAAI,QAAU,OACrD,IAAIC,EAAI,KAAK,UAAUF,CAAK,EAC5B,GAAI,CAACH,GACDC,IACC,CAACC,GAAOA,IAAQ,4BACjB,MAAM,KAAKG,CAAC,EAAG,CACf,IAAIC,EAAID,EAAE,QAAQ,GAAG,EACjBC,EAAI,IACJA,EAAID,EAAE,OACNA,GAAK,KAET,IAAIE,EAAIN,GAAqBI,EAAE,OAASC,EAAI,GAC5C,KAAOC,KAAM,GACTF,GAAK,GACb,CACA,OAAOA,CACX,CAEAP,GAAQ,gBAAkBC,KCzB1B,IAAAS,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAS,IACTC,GAAkB,KAEhBC,GAAW,CACb,SAAUC,GAAS,OAAOA,GAAU,SACpC,QAAS,GACT,IAAK,0BACL,KAAM,iDACN,QAASC,GAAOA,EAAI,MAAM,EAAE,EAAE,YAAY,IAAM,MAC1C,IACAA,EAAI,CAAC,IAAM,IACP,OAAO,kBACP,OAAO,kBACjB,UAAWH,GAAgB,eAC/B,EACMI,GAAW,CACb,SAAUF,GAAS,OAAOA,GAAU,SACpC,QAAS,GACT,IAAK,0BACL,OAAQ,MACR,KAAM,yDACN,QAASC,GAAO,WAAWA,CAAG,EAC9B,UAAUE,EAAM,CACZ,IAAMC,EAAM,OAAOD,EAAK,KAAK,EAC7B,OAAO,SAASC,CAAG,EAAIA,EAAI,cAAc,EAAIN,GAAgB,gBAAgBK,CAAI,CACrF,CACJ,EACME,GAAQ,CACV,SAAUL,GAAS,OAAOA,GAAU,SACpC,QAAS,GACT,IAAK,0BACL,KAAM,qCACN,QAAQC,EAAK,CACT,IAAME,EAAO,IAAIN,GAAO,OAAO,WAAWI,CAAG,CAAC,EACxCK,EAAML,EAAI,QAAQ,GAAG,EAC3B,OAAIK,IAAQ,IAAML,EAAIA,EAAI,OAAS,CAAC,IAAM,MACtCE,EAAK,kBAAoBF,EAAI,OAASK,EAAM,GACzCH,CACX,EACA,UAAWL,GAAgB,eAC/B,EAEAF,GAAQ,MAAQS,GAChBT,GAAQ,SAAWM,GACnBN,GAAQ,SAAWG,KC9CnB,IAAAQ,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAkB,KAEhBC,GAAeC,GAAU,OAAOA,GAAU,UAAY,OAAO,UAAUA,CAAK,EAC5EC,GAAa,CAACC,EAAKC,EAAQC,EAAO,CAAE,YAAAC,CAAY,IAAOA,EAAc,OAAOH,CAAG,EAAI,SAASA,EAAI,UAAUC,CAAM,EAAGC,CAAK,EAC9H,SAASE,GAAaC,EAAMH,EAAOI,EAAQ,CACvC,GAAM,CAAE,MAAAR,CAAM,EAAIO,EAClB,OAAIR,GAAYC,CAAK,GAAKA,GAAS,EACxBQ,EAASR,EAAM,SAASI,CAAK,EACjCN,GAAgB,gBAAgBS,CAAI,CAC/C,CACA,IAAME,GAAS,CACX,SAAUT,GAASD,GAAYC,CAAK,GAAKA,GAAS,EAClD,QAAS,GACT,IAAK,wBACL,OAAQ,MACR,KAAM,aACN,QAAS,CAACE,EAAKQ,EAAUC,IAAQV,GAAWC,EAAK,EAAG,EAAGS,CAAG,EAC1D,UAAWJ,GAAQD,GAAaC,EAAM,EAAG,IAAI,CACjD,EACMK,GAAM,CACR,SAAUb,GACV,QAAS,GACT,IAAK,wBACL,KAAM,gBACN,QAAS,CAACG,EAAKQ,EAAUC,IAAQV,GAAWC,EAAK,EAAG,GAAIS,CAAG,EAC3D,UAAWb,GAAgB,eAC/B,EACMe,GAAS,CACX,SAAUb,GAASD,GAAYC,CAAK,GAAKA,GAAS,EAClD,QAAS,GACT,IAAK,wBACL,OAAQ,MACR,KAAM,mBACN,QAAS,CAACE,EAAKQ,EAAUC,IAAQV,GAAWC,EAAK,EAAG,GAAIS,CAAG,EAC3D,UAAWJ,GAAQD,GAAaC,EAAM,GAAI,IAAI,CAClD,EAEAV,GAAQ,IAAMe,GACdf,GAAQ,OAASgB,GACjBhB,GAAQ,OAASY,KCzCjB,IAAAK,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAM,KACNC,GAAQ,KACRC,GAAM,KACNC,GAAS,KACTC,GAAO,KACPC,GAAQ,KACRC,GAAM,KAEJC,GAAS,CACXP,GAAI,IACJE,GAAI,IACJC,GAAO,OACPF,GAAM,QACNG,GAAK,QACLE,GAAI,OACJA,GAAI,IACJA,GAAI,OACJD,GAAM,SACNA,GAAM,SACNA,GAAM,KACV,EAEAN,GAAQ,OAASQ,KCxBjB,IAAAC,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAS,IACTC,GAAM,KACNC,GAAM,KAEV,SAASC,GAAYC,EAAO,CACxB,OAAO,OAAOA,GAAU,UAAY,OAAO,UAAUA,CAAK,CAC9D,CACA,IAAMC,GAAgB,CAAC,CAAE,MAAAD,CAAM,IAAM,KAAK,UAAUA,CAAK,EACnDE,GAAc,CAChB,CACI,SAAUF,GAAS,OAAOA,GAAU,SACpC,QAAS,GACT,IAAK,wBACL,QAASG,GAAOA,EAChB,UAAWF,EACf,EACA,CACI,SAAUD,GAASA,GAAS,KAC5B,WAAY,IAAM,IAAIJ,GAAO,OAAO,IAAI,EACxC,QAAS,GACT,IAAK,yBACL,KAAM,SACN,QAAS,IAAM,KACf,UAAWK,EACf,EACA,CACI,SAAUD,GAAS,OAAOA,GAAU,UACpC,QAAS,GACT,IAAK,yBACL,KAAM,iBACN,QAASG,GAAOA,IAAQ,OACxB,UAAWF,EACf,EACA,CACI,SAAUF,GACV,QAAS,GACT,IAAK,wBACL,KAAM,wBACN,QAAS,CAACI,EAAKC,EAAU,CAAE,YAAAC,CAAY,IAAMA,EAAc,OAAOF,CAAG,EAAI,SAASA,EAAK,EAAE,EACzF,UAAW,CAAC,CAAE,MAAAH,CAAM,IAAMD,GAAYC,CAAK,EAAIA,EAAM,SAAS,EAAI,KAAK,UAAUA,CAAK,CAC1F,EACA,CACI,SAAUA,GAAS,OAAOA,GAAU,SACpC,QAAS,GACT,IAAK,0BACL,KAAM,yDACN,QAASG,GAAO,WAAWA,CAAG,EAC9B,UAAWF,EACf,CACJ,EACMK,GAAY,CACd,QAAS,GACT,IAAK,GACL,KAAM,IACN,QAAQH,EAAKI,EAAS,CAClB,OAAAA,EAAQ,2BAA2B,KAAK,UAAUJ,CAAG,CAAC,EAAE,EACjDA,CACX,CACJ,EACMK,GAAS,CAACX,GAAI,IAAKC,GAAI,GAAG,EAAE,OAAOI,GAAaI,EAAS,EAE/DX,GAAQ,OAASa,KC/DjB,IAAAC,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAc,QAAQ,aAAa,EACnCC,GAAS,IACTC,GAAkB,KAEhBC,GAAS,CACX,SAAUC,GAASA,aAAiB,WACpC,QAAS,GACT,IAAK,2BASL,QAAQC,EAAKC,EAAS,CAClB,GAAI,OAAON,GAAY,QAAW,WAC9B,OAAOA,GAAY,OAAO,KAAKK,EAAK,QAAQ,EAE3C,GAAI,OAAO,MAAS,WAAY,CAEjC,IAAME,EAAM,KAAKF,EAAI,QAAQ,UAAW,EAAE,CAAC,EACrCG,EAAS,IAAI,WAAWD,EAAI,MAAM,EACxC,QAAS,EAAI,EAAG,EAAIA,EAAI,OAAQ,EAAE,EAC9BC,EAAO,CAAC,EAAID,EAAI,WAAW,CAAC,EAChC,OAAOC,CACX,KAEI,QAAAF,EAAQ,0FAA0F,EAC3FD,CAEf,EACA,UAAU,CAAE,QAAAI,EAAS,KAAAC,EAAM,MAAAN,CAAM,EAAGO,EAAKC,EAAWC,EAAa,CAC7D,IAAMC,EAAMV,EACRG,EACJ,GAAI,OAAOP,GAAY,QAAW,WAC9BO,EACIO,aAAed,GAAY,OACrBc,EAAI,SAAS,QAAQ,EACrBd,GAAY,OAAO,KAAKc,EAAI,MAAM,EAAE,SAAS,QAAQ,UAE1D,OAAO,MAAS,WAAY,CACjC,IAAIC,EAAI,GACR,QAASC,EAAI,EAAGA,EAAIF,EAAI,OAAQ,EAAEE,EAC9BD,GAAK,OAAO,aAAaD,EAAIE,CAAC,CAAC,EACnCT,EAAM,KAAKQ,CAAC,CAChB,KAEI,OAAM,IAAI,MAAM,0FAA0F,EAI9G,GAFKL,IACDA,EAAOT,GAAO,OAAO,eACrBS,IAAST,GAAO,OAAO,aAAc,CACrC,IAAMgB,EAAY,KAAK,IAAIN,EAAI,QAAQ,UAAYA,EAAI,OAAO,OAAQA,EAAI,QAAQ,eAAe,EAC3FO,EAAI,KAAK,KAAKX,EAAI,OAASU,CAAS,EACpCE,EAAQ,IAAI,MAAMD,CAAC,EACzB,QAASF,EAAI,EAAGI,EAAI,EAAGJ,EAAIE,EAAG,EAAEF,EAAGI,GAAKH,EACpCE,EAAMH,CAAC,EAAIT,EAAI,OAAOa,EAAGH,CAAS,EAEtCV,EAAMY,EAAM,KAAKT,IAAST,GAAO,OAAO,cAAgB;AAAA,EAAO,GAAG,CACtE,CACA,OAAOC,GAAgB,gBAAgB,CAAE,QAAAO,EAAS,KAAAC,EAAM,MAAOH,CAAI,EAAGI,EAAKC,EAAWC,CAAW,CACrG,CACJ,EAEAd,GAAQ,OAASI,KCpEjB,IAAAkB,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAO,IACPC,GAAS,IACTC,GAAU,IAEd,SAASC,GAAaC,EAAKC,EAAS,CAChC,GAAIN,GAAS,MAAMK,CAAG,EAClB,QAASE,EAAI,EAAGA,EAAIF,EAAI,MAAM,OAAQ,EAAEE,EAAG,CACvC,IAAIC,EAAOH,EAAI,MAAME,CAAC,EACtB,GAAI,CAAAP,GAAS,OAAOQ,CAAI,EAEnB,IAAIR,GAAS,MAAMQ,CAAI,EAAG,CACvBA,EAAK,MAAM,OAAS,GACpBF,EAAQ,gDAAgD,EAC5D,IAAMG,EAAOD,EAAK,MAAM,CAAC,GAAK,IAAIP,GAAK,KAAK,IAAIC,GAAO,OAAO,IAAI,CAAC,EAKnE,GAJIM,EAAK,gBACLC,EAAK,IAAI,cAAgBA,EAAK,IAAI,cAC5B,GAAGD,EAAK,aAAa;AAAA,EAAKC,EAAK,IAAI,aAAa,GAChDD,EAAK,eACXA,EAAK,QAAS,CACd,IAAME,EAAKD,EAAK,OAASA,EAAK,IAC9BC,EAAG,QAAUA,EAAG,QACV,GAAGF,EAAK,OAAO;AAAA,EAAKE,EAAG,OAAO,GAC9BF,EAAK,OACf,CACAA,EAAOC,CACX,CACAJ,EAAI,MAAME,CAAC,EAAIP,GAAS,OAAOQ,CAAI,EAAIA,EAAO,IAAIP,GAAK,KAAKO,CAAI,EACpE,MAGAF,EAAQ,kCAAkC,EAC9C,OAAOD,CACX,CACA,SAASM,GAAYC,EAAQC,EAAUC,EAAK,CACxC,GAAM,CAAE,SAAAC,CAAS,EAAID,EACfE,EAAQ,IAAIb,GAAQ,QAAQS,CAAM,EACxCI,EAAM,IAAM,0BACZ,IAAIT,EAAI,EACR,GAAIM,GAAY,OAAO,YAAY,OAAOA,CAAQ,EAC9C,QAASI,KAAMJ,EAAU,CACjB,OAAOE,GAAa,aACpBE,EAAKF,EAAS,KAAKF,EAAU,OAAON,GAAG,EAAGU,CAAE,GAChD,IAAIC,EAAKC,EACT,GAAI,MAAM,QAAQF,CAAE,EAChB,GAAIA,EAAG,SAAW,EACdC,EAAMD,EAAG,CAAC,EACVE,EAAQF,EAAG,CAAC,MAGZ,OAAM,IAAI,UAAU,gCAAgCA,CAAE,EAAE,UAEvDA,GAAMA,aAAc,OAAQ,CACjC,IAAMG,EAAO,OAAO,KAAKH,CAAE,EAC3B,GAAIG,EAAK,SAAW,EAChBF,EAAME,EAAK,CAAC,EACZD,EAAQF,EAAGC,CAAG,MAGd,OAAM,IAAI,UAAU,oCAAoCE,EAAK,MAAM,OAAO,CAElF,MAEIF,EAAMD,EAEVD,EAAM,MAAM,KAAKf,GAAK,WAAWiB,EAAKC,EAAOL,CAAG,CAAC,CACrD,CACJ,OAAOE,CACX,CACA,IAAMA,GAAQ,CACV,WAAY,MACZ,QAAS,GACT,IAAK,0BACL,QAASZ,GACT,WAAYO,EAChB,EAEAZ,GAAQ,YAAcY,GACtBZ,GAAQ,MAAQiB,GAChBjB,GAAQ,aAAeK,KCjFvB,IAAAiB,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAO,IACPC,GAAU,IACVC,GAAU,IACVC,GAAQ,KAENC,GAAN,MAAMC,UAAiBH,GAAQ,OAAQ,CACnC,aAAc,CACV,MAAM,EACN,KAAK,IAAMD,GAAQ,QAAQ,UAAU,IAAI,KAAK,IAAI,EAClD,KAAK,OAASA,GAAQ,QAAQ,UAAU,OAAO,KAAK,IAAI,EACxD,KAAK,IAAMA,GAAQ,QAAQ,UAAU,IAAI,KAAK,IAAI,EAClD,KAAK,IAAMA,GAAQ,QAAQ,UAAU,IAAI,KAAK,IAAI,EAClD,KAAK,IAAMA,GAAQ,QAAQ,UAAU,IAAI,KAAK,IAAI,EAClD,KAAK,IAAMI,EAAS,GACxB,CAKA,OAAOC,EAAGC,EAAK,CACX,GAAI,CAACA,EACD,OAAO,MAAM,OAAOD,CAAC,EACzB,IAAME,EAAM,IAAI,IACZD,GAAK,UACLA,EAAI,SAASC,CAAG,EACpB,QAAWC,KAAQ,KAAK,MAAO,CAC3B,IAAIC,EAAKC,EAQT,GAPIZ,GAAS,OAAOU,CAAI,GACpBC,EAAMV,GAAK,KAAKS,EAAK,IAAK,GAAIF,CAAG,EACjCI,EAAQX,GAAK,KAAKS,EAAK,MAAOC,EAAKH,CAAG,GAGtCG,EAAMV,GAAK,KAAKS,EAAM,GAAIF,CAAG,EAE7BC,EAAI,IAAIE,CAAG,EACX,MAAM,IAAI,MAAM,8CAA8C,EAClEF,EAAI,IAAIE,EAAKC,CAAK,CACtB,CACA,OAAOH,CACX,CACA,OAAO,KAAKI,EAAQC,EAAUN,EAAK,CAC/B,IAAMO,EAAUX,GAAM,YAAYS,EAAQC,EAAUN,CAAG,EACjDQ,EAAO,IAAI,KACjB,OAAAA,EAAK,MAAQD,EAAQ,MACdC,CACX,CACJ,EACAX,GAAS,IAAM,yBACf,IAAMW,GAAO,CACT,WAAY,MACZ,SAAUJ,GAASA,aAAiB,IACpC,UAAWP,GACX,QAAS,GACT,IAAK,yBACL,QAAQY,EAAKC,EAAS,CAClB,IAAMH,EAAUX,GAAM,aAAaa,EAAKC,CAAO,EACzCC,EAAW,CAAC,EAClB,OAAW,CAAE,IAAAR,CAAI,IAAKI,EAAQ,MACtBf,GAAS,SAASW,CAAG,IACjBQ,EAAS,SAASR,EAAI,KAAK,EAC3BO,EAAQ,iDAAiDP,EAAI,KAAK,EAAE,EAGpEQ,EAAS,KAAKR,EAAI,KAAK,GAInC,OAAO,OAAO,OAAO,IAAIN,GAAYU,CAAO,CAChD,EACA,WAAY,CAACF,EAAQC,EAAUN,IAAQH,GAAS,KAAKQ,EAAQC,EAAUN,CAAG,CAC9E,EAEAT,GAAQ,SAAWM,GACnBN,GAAQ,KAAOiB,KC5Ef,IAAAI,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAS,IAEb,SAASC,GAAc,CAAE,MAAAC,EAAO,OAAAC,CAAO,EAAGC,EAAK,CAE3C,OAAID,IADYD,EAAQG,GAAUC,IACZ,KAAK,KAAKH,CAAM,EAC3BA,EACJD,EAAQE,EAAI,QAAQ,QAAUA,EAAI,QAAQ,QACrD,CACA,IAAMC,GAAU,CACZ,SAAUH,GAASA,IAAU,GAC7B,QAAS,GACT,IAAK,yBACL,KAAM,6CACN,QAAS,IAAM,IAAIF,GAAO,OAAO,EAAI,EACrC,UAAWC,EACf,EACMK,GAAW,CACb,SAAUJ,GAASA,IAAU,GAC7B,QAAS,GACT,IAAK,yBACL,KAAM,+CACN,QAAS,IAAM,IAAIF,GAAO,OAAO,EAAK,EACtC,UAAWC,EACf,EAEAF,GAAQ,SAAWO,GACnBP,GAAQ,QAAUM,KC5BlB,IAAAE,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAS,IACTC,GAAkB,KAEhBC,GAAW,CACb,SAAUC,GAAS,OAAOA,GAAU,SACpC,QAAS,GACT,IAAK,0BACL,KAAM,iDACN,QAAUC,GAAQA,EAAI,MAAM,EAAE,EAAE,YAAY,IAAM,MAC5C,IACAA,EAAI,CAAC,IAAM,IACP,OAAO,kBACP,OAAO,kBACjB,UAAWH,GAAgB,eAC/B,EACMI,GAAW,CACb,SAAUF,GAAS,OAAOA,GAAU,SACpC,QAAS,GACT,IAAK,0BACL,OAAQ,MACR,KAAM,wDACN,QAAUC,GAAQ,WAAWA,EAAI,QAAQ,KAAM,EAAE,CAAC,EAClD,UAAUE,EAAM,CACZ,IAAMC,EAAM,OAAOD,EAAK,KAAK,EAC7B,OAAO,SAASC,CAAG,EAAIA,EAAI,cAAc,EAAIN,GAAgB,gBAAgBK,CAAI,CACrF,CACJ,EACME,GAAQ,CACV,SAAUL,GAAS,OAAOA,GAAU,SACpC,QAAS,GACT,IAAK,0BACL,KAAM,oCACN,QAAQC,EAAK,CACT,IAAME,EAAO,IAAIN,GAAO,OAAO,WAAWI,EAAI,QAAQ,KAAM,EAAE,CAAC,CAAC,EAC1DK,EAAML,EAAI,QAAQ,GAAG,EAC3B,GAAIK,IAAQ,GAAI,CACZ,IAAMC,EAAIN,EAAI,UAAUK,EAAM,CAAC,EAAE,QAAQ,KAAM,EAAE,EAC7CC,EAAEA,EAAE,OAAS,CAAC,IAAM,MACpBJ,EAAK,kBAAoBI,EAAE,OACnC,CACA,OAAOJ,CACX,EACA,UAAWL,GAAgB,eAC/B,EAEAF,GAAQ,MAAQS,GAChBT,GAAQ,SAAWM,GACnBN,GAAQ,SAAWG,KCjDnB,IAAAS,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAkB,KAEhBC,GAAeC,GAAU,OAAOA,GAAU,UAAY,OAAO,UAAUA,CAAK,EAClF,SAASC,GAAWC,EAAKC,EAAQC,EAAO,CAAE,YAAAC,CAAY,EAAG,CACrD,IAAMC,EAAOJ,EAAI,CAAC,EAIlB,IAHII,IAAS,KAAOA,IAAS,OACzBH,GAAU,GACdD,EAAMA,EAAI,UAAUC,CAAM,EAAE,QAAQ,KAAM,EAAE,EACxCE,EAAa,CACb,OAAQD,EAAO,CACX,IAAK,GACDF,EAAM,KAAKA,CAAG,GACd,MACJ,IAAK,GACDA,EAAM,KAAKA,CAAG,GACd,MACJ,IAAK,IACDA,EAAM,KAAKA,CAAG,GACd,KACR,CACA,IAAMK,EAAI,OAAOL,CAAG,EACpB,OAAOI,IAAS,IAAM,OAAO,EAAE,EAAIC,EAAIA,CAC3C,CACA,IAAMA,EAAI,SAASL,EAAKE,CAAK,EAC7B,OAAOE,IAAS,IAAM,GAAKC,EAAIA,CACnC,CACA,SAASC,GAAaC,EAAML,EAAOM,EAAQ,CACvC,GAAM,CAAE,MAAAV,CAAM,EAAIS,EAClB,GAAIV,GAAYC,CAAK,EAAG,CACpB,IAAME,EAAMF,EAAM,SAASI,CAAK,EAChC,OAAOJ,EAAQ,EAAI,IAAMU,EAASR,EAAI,OAAO,CAAC,EAAIQ,EAASR,CAC/D,CACA,OAAOJ,GAAgB,gBAAgBW,CAAI,CAC/C,CACA,IAAME,GAAS,CACX,SAAUZ,GACV,QAAS,GACT,IAAK,wBACL,OAAQ,MACR,KAAM,mBACN,QAAS,CAACG,EAAKU,EAAUC,IAAQZ,GAAWC,EAAK,EAAG,EAAGW,CAAG,EAC1D,UAAWJ,GAAQD,GAAaC,EAAM,EAAG,IAAI,CACjD,EACMK,GAAS,CACX,SAAUf,GACV,QAAS,GACT,IAAK,wBACL,OAAQ,MACR,KAAM,kBACN,QAAS,CAACG,EAAKU,EAAUC,IAAQZ,GAAWC,EAAK,EAAG,EAAGW,CAAG,EAC1D,UAAWJ,GAAQD,GAAaC,EAAM,EAAG,GAAG,CAChD,EACMM,GAAM,CACR,SAAUhB,GACV,QAAS,GACT,IAAK,wBACL,KAAM,sBACN,QAAS,CAACG,EAAKU,EAAUC,IAAQZ,GAAWC,EAAK,EAAG,GAAIW,CAAG,EAC3D,UAAWf,GAAgB,eAC/B,EACMkB,GAAS,CACX,SAAUjB,GACV,QAAS,GACT,IAAK,wBACL,OAAQ,MACR,KAAM,yBACN,QAAS,CAACG,EAAKU,EAAUC,IAAQZ,GAAWC,EAAK,EAAG,GAAIW,CAAG,EAC3D,UAAWJ,GAAQD,GAAaC,EAAM,GAAI,IAAI,CAClD,EAEAZ,GAAQ,IAAMkB,GACdlB,GAAQ,OAASc,GACjBd,GAAQ,OAASmB,GACjBnB,GAAQ,OAASiB,KC3EjB,IAAAG,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAO,IACPC,GAAU,IAERC,GAAN,MAAMC,UAAgBF,GAAQ,OAAQ,CAClC,YAAYG,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,IAAMD,EAAQ,GACvB,CACA,IAAIE,EAAK,CACL,IAAIC,EACAP,GAAS,OAAOM,CAAG,EACnBC,EAAOD,EACFA,GACL,OAAOA,GAAQ,UACf,QAASA,GACT,UAAWA,GACXA,EAAI,QAAU,KACdC,EAAO,IAAIN,GAAK,KAAKK,EAAI,IAAK,IAAI,EAElCC,EAAO,IAAIN,GAAK,KAAKK,EAAK,IAAI,EACrBJ,GAAQ,SAAS,KAAK,MAAOK,EAAK,GAAG,GAE9C,KAAK,MAAM,KAAKA,CAAI,CAC5B,CAKA,IAAID,EAAKE,EAAU,CACf,IAAMD,EAAOL,GAAQ,SAAS,KAAK,MAAOI,CAAG,EAC7C,MAAO,CAACE,GAAYR,GAAS,OAAOO,CAAI,EAClCP,GAAS,SAASO,EAAK,GAAG,EACtBA,EAAK,IAAI,MACTA,EAAK,IACTA,CACV,CACA,IAAID,EAAKG,EAAO,CACZ,GAAI,OAAOA,GAAU,UACjB,MAAM,IAAI,MAAM,iEAAiE,OAAOA,CAAK,EAAE,EACnG,IAAMC,EAAOR,GAAQ,SAAS,KAAK,MAAOI,CAAG,EACzCI,GAAQ,CAACD,EACT,KAAK,MAAM,OAAO,KAAK,MAAM,QAAQC,CAAI,EAAG,CAAC,EAExC,CAACA,GAAQD,GACd,KAAK,MAAM,KAAK,IAAIR,GAAK,KAAKK,CAAG,CAAC,CAE1C,CACA,OAAOK,EAAGC,EAAK,CACX,OAAO,MAAM,OAAOD,EAAGC,EAAK,GAAG,CACnC,CACA,SAASA,EAAKC,EAAWC,EAAa,CAClC,GAAI,CAACF,EACD,OAAO,KAAK,UAAU,IAAI,EAC9B,GAAI,KAAK,iBAAiB,EAAI,EAC1B,OAAO,MAAM,SAAS,OAAO,OAAO,CAAC,EAAGA,EAAK,CAAE,cAAe,EAAK,CAAC,EAAGC,EAAWC,CAAW,EAE7F,MAAM,IAAI,MAAM,qCAAqC,CAC7D,CACA,OAAO,KAAKT,EAAQU,EAAUH,EAAK,CAC/B,GAAM,CAAE,SAAAI,CAAS,EAAIJ,EACfK,EAAM,IAAI,KAAKZ,CAAM,EAC3B,GAAIU,GAAY,OAAO,YAAY,OAAOA,CAAQ,EAC9C,QAASN,KAASM,EACV,OAAOC,GAAa,aACpBP,EAAQO,EAAS,KAAKD,EAAUN,EAAOA,CAAK,GAChDQ,EAAI,MAAM,KAAKhB,GAAK,WAAWQ,EAAO,KAAMG,CAAG,CAAC,EAExD,OAAOK,CACX,CACJ,EACAd,GAAQ,IAAM,wBACd,IAAMc,GAAM,CACR,WAAY,MACZ,SAAUR,GAASA,aAAiB,IACpC,UAAWN,GACX,QAAS,GACT,IAAK,wBACL,WAAY,CAACE,EAAQU,EAAUH,IAAQT,GAAQ,KAAKE,EAAQU,EAAUH,CAAG,EACzE,QAAQM,EAAKC,EAAS,CAClB,GAAInB,GAAS,MAAMkB,CAAG,EAAG,CACrB,GAAIA,EAAI,iBAAiB,EAAI,EACzB,OAAO,OAAO,OAAO,IAAIf,GAAWe,CAAG,EAEvCC,EAAQ,qCAAqC,CACrD,MAEIA,EAAQ,iCAAiC,EAC7C,OAAOD,CACX,CACJ,EAEAnB,GAAQ,QAAUI,GAClBJ,GAAQ,IAAMkB,KC/Fd,IAAAG,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAkB,KAGtB,SAASC,GAAiBC,EAAKC,EAAU,CACrC,IAAMC,EAAOF,EAAI,CAAC,EACZG,EAAQD,IAAS,KAAOA,IAAS,IAAMF,EAAI,UAAU,CAAC,EAAIA,EAC1DI,EAAOC,GAAMJ,EAAW,OAAOI,CAAC,EAAI,OAAOA,CAAC,EAC5CC,EAAMH,EACP,QAAQ,KAAM,EAAE,EAChB,MAAM,GAAG,EACT,OAAO,CAACG,EAAKC,IAAMD,EAAMF,EAAI,EAAE,EAAIA,EAAIG,CAAC,EAAGH,EAAI,CAAC,CAAC,EACtD,OAAQF,IAAS,IAAME,EAAI,EAAE,EAAIE,EAAMA,CAC3C,CAMA,SAASE,GAAqBC,EAAM,CAChC,GAAI,CAAE,MAAAC,CAAM,EAAID,EACZL,EAAOC,GAAMA,EACjB,GAAI,OAAOK,GAAU,SACjBN,EAAMC,GAAK,OAAOA,CAAC,UACd,MAAMK,CAAK,GAAK,CAAC,SAASA,CAAK,EACpC,OAAOZ,GAAgB,gBAAgBW,CAAI,EAC/C,IAAIP,EAAO,GACPQ,EAAQ,IACRR,EAAO,IACPQ,GAASN,EAAI,EAAE,GAEnB,IAAMO,EAAMP,EAAI,EAAE,EACZD,EAAQ,CAACO,EAAQC,CAAG,EAC1B,OAAID,EAAQ,GACRP,EAAM,QAAQ,CAAC,GAGfO,GAASA,EAAQP,EAAM,CAAC,GAAKQ,EAC7BR,EAAM,QAAQO,EAAQC,CAAG,EACrBD,GAAS,KACTA,GAASA,EAAQP,EAAM,CAAC,GAAKQ,EAC7BR,EAAM,QAAQO,CAAK,IAGnBR,EACJC,EACK,IAAIE,GAAK,OAAOA,CAAC,EAAE,SAAS,EAAG,GAAG,CAAC,EACnC,KAAK,GAAG,EACR,QAAQ,aAAc,EAAE,CAErC,CACA,IAAMO,GAAU,CACZ,SAAUF,GAAS,OAAOA,GAAU,UAAY,OAAO,UAAUA,CAAK,EACtE,QAAS,GACT,IAAK,wBACL,OAAQ,OACR,KAAM,uCACN,QAAS,CAACV,EAAKa,EAAU,CAAE,YAAAC,CAAY,IAAMf,GAAiBC,EAAKc,CAAW,EAC9E,UAAWN,EACf,EACMO,GAAY,CACd,SAAUL,GAAS,OAAOA,GAAU,SACpC,QAAS,GACT,IAAK,0BACL,OAAQ,OACR,KAAM,gDACN,QAASV,GAAOD,GAAiBC,EAAK,EAAK,EAC3C,UAAWQ,EACf,EACMQ,GAAY,CACd,SAAUN,GAASA,aAAiB,KACpC,QAAS,GACT,IAAK,8BAIL,KAAM,OAAO,2JAKJ,EACT,QAAQV,EAAK,CACT,IAAMiB,EAAQjB,EAAI,MAAMgB,GAAU,IAAI,EACtC,GAAI,CAACC,EACD,MAAM,IAAI,MAAM,sDAAsD,EAC1E,GAAM,CAAC,CAAEC,EAAMC,EAAOC,EAAKC,EAAMC,EAAQC,CAAM,EAAIN,EAAM,IAAI,MAAM,EAC7DO,EAAWP,EAAM,CAAC,EAAI,QAAQA,EAAM,CAAC,EAAI,MAAM,OAAO,EAAG,CAAC,CAAC,EAAI,EACjEQ,EAAO,KAAK,IAAIP,EAAMC,EAAQ,EAAGC,EAAKC,GAAQ,EAAGC,GAAU,EAAGC,GAAU,EAAGC,CAAQ,EACjFE,EAAKT,EAAM,CAAC,EAClB,GAAIS,GAAMA,IAAO,IAAK,CAClB,IAAIC,EAAI5B,GAAiB2B,EAAI,EAAK,EAC9B,KAAK,IAAIC,CAAC,EAAI,KACdA,GAAK,IACTF,GAAQ,IAAQE,CACpB,CACA,OAAO,IAAI,KAAKF,CAAI,CACxB,EACA,UAAW,CAAC,CAAE,MAAAf,CAAM,IAAMA,EAAM,YAAY,EAAE,QAAQ,sBAAuB,EAAE,CACnF,EAEAb,GAAQ,UAAYkB,GACpBlB,GAAQ,QAAUe,GAClBf,GAAQ,UAAYmB,KCxGpB,IAAAY,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAM,KACNC,GAAQ,KACRC,GAAM,KACNC,GAAS,KACTC,GAAS,KACTC,GAAO,KACPC,GAAQ,KACRC,GAAM,KACNC,GAAQ,KACRC,GAAO,KACPC,GAAQ,KACRC,GAAM,KACNC,GAAY,KAEVC,GAAS,CACXb,GAAI,IACJE,GAAI,IACJC,GAAO,OACPF,GAAM,QACNI,GAAK,QACLA,GAAK,SACLE,GAAI,OACJA,GAAI,OACJA,GAAI,IACJA,GAAI,OACJD,GAAM,SACNA,GAAM,SACNA,GAAM,MACNF,GAAO,OACPI,GAAM,MACNC,GAAK,KACLC,GAAM,MACNC,GAAI,IACJC,GAAU,QACVA,GAAU,UACVA,GAAU,SACd,EAEAb,GAAQ,OAASc,KCxCjB,IAAAC,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAM,KACNC,GAAQ,KACRC,GAAM,KACNC,GAAS,KACTC,GAAO,KACPC,GAAQ,KACRC,GAAM,KACNC,GAAS,KACTC,GAAW,KACXC,GAAS,KACTC,GAAQ,KACRC,GAAO,KACPC,GAAQ,KACRC,GAAW,KACXC,GAAM,KACNC,GAAY,KAEVC,GAAU,IAAI,IAAI,CACpB,CAAC,OAAQT,GAAO,MAAM,EACtB,CAAC,WAAY,CAACP,GAAI,IAAKE,GAAI,IAAKC,GAAO,MAAM,CAAC,EAC9C,CAAC,OAAQK,GAAS,MAAM,EACxB,CAAC,SAAUK,GAAS,MAAM,EAC1B,CAAC,WAAYA,GAAS,MAAM,CAChC,CAAC,EACKI,GAAa,CACf,OAAQR,GAAO,OACf,KAAML,GAAK,QACX,MAAOC,GAAM,MACb,SAAUA,GAAM,SAChB,SAAUA,GAAM,SAChB,UAAWU,GAAU,UACrB,IAAKT,GAAI,IACT,OAAQA,GAAI,OACZ,OAAQA,GAAI,OACZ,QAASS,GAAU,QACnB,IAAKf,GAAI,IACT,MAAOU,GAAM,MACb,KAAMT,GAAM,QACZ,KAAMU,GAAK,KACX,MAAOC,GAAM,MACb,IAAKV,GAAI,IACT,IAAKY,GAAI,IACT,UAAWC,GAAU,SACzB,EACMG,GAAgB,CAClB,2BAA4BT,GAAO,OACnC,0BAA2BC,GAAM,MACjC,yBAA0BC,GAAK,KAC/B,0BAA2BC,GAAM,MACjC,wBAAyBE,GAAI,IAC7B,8BAA+BC,GAAU,SAC7C,EACA,SAASI,GAAQC,EAAYC,EAAYC,EAAa,CAClD,IAAMC,EAAaP,GAAQ,IAAIK,CAAU,EACzC,GAAIE,GAAc,CAACH,EACf,OAAOE,GAAe,CAACC,EAAW,SAASb,GAAM,KAAK,EAChDa,EAAW,OAAOb,GAAM,KAAK,EAC7Ba,EAAW,MAAM,EAE3B,IAAIC,EAAOD,EACX,GAAI,CAACC,EACD,GAAI,MAAM,QAAQJ,CAAU,EACxBI,EAAO,CAAC,MACP,CACD,IAAMC,EAAO,MAAM,KAAKT,GAAQ,KAAK,CAAC,EACjC,OAAOU,GAAOA,IAAQ,QAAQ,EAC9B,IAAIA,GAAO,KAAK,UAAUA,CAAG,CAAC,EAC9B,KAAK,IAAI,EACd,MAAM,IAAI,MAAM,mBAAmBL,CAAU,iBAAiBI,CAAI,6BAA6B,CACnG,CAEJ,GAAI,MAAM,QAAQL,CAAU,EACxB,QAAWO,KAAOP,EACdI,EAAOA,EAAK,OAAOG,CAAG,OAErB,OAAOP,GAAe,aAC3BI,EAAOJ,EAAWI,EAAK,MAAM,CAAC,GAElC,OAAIF,IACAE,EAAOA,EAAK,OAAOd,GAAM,KAAK,GAC3Bc,EAAK,OAAO,CAACA,EAAMG,IAAQ,CAC9B,IAAMC,EAAS,OAAOD,GAAQ,SAAWV,GAAWU,CAAG,EAAIA,EAC3D,GAAI,CAACC,EAAQ,CACT,IAAMC,EAAU,KAAK,UAAUF,CAAG,EAC5BF,EAAO,OAAO,KAAKR,EAAU,EAC9B,IAAIS,GAAO,KAAK,UAAUA,CAAG,CAAC,EAC9B,KAAK,IAAI,EACd,MAAM,IAAI,MAAM,sBAAsBG,CAAO,gBAAgBJ,CAAI,EAAE,CACvE,CACA,OAAKD,EAAK,SAASI,CAAM,GACrBJ,EAAK,KAAKI,CAAM,EACbJ,CACX,EAAG,CAAC,CAAC,CACT,CAEAzB,GAAQ,cAAgBmB,GACxBnB,GAAQ,QAAUoB,KClGlB,IAAAW,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAM,KACNC,GAAM,KACNC,GAAS,KACTC,GAAO,KAELC,GAAsB,CAACC,EAAGC,IAAMD,EAAE,IAAMC,EAAE,IAAM,GAAKD,EAAE,IAAMC,EAAE,IAAM,EAAI,EACzEC,GAAN,MAAMC,CAAO,CACT,YAAY,CAAE,OAAAC,EAAQ,WAAAC,EAAY,MAAAC,EAAO,iBAAAC,EAAkB,OAAAC,EAAQ,eAAAC,EAAgB,iBAAAC,CAAiB,EAAG,CACnG,KAAK,OAAS,MAAM,QAAQN,CAAM,EAC5BN,GAAK,QAAQM,EAAQ,QAAQ,EAC7BA,EACIN,GAAK,QAAQ,KAAMM,CAAM,EACzB,KACV,KAAK,KAAQ,OAAOI,GAAW,UAAYA,GAAW,OACtD,KAAK,UAAYD,EAAmBT,GAAK,cAAgB,CAAC,EAC1D,KAAK,KAAOA,GAAK,QAAQO,EAAY,KAAK,KAAMC,CAAK,EACrD,KAAK,gBAAkBI,GAAoB,KAC3C,OAAO,eAAe,KAAMhB,GAAS,IAAK,CAAE,MAAOC,GAAI,GAAI,CAAC,EAC5D,OAAO,eAAe,KAAMD,GAAS,OAAQ,CAAE,MAAOG,GAAO,MAAO,CAAC,EACrE,OAAO,eAAe,KAAMH,GAAS,IAAK,CAAE,MAAOE,GAAI,GAAI,CAAC,EAE5D,KAAK,eACD,OAAOa,GAAmB,WACpBA,EACAA,IAAmB,GACfV,GACA,IAClB,CACA,OAAQ,CACJ,IAAMY,EAAO,OAAO,OAAOR,EAAO,UAAW,OAAO,0BAA0B,IAAI,CAAC,EACnF,OAAAQ,EAAK,KAAO,KAAK,KAAK,MAAM,EACrBA,CACX,CACJ,EAEAlB,GAAQ,OAASS,KCtCjB,IAAAU,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAY,KACZC,GAAmB,KAEvB,SAASC,GAAkBC,EAAKC,EAAS,CACrC,IAAMC,EAAQ,CAAC,EACXC,EAAgBF,EAAQ,aAAe,GAC3C,GAAIA,EAAQ,aAAe,IAASD,EAAI,WAAY,CAChD,IAAMI,EAAMJ,EAAI,WAAW,SAASA,CAAG,EACnCI,GACAF,EAAM,KAAKE,CAAG,EACdD,EAAgB,IAEXH,EAAI,WAAW,WACpBG,EAAgB,GACxB,CACIA,GACAD,EAAM,KAAK,KAAK,EACpB,IAAMG,EAAMR,GAAU,uBAAuBG,EAAKC,CAAO,EACnD,CAAE,cAAAK,CAAc,EAAID,EAAI,QAC9B,GAAIL,EAAI,cAAe,CACfE,EAAM,SAAW,GACjBA,EAAM,QAAQ,EAAE,EACpB,IAAMK,EAAKD,EAAcN,EAAI,aAAa,EAC1CE,EAAM,QAAQJ,GAAiB,cAAcS,EAAI,EAAE,CAAC,CACxD,CACA,IAAIC,EAAY,GACZC,EAAiB,KACrB,GAAIT,EAAI,SAAU,CACd,GAAIJ,GAAS,OAAOI,EAAI,QAAQ,EAAG,CAG/B,GAFIA,EAAI,SAAS,aAAeG,GAC5BD,EAAM,KAAK,EAAE,EACbF,EAAI,SAAS,cAAe,CAC5B,IAAMO,EAAKD,EAAcN,EAAI,SAAS,aAAa,EACnDE,EAAM,KAAKJ,GAAiB,cAAcS,EAAI,EAAE,CAAC,CACrD,CAEAF,EAAI,iBAAmB,CAAC,CAACL,EAAI,QAC7BS,EAAiBT,EAAI,SAAS,OAClC,CACA,IAAMU,EAAcD,EAAiB,OAAY,IAAOD,EAAY,GAChEG,EAAOd,GAAU,UAAUG,EAAI,SAAUK,EAAK,IAAOI,EAAiB,KAAOC,CAAW,EACxFD,IACAE,GAAQb,GAAiB,YAAYa,EAAM,GAAIL,EAAcG,CAAc,CAAC,IAC3EE,EAAK,CAAC,IAAM,KAAOA,EAAK,CAAC,IAAM,MAChCT,EAAMA,EAAM,OAAS,CAAC,IAAM,MAG5BA,EAAMA,EAAM,OAAS,CAAC,EAAI,OAAOS,CAAI,GAGrCT,EAAM,KAAKS,CAAI,CACvB,MAEIT,EAAM,KAAKL,GAAU,UAAUG,EAAI,SAAUK,CAAG,CAAC,EAErD,GAAIL,EAAI,YAAY,OAChB,GAAIA,EAAI,QAAS,CACb,IAAMO,EAAKD,EAAcN,EAAI,OAAO,EAChCO,EAAG,SAAS;AAAA,CAAI,GAChBL,EAAM,KAAK,KAAK,EAChBA,EAAM,KAAKJ,GAAiB,cAAcS,EAAI,EAAE,CAAC,GAGjDL,EAAM,KAAK,OAAOK,CAAE,EAAE,CAE9B,MAEIL,EAAM,KAAK,KAAK,MAGnB,CACD,IAAIU,EAAKZ,EAAI,QACTY,GAAMJ,IACNI,EAAKA,EAAG,QAAQ,OAAQ,EAAE,GAC1BA,KACK,CAACJ,GAAaC,IAAmBP,EAAMA,EAAM,OAAS,CAAC,IAAM,IAC9DA,EAAM,KAAK,EAAE,EACjBA,EAAM,KAAKJ,GAAiB,cAAcQ,EAAcM,CAAE,EAAG,EAAE,CAAC,EAExE,CACA,OAAOV,EAAM,KAAK;AAAA,CAAI,EAAI;AAAA,CAC9B,CAEAP,GAAQ,kBAAoBI,KCtF5B,IAAAc,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAQ,KACRC,GAAa,KACbC,EAAW,IACXC,GAAO,IACPC,GAAO,IACPC,GAAS,KACTC,GAAoB,KACpBC,GAAU,KACVC,GAAe,KACfC,GAAa,KACbC,GAAa,KAEXC,GAAN,MAAMC,CAAS,CACX,YAAYC,EAAOC,EAAUC,EAAS,CAElC,KAAK,cAAgB,KAErB,KAAK,QAAU,KAEf,KAAK,OAAS,CAAC,EAEf,KAAK,SAAW,CAAC,EACjB,OAAO,eAAe,KAAMb,EAAS,UAAW,CAAE,MAAOA,EAAS,GAAI,CAAC,EACvE,IAAIc,EAAY,KACZ,OAAOF,GAAa,YAAc,MAAM,QAAQA,CAAQ,EACxDE,EAAYF,EAEPC,IAAY,QAAaD,IAC9BC,EAAUD,EACVA,EAAW,QAEf,IAAMG,EAAM,OAAO,OAAO,CACtB,YAAa,GACb,iBAAkB,GAClB,SAAU,OACV,aAAc,GACd,OAAQ,GACR,WAAY,GACZ,WAAY,GACZ,QAAS,KACb,EAAGF,CAAO,EACV,KAAK,QAAUE,EACf,GAAI,CAAE,QAAAC,CAAQ,EAAID,EACdF,GAAS,aACT,KAAK,WAAaA,EAAQ,YAAY,WAAW,EAC7C,KAAK,WAAW,KAAK,WACrBG,EAAU,KAAK,WAAW,KAAK,UAGnC,KAAK,WAAa,IAAIR,GAAW,WAAW,CAAE,QAAAQ,CAAQ,CAAC,EAC3D,KAAK,UAAUA,EAASH,CAAO,EAE/B,KAAK,SACDF,IAAU,OAAY,KAAO,KAAK,WAAWA,EAAOG,EAAWD,CAAO,CAC9E,CAMA,OAAQ,CACJ,IAAMI,EAAO,OAAO,OAAOP,EAAS,UAAW,CAC3C,CAACV,EAAS,SAAS,EAAG,CAAE,MAAOA,EAAS,GAAI,CAChD,CAAC,EACD,OAAAiB,EAAK,cAAgB,KAAK,cAC1BA,EAAK,QAAU,KAAK,QACpBA,EAAK,OAAS,KAAK,OAAO,MAAM,EAChCA,EAAK,SAAW,KAAK,SAAS,MAAM,EACpCA,EAAK,QAAU,OAAO,OAAO,CAAC,EAAG,KAAK,OAAO,EACzC,KAAK,aACLA,EAAK,WAAa,KAAK,WAAW,MAAM,GAC5CA,EAAK,OAAS,KAAK,OAAO,MAAM,EAEhCA,EAAK,SAAWjB,EAAS,OAAO,KAAK,QAAQ,EACvC,KAAK,SAAS,MAAMiB,EAAK,MAAM,EAC/B,KAAK,SACP,KAAK,QACLA,EAAK,MAAQ,KAAK,MAAM,MAAM,GAC3BA,CACX,CAEA,IAAIN,EAAO,CACHO,GAAiB,KAAK,QAAQ,GAC9B,KAAK,SAAS,IAAIP,CAAK,CAC/B,CAEA,MAAMQ,EAAMR,EAAO,CACXO,GAAiB,KAAK,QAAQ,GAC9B,KAAK,SAAS,MAAMC,EAAMR,CAAK,CACvC,CAUA,YAAYS,EAAMC,EAAM,CACpB,GAAI,CAACD,EAAK,OAAQ,CACd,IAAME,EAAOjB,GAAQ,YAAY,IAAI,EACrCe,EAAK,OAED,CAACC,GAAQC,EAAK,IAAID,CAAI,EAAIhB,GAAQ,cAAcgB,GAAQ,IAAKC,CAAI,EAAID,CAC7E,CACA,OAAO,IAAIvB,GAAM,MAAMsB,EAAK,MAAM,CACtC,CACA,WAAWT,EAAOC,EAAUC,EAAS,CACjC,IAAIC,EACJ,GAAI,OAAOF,GAAa,WACpBD,EAAQC,EAAS,KAAK,CAAE,GAAID,CAAM,EAAG,GAAIA,CAAK,EAC9CG,EAAYF,UAEP,MAAM,QAAQA,CAAQ,EAAG,CAC9B,IAAMW,EAAYC,GAAM,OAAOA,GAAM,UAAYA,aAAa,QAAUA,aAAa,OAC/EC,EAAQb,EAAS,OAAOW,CAAQ,EAAE,IAAI,MAAM,EAC9CE,EAAM,OAAS,IACfb,EAAWA,EAAS,OAAOa,CAAK,GACpCX,EAAYF,CAChB,MACSC,IAAY,QAAaD,IAC9BC,EAAUD,EACVA,EAAW,QAEf,GAAM,CAAE,sBAAAc,EAAuB,aAAAC,EAAc,KAAAC,EAAM,cAAAC,EAAe,SAAAC,EAAU,IAAAC,CAAI,EAAIlB,GAAW,CAAC,EAC1F,CAAE,SAAAmB,EAAU,WAAAC,EAAY,cAAAC,CAAc,EAAI7B,GAAQ,kBAAkB,KAE1EsB,GAAgB,GAAG,EACbQ,EAAM,CACR,sBAAuBT,GAAyB,GAChD,cAAeG,GAAiB,GAChC,SAAAG,EACA,SAAAF,EACA,SAAUhB,EACV,OAAQ,KAAK,OACb,cAAAoB,CACJ,EACMd,EAAOb,GAAW,WAAWI,EAAOoB,EAAKI,CAAG,EAClD,OAAIP,GAAQ5B,EAAS,aAAaoB,CAAI,IAClCA,EAAK,KAAO,IAChBa,EAAW,EACJb,CACX,CAKA,WAAWgB,EAAKzB,EAAOE,EAAU,CAAC,EAAG,CACjC,IAAMwB,EAAI,KAAK,WAAWD,EAAK,KAAMvB,CAAO,EACtCW,EAAI,KAAK,WAAWb,EAAO,KAAME,CAAO,EAC9C,OAAO,IAAIZ,GAAK,KAAKoC,EAAGb,CAAC,CAC7B,CAKA,OAAOY,EAAK,CACR,OAAOlB,GAAiB,KAAK,QAAQ,EAAI,KAAK,SAAS,OAAOkB,CAAG,EAAI,EACzE,CAKA,SAASjB,EAAM,CACX,OAAIpB,GAAW,YAAYoB,CAAI,EACvB,KAAK,UAAY,KACV,IAEX,KAAK,SAAW,KACT,IAEJD,GAAiB,KAAK,QAAQ,EAC/B,KAAK,SAAS,SAASC,CAAI,EAC3B,EACV,CAMA,IAAIiB,EAAKE,EAAY,CACjB,OAAOtC,EAAS,aAAa,KAAK,QAAQ,EACpC,KAAK,SAAS,IAAIoC,EAAKE,CAAU,EACjC,MACV,CAMA,MAAMnB,EAAMmB,EAAY,CACpB,OAAIvC,GAAW,YAAYoB,CAAI,EACpB,CAACmB,GAActC,EAAS,SAAS,KAAK,QAAQ,EAC/C,KAAK,SAAS,MACd,KAAK,SACRA,EAAS,aAAa,KAAK,QAAQ,EACpC,KAAK,SAAS,MAAMmB,EAAMmB,CAAU,EACpC,MACV,CAIA,IAAIF,EAAK,CACL,OAAOpC,EAAS,aAAa,KAAK,QAAQ,EAAI,KAAK,SAAS,IAAIoC,CAAG,EAAI,EAC3E,CAIA,MAAMjB,EAAM,CACR,OAAIpB,GAAW,YAAYoB,CAAI,EACpB,KAAK,WAAa,OACtBnB,EAAS,aAAa,KAAK,QAAQ,EAAI,KAAK,SAAS,MAAMmB,CAAI,EAAI,EAC9E,CAKA,IAAIiB,EAAKzB,EAAO,CACR,KAAK,UAAY,KAEjB,KAAK,SAAWZ,GAAW,mBAAmB,KAAK,OAAQ,CAACqC,CAAG,EAAGzB,CAAK,EAElEO,GAAiB,KAAK,QAAQ,GACnC,KAAK,SAAS,IAAIkB,EAAKzB,CAAK,CAEpC,CAKA,MAAMQ,EAAMR,EAAO,CACXZ,GAAW,YAAYoB,CAAI,EAE3B,KAAK,SAAWR,EAEX,KAAK,UAAY,KAEtB,KAAK,SAAWZ,GAAW,mBAAmB,KAAK,OAAQ,MAAM,KAAKoB,CAAI,EAAGR,CAAK,EAE7EO,GAAiB,KAAK,QAAQ,GACnC,KAAK,SAAS,MAAMC,EAAMR,CAAK,CAEvC,CAQA,UAAUK,EAASH,EAAU,CAAC,EAAG,CACzB,OAAOG,GAAY,WACnBA,EAAU,OAAOA,CAAO,GAC5B,IAAID,EACJ,OAAQC,EAAS,CACb,IAAK,MACG,KAAK,WACL,KAAK,WAAW,KAAK,QAAU,MAE/B,KAAK,WAAa,IAAIR,GAAW,WAAW,CAAE,QAAS,KAAM,CAAC,EAClEO,EAAM,CAAE,iBAAkB,GAAO,OAAQ,UAAW,EACpD,MACJ,IAAK,MACL,IAAK,OACG,KAAK,WACL,KAAK,WAAW,KAAK,QAAUC,EAE/B,KAAK,WAAa,IAAIR,GAAW,WAAW,CAAE,QAAAQ,CAAQ,CAAC,EAC3DD,EAAM,CAAE,iBAAkB,GAAM,OAAQ,MAAO,EAC/C,MACJ,KAAK,KACG,KAAK,YACL,OAAO,KAAK,WAChBA,EAAM,KACN,MACJ,QAAS,CACL,IAAMwB,EAAK,KAAK,UAAUvB,CAAO,EACjC,MAAM,IAAI,MAAM,+DAA+DuB,CAAE,EAAE,CACvF,CACJ,CAEA,GAAI1B,EAAQ,kBAAkB,OAC1B,KAAK,OAASA,EAAQ,eACjBE,EACL,KAAK,OAAS,IAAIZ,GAAO,OAAO,OAAO,OAAOY,EAAKF,CAAO,CAAC,MAE3D,OAAM,IAAI,MAAM,qEAAqE,CAC7F,CAEA,KAAK,CAAE,KAAA2B,EAAM,QAAAC,EAAS,SAAAC,EAAU,cAAAC,EAAe,SAAAX,EAAU,QAAAY,CAAQ,EAAI,CAAC,EAAG,CACrE,IAAMT,EAAM,CACR,QAAS,IAAI,IACb,IAAK,KACL,KAAM,CAACK,EACP,SAAUE,IAAa,GACvB,aAAc,GACd,cAAe,OAAOC,GAAkB,SAAWA,EAAgB,GACvE,EACME,EAAM3C,GAAK,KAAK,KAAK,SAAUuC,GAAW,GAAIN,CAAG,EACvD,GAAI,OAAOH,GAAa,WACpB,OAAW,CAAE,MAAAc,EAAO,IAAAD,CAAI,IAAKV,EAAI,QAAQ,OAAO,EAC5CH,EAASa,EAAKC,CAAK,EAC3B,OAAO,OAAOF,GAAY,WACpBtC,GAAa,aAAasC,EAAS,CAAE,GAAIC,CAAI,EAAG,GAAIA,CAAG,EACvDA,CACV,CAOA,OAAOJ,EAAST,EAAU,CACtB,OAAO,KAAK,KAAK,CAAE,KAAM,GAAM,QAAAS,EAAS,SAAU,GAAO,SAAAT,CAAS,CAAC,CACvE,CAEA,SAASnB,EAAU,CAAC,EAAG,CACnB,GAAI,KAAK,OAAO,OAAS,EACrB,MAAM,IAAI,MAAM,4CAA4C,EAChE,GAAI,WAAYA,IACX,CAAC,OAAO,UAAUA,EAAQ,MAAM,GAAK,OAAOA,EAAQ,MAAM,GAAK,GAAI,CACpE,IAAMkC,EAAI,KAAK,UAAUlC,EAAQ,MAAM,EACvC,MAAM,IAAI,MAAM,mDAAmDkC,CAAC,EAAE,CAC1E,CACA,OAAO3C,GAAkB,kBAAkB,KAAMS,CAAO,CAC5D,CACJ,EACA,SAASK,GAAiB8B,EAAU,CAChC,GAAIhD,EAAS,aAAagD,CAAQ,EAC9B,MAAO,GACX,MAAM,IAAI,MAAM,iDAAiD,CACrE,CAEAnD,GAAQ,SAAWY,KChVnB,IAAAwC,GAAAC,EAAAC,IAAA,cAEA,IAAMC,GAAN,cAAwB,KAAM,CAC1B,YAAYC,EAAMC,EAAKC,EAAMC,EAAS,CAClC,MAAM,EACN,KAAK,KAAOH,EACZ,KAAK,KAAOE,EACZ,KAAK,QAAUC,EACf,KAAK,IAAMF,CACf,CACJ,EACMG,GAAN,cAA6BL,EAAU,CACnC,YAAYE,EAAKC,EAAMC,EAAS,CAC5B,MAAM,iBAAkBF,EAAKC,EAAMC,CAAO,CAC9C,CACJ,EACME,GAAN,cAA0BN,EAAU,CAChC,YAAYE,EAAKC,EAAMC,EAAS,CAC5B,MAAM,cAAeF,EAAKC,EAAMC,CAAO,CAC3C,CACJ,EACMG,GAAgB,CAACC,EAAKC,IAAQC,GAAU,CAC1C,GAAIA,EAAM,IAAI,CAAC,IAAM,GACjB,OACJA,EAAM,QAAUA,EAAM,IAAI,IAAIR,GAAOO,EAAG,QAAQP,CAAG,CAAC,EACpD,GAAM,CAAE,KAAAS,EAAM,IAAAC,CAAI,EAAIF,EAAM,QAAQ,CAAC,EACrCA,EAAM,SAAW,YAAYC,CAAI,YAAYC,CAAG,GAChD,IAAIC,EAAKD,EAAM,EACXE,EAAUN,EACT,UAAUC,EAAG,WAAWE,EAAO,CAAC,EAAGF,EAAG,WAAWE,CAAI,CAAC,EACtD,QAAQ,WAAY,EAAE,EAE3B,GAAIE,GAAM,IAAMC,EAAQ,OAAS,GAAI,CACjC,IAAMC,EAAY,KAAK,IAAIF,EAAK,GAAIC,EAAQ,OAAS,EAAE,EACvDA,EAAU,SAAMA,EAAQ,UAAUC,CAAS,EAC3CF,GAAME,EAAY,CACtB,CAIA,GAHID,EAAQ,OAAS,KACjBA,EAAUA,EAAQ,UAAU,EAAG,EAAE,EAAI,UAErCH,EAAO,GAAK,OAAO,KAAKG,EAAQ,UAAU,EAAGD,CAAE,CAAC,EAAG,CAEnD,IAAIG,EAAOR,EAAI,UAAUC,EAAG,WAAWE,EAAO,CAAC,EAAGF,EAAG,WAAWE,EAAO,CAAC,CAAC,EACrEK,EAAK,OAAS,KACdA,EAAOA,EAAK,UAAU,EAAG,EAAE,EAAI;AAAA,GACnCF,EAAUE,EAAOF,CACrB,CACA,GAAI,OAAO,KAAKA,CAAO,EAAG,CACtB,IAAIG,EAAQ,EACNC,EAAMR,EAAM,QAAQ,CAAC,EACvBQ,GAAOA,EAAI,OAASP,GAAQO,EAAI,IAAMN,IACtCK,EAAQ,KAAK,IAAI,EAAG,KAAK,IAAIC,EAAI,IAAMN,EAAK,GAAKC,CAAE,CAAC,GAExD,IAAMM,EAAU,IAAI,OAAON,CAAE,EAAI,IAAI,OAAOI,CAAK,EACjDP,EAAM,SAAW;AAAA;AAAA,EAAQI,CAAO;AAAA,EAAKK,CAAO;AAAA,CAChD,CACJ,EAEApB,GAAQ,UAAYC,GACpBD,GAAQ,eAAiBM,GACzBN,GAAQ,YAAcO,GACtBP,GAAQ,cAAgBQ,KC7DxB,IAAAa,GAAAC,EAAAC,IAAA,cAEA,SAASC,GAAaC,EAAQ,CAAE,KAAAC,EAAM,UAAAC,EAAW,KAAAC,EAAM,OAAAC,EAAQ,QAAAC,EAAS,aAAAC,EAAc,eAAAC,CAAe,EAAG,CACpG,IAAIC,EAAc,GACdC,EAAYF,EACZG,EAAWH,EACXI,EAAU,GACVC,EAAa,GACbC,EAAa,GACbC,EAAW,GACXC,EAAM,KACNC,EAAS,KACTC,EAAM,KACNC,EAAmB,KACnBC,EAAQ,KACRC,EAAQ,KACRC,EAAQ,KACZ,QAAWC,KAAStB,EAchB,OAbIc,IACIQ,EAAM,OAAS,SACfA,EAAM,OAAS,WACfA,EAAM,OAAS,SACfjB,EAAQiB,EAAM,OAAQ,eAAgB,uEAAuE,EACjHR,EAAW,IAEXC,IACIN,GAAaa,EAAM,OAAS,WAAaA,EAAM,OAAS,WACxDjB,EAAQU,EAAK,gBAAiB,qCAAqC,EAEvEA,EAAM,MAEFO,EAAM,KAAM,CAChB,IAAK,QAIG,CAACrB,IACAC,IAAc,aAAeC,GAAM,OAAS,oBAC7CmB,EAAM,OAAO,SAAS,GAAI,IAC1BP,EAAMO,GAEVZ,EAAW,GACX,MACJ,IAAK,UAAW,CACPA,GACDL,EAAQiB,EAAO,eAAgB,wEAAwE,EAC3G,IAAMC,EAAKD,EAAM,OAAO,UAAU,CAAC,GAAK,IACnCX,EAGDA,GAAWC,EAAaW,EAFxBZ,EAAUY,EAGdX,EAAa,GACbH,EAAY,GACZ,KACJ,CACA,IAAK,UACGA,EACIE,EACAA,GAAWW,EAAM,QACZ,CAACF,GAASlB,IAAc,kBAC7BM,EAAc,IAGlBI,GAAcU,EAAM,OACxBb,EAAY,GACZI,EAAa,IACTG,GAAUC,KACVC,EAAmBI,GACvBZ,EAAW,GACX,MACJ,IAAK,SACGM,GACAX,EAAQiB,EAAO,mBAAoB,oCAAoC,EACvEA,EAAM,OAAO,SAAS,GAAG,GACzBjB,EAAQiB,EAAM,OAASA,EAAM,OAAO,OAAS,EAAG,YAAa,kCAAmC,EAAI,EACxGN,EAASM,EACLD,IAAU,OACVA,EAAQC,EAAM,QAClBb,EAAY,GACZC,EAAW,GACXI,EAAW,GACX,MACJ,IAAK,MAAO,CACJG,GACAZ,EAAQiB,EAAO,gBAAiB,iCAAiC,EACrEL,EAAMK,EACFD,IAAU,OACVA,EAAQC,EAAM,QAClBb,EAAY,GACZC,EAAW,GACXI,EAAW,GACX,KACJ,CACA,KAAKZ,GAEGc,GAAUC,IACVZ,EAAQiB,EAAO,iBAAkB,sCAAsCA,EAAM,MAAM,YAAY,EAC/FF,GACAf,EAAQiB,EAAO,mBAAoB,cAAcA,EAAM,MAAM,OAAOrB,GAAQ,YAAY,EAAE,EAC9FmB,EAAQE,EACRb,EACIP,IAAc,gBAAkBA,IAAc,mBAClDQ,EAAW,GACX,MACJ,IAAK,QACD,GAAIT,EAAM,CACFkB,GACAd,EAAQiB,EAAO,mBAAoB,mBAAmBrB,CAAI,EAAE,EAChEkB,EAAQG,EACRb,EAAY,GACZC,EAAW,GACX,KACJ,CAEJ,QACIL,EAAQiB,EAAO,mBAAoB,cAAcA,EAAM,IAAI,QAAQ,EACnEb,EAAY,GACZC,EAAW,EACnB,CAEJ,IAAMc,EAAOxB,EAAOA,EAAO,OAAS,CAAC,EAC/ByB,EAAMD,EAAOA,EAAK,OAASA,EAAK,OAAO,OAASpB,EACtD,OAAIU,GACAX,GACAA,EAAK,OAAS,SACdA,EAAK,OAAS,WACdA,EAAK,OAAS,UACbA,EAAK,OAAS,UAAYA,EAAK,SAAW,KAC3CE,EAAQF,EAAK,OAAQ,eAAgB,uEAAuE,EAE5GY,IACEN,GAAaM,EAAI,QAAUT,GACzBH,GAAM,OAAS,aACfA,GAAM,OAAS,cACnBE,EAAQU,EAAK,gBAAiB,qCAAqC,EAChE,CACH,MAAAI,EACA,MAAAC,EACA,YAAAZ,EACA,QAAAG,EACA,WAAAE,EACA,OAAAG,EACA,IAAAC,EACA,iBAAAC,EACA,IAAAO,EACA,MAAOJ,GAASI,CACpB,CACJ,CAEA3B,GAAQ,aAAeC,KCrJvB,IAAA2B,GAAAC,EAAAC,IAAA,cAEA,SAASC,GAAgBC,EAAK,CAC1B,GAAI,CAACA,EACD,OAAO,KACX,OAAQA,EAAI,KAAM,CACd,IAAK,QACL,IAAK,SACL,IAAK,uBACL,IAAK,uBACD,GAAIA,EAAI,OAAO,SAAS;AAAA,CAAI,EACxB,MAAO,GACX,GAAIA,EAAI,KACJ,QAAWC,KAAMD,EAAI,IACjB,GAAIC,EAAG,OAAS,UACZ,MAAO,GACnB,MAAO,GACX,IAAK,kBACD,QAAWC,KAAMF,EAAI,MAAO,CACxB,QAAWC,KAAMC,EAAG,MAChB,GAAID,EAAG,OAAS,UACZ,MAAO,GACf,GAAIC,EAAG,KACH,QAAWD,KAAMC,EAAG,IAChB,GAAID,EAAG,OAAS,UACZ,MAAO,GACnB,GAAIF,GAAgBG,EAAG,GAAG,GAAKH,GAAgBG,EAAG,KAAK,EACnD,MAAO,EACf,CACA,MAAO,GACX,QACI,MAAO,EACf,CACJ,CAEAJ,GAAQ,gBAAkBC,KCnC1B,IAAAI,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAsB,KAE1B,SAASC,GAAgBC,EAAQC,EAAIC,EAAS,CAC1C,GAAID,GAAI,OAAS,kBAAmB,CAChC,IAAME,EAAMF,EAAG,IAAI,CAAC,EAChBE,EAAI,SAAWH,IACdG,EAAI,SAAW,KAAOA,EAAI,SAAW,MACtCL,GAAoB,gBAAgBG,CAAE,GAEtCC,EAAQC,EAAK,aADD,yDACoB,EAAI,CAE5C,CACJ,CAEAN,GAAQ,gBAAkBE,KChB1B,IAAAK,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IAEf,SAASC,GAAYC,EAAKC,EAAOC,EAAQ,CACrC,GAAM,CAAE,WAAAC,CAAW,EAAIH,EAAI,QAC3B,GAAIG,IAAe,GACf,MAAO,GACX,IAAMC,EAAU,OAAOD,GAAe,WAChCA,EACA,CAACE,EAAGC,IAAMD,IAAMC,GAAMR,GAAS,SAASO,CAAC,GAAKP,GAAS,SAASQ,CAAC,GAAKD,EAAE,QAAUC,EAAE,MAC1F,OAAOL,EAAM,KAAKM,GAAQH,EAAQG,EAAK,IAAKL,CAAM,CAAC,CACvD,CAEAL,GAAQ,YAAcE,KCdtB,IAAAS,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAO,IACPC,GAAU,IACVC,GAAe,KACfC,GAAsB,KACtBC,GAAsB,KACtBC,GAAkB,KAEhBC,GAAc,kDACpB,SAASC,GAAgB,CAAE,YAAAC,EAAa,iBAAAC,CAAiB,EAAGC,EAAKC,EAAIC,EAASC,EAAK,CAC/E,IAAMC,EAAYD,GAAK,WAAaZ,GAAQ,QACtCc,EAAM,IAAID,EAAUJ,EAAI,MAAM,EAChCA,EAAI,SACJA,EAAI,OAAS,IACjB,IAAIM,EAASL,EAAG,OACZM,EAAa,KACjB,QAAWC,KAAYP,EAAG,MAAO,CAC7B,GAAM,CAAE,MAAAQ,EAAO,IAAAC,EAAK,IAAAC,EAAK,MAAAC,CAAM,EAAIJ,EAE7BK,EAAWrB,GAAa,aAAaiB,EAAO,CAC9C,UAAW,mBACX,KAAMC,GAAOC,IAAM,CAAC,EACpB,OAAAL,EACA,QAAAJ,EACA,aAAcD,EAAG,OACjB,eAAgB,EACpB,CAAC,EACKa,EAAc,CAACD,EAAS,MAC9B,GAAIC,EAAa,CAOb,GANIJ,IACIA,EAAI,OAAS,YACbR,EAAQI,EAAQ,wBAAyB,yDAAyD,EAC7F,WAAYI,GAAOA,EAAI,SAAWT,EAAG,QAC1CC,EAAQI,EAAQ,aAAcV,EAAW,GAE7C,CAACiB,EAAS,QAAU,CAACA,EAAS,KAAO,CAACF,EAAK,CAC3CJ,EAAaM,EAAS,IAClBA,EAAS,UACLR,EAAI,QACJA,EAAI,SAAW;AAAA,EAAOQ,EAAS,QAE/BR,EAAI,QAAUQ,EAAS,SAE/B,QACJ,EACIA,EAAS,kBAAoBpB,GAAoB,gBAAgBiB,CAAG,IACpER,EAAQQ,GAAOD,EAAMA,EAAM,OAAS,CAAC,EAAG,yBAA0B,2CAA2C,CAErH,MACSI,EAAS,OAAO,SAAWZ,EAAG,QACnCC,EAAQI,EAAQ,aAAcV,EAAW,EAG7CI,EAAI,MAAQ,GACZ,IAAMe,EAAWF,EAAS,IACpBG,EAAUN,EACVZ,EAAYE,EAAKU,EAAKG,EAAUX,CAAO,EACvCH,EAAiBC,EAAKe,EAAUN,EAAO,KAAMI,EAAUX,CAAO,EAChEF,EAAI,OAAO,QACXN,GAAoB,gBAAgBO,EAAG,OAAQS,EAAKR,CAAO,EAC/DF,EAAI,MAAQ,GACRL,GAAgB,YAAYK,EAAKK,EAAI,MAAOW,CAAO,GACnDd,EAAQa,EAAU,gBAAiB,yBAAyB,EAEhE,IAAME,EAAazB,GAAa,aAAamB,GAAO,CAAC,EAAG,CACpD,UAAW,gBACX,KAAMC,EACN,OAAQI,EAAQ,MAAM,CAAC,EACvB,QAAAd,EACA,aAAcD,EAAG,OACjB,eAAgB,CAACS,GAAOA,EAAI,OAAS,cACzC,CAAC,EAED,GADAJ,EAASW,EAAW,IAChBA,EAAW,MAAO,CACdH,IACIF,GAAO,OAAS,aAAe,CAACK,EAAW,YAC3Cf,EAAQI,EAAQ,wBAAyB,qDAAqD,EAC9FN,EAAI,QAAQ,QACZa,EAAS,MAAQI,EAAW,MAAM,OAAS,MAC3Cf,EAAQc,EAAQ,MAAO,sBAAuB,6FAA6F,GAGnJ,IAAME,EAAYN,EACZd,EAAYE,EAAKY,EAAOK,EAAYf,CAAO,EAC3CH,EAAiBC,EAAKM,EAAQK,EAAK,KAAMM,EAAYf,CAAO,EAC9DF,EAAI,OAAO,QACXN,GAAoB,gBAAgBO,EAAG,OAAQW,EAAOV,CAAO,EACjEI,EAASY,EAAU,MAAM,CAAC,EAC1B,IAAMC,EAAO,IAAI7B,GAAK,KAAK0B,EAASE,CAAS,EACzClB,EAAI,QAAQ,mBACZmB,EAAK,SAAWX,GACpBH,EAAI,MAAM,KAAKc,CAAI,CACvB,KACK,CAEGL,GACAZ,EAAQc,EAAQ,MAAO,eAAgB,qDAAqD,EAC5FC,EAAW,UACPD,EAAQ,QACRA,EAAQ,SAAW;AAAA,EAAOC,EAAW,QAErCD,EAAQ,QAAUC,EAAW,SAErC,IAAME,EAAO,IAAI7B,GAAK,KAAK0B,CAAO,EAC9BhB,EAAI,QAAQ,mBACZmB,EAAK,SAAWX,GACpBH,EAAI,MAAM,KAAKc,CAAI,CACvB,CACJ,CACA,OAAIZ,GAAcA,EAAaD,GAC3BJ,EAAQK,EAAY,aAAc,mCAAmC,EACzEF,EAAI,MAAQ,CAACJ,EAAG,OAAQK,EAAQC,GAAcD,CAAM,EAC7CD,CACX,CAEAhB,GAAQ,gBAAkBQ,KCpH1B,IAAAuB,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAU,IACVC,GAAe,KACfC,GAAsB,KAE1B,SAASC,GAAgB,CAAE,YAAAC,EAAa,iBAAAC,CAAiB,EAAGC,EAAKC,EAAIC,EAASC,EAAK,CAC/E,IAAMC,EAAYD,GAAK,WAAaT,GAAQ,QACtCW,EAAM,IAAID,EAAUJ,EAAI,MAAM,EAChCA,EAAI,SACJA,EAAI,OAAS,IACbA,EAAI,QACJA,EAAI,MAAQ,IAChB,IAAIM,EAASL,EAAG,OACZM,EAAa,KACjB,OAAW,CAAE,MAAAC,EAAO,MAAAC,CAAM,IAAKR,EAAG,MAAO,CACrC,IAAMS,EAAQf,GAAa,aAAaa,EAAO,CAC3C,UAAW,eACX,KAAMC,EACN,OAAAH,EACA,QAAAJ,EACA,aAAcD,EAAG,OACjB,eAAgB,EACpB,CAAC,EACD,GAAI,CAACS,EAAM,MACP,GAAIA,EAAM,QAAUA,EAAM,KAAOD,EACzBA,GAASA,EAAM,OAAS,YACxBP,EAAQQ,EAAM,IAAK,aAAc,kDAAkD,EAEnFR,EAAQI,EAAQ,eAAgB,mCAAmC,MAEtE,CACDC,EAAaG,EAAM,IACfA,EAAM,UACNL,EAAI,QAAUK,EAAM,SACxB,QACJ,CAEJ,IAAMC,EAAOF,EACPX,EAAYE,EAAKS,EAAOC,EAAOR,CAAO,EACtCH,EAAiBC,EAAKU,EAAM,IAAKF,EAAO,KAAME,EAAOR,CAAO,EAC9DF,EAAI,OAAO,QACXJ,GAAoB,gBAAgBK,EAAG,OAAQQ,EAAOP,CAAO,EACjEI,EAASK,EAAK,MAAM,CAAC,EACrBN,EAAI,MAAM,KAAKM,CAAI,CACvB,CACA,OAAAN,EAAI,MAAQ,CAACJ,EAAG,OAAQK,EAAQC,GAAcD,CAAM,EAC7CD,CACX,CAEAZ,GAAQ,gBAAkBI,KClD1B,IAAAe,GAAAC,EAAAC,IAAA,cAEA,SAASC,GAAWC,EAAKC,EAAQC,EAAUC,EAAS,CAChD,IAAIC,EAAU,GACd,GAAIJ,EAAK,CACL,IAAIK,EAAW,GACXC,EAAM,GACV,QAAWC,KAASP,EAAK,CACrB,GAAM,CAAE,OAAAQ,EAAQ,KAAAC,CAAK,EAAIF,EACzB,OAAQE,EAAM,CACV,IAAK,QACDJ,EAAW,GACX,MACJ,IAAK,UAAW,CACRH,GAAY,CAACG,GACbF,EAAQI,EAAO,eAAgB,wEAAwE,EAC3G,IAAMG,EAAKF,EAAO,UAAU,CAAC,GAAK,IAC7BJ,EAGDA,GAAWE,EAAMI,EAFjBN,EAAUM,EAGdJ,EAAM,GACN,KACJ,CACA,IAAK,UACGF,IACAE,GAAOE,GACXH,EAAW,GACX,MACJ,QACIF,EAAQI,EAAO,mBAAoB,cAAcE,CAAI,cAAc,CAC3E,CACAR,GAAUO,EAAO,MACrB,CACJ,CACA,MAAO,CAAE,QAAAJ,EAAS,OAAAH,CAAO,CAC7B,CAEAH,GAAQ,WAAaC,KCtCrB,IAAAY,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAO,IACPC,GAAU,IACVC,GAAU,IACVC,GAAa,KACbC,GAAe,KACfC,GAAsB,KACtBC,GAAkB,KAEhBC,GAAW,4DACXC,GAAWC,GAAUA,IAAUA,EAAM,OAAS,aAAeA,EAAM,OAAS,aAClF,SAASC,GAAsB,CAAE,YAAAC,EAAa,iBAAAC,CAAiB,EAAGC,EAAKC,EAAIC,EAASC,EAAK,CACrF,IAAMC,EAAQH,EAAG,MAAM,SAAW,IAC5BI,EAASD,EAAQ,WAAa,gBAC9BE,EAAaH,GAAK,YAAcC,EAAQhB,GAAQ,QAAUC,GAAQ,SAClEkB,EAAO,IAAID,EAAUN,EAAI,MAAM,EACrCO,EAAK,KAAO,GACZ,IAAMC,EAASR,EAAI,OACfQ,IACAR,EAAI,OAAS,IACbA,EAAI,QACJA,EAAI,MAAQ,IAChB,IAAIS,EAASR,EAAG,OAASA,EAAG,MAAM,OAAO,OACzC,QAASS,EAAI,EAAGA,EAAIT,EAAG,MAAM,OAAQ,EAAES,EAAG,CACtC,IAAMC,EAAWV,EAAG,MAAMS,CAAC,EACrB,CAAE,MAAAE,EAAO,IAAAC,EAAK,IAAAC,EAAK,MAAAC,CAAM,EAAIJ,EAC7BK,EAAQzB,GAAa,aAAaqB,EAAO,CAC3C,KAAMP,EACN,UAAW,mBACX,KAAMQ,GAAOC,IAAM,CAAC,EACpB,OAAAL,EACA,QAAAP,EACA,aAAcD,EAAG,OACjB,eAAgB,EACpB,CAAC,EACD,GAAI,CAACe,EAAM,MAAO,CACd,GAAI,CAACA,EAAM,QAAU,CAACA,EAAM,KAAO,CAACF,GAAO,CAACC,EAAO,CAC3CL,IAAM,GAAKM,EAAM,MACjBd,EAAQc,EAAM,MAAO,mBAAoB,mBAAmBX,CAAM,EAAE,EAC/DK,EAAIT,EAAG,MAAM,OAAS,GAC3BC,EAAQc,EAAM,MAAO,mBAAoB,4BAA4BX,CAAM,EAAE,EAC7EW,EAAM,UACFT,EAAK,QACLA,EAAK,SAAW;AAAA,EAAOS,EAAM,QAE7BT,EAAK,QAAUS,EAAM,SAE7BP,EAASO,EAAM,IACf,QACJ,CACI,CAACZ,GAASJ,EAAI,QAAQ,QAAUR,GAAoB,gBAAgBqB,CAAG,GACvEX,EAAQW,EACR,yBAA0B,kEAAkE,CACpG,CACA,GAAIH,IAAM,EACFM,EAAM,OACNd,EAAQc,EAAM,MAAO,mBAAoB,mBAAmBX,CAAM,EAAE,UAGnEW,EAAM,OACPd,EAAQc,EAAM,MAAO,eAAgB,qBAAqBX,CAAM,QAAQ,EACxEW,EAAM,QAAS,CACf,IAAIC,EAAkB,GACtBC,EAAM,QAAWC,KAAMP,EACnB,OAAQO,EAAG,KAAM,CACb,IAAK,QACL,IAAK,QACD,MACJ,IAAK,UACDF,EAAkBE,EAAG,OAAO,UAAU,CAAC,EACvC,MAAMD,EACV,QACI,MAAMA,CACd,CAEJ,GAAID,EAAiB,CACjB,IAAIG,EAAOb,EAAK,MAAMA,EAAK,MAAM,OAAS,CAAC,EACvCrB,GAAS,OAAOkC,CAAI,IACpBA,EAAOA,EAAK,OAASA,EAAK,KAC1BA,EAAK,QACLA,EAAK,SAAW;AAAA,EAAOH,EAEvBG,EAAK,QAAUH,EACnBD,EAAM,QAAUA,EAAM,QAAQ,UAAUC,EAAgB,OAAS,CAAC,CACtE,CACJ,CAEJ,GAAI,CAACb,GAAS,CAACU,GAAO,CAACE,EAAM,MAAO,CAGhC,IAAMK,EAAYN,EACZjB,EAAYE,EAAKe,EAAOC,EAAOd,CAAO,EACtCH,EAAiBC,EAAKgB,EAAM,IAAKF,EAAK,KAAME,EAAOd,CAAO,EAChEK,EAAK,MAAM,KAAKc,CAAS,EACzBZ,EAASY,EAAU,MAAM,CAAC,EACtB1B,GAAQoB,CAAK,GACbb,EAAQmB,EAAU,MAAO,gBAAiB3B,EAAQ,CAC1D,KACK,CAGDM,EAAI,MAAQ,GACZ,IAAMsB,EAAWN,EAAM,IACjBO,EAAUV,EACVf,EAAYE,EAAKa,EAAKG,EAAOd,CAAO,EACpCH,EAAiBC,EAAKsB,EAAUV,EAAO,KAAMI,EAAOd,CAAO,EAC7DP,GAAQkB,CAAG,GACXX,EAAQqB,EAAQ,MAAO,gBAAiB7B,EAAQ,EACpDM,EAAI,MAAQ,GAEZ,IAAMwB,EAAajC,GAAa,aAAauB,GAAO,CAAC,EAAG,CACpD,KAAMT,EACN,UAAW,gBACX,KAAMU,EACN,OAAQQ,EAAQ,MAAM,CAAC,EACvB,QAAArB,EACA,aAAcD,EAAG,OACjB,eAAgB,EACpB,CAAC,EACD,GAAIuB,EAAW,OACX,GAAI,CAACpB,GAAS,CAACY,EAAM,OAAShB,EAAI,QAAQ,OAAQ,CAC9C,GAAIc,EACA,QAAWK,KAAML,EAAK,CAClB,GAAIK,IAAOK,EAAW,MAClB,MACJ,GAAIL,EAAG,OAAS,UAAW,CACvBjB,EAAQiB,EAAI,yBAA0B,kEAAkE,EACxG,KACJ,CACJ,CACAH,EAAM,MAAQQ,EAAW,MAAM,OAAS,MACxCtB,EAAQsB,EAAW,MAAO,sBAAuB,6FAA6F,CACtJ,OAEKT,IACD,WAAYA,GAASA,EAAM,QAAUA,EAAM,OAAO,CAAC,IAAM,IACzDb,EAAQa,EAAO,eAAgB,4BAA4BV,CAAM,EAAE,EAEnEH,EAAQsB,EAAW,MAAO,eAAgB,0BAA0BnB,CAAM,QAAQ,GAG1F,IAAMgB,EAAYN,EACZjB,EAAYE,EAAKe,EAAOS,EAAYtB,CAAO,EAC3CsB,EAAW,MACPzB,EAAiBC,EAAKwB,EAAW,IAAKV,EAAK,KAAMU,EAAYtB,CAAO,EACpE,KACNmB,EACI1B,GAAQoB,CAAK,GACbb,EAAQmB,EAAU,MAAO,gBAAiB3B,EAAQ,EAEjD8B,EAAW,UACZD,EAAQ,QACRA,EAAQ,SAAW;AAAA,EAAOC,EAAW,QAErCD,EAAQ,QAAUC,EAAW,SAErC,IAAMC,GAAO,IAAItC,GAAK,KAAKoC,EAASF,CAAS,EAG7C,GAFIrB,EAAI,QAAQ,mBACZyB,GAAK,SAAWd,GAChBP,EAAO,CACP,IAAMsB,EAAMnB,EACRd,GAAgB,YAAYO,EAAK0B,EAAI,MAAOH,CAAO,GACnDrB,EAAQoB,EAAU,gBAAiB,yBAAyB,EAChEI,EAAI,MAAM,KAAKD,EAAI,CACvB,KACK,CACD,IAAMC,EAAM,IAAItC,GAAQ,QAAQY,EAAI,MAAM,EAC1C0B,EAAI,KAAO,GACXA,EAAI,MAAM,KAAKD,EAAI,EACnB,IAAME,IAAYN,GAAaE,GAAS,MACxCG,EAAI,MAAQ,CAACH,EAAQ,MAAM,CAAC,EAAGI,GAAS,CAAC,EAAGA,GAAS,CAAC,CAAC,EACvDpB,EAAK,MAAM,KAAKmB,CAAG,CACvB,CACAjB,EAASY,EAAYA,EAAU,MAAM,CAAC,EAAIG,EAAW,GACzD,CACJ,CACA,IAAMI,EAAcxB,EAAQ,IAAM,IAC5B,CAACyB,EAAI,GAAGC,CAAE,EAAI7B,EAAG,IACnB8B,EAAQtB,EACZ,GAAIoB,GAAMA,EAAG,SAAWD,EACpBG,EAAQF,EAAG,OAASA,EAAG,OAAO,WAC7B,CACD,IAAMG,EAAO3B,EAAO,CAAC,EAAE,YAAY,EAAIA,EAAO,UAAU,CAAC,EACnD4B,EAAMzB,EACN,GAAGwB,CAAI,oBAAoBJ,CAAW,GACtC,GAAGI,CAAI,qEAAqEJ,CAAW,GAC7F1B,EAAQO,EAAQD,EAAS,eAAiB,aAAcyB,CAAG,EACvDJ,GAAMA,EAAG,OAAO,SAAW,GAC3BC,EAAG,QAAQD,CAAE,CACrB,CACA,GAAIC,EAAG,OAAS,EAAG,CACf,IAAMI,EAAM5C,GAAW,WAAWwC,EAAIC,EAAO/B,EAAI,QAAQ,OAAQE,CAAO,EACpEgC,EAAI,UACA3B,EAAK,QACLA,EAAK,SAAW;AAAA,EAAO2B,EAAI,QAE3B3B,EAAK,QAAU2B,EAAI,SAE3B3B,EAAK,MAAQ,CAACN,EAAG,OAAQ8B,EAAOG,EAAI,MAAM,CAC9C,MAEI3B,EAAK,MAAQ,CAACN,EAAG,OAAQ8B,EAAOA,CAAK,EAEzC,OAAOxB,CACX,CAEAtB,GAAQ,sBAAwBY,KChNhC,IAAAsC,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAS,IACTC,GAAU,IACVC,GAAU,IACVC,GAAkB,KAClBC,GAAkB,KAClBC,GAAwB,KAE5B,SAASC,GAAkBC,EAAIC,EAAKC,EAAOC,EAASC,EAASC,EAAK,CAC9D,IAAMC,EAAOJ,EAAM,OAAS,YACtBN,GAAgB,gBAAgBI,EAAIC,EAAKC,EAAOC,EAASE,CAAG,EAC5DH,EAAM,OAAS,YACXL,GAAgB,gBAAgBG,EAAIC,EAAKC,EAAOC,EAASE,CAAG,EAC5DP,GAAsB,sBAAsBE,EAAIC,EAAKC,EAAOC,EAASE,CAAG,EAC5EE,EAAOD,EAAK,YAGlB,OAAIF,IAAY,KAAOA,IAAYG,EAAK,SACpCD,EAAK,IAAMC,EAAK,QACTD,IAEPF,IACAE,EAAK,IAAMF,GACRE,EACX,CACA,SAASE,GAAkBR,EAAIC,EAAKC,EAAOO,EAAON,EAAS,CACvD,IAAMO,EAAWD,EAAM,IACjBL,EAAWM,EAEXT,EAAI,WAAW,QAAQS,EAAS,OAAQC,GAAOR,EAAQO,EAAU,qBAAsBC,CAAG,CAAC,EAD3F,KAEN,GAAIT,EAAM,OAAS,YAAa,CAC5B,GAAM,CAAE,OAAAU,EAAQ,iBAAkBC,CAAG,EAAIJ,EACnCK,EAAWF,GAAUF,EACrBE,EAAO,OAASF,EAAS,OACrBE,EACAF,EACHE,GAAUF,EACbI,IAAa,CAACD,GAAMA,EAAG,OAASC,EAAS,SAEzCX,EAAQW,EAAU,eADF,4CACyB,CAEjD,CACA,IAAMC,EAAUb,EAAM,OAAS,YACzB,MACAA,EAAM,OAAS,YACX,MACAA,EAAM,MAAM,SAAW,IACnB,MACA,MAGd,GAAI,CAACQ,GACD,CAACN,GACDA,IAAY,KACXA,IAAYV,GAAQ,QAAQ,SAAWqB,IAAY,OACnDX,IAAYT,GAAQ,QAAQ,SAAWoB,IAAY,MACpD,OAAOhB,GAAkBC,EAAIC,EAAKC,EAAOC,EAASC,CAAO,EAE7D,IAAIC,EAAMJ,EAAI,OAAO,KAAK,KAAKe,GAAKA,EAAE,MAAQZ,GAAWY,EAAE,aAAeD,CAAO,EACjF,GAAI,CAACV,EAAK,CACN,IAAMY,EAAKhB,EAAI,OAAO,UAAUG,CAAO,EACvC,GAAIa,GAAMA,EAAG,aAAeF,EACxBd,EAAI,OAAO,KAAK,KAAK,OAAO,OAAO,CAAC,EAAGgB,EAAI,CAAE,QAAS,EAAM,CAAC,CAAC,EAC9DZ,EAAMY,MAGN,QAAIA,GAAI,WACJd,EAAQO,EAAU,sBAAuB,GAAGO,EAAG,GAAG,aAAaF,CAAO,4BAA4BE,EAAG,UAAU,GAAI,EAAI,EAGvHd,EAAQO,EAAU,qBAAsB,mBAAmBN,CAAO,GAAI,EAAI,EAEvEL,GAAkBC,EAAIC,EAAKC,EAAOC,EAASC,CAAO,CAEjE,CACA,IAAME,EAAOP,GAAkBC,EAAIC,EAAKC,EAAOC,EAASC,EAASC,CAAG,EAC9Da,EAAMb,EAAI,UAAUC,EAAMK,GAAOR,EAAQO,EAAU,qBAAsBC,CAAG,EAAGV,EAAI,OAAO,GAAKK,EAC/Fa,EAAO3B,GAAS,OAAO0B,CAAG,EAC1BA,EACA,IAAIzB,GAAO,OAAOyB,CAAG,EAC3B,OAAAC,EAAK,MAAQb,EAAK,MAClBa,EAAK,IAAMf,EACPC,GAAK,SACLc,EAAK,OAASd,EAAI,QACfc,CACX,CAEA5B,GAAQ,kBAAoBiB,KCzF5B,IAAAY,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAS,IAEb,SAASC,GAAmBC,EAAKC,EAAQC,EAAS,CAC9C,IAAMC,EAAQF,EAAO,OACfG,EAASC,GAAuBJ,EAAQD,EAAI,QAAQ,OAAQE,CAAO,EACzE,GAAI,CAACE,EACD,MAAO,CAAE,MAAO,GAAI,KAAM,KAAM,QAAS,GAAI,MAAO,CAACD,EAAOA,EAAOA,CAAK,CAAE,EAC9E,IAAMG,EAAOF,EAAO,OAAS,IAAMN,GAAO,OAAO,aAAeA,GAAO,OAAO,cACxES,EAAQN,EAAO,OAASO,GAAWP,EAAO,MAAM,EAAI,CAAC,EAEvDQ,EAAaF,EAAM,OACvB,QAASG,EAAIH,EAAM,OAAS,EAAGG,GAAK,EAAG,EAAEA,EAAG,CACxC,IAAMC,EAAUJ,EAAMG,CAAC,EAAE,CAAC,EAC1B,GAAIC,IAAY,IAAMA,IAAY,KAC9BF,EAAaC,MAEb,MACR,CAEA,GAAID,IAAe,EAAG,CAClB,IAAMG,EAAQR,EAAO,QAAU,KAAOG,EAAM,OAAS,EAC/C;AAAA,EAAK,OAAO,KAAK,IAAI,EAAGA,EAAM,OAAS,CAAC,CAAC,EACzC,GACFM,EAAMV,EAAQC,EAAO,OACzB,OAAIH,EAAO,SACPY,GAAOZ,EAAO,OAAO,QAClB,CAAE,MAAAW,EAAO,KAAAN,EAAM,QAASF,EAAO,QAAS,MAAO,CAACD,EAAOU,EAAKA,CAAG,CAAE,CAC5E,CAEA,IAAIC,EAAab,EAAO,OAASG,EAAO,OACpCW,EAASd,EAAO,OAASG,EAAO,OAChCY,EAAe,EACnB,QAASN,EAAI,EAAGA,EAAID,EAAY,EAAEC,EAAG,CACjC,GAAM,CAACO,EAAQN,CAAO,EAAIJ,EAAMG,CAAC,EACjC,GAAIC,IAAY,IAAMA,IAAY,KAC1BP,EAAO,SAAW,GAAKa,EAAO,OAASH,IACvCA,EAAaG,EAAO,YAEvB,CACGA,EAAO,OAASH,GAEhBZ,EAAQa,EAASE,EAAO,OAAQ,eADhB,iGACuC,EAEvDb,EAAO,SAAW,IAClBU,EAAaG,EAAO,QACxBD,EAAeN,EACXI,IAAe,GAAK,CAACd,EAAI,QAEzBE,EAAQa,EAAQ,aADA,qDACqB,EAEzC,KACJ,CACAA,GAAUE,EAAO,OAASN,EAAQ,OAAS,CAC/C,CAEA,QAASD,EAAIH,EAAM,OAAS,EAAGG,GAAKD,EAAY,EAAEC,EAC1CH,EAAMG,CAAC,EAAE,CAAC,EAAE,OAASI,IACrBL,EAAaC,EAAI,GAEzB,IAAIE,EAAQ,GACRM,EAAM,GACNC,EAAmB,GAEvB,QAAST,EAAI,EAAGA,EAAIM,EAAc,EAAEN,EAChCE,GAASL,EAAMG,CAAC,EAAE,CAAC,EAAE,MAAMI,CAAU,EAAI;AAAA,EAC7C,QAASJ,EAAIM,EAAcN,EAAID,EAAY,EAAEC,EAAG,CAC5C,GAAI,CAACO,EAAQN,CAAO,EAAIJ,EAAMG,CAAC,EAC/BK,GAAUE,EAAO,OAASN,EAAQ,OAAS,EAC3C,IAAMS,EAAOT,EAAQA,EAAQ,OAAS,CAAC,IAAM,KAI7C,GAHIS,IACAT,EAAUA,EAAQ,MAAM,EAAG,EAAE,GAE7BA,GAAWM,EAAO,OAASH,EAAY,CAIvC,IAAMO,EAAU,2DAHJjB,EAAO,OACb,iCACA,YACwE,GAC9EF,EAAQa,EAASJ,EAAQ,QAAUS,EAAO,EAAI,GAAI,aAAcC,CAAO,EACvEJ,EAAS,EACb,CACIX,IAASR,GAAO,OAAO,eACvBc,GAASM,EAAMD,EAAO,MAAMH,CAAU,EAAIH,EAC1CO,EAAM;AAAA,GAEDD,EAAO,OAASH,GAAcH,EAAQ,CAAC,IAAM,KAE9CO,IAAQ,IACRA,EAAM;AAAA,EACD,CAACC,GAAoBD,IAAQ;AAAA,IAClCA,EAAM;AAAA;AAAA,GACVN,GAASM,EAAMD,EAAO,MAAMH,CAAU,EAAIH,EAC1CO,EAAM;AAAA,EACNC,EAAmB,IAEdR,IAAY,GAEbO,IAAQ;AAAA,EACRN,GAAS;AAAA,EAETM,EAAM;AAAA,GAGVN,GAASM,EAAMP,EACfO,EAAM,IACNC,EAAmB,GAE3B,CACA,OAAQf,EAAO,MAAO,CAClB,IAAK,IACD,MACJ,IAAK,IACD,QAASM,EAAID,EAAYC,EAAIH,EAAM,OAAQ,EAAEG,EACzCE,GAAS;AAAA,EAAOL,EAAMG,CAAC,EAAE,CAAC,EAAE,MAAMI,CAAU,EAC5CF,EAAMA,EAAM,OAAS,CAAC,IAAM;AAAA,IAC5BA,GAAS;AAAA,GACb,MACJ,QACIA,GAAS;AAAA,CACjB,CACA,IAAMC,EAAMV,EAAQC,EAAO,OAASH,EAAO,OAAO,OAClD,MAAO,CAAE,MAAAW,EAAO,KAAAN,EAAM,QAASF,EAAO,QAAS,MAAO,CAACD,EAAOU,EAAKA,CAAG,CAAE,CAC5E,CACA,SAASR,GAAuB,CAAE,OAAAU,EAAQ,MAAAO,CAAM,EAAGC,EAAQrB,EAAS,CAEhE,GAAIoB,EAAM,CAAC,EAAE,OAAS,sBAClB,OAAApB,EAAQoB,EAAM,CAAC,EAAG,aAAc,+BAA+B,EACxD,KAEX,GAAM,CAAE,OAAAE,CAAO,EAAIF,EAAM,CAAC,EACpBG,EAAOD,EAAO,CAAC,EACjBP,EAAS,EACTS,EAAQ,GACRC,EAAQ,GACZ,QAASjB,EAAI,EAAGA,EAAIc,EAAO,OAAQ,EAAEd,EAAG,CACpC,IAAMkB,EAAKJ,EAAOd,CAAC,EACnB,GAAI,CAACgB,IAAUE,IAAO,KAAOA,IAAO,KAChCF,EAAQE,MACP,CACD,IAAMC,EAAI,OAAOD,CAAE,EACf,CAACX,GAAUY,EACXZ,EAASY,EACJF,IAAU,KACfA,EAAQZ,EAASL,EACzB,CACJ,CACIiB,IAAU,IACVzB,EAAQyB,EAAO,mBAAoB,kDAAkDH,CAAM,EAAE,EACjG,IAAIM,EAAW,GACXC,EAAU,GACVC,EAASR,EAAO,OACpB,QAASd,EAAI,EAAGA,EAAIY,EAAM,OAAQ,EAAEZ,EAAG,CACnC,IAAMuB,EAAQX,EAAMZ,CAAC,EACrB,OAAQuB,EAAM,KAAM,CAChB,IAAK,QACDH,EAAW,GAEf,IAAK,UACDE,GAAUC,EAAM,OAAO,OACvB,MACJ,IAAK,UACGV,GAAU,CAACO,GAEX5B,EAAQ+B,EAAO,eADC,wEACsB,EAE1CD,GAAUC,EAAM,OAAO,OACvBF,EAAUE,EAAM,OAAO,UAAU,CAAC,EAClC,MACJ,IAAK,QACD/B,EAAQ+B,EAAO,mBAAoBA,EAAM,OAAO,EAChDD,GAAUC,EAAM,OAAO,OACvB,MAEJ,QAAS,CACL,IAAMZ,EAAU,4CAA4CY,EAAM,IAAI,GACtE/B,EAAQ+B,EAAO,mBAAoBZ,CAAO,EAC1C,IAAMa,EAAKD,EAAM,OACbC,GAAM,OAAOA,GAAO,WACpBF,GAAUE,EAAG,OACrB,CACJ,CACJ,CACA,MAAO,CAAE,KAAAT,EAAM,OAAAR,EAAQ,MAAAS,EAAO,QAAAK,EAAS,OAAAC,CAAO,CAClD,CAEA,SAASxB,GAAWgB,EAAQ,CACxB,IAAMW,EAAQX,EAAO,MAAM,QAAQ,EAC7BY,EAAQD,EAAM,CAAC,EACfE,EAAID,EAAM,MAAM,OAAO,EAIvB7B,EAAQ,CAHA8B,IAAI,CAAC,EACb,CAACA,EAAE,CAAC,EAAGD,EAAM,MAAMC,EAAE,CAAC,EAAE,MAAM,CAAC,EAC/B,CAAC,GAAID,CAAK,CACI,EACpB,QAAS1B,EAAI,EAAGA,EAAIyB,EAAM,OAAQzB,GAAK,EACnCH,EAAM,KAAK,CAAC4B,EAAMzB,CAAC,EAAGyB,EAAMzB,EAAI,CAAC,CAAC,CAAC,EACvC,OAAOH,CACX,CAEAV,GAAQ,mBAAqBE,KCvM7B,IAAAuC,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAS,IACTC,GAAa,KAEjB,SAASC,GAAkBC,EAAQC,EAAQC,EAAS,CAChD,GAAM,CAAE,OAAAC,EAAQ,KAAAC,EAAM,OAAAC,EAAQ,IAAAC,CAAI,EAAIN,EAClCO,EACAC,EACEC,EAAW,CAACC,EAAKC,EAAMC,IAAQV,EAAQC,EAASO,EAAKC,EAAMC,CAAG,EACpE,OAAQR,EAAM,CACV,IAAK,SACDG,EAAQV,GAAO,OAAO,MACtBW,EAAQK,GAAWR,EAAQI,CAAQ,EACnC,MACJ,IAAK,uBACDF,EAAQV,GAAO,OAAO,aACtBW,EAAQM,GAAkBT,EAAQI,CAAQ,EAC1C,MACJ,IAAK,uBACDF,EAAQV,GAAO,OAAO,aACtBW,EAAQO,GAAkBV,EAAQI,CAAQ,EAC1C,MAEJ,QACI,OAAAP,EAAQF,EAAQ,mBAAoB,4CAA4CI,CAAI,EAAE,EAC/E,CACH,MAAO,GACP,KAAM,KACN,QAAS,GACT,MAAO,CAACD,EAAQA,EAASE,EAAO,OAAQF,EAASE,EAAO,MAAM,CAClE,CACR,CACA,IAAMW,EAAWb,EAASE,EAAO,OAC3BY,EAAKnB,GAAW,WAAWQ,EAAKU,EAAUf,EAAQC,CAAO,EAC/D,MAAO,CACH,MAAAM,EACA,KAAMD,EACN,QAASU,EAAG,QACZ,MAAO,CAACd,EAAQa,EAAUC,EAAG,MAAM,CACvC,CACJ,CACA,SAASJ,GAAWR,EAAQH,EAAS,CACjC,IAAIgB,EAAU,GACd,OAAQb,EAAO,CAAC,EAAG,CAEf,IAAK,IACDa,EAAU,kBACV,MACJ,IAAK,IACDA,EAAU,6BACV,MACJ,IAAK,IACDA,EAAU,kCACV,MACJ,IAAK,IACL,IAAK,IAAK,CACNA,EAAU,0BAA0Bb,EAAO,CAAC,CAAC,GAC7C,KACJ,CACA,IAAK,IACL,IAAK,IAAK,CACNa,EAAU,sBAAsBb,EAAO,CAAC,CAAC,GACzC,KACJ,CACJ,CACA,OAAIa,GACAhB,EAAQ,EAAG,mBAAoB,iCAAiCgB,CAAO,EAAE,EACtEC,GAAUd,CAAM,CAC3B,CACA,SAASS,GAAkBT,EAAQH,EAAS,CACxC,OAAIG,EAAOA,EAAO,OAAS,CAAC,IAAM,KAAOA,EAAO,SAAW,IACvDH,EAAQG,EAAO,OAAQ,eAAgB,wBAAwB,EAC5Dc,GAAUd,EAAO,MAAM,EAAG,EAAE,CAAC,EAAE,QAAQ,MAAO,GAAG,CAC5D,CACA,SAASc,GAAUd,EAAQ,CAQvB,IAAIe,EAAOC,EACX,GAAI,CACAD,EAAQ,IAAI,OAAO;AAAA,EAA8B,IAAI,EACrDC,EAAO,IAAI,OAAO;AAAA,EAAyC,IAAI,CACnE,MACM,CACFD,EAAQ,qBACRC,EAAO,0BACX,CACA,IAAIC,EAAQF,EAAM,KAAKf,CAAM,EAC7B,GAAI,CAACiB,EACD,OAAOjB,EACX,IAAIkB,EAAMD,EAAM,CAAC,EACbE,EAAM,IACNC,EAAML,EAAM,UAEhB,IADAC,EAAK,UAAYI,EACTH,EAAQD,EAAK,KAAKhB,CAAM,GACxBiB,EAAM,CAAC,IAAM,GACTE,IAAQ;AAAA,EACRD,GAAOC,EAEPA,EAAM;AAAA,GAGVD,GAAOC,EAAMF,EAAM,CAAC,EACpBE,EAAM,KAEVC,EAAMJ,EAAK,UAEf,IAAMK,EAAO,eACb,OAAAA,EAAK,UAAYD,EACjBH,EAAQI,EAAK,KAAKrB,CAAM,EACjBkB,EAAMC,GAAOF,IAAQ,CAAC,GAAK,GACtC,CACA,SAASP,GAAkBV,EAAQH,EAAS,CACxC,IAAIqB,EAAM,GACV,QAASI,EAAI,EAAGA,EAAItB,EAAO,OAAS,EAAG,EAAEsB,EAAG,CACxC,IAAMC,EAAKvB,EAAOsB,CAAC,EACnB,GAAI,EAAAC,IAAO,MAAQvB,EAAOsB,EAAI,CAAC,IAAM;AAAA,GAErC,GAAIC,IAAO;AAAA,EAAM,CACb,GAAM,CAAE,KAAAC,EAAM,OAAA1B,CAAO,EAAI2B,GAAYzB,EAAQsB,CAAC,EAC9CJ,GAAOM,EACPF,EAAIxB,CACR,SACSyB,IAAO,KAAM,CAClB,IAAIG,EAAO1B,EAAO,EAAEsB,CAAC,EACfK,EAAKC,GAAYF,CAAI,EAC3B,GAAIC,EACAT,GAAOS,UACFD,IAAS;AAAA,EAGd,IADAA,EAAO1B,EAAOsB,EAAI,CAAC,EACZI,IAAS,KAAOA,IAAS,KAC5BA,EAAO1B,EAAO,EAAEsB,EAAI,CAAC,UAEpBI,IAAS,MAAQ1B,EAAOsB,EAAI,CAAC,IAAM;AAAA,EAGxC,IADAI,EAAO1B,EAAO,EAAEsB,EAAI,CAAC,EACdI,IAAS,KAAOA,IAAS,KAC5BA,EAAO1B,EAAO,EAAEsB,EAAI,CAAC,UAEpBI,IAAS,KAAOA,IAAS,KAAOA,IAAS,IAAK,CACnD,IAAMG,EAAS,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,CAAE,EAAEH,CAAI,EACxCR,GAAOY,GAAc9B,EAAQsB,EAAI,EAAGO,EAAQhC,CAAO,EACnDyB,GAAKO,CACT,KACK,CACD,IAAME,EAAM/B,EAAO,OAAOsB,EAAI,EAAG,CAAC,EAClCzB,EAAQyB,EAAI,EAAG,gBAAiB,2BAA2BS,CAAG,EAAE,EAChEb,GAAOa,CACX,CACJ,SACSR,IAAO,KAAOA,IAAO,IAAM,CAEhC,IAAMS,EAAUV,EACZI,EAAO1B,EAAOsB,EAAI,CAAC,EACvB,KAAOI,IAAS,KAAOA,IAAS,KAC5BA,EAAO1B,EAAO,EAAEsB,EAAI,CAAC,EACrBI,IAAS;AAAA,GAAQ,EAAEA,IAAS,MAAQ1B,EAAOsB,EAAI,CAAC,IAAM;AAAA,KACtDJ,GAAOI,EAAIU,EAAUhC,EAAO,MAAMgC,EAASV,EAAI,CAAC,EAAIC,EAC5D,MAEIL,GAAOK,CAEf,CACA,OAAIvB,EAAOA,EAAO,OAAS,CAAC,IAAM,KAAOA,EAAO,SAAW,IACvDH,EAAQG,EAAO,OAAQ,eAAgB,wBAAwB,EAC5DkB,CACX,CAKA,SAASO,GAAYzB,EAAQF,EAAQ,CACjC,IAAI0B,EAAO,GACPD,EAAKvB,EAAOF,EAAS,CAAC,EAC1B,MAAOyB,IAAO,KAAOA,IAAO,KAAQA,IAAO;AAAA,GAAQA,IAAO,OAClD,EAAAA,IAAO,MAAQvB,EAAOF,EAAS,CAAC,IAAM;AAAA,IAEtCyB,IAAO;AAAA,IACPC,GAAQ;AAAA,GACZ1B,GAAU,EACVyB,EAAKvB,EAAOF,EAAS,CAAC,EAE1B,OAAK0B,IACDA,EAAO,KACJ,CAAE,KAAAA,EAAM,OAAA1B,CAAO,CAC1B,CACA,IAAM8B,GAAc,CAChB,EAAK,KACL,EAAG,OACH,EAAG,KACH,EAAG,OACH,EAAG,KACH,EAAG;AAAA,EACH,EAAG,KACH,EAAG,IACH,EAAG,KACH,EAAG,OACH,EAAG,OACH,EAAG,SACH,EAAG,SACH,IAAK,IACL,IAAK,IACL,IAAK,IACL,KAAM,KACN,IAAM,GACV,EACA,SAASE,GAAc9B,EAAQF,EAAQ+B,EAAQhC,EAAS,CACpD,IAAM8B,EAAK3B,EAAO,OAAOF,EAAQ+B,CAAM,EAEjCvB,EADKqB,EAAG,SAAWE,GAAU,iBAAiB,KAAKF,CAAE,EACzC,SAASA,EAAI,EAAE,EAAI,IACrC,GAAI,MAAMrB,CAAI,EAAG,CACb,IAAMyB,EAAM/B,EAAO,OAAOF,EAAS,EAAG+B,EAAS,CAAC,EAChD,OAAAhC,EAAQC,EAAS,EAAG,gBAAiB,2BAA2BiC,CAAG,EAAE,EAC9DA,CACX,CACA,OAAO,OAAO,cAAczB,CAAI,CACpC,CAEAf,GAAQ,kBAAoBG,KChO5B,IAAAuC,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,IACXC,GAAS,IACTC,GAAqB,KACrBC,GAAoB,KAExB,SAASC,GAAcC,EAAKC,EAAOC,EAAUC,EAAS,CAClD,GAAM,CAAE,MAAAC,EAAO,KAAAC,EAAM,QAAAC,EAAS,MAAAC,CAAM,EAAIN,EAAM,OAAS,eACjDJ,GAAmB,mBAAmBG,EAAKC,EAAOE,CAAO,EACzDL,GAAkB,kBAAkBG,EAAOD,EAAI,QAAQ,OAAQG,CAAO,EACtEK,EAAUN,EACVF,EAAI,WAAW,QAAQE,EAAS,OAAQO,GAAON,EAAQD,EAAU,qBAAsBO,CAAG,CAAC,EAC3F,KACFC,EACAV,EAAI,QAAQ,YAAcA,EAAI,MAC9BU,EAAMV,EAAI,OAAOL,GAAS,MAAM,EAE3Ba,EACLE,EAAMC,GAAoBX,EAAI,OAAQI,EAAOI,EAASN,EAAUC,CAAO,EAClEF,EAAM,OAAS,SACpBS,EAAME,GAAoBZ,EAAKI,EAAOH,EAAOE,CAAO,EAEpDO,EAAMV,EAAI,OAAOL,GAAS,MAAM,EACpC,IAAIkB,EACJ,GAAI,CACA,IAAMC,EAAMJ,EAAI,QAAQN,EAAOK,GAAON,EAAQD,GAAYD,EAAO,qBAAsBQ,CAAG,EAAGT,EAAI,OAAO,EACxGa,EAASlB,GAAS,SAASmB,CAAG,EAAIA,EAAM,IAAIlB,GAAO,OAAOkB,CAAG,CACjE,OACOC,EAAO,CACV,IAAMN,EAAMM,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,EACjEZ,EAAQD,GAAYD,EAAO,qBAAsBQ,CAAG,EACpDI,EAAS,IAAIjB,GAAO,OAAOQ,CAAK,CACpC,CACA,OAAAS,EAAO,MAAQN,EACfM,EAAO,OAAST,EACZC,IACAQ,EAAO,KAAOR,GACdG,IACAK,EAAO,IAAML,GACbE,EAAI,SACJG,EAAO,OAASH,EAAI,QACpBJ,IACAO,EAAO,QAAUP,GACdO,CACX,CACA,SAASF,GAAoBK,EAAQZ,EAAOI,EAASN,EAAUC,EAAS,CACpE,GAAIK,IAAY,IACZ,OAAOQ,EAAOrB,GAAS,MAAM,EACjC,IAAMsB,EAAgB,CAAC,EACvB,QAAWP,KAAOM,EAAO,KACrB,GAAI,CAACN,EAAI,YAAcA,EAAI,MAAQF,EAC/B,GAAIE,EAAI,SAAWA,EAAI,KACnBO,EAAc,KAAKP,CAAG,MAEtB,QAAOA,EAGnB,QAAWA,KAAOO,EACd,GAAIP,EAAI,MAAM,KAAKN,CAAK,EACpB,OAAOM,EACf,IAAMQ,EAAKF,EAAO,UAAUR,CAAO,EACnC,OAAIU,GAAM,CAACA,EAAG,YAGVF,EAAO,KAAK,KAAK,OAAO,OAAO,CAAC,EAAGE,EAAI,CAAE,QAAS,GAAO,KAAM,MAAU,CAAC,CAAC,EACpEA,IAEXf,EAAQD,EAAU,qBAAsB,mBAAmBM,CAAO,GAAIA,IAAY,uBAAuB,EAClGQ,EAAOrB,GAAS,MAAM,EACjC,CACA,SAASiB,GAAoB,CAAE,MAAAO,EAAO,WAAAC,EAAY,OAAAJ,CAAO,EAAGZ,EAAOH,EAAOE,EAAS,CAC/E,IAAMO,EAAMM,EAAO,KAAK,KAAKN,IAAQA,EAAI,UAAY,IAASS,GAAST,EAAI,UAAY,QACnFA,EAAI,MAAM,KAAKN,CAAK,CAAC,GAAKY,EAAOrB,GAAS,MAAM,EACpD,GAAIqB,EAAO,OAAQ,CACf,IAAMK,EAASL,EAAO,OAAO,KAAKN,GAAOA,EAAI,SAAWA,EAAI,MAAM,KAAKN,CAAK,CAAC,GACzEY,EAAOrB,GAAS,MAAM,EAC1B,GAAIe,EAAI,MAAQW,EAAO,IAAK,CACxB,IAAMC,EAAKF,EAAW,UAAUV,EAAI,GAAG,EACjCa,EAAKH,EAAW,UAAUC,EAAO,GAAG,EACpCZ,EAAM,iCAAiCa,CAAE,OAAOC,CAAE,GACxDpB,EAAQF,EAAO,qBAAsBQ,EAAK,EAAI,CAClD,CACJ,CACA,OAAOC,CACX,CAEAhB,GAAQ,cAAgBK,KCvFxB,IAAAyB,GAAAC,EAAAC,IAAA,cAEA,SAASC,GAAoBC,EAAQC,EAAQC,EAAK,CAC9C,GAAID,EAAQ,CACJC,IAAQ,OACRA,EAAMD,EAAO,QACjB,QAASE,EAAID,EAAM,EAAGC,GAAK,EAAG,EAAEA,EAAG,CAC/B,IAAIC,EAAKH,EAAOE,CAAC,EACjB,OAAQC,EAAG,KAAM,CACb,IAAK,QACL,IAAK,UACL,IAAK,UACDJ,GAAUI,EAAG,OAAO,OACpB,QACR,CAIA,IADAA,EAAKH,EAAO,EAAEE,CAAC,EACRC,GAAI,OAAS,SAChBJ,GAAUI,EAAG,OAAO,OACpBA,EAAKH,EAAO,EAAEE,CAAC,EAEnB,KACJ,CACJ,CACA,OAAOH,CACX,CAEAF,GAAQ,oBAAsBC,KC5B9B,IAAAM,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAQ,KACRC,GAAW,IACXC,GAAoB,KACpBC,GAAgB,KAChBC,GAAa,KACbC,GAA0B,KAExBC,GAAK,CAAE,YAAAC,GAAa,iBAAAC,EAAiB,EAC3C,SAASD,GAAYE,EAAKC,EAAOC,EAAOC,EAAS,CAC7C,IAAMC,EAAQJ,EAAI,MACZ,CAAE,YAAAK,EAAa,QAAAC,EAAS,OAAAC,EAAQ,IAAAC,CAAI,EAAIN,EAC1CO,EACAC,EAAa,GACjB,OAAQT,EAAM,KAAM,CAChB,IAAK,QACDQ,EAAOE,GAAaX,EAAKC,EAAOE,CAAO,GACnCI,GAAUC,IACVL,EAAQF,EAAO,cAAe,+CAA+C,EACjF,MACJ,IAAK,SACL,IAAK,uBACL,IAAK,uBACL,IAAK,eACDQ,EAAOf,GAAc,cAAcM,EAAKC,EAAOO,EAAKL,CAAO,EACvDI,IACAE,EAAK,OAASF,EAAO,OAAO,UAAU,CAAC,GAC3C,MACJ,IAAK,YACL,IAAK,YACL,IAAK,kBACDE,EAAOhB,GAAkB,kBAAkBI,GAAIG,EAAKC,EAAOC,EAAOC,CAAO,EACrEI,IACAE,EAAK,OAASF,EAAO,OAAO,UAAU,CAAC,GAC3C,MACJ,QAAS,CACL,IAAMK,EAAUX,EAAM,OAAS,QACzBA,EAAM,QACN,4BAA4BA,EAAM,IAAI,IAC5CE,EAAQF,EAAO,mBAAoBW,CAAO,EAC1CH,EAAOV,GAAiBC,EAAKC,EAAM,OAAQ,OAAW,KAAMC,EAAOC,CAAO,EAC1EO,EAAa,EACjB,CACJ,CACA,OAAIH,GAAUE,EAAK,SAAW,IAC1BN,EAAQI,EAAQ,YAAa,kCAAkC,EAC/DH,GACAJ,EAAI,QAAQ,aACX,CAACR,GAAS,SAASiB,CAAI,GACpB,OAAOA,EAAK,OAAU,UACrBA,EAAK,KAAOA,EAAK,MAAQ,0BAE9BN,EAAQK,GAAOP,EAAO,iBADV,2CAC+B,EAE3CI,IACAI,EAAK,YAAc,IACnBH,IACIL,EAAM,OAAS,UAAYA,EAAM,SAAW,GAC5CQ,EAAK,QAAUH,EAEfG,EAAK,cAAgBH,GAGzBN,EAAI,QAAQ,kBAAoBU,IAChCD,EAAK,SAAWR,GACbQ,CACX,CACA,SAASV,GAAiBC,EAAKa,EAAQC,EAAQC,EAAK,CAAE,YAAAV,EAAa,QAAAC,EAAS,OAAAC,EAAQ,IAAAC,EAAK,IAAAQ,CAAI,EAAGb,EAAS,CACrG,IAAMF,EAAQ,CACV,KAAM,SACN,OAAQL,GAAwB,oBAAoBiB,EAAQC,EAAQC,CAAG,EACvE,OAAQ,GACR,OAAQ,EACZ,EACMN,EAAOf,GAAc,cAAcM,EAAKC,EAAOO,EAAKL,CAAO,EACjE,OAAII,IACAE,EAAK,OAASF,EAAO,OAAO,UAAU,CAAC,EACnCE,EAAK,SAAW,IAChBN,EAAQI,EAAQ,YAAa,kCAAkC,GAEnEF,IACAI,EAAK,YAAc,IACnBH,IACAG,EAAK,QAAUH,EACfG,EAAK,MAAM,CAAC,EAAIO,GAEbP,CACX,CACA,SAASE,GAAa,CAAE,QAAAM,CAAQ,EAAG,CAAE,OAAAJ,EAAQ,OAAAK,EAAQ,IAAAF,CAAI,EAAGb,EAAS,CACjE,IAAMgB,EAAQ,IAAI5B,GAAM,MAAM2B,EAAO,UAAU,CAAC,CAAC,EAC7CC,EAAM,SAAW,IACjBhB,EAAQU,EAAQ,YAAa,iCAAiC,EAC9DM,EAAM,OAAO,SAAS,GAAG,GACzBhB,EAAQU,EAASK,EAAO,OAAS,EAAG,YAAa,iCAAkC,EAAI,EAC3F,IAAME,EAAWP,EAASK,EAAO,OAC3BG,EAAK1B,GAAW,WAAWqB,EAAKI,EAAUH,EAAQ,OAAQd,CAAO,EACvE,OAAAgB,EAAM,MAAQ,CAACN,EAAQO,EAAUC,EAAG,MAAM,EACtCA,EAAG,UACHF,EAAM,QAAUE,EAAG,SAChBF,CACX,CAEA7B,GAAQ,iBAAmBS,GAC3BT,GAAQ,YAAcQ,KCxGtB,IAAAwB,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,KACXC,GAAc,KACdC,GAAa,KACbC,GAAe,KAEnB,SAASC,GAAWC,EAASC,EAAY,CAAE,OAAAC,EAAQ,MAAAC,EAAO,MAAAC,EAAO,IAAAC,CAAI,EAAGC,EAAS,CAC7E,IAAMC,EAAO,OAAO,OAAO,CAAE,YAAaN,CAAW,EAAGD,CAAO,EACzDQ,EAAM,IAAIb,GAAS,SAAS,OAAWY,CAAI,EAC3CE,EAAM,CACR,MAAO,GACP,OAAQ,GACR,WAAYD,EAAI,WAChB,QAASA,EAAI,QACb,OAAQA,EAAI,MAChB,EACME,EAAQZ,GAAa,aAAaK,EAAO,CAC3C,UAAW,YACX,KAAMC,GAASC,IAAM,CAAC,EACtB,OAAAH,EACA,QAAAI,EACA,aAAc,EACd,eAAgB,EACpB,CAAC,EACGI,EAAM,QACNF,EAAI,WAAW,SAAW,GACtBJ,IACCA,EAAM,OAAS,aAAeA,EAAM,OAAS,cAC9C,CAACM,EAAM,YACPJ,EAAQI,EAAM,IAAK,eAAgB,uEAAuE,GAGlHF,EAAI,SAAWJ,EACTR,GAAY,YAAYa,EAAKL,EAAOM,EAAOJ,CAAO,EAClDV,GAAY,iBAAiBa,EAAKC,EAAM,IAAKP,EAAO,KAAMO,EAAOJ,CAAO,EAC9E,IAAMK,EAAaH,EAAI,SAAS,MAAM,CAAC,EACjCI,EAAKf,GAAW,WAAWQ,EAAKM,EAAY,GAAOL,CAAO,EAChE,OAAIM,EAAG,UACHJ,EAAI,QAAUI,EAAG,SACrBJ,EAAI,MAAQ,CAACN,EAAQS,EAAYC,EAAG,MAAM,EACnCJ,CACX,CAEAd,GAAQ,WAAaK,KC5CrB,IAAAc,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAe,QAAQ,cAAc,EACrCC,GAAa,KACbC,GAAW,KACXC,GAAS,KACTC,GAAW,IACXC,GAAa,KACbC,GAAa,KAEjB,SAASC,GAAYC,EAAK,CACtB,GAAI,OAAOA,GAAQ,SACf,MAAO,CAACA,EAAKA,EAAM,CAAC,EACxB,GAAI,MAAM,QAAQA,CAAG,EACjB,OAAOA,EAAI,SAAW,EAAIA,EAAM,CAACA,EAAI,CAAC,EAAGA,EAAI,CAAC,CAAC,EACnD,GAAM,CAAE,OAAAC,EAAQ,OAAAC,CAAO,EAAIF,EAC3B,MAAO,CAACC,EAAQA,GAAU,OAAOC,GAAW,SAAWA,EAAO,OAAS,EAAE,CAC7E,CACA,SAASC,GAAaC,EAAS,CAC3B,IAAIC,EAAU,GACVC,EAAY,GACZC,EAAiB,GACrB,QAAS,EAAI,EAAG,EAAIH,EAAQ,OAAQ,EAAE,EAAG,CACrC,IAAMF,EAASE,EAAQ,CAAC,EACxB,OAAQF,EAAO,CAAC,EAAG,CACf,IAAK,IACDG,IACKA,IAAY,GAAK,GAAKE,EAAiB;AAAA;AAAA,EAAS;AAAA,IAC5CL,EAAO,UAAU,CAAC,GAAK,KAChCI,EAAY,GACZC,EAAiB,GACjB,MACJ,IAAK,IACGH,EAAQ,EAAI,CAAC,IAAI,CAAC,IAAM,MACxB,GAAK,GACTE,EAAY,GACZ,MACJ,QAESA,IACDC,EAAiB,IACrBD,EAAY,EACpB,CACJ,CACA,MAAO,CAAE,QAAAD,EAAS,eAAAE,CAAe,CACrC,CAYA,IAAMC,GAAN,KAAe,CACX,YAAYC,EAAU,CAAC,EAAG,CACtB,KAAK,IAAM,KACX,KAAK,aAAe,GACpB,KAAK,QAAU,CAAC,EAChB,KAAK,OAAS,CAAC,EACf,KAAK,SAAW,CAAC,EACjB,KAAK,QAAU,CAACP,EAAQQ,EAAMC,EAASC,IAAY,CAC/C,IAAMC,EAAMd,GAAYG,CAAM,EAC1BU,EACA,KAAK,SAAS,KAAK,IAAIjB,GAAO,YAAYkB,EAAKH,EAAMC,CAAO,CAAC,EAE7D,KAAK,OAAO,KAAK,IAAIhB,GAAO,eAAekB,EAAKH,EAAMC,CAAO,CAAC,CACtE,EAEA,KAAK,WAAa,IAAIlB,GAAW,WAAW,CAAE,QAASgB,EAAQ,SAAW,KAAM,CAAC,EACjF,KAAK,QAAUA,CACnB,CACA,SAASK,EAAKC,EAAU,CACpB,GAAM,CAAE,QAAAV,EAAS,eAAAE,CAAe,EAAIJ,GAAa,KAAK,OAAO,EAE7D,GAAIE,EAAS,CACT,IAAMW,EAAKF,EAAI,SACf,GAAIC,EACAD,EAAI,QAAUA,EAAI,QAAU,GAAGA,EAAI,OAAO;AAAA,EAAKT,CAAO,GAAKA,UAEtDE,GAAkBO,EAAI,WAAW,UAAY,CAACE,EACnDF,EAAI,cAAgBT,UAEfT,GAAS,aAAaoB,CAAE,GAAK,CAACA,EAAG,MAAQA,EAAG,MAAM,OAAS,EAAG,CACnE,IAAIC,EAAKD,EAAG,MAAM,CAAC,EACfpB,GAAS,OAAOqB,CAAE,IAClBA,EAAKA,EAAG,KACZ,IAAMC,EAAKD,EAAG,cACdA,EAAG,cAAgBC,EAAK,GAAGb,CAAO;AAAA,EAAKa,CAAE,GAAKb,CAClD,KACK,CACD,IAAMa,EAAKF,EAAG,cACdA,EAAG,cAAgBE,EAAK,GAAGb,CAAO;AAAA,EAAKa,CAAE,GAAKb,CAClD,CACJ,CACIU,GACA,MAAM,UAAU,KAAK,MAAMD,EAAI,OAAQ,KAAK,MAAM,EAClD,MAAM,UAAU,KAAK,MAAMA,EAAI,SAAU,KAAK,QAAQ,IAGtDA,EAAI,OAAS,KAAK,OAClBA,EAAI,SAAW,KAAK,UAExB,KAAK,QAAU,CAAC,EAChB,KAAK,OAAS,CAAC,EACf,KAAK,SAAW,CAAC,CACrB,CAMA,YAAa,CACT,MAAO,CACH,QAASX,GAAa,KAAK,OAAO,EAAE,QACpC,WAAY,KAAK,WACjB,OAAQ,KAAK,OACb,SAAU,KAAK,QACnB,CACJ,CAOA,CAAC,QAAQgB,EAAQC,EAAW,GAAOC,EAAY,GAAI,CAC/C,QAAWC,KAASH,EAChB,MAAO,KAAK,KAAKG,CAAK,EAC1B,MAAO,KAAK,IAAIF,EAAUC,CAAS,CACvC,CAEA,CAAC,KAAKC,EAAO,CAGT,OAFI9B,GAAa,IAAI,YACjB,QAAQ,IAAI8B,EAAO,CAAE,MAAO,IAAK,CAAC,EAC9BA,EAAM,KAAM,CAChB,IAAK,YACD,KAAK,WAAW,IAAIA,EAAM,OAAQ,CAACrB,EAAQU,EAASC,IAAY,CAC5D,IAAMC,EAAMd,GAAYuB,CAAK,EAC7BT,EAAI,CAAC,GAAKZ,EACV,KAAK,QAAQY,EAAK,gBAAiBF,EAASC,CAAO,CACvD,CAAC,EACD,KAAK,QAAQ,KAAKU,EAAM,MAAM,EAC9B,KAAK,aAAe,GACpB,MACJ,IAAK,WAAY,CACb,IAAMR,EAAMjB,GAAW,WAAW,KAAK,QAAS,KAAK,WAAYyB,EAAO,KAAK,OAAO,EAChF,KAAK,cAAgB,CAACR,EAAI,WAAW,UACrC,KAAK,QAAQQ,EAAO,eAAgB,iDAAiD,EACzF,KAAK,SAASR,EAAK,EAAK,EACpB,KAAK,MACL,MAAM,KAAK,KACf,KAAK,IAAMA,EACX,KAAK,aAAe,GACpB,KACJ,CACA,IAAK,kBACL,IAAK,QACD,MACJ,IAAK,UACL,IAAK,UACD,KAAK,QAAQ,KAAKQ,EAAM,MAAM,EAC9B,MACJ,IAAK,QAAS,CACV,IAAMC,EAAMD,EAAM,OACZ,GAAGA,EAAM,OAAO,KAAK,KAAK,UAAUA,EAAM,MAAM,CAAC,GACjDA,EAAM,QACNE,EAAQ,IAAI7B,GAAO,eAAeI,GAAYuB,CAAK,EAAG,mBAAoBC,CAAG,EAC/E,KAAK,cAAgB,CAAC,KAAK,IAC3B,KAAK,OAAO,KAAKC,CAAK,EAEtB,KAAK,IAAI,OAAO,KAAKA,CAAK,EAC9B,KACJ,CACA,IAAK,UAAW,CACZ,GAAI,CAAC,KAAK,IAAK,CACX,IAAMD,EAAM,gDACZ,KAAK,OAAO,KAAK,IAAI5B,GAAO,eAAeI,GAAYuB,CAAK,EAAG,mBAAoBC,CAAG,CAAC,EACvF,KACJ,CACA,KAAK,IAAI,WAAW,OAAS,GAC7B,IAAME,EAAM3B,GAAW,WAAWwB,EAAM,IAAKA,EAAM,OAASA,EAAM,OAAO,OAAQ,KAAK,IAAI,QAAQ,OAAQ,KAAK,OAAO,EAEtH,GADA,KAAK,SAAS,KAAK,IAAK,EAAI,EACxBG,EAAI,QAAS,CACb,IAAMT,EAAK,KAAK,IAAI,QACpB,KAAK,IAAI,QAAUA,EAAK,GAAGA,CAAE;AAAA,EAAKS,EAAI,OAAO,GAAKA,EAAI,OAC1D,CACA,KAAK,IAAI,MAAM,CAAC,EAAIA,EAAI,OACxB,KACJ,CACA,QACI,KAAK,OAAO,KAAK,IAAI9B,GAAO,eAAeI,GAAYuB,CAAK,EAAG,mBAAoB,qBAAqBA,EAAM,IAAI,EAAE,CAAC,CAC7H,CACJ,CAOA,CAAC,IAAIF,EAAW,GAAOC,EAAY,GAAI,CACnC,GAAI,KAAK,IACL,KAAK,SAAS,KAAK,IAAK,EAAI,EAC5B,MAAM,KAAK,IACX,KAAK,IAAM,aAEND,EAAU,CACf,IAAMM,EAAO,OAAO,OAAO,CAAE,YAAa,KAAK,UAAW,EAAG,KAAK,OAAO,EACnEZ,EAAM,IAAIpB,GAAS,SAAS,OAAWgC,CAAI,EAC7C,KAAK,cACL,KAAK,QAAQL,EAAW,eAAgB,uCAAuC,EACnFP,EAAI,MAAQ,CAAC,EAAGO,EAAWA,CAAS,EACpC,KAAK,SAASP,EAAK,EAAK,EACxB,MAAMA,CACV,CACJ,CACJ,EAEAvB,GAAQ,SAAWiB,KC7NnB,IAAAmB,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAqB,KACrBC,GAAoB,KACpBC,GAAS,KACTC,GAAkB,KAEtB,SAASC,GAAgBC,EAAOC,EAAS,GAAMC,EAAS,CACpD,GAAIF,EAAO,CACP,IAAMG,EAAW,CAACC,EAAKC,EAAMC,IAAY,CACrC,IAAMC,EAAS,OAAOH,GAAQ,SAAWA,EAAM,MAAM,QAAQA,CAAG,EAAIA,EAAI,CAAC,EAAIA,EAAI,OACjF,GAAIF,EACAA,EAAQK,EAAQF,EAAMC,CAAO,MAE7B,OAAM,IAAIT,GAAO,eAAe,CAACU,EAAQA,EAAS,CAAC,EAAGF,EAAMC,CAAO,CAC3E,EACA,OAAQN,EAAM,KAAM,CAChB,IAAK,SACL,IAAK,uBACL,IAAK,uBACD,OAAOJ,GAAkB,kBAAkBI,EAAOC,EAAQE,CAAQ,EACtE,IAAK,eACD,OAAOR,GAAmB,mBAAmB,CAAE,QAAS,CAAE,OAAAM,CAAO,CAAE,EAAGD,EAAOG,CAAQ,CAC7F,CACJ,CACA,OAAO,IACX,CAeA,SAASK,GAAkBC,EAAOC,EAAS,CACvC,GAAM,CAAE,YAAAC,EAAc,GAAO,OAAAC,EAAQ,OAAAC,EAAS,GAAO,OAAAN,EAAS,GAAI,KAAAO,EAAO,OAAQ,EAAIJ,EAC/EK,EAASjB,GAAgB,gBAAgB,CAAE,KAAAgB,EAAM,MAAAL,CAAM,EAAG,CAC5D,YAAAE,EACA,OAAQC,EAAS,EAAI,IAAI,OAAOA,CAAM,EAAI,GAC1C,OAAAC,EACA,QAAS,CAAE,WAAY,GAAM,UAAW,EAAG,CAC/C,CAAC,EACKG,EAAMN,EAAQ,KAAO,CACvB,CAAE,KAAM,UAAW,OAAQ,GAAI,OAAAE,EAAQ,OAAQ;AAAA,CAAK,CACxD,EACA,OAAQG,EAAO,CAAC,EAAG,CACf,IAAK,IACL,IAAK,IAAK,CACN,IAAME,EAAKF,EAAO,QAAQ;AAAA,CAAI,EACxBG,EAAOH,EAAO,UAAU,EAAGE,CAAE,EAC7BE,EAAOJ,EAAO,UAAUE,EAAK,CAAC,EAAI;AAAA,EAClCG,EAAQ,CACV,CAAE,KAAM,sBAAuB,OAAAb,EAAQ,OAAAK,EAAQ,OAAQM,CAAK,CAChE,EACA,OAAKG,GAAmBD,EAAOJ,CAAG,GAC9BI,EAAM,KAAK,CAAE,KAAM,UAAW,OAAQ,GAAI,OAAAR,EAAQ,OAAQ;AAAA,CAAK,CAAC,EAC7D,CAAE,KAAM,eAAgB,OAAAL,EAAQ,OAAAK,EAAQ,MAAAQ,EAAO,OAAQD,CAAK,CACvE,CACA,IAAK,IACD,MAAO,CAAE,KAAM,uBAAwB,OAAAZ,EAAQ,OAAAK,EAAQ,OAAAG,EAAQ,IAAAC,CAAI,EACvE,IAAK,IACD,MAAO,CAAE,KAAM,uBAAwB,OAAAT,EAAQ,OAAAK,EAAQ,OAAAG,EAAQ,IAAAC,CAAI,EACvE,QACI,MAAO,CAAE,KAAM,SAAU,OAAAT,EAAQ,OAAAK,EAAQ,OAAAG,EAAQ,IAAAC,CAAI,CAC7D,CACJ,CAiBA,SAASM,GAAetB,EAAOS,EAAOC,EAAU,CAAC,EAAG,CAChD,GAAI,CAAE,SAAAa,EAAW,GAAO,YAAAZ,EAAc,GAAO,OAAAE,EAAS,GAAO,KAAAC,CAAK,EAAIJ,EAClEE,EAAS,WAAYZ,EAAQA,EAAM,OAAS,KAGhD,GAFIuB,GAAY,OAAOX,GAAW,WAC9BA,GAAU,GACV,CAACE,EACD,OAAQd,EAAM,KAAM,CAChB,IAAK,uBACDc,EAAO,eACP,MACJ,IAAK,uBACDA,EAAO,eACP,MACJ,IAAK,eAAgB,CACjB,IAAMU,EAASxB,EAAM,MAAM,CAAC,EAC5B,GAAIwB,EAAO,OAAS,sBAChB,MAAM,IAAI,MAAM,6BAA6B,EACjDV,EAAOU,EAAO,OAAO,CAAC,IAAM,IAAM,eAAiB,gBACnD,KACJ,CACA,QACIV,EAAO,OACf,CACJ,IAAMC,EAASjB,GAAgB,gBAAgB,CAAE,KAAAgB,EAAM,MAAAL,CAAM,EAAG,CAC5D,YAAaE,GAAeC,IAAW,KACvC,OAAQA,IAAW,MAAQA,EAAS,EAAI,IAAI,OAAOA,CAAM,EAAI,GAC7D,OAAAC,EACA,QAAS,CAAE,WAAY,GAAM,UAAW,EAAG,CAC/C,CAAC,EACD,OAAQE,EAAO,CAAC,EAAG,CACf,IAAK,IACL,IAAK,IACDU,GAAoBzB,EAAOe,CAAM,EACjC,MACJ,IAAK,IACDW,GAAmB1B,EAAOe,EAAQ,sBAAsB,EACxD,MACJ,IAAK,IACDW,GAAmB1B,EAAOe,EAAQ,sBAAsB,EACxD,MACJ,QACIW,GAAmB1B,EAAOe,EAAQ,QAAQ,CAClD,CACJ,CACA,SAASU,GAAoBzB,EAAOe,EAAQ,CACxC,IAAME,EAAKF,EAAO,QAAQ;AAAA,CAAI,EACxBG,EAAOH,EAAO,UAAU,EAAGE,CAAE,EAC7BE,EAAOJ,EAAO,UAAUE,EAAK,CAAC,EAAI;AAAA,EACxC,GAAIjB,EAAM,OAAS,eAAgB,CAC/B,IAAMwB,EAASxB,EAAM,MAAM,CAAC,EAC5B,GAAIwB,EAAO,OAAS,sBAChB,MAAM,IAAI,MAAM,6BAA6B,EACjDA,EAAO,OAASN,EAChBlB,EAAM,OAASmB,CACnB,KACK,CACD,GAAM,CAAE,OAAAZ,CAAO,EAAIP,EACbY,EAAS,WAAYZ,EAAQA,EAAM,OAAS,GAC5CoB,EAAQ,CACV,CAAE,KAAM,sBAAuB,OAAAb,EAAQ,OAAAK,EAAQ,OAAQM,CAAK,CAChE,EACKG,GAAmBD,EAAO,QAASpB,EAAQA,EAAM,IAAM,MAAS,GACjEoB,EAAM,KAAK,CAAE,KAAM,UAAW,OAAQ,GAAI,OAAAR,EAAQ,OAAQ;AAAA,CAAK,CAAC,EACpE,QAAWe,KAAO,OAAO,KAAK3B,CAAK,EAC3B2B,IAAQ,QAAUA,IAAQ,UAC1B,OAAO3B,EAAM2B,CAAG,EACxB,OAAO,OAAO3B,EAAO,CAAE,KAAM,eAAgB,OAAAY,EAAQ,MAAAQ,EAAO,OAAQD,CAAK,CAAC,CAC9E,CACJ,CAEA,SAASE,GAAmBD,EAAOJ,EAAK,CACpC,GAAIA,EACA,QAAWY,KAAMZ,EACb,OAAQY,EAAG,KAAM,CACb,IAAK,QACL,IAAK,UACDR,EAAM,KAAKQ,CAAE,EACb,MACJ,IAAK,UACD,OAAAR,EAAM,KAAKQ,CAAE,EACN,EACf,CACR,MAAO,EACX,CACA,SAASF,GAAmB1B,EAAOe,EAAQD,EAAM,CAC7C,OAAQd,EAAM,KAAM,CAChB,IAAK,SACL,IAAK,uBACL,IAAK,uBACDA,EAAM,KAAOc,EACbd,EAAM,OAASe,EACf,MACJ,IAAK,eAAgB,CACjB,IAAMC,EAAMhB,EAAM,MAAM,MAAM,CAAC,EAC3B6B,EAAKd,EAAO,OACZf,EAAM,MAAM,CAAC,EAAE,OAAS,wBACxB6B,GAAM7B,EAAM,MAAM,CAAC,EAAE,OAAO,QAChC,QAAW8B,KAAOd,EACdc,EAAI,QAAUD,EAClB,OAAO7B,EAAM,MACb,OAAO,OAAOA,EAAO,CAAE,KAAAc,EAAM,OAAAC,EAAQ,IAAAC,CAAI,CAAC,EAC1C,KACJ,CACA,IAAK,YACL,IAAK,YAAa,CAEd,IAAMe,EAAK,CAAE,KAAM,UAAW,OADf/B,EAAM,OAASe,EAAO,OACC,OAAQf,EAAM,OAAQ,OAAQ;AAAA,CAAK,EACzE,OAAOA,EAAM,MACb,OAAO,OAAOA,EAAO,CAAE,KAAAc,EAAM,OAAAC,EAAQ,IAAK,CAACgB,CAAE,CAAE,CAAC,EAChD,KACJ,CACA,QAAS,CACL,IAAMnB,EAAS,WAAYZ,EAAQA,EAAM,OAAS,GAC5CgB,EAAM,QAAShB,GAAS,MAAM,QAAQA,EAAM,GAAG,EAC/CA,EAAM,IAAI,OAAO4B,GAAMA,EAAG,OAAS,SACjCA,EAAG,OAAS,WACZA,EAAG,OAAS,SAAS,EACvB,CAAC,EACP,QAAWD,KAAO,OAAO,KAAK3B,CAAK,EAC3B2B,IAAQ,QAAUA,IAAQ,UAC1B,OAAO3B,EAAM2B,CAAG,EACxB,OAAO,OAAO3B,EAAO,CAAE,KAAAc,EAAM,OAAAF,EAAQ,OAAAG,EAAQ,IAAAC,CAAI,CAAC,CACtD,CACJ,CACJ,CAEAtB,GAAQ,kBAAoBc,GAC5Bd,GAAQ,gBAAkBK,GAC1BL,GAAQ,eAAiB4B,KCzNzB,IAAAU,GAAAC,EAAAC,IAAA,cAQA,IAAMC,GAAaC,GAAQ,SAAUA,EAAMC,GAAeD,CAAG,EAAIE,GAAcF,CAAG,EAClF,SAASC,GAAeE,EAAO,CAC3B,OAAQA,EAAM,KAAM,CAChB,IAAK,eAAgB,CACjB,IAAIC,EAAM,GACV,QAAWC,KAAOF,EAAM,MACpBC,GAAOH,GAAeI,CAAG,EAC7B,OAAOD,EAAMD,EAAM,MACvB,CACA,IAAK,YACL,IAAK,YAAa,CACd,IAAIC,EAAM,GACV,QAAWE,KAAQH,EAAM,MACrBC,GAAOF,GAAcI,CAAI,EAC7B,OAAOF,CACX,CACA,IAAK,kBAAmB,CACpB,IAAIA,EAAMD,EAAM,MAAM,OACtB,QAAWG,KAAQH,EAAM,MACrBC,GAAOF,GAAcI,CAAI,EAC7B,QAAWC,KAAMJ,EAAM,IACnBC,GAAOG,EAAG,OACd,OAAOH,CACX,CACA,IAAK,WAAY,CACb,IAAIA,EAAMF,GAAcC,CAAK,EAC7B,GAAIA,EAAM,IACN,QAAWI,KAAMJ,EAAM,IACnBC,GAAOG,EAAG,OAClB,OAAOH,CACX,CACA,QAAS,CACL,IAAIA,EAAMD,EAAM,OAChB,GAAI,QAASA,GAASA,EAAM,IACxB,QAAWI,KAAMJ,EAAM,IACnBC,GAAOG,EAAG,OAClB,OAAOH,CACX,CACJ,CACJ,CACA,SAASF,GAAc,CAAE,MAAAM,EAAO,IAAAC,EAAK,IAAAC,EAAK,MAAAC,CAAM,EAAG,CAC/C,IAAIP,EAAM,GACV,QAAWG,KAAMC,EACbJ,GAAOG,EAAG,OAGd,GAFIE,IACAL,GAAOH,GAAeQ,CAAG,GACzBC,EACA,QAAWH,KAAMG,EACbN,GAAOG,EAAG,OAClB,OAAII,IACAP,GAAOH,GAAeU,CAAK,GACxBP,CACX,CAEAN,GAAQ,UAAYC,KC9DpB,IAAAa,GAAAC,EAAAC,IAAA,cAEA,IAAMC,GAAQ,OAAO,aAAa,EAC5BC,GAAO,OAAO,eAAe,EAC7BC,GAAS,OAAO,aAAa,EA6BnC,SAASC,GAAMC,EAAKC,EAAS,CACrB,SAAUD,GAAOA,EAAI,OAAS,aAC9BA,EAAM,CAAE,MAAOA,EAAI,MAAO,MAAOA,EAAI,KAAM,GAC/CE,GAAO,OAAO,OAAO,CAAC,CAAC,EAAGF,EAAKC,CAAO,CAC1C,CAKAF,GAAM,MAAQH,GAEdG,GAAM,KAAOF,GAEbE,GAAM,OAASD,GAEfC,GAAM,WAAa,CAACC,EAAKG,IAAS,CAC9B,IAAIC,EAAOJ,EACX,OAAW,CAACK,EAAOC,CAAK,IAAKH,EAAM,CAC/B,IAAMI,EAAMH,IAAOC,CAAK,EACxB,GAAIE,GAAO,UAAWA,EAClBH,EAAOG,EAAI,MAAMD,CAAK,MAGtB,OACR,CACA,OAAOF,CACX,EAMAL,GAAM,iBAAmB,CAACC,EAAKG,IAAS,CACpC,IAAMK,EAAST,GAAM,WAAWC,EAAKG,EAAK,MAAM,EAAG,EAAE,CAAC,EAChDE,EAAQF,EAAKA,EAAK,OAAS,CAAC,EAAE,CAAC,EAC/BM,EAAOD,IAASH,CAAK,EAC3B,GAAII,GAAQ,UAAWA,EACnB,OAAOA,EACX,MAAM,IAAI,MAAM,6BAA6B,CACjD,EACA,SAASP,GAAOC,EAAMC,EAAMH,EAAS,CACjC,IAAIS,EAAOT,EAAQG,EAAMD,CAAI,EAC7B,GAAI,OAAOO,GAAS,SAChB,OAAOA,EACX,QAAWL,IAAS,CAAC,MAAO,OAAO,EAAG,CAClC,IAAMM,EAAQP,EAAKC,CAAK,EACxB,GAAIM,GAAS,UAAWA,EAAO,CAC3B,QAASC,EAAI,EAAGA,EAAID,EAAM,MAAM,OAAQ,EAAEC,EAAG,CACzC,IAAMC,EAAKX,GAAO,OAAO,OAAOC,EAAK,OAAO,CAAC,CAACE,EAAOO,CAAC,CAAC,CAAC,CAAC,EAAGD,EAAM,MAAMC,CAAC,EAAGX,CAAO,EACnF,GAAI,OAAOY,GAAO,SACdD,EAAIC,EAAK,MACR,IAAIA,IAAOjB,GACZ,OAAOA,GACFiB,IAAOf,KACZa,EAAM,MAAM,OAAOC,EAAG,CAAC,EACvBA,GAAK,GAEb,CACI,OAAOF,GAAS,YAAcL,IAAU,QACxCK,EAAOA,EAAKN,EAAMD,CAAI,EAC9B,CACJ,CACA,OAAO,OAAOO,GAAS,WAAaA,EAAKN,EAAMD,CAAI,EAAIO,CAC3D,CAEAf,GAAQ,MAAQI,KClGhB,IAAAe,GAAAC,EAAAC,GAAA,cAEA,IAAIC,GAAY,KACZC,GAAe,KACfC,GAAW,KAGTC,GAAM,SAENC,GAAW,IAEXC,GAAW,IAEXC,GAAS,IAETC,GAAgBC,GAAU,CAAC,CAACA,GAAS,UAAWA,EAEhDC,GAAYD,GAAU,CAAC,CAACA,IACzBA,EAAM,OAAS,UACZA,EAAM,OAAS,wBACfA,EAAM,OAAS,wBACfA,EAAM,OAAS,gBAGvB,SAASE,GAAYF,EAAO,CACxB,OAAQA,EAAO,CACX,KAAKL,GACD,MAAO,QACX,KAAKC,GACD,MAAO,QACX,KAAKC,GACD,MAAO,aACX,KAAKC,GACD,MAAO,WACX,QACI,OAAO,KAAK,UAAUE,CAAK,CACnC,CACJ,CAEA,SAASG,GAAUC,EAAQ,CACvB,OAAQA,EAAQ,CACZ,KAAKT,GACD,MAAO,kBACX,KAAKC,GACD,MAAO,WACX,KAAKC,GACD,MAAO,iBACX,KAAKC,GACD,MAAO,SACX,IAAK,MACD,MAAO,YACX,IAAK,MACD,MAAO,UACX,IAAK,GACL,IAAK;AAAA,EACL,IAAK;AAAA,EACD,MAAO,UACX,IAAK,IACD,MAAO,eACX,IAAK,IACD,MAAO,mBACX,IAAK,IACD,MAAO,gBACX,IAAK,IACD,MAAO,iBACX,IAAK,IACD,MAAO,eACX,IAAK,IACD,MAAO,iBACX,IAAK,IACD,MAAO,eACX,IAAK,IACD,MAAO,OACf,CACA,OAAQM,EAAO,CAAC,EAAG,CACf,IAAK,IACL,IAAK,IACD,MAAO,QACX,IAAK,IACD,MAAO,UACX,IAAK,IACD,MAAO,iBACX,IAAK,IACD,MAAO,QACX,IAAK,IACD,MAAO,SACX,IAAK,IACD,MAAO,MACX,IAAK,IACD,MAAO,uBACX,IAAK,IACD,MAAO,uBACX,IAAK,IACL,IAAK,IACD,MAAO,qBACf,CACA,OAAO,IACX,CAEAb,EAAQ,kBAAoBC,GAAU,kBACtCD,EAAQ,gBAAkBC,GAAU,gBACpCD,EAAQ,eAAiBC,GAAU,eACnCD,EAAQ,UAAYE,GAAa,UACjCF,EAAQ,MAAQG,GAAS,MACzBH,EAAQ,IAAMI,GACdJ,EAAQ,SAAWK,GACnBL,EAAQ,SAAWM,GACnBN,EAAQ,OAASO,GACjBP,EAAQ,aAAeQ,GACvBR,EAAQ,SAAWU,GACnBV,EAAQ,YAAcW,GACtBX,EAAQ,UAAYY,KC/GpB,IAAAE,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAM,KAqEV,SAASC,EAAQC,EAAI,CACjB,OAAQA,EAAI,CACR,KAAK,OACL,IAAK,IACL,IAAK;AAAA,EACL,IAAK,KACL,IAAK,IACD,MAAO,GACX,QACI,MAAO,EACf,CACJ,CACA,IAAMC,GAAY,IAAI,IAAI,wBAAwB,EAC5CC,GAAW,IAAI,IAAI,mFAAmF,EACtGC,GAAqB,IAAI,IAAI,OAAO,EACpCC,GAAqB,IAAI,IAAI;AAAA,IAAc,EAC3CC,GAAmBL,GAAO,CAACA,GAAMI,GAAmB,IAAIJ,CAAE,EAgB1DM,GAAN,KAAY,CACR,aAAc,CAKV,KAAK,MAAQ,GAMb,KAAK,kBAAoB,GAMzB,KAAK,gBAAkB,GAEvB,KAAK,OAAS,GAKd,KAAK,QAAU,GAEf,KAAK,UAAY,EAKjB,KAAK,WAAa,EAElB,KAAK,YAAc,EAEnB,KAAK,WAAa,KAElB,KAAK,KAAO,KAEZ,KAAK,IAAM,CACf,CAOA,CAAC,IAAIC,EAAQC,EAAa,GAAO,CAC7B,GAAID,EAAQ,CACR,GAAI,OAAOA,GAAW,SAClB,MAAM,UAAU,wBAAwB,EAC5C,KAAK,OAAS,KAAK,OAAS,KAAK,OAASA,EAASA,EACnD,KAAK,WAAa,IACtB,CACA,KAAK,MAAQ,CAACC,EACd,IAAIC,EAAO,KAAK,MAAQ,SACxB,KAAOA,IAASD,GAAc,KAAK,SAAS,CAAC,IACzCC,EAAO,MAAO,KAAK,UAAUA,CAAI,CACzC,CACA,WAAY,CACR,IAAIC,EAAI,KAAK,IACTV,EAAK,KAAK,OAAOU,CAAC,EACtB,KAAOV,IAAO,KAAOA,IAAO,KACxBA,EAAK,KAAK,OAAO,EAAEU,CAAC,EACxB,MAAI,CAACV,GAAMA,IAAO,KAAOA,IAAO;AAAA,EACrB,GACPA,IAAO,KACA,KAAK,OAAOU,EAAI,CAAC,IAAM;AAAA,EAC3B,EACX,CACA,OAAOC,EAAG,CACN,OAAO,KAAK,OAAO,KAAK,IAAMA,CAAC,CACnC,CACA,eAAeC,EAAQ,CACnB,IAAIZ,EAAK,KAAK,OAAOY,CAAM,EAC3B,GAAI,KAAK,WAAa,EAAG,CACrB,IAAIC,EAAS,EACb,KAAOb,IAAO,KACVA,EAAK,KAAK,OAAO,EAAEa,EAASD,CAAM,EACtC,GAAIZ,IAAO,KAAM,CACb,IAAMS,EAAO,KAAK,OAAOI,EAASD,EAAS,CAAC,EAC5C,GAAIH,IAAS;AAAA,GAAS,CAACA,GAAQ,CAAC,KAAK,MACjC,OAAOG,EAASC,EAAS,CACjC,CACA,OAAOb,IAAO;AAAA,GAAQa,GAAU,KAAK,YAAe,CAACb,GAAM,CAAC,KAAK,MAC3DY,EAASC,EACT,EACV,CACA,GAAIb,IAAO,KAAOA,IAAO,IAAK,CAC1B,IAAMc,EAAK,KAAK,OAAO,OAAOF,EAAQ,CAAC,EACvC,IAAKE,IAAO,OAASA,IAAO,QAAUf,EAAQ,KAAK,OAAOa,EAAS,CAAC,CAAC,EACjE,MAAO,EACf,CACA,OAAOA,CACX,CACA,SAAU,CACN,IAAIG,EAAM,KAAK,WAKf,OAJI,OAAOA,GAAQ,UAAaA,IAAQ,IAAMA,EAAM,KAAK,OACrDA,EAAM,KAAK,OAAO,QAAQ;AAAA,EAAM,KAAK,GAAG,EACxC,KAAK,WAAaA,GAElBA,IAAQ,GACD,KAAK,MAAQ,KAAK,OAAO,UAAU,KAAK,GAAG,EAAI,MACtD,KAAK,OAAOA,EAAM,CAAC,IAAM,OACzBA,GAAO,GACJ,KAAK,OAAO,UAAU,KAAK,IAAKA,CAAG,EAC9C,CACA,SAASJ,EAAG,CACR,OAAO,KAAK,IAAMA,GAAK,KAAK,OAAO,MACvC,CACA,QAAQK,EAAO,CACX,YAAK,OAAS,KAAK,OAAO,UAAU,KAAK,GAAG,EAC5C,KAAK,IAAM,EACX,KAAK,WAAa,KAClB,KAAK,KAAOA,EACL,IACX,CACA,KAAKL,EAAG,CACJ,OAAO,KAAK,OAAO,OAAO,KAAK,IAAKA,CAAC,CACzC,CACA,CAAC,UAAUF,EAAM,CACb,OAAQA,EAAM,CACV,IAAK,SACD,OAAO,MAAO,KAAK,YAAY,EACnC,IAAK,aACD,OAAO,MAAO,KAAK,eAAe,EACtC,IAAK,cACD,OAAO,MAAO,KAAK,gBAAgB,EACvC,IAAK,MACD,OAAO,MAAO,KAAK,cAAc,EACrC,IAAK,OACD,OAAO,MAAO,KAAK,oBAAoB,EAC3C,IAAK,gBACD,OAAO,MAAO,KAAK,kBAAkB,EACzC,IAAK,eACD,OAAO,MAAO,KAAK,iBAAiB,EACxC,IAAK,eACD,OAAO,MAAO,KAAK,iBAAiB,CAC5C,CACJ,CACA,CAAC,aAAc,CACX,IAAIQ,EAAO,KAAK,QAAQ,EACxB,GAAIA,IAAS,KACT,OAAO,KAAK,QAAQ,QAAQ,EAKhC,GAJIA,EAAK,CAAC,IAAMnB,GAAI,MAChB,MAAO,KAAK,UAAU,CAAC,EACvBmB,EAAOA,EAAK,UAAU,CAAC,GAEvBA,EAAK,CAAC,IAAM,IAAK,CACjB,IAAIC,EAASD,EAAK,OACdE,EAAKF,EAAK,QAAQ,GAAG,EACzB,KAAOE,IAAO,IAAI,CACd,IAAMnB,EAAKiB,EAAKE,EAAK,CAAC,EACtB,GAAInB,IAAO,KAAOA,IAAO,IAAM,CAC3BkB,EAASC,EAAK,EACd,KACJ,MAEIA,EAAKF,EAAK,QAAQ,IAAKE,EAAK,CAAC,CAErC,CACA,OAAa,CACT,IAAMnB,EAAKiB,EAAKC,EAAS,CAAC,EAC1B,GAAIlB,IAAO,KAAOA,IAAO,IACrBkB,GAAU,MAEV,MACR,CACA,IAAMP,GAAK,MAAO,KAAK,UAAUO,CAAM,IAAM,MAAO,KAAK,WAAW,EAAI,GACxE,aAAO,KAAK,UAAUD,EAAK,OAASN,CAAC,EACrC,KAAK,YAAY,EACV,QACX,CACA,GAAI,KAAK,UAAU,EAAG,CAClB,IAAMS,EAAK,MAAO,KAAK,WAAW,EAAI,EACtC,aAAO,KAAK,UAAUH,EAAK,OAASG,CAAE,EACtC,MAAO,KAAK,YAAY,EACjB,QACX,CACA,aAAMtB,GAAI,SACH,MAAO,KAAK,eAAe,CACtC,CACA,CAAC,gBAAiB,CACd,IAAME,EAAK,KAAK,OAAO,CAAC,EACxB,GAAI,CAACA,GAAM,CAAC,KAAK,MACb,OAAO,KAAK,QAAQ,YAAY,EACpC,GAAIA,IAAO,KAAOA,IAAO,IAAK,CAC1B,GAAI,CAAC,KAAK,OAAS,CAAC,KAAK,SAAS,CAAC,EAC/B,OAAO,KAAK,QAAQ,YAAY,EACpC,IAAMqB,EAAI,KAAK,KAAK,CAAC,EACrB,IAAKA,IAAM,OAASA,IAAM,QAAUtB,EAAQ,KAAK,OAAO,CAAC,CAAC,EACtD,aAAO,KAAK,UAAU,CAAC,EACvB,KAAK,YAAc,EACnB,KAAK,WAAa,EACXsB,IAAM,MAAQ,MAAQ,QAErC,CACA,YAAK,YAAc,MAAO,KAAK,WAAW,EAAK,EAC3C,KAAK,WAAa,KAAK,aAAe,CAACtB,EAAQ,KAAK,OAAO,CAAC,CAAC,IAC7D,KAAK,WAAa,KAAK,aACpB,MAAO,KAAK,gBAAgB,CACvC,CACA,CAAC,iBAAkB,CACf,GAAM,CAACuB,EAAKC,CAAG,EAAI,KAAK,KAAK,CAAC,EAC9B,GAAI,CAACA,GAAO,CAAC,KAAK,MACd,OAAO,KAAK,QAAQ,aAAa,EACrC,IAAKD,IAAQ,KAAOA,IAAQ,KAAOA,IAAQ,MAAQvB,EAAQwB,CAAG,EAAG,CAC7D,IAAM,GAAK,MAAO,KAAK,UAAU,CAAC,IAAM,MAAO,KAAK,WAAW,EAAI,GACnE,YAAK,WAAa,KAAK,YAAc,EACrC,KAAK,aAAe,EACb,MAAO,KAAK,gBAAgB,CACvC,CACA,MAAO,KACX,CACA,CAAC,eAAgB,CACb,MAAO,KAAK,WAAW,EAAI,EAC3B,IAAMN,EAAO,KAAK,QAAQ,EAC1B,GAAIA,IAAS,KACT,OAAO,KAAK,QAAQ,KAAK,EAC7B,IAAIN,EAAI,MAAO,KAAK,eAAe,EACnC,OAAQM,EAAKN,CAAC,EAAG,CACb,IAAK,IACD,MAAO,KAAK,UAAUM,EAAK,OAASN,CAAC,EAEzC,KAAK,OACD,aAAO,KAAK,YAAY,EACjB,MAAO,KAAK,eAAe,EACtC,IAAK,IACL,IAAK,IACD,aAAO,KAAK,UAAU,CAAC,EACvB,KAAK,QAAU,GACf,KAAK,UAAY,EACV,OACX,IAAK,IACL,IAAK,IAED,aAAO,KAAK,UAAU,CAAC,EAChB,MACX,IAAK,IACD,aAAO,KAAK,UAAUN,EAAe,EAC9B,MACX,IAAK,IACL,IAAK,IACD,OAAO,MAAO,KAAK,kBAAkB,EACzC,IAAK,IACL,IAAK,IACD,OAAAM,GAAK,MAAO,KAAK,uBAAuB,EACxCA,GAAK,MAAO,KAAK,WAAW,EAAI,EAChC,MAAO,KAAK,UAAUM,EAAK,OAASN,CAAC,EACrC,MAAO,KAAK,YAAY,EACjB,MAAO,KAAK,iBAAiB,EACxC,QACI,OAAO,MAAO,KAAK,iBAAiB,CAC5C,CACJ,CACA,CAAC,qBAAsB,CACnB,IAAIa,EAAIJ,EACJP,EAAS,GACb,GACIW,EAAK,MAAO,KAAK,YAAY,EACzBA,EAAK,GACLJ,EAAK,MAAO,KAAK,WAAW,EAAK,EACjC,KAAK,YAAcP,EAASO,GAG5BA,EAAK,EAETA,GAAM,MAAO,KAAK,WAAW,EAAI,QAC5BI,EAAKJ,EAAK,GACnB,IAAMH,EAAO,KAAK,QAAQ,EAC1B,GAAIA,IAAS,KACT,OAAO,KAAK,QAAQ,MAAM,EAC9B,IAAKJ,IAAW,IAAMA,EAAS,KAAK,YAAcI,EAAK,CAAC,IAAM,KACzDJ,IAAW,IACPI,EAAK,WAAW,KAAK,GAAKA,EAAK,WAAW,KAAK,IAChDlB,EAAQkB,EAAK,CAAC,CAAC,IAOf,EAHoBJ,IAAW,KAAK,WAAa,GACjD,KAAK,YAAc,IAClBI,EAAK,CAAC,IAAM,KAAOA,EAAK,CAAC,IAAM,MAGhC,YAAK,UAAY,EACjB,MAAMnB,GAAI,SACH,MAAO,KAAK,eAAe,EAG1C,IAAIa,EAAI,EACR,KAAOM,EAAKN,CAAC,IAAM,KACfA,GAAK,MAAO,KAAK,UAAU,CAAC,EAC5BA,GAAK,MAAO,KAAK,WAAW,EAAI,EAChC,KAAK,QAAU,GAGnB,OADAA,GAAK,MAAO,KAAK,eAAe,EACxBM,EAAKN,CAAC,EAAG,CACb,KAAK,OACD,MAAO,OACX,IAAK,IACD,aAAO,KAAK,UAAUM,EAAK,OAASN,CAAC,EAC9B,OACX,IAAK,IACL,IAAK,IACD,aAAO,KAAK,UAAU,CAAC,EACvB,KAAK,QAAU,GACf,KAAK,WAAa,EACX,OACX,IAAK,IACL,IAAK,IACD,aAAO,KAAK,UAAU,CAAC,EACvB,KAAK,QAAU,GACf,KAAK,WAAa,EACX,KAAK,UAAY,OAAS,MACrC,IAAK,IACD,aAAO,KAAK,UAAUN,EAAe,EAC9B,OACX,IAAK,IACL,IAAK,IACD,YAAK,QAAU,GACR,MAAO,KAAK,kBAAkB,EACzC,IAAK,IAAK,CACN,IAAMI,EAAO,KAAK,OAAO,CAAC,EAC1B,GAAI,KAAK,SAAWV,EAAQU,CAAI,GAAKA,IAAS,IAC1C,YAAK,QAAU,GACf,MAAO,KAAK,UAAU,CAAC,EACvB,MAAO,KAAK,WAAW,EAAI,EACpB,MAEf,CAEA,QACI,YAAK,QAAU,GACR,MAAO,KAAK,iBAAiB,CAC5C,CACJ,CACA,CAAC,mBAAoB,CACjB,IAAMgB,EAAQ,KAAK,OAAO,CAAC,EACvBV,EAAM,KAAK,OAAO,QAAQU,EAAO,KAAK,IAAM,CAAC,EACjD,GAAIA,IAAU,IACV,KAAOV,IAAQ,IAAM,KAAK,OAAOA,EAAM,CAAC,IAAM,KAC1CA,EAAM,KAAK,OAAO,QAAQ,IAAKA,EAAM,CAAC,MAI1C,MAAOA,IAAQ,IAAI,CACf,IAAIJ,EAAI,EACR,KAAO,KAAK,OAAOI,EAAM,EAAIJ,CAAC,IAAM,MAChCA,GAAK,EACT,GAAIA,EAAI,IAAM,EACV,MACJI,EAAM,KAAK,OAAO,QAAQ,IAAKA,EAAM,CAAC,CAC1C,CAGJ,IAAMW,EAAK,KAAK,OAAO,UAAU,EAAGX,CAAG,EACnCS,EAAKE,EAAG,QAAQ;AAAA,EAAM,KAAK,GAAG,EAClC,GAAIF,IAAO,GAAI,CACX,KAAOA,IAAO,IAAI,CACd,IAAML,EAAK,KAAK,eAAeK,EAAK,CAAC,EACrC,GAAIL,IAAO,GACP,MACJK,EAAKE,EAAG,QAAQ;AAAA,EAAMP,CAAE,CAC5B,CACIK,IAAO,KAEPT,EAAMS,GAAME,EAAGF,EAAK,CAAC,IAAM,KAAO,EAAI,GAE9C,CACA,GAAIT,IAAQ,GAAI,CACZ,GAAI,CAAC,KAAK,MACN,OAAO,KAAK,QAAQ,eAAe,EACvCA,EAAM,KAAK,OAAO,MACtB,CACA,aAAO,KAAK,YAAYA,EAAM,EAAG,EAAK,EAC/B,KAAK,UAAY,OAAS,KACrC,CACA,CAAC,wBAAyB,CACtB,KAAK,kBAAoB,GACzB,KAAK,gBAAkB,GACvB,IAAIL,EAAI,KAAK,IACb,OAAa,CACT,IAAMV,EAAK,KAAK,OAAO,EAAEU,CAAC,EAC1B,GAAIV,IAAO,IACP,KAAK,gBAAkB,WAClBA,EAAK,KAAOA,GAAM,IACvB,KAAK,kBAAoB,OAAOA,CAAE,EAAI,UACjCA,IAAO,IACZ,KACR,CACA,OAAO,MAAO,KAAK,UAAUA,GAAMD,EAAQC,CAAE,GAAKA,IAAO,GAAG,CAChE,CACA,CAAC,kBAAmB,CAChB,IAAIwB,EAAK,KAAK,IAAM,EAChBX,EAAS,EACTb,EACJ2B,EAAM,QAASjB,EAAI,KAAK,IAAMV,EAAK,KAAK,OAAOU,CAAC,EAAI,EAAEA,EAClD,OAAQV,EAAI,CACR,IAAK,IACDa,GAAU,EACV,MACJ,IAAK;AAAA,EACDW,EAAKd,EACLG,EAAS,EACT,MACJ,IAAK,KAAM,CACP,IAAMJ,EAAO,KAAK,OAAOC,EAAI,CAAC,EAC9B,GAAI,CAACD,GAAQ,CAAC,KAAK,MACf,OAAO,KAAK,QAAQ,cAAc,EACtC,GAAIA,IAAS;AAAA,EACT,KACR,CACA,QACI,MAAMkB,CACd,CAEJ,GAAI,CAAC3B,GAAM,CAAC,KAAK,MACb,OAAO,KAAK,QAAQ,cAAc,EACtC,GAAIa,GAAU,KAAK,WAAY,CACvB,KAAK,oBAAsB,GAC3B,KAAK,WAAaA,EAElB,KAAK,WACD,KAAK,mBAAqB,KAAK,aAAe,EAAI,EAAI,KAAK,YAEnE,EAAG,CACC,IAAMM,EAAK,KAAK,eAAeK,EAAK,CAAC,EACrC,GAAIL,IAAO,GACP,MACJK,EAAK,KAAK,OAAO,QAAQ;AAAA,EAAML,CAAE,CACrC,OAASK,IAAO,IAChB,GAAIA,IAAO,GAAI,CACX,GAAI,CAAC,KAAK,MACN,OAAO,KAAK,QAAQ,cAAc,EACtCA,EAAK,KAAK,OAAO,MACrB,CACJ,CAGA,IAAI,EAAIA,EAAK,EAEb,IADAxB,EAAK,KAAK,OAAO,CAAC,EACXA,IAAO,KACVA,EAAK,KAAK,OAAO,EAAE,CAAC,EACxB,GAAIA,IAAO,IAAM,CACb,KAAOA,IAAO,KAAQA,IAAO,KAAOA,IAAO,MAAQA,IAAO;AAAA,GACtDA,EAAK,KAAK,OAAO,EAAE,CAAC,EACxBwB,EAAK,EAAI,CACb,SACS,CAAC,KAAK,gBACX,EAAG,CACC,IAAId,EAAIc,EAAK,EACTxB,EAAK,KAAK,OAAOU,CAAC,EAClBV,IAAO,OACPA,EAAK,KAAK,OAAO,EAAEU,CAAC,GACxB,IAAMkB,EAAWlB,EACjB,KAAOV,IAAO,KACVA,EAAK,KAAK,OAAO,EAAEU,CAAC,EACxB,GAAIV,IAAO;AAAA,GAAQU,GAAK,KAAK,KAAOA,EAAI,EAAIG,EAASe,EACjDJ,EAAKd,MAEL,MACR,OAAS,IAEb,aAAMZ,GAAI,OACV,MAAO,KAAK,YAAY0B,EAAK,EAAG,EAAI,EAC7B,MAAO,KAAK,eAAe,CACtC,CACA,CAAC,kBAAmB,CAChB,IAAMK,EAAS,KAAK,UAAY,EAC5Bd,EAAM,KAAK,IAAM,EACjBL,EAAI,KAAK,IAAM,EACfV,EACJ,KAAQA,EAAK,KAAK,OAAO,EAAEU,CAAC,GACxB,GAAIV,IAAO,IAAK,CACZ,IAAMS,EAAO,KAAK,OAAOC,EAAI,CAAC,EAC9B,GAAIX,EAAQU,CAAI,GAAMoB,GAAU1B,GAAmB,IAAIM,CAAI,EACvD,MACJM,EAAML,CACV,SACSX,EAAQC,CAAE,EAAG,CAClB,IAAIS,EAAO,KAAK,OAAOC,EAAI,CAAC,EAU5B,GATIV,IAAO,OACHS,IAAS;AAAA,GACTC,GAAK,EACLV,EAAK;AAAA,EACLS,EAAO,KAAK,OAAOC,EAAI,CAAC,GAGxBK,EAAML,GAEVD,IAAS,KAAQoB,GAAU1B,GAAmB,IAAIM,CAAI,EACtD,MACJ,GAAIT,IAAO;AAAA,EAAM,CACb,IAAMmB,EAAK,KAAK,eAAeT,EAAI,CAAC,EACpC,GAAIS,IAAO,GACP,MACJT,EAAI,KAAK,IAAIA,EAAGS,EAAK,CAAC,CAC1B,CACJ,KACK,CACD,GAAIU,GAAU1B,GAAmB,IAAIH,CAAE,EACnC,MACJe,EAAML,CACV,CAEJ,MAAI,CAACV,GAAM,CAAC,KAAK,MACN,KAAK,QAAQ,cAAc,GACtC,MAAMF,GAAI,OACV,MAAO,KAAK,YAAYiB,EAAM,EAAG,EAAI,EAC9Bc,EAAS,OAAS,MAC7B,CACA,CAAC,UAAUlB,EAAG,CACV,OAAIA,EAAI,GACJ,MAAM,KAAK,OAAO,OAAO,KAAK,IAAKA,CAAC,EACpC,KAAK,KAAOA,EACLA,GAEJ,CACX,CACA,CAAC,YAAYD,EAAGoB,EAAY,CACxB,IAAMT,EAAI,KAAK,OAAO,MAAM,KAAK,IAAKX,CAAC,EACvC,OAAIW,GACA,MAAMA,EACN,KAAK,KAAOA,EAAE,OACPA,EAAE,SAEJS,IACL,KAAM,IACH,EACX,CACA,CAAC,gBAAiB,CACd,OAAQ,KAAK,OAAO,CAAC,EAAG,CACpB,IAAK,IACD,OAAS,MAAO,KAAK,QAAQ,IACxB,MAAO,KAAK,WAAW,EAAI,IAC3B,MAAO,KAAK,eAAe,GACpC,IAAK,IACD,OAAS,MAAO,KAAK,UAAUzB,EAAe,IACzC,MAAO,KAAK,WAAW,EAAI,IAC3B,MAAO,KAAK,eAAe,GACpC,IAAK,IACL,IAAK,IACL,IAAK,IAAK,CACN,IAAMwB,EAAS,KAAK,UAAY,EAC1BN,EAAM,KAAK,OAAO,CAAC,EACzB,GAAIxB,EAAQwB,CAAG,GAAMM,GAAU1B,GAAmB,IAAIoB,CAAG,EACrD,OAAKM,EAEI,KAAK,UACV,KAAK,QAAU,IAFf,KAAK,WAAa,KAAK,YAAc,GAGhC,MAAO,KAAK,UAAU,CAAC,IAC3B,MAAO,KAAK,WAAW,EAAI,IAC3B,MAAO,KAAK,eAAe,EAExC,CACJ,CACA,MAAO,EACX,CACA,CAAC,SAAU,CACP,GAAI,KAAK,OAAO,CAAC,IAAM,IAAK,CACxB,IAAInB,EAAI,KAAK,IAAM,EACfV,EAAK,KAAK,OAAOU,CAAC,EACtB,KAAO,CAACX,EAAQC,CAAE,GAAKA,IAAO,KAC1BA,EAAK,KAAK,OAAO,EAAEU,CAAC,EACxB,OAAO,MAAO,KAAK,YAAYV,IAAO,IAAMU,EAAI,EAAIA,EAAG,EAAK,CAChE,KACK,CACD,IAAIA,EAAI,KAAK,IAAM,EACfV,EAAK,KAAK,OAAOU,CAAC,EACtB,KAAOV,GACH,GAAIE,GAAS,IAAIF,CAAE,EACfA,EAAK,KAAK,OAAO,EAAEU,CAAC,UACfV,IAAO,KACZC,GAAU,IAAI,KAAK,OAAOS,EAAI,CAAC,CAAC,GAChCT,GAAU,IAAI,KAAK,OAAOS,EAAI,CAAC,CAAC,EAChCV,EAAK,KAAK,OAAQU,GAAK,CAAE,MAGzB,OAER,OAAO,MAAO,KAAK,YAAYA,EAAG,EAAK,CAC3C,CACJ,CACA,CAAC,aAAc,CACX,IAAMV,EAAK,KAAK,OAAO,KAAK,GAAG,EAC/B,OAAIA,IAAO;AAAA,EACA,MAAO,KAAK,UAAU,CAAC,EACzBA,IAAO,MAAQ,KAAK,OAAO,CAAC,IAAM;AAAA,EAChC,MAAO,KAAK,UAAU,CAAC,EAEvB,CACf,CACA,CAAC,WAAW+B,EAAW,CACnB,IAAIrB,EAAI,KAAK,IAAM,EACfV,EACJ,GACIA,EAAK,KAAK,OAAO,EAAEU,CAAC,QACfV,IAAO,KAAQ+B,GAAa/B,IAAO,KAC5C,IAAMW,EAAID,EAAI,KAAK,IACnB,OAAIC,EAAI,IACJ,MAAM,KAAK,OAAO,OAAO,KAAK,IAAKA,CAAC,EACpC,KAAK,IAAMD,GAERC,CACX,CACA,CAAC,UAAUqB,EAAM,CACb,IAAItB,EAAI,KAAK,IACTV,EAAK,KAAK,OAAOU,CAAC,EACtB,KAAO,CAACsB,EAAKhC,CAAE,GACXA,EAAK,KAAK,OAAO,EAAEU,CAAC,EACxB,OAAO,MAAO,KAAK,YAAYA,EAAG,EAAK,CAC3C,CACJ,EAEAb,GAAQ,MAAQS,KC9sBhB,IAAA2B,GAAAC,EAAAC,IAAA,cAOA,IAAMC,GAAN,KAAkB,CACd,aAAc,CACV,KAAK,WAAa,CAAC,EAKnB,KAAK,WAAcC,GAAW,KAAK,WAAW,KAAKA,CAAM,EAMzD,KAAK,QAAWA,GAAW,CACvB,IAAIC,EAAM,EACNC,EAAO,KAAK,WAAW,OAC3B,KAAOD,EAAMC,GAAM,CACf,IAAMC,EAAOF,EAAMC,GAAS,EACxB,KAAK,WAAWC,CAAG,EAAIH,EACvBC,EAAME,EAAM,EAEZD,EAAOC,CACf,CACA,GAAI,KAAK,WAAWF,CAAG,IAAMD,EACzB,MAAO,CAAE,KAAMC,EAAM,EAAG,IAAK,CAAE,EACnC,GAAIA,IAAQ,EACR,MAAO,CAAE,KAAM,EAAG,IAAKD,CAAO,EAClC,IAAMI,EAAQ,KAAK,WAAWH,EAAM,CAAC,EACrC,MAAO,CAAE,KAAMA,EAAK,IAAKD,EAASI,EAAQ,CAAE,CAChD,CACJ,CACJ,EAEAN,GAAQ,YAAcC,KCxCtB,IAAAM,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAe,QAAQ,cAAc,EACrCC,GAAM,KACNC,GAAQ,KAEZ,SAASC,GAAcC,EAAMC,EAAM,CAC/B,QAASC,EAAI,EAAGA,EAAIF,EAAK,OAAQ,EAAEE,EAC/B,GAAIF,EAAKE,CAAC,EAAE,OAASD,EACjB,MAAO,GACf,MAAO,EACX,CACA,SAASE,GAAkBH,EAAM,CAC7B,QAASE,EAAI,EAAGA,EAAIF,EAAK,OAAQ,EAAEE,EAC/B,OAAQF,EAAKE,CAAC,EAAE,KAAM,CAClB,IAAK,QACL,IAAK,UACL,IAAK,UACD,MACJ,QACI,OAAOA,CACf,CAEJ,MAAO,EACX,CACA,SAASE,GAAYC,EAAO,CACxB,OAAQA,GAAO,KAAM,CACjB,IAAK,QACL,IAAK,SACL,IAAK,uBACL,IAAK,uBACL,IAAK,kBACD,MAAO,GACX,QACI,MAAO,EACf,CACJ,CACA,SAASC,GAAaC,EAAQ,CAC1B,OAAQA,EAAO,KAAM,CACjB,IAAK,WACD,OAAOA,EAAO,MAClB,IAAK,YAAa,CACd,IAAMC,EAAKD,EAAO,MAAMA,EAAO,MAAM,OAAS,CAAC,EAC/C,OAAOC,EAAG,KAAOA,EAAG,KACxB,CACA,IAAK,YACD,OAAOD,EAAO,MAAMA,EAAO,MAAM,OAAS,CAAC,EAAE,MAEjD,QACI,MAAO,CAAC,CAChB,CACJ,CAEA,SAASE,GAAsBC,EAAM,CACjC,GAAIA,EAAK,SAAW,EAChB,MAAO,CAAC,EACZ,IAAIR,EAAIQ,EAAK,OACbC,EAAM,KAAO,EAAET,GAAK,GAChB,OAAQQ,EAAKR,CAAC,EAAE,KAAM,CAClB,IAAK,YACL,IAAK,mBACL,IAAK,gBACL,IAAK,eACL,IAAK,UACD,MAAMS,CACd,CAEJ,KAAOD,EAAK,EAAER,CAAC,GAAG,OAAS,SAAS,CAGpC,OAAOQ,EAAK,OAAOR,EAAGQ,EAAK,MAAM,CACrC,CACA,SAASE,GAAgBC,EAAI,CACzB,GAAIA,EAAG,MAAM,OAAS,iBAClB,QAAWL,KAAMK,EAAG,MACZL,EAAG,KACH,CAACA,EAAG,OACJ,CAACT,GAAcS,EAAG,MAAO,kBAAkB,GAC3C,CAACT,GAAcS,EAAG,IAAK,eAAe,IAClCA,EAAG,MACHA,EAAG,MAAQA,EAAG,KAClB,OAAOA,EAAG,IACNJ,GAAYI,EAAG,KAAK,EAChBA,EAAG,MAAM,IACT,MAAM,UAAU,KAAK,MAAMA,EAAG,MAAM,IAAKA,EAAG,GAAG,EAE/CA,EAAG,MAAM,IAAMA,EAAG,IAGtB,MAAM,UAAU,KAAK,MAAMA,EAAG,MAAOA,EAAG,GAAG,EAC/C,OAAOA,EAAG,IAI1B,CA4BA,IAAMM,GAAN,KAAa,CAKT,YAAYC,EAAW,CAEnB,KAAK,UAAY,GAEjB,KAAK,SAAW,GAEhB,KAAK,OAAS,EAEd,KAAK,OAAS,EAEd,KAAK,UAAY,GAEjB,KAAK,MAAQ,CAAC,EAEd,KAAK,OAAS,GAEd,KAAK,KAAO,GAEZ,KAAK,MAAQ,IAAIjB,GAAM,MACvB,KAAK,UAAYiB,CACrB,CASA,CAAC,MAAMC,EAAQC,EAAa,GAAO,CAC3B,KAAK,WAAa,KAAK,SAAW,GAClC,KAAK,UAAU,CAAC,EACpB,QAAWC,KAAU,KAAK,MAAM,IAAIF,EAAQC,CAAU,EAClD,MAAO,KAAK,KAAKC,CAAM,EACtBD,IACD,MAAO,KAAK,IAAI,EACxB,CAIA,CAAC,KAAKD,EAAQ,CAIV,GAHA,KAAK,OAASA,EACVpB,GAAa,IAAI,YACjB,QAAQ,IAAI,IAAKC,GAAI,YAAYmB,CAAM,CAAC,EACxC,KAAK,SAAU,CACf,KAAK,SAAW,GAChB,MAAO,KAAK,KAAK,EACjB,KAAK,QAAUA,EAAO,OACtB,MACJ,CACA,IAAMf,EAAOJ,GAAI,UAAUmB,CAAM,EACjC,GAAKf,EAKA,GAAIA,IAAS,SACd,KAAK,UAAY,GACjB,KAAK,SAAW,GAChB,KAAK,KAAO,aAEX,CAGD,OAFA,KAAK,KAAOA,EACZ,MAAO,KAAK,KAAK,EACTA,EAAM,CACV,IAAK,UACD,KAAK,UAAY,GACjB,KAAK,OAAS,EACV,KAAK,WACL,KAAK,UAAU,KAAK,OAASe,EAAO,MAAM,EAC9C,MACJ,IAAK,QACG,KAAK,WAAaA,EAAO,CAAC,IAAM,MAChC,KAAK,QAAUA,EAAO,QAC1B,MACJ,IAAK,mBACL,IAAK,gBACL,IAAK,eACG,KAAK,YACL,KAAK,QAAUA,EAAO,QAC1B,MACJ,IAAK,WACL,IAAK,iBACD,OACJ,QACI,KAAK,UAAY,EACzB,CACA,KAAK,QAAUA,EAAO,MAC1B,KArCW,CACP,IAAMG,EAAU,qBAAqBH,CAAM,GAC3C,MAAO,KAAK,IAAI,CAAE,KAAM,QAAS,OAAQ,KAAK,OAAQ,QAAAG,EAAS,OAAAH,CAAO,CAAC,EACvE,KAAK,QAAUA,EAAO,MAC1B,CAkCJ,CAEA,CAAC,KAAM,CACH,KAAO,KAAK,MAAM,OAAS,GACvB,MAAO,KAAK,IAAI,CACxB,CACA,IAAI,aAAc,CAOd,MANW,CACP,KAAM,KAAK,KACX,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,OAAQ,KAAK,MACjB,CAEJ,CACA,CAAC,MAAO,CACJ,IAAMI,EAAM,KAAK,KAAK,CAAC,EACvB,GAAI,KAAK,OAAS,YAAc,CAACA,GAAOA,EAAI,OAAS,WAAY,CAC7D,KAAO,KAAK,MAAM,OAAS,GACvB,MAAO,KAAK,IAAI,EACpB,KAAK,MAAM,KAAK,CACZ,KAAM,UACN,OAAQ,KAAK,OACb,OAAQ,KAAK,MACjB,CAAC,EACD,MACJ,CACA,GAAI,CAACA,EACD,OAAO,MAAO,KAAK,OAAO,EAC9B,OAAQA,EAAI,KAAM,CACd,IAAK,WACD,OAAO,MAAO,KAAK,SAASA,CAAG,EACnC,IAAK,QACL,IAAK,SACL,IAAK,uBACL,IAAK,uBACD,OAAO,MAAO,KAAK,OAAOA,CAAG,EACjC,IAAK,eACD,OAAO,MAAO,KAAK,YAAYA,CAAG,EACtC,IAAK,YACD,OAAO,MAAO,KAAK,SAASA,CAAG,EACnC,IAAK,YACD,OAAO,MAAO,KAAK,cAAcA,CAAG,EACxC,IAAK,kBACD,OAAO,MAAO,KAAK,eAAeA,CAAG,EACzC,IAAK,UACD,OAAO,MAAO,KAAK,YAAYA,CAAG,CAC1C,CAEA,MAAO,KAAK,IAAI,CACpB,CACA,KAAKC,EAAG,CACJ,OAAO,KAAK,MAAM,KAAK,MAAM,OAASA,CAAC,CAC3C,CACA,CAAC,IAAIC,EAAO,CACR,IAAMjB,EAAQiB,GAAS,KAAK,MAAM,IAAI,EAEtC,GAAI,CAACjB,EAED,KAAM,CAAE,KAAM,QAAS,OAAQ,KAAK,OAAQ,OAAQ,GAAI,QADxC,6BACgD,UAE3D,KAAK,MAAM,SAAW,EAC3B,MAAMA,MAEL,CACD,IAAMe,EAAM,KAAK,KAAK,CAAC,EAWvB,OAVIf,EAAM,OAAS,eAEfA,EAAM,OAAS,WAAYe,EAAMA,EAAI,OAAS,EAEzCf,EAAM,OAAS,mBAAqBe,EAAI,OAAS,aAEtDf,EAAM,OAAS,GAEfA,EAAM,OAAS,mBACfO,GAAgBP,CAAK,EACjBe,EAAI,KAAM,CACd,IAAK,WACDA,EAAI,MAAQf,EACZ,MACJ,IAAK,eACDe,EAAI,MAAM,KAAKf,CAAK,EACpB,MACJ,IAAK,YAAa,CACd,IAAMG,EAAKY,EAAI,MAAMA,EAAI,MAAM,OAAS,CAAC,EACzC,GAAIZ,EAAG,MAAO,CACVY,EAAI,MAAM,KAAK,CAAE,MAAO,CAAC,EAAG,IAAKf,EAAO,IAAK,CAAC,CAAE,CAAC,EACjD,KAAK,UAAY,GACjB,MACJ,SACSG,EAAG,IACRA,EAAG,MAAQH,MAEV,CACD,OAAO,OAAOG,EAAI,CAAE,IAAKH,EAAO,IAAK,CAAC,CAAE,CAAC,EACzC,KAAK,UAAY,CAACG,EAAG,YACrB,MACJ,CACA,KACJ,CACA,IAAK,YAAa,CACd,IAAMA,EAAKY,EAAI,MAAMA,EAAI,MAAM,OAAS,CAAC,EACrCZ,EAAG,MACHY,EAAI,MAAM,KAAK,CAAE,MAAO,CAAC,EAAG,MAAOf,CAAM,CAAC,EAE1CG,EAAG,MAAQH,EACf,KACJ,CACA,IAAK,kBAAmB,CACpB,IAAMG,EAAKY,EAAI,MAAMA,EAAI,MAAM,OAAS,CAAC,EACrC,CAACZ,GAAMA,EAAG,MACVY,EAAI,MAAM,KAAK,CAAE,MAAO,CAAC,EAAG,IAAKf,EAAO,IAAK,CAAC,CAAE,CAAC,EAC5CG,EAAG,IACRA,EAAG,MAAQH,EAEX,OAAO,OAAOG,EAAI,CAAE,IAAKH,EAAO,IAAK,CAAC,CAAE,CAAC,EAC7C,MACJ,CAEA,QACI,MAAO,KAAK,IAAI,EAChB,MAAO,KAAK,IAAIA,CAAK,CAC7B,CACA,IAAKe,EAAI,OAAS,YACdA,EAAI,OAAS,aACbA,EAAI,OAAS,eACZf,EAAM,OAAS,aAAeA,EAAM,OAAS,aAAc,CAC5D,IAAMkB,EAAOlB,EAAM,MAAMA,EAAM,MAAM,OAAS,CAAC,EAC3CkB,GACA,CAACA,EAAK,KACN,CAACA,EAAK,OACNA,EAAK,MAAM,OAAS,GACpBpB,GAAkBoB,EAAK,KAAK,IAAM,KACjClB,EAAM,SAAW,GACdkB,EAAK,MAAM,MAAMC,GAAMA,EAAG,OAAS,WAAaA,EAAG,OAASnB,EAAM,MAAM,KACxEe,EAAI,OAAS,WACbA,EAAI,IAAMG,EAAK,MAEfH,EAAI,MAAM,KAAK,CAAE,MAAOG,EAAK,KAAM,CAAC,EACxClB,EAAM,MAAM,OAAO,GAAI,CAAC,EAEhC,CACJ,CACJ,CACA,CAAC,QAAS,CACN,OAAQ,KAAK,KAAM,CACf,IAAK,iBACD,KAAM,CAAE,KAAM,YAAa,OAAQ,KAAK,OAAQ,OAAQ,KAAK,MAAO,EACpE,OACJ,IAAK,kBACL,IAAK,QACL,IAAK,UACL,IAAK,UACD,MAAM,KAAK,YACX,OACJ,IAAK,WACL,IAAK,YAAa,CACd,IAAMoB,EAAM,CACR,KAAM,WACN,OAAQ,KAAK,OACb,MAAO,CAAC,CACZ,EACI,KAAK,OAAS,aACdA,EAAI,MAAM,KAAK,KAAK,WAAW,EACnC,KAAK,MAAM,KAAKA,CAAG,EACnB,MACJ,CACJ,CACA,KAAM,CACF,KAAM,QACN,OAAQ,KAAK,OACb,QAAS,cAAc,KAAK,IAAI,wBAChC,OAAQ,KAAK,MACjB,CACJ,CACA,CAAC,SAASA,EAAK,CACX,GAAIA,EAAI,MACJ,OAAO,MAAO,KAAK,QAAQA,CAAG,EAClC,OAAQ,KAAK,KAAM,CACf,IAAK,YAAa,CACVtB,GAAkBsB,EAAI,KAAK,IAAM,IACjC,MAAO,KAAK,IAAI,EAChB,MAAO,KAAK,KAAK,GAGjBA,EAAI,MAAM,KAAK,KAAK,WAAW,EACnC,MACJ,CACA,IAAK,SACL,IAAK,MACL,IAAK,QACL,IAAK,UACL,IAAK,UACDA,EAAI,MAAM,KAAK,KAAK,WAAW,EAC/B,MACR,CACA,IAAMC,EAAK,KAAK,gBAAgBD,CAAG,EAC/BC,EACA,KAAK,MAAM,KAAKA,CAAE,EAElB,KAAM,CACF,KAAM,QACN,OAAQ,KAAK,OACb,QAAS,cAAc,KAAK,IAAI,0BAChC,OAAQ,KAAK,MACjB,CAER,CACA,CAAC,OAAOC,EAAQ,CACZ,GAAI,KAAK,OAAS,gBAAiB,CAC/B,IAAMjB,EAAOJ,GAAa,KAAK,KAAK,CAAC,CAAC,EAChCsB,EAAQnB,GAAsBC,CAAI,EACpCmB,EACAF,EAAO,KACPE,EAAMF,EAAO,IACbE,EAAI,KAAK,KAAK,WAAW,EACzB,OAAOF,EAAO,KAGdE,EAAM,CAAC,KAAK,WAAW,EAC3B,IAAMC,EAAM,CACR,KAAM,YACN,OAAQH,EAAO,OACf,OAAQA,EAAO,OACf,MAAO,CAAC,CAAE,MAAAC,EAAO,IAAKD,EAAQ,IAAAE,CAAI,CAAC,CACvC,EACA,KAAK,UAAY,GACjB,KAAK,MAAM,KAAK,MAAM,OAAS,CAAC,EAAIC,CACxC,MAEI,MAAO,KAAK,QAAQH,CAAM,CAClC,CACA,CAAC,YAAYA,EAAQ,CACjB,OAAQ,KAAK,KAAM,CACf,IAAK,QACL,IAAK,UACL,IAAK,UACDA,EAAO,MAAM,KAAK,KAAK,WAAW,EAClC,OACJ,IAAK,SAKD,GAJAA,EAAO,OAAS,KAAK,OAErB,KAAK,UAAY,GACjB,KAAK,OAAS,EACV,KAAK,UAAW,CAChB,IAAII,EAAK,KAAK,OAAO,QAAQ;AAAA,CAAI,EAAI,EACrC,KAAOA,IAAO,GACV,KAAK,UAAU,KAAK,OAASA,CAAE,EAC/BA,EAAK,KAAK,OAAO,QAAQ;AAAA,EAAMA,CAAE,EAAI,CAE7C,CACA,MAAO,KAAK,IAAI,EAChB,MAEJ,QACI,MAAO,KAAK,IAAI,EAChB,MAAO,KAAK,KAAK,CACzB,CACJ,CACA,CAAC,SAASD,EAAK,CACX,IAAMtB,EAAKsB,EAAI,MAAMA,EAAI,MAAM,OAAS,CAAC,EAEzC,OAAQ,KAAK,KAAM,CACf,IAAK,UAED,GADA,KAAK,UAAY,GACbtB,EAAG,MAAO,CACV,IAAMwB,EAAM,QAASxB,EAAG,MAAQA,EAAG,MAAM,IAAM,QAClC,MAAM,QAAQwB,CAAG,EAAIA,EAAIA,EAAI,OAAS,CAAC,EAAI,SAC9C,OAAS,UACfA,GAAK,KAAK,KAAK,WAAW,EAE1BF,EAAI,MAAM,KAAK,CAAE,MAAO,CAAC,KAAK,WAAW,CAAE,CAAC,CACpD,MACStB,EAAG,IACRA,EAAG,IAAI,KAAK,KAAK,WAAW,EAG5BA,EAAG,MAAM,KAAK,KAAK,WAAW,EAElC,OACJ,IAAK,QACL,IAAK,UACD,GAAIA,EAAG,MACHsB,EAAI,MAAM,KAAK,CAAE,MAAO,CAAC,KAAK,WAAW,CAAE,CAAC,UAEvCtB,EAAG,IACRA,EAAG,IAAI,KAAK,KAAK,WAAW,MAE3B,CACD,GAAI,KAAK,kBAAkBA,EAAG,MAAOsB,EAAI,MAAM,EAAG,CAE9C,IAAME,EADOF,EAAI,MAAMA,EAAI,MAAM,OAAS,CAAC,GACzB,OAAO,IACzB,GAAI,MAAM,QAAQE,CAAG,EAAG,CACpB,MAAM,UAAU,KAAK,MAAMA,EAAKxB,EAAG,KAAK,EACxCwB,EAAI,KAAK,KAAK,WAAW,EACzBF,EAAI,MAAM,IAAI,EACd,MACJ,CACJ,CACAtB,EAAG,MAAM,KAAK,KAAK,WAAW,CAClC,CACA,MACR,CACA,GAAI,KAAK,QAAUsB,EAAI,OAAQ,CAC3B,IAAMG,EAAc,CAAC,KAAK,WAAa,KAAK,SAAWH,EAAI,OACrDI,EAAaD,IACdzB,EAAG,KAAOA,EAAG,cACd,KAAK,OAAS,eAEdoB,EAAQ,CAAC,EACb,GAAIM,GAAc1B,EAAG,KAAO,CAACA,EAAG,MAAO,CACnC,IAAMuB,EAAK,CAAC,EACZ,QAAS7B,EAAI,EAAGA,EAAIM,EAAG,IAAI,OAAQ,EAAEN,EAAG,CACpC,IAAMsB,EAAKhB,EAAG,IAAIN,CAAC,EACnB,OAAQsB,EAAG,KAAM,CACb,IAAK,UACDO,EAAG,KAAK7B,CAAC,EACT,MACJ,IAAK,QACD,MACJ,IAAK,UACGsB,EAAG,OAASM,EAAI,SAChBC,EAAG,OAAS,GAChB,MACJ,QACIA,EAAG,OAAS,CACpB,CACJ,CACIA,EAAG,QAAU,IACbH,EAAQpB,EAAG,IAAI,OAAOuB,EAAG,CAAC,CAAC,EACnC,CACA,OAAQ,KAAK,KAAM,CACf,IAAK,SACL,IAAK,MACGG,GAAc1B,EAAG,OACjBoB,EAAM,KAAK,KAAK,WAAW,EAC3BE,EAAI,MAAM,KAAK,CAAE,MAAAF,CAAM,CAAC,EACxB,KAAK,UAAY,IAEZpB,EAAG,IACRA,EAAG,IAAI,KAAK,KAAK,WAAW,EAG5BA,EAAG,MAAM,KAAK,KAAK,WAAW,EAElC,OACJ,IAAK,mBACG,CAACA,EAAG,KAAO,CAACA,EAAG,aACfA,EAAG,MAAM,KAAK,KAAK,WAAW,EAC9BA,EAAG,YAAc,IAEZ0B,GAAc1B,EAAG,OACtBoB,EAAM,KAAK,KAAK,WAAW,EAC3BE,EAAI,MAAM,KAAK,CAAE,MAAAF,EAAO,YAAa,EAAK,CAAC,GAG3C,KAAK,MAAM,KAAK,CACZ,KAAM,YACN,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,CAAC,CAAE,MAAO,CAAC,KAAK,WAAW,EAAG,YAAa,EAAK,CAAC,CAC5D,CAAC,EAEL,KAAK,UAAY,GACjB,OACJ,IAAK,gBACD,GAAIpB,EAAG,YACH,GAAKA,EAAG,IAcH,GAAIA,EAAG,MACRsB,EAAI,MAAM,KAAK,CAAE,MAAO,CAAC,EAAG,IAAK,KAAM,IAAK,CAAC,KAAK,WAAW,CAAE,CAAC,UAE3D/B,GAAcS,EAAG,IAAK,eAAe,EAC1C,KAAK,MAAM,KAAK,CACZ,KAAM,YACN,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,CAAC,CAAE,MAAAoB,EAAO,IAAK,KAAM,IAAK,CAAC,KAAK,WAAW,CAAE,CAAC,CACzD,CAAC,UAEIxB,GAAYI,EAAG,GAAG,GACvB,CAACT,GAAcS,EAAG,IAAK,SAAS,EAAG,CACnC,IAAMoB,EAAQnB,GAAsBD,EAAG,KAAK,EACtC2B,EAAM3B,EAAG,IACTqB,EAAMrB,EAAG,IACfqB,EAAI,KAAK,KAAK,WAAW,EAEzB,OAAOrB,EAAG,IAEV,OAAOA,EAAG,IACV,KAAK,MAAM,KAAK,CACZ,KAAM,YACN,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,CAAC,CAAE,MAAAoB,EAAO,IAAAO,EAAK,IAAAN,CAAI,CAAC,CAC/B,CAAC,CACL,MACSD,EAAM,OAAS,EAEpBpB,EAAG,IAAMA,EAAG,IAAI,OAAOoB,EAAO,KAAK,WAAW,EAG9CpB,EAAG,IAAI,KAAK,KAAK,WAAW,UA9CxBT,GAAcS,EAAG,MAAO,SAAS,EACjC,OAAO,OAAOA,EAAI,CAAE,IAAK,KAAM,IAAK,CAAC,KAAK,WAAW,CAAE,CAAC,MAEvD,CACD,IAAMoB,EAAQnB,GAAsBD,EAAG,KAAK,EAC5C,KAAK,MAAM,KAAK,CACZ,KAAM,YACN,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,CAAC,CAAE,MAAAoB,EAAO,IAAK,KAAM,IAAK,CAAC,KAAK,WAAW,CAAE,CAAC,CACzD,CAAC,CACL,MAuCCpB,EAAG,IAGCA,EAAG,OAAS0B,EACjBJ,EAAI,MAAM,KAAK,CAAE,MAAAF,EAAO,IAAK,KAAM,IAAK,CAAC,KAAK,WAAW,CAAE,CAAC,EAEvD7B,GAAcS,EAAG,IAAK,eAAe,EAC1C,KAAK,MAAM,KAAK,CACZ,KAAM,YACN,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,CAAC,CAAE,MAAO,CAAC,EAAG,IAAK,KAAM,IAAK,CAAC,KAAK,WAAW,CAAE,CAAC,CAC7D,CAAC,EAGDA,EAAG,IAAI,KAAK,KAAK,WAAW,EAd5B,OAAO,OAAOA,EAAI,CAAE,IAAK,KAAM,IAAK,CAAC,KAAK,WAAW,CAAE,CAAC,EAiBhE,KAAK,UAAY,GACjB,OACJ,IAAK,QACL,IAAK,SACL,IAAK,uBACL,IAAK,uBAAwB,CACzB,IAAM4B,EAAK,KAAK,WAAW,KAAK,IAAI,EAChCF,GAAc1B,EAAG,OACjBsB,EAAI,MAAM,KAAK,CAAE,MAAAF,EAAO,IAAKQ,EAAI,IAAK,CAAC,CAAE,CAAC,EAC1C,KAAK,UAAY,IAEZ5B,EAAG,IACR,KAAK,MAAM,KAAK4B,CAAE,GAGlB,OAAO,OAAO5B,EAAI,CAAE,IAAK4B,EAAI,IAAK,CAAC,CAAE,CAAC,EACtC,KAAK,UAAY,IAErB,MACJ,CACA,QAAS,CACL,IAAMV,EAAK,KAAK,gBAAgBI,CAAG,EACnC,GAAIJ,EAAI,CACAO,GAAeP,EAAG,OAAS,aAC3BI,EAAI,MAAM,KAAK,CAAE,MAAAF,CAAM,CAAC,EAE5B,KAAK,MAAM,KAAKF,CAAE,EAClB,MACJ,CACJ,CACJ,CACJ,CACA,MAAO,KAAK,IAAI,EAChB,MAAO,KAAK,KAAK,CACrB,CACA,CAAC,cAAcW,EAAK,CAChB,IAAM7B,EAAK6B,EAAI,MAAMA,EAAI,MAAM,OAAS,CAAC,EACzC,OAAQ,KAAK,KAAM,CACf,IAAK,UACD,GAAI7B,EAAG,MAAO,CACV,IAAMwB,EAAM,QAASxB,EAAG,MAAQA,EAAG,MAAM,IAAM,QAClC,MAAM,QAAQwB,CAAG,EAAIA,EAAIA,EAAI,OAAS,CAAC,EAAI,SAC9C,OAAS,UACfA,GAAK,KAAK,KAAK,WAAW,EAE1BK,EAAI,MAAM,KAAK,CAAE,MAAO,CAAC,KAAK,WAAW,CAAE,CAAC,CACpD,MAEI7B,EAAG,MAAM,KAAK,KAAK,WAAW,EAClC,OACJ,IAAK,QACL,IAAK,UACD,GAAIA,EAAG,MACH6B,EAAI,MAAM,KAAK,CAAE,MAAO,CAAC,KAAK,WAAW,CAAE,CAAC,MAC3C,CACD,GAAI,KAAK,kBAAkB7B,EAAG,MAAO6B,EAAI,MAAM,EAAG,CAE9C,IAAML,EADOK,EAAI,MAAMA,EAAI,MAAM,OAAS,CAAC,GACzB,OAAO,IACzB,GAAI,MAAM,QAAQL,CAAG,EAAG,CACpB,MAAM,UAAU,KAAK,MAAMA,EAAKxB,EAAG,KAAK,EACxCwB,EAAI,KAAK,KAAK,WAAW,EACzBK,EAAI,MAAM,IAAI,EACd,MACJ,CACJ,CACA7B,EAAG,MAAM,KAAK,KAAK,WAAW,CAClC,CACA,OACJ,IAAK,SACL,IAAK,MACD,GAAIA,EAAG,OAAS,KAAK,QAAU6B,EAAI,OAC/B,MACJ7B,EAAG,MAAM,KAAK,KAAK,WAAW,EAC9B,OACJ,IAAK,eACD,GAAI,KAAK,SAAW6B,EAAI,OACpB,MACA7B,EAAG,OAAST,GAAcS,EAAG,MAAO,cAAc,EAClD6B,EAAI,MAAM,KAAK,CAAE,MAAO,CAAC,KAAK,WAAW,CAAE,CAAC,EAE5C7B,EAAG,MAAM,KAAK,KAAK,WAAW,EAClC,MACR,CACA,GAAI,KAAK,OAAS6B,EAAI,OAAQ,CAC1B,IAAMX,EAAK,KAAK,gBAAgBW,CAAG,EACnC,GAAIX,EAAI,CACJ,KAAK,MAAM,KAAKA,CAAE,EAClB,MACJ,CACJ,CACA,MAAO,KAAK,IAAI,EAChB,MAAO,KAAK,KAAK,CACrB,CACA,CAAC,eAAeb,EAAI,CAChB,IAAML,EAAKK,EAAG,MAAMA,EAAG,MAAM,OAAS,CAAC,EACvC,GAAI,KAAK,OAAS,iBAAkB,CAChC,IAAIO,EACJ,GACI,MAAO,KAAK,IAAI,EAChBA,EAAM,KAAK,KAAK,CAAC,QACZA,GAAOA,EAAI,OAAS,kBACjC,SACSP,EAAG,IAAI,SAAW,EAAG,CAC1B,OAAQ,KAAK,KAAM,CACf,IAAK,QACL,IAAK,mBACG,CAACL,GAAMA,EAAG,IACVK,EAAG,MAAM,KAAK,CAAE,MAAO,CAAC,KAAK,WAAW,CAAE,CAAC,EAE3CL,EAAG,MAAM,KAAK,KAAK,WAAW,EAClC,OACJ,IAAK,gBACG,CAACA,GAAMA,EAAG,MACVK,EAAG,MAAM,KAAK,CAAE,MAAO,CAAC,EAAG,IAAK,KAAM,IAAK,CAAC,KAAK,WAAW,CAAE,CAAC,EAC1DL,EAAG,IACRA,EAAG,IAAI,KAAK,KAAK,WAAW,EAE5B,OAAO,OAAOA,EAAI,CAAE,IAAK,KAAM,IAAK,CAAC,KAAK,WAAW,CAAE,CAAC,EAC5D,OACJ,IAAK,QACL,IAAK,UACL,IAAK,UACL,IAAK,SACL,IAAK,MACG,CAACA,GAAMA,EAAG,MACVK,EAAG,MAAM,KAAK,CAAE,MAAO,CAAC,KAAK,WAAW,CAAE,CAAC,EACtCL,EAAG,IACRA,EAAG,IAAI,KAAK,KAAK,WAAW,EAE5BA,EAAG,MAAM,KAAK,KAAK,WAAW,EAClC,OACJ,IAAK,QACL,IAAK,SACL,IAAK,uBACL,IAAK,uBAAwB,CACzB,IAAM4B,EAAK,KAAK,WAAW,KAAK,IAAI,EAChC,CAAC5B,GAAMA,EAAG,MACVK,EAAG,MAAM,KAAK,CAAE,MAAO,CAAC,EAAG,IAAKuB,EAAI,IAAK,CAAC,CAAE,CAAC,EACxC5B,EAAG,IACR,KAAK,MAAM,KAAK4B,CAAE,EAElB,OAAO,OAAO5B,EAAI,CAAE,IAAK4B,EAAI,IAAK,CAAC,CAAE,CAAC,EAC1C,MACJ,CACA,IAAK,eACL,IAAK,eACDvB,EAAG,IAAI,KAAK,KAAK,WAAW,EAC5B,MACR,CACA,IAAMa,EAAK,KAAK,gBAAgBb,CAAE,EAE9Ba,EACA,KAAK,MAAM,KAAKA,CAAE,GAElB,MAAO,KAAK,IAAI,EAChB,MAAO,KAAK,KAAK,EAEzB,KACK,CACD,IAAMnB,EAAS,KAAK,KAAK,CAAC,EAC1B,GAAIA,EAAO,OAAS,cACd,KAAK,OAAS,iBAAmBA,EAAO,SAAWM,EAAG,QACnD,KAAK,OAAS,WACX,CAACN,EAAO,MAAMA,EAAO,MAAM,OAAS,CAAC,EAAE,KAC/C,MAAO,KAAK,IAAI,EAChB,MAAO,KAAK,KAAK,UAEZ,KAAK,OAAS,iBACnBA,EAAO,OAAS,kBAAmB,CACnC,IAAMG,EAAOJ,GAAaC,CAAM,EAC1BqB,EAAQnB,GAAsBC,CAAI,EACxCE,GAAgBC,CAAE,EAClB,IAAMgB,EAAMhB,EAAG,IAAI,OAAO,EAAGA,EAAG,IAAI,MAAM,EAC1CgB,EAAI,KAAK,KAAK,WAAW,EACzB,IAAMC,EAAM,CACR,KAAM,YACN,OAAQjB,EAAG,OACX,OAAQA,EAAG,OACX,MAAO,CAAC,CAAE,MAAAe,EAAO,IAAKf,EAAI,IAAAgB,CAAI,CAAC,CACnC,EACA,KAAK,UAAY,GACjB,KAAK,MAAM,KAAK,MAAM,OAAS,CAAC,EAAIC,CACxC,MAEI,MAAO,KAAK,QAAQjB,CAAE,CAE9B,CACJ,CACA,WAAWZ,EAAM,CACb,GAAI,KAAK,UAAW,CAChB,IAAI8B,EAAK,KAAK,OAAO,QAAQ;AAAA,CAAI,EAAI,EACrC,KAAOA,IAAO,GACV,KAAK,UAAU,KAAK,OAASA,CAAE,EAC/BA,EAAK,KAAK,OAAO,QAAQ;AAAA,EAAMA,CAAE,EAAI,CAE7C,CACA,MAAO,CACH,KAAA9B,EACA,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,OAAQ,KAAK,MACjB,CACJ,CACA,gBAAgBM,EAAQ,CACpB,OAAQ,KAAK,KAAM,CACf,IAAK,QACL,IAAK,SACL,IAAK,uBACL,IAAK,uBACD,OAAO,KAAK,WAAW,KAAK,IAAI,EACpC,IAAK,sBACD,MAAO,CACH,KAAM,eACN,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,CAAC,KAAK,WAAW,EACxB,OAAQ,EACZ,EACJ,IAAK,iBACL,IAAK,iBACD,MAAO,CACH,KAAM,kBACN,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,YACZ,MAAO,CAAC,EACR,IAAK,CAAC,CACV,EACJ,IAAK,eACD,MAAO,CACH,KAAM,YACN,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,CAAC,CAAE,MAAO,CAAC,KAAK,WAAW,CAAE,CAAC,CACzC,EACJ,IAAK,mBAAoB,CACrB,KAAK,UAAY,GACjB,IAAMG,EAAOJ,GAAaC,CAAM,EAC1BqB,EAAQnB,GAAsBC,CAAI,EACxC,OAAAkB,EAAM,KAAK,KAAK,WAAW,EACpB,CACH,KAAM,YACN,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,CAAC,CAAE,MAAAA,EAAO,YAAa,EAAK,CAAC,CACxC,CACJ,CACA,IAAK,gBAAiB,CAClB,KAAK,UAAY,GACjB,IAAMlB,EAAOJ,GAAaC,CAAM,EAC1BqB,EAAQnB,GAAsBC,CAAI,EACxC,MAAO,CACH,KAAM,YACN,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,CAAC,CAAE,MAAAkB,EAAO,IAAK,KAAM,IAAK,CAAC,KAAK,WAAW,CAAE,CAAC,CACzD,CACJ,CACJ,CACA,OAAO,IACX,CACA,kBAAkBA,EAAOU,EAAQ,CAG7B,OAFI,KAAK,OAAS,WAEd,KAAK,QAAUA,EACR,GACJV,EAAM,MAAMJ,GAAMA,EAAG,OAAS,WAAaA,EAAG,OAAS,OAAO,CACzE,CACA,CAAC,YAAYe,EAAQ,CACb,KAAK,OAAS,aACVA,EAAO,IACPA,EAAO,IAAI,KAAK,KAAK,WAAW,EAEhCA,EAAO,IAAM,CAAC,KAAK,WAAW,EAC9B,KAAK,OAAS,YACd,MAAO,KAAK,IAAI,GAE5B,CACA,CAAC,QAAQlC,EAAO,CACZ,OAAQ,KAAK,KAAM,CACf,IAAK,QACL,IAAK,YACL,IAAK,UACL,IAAK,eACL,IAAK,eACL,IAAK,gBACD,MAAO,KAAK,IAAI,EAChB,MAAO,KAAK,KAAK,EACjB,MACJ,IAAK,UACD,KAAK,UAAY,GAErB,IAAK,QACL,IAAK,UACL,QAEQA,EAAM,IACNA,EAAM,IAAI,KAAK,KAAK,WAAW,EAE/BA,EAAM,IAAM,CAAC,KAAK,WAAW,EAC7B,KAAK,OAAS,YACd,MAAO,KAAK,IAAI,EAC5B,CACJ,CACJ,EAEAV,GAAQ,OAASmB,KC97BjB,IAAA0B,GAAAC,EAAAC,IAAA,cAEA,IAAIC,GAAW,KACXC,GAAW,KACXC,GAAS,KACTC,GAAM,KACNC,GAAW,IACXC,GAAc,KACdC,GAAS,KAEb,SAASC,GAAaC,EAAS,CAC3B,IAAMC,EAAeD,EAAQ,eAAiB,GAE9C,MAAO,CAAE,YADaA,EAAQ,aAAgBC,GAAgB,IAAIJ,GAAY,aAAkB,KAC3D,aAAAI,CAAa,CACtD,CAUA,SAASC,GAAkBC,EAAQH,EAAU,CAAC,EAAG,CAC7C,GAAM,CAAE,YAAAH,EAAa,aAAAI,CAAa,EAAIF,GAAaC,CAAO,EACpDI,EAAW,IAAIN,GAAO,OAAOD,GAAa,UAAU,EACpDQ,EAAa,IAAIb,GAAS,SAASQ,CAAO,EAC1CM,EAAO,MAAM,KAAKD,EAAW,QAAQD,EAAS,MAAMD,CAAM,CAAC,CAAC,EAClE,GAAIF,GAAgBJ,EAChB,QAAWU,KAAOD,EACdC,EAAI,OAAO,QAAQb,GAAO,cAAcS,EAAQN,CAAW,CAAC,EAC5DU,EAAI,SAAS,QAAQb,GAAO,cAAcS,EAAQN,CAAW,CAAC,EAEtE,OAAIS,EAAK,OAAS,EACPA,EACJ,OAAO,OAAO,CAAC,EAAG,CAAE,MAAO,EAAK,EAAGD,EAAW,WAAW,CAAC,CACrE,CAEA,SAASG,GAAcL,EAAQH,EAAU,CAAC,EAAG,CACzC,GAAM,CAAE,YAAAH,EAAa,aAAAI,CAAa,EAAIF,GAAaC,CAAO,EACpDI,EAAW,IAAIN,GAAO,OAAOD,GAAa,UAAU,EACpDQ,EAAa,IAAIb,GAAS,SAASQ,CAAO,EAE5CO,EAAM,KACV,QAAWE,KAAQJ,EAAW,QAAQD,EAAS,MAAMD,CAAM,EAAG,GAAMA,EAAO,MAAM,EAC7E,GAAI,CAACI,EACDA,EAAME,UACDF,EAAI,QAAQ,WAAa,SAAU,CACxCA,EAAI,OAAO,KAAK,IAAIb,GAAO,eAAee,EAAK,MAAM,MAAM,EAAG,CAAC,EAAG,gBAAiB,yEAAyE,CAAC,EAC7J,KACJ,CAEJ,OAAIR,GAAgBJ,IAChBU,EAAI,OAAO,QAAQb,GAAO,cAAcS,EAAQN,CAAW,CAAC,EAC5DU,EAAI,SAAS,QAAQb,GAAO,cAAcS,EAAQN,CAAW,CAAC,GAE3DU,CACX,CACA,SAASG,GAAMC,EAAKC,EAASZ,EAAS,CAClC,IAAIa,EACA,OAAOD,GAAY,WACnBC,EAAWD,EAENZ,IAAY,QAAaY,GAAW,OAAOA,GAAY,WAC5DZ,EAAUY,GAEd,IAAML,EAAMC,GAAcG,EAAKX,CAAO,EACtC,GAAI,CAACO,EACD,OAAO,KAEX,GADAA,EAAI,SAAS,QAAQO,GAAWnB,GAAI,KAAKY,EAAI,QAAQ,SAAUO,CAAO,CAAC,EACnEP,EAAI,OAAO,OAAS,EAAG,CACvB,GAAIA,EAAI,QAAQ,WAAa,SACzB,MAAMA,EAAI,OAAO,CAAC,EAElBA,EAAI,OAAS,CAAC,CACtB,CACA,OAAOA,EAAI,KAAK,OAAO,OAAO,CAAE,QAASM,CAAS,EAAGb,CAAO,CAAC,CACjE,CACA,SAASe,GAAUC,EAAOC,EAAUjB,EAAS,CACzC,IAAIkB,EAAY,KAShB,GARI,OAAOD,GAAa,YAAc,MAAM,QAAQA,CAAQ,EACxDC,EAAYD,EAEPjB,IAAY,QAAaiB,IAC9BjB,EAAUiB,GAEV,OAAOjB,GAAY,WACnBA,EAAUA,EAAQ,QAClB,OAAOA,GAAY,SAAU,CAC7B,IAAMmB,EAAS,KAAK,MAAMnB,CAAO,EACjCA,EAAUmB,EAAS,EAAI,OAAYA,EAAS,EAAI,CAAE,OAAQ,CAAE,EAAI,CAAE,OAAAA,CAAO,CAC7E,CACA,GAAIH,IAAU,OAAW,CACrB,GAAM,CAAE,cAAAI,CAAc,EAAIpB,GAAWiB,GAAY,CAAC,EAClD,GAAI,CAACG,EACD,MACR,CACA,OAAIxB,GAAS,WAAWoB,CAAK,GAAK,CAACE,EACxBF,EAAM,SAAShB,CAAO,EAC1B,IAAIP,GAAS,SAASuB,EAAOE,EAAWlB,CAAO,EAAE,SAASA,CAAO,CAC5E,CAEAT,GAAQ,MAAQmB,GAChBnB,GAAQ,kBAAoBW,GAC5BX,GAAQ,cAAgBiB,GACxBjB,GAAQ,UAAYwB,KC1GpB,IAAAM,GAAAC,EAAAC,GAAA,cAEA,IAAIC,GAAW,KACXC,GAAW,KACXC,GAAS,KACTC,GAAS,KACTC,GAAQ,KACRC,EAAW,IACXC,GAAO,IACPC,GAAS,IACTC,GAAU,IACVC,GAAU,IACVC,GAAM,KACNC,GAAQ,KACRC,GAAc,KACdC,GAAS,KACTC,GAAY,KACZC,GAAQ,KAIZhB,EAAQ,SAAWC,GAAS,SAC5BD,EAAQ,SAAWE,GAAS,SAC5BF,EAAQ,OAASG,GAAO,OACxBH,EAAQ,UAAYI,GAAO,UAC3BJ,EAAQ,eAAiBI,GAAO,eAChCJ,EAAQ,YAAcI,GAAO,YAC7BJ,EAAQ,MAAQK,GAAM,MACtBL,EAAQ,QAAUM,EAAS,QAC3BN,EAAQ,aAAeM,EAAS,aAChCN,EAAQ,WAAaM,EAAS,WAC9BN,EAAQ,MAAQM,EAAS,MACzBN,EAAQ,OAASM,EAAS,OAC1BN,EAAQ,OAASM,EAAS,OAC1BN,EAAQ,SAAWM,EAAS,SAC5BN,EAAQ,MAAQM,EAAS,MACzBN,EAAQ,KAAOO,GAAK,KACpBP,EAAQ,OAASQ,GAAO,OACxBR,EAAQ,QAAUS,GAAQ,QAC1BT,EAAQ,QAAUU,GAAQ,QAC1BV,EAAQ,IAAMW,GACdX,EAAQ,MAAQY,GAAM,MACtBZ,EAAQ,YAAca,GAAY,YAClCb,EAAQ,OAASc,GAAO,OACxBd,EAAQ,MAAQe,GAAU,MAC1Bf,EAAQ,kBAAoBe,GAAU,kBACtCf,EAAQ,cAAgBe,GAAU,cAClCf,EAAQ,UAAYe,GAAU,UAC9Bf,EAAQ,MAAQgB,GAAM,MACtBhB,EAAQ,WAAagB,GAAM,aCjD3B,IAAAC,GAAA,GAAAC,GAAAD,GAAA,aAAAE,KAAA,eAAAC,GAAAH,IAAA,IAAAI,EAA4E,wBCOrE,IAAMC,GAAqB,KACrBC,GAAqBD,IAAsB,EAC3CE,GAAqBD,IAAsB,ECHxD,IAAAE,GAAgC,wBA6GzB,SAASC,GAAkBC,EAAwB,CACxD,OAAQA,EAAO,KAAM,CACnB,IAAK,yBACH,MAAO,yBAA+B,mBAAmBA,EAAO,MAAM,IAAI,EAE5E,IAAK,wBACH,MAAO,wBAA8B,mBAAmBA,EAAO,IAAI,EAErE,IAAK,4CACH,MAAO,4CAA+B,mBAAmBA,EAAO,MAAM,IAAI,EAE5E,IAAK,qCAAoC,CACvC,IAAMC,EAAeD,EAAO,QAAU,YAAc,mBAAmBA,EAAO,OAAO,EAAI,GACzF,MACE,sCACCA,EAAO,QAAU,gBAAkB,gBACpC,SACA,mBAAmBA,EAAO,IAAI,EAC9B,UACA,mBAAmBA,EAAO,MAAM,IAAI,EACpCC,GACCD,EAAO,OAAS,mBAAqB,GAE1C,CACA,IAAK,wBACH,MACE,wBACA,mBAAmBA,EAAO,MAAM,IAAI,EACpC,SACA,mBAAmBA,EAAO,IAAI,EAC9B,YACA,mBAAmBA,EAAO,SAAW,EAAE,EAG3C,IAAK,gDAA+B,CAClC,IAAMC,EAAeD,EAAO,QAAU,YAAc,mBAAmBA,EAAO,OAAO,EAAI,GACzF,MACE,gDACA,mBAAmBA,EAAO,IAAI,EAC9B,SACA,mBAAmBA,EAAO,IAAI,EAC9B,UACA,mBAAmBA,EAAO,MAAM,IAAI,EACpCC,GACCD,EAAO,OAAS,mBAAqB,GAE1C,CACA,QACE,MAAO,EAEX,CACF,CCtKA,IAAAE,GAAuB,wBAKdC,GAAA,6BAHF,SAASC,IAAsB,CAGpC,SAAO,QAAC,WAAO,SADb;AAAA;AAAA,uHAC6B,CACjC,CCNA,IAAAC,GAAiC,wBAgC1B,SAASC,GAA8BC,EAA8B,IAC1E,cAAU,CACR,MAAO,sCACP,QAASA,EAAoB,IAAKC,GAAiBA,EAAM,IAAI,EAAE,KAAK,IAAI,EACxE,MAAO,SAAM,MAAM,OACrB,CAAC,CACH,CCtCA,IAAAC,GAAuB,wBAMdC,GAAA,6BAJM,SAARC,GAA+C,CAAE,UAAAC,CAAU,EAA2B,CAE3F,IAAMC,EAAO,0CADKD,EAAY,UAAUA,CAAS,IAAM,WACS;AAAA;AAAA;AAAA,iDAEhE,SAAO,QAAC,WAAO,gBAAgB,oCAAoC,SAAUC,EAAM,CACrF,CCPA,IAAAC,GAAsD,wBACtDC,EAAwE,iBCDxE,IAAAC,GAA0C,wBAC1CC,GAAoB,kBACpBC,GAAyB,uBACzBC,GAAwB,cACxBC,GAAmD,oBCJnD,IAAAC,GAAiB,SCAjB,IAAAC,GAAoC,wBCA7B,IAAMC,GAAN,KAAa,CAGlB,YAAYC,EAAe,CACzB,KAAK,KAAOA,GAAQ,QACtB,CAEQ,WAAoB,CAC1B,OAAO,IAAI,KAAK,EAAE,YAAY,CAChC,CAEQ,cAAcC,EAA0B,CAC9C,GAAI,OAAOA,GAAY,SACrB,OAAOA,EAGT,GAAIA,aAAmB,MACrB,MAAO,GAAGA,EAAQ,OAAO;AAAA,EAAKA,EAAQ,KAAK,GAG7C,GAAI,OAAOA,GAAY,UAAYA,IAAY,KAC7C,GAAI,CACF,OAAO,KAAK,UAAUA,EAAS,KAAM,CAAC,CACxC,MAAY,CACV,OAAO,OAAOA,CAAO,CACvB,CAGF,OAAO,OAAOA,CAAO,CACvB,CAEA,KAAKA,EAAwB,CAC3B,QAAQ,IAAI,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,IAAI,YAAY,KAAK,cAAcA,CAAO,CAAC,EAAE,CAC1F,CAEA,QAAQA,EAAwB,CAC9B,QAAQ,IAAI,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,IAAI,eAAe,KAAK,cAAcA,CAAO,CAAC,EAAE,CAC7F,CAEA,QAAQA,EAAwB,CAC9B,QAAQ,IAAI,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,IAAI,eAAe,KAAK,cAAcA,CAAO,CAAC,EAAE,CAC7F,CAEA,MAAMA,EAAwB,CAC5B,QAAQ,IAAI,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,IAAI,aAAa,KAAK,cAAcA,CAAO,CAAC,EAAE,CAC3F,CAEA,MAAMA,EAAwB,CAC5B,QAAQ,IAAI,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,IAAI,aAAa,KAAK,cAAcA,CAAO,CAAC,EAAE,CAC3F,CAEA,MAAMA,EAAwB,CAC5B,QAAQ,IAAI,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,IAAI,aAAa,KAAK,cAAcA,CAAO,CAAC,EAAE,CAC3F,CACF,ED9CA,IAAMC,GAAS,IAAIC,GAAO,WAAW,EFMrC,SAASC,GAAqBC,EAA2B,CACvD,IAAMC,EAAOD,EACV,MAAM,GAAAE,QAAO,GAAG,EAChB,OAAQC,GAAM,CACb,GAAIA,GAAK,GACP,OAAOA,CAEX,CAAC,EACA,IAAI,EACP,OAAIF,GAGK,kDAEX,CAEO,SAASG,IAAuB,CAGrC,SAFgC,wBAAoB,EAC3B,UAEtB,MAAM,GAAG,EACT,OAAQJ,GAAcA,EAAU,KAAK,IAAM,EAAE,EAC7C,OAAQA,GAAiB,cAAWA,CAAS,CAAC,EAC9C,IAAKK,IAAW,CAAE,KAAMN,GAAqBM,EAAM,KAAK,CAAC,EAAG,IAAKA,EAAM,KAAK,EAAG,KAAMA,EAAM,KAAK,CAAE,EAAE,CACzG,CAEA,eAAsBC,IAAqC,CACzD,IAAMC,EAAmB,GAAAL,QAAO,QAAQ,MAAG,YAAQ,CAAC,qDAAqD,EACzG,GAAI,CACF,IAAMM,EAAe,KAAK,MAAM,QAAM,aAASD,EAAkB,MAAM,CAAC,EACxE,OAAO,OAAO,OAAOC,EAAa,MAAM,EAAE,IAAI,CAAC,CAAE,KAAAC,CAAK,KAAO,CAC3D,KAAMV,GAAqBU,CAAI,EAC/B,IAAKA,EACL,KAAAA,CACF,EAAE,CACJ,MAAY,CACV,MAAO,CAAC,CACV,CACF,CIpDA,IAAAC,GAAsB,wBAWtB,IAAMC,GAAS,IAAIC,GAAO,OAAO,EAC3BC,GAAQ,IAAI,SAAM,CAAE,SAAUC,GAAqB,GAAI,CAAC,ELA9D,IAAMC,GAAS,IAAIC,GAAO,OAAO,EAEpBC,MAAe,iBAAc,CAAC,CAAW,EACzCC,MAAuB,iBAAe,IAAM,CAAC,CAAyC,EA6D5F,SAASC,IAAyC,CACvD,IAAMC,KAAO,WAAQ,OAAM,wBAAoB,EAAG,CAAC,CAAC,EAC9C,CAACC,EAAOC,CAAQ,KAAI,YACxBF,EAAK,UACD,CACE,MAAO,GACP,OAAQG,GAAY,CACtB,EACA,CAAE,MAAO,GAAO,OAAQ,CAAC,CAAE,CACjC,EAEA,OAAAC,GAAO,KAAK,+BAA+B,KAE3C,aAAU,IAAM,CACTH,EAAM,OACTI,GAAiB,EACd,KAAMC,GAAW,CAChBJ,EAAS,CAAE,OAAAI,EAAQ,MAAO,EAAK,CAAC,CAClC,CAAC,EACA,MAAM,IAAMJ,EAAS,CAAE,OAAQC,GAAY,EAAG,MAAO,EAAK,CAAC,CAAC,CAEnE,EAAG,CAAC,CAAC,EAEEF,CACT,CMpGA,IAAAM,GAAoC,wBAEpCC,GAAe,kBAER,SAASC,GAAiBC,EAAiBC,EAAgB,CAChE,GAAM,CAAE,eAAAC,CAAe,KAAI,wBAAoB,EAEzCC,EAA+B,CAAC,EAiBtC,MAAO,CAhBkBH,EAAO,OAAQI,GAAiB,CACvD,IAAMC,EAAuB,GAAGD,EAAM,IAAI,IAAIF,GAAkB,WAAW,0BAE3E,GAAI,CAAC,GAAAI,QAAG,WAAWD,CAAoB,EACrC,OAAAF,EAAoB,KAAKC,CAAK,EACvB,GAIT,IAAMG,EADoB,KAAK,MAAM,GAAAD,QAAG,aAAaD,EAAsB,OAAO,CAAC,EACzD,SAASJ,CAAM,EAEzC,OAAKM,GACHJ,EAAoB,KAAKC,CAAK,EAEzBG,CACT,CAAC,EACyBJ,CAAmB,CAC/C,CZZW,IAAAK,GAAA,6BAJI,SAARC,IAA2B,CAChC,GAAM,CAAE,OAAAC,EAAQ,MAAAC,CAAM,EAAIC,GAAkB,EAE5C,GAAKD,GAEE,GAAID,EAAO,SAAW,EAC3B,SAAO,QAACG,GAAA,EAAoB,MAF5B,UAAO,QAAC,QAAK,UAAW,GAAM,EAKhC,GAAM,CAACC,EAAkBC,CAAmB,EAAIC,GAAiBN,EAAQ,uBAAuB,EAKhG,GAHIK,EAAoB,OAAS,GAC/BE,GAA8BF,CAAmB,EAE/CD,EAAiB,QAAU,EAC7B,SAAO,QAACI,GAAA,EAA8B,EAGxC,GAAIJ,EAAiB,QAAU,EAAG,CAChC,IAAMK,EAASC,GAAkB,CAAE,iDAAoC,MAAON,EAAiB,CAAC,CAAE,CAAC,KACnG,QAAKK,CAAM,KACX,aAAU,KACV,mBAAgB,CAClB,CAEA,SACE,QAAC,QAAK,UAAWL,IAAqB,OACnC,SAAAA,GAAkB,IAAKO,MACtB,QAAC,OAAK,KAAL,CACC,MAAOA,EAAM,KAEb,WACE,QAAC,eACC,oBAAC,SAAO,KAAP,CACC,MAAM,aACN,OAAQD,GAAkB,CAAE,iDAAoC,MAAOC,CAAM,CAAC,EAChF,EACF,GAPGA,EAAM,GASb,CACD,EACH,CAEJ", "names": ["require_identity", "__commonJSMin", "exports", "ALIAS", "DOC", "MAP", "PAIR", "SCALAR", "SEQ", "NODE_TYPE", "<PERSON><PERSON><PERSON><PERSON>", "node", "isDocument", "isMap", "isPair", "isScalar", "isSeq", "isCollection", "isNode", "hasAnchor", "require_visit", "__commonJSMin", "exports", "identity", "BREAK", "SKIP", "REMOVE", "visit", "node", "visitor", "visitor_", "initVisitor", "visit_", "key", "path", "ctrl", "callVisitor", "replaceNode", "i", "ci", "ck", "cv", "visitAsync", "visitAsync_", "parent", "pt", "require_directives", "__commonJSMin", "exports", "identity", "visit", "escape<PERSON><PERSON><PERSON>", "escapeTagName", "tn", "ch", "Directives", "_Directives", "yaml", "tags", "copy", "res", "line", "onError", "parts", "name", "handle", "prefix", "version", "<PERSON><PERSON><PERSON><PERSON>", "source", "verbatim", "suffix", "error", "tag", "doc", "lines", "tagEntries", "tagNames", "_key", "node", "require_anchors", "__commonJSMin", "exports", "identity", "visit", "anchorIsValid", "anchor", "msg", "anchorNames", "root", "anchors", "_key", "node", "findNewAnchor", "prefix", "exclude", "i", "name", "createNodeAnchors", "doc", "aliasObjects", "sourceObjects", "prevAnchors", "source", "ref", "error", "require_applyReviver", "__commonJSMin", "exports", "applyReviver", "reviver", "obj", "key", "val", "len", "v0", "v1", "k", "require_toJS", "__commonJSMin", "exports", "identity", "toJS", "value", "arg", "ctx", "v", "data", "res", "require_Node", "__commonJSMin", "exports", "applyReviver", "identity", "toJS", "NodeBase", "type", "copy", "doc", "mapAsMap", "max<PERSON><PERSON>s<PERSON>ount", "onAnchor", "reviver", "ctx", "res", "count", "require_Alias", "__commonJSMin", "exports", "anchors", "visit", "identity", "Node", "toJS", "<PERSON><PERSON>", "source", "doc", "found", "_key", "node", "_arg", "ctx", "max<PERSON><PERSON>s<PERSON>ount", "msg", "data", "getAliasCount", "_onComment", "_onChompKeep", "src", "anchor", "count", "item", "c", "kc", "vc", "require_<PERSON><PERSON><PERSON>", "__commonJSMin", "exports", "identity", "Node", "toJS", "isScalarValue", "value", "<PERSON><PERSON><PERSON>", "arg", "ctx", "require_createNode", "__commonJSMin", "exports", "<PERSON><PERSON>", "identity", "<PERSON><PERSON><PERSON>", "defaultTagPrefix", "findTagObject", "value", "tagName", "tags", "match", "t", "tagObj", "createNode", "ctx", "map", "aliasDuplicateObjects", "onAnchor", "onTagObj", "schema", "sourceObjects", "ref", "node", "require_Collection", "__commonJSMin", "exports", "createNode", "identity", "Node", "collectionFromPath", "schema", "path", "value", "v", "k", "a", "isEmptyPath", "Collection", "type", "copy", "it", "key", "rest", "node", "keepScalar", "allowScalar", "require_stringifyComment", "__commonJSMin", "exports", "stringifyComment", "str", "indentComment", "comment", "indent", "lineComment", "require_foldFlowLines", "__commonJSMin", "exports", "FOLD_FLOW", "FOLD_BLOCK", "FOLD_QUOTED", "foldFlowLines", "text", "indent", "mode", "indentAtStart", "lineWidth", "minC<PERSON>nt<PERSON>id<PERSON>", "onFold", "onOverflow", "endStep", "folds", "escaped<PERSON><PERSON>s", "end", "split", "prev", "overflow", "i", "escStart", "escEnd", "consumeMoreIndentedLines", "ch", "next", "j", "res", "fold", "start", "require_stringifyString", "__commonJSMin", "exports", "<PERSON><PERSON><PERSON>", "foldFlowLines", "getFoldOptions", "ctx", "isBlock", "containsDocumentMarker", "str", "lineLengthOverLimit", "lineWidth", "indentLength", "limit", "strLen", "i", "start", "doubleQuotedString", "value", "json", "implicitKey", "minMultiLineLength", "indent", "ch", "code", "singleQuotedString", "res", "quotedString", "singleQuote", "qs", "hasDouble", "hasSingle", "blockEndNewlines", "blockString", "comment", "type", "onComment", "onChompKeep", "blockQuote", "commentString", "literal", "chomp", "endStart", "end", "endNlPos", "startWithSpace", "startEnd", "startNlPos", "header", "foldedValue", "<PERSON><PERSON><PERSON>back", "foldOptions", "body", "plainString", "item", "actualString", "indentStep", "inFlow", "test", "tag", "compat", "tags", "stringifyString", "ss", "_stringify", "_type", "defaultKeyType", "defaultStringType", "t", "require_stringify", "__commonJSMin", "exports", "anchors", "identity", "stringifyComment", "stringifyString", "createStringifyContext", "doc", "options", "opt", "inFlow", "getTagObject", "tags", "item", "match", "t", "tagObj", "obj", "testMatch", "name", "stringifyProps", "node", "anchors$1", "props", "anchor", "tag", "stringify", "ctx", "onComment", "onChompKeep", "o", "str", "require_stringifyPair", "__commonJSMin", "exports", "identity", "<PERSON><PERSON><PERSON>", "stringify", "stringifyComment", "stringifyPair", "key", "value", "ctx", "onComment", "onChompKeep", "allNullValues", "doc", "indent", "indentStep", "commentString", "indentSeq", "simpleKeys", "keyComment", "msg", "<PERSON><PERSON><PERSON>", "keyCommentDone", "chomp<PERSON><PERSON>", "str", "vsb", "vcb", "valueComment", "valueCommentDone", "valueStr", "ws", "cs", "vs0", "nl0", "hasNewline", "flow", "hasPropsLine", "sp0", "require_log", "__commonJSMin", "exports", "node_process", "debug", "logLevel", "messages", "warn", "warning", "require_merge", "__commonJSMin", "exports", "identity", "<PERSON><PERSON><PERSON>", "MERGE_KEY", "merge", "value", "addMergeToJSMap", "isMergeKey", "ctx", "key", "tag", "map", "it", "mergeValue", "source", "srcMap", "require_addPairToJSMap", "__commonJSMin", "exports", "log", "merge", "stringify", "identity", "toJS", "addPairToJSMap", "ctx", "map", "key", "value", "js<PERSON>ey", "<PERSON><PERSON><PERSON>", "stringify<PERSON>ey", "jsValue", "strCtx", "node", "str<PERSON><PERSON>", "jsonStr", "require_Pair", "__commonJSMin", "exports", "createNode", "stringifyPair", "addPairToJSMap", "identity", "createPair", "key", "value", "ctx", "k", "v", "Pair", "_Pair", "schema", "_", "pair", "onComment", "onChompKeep", "require_stringifyCollection", "__commonJSMin", "exports", "identity", "stringify", "stringifyComment", "stringifyCollection", "collection", "ctx", "options", "stringifyFlowCollection", "stringifyBlockCollection", "comment", "items", "blockItemPrefix", "flowChars", "itemIndent", "onChompKeep", "onComment", "indent", "commentString", "itemCtx", "chomp<PERSON><PERSON>", "lines", "i", "item", "addCommentBefore", "ik", "str", "line", "indentStep", "fcPadding", "reqNewline", "linesAtValue", "iv", "start", "end", "len", "sum", "ic", "require_YAMLMap", "__commonJSMin", "exports", "stringifyCollection", "addPairToJSMap", "Collection", "identity", "Pair", "<PERSON><PERSON><PERSON>", "find<PERSON>air", "items", "key", "k", "it", "YAMLMap", "schema", "obj", "ctx", "keepUndefined", "replacer", "map", "add", "value", "pair", "overwrite", "_pair", "prev", "sortEntries", "i", "item", "keepScalar", "node", "_", "Type", "onComment", "onChompKeep", "require_map", "__commonJSMin", "exports", "identity", "YAMLMap", "map", "onError", "schema", "obj", "ctx", "require_YAMLSeq", "__commonJSMin", "exports", "createNode", "stringifyCollection", "Collection", "identity", "<PERSON><PERSON><PERSON>", "toJS", "YAMLSeq", "schema", "value", "key", "idx", "asItemIndex", "keepScalar", "it", "prev", "_", "ctx", "seq", "item", "onComment", "onChompKeep", "obj", "replacer", "i", "require_seq", "__commonJSMin", "exports", "identity", "YAMLSeq", "seq", "onError", "schema", "obj", "ctx", "require_string", "__commonJSMin", "exports", "stringifyString", "string", "value", "str", "item", "ctx", "onComment", "onChompKeep", "require_null", "__commonJSMin", "exports", "<PERSON><PERSON><PERSON>", "nullTag", "value", "source", "ctx", "require_bool", "__commonJSMin", "exports", "<PERSON><PERSON><PERSON>", "boolTag", "value", "str", "source", "ctx", "sv", "require_stringifyNumber", "__commonJSMin", "exports", "stringifyNumber", "format", "minFractionDigits", "tag", "value", "num", "n", "i", "d", "require_float", "__commonJSMin", "exports", "<PERSON><PERSON><PERSON>", "stringifyNumber", "floatNaN", "value", "str", "floatExp", "node", "num", "float", "dot", "require_int", "__commonJSMin", "exports", "stringifyNumber", "intIdentify", "value", "intResolve", "str", "offset", "radix", "intAsBigInt", "intStringify", "node", "prefix", "intOct", "_onError", "opt", "int", "intHex", "require_schema", "__commonJSMin", "exports", "map", "_null", "seq", "string", "bool", "float", "int", "schema", "require_schema", "__commonJSMin", "exports", "<PERSON><PERSON><PERSON>", "map", "seq", "intIdentify", "value", "stringifyJSON", "jsonScalars", "str", "_onError", "intAsBigInt", "jsonError", "onError", "schema", "require_binary", "__commonJSMin", "exports", "node_buffer", "<PERSON><PERSON><PERSON>", "stringifyString", "binary", "value", "src", "onError", "str", "buffer", "comment", "type", "ctx", "onComment", "onChompKeep", "buf", "s", "i", "lineWidth", "n", "lines", "o", "require_pairs", "__commonJSMin", "exports", "identity", "Pair", "<PERSON><PERSON><PERSON>", "YAMLSeq", "resolvePairs", "seq", "onError", "i", "item", "pair", "cn", "createPairs", "schema", "iterable", "ctx", "replacer", "pairs", "it", "key", "value", "keys", "require_omap", "__commonJSMin", "exports", "identity", "toJS", "YAMLMap", "YAMLSeq", "pairs", "YAMLOMap", "_YAMLOMap", "_", "ctx", "map", "pair", "key", "value", "schema", "iterable", "pairs$1", "omap", "seq", "onError", "<PERSON><PERSON><PERSON><PERSON>", "require_bool", "__commonJSMin", "exports", "<PERSON><PERSON><PERSON>", "boolStringify", "value", "source", "ctx", "trueTag", "falseTag", "require_float", "__commonJSMin", "exports", "<PERSON><PERSON><PERSON>", "stringifyNumber", "floatNaN", "value", "str", "floatExp", "node", "num", "float", "dot", "f", "require_int", "__commonJSMin", "exports", "stringifyNumber", "intIdentify", "value", "intResolve", "str", "offset", "radix", "intAsBigInt", "sign", "n", "intStringify", "node", "prefix", "intBin", "_onError", "opt", "intOct", "int", "intHex", "require_set", "__commonJSMin", "exports", "identity", "Pair", "YAMLMap", "YAMLSet", "_YAMLSet", "schema", "key", "pair", "keepPair", "value", "prev", "_", "ctx", "onComment", "onChompKeep", "iterable", "replacer", "set", "map", "onError", "require_timestamp", "__commonJSMin", "exports", "stringifyNumber", "parseSexagesimal", "str", "asBigInt", "sign", "parts", "num", "n", "res", "p", "stringifySexagesimal", "node", "value", "_60", "intTime", "_onError", "intAsBigInt", "floatTime", "timestamp", "match", "year", "month", "day", "hour", "minute", "second", "millisec", "date", "tz", "d", "require_schema", "__commonJSMin", "exports", "map", "_null", "seq", "string", "binary", "bool", "float", "int", "merge", "omap", "pairs", "set", "timestamp", "schema", "require_tags", "__commonJSMin", "exports", "map", "_null", "seq", "string", "bool", "float", "int", "schema", "schema$1", "binary", "merge", "omap", "pairs", "schema$2", "set", "timestamp", "schemas", "tagsByName", "coreKnownTags", "getTags", "customTags", "schemaName", "addMergeTag", "schemaTags", "tags", "keys", "key", "tag", "tagObj", "tagName", "require_Schema", "__commonJSMin", "exports", "identity", "map", "seq", "string", "tags", "sortMapEntriesByKey", "a", "b", "<PERSON><PERSON><PERSON>", "_Schema", "compat", "customTags", "merge", "resolveKnownTags", "schema", "sortMapEntries", "to<PERSON><PERSON>Defaults", "copy", "require_stringifyDocument", "__commonJSMin", "exports", "identity", "stringify", "stringifyComment", "stringifyDocument", "doc", "options", "lines", "hasDirectives", "dir", "ctx", "commentString", "cs", "chomp<PERSON><PERSON>", "contentComment", "onChompKeep", "body", "dc", "require_Document", "__commonJSMin", "exports", "<PERSON><PERSON>", "Collection", "identity", "Pair", "toJS", "<PERSON><PERSON><PERSON>", "stringifyDocument", "anchors", "applyReviver", "createNode", "directives", "Document", "_Document", "value", "replacer", "options", "_replacer", "opt", "version", "copy", "assertCollection", "path", "node", "name", "prev", "keyToStr", "v", "asStr", "aliasDuplicateObjects", "anchorPrefix", "flow", "keepUndefined", "onTagObj", "tag", "onAnchor", "setAnchors", "sourceObjects", "ctx", "key", "k", "keepScalar", "sv", "json", "jsonArg", "mapAsMap", "max<PERSON><PERSON>s<PERSON>ount", "reviver", "res", "count", "s", "contents", "require_errors", "__commonJSMin", "exports", "YAMLError", "name", "pos", "code", "message", "YAMLParseError", "YAMLWarning", "prettifyError", "src", "lc", "error", "line", "col", "ci", "lineStr", "trimStart", "prev", "count", "end", "pointer", "require_resolve_props", "__commonJSMin", "exports", "resolveProps", "tokens", "flow", "indicator", "next", "offset", "onError", "parentIndent", "startOnNewline", "spaceBefore", "atNewline", "hasSpace", "comment", "commentSep", "hasNewline", "reqSpace", "tab", "anchor", "tag", "newlineAfterProp", "comma", "found", "start", "token", "cb", "last", "end", "require_util_contains_newline", "__commonJSMin", "exports", "containsNewline", "key", "st", "it", "require_util_flow_indent_check", "__commonJSMin", "exports", "utilContainsNewline", "flowIndentCheck", "indent", "fc", "onError", "end", "require_util_map_includes", "__commonJSMin", "exports", "identity", "mapIncludes", "ctx", "items", "search", "uniqueKeys", "isEqual", "a", "b", "pair", "require_resolve_block_map", "__commonJSMin", "exports", "Pair", "YAMLMap", "resolveProps", "utilContainsNewline", "utilFlowIndentCheck", "utilMapIncludes", "startColMsg", "resolveBlockMap", "composeNode", "composeEmptyNode", "ctx", "bm", "onError", "tag", "NodeClass", "map", "offset", "commentEnd", "collItem", "start", "key", "sep", "value", "keyProps", "implicitKey", "keyStart", "keyNode", "valueProps", "valueNode", "pair", "require_resolve_block_seq", "__commonJSMin", "exports", "YAMLSeq", "resolveProps", "utilFlowIndentCheck", "resolveBlockSeq", "composeNode", "composeEmptyNode", "ctx", "bs", "onError", "tag", "NodeClass", "seq", "offset", "commentEnd", "start", "value", "props", "node", "require_resolve_end", "__commonJSMin", "exports", "resolveEnd", "end", "offset", "reqSpace", "onError", "comment", "hasSpace", "sep", "token", "source", "type", "cb", "require_resolve_flow_collection", "__commonJSMin", "exports", "identity", "Pair", "YAMLMap", "YAMLSeq", "resolveEnd", "resolveProps", "utilContainsNewline", "utilMapIncludes", "blockMsg", "isBlock", "token", "resolveFlowCollection", "composeNode", "composeEmptyNode", "ctx", "fc", "onError", "tag", "isMap", "fcName", "NodeClass", "coll", "atRoot", "offset", "i", "collItem", "start", "key", "sep", "value", "props", "prevItemComment", "loop", "st", "prev", "valueNode", "keyStart", "keyNode", "valueProps", "pair", "map", "endRange", "expectedEnd", "ce", "ee", "cePos", "name", "msg", "end", "require_compose_collection", "__commonJSMin", "exports", "identity", "<PERSON><PERSON><PERSON>", "YAMLMap", "YAMLSeq", "resolveBlockMap", "resolveBlockSeq", "resolveFlowCollection", "resolveCollection", "CN", "ctx", "token", "onError", "tagName", "tag", "coll", "Coll", "composeCollection", "props", "tagToken", "msg", "anchor", "nl", "lastProp", "expType", "t", "kt", "res", "node", "require_resolve_block_scalar", "__commonJSMin", "exports", "<PERSON><PERSON><PERSON>", "resolveBlockScalar", "ctx", "scalar", "onError", "start", "header", "parseBlockScalarHeader", "type", "lines", "splitLines", "chompStart", "i", "content", "value", "end", "trimIndent", "offset", "contentStart", "indent", "sep", "prevMoreIndented", "crlf", "message", "props", "strict", "source", "mode", "chomp", "error", "ch", "n", "hasSpace", "comment", "length", "token", "ts", "split", "first", "m", "require_resolve_flow_scalar", "__commonJSMin", "exports", "<PERSON><PERSON><PERSON>", "resolveEnd", "resolveFlowScalar", "scalar", "strict", "onError", "offset", "type", "source", "end", "_type", "value", "_onError", "rel", "code", "msg", "plainValue", "singleQuotedValue", "doubleQuotedV<PERSON>ue", "valueEnd", "re", "badChar", "foldLines", "first", "line", "match", "res", "sep", "pos", "last", "i", "ch", "fold", "foldNewline", "next", "cc", "escapeCodes", "length", "parseCharCode", "raw", "wsStart", "require_compose_scalar", "__commonJSMin", "exports", "identity", "<PERSON><PERSON><PERSON>", "resolveBlockScalar", "resolveFlowScalar", "composeScalar", "ctx", "token", "tagToken", "onError", "value", "type", "comment", "range", "tagName", "msg", "tag", "findScalarTagByName", "findScalarTagByTest", "scalar", "res", "error", "schema", "matchWithTest", "kt", "atKey", "directives", "compat", "ts", "cs", "require_util_empty_scalar_position", "__commonJSMin", "exports", "emptyScalarPosition", "offset", "before", "pos", "i", "st", "require_compose_node", "__commonJSMin", "exports", "<PERSON><PERSON>", "identity", "composeCollection", "composeScalar", "resolveEnd", "utilEmptyScalarPosition", "CN", "composeNode", "composeEmptyNode", "ctx", "token", "props", "onError", "atKey", "spaceBefore", "comment", "anchor", "tag", "node", "isSrcToken", "<PERSON><PERSON><PERSON><PERSON>", "message", "offset", "before", "pos", "end", "options", "source", "alias", "valueEnd", "re", "require_compose_doc", "__commonJSMin", "exports", "Document", "composeNode", "resolveEnd", "resolveProps", "composeDoc", "options", "directives", "offset", "start", "value", "end", "onError", "opts", "doc", "ctx", "props", "contentEnd", "re", "require_composer", "__commonJSMin", "exports", "node_process", "directives", "Document", "errors", "identity", "composeDoc", "resolveEnd", "getErrorPos", "src", "offset", "source", "parsePrelude", "prelude", "comment", "atComment", "afterEmptyLine", "Composer", "options", "code", "message", "warning", "pos", "doc", "afterDoc", "dc", "it", "cb", "tokens", "forceDoc", "endOffset", "token", "msg", "error", "end", "opts", "require_cst_scalar", "__commonJSMin", "exports", "resolveBlockScalar", "resolveFlowScalar", "errors", "stringifyString", "resolveAsScalar", "token", "strict", "onError", "_onError", "pos", "code", "message", "offset", "createScalarToken", "value", "context", "implicitKey", "indent", "inFlow", "type", "source", "end", "he", "head", "body", "props", "addEndtoBlockProps", "setScalarValue", "<PERSON><PERSON><PERSON>", "header", "setBlockScalarValue", "setFlowScalarValue", "key", "st", "oa", "tok", "nl", "require_cst_stringify", "__commonJSMin", "exports", "stringify", "cst", "stringifyToken", "stringifyItem", "token", "res", "tok", "item", "st", "start", "key", "sep", "value", "require_cst_visit", "__commonJSMin", "exports", "BREAK", "SKIP", "REMOVE", "visit", "cst", "visitor", "_visit", "path", "item", "field", "index", "tok", "parent", "coll", "ctrl", "token", "i", "ci", "require_cst", "__commonJSMin", "exports", "cstScalar", "cstStringify", "cstVisit", "BOM", "DOCUMENT", "FLOW_END", "SCALAR", "isCollection", "token", "isScalar", "prettyToken", "tokenType", "source", "require_lexer", "__commonJSMin", "exports", "cst", "isEmpty", "ch", "hexDigits", "tagChars", "flowIndicatorChars", "invalidAnchorChars", "isNotAnchorChar", "<PERSON><PERSON>", "source", "incomplete", "next", "i", "n", "offset", "indent", "dt", "end", "state", "line", "dirEnd", "cs", "sp", "s", "ch0", "ch1", "nl", "quote", "qb", "loop", "lastChar", "inFlow", "allowEmpty", "allowTabs", "test", "require_line_counter", "__commonJSMin", "exports", "LineCounter", "offset", "low", "high", "mid", "start", "require_parser", "__commonJSMin", "exports", "node_process", "cst", "lexer", "includesToken", "list", "type", "i", "findNonEmptyIndex", "isFlowToken", "token", "getPrevProps", "parent", "it", "getFirstKeyStartProps", "prev", "loop", "fixFlowSeqItems", "fc", "<PERSON><PERSON><PERSON>", "onNewLine", "source", "incomplete", "lexeme", "message", "top", "n", "error", "last", "st", "doc", "bv", "scalar", "start", "sep", "map", "nl", "end", "atMapIndent", "atNextItem", "key", "fs", "seq", "indent", "docEnd", "require_public_api", "__commonJSMin", "exports", "composer", "Document", "errors", "log", "identity", "lineCounter", "parser", "parseOptions", "options", "prettyErrors", "parseAllDocuments", "source", "parser$1", "composer$1", "docs", "doc", "parseDocument", "_doc", "parse", "src", "reviver", "_reviver", "warning", "stringify", "value", "replacer", "_replacer", "indent", "keepUndefined", "require_dist", "__commonJSMin", "exports", "composer", "Document", "<PERSON><PERSON><PERSON>", "errors", "<PERSON><PERSON>", "identity", "Pair", "<PERSON><PERSON><PERSON>", "YAMLMap", "YAMLSeq", "cst", "lexer", "lineCounter", "parser", "publicApi", "visit", "dailyNoteCommand_exports", "__export", "Command", "__toCommonJS", "import_api", "BYTES_PER_KILOBYTE", "BYTES_PER_MEGABYTE", "BYTES_PER_GIGABYTE", "import_api", "getObsidianTarget", "target", "headingParam", "import_api", "import_jsx_runtime", "NoVaultFoundMessage", "import_api", "vaultsWithoutAdvancedURIToast", "vaultsWithoutPlugin", "vault", "import_api", "import_jsx_runtime", "AdvancedURIPluginNotInstalled", "<PERSON><PERSON><PERSON>", "text", "import_api", "import_react", "import_api", "fs", "import_promises", "import_os", "import_path", "import_yaml", "import_api", "<PERSON><PERSON>", "name", "message", "logger", "<PERSON><PERSON>", "getVaultNameFromPath", "vaultPath", "name", "fsPath", "i", "parse<PERSON><PERSON><PERSON>", "vault", "loadObsidianJson", "obsidianJsonPath", "obsidianJson", "path", "import_api", "logger", "<PERSON><PERSON>", "cache", "BYTES_PER_MEGABYTE", "logger", "<PERSON><PERSON>", "NotesContext", "NotesDispatchContext", "useObsidianVaults", "pref", "state", "setState", "parse<PERSON><PERSON><PERSON>", "logger", "loadObsidianJson", "vaults", "import_api", "import_fs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vaults", "plugin", "configFileName", "vaultsWithoutPlugin", "vault", "communityPluginsPath", "fs", "hasPlugin", "import_jsx_runtime", "Command", "vaults", "ready", "useObsidianVaults", "NoVaultFoundMessage", "vaultsWithPlugin", "vaultsWithoutPlugin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vaultsWithoutAdvancedURIToast", "AdvancedURIPluginNotInstalled", "target", "getObsidianTarget", "vault"]}