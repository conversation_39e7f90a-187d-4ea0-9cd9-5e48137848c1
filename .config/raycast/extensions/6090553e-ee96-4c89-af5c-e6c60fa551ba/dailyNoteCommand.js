"use strict";var Qo=Object.create;var ze=Object.defineProperty;var Ho=Object.getOwnPropertyDescriptor;var Xo=Object.getOwnPropertyNames;var zo=Object.getPrototypeOf,Zo=Object.prototype.hasOwnProperty;var g=(s,e)=>()=>(e||s((e={exports:{}}).exports,e),e.exports),ea=(s,e)=>{for(var t in e)ze(s,t,{get:e[t],enumerable:!0})},Vn=(s,e,t,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Xo(e))!Zo.call(s,i)&&i!==t&&ze(s,i,{get:()=>e[i],enumerable:!(n=Ho(e,i))||n.enumerable});return s};var Ze=(s,e,t)=>(t=s!=null?Qo(zo(s)):{},Vn(e||!s||!s.__esModule?ze(t,"default",{value:s,enumerable:!0}):t,s)),ta=s=>Vn(ze({},"__esModule",{value:!0}),s);var O=g(P=>{"use strict";var ns=Symbol.for("yaml.alias"),Hn=Symbol.for("yaml.document"),tt=Symbol.for("yaml.map"),Xn=Symbol.for("yaml.pair"),is=Symbol.for("yaml.scalar"),st=Symbol.for("yaml.seq"),V=Symbol.for("yaml.node.type"),na=s=>!!s&&typeof s=="object"&&s[V]===ns,ia=s=>!!s&&typeof s=="object"&&s[V]===Hn,ra=s=>!!s&&typeof s=="object"&&s[V]===tt,oa=s=>!!s&&typeof s=="object"&&s[V]===Xn,zn=s=>!!s&&typeof s=="object"&&s[V]===is,aa=s=>!!s&&typeof s=="object"&&s[V]===st;function Zn(s){if(s&&typeof s=="object")switch(s[V]){case tt:case st:return!0}return!1}function la(s){if(s&&typeof s=="object")switch(s[V]){case ns:case tt:case is:case st:return!0}return!1}var ca=s=>(zn(s)||Zn(s))&&!!s.anchor;P.ALIAS=ns;P.DOC=Hn;P.MAP=tt;P.NODE_TYPE=V;P.PAIR=Xn;P.SCALAR=is;P.SEQ=st;P.hasAnchor=ca;P.isAlias=na;P.isCollection=Zn;P.isDocument=ia;P.isMap=ra;P.isNode=la;P.isPair=oa;P.isScalar=zn;P.isSeq=aa});var we=g(rs=>{"use strict";var C=O(),M=Symbol("break visit"),ei=Symbol("skip children"),R=Symbol("remove node");function nt(s,e){let t=ti(e);C.isDocument(s)?fe(null,s.contents,t,Object.freeze([s]))===R&&(s.contents=null):fe(null,s,t,Object.freeze([]))}nt.BREAK=M;nt.SKIP=ei;nt.REMOVE=R;function fe(s,e,t,n){let i=si(s,e,t,n);if(C.isNode(i)||C.isPair(i))return ni(s,n,i),fe(s,i,t,n);if(typeof i!="symbol"){if(C.isCollection(e)){n=Object.freeze(n.concat(e));for(let r=0;r<e.items.length;++r){let o=fe(r,e.items[r],t,n);if(typeof o=="number")r=o-1;else{if(o===M)return M;o===R&&(e.items.splice(r,1),r-=1)}}}else if(C.isPair(e)){n=Object.freeze(n.concat(e));let r=fe("key",e.key,t,n);if(r===M)return M;r===R&&(e.key=null);let o=fe("value",e.value,t,n);if(o===M)return M;o===R&&(e.value=null)}}return i}async function it(s,e){let t=ti(e);C.isDocument(s)?await ue(null,s.contents,t,Object.freeze([s]))===R&&(s.contents=null):await ue(null,s,t,Object.freeze([]))}it.BREAK=M;it.SKIP=ei;it.REMOVE=R;async function ue(s,e,t,n){let i=await si(s,e,t,n);if(C.isNode(i)||C.isPair(i))return ni(s,n,i),ue(s,i,t,n);if(typeof i!="symbol"){if(C.isCollection(e)){n=Object.freeze(n.concat(e));for(let r=0;r<e.items.length;++r){let o=await ue(r,e.items[r],t,n);if(typeof o=="number")r=o-1;else{if(o===M)return M;o===R&&(e.items.splice(r,1),r-=1)}}}else if(C.isPair(e)){n=Object.freeze(n.concat(e));let r=await ue("key",e.key,t,n);if(r===M)return M;r===R&&(e.key=null);let o=await ue("value",e.value,t,n);if(o===M)return M;o===R&&(e.value=null)}}return i}function ti(s){return typeof s=="object"&&(s.Collection||s.Node||s.Value)?Object.assign({Alias:s.Node,Map:s.Node,Scalar:s.Node,Seq:s.Node},s.Value&&{Map:s.Value,Scalar:s.Value,Seq:s.Value},s.Collection&&{Map:s.Collection,Seq:s.Collection},s):s}function si(s,e,t,n){if(typeof t=="function")return t(s,e,n);if(C.isMap(e))return t.Map?.(s,e,n);if(C.isSeq(e))return t.Seq?.(s,e,n);if(C.isPair(e))return t.Pair?.(s,e,n);if(C.isScalar(e))return t.Scalar?.(s,e,n);if(C.isAlias(e))return t.Alias?.(s,e,n)}function ni(s,e,t){let n=e[e.length-1];if(C.isCollection(n))n.items[s]=t;else if(C.isPair(n))s==="key"?n.key=t:n.value=t;else if(C.isDocument(n))n.contents=t;else{let i=C.isAlias(n)?"alias":"scalar";throw new Error(`Cannot replace node with ${i} parent`)}}rs.visit=nt;rs.visitAsync=it});var os=g(ri=>{"use strict";var ii=O(),fa=we(),ua={"!":"%21",",":"%2C","[":"%5B","]":"%5D","{":"%7B","}":"%7D"},ha=s=>s.replace(/[!,[\]{}]/g,e=>ua[e]),ve=class s{constructor(e,t){this.docStart=null,this.docEnd=!1,this.yaml=Object.assign({},s.defaultYaml,e),this.tags=Object.assign({},s.defaultTags,t)}clone(){let e=new s(this.yaml,this.tags);return e.docStart=this.docStart,e}atDocument(){let e=new s(this.yaml,this.tags);switch(this.yaml.version){case"1.1":this.atNextDocument=!0;break;case"1.2":this.atNextDocument=!1,this.yaml={explicit:s.defaultYaml.explicit,version:"1.2"},this.tags=Object.assign({},s.defaultTags);break}return e}add(e,t){this.atNextDocument&&(this.yaml={explicit:s.defaultYaml.explicit,version:"1.1"},this.tags=Object.assign({},s.defaultTags),this.atNextDocument=!1);let n=e.trim().split(/[ \t]+/),i=n.shift();switch(i){case"%TAG":{if(n.length!==2&&(t(0,"%TAG directive should contain exactly two parts"),n.length<2))return!1;let[r,o]=n;return this.tags[r]=o,!0}case"%YAML":{if(this.yaml.explicit=!0,n.length!==1)return t(0,"%YAML directive should contain exactly one part"),!1;let[r]=n;if(r==="1.1"||r==="1.2")return this.yaml.version=r,!0;{let o=/^\d+\.\d+$/.test(r);return t(6,`Unsupported YAML version ${r}`,o),!1}}default:return t(0,`Unknown directive ${i}`,!0),!1}}tagName(e,t){if(e==="!")return"!";if(e[0]!=="!")return t(`Not a valid tag: ${e}`),null;if(e[1]==="<"){let o=e.slice(2,-1);return o==="!"||o==="!!"?(t(`Verbatim tags aren't resolved, so ${e} is invalid.`),null):(e[e.length-1]!==">"&&t("Verbatim tags must end with a >"),o)}let[,n,i]=e.match(/^(.*!)([^!]*)$/s);i||t(`The ${e} tag has no suffix`);let r=this.tags[n];if(r)try{return r+decodeURIComponent(i)}catch(o){return t(String(o)),null}return n==="!"?e:(t(`Could not resolve tag: ${e}`),null)}tagString(e){for(let[t,n]of Object.entries(this.tags))if(e.startsWith(n))return t+ha(e.substring(n.length));return e[0]==="!"?e:`!<${e}>`}toString(e){let t=this.yaml.explicit?[`%YAML ${this.yaml.version||"1.2"}`]:[],n=Object.entries(this.tags),i;if(e&&n.length>0&&ii.isNode(e.contents)){let r={};fa.visit(e.contents,(o,a)=>{ii.isNode(a)&&a.tag&&(r[a.tag]=!0)}),i=Object.keys(r)}else i=[];for(let[r,o]of n)r==="!!"&&o==="tag:yaml.org,2002:"||(!e||i.some(a=>a.startsWith(o)))&&t.push(`%TAG ${r} ${o}`);return t.join(`
`)}};ve.defaultYaml={explicit:!1,version:"1.2"};ve.defaultTags={"!!":"tag:yaml.org,2002:"};ri.Directives=ve});var rt=g(ke=>{"use strict";var oi=O(),da=we();function pa(s){if(/[\x00-\x19\s,[\]{}]/.test(s)){let t=`Anchor must not contain whitespace or control characters: ${JSON.stringify(s)}`;throw new Error(t)}return!0}function ai(s){let e=new Set;return da.visit(s,{Value(t,n){n.anchor&&e.add(n.anchor)}}),e}function li(s,e){for(let t=1;;++t){let n=`${s}${t}`;if(!e.has(n))return n}}function ma(s,e){let t=[],n=new Map,i=null;return{onAnchor:r=>{t.push(r),i||(i=ai(s));let o=li(e,i);return i.add(o),o},setAnchors:()=>{for(let r of t){let o=n.get(r);if(typeof o=="object"&&o.anchor&&(oi.isScalar(o.node)||oi.isCollection(o.node)))o.node.anchor=o.anchor;else{let a=new Error("Failed to resolve repeated object (this should not happen)");throw a.source=r,a}}},sourceObjects:n}}ke.anchorIsValid=pa;ke.anchorNames=ai;ke.createNodeAnchors=ma;ke.findNewAnchor=li});var as=g(ci=>{"use strict";function Ne(s,e,t,n){if(n&&typeof n=="object")if(Array.isArray(n))for(let i=0,r=n.length;i<r;++i){let o=n[i],a=Ne(s,n,String(i),o);a===void 0?delete n[i]:a!==o&&(n[i]=a)}else if(n instanceof Map)for(let i of Array.from(n.keys())){let r=n.get(i),o=Ne(s,n,i,r);o===void 0?n.delete(i):o!==r&&n.set(i,o)}else if(n instanceof Set)for(let i of Array.from(n)){let r=Ne(s,n,i,i);r===void 0?n.delete(i):r!==i&&(n.delete(i),n.add(r))}else for(let[i,r]of Object.entries(n)){let o=Ne(s,n,i,r);o===void 0?delete n[i]:o!==r&&(n[i]=o)}return s.call(e,t,n)}ci.applyReviver=Ne});var J=g(ui=>{"use strict";var ga=O();function fi(s,e,t){if(Array.isArray(s))return s.map((n,i)=>fi(n,String(i),t));if(s&&typeof s.toJSON=="function"){if(!t||!ga.hasAnchor(s))return s.toJSON(e,t);let n={aliasCount:0,count:1,res:void 0};t.anchors.set(s,n),t.onCreate=r=>{n.res=r,delete t.onCreate};let i=s.toJSON(e,t);return t.onCreate&&t.onCreate(i),i}return typeof s=="bigint"&&!t?.keep?Number(s):s}ui.toJS=fi});var ot=g(di=>{"use strict";var ya=as(),hi=O(),ba=J(),ls=class{constructor(e){Object.defineProperty(this,hi.NODE_TYPE,{value:e})}clone(){let e=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return this.range&&(e.range=this.range.slice()),e}toJS(e,{mapAsMap:t,maxAliasCount:n,onAnchor:i,reviver:r}={}){if(!hi.isDocument(e))throw new TypeError("A document argument is required");let o={anchors:new Map,doc:e,keep:!0,mapAsMap:t===!0,mapKeyWarned:!1,maxAliasCount:typeof n=="number"?n:100},a=ba.toJS(this,"",o);if(typeof i=="function")for(let{count:l,res:c}of o.anchors.values())i(c,l);return typeof r=="function"?ya.applyReviver(r,{"":a},"",a):a}};di.NodeBase=ls});var Ae=g(mi=>{"use strict";var Sa=rt(),pi=we(),at=O(),wa=ot(),va=J(),cs=class extends wa.NodeBase{constructor(e){super(at.ALIAS),this.source=e,Object.defineProperty(this,"tag",{set(){throw new Error("Alias nodes cannot have tags")}})}resolve(e){let t;return pi.visit(e,{Node:(n,i)=>{if(i===this)return pi.visit.BREAK;i.anchor===this.source&&(t=i)}}),t}toJSON(e,t){if(!t)return{source:this.source};let{anchors:n,doc:i,maxAliasCount:r}=t,o=this.resolve(i);if(!o){let l=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new ReferenceError(l)}let a=n.get(o);if(a||(va.toJS(o,null,t),a=n.get(o)),!a||a.res===void 0){let l="This should not happen: Alias anchor was not resolved?";throw new ReferenceError(l)}if(r>=0&&(a.count+=1,a.aliasCount===0&&(a.aliasCount=lt(i,o,n)),a.count*a.aliasCount>r)){let l="Excessive alias count indicates a resource exhaustion attack";throw new ReferenceError(l)}return a.res}toString(e,t,n){let i=`*${this.source}`;if(e){if(Sa.anchorIsValid(this.source),e.options.verifyAliasOrder&&!e.anchors.has(this.source)){let r=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new Error(r)}if(e.implicitKey)return`${i} `}return i}};function lt(s,e,t){if(at.isAlias(e)){let n=e.resolve(s),i=t&&n&&t.get(n);return i?i.count*i.aliasCount:0}else if(at.isCollection(e)){let n=0;for(let i of e.items){let r=lt(s,i,t);r>n&&(n=r)}return n}else if(at.isPair(e)){let n=lt(s,e.key,t),i=lt(s,e.value,t);return Math.max(n,i)}return 1}mi.Alias=cs});var q=g(fs=>{"use strict";var ka=O(),Na=ot(),Aa=J(),Oa=s=>!s||typeof s!="function"&&typeof s!="object",Y=class extends Na.NodeBase{constructor(e){super(ka.SCALAR),this.value=e}toJSON(e,t){return t?.keep?this.value:Aa.toJS(this.value,e,t)}toString(){return String(this.value)}};Y.BLOCK_FOLDED="BLOCK_FOLDED";Y.BLOCK_LITERAL="BLOCK_LITERAL";Y.PLAIN="PLAIN";Y.QUOTE_DOUBLE="QUOTE_DOUBLE";Y.QUOTE_SINGLE="QUOTE_SINGLE";fs.Scalar=Y;fs.isScalarValue=Oa});var Oe=g(yi=>{"use strict";var Ea=Ae(),se=O(),gi=q(),Ta="tag:yaml.org,2002:";function qa(s,e,t){if(e){let n=t.filter(r=>r.tag===e),i=n.find(r=>!r.format)??n[0];if(!i)throw new Error(`Tag ${e} not found`);return i}return t.find(n=>n.identify?.(s)&&!n.format)}function La(s,e,t){if(se.isDocument(s)&&(s=s.contents),se.isNode(s))return s;if(se.isPair(s)){let f=t.schema[se.MAP].createNode?.(t.schema,null,t);return f.items.push(s),f}(s instanceof String||s instanceof Number||s instanceof Boolean||typeof BigInt<"u"&&s instanceof BigInt)&&(s=s.valueOf());let{aliasDuplicateObjects:n,onAnchor:i,onTagObj:r,schema:o,sourceObjects:a}=t,l;if(n&&s&&typeof s=="object"){if(l=a.get(s),l)return l.anchor||(l.anchor=i(s)),new Ea.Alias(l.anchor);l={anchor:null,node:null},a.set(s,l)}e?.startsWith("!!")&&(e=Ta+e.slice(2));let c=qa(s,e,o.tags);if(!c){if(s&&typeof s.toJSON=="function"&&(s=s.toJSON()),!s||typeof s!="object"){let f=new gi.Scalar(s);return l&&(l.node=f),f}c=s instanceof Map?o[se.MAP]:Symbol.iterator in Object(s)?o[se.SEQ]:o[se.MAP]}r&&(r(c),delete t.onTagObj);let d=c?.createNode?c.createNode(t.schema,s,t):typeof c?.nodeClass?.from=="function"?c.nodeClass.from(t.schema,s,t):new gi.Scalar(s);return e?d.tag=e:c.default||(d.tag=c.tag),l&&(l.node=d),d}yi.createNode=La});var ft=g(ct=>{"use strict";var Ia=Oe(),K=O(),Ca=ot();function us(s,e,t){let n=t;for(let i=e.length-1;i>=0;--i){let r=e[i];if(typeof r=="number"&&Number.isInteger(r)&&r>=0){let o=[];o[r]=n,n=o}else n=new Map([[r,n]])}return Ia.createNode(n,void 0,{aliasDuplicateObjects:!1,keepUndefined:!1,onAnchor:()=>{throw new Error("This should not happen, please report a bug.")},schema:s,sourceObjects:new Map})}var bi=s=>s==null||typeof s=="object"&&!!s[Symbol.iterator]().next().done,hs=class extends Ca.NodeBase{constructor(e,t){super(e),Object.defineProperty(this,"schema",{value:t,configurable:!0,enumerable:!1,writable:!0})}clone(e){let t=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return e&&(t.schema=e),t.items=t.items.map(n=>K.isNode(n)||K.isPair(n)?n.clone(e):n),this.range&&(t.range=this.range.slice()),t}addIn(e,t){if(bi(e))this.add(t);else{let[n,...i]=e,r=this.get(n,!0);if(K.isCollection(r))r.addIn(i,t);else if(r===void 0&&this.schema)this.set(n,us(this.schema,i,t));else throw new Error(`Expected YAML collection at ${n}. Remaining path: ${i}`)}}deleteIn(e){let[t,...n]=e;if(n.length===0)return this.delete(t);let i=this.get(t,!0);if(K.isCollection(i))return i.deleteIn(n);throw new Error(`Expected YAML collection at ${t}. Remaining path: ${n}`)}getIn(e,t){let[n,...i]=e,r=this.get(n,!0);return i.length===0?!t&&K.isScalar(r)?r.value:r:K.isCollection(r)?r.getIn(i,t):void 0}hasAllNullValues(e){return this.items.every(t=>{if(!K.isPair(t))return!1;let n=t.value;return n==null||e&&K.isScalar(n)&&n.value==null&&!n.commentBefore&&!n.comment&&!n.tag})}hasIn(e){let[t,...n]=e;if(n.length===0)return this.has(t);let i=this.get(t,!0);return K.isCollection(i)?i.hasIn(n):!1}setIn(e,t){let[n,...i]=e;if(i.length===0)this.set(n,t);else{let r=this.get(n,!0);if(K.isCollection(r))r.setIn(i,t);else if(r===void 0&&this.schema)this.set(n,us(this.schema,i,t));else throw new Error(`Expected YAML collection at ${n}. Remaining path: ${i}`)}}};ct.Collection=hs;ct.collectionFromPath=us;ct.isEmptyPath=bi});var Ee=g(ut=>{"use strict";var Pa=s=>s.replace(/^(?!$)(?: $)?/gm,"#");function ds(s,e){return/^\n+$/.test(s)?s.substring(1):e?s.replace(/^(?! *$)/gm,e):s}var Ma=(s,e,t)=>s.endsWith(`
`)?ds(t,e):t.includes(`
`)?`
`+ds(t,e):(s.endsWith(" ")?"":" ")+t;ut.indentComment=ds;ut.lineComment=Ma;ut.stringifyComment=Pa});var wi=g(Te=>{"use strict";var _a="flow",ps="block",ht="quoted";function $a(s,e,t="flow",{indentAtStart:n,lineWidth:i=80,minContentWidth:r=20,onFold:o,onOverflow:a}={}){if(!i||i<0)return s;i<r&&(r=0);let l=Math.max(1+r,1+i-e.length);if(s.length<=l)return s;let c=[],d={},f=i-e.length;typeof n=="number"&&(n>i-Math.max(2,r)?c.push(0):f=i-n);let u,m,y=!1,h=-1,p=-1,S=-1;t===ps&&(h=Si(s,h,e.length),h!==-1&&(f=h+l));for(let v;v=s[h+=1];){if(t===ht&&v==="\\"){switch(p=h,s[h+1]){case"x":h+=3;break;case"u":h+=5;break;case"U":h+=9;break;default:h+=1}S=h}if(v===`
`)t===ps&&(h=Si(s,h,e.length)),f=h+e.length+l,u=void 0;else{if(v===" "&&m&&m!==" "&&m!==`
`&&m!=="	"){let k=s[h+1];k&&k!==" "&&k!==`
`&&k!=="	"&&(u=h)}if(h>=f)if(u)c.push(u),f=u+l,u=void 0;else if(t===ht){for(;m===" "||m==="	";)m=v,v=s[h+=1],y=!0;let k=h>S+1?h-2:p-1;if(d[k])return s;c.push(k),d[k]=!0,f=k+l,u=void 0}else y=!0}m=v}if(y&&a&&a(),c.length===0)return s;o&&o();let w=s.slice(0,c[0]);for(let v=0;v<c.length;++v){let k=c[v],N=c[v+1]||s.length;k===0?w=`
${e}${s.slice(0,N)}`:(t===ht&&d[k]&&(w+=`${s[k]}\\`),w+=`
${e}${s.slice(k+1,N)}`)}return w}function Si(s,e,t){let n=e,i=e+1,r=s[i];for(;r===" "||r==="	";)if(e<i+t)r=s[++e];else{do r=s[++e];while(r&&r!==`
`);n=e,i=e+1,r=s[i]}return n}Te.FOLD_BLOCK=ps;Te.FOLD_FLOW=_a;Te.FOLD_QUOTED=ht;Te.foldFlowLines=$a});var Le=g(vi=>{"use strict";var B=q(),G=wi(),pt=(s,e)=>({indentAtStart:e?s.indent.length:s.indentAtStart,lineWidth:s.options.lineWidth,minContentWidth:s.options.minContentWidth}),mt=s=>/^(%|---|\.\.\.)/m.test(s);function Da(s,e,t){if(!e||e<0)return!1;let n=e-t,i=s.length;if(i<=n)return!1;for(let r=0,o=0;r<i;++r)if(s[r]===`
`){if(r-o>n)return!0;if(o=r+1,i-o<=n)return!1}return!0}function qe(s,e){let t=JSON.stringify(s);if(e.options.doubleQuotedAsJSON)return t;let{implicitKey:n}=e,i=e.options.doubleQuotedMinMultiLineLength,r=e.indent||(mt(s)?"  ":""),o="",a=0;for(let l=0,c=t[l];c;c=t[++l])if(c===" "&&t[l+1]==="\\"&&t[l+2]==="n"&&(o+=t.slice(a,l)+"\\ ",l+=1,a=l,c="\\"),c==="\\")switch(t[l+1]){case"u":{o+=t.slice(a,l);let d=t.substr(l+2,4);switch(d){case"0000":o+="\\0";break;case"0007":o+="\\a";break;case"000b":o+="\\v";break;case"001b":o+="\\e";break;case"0085":o+="\\N";break;case"00a0":o+="\\_";break;case"2028":o+="\\L";break;case"2029":o+="\\P";break;default:d.substr(0,2)==="00"?o+="\\x"+d.substr(2):o+=t.substr(l,6)}l+=5,a=l+1}break;case"n":if(n||t[l+2]==='"'||t.length<i)l+=1;else{for(o+=t.slice(a,l)+`

`;t[l+2]==="\\"&&t[l+3]==="n"&&t[l+4]!=='"';)o+=`
`,l+=2;o+=r,t[l+2]===" "&&(o+="\\"),l+=1,a=l+1}break;default:l+=1}return o=a?o+t.slice(a):t,n?o:G.foldFlowLines(o,r,G.FOLD_QUOTED,pt(e,!1))}function ms(s,e){if(e.options.singleQuote===!1||e.implicitKey&&s.includes(`
`)||/[ \t]\n|\n[ \t]/.test(s))return qe(s,e);let t=e.indent||(mt(s)?"  ":""),n="'"+s.replace(/'/g,"''").replace(/\n+/g,`$&
${t}`)+"'";return e.implicitKey?n:G.foldFlowLines(n,t,G.FOLD_FLOW,pt(e,!1))}function he(s,e){let{singleQuote:t}=e.options,n;if(t===!1)n=qe;else{let i=s.includes('"'),r=s.includes("'");i&&!r?n=ms:r&&!i?n=qe:n=t?ms:qe}return n(s,e)}var gs;try{gs=new RegExp(`(^|(?<!
))
+(?!
|$)`,"g")}catch{gs=/\n+(?!\n|$)/g}function dt({comment:s,type:e,value:t},n,i,r){let{blockQuote:o,commentString:a,lineWidth:l}=n.options;if(!o||/\n[\t ]+$/.test(t)||/^\s*$/.test(t))return he(t,n);let c=n.indent||(n.forceBlockIndent||mt(t)?"  ":""),d=o==="literal"?!0:o==="folded"||e===B.Scalar.BLOCK_FOLDED?!1:e===B.Scalar.BLOCK_LITERAL?!0:!Da(t,l,c.length);if(!t)return d?`|
`:`>
`;let f,u;for(u=t.length;u>0;--u){let N=t[u-1];if(N!==`
`&&N!=="	"&&N!==" ")break}let m=t.substring(u),y=m.indexOf(`
`);y===-1?f="-":t===m||y!==m.length-1?(f="+",r&&r()):f="",m&&(t=t.slice(0,-m.length),m[m.length-1]===`
`&&(m=m.slice(0,-1)),m=m.replace(gs,`$&${c}`));let h=!1,p,S=-1;for(p=0;p<t.length;++p){let N=t[p];if(N===" ")h=!0;else if(N===`
`)S=p;else break}let w=t.substring(0,S<p?S+1:p);w&&(t=t.substring(w.length),w=w.replace(/\n+/g,`$&${c}`));let k=(h?c?"2":"1":"")+f;if(s&&(k+=" "+a(s.replace(/ ?[\r\n]+/g," ")),i&&i()),!d){let N=t.replace(/\n+/g,`
$&`).replace(/(?:^|\n)([\t ].*)(?:([\n\t ]*)\n(?![\n\t ]))?/g,"$1$2").replace(/\n+/g,`$&${c}`),A=!1,T=pt(n,!0);o!=="folded"&&e!==B.Scalar.BLOCK_FOLDED&&(T.onOverflow=()=>{A=!0});let b=G.foldFlowLines(`${w}${N}${m}`,c,G.FOLD_BLOCK,T);if(!A)return`>${k}
${c}${b}`}return t=t.replace(/\n+/g,`$&${c}`),`|${k}
${c}${w}${t}${m}`}function Ba(s,e,t,n){let{type:i,value:r}=s,{actualString:o,implicitKey:a,indent:l,indentStep:c,inFlow:d}=e;if(a&&r.includes(`
`)||d&&/[[\]{},]/.test(r))return he(r,e);if(!r||/^[\n\t ,[\]{}#&*!|>'"%@`]|^[?-]$|^[?-][ \t]|[\n:][ \t]|[ \t]\n|[\n\t ]#|[\n\t :]$/.test(r))return a||d||!r.includes(`
`)?he(r,e):dt(s,e,t,n);if(!a&&!d&&i!==B.Scalar.PLAIN&&r.includes(`
`))return dt(s,e,t,n);if(mt(r)){if(l==="")return e.forceBlockIndent=!0,dt(s,e,t,n);if(a&&l===c)return he(r,e)}let f=r.replace(/\n+/g,`$&
${l}`);if(o){let u=h=>h.default&&h.tag!=="tag:yaml.org,2002:str"&&h.test?.test(f),{compat:m,tags:y}=e.doc.schema;if(y.some(u)||m?.some(u))return he(r,e)}return a?f:G.foldFlowLines(f,l,G.FOLD_FLOW,pt(e,!1))}function Fa(s,e,t,n){let{implicitKey:i,inFlow:r}=e,o=typeof s.value=="string"?s:Object.assign({},s,{value:String(s.value)}),{type:a}=s;a!==B.Scalar.QUOTE_DOUBLE&&/[\x00-\x08\x0b-\x1f\x7f-\x9f\u{D800}-\u{DFFF}]/u.test(o.value)&&(a=B.Scalar.QUOTE_DOUBLE);let l=d=>{switch(d){case B.Scalar.BLOCK_FOLDED:case B.Scalar.BLOCK_LITERAL:return i||r?he(o.value,e):dt(o,e,t,n);case B.Scalar.QUOTE_DOUBLE:return qe(o.value,e);case B.Scalar.QUOTE_SINGLE:return ms(o.value,e);case B.Scalar.PLAIN:return Ba(o,e,t,n);default:return null}},c=l(a);if(c===null){let{defaultKeyType:d,defaultStringType:f}=e.options,u=i&&d||f;if(c=l(u),c===null)throw new Error(`Unsupported default string type ${u}`)}return c}vi.stringifyString=Fa});var Ie=g(ys=>{"use strict";var Ra=rt(),W=O(),Ka=Ee(),ja=Le();function Va(s,e){let t=Object.assign({blockQuote:!0,commentString:Ka.stringifyComment,defaultKeyType:null,defaultStringType:"PLAIN",directives:null,doubleQuotedAsJSON:!1,doubleQuotedMinMultiLineLength:40,falseStr:"false",flowCollectionPadding:!0,indentSeq:!0,lineWidth:80,minContentWidth:20,nullStr:"null",simpleKeys:!1,singleQuote:null,trueStr:"true",verifyAliasOrder:!0},s.schema.toStringOptions,e),n;switch(t.collectionStyle){case"block":n=!1;break;case"flow":n=!0;break;default:n=null}return{anchors:new Set,doc:s,flowCollectionPadding:t.flowCollectionPadding?" ":"",indent:"",indentStep:typeof t.indent=="number"?" ".repeat(t.indent):"  ",inFlow:n,options:t}}function xa(s,e){if(e.tag){let i=s.filter(r=>r.tag===e.tag);if(i.length>0)return i.find(r=>r.format===e.format)??i[0]}let t,n;if(W.isScalar(e)){n=e.value;let i=s.filter(r=>r.identify?.(n));if(i.length>1){let r=i.filter(o=>o.test);r.length>0&&(i=r)}t=i.find(r=>r.format===e.format)??i.find(r=>!r.format)}else n=e,t=s.find(i=>i.nodeClass&&n instanceof i.nodeClass);if(!t){let i=n?.constructor?.name??typeof n;throw new Error(`Tag not resolved for ${i} value`)}return t}function Ua(s,e,{anchors:t,doc:n}){if(!n.directives)return"";let i=[],r=(W.isScalar(s)||W.isCollection(s))&&s.anchor;r&&Ra.anchorIsValid(r)&&(t.add(r),i.push(`&${r}`));let o=s.tag?s.tag:e.default?null:e.tag;return o&&i.push(n.directives.tagString(o)),i.join(" ")}function Ja(s,e,t,n){if(W.isPair(s))return s.toString(e,t,n);if(W.isAlias(s)){if(e.doc.directives)return s.toString(e);if(e.resolvedAliases?.has(s))throw new TypeError("Cannot stringify circular structure without alias nodes");e.resolvedAliases?e.resolvedAliases.add(s):e.resolvedAliases=new Set([s]),s=s.resolve(e.doc)}let i,r=W.isNode(s)?s:e.doc.createNode(s,{onTagObj:l=>i=l});i||(i=xa(e.doc.schema.tags,r));let o=Ua(r,i,e);o.length>0&&(e.indentAtStart=(e.indentAtStart??0)+o.length+1);let a=typeof i.stringify=="function"?i.stringify(r,e,t,n):W.isScalar(r)?ja.stringifyString(r,e,t,n):r.toString(e,t,n);return o?W.isScalar(r)||a[0]==="{"||a[0]==="["?`${o} ${a}`:`${o}
${e.indent}${a}`:a}ys.createStringifyContext=Va;ys.stringify=Ja});var Oi=g(Ai=>{"use strict";var x=O(),ki=q(),Ni=Ie(),Ce=Ee();function Ya({key:s,value:e},t,n,i){let{allNullValues:r,doc:o,indent:a,indentStep:l,options:{commentString:c,indentSeq:d,simpleKeys:f}}=t,u=x.isNode(s)&&s.comment||null;if(f){if(u)throw new Error("With simple keys, key nodes cannot have comments");if(x.isCollection(s)||!x.isNode(s)&&typeof s=="object"){let T="With simple keys, collection cannot be used as a key value";throw new Error(T)}}let m=!f&&(!s||u&&e==null&&!t.inFlow||x.isCollection(s)||(x.isScalar(s)?s.type===ki.Scalar.BLOCK_FOLDED||s.type===ki.Scalar.BLOCK_LITERAL:typeof s=="object"));t=Object.assign({},t,{allNullValues:!1,implicitKey:!m&&(f||!r),indent:a+l});let y=!1,h=!1,p=Ni.stringify(s,t,()=>y=!0,()=>h=!0);if(!m&&!t.inFlow&&p.length>1024){if(f)throw new Error("With simple keys, single line scalar must not span more than 1024 characters");m=!0}if(t.inFlow){if(r||e==null)return y&&n&&n(),p===""?"?":m?`? ${p}`:p}else if(r&&!f||e==null&&m)return p=`? ${p}`,u&&!y?p+=Ce.lineComment(p,t.indent,c(u)):h&&i&&i(),p;y&&(u=null),m?(u&&(p+=Ce.lineComment(p,t.indent,c(u))),p=`? ${p}
${a}:`):(p=`${p}:`,u&&(p+=Ce.lineComment(p,t.indent,c(u))));let S,w,v;x.isNode(e)?(S=!!e.spaceBefore,w=e.commentBefore,v=e.comment):(S=!1,w=null,v=null,e&&typeof e=="object"&&(e=o.createNode(e))),t.implicitKey=!1,!m&&!u&&x.isScalar(e)&&(t.indentAtStart=p.length+1),h=!1,!d&&l.length>=2&&!t.inFlow&&!m&&x.isSeq(e)&&!e.flow&&!e.tag&&!e.anchor&&(t.indent=t.indent.substring(2));let k=!1,N=Ni.stringify(e,t,()=>k=!0,()=>h=!0),A=" ";if(u||S||w){if(A=S?`
`:"",w){let T=c(w);A+=`
${Ce.indentComment(T,t.indent)}`}N===""&&!t.inFlow?A===`
`&&(A=`

`):A+=`
${t.indent}`}else if(!m&&x.isCollection(e)){let T=N[0],b=N.indexOf(`
`),L=b!==-1,U=t.inFlow??e.flow??e.items.length===0;if(L||!U){let ce=!1;if(L&&(T==="&"||T==="!")){let I=N.indexOf(" ");T==="&"&&I!==-1&&I<b&&N[I+1]==="!"&&(I=N.indexOf(" ",I+1)),(I===-1||b<I)&&(ce=!0)}ce||(A=`
${t.indent}`)}}else(N===""||N[0]===`
`)&&(A="");return p+=A+N,t.inFlow?k&&n&&n():v&&!k?p+=Ce.lineComment(p,t.indent,c(v)):h&&i&&i(),p}Ai.stringifyPair=Ya});var Ss=g(bs=>{"use strict";var Ei=require("node:process");function Ga(s,...e){s==="debug"&&console.log(...e)}function Wa(s,e){(s==="debug"||s==="warn")&&(typeof Ei.emitWarning=="function"?Ei.emitWarning(e):console.warn(e))}bs.debug=Ga;bs.warn=Wa});var St=g(bt=>{"use strict";var Pe=O(),Ti=q(),gt="<<",yt={identify:s=>s===gt||typeof s=="symbol"&&s.description===gt,default:"key",tag:"tag:yaml.org,2002:merge",test:/^<<$/,resolve:()=>Object.assign(new Ti.Scalar(Symbol(gt)),{addToJSMap:qi}),stringify:()=>gt},Qa=(s,e)=>(yt.identify(e)||Pe.isScalar(e)&&(!e.type||e.type===Ti.Scalar.PLAIN)&&yt.identify(e.value))&&s?.doc.schema.tags.some(t=>t.tag===yt.tag&&t.default);function qi(s,e,t){if(t=s&&Pe.isAlias(t)?t.resolve(s.doc):t,Pe.isSeq(t))for(let n of t.items)ws(s,e,n);else if(Array.isArray(t))for(let n of t)ws(s,e,n);else ws(s,e,t)}function ws(s,e,t){let n=s&&Pe.isAlias(t)?t.resolve(s.doc):t;if(!Pe.isMap(n))throw new Error("Merge sources must be maps or map aliases");let i=n.toJSON(null,s,Map);for(let[r,o]of i)e instanceof Map?e.has(r)||e.set(r,o):e instanceof Set?e.add(r):Object.prototype.hasOwnProperty.call(e,r)||Object.defineProperty(e,r,{value:o,writable:!0,enumerable:!0,configurable:!0});return e}bt.addMergeToJSMap=qi;bt.isMergeKey=Qa;bt.merge=yt});var ks=g(Ci=>{"use strict";var Ha=Ss(),Li=St(),Xa=Ie(),Ii=O(),vs=J();function za(s,e,{key:t,value:n}){if(Ii.isNode(t)&&t.addToJSMap)t.addToJSMap(s,e,n);else if(Li.isMergeKey(s,t))Li.addMergeToJSMap(s,e,n);else{let i=vs.toJS(t,"",s);if(e instanceof Map)e.set(i,vs.toJS(n,i,s));else if(e instanceof Set)e.add(i);else{let r=Za(t,i,s),o=vs.toJS(n,r,s);r in e?Object.defineProperty(e,r,{value:o,writable:!0,enumerable:!0,configurable:!0}):e[r]=o}}return e}function Za(s,e,t){if(e===null)return"";if(typeof e!="object")return String(e);if(Ii.isNode(s)&&t?.doc){let n=Xa.createStringifyContext(t.doc,{});n.anchors=new Set;for(let r of t.anchors.keys())n.anchors.add(r.anchor);n.inFlow=!0,n.inStringifyKey=!0;let i=s.toString(n);if(!t.mapKeyWarned){let r=JSON.stringify(i);r.length>40&&(r=r.substring(0,36)+'..."'),Ha.warn(t.doc.options.logLevel,`Keys with collection values will be stringified due to JS Object restrictions: ${r}. Set mapAsMap: true to use object keys.`),t.mapKeyWarned=!0}return i}return JSON.stringify(e)}Ci.addPairToJSMap=za});var Q=g(Ns=>{"use strict";var Pi=Oe(),el=Oi(),tl=ks(),wt=O();function sl(s,e,t){let n=Pi.createNode(s,void 0,t),i=Pi.createNode(e,void 0,t);return new vt(n,i)}var vt=class s{constructor(e,t=null){Object.defineProperty(this,wt.NODE_TYPE,{value:wt.PAIR}),this.key=e,this.value=t}clone(e){let{key:t,value:n}=this;return wt.isNode(t)&&(t=t.clone(e)),wt.isNode(n)&&(n=n.clone(e)),new s(t,n)}toJSON(e,t){let n=t?.mapAsMap?new Map:{};return tl.addPairToJSMap(t,n,this)}toString(e,t,n){return e?.doc?el.stringifyPair(this,e,t,n):JSON.stringify(this)}};Ns.Pair=vt;Ns.createPair=sl});var As=g(_i=>{"use strict";var ne=O(),Mi=Ie(),kt=Ee();function nl(s,e,t){return(e.inFlow??s.flow?rl:il)(s,e,t)}function il({comment:s,items:e},t,{blockItemPrefix:n,flowChars:i,itemIndent:r,onChompKeep:o,onComment:a}){let{indent:l,options:{commentString:c}}=t,d=Object.assign({},t,{indent:r,type:null}),f=!1,u=[];for(let y=0;y<e.length;++y){let h=e[y],p=null;if(ne.isNode(h))!f&&h.spaceBefore&&u.push(""),Nt(t,u,h.commentBefore,f),h.comment&&(p=h.comment);else if(ne.isPair(h)){let w=ne.isNode(h.key)?h.key:null;w&&(!f&&w.spaceBefore&&u.push(""),Nt(t,u,w.commentBefore,f))}f=!1;let S=Mi.stringify(h,d,()=>p=null,()=>f=!0);p&&(S+=kt.lineComment(S,r,c(p))),f&&p&&(f=!1),u.push(n+S)}let m;if(u.length===0)m=i.start+i.end;else{m=u[0];for(let y=1;y<u.length;++y){let h=u[y];m+=h?`
${l}${h}`:`
`}}return s?(m+=`
`+kt.indentComment(c(s),l),a&&a()):f&&o&&o(),m}function rl({items:s},e,{flowChars:t,itemIndent:n}){let{indent:i,indentStep:r,flowCollectionPadding:o,options:{commentString:a}}=e;n+=r;let l=Object.assign({},e,{indent:n,inFlow:!0,type:null}),c=!1,d=0,f=[];for(let y=0;y<s.length;++y){let h=s[y],p=null;if(ne.isNode(h))h.spaceBefore&&f.push(""),Nt(e,f,h.commentBefore,!1),h.comment&&(p=h.comment);else if(ne.isPair(h)){let w=ne.isNode(h.key)?h.key:null;w&&(w.spaceBefore&&f.push(""),Nt(e,f,w.commentBefore,!1),w.comment&&(c=!0));let v=ne.isNode(h.value)?h.value:null;v?(v.comment&&(p=v.comment),v.commentBefore&&(c=!0)):h.value==null&&w?.comment&&(p=w.comment)}p&&(c=!0);let S=Mi.stringify(h,l,()=>p=null);y<s.length-1&&(S+=","),p&&(S+=kt.lineComment(S,n,a(p))),!c&&(f.length>d||S.includes(`
`))&&(c=!0),f.push(S),d=f.length}let{start:u,end:m}=t;if(f.length===0)return u+m;if(!c){let y=f.reduce((h,p)=>h+p.length+2,2);c=e.options.lineWidth>0&&y>e.options.lineWidth}if(c){let y=u;for(let h of f)y+=h?`
${r}${i}${h}`:`
`;return`${y}
${i}${m}`}else return`${u}${o}${f.join(" ")}${o}${m}`}function Nt({indent:s,options:{commentString:e}},t,n,i){if(n&&i&&(n=n.replace(/^\n+/,"")),n){let r=kt.indentComment(e(n),s);t.push(r.trimStart())}}_i.stringifyCollection=nl});var X=g(Es=>{"use strict";var ol=As(),al=ks(),ll=ft(),H=O(),At=Q(),cl=q();function Me(s,e){let t=H.isScalar(e)?e.value:e;for(let n of s)if(H.isPair(n)&&(n.key===e||n.key===t||H.isScalar(n.key)&&n.key.value===t))return n}var Os=class extends ll.Collection{static get tagName(){return"tag:yaml.org,2002:map"}constructor(e){super(H.MAP,e),this.items=[]}static from(e,t,n){let{keepUndefined:i,replacer:r}=n,o=new this(e),a=(l,c)=>{if(typeof r=="function")c=r.call(t,l,c);else if(Array.isArray(r)&&!r.includes(l))return;(c!==void 0||i)&&o.items.push(At.createPair(l,c,n))};if(t instanceof Map)for(let[l,c]of t)a(l,c);else if(t&&typeof t=="object")for(let l of Object.keys(t))a(l,t[l]);return typeof e.sortMapEntries=="function"&&o.items.sort(e.sortMapEntries),o}add(e,t){let n;H.isPair(e)?n=e:!e||typeof e!="object"||!("key"in e)?n=new At.Pair(e,e?.value):n=new At.Pair(e.key,e.value);let i=Me(this.items,n.key),r=this.schema?.sortMapEntries;if(i){if(!t)throw new Error(`Key ${n.key} already set`);H.isScalar(i.value)&&cl.isScalarValue(n.value)?i.value.value=n.value:i.value=n.value}else if(r){let o=this.items.findIndex(a=>r(n,a)<0);o===-1?this.items.push(n):this.items.splice(o,0,n)}else this.items.push(n)}delete(e){let t=Me(this.items,e);return t?this.items.splice(this.items.indexOf(t),1).length>0:!1}get(e,t){let i=Me(this.items,e)?.value;return(!t&&H.isScalar(i)?i.value:i)??void 0}has(e){return!!Me(this.items,e)}set(e,t){this.add(new At.Pair(e,t),!0)}toJSON(e,t,n){let i=n?new n:t?.mapAsMap?new Map:{};t?.onCreate&&t.onCreate(i);for(let r of this.items)al.addPairToJSMap(t,i,r);return i}toString(e,t,n){if(!e)return JSON.stringify(this);for(let i of this.items)if(!H.isPair(i))throw new Error(`Map items must all be pairs; found ${JSON.stringify(i)} instead`);return!e.allNullValues&&this.hasAllNullValues(!1)&&(e=Object.assign({},e,{allNullValues:!0})),ol.stringifyCollection(this,e,{blockItemPrefix:"",flowChars:{start:"{",end:"}"},itemIndent:e.indent||"",onChompKeep:n,onComment:t})}};Es.YAMLMap=Os;Es.findPair=Me});var de=g(Di=>{"use strict";var fl=O(),$i=X(),ul={collection:"map",default:!0,nodeClass:$i.YAMLMap,tag:"tag:yaml.org,2002:map",resolve(s,e){return fl.isMap(s)||e("Expected a mapping for this tag"),s},createNode:(s,e,t)=>$i.YAMLMap.from(s,e,t)};Di.map=ul});var z=g(Bi=>{"use strict";var hl=Oe(),dl=As(),pl=ft(),Et=O(),ml=q(),gl=J(),Ts=class extends pl.Collection{static get tagName(){return"tag:yaml.org,2002:seq"}constructor(e){super(Et.SEQ,e),this.items=[]}add(e){this.items.push(e)}delete(e){let t=Ot(e);return typeof t!="number"?!1:this.items.splice(t,1).length>0}get(e,t){let n=Ot(e);if(typeof n!="number")return;let i=this.items[n];return!t&&Et.isScalar(i)?i.value:i}has(e){let t=Ot(e);return typeof t=="number"&&t<this.items.length}set(e,t){let n=Ot(e);if(typeof n!="number")throw new Error(`Expected a valid index, not ${e}.`);let i=this.items[n];Et.isScalar(i)&&ml.isScalarValue(t)?i.value=t:this.items[n]=t}toJSON(e,t){let n=[];t?.onCreate&&t.onCreate(n);let i=0;for(let r of this.items)n.push(gl.toJS(r,String(i++),t));return n}toString(e,t,n){return e?dl.stringifyCollection(this,e,{blockItemPrefix:"- ",flowChars:{start:"[",end:"]"},itemIndent:(e.indent||"")+"  ",onChompKeep:n,onComment:t}):JSON.stringify(this)}static from(e,t,n){let{replacer:i}=n,r=new this(e);if(t&&Symbol.iterator in Object(t)){let o=0;for(let a of t){if(typeof i=="function"){let l=t instanceof Set?a:String(o++);a=i.call(t,l,a)}r.items.push(hl.createNode(a,void 0,n))}}return r}};function Ot(s){let e=Et.isScalar(s)?s.value:s;return e&&typeof e=="string"&&(e=Number(e)),typeof e=="number"&&Number.isInteger(e)&&e>=0?e:null}Bi.YAMLSeq=Ts});var pe=g(Ri=>{"use strict";var yl=O(),Fi=z(),bl={collection:"seq",default:!0,nodeClass:Fi.YAMLSeq,tag:"tag:yaml.org,2002:seq",resolve(s,e){return yl.isSeq(s)||e("Expected a sequence for this tag"),s},createNode:(s,e,t)=>Fi.YAMLSeq.from(s,e,t)};Ri.seq=bl});var _e=g(Ki=>{"use strict";var Sl=Le(),wl={identify:s=>typeof s=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:s=>s,stringify(s,e,t,n){return e=Object.assign({actualString:!0},e),Sl.stringifyString(s,e,t,n)}};Ki.string=wl});var Tt=g(xi=>{"use strict";var ji=q(),Vi={identify:s=>s==null,createNode:()=>new ji.Scalar(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:()=>new ji.Scalar(null),stringify:({source:s},e)=>typeof s=="string"&&Vi.test.test(s)?s:e.options.nullStr};xi.nullTag=Vi});var qs=g(Ji=>{"use strict";var vl=q(),Ui={identify:s=>typeof s=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,resolve:s=>new vl.Scalar(s[0]==="t"||s[0]==="T"),stringify({source:s,value:e},t){if(s&&Ui.test.test(s)){let n=s[0]==="t"||s[0]==="T";if(e===n)return s}return e?t.options.trueStr:t.options.falseStr}};Ji.boolTag=Ui});var me=g(Yi=>{"use strict";function kl({format:s,minFractionDigits:e,tag:t,value:n}){if(typeof n=="bigint")return String(n);let i=typeof n=="number"?n:Number(n);if(!isFinite(i))return isNaN(i)?".nan":i<0?"-.inf":".inf";let r=JSON.stringify(n);if(!s&&e&&(!t||t==="tag:yaml.org,2002:float")&&/^\d/.test(r)){let o=r.indexOf(".");o<0&&(o=r.length,r+=".");let a=e-(r.length-o-1);for(;a-- >0;)r+="0"}return r}Yi.stringifyNumber=kl});var Is=g(qt=>{"use strict";var Nl=q(),Ls=me(),Al={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:s=>s.slice(-3).toLowerCase()==="nan"?NaN:s[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:Ls.stringifyNumber},Ol={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:\.[0-9]+|[0-9]+(?:\.[0-9]*)?)[eE][-+]?[0-9]+$/,resolve:s=>parseFloat(s),stringify(s){let e=Number(s.value);return isFinite(e)?e.toExponential():Ls.stringifyNumber(s)}},El={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:\.[0-9]+|[0-9]+\.[0-9]*)$/,resolve(s){let e=new Nl.Scalar(parseFloat(s)),t=s.indexOf(".");return t!==-1&&s[s.length-1]==="0"&&(e.minFractionDigits=s.length-t-1),e},stringify:Ls.stringifyNumber};qt.float=El;qt.floatExp=Ol;qt.floatNaN=Al});var Ps=g(It=>{"use strict";var Gi=me(),Lt=s=>typeof s=="bigint"||Number.isInteger(s),Cs=(s,e,t,{intAsBigInt:n})=>n?BigInt(s):parseInt(s.substring(e),t);function Wi(s,e,t){let{value:n}=s;return Lt(n)&&n>=0?t+n.toString(e):Gi.stringifyNumber(s)}var Tl={identify:s=>Lt(s)&&s>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^0o[0-7]+$/,resolve:(s,e,t)=>Cs(s,2,8,t),stringify:s=>Wi(s,8,"0o")},ql={identify:Lt,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9]+$/,resolve:(s,e,t)=>Cs(s,0,10,t),stringify:Gi.stringifyNumber},Ll={identify:s=>Lt(s)&&s>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x[0-9a-fA-F]+$/,resolve:(s,e,t)=>Cs(s,2,16,t),stringify:s=>Wi(s,16,"0x")};It.int=ql;It.intHex=Ll;It.intOct=Tl});var Hi=g(Qi=>{"use strict";var Il=de(),Cl=Tt(),Pl=pe(),Ml=_e(),_l=qs(),Ms=Is(),_s=Ps(),$l=[Il.map,Pl.seq,Ml.string,Cl.nullTag,_l.boolTag,_s.intOct,_s.int,_s.intHex,Ms.floatNaN,Ms.floatExp,Ms.float];Qi.schema=$l});var Zi=g(zi=>{"use strict";var Dl=q(),Bl=de(),Fl=pe();function Xi(s){return typeof s=="bigint"||Number.isInteger(s)}var Ct=({value:s})=>JSON.stringify(s),Rl=[{identify:s=>typeof s=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:s=>s,stringify:Ct},{identify:s=>s==null,createNode:()=>new Dl.Scalar(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^null$/,resolve:()=>null,stringify:Ct},{identify:s=>typeof s=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^true$|^false$/,resolve:s=>s==="true",stringify:Ct},{identify:Xi,default:!0,tag:"tag:yaml.org,2002:int",test:/^-?(?:0|[1-9][0-9]*)$/,resolve:(s,e,{intAsBigInt:t})=>t?BigInt(s):parseInt(s,10),stringify:({value:s})=>Xi(s)?s.toString():JSON.stringify(s)},{identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^-?(?:0|[1-9][0-9]*)(?:\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,resolve:s=>parseFloat(s),stringify:Ct}],Kl={default:!0,tag:"",test:/^/,resolve(s,e){return e(`Unresolved plain scalar ${JSON.stringify(s)}`),s}},jl=[Bl.map,Fl.seq].concat(Rl,Kl);zi.schema=jl});var Ds=g(er=>{"use strict";var $e=require("node:buffer"),$s=q(),Vl=Le(),xl={identify:s=>s instanceof Uint8Array,default:!1,tag:"tag:yaml.org,2002:binary",resolve(s,e){if(typeof $e.Buffer=="function")return $e.Buffer.from(s,"base64");if(typeof atob=="function"){let t=atob(s.replace(/[\n\r]/g,"")),n=new Uint8Array(t.length);for(let i=0;i<t.length;++i)n[i]=t.charCodeAt(i);return n}else return e("This environment does not support reading binary tags; either Buffer or atob is required"),s},stringify({comment:s,type:e,value:t},n,i,r){let o=t,a;if(typeof $e.Buffer=="function")a=o instanceof $e.Buffer?o.toString("base64"):$e.Buffer.from(o.buffer).toString("base64");else if(typeof btoa=="function"){let l="";for(let c=0;c<o.length;++c)l+=String.fromCharCode(o[c]);a=btoa(l)}else throw new Error("This environment does not support writing binary tags; either Buffer or btoa is required");if(e||(e=$s.Scalar.BLOCK_LITERAL),e!==$s.Scalar.QUOTE_DOUBLE){let l=Math.max(n.options.lineWidth-n.indent.length,n.options.minContentWidth),c=Math.ceil(a.length/l),d=new Array(c);for(let f=0,u=0;f<c;++f,u+=l)d[f]=a.substr(u,l);a=d.join(e===$s.Scalar.BLOCK_LITERAL?`
`:" ")}return Vl.stringifyString({comment:s,type:e,value:a},n,i,r)}};er.binary=xl});var _t=g(Mt=>{"use strict";var Pt=O(),Bs=Q(),Ul=q(),Jl=z();function tr(s,e){if(Pt.isSeq(s))for(let t=0;t<s.items.length;++t){let n=s.items[t];if(!Pt.isPair(n)){if(Pt.isMap(n)){n.items.length>1&&e("Each pair must have its own sequence indicator");let i=n.items[0]||new Bs.Pair(new Ul.Scalar(null));if(n.commentBefore&&(i.key.commentBefore=i.key.commentBefore?`${n.commentBefore}
${i.key.commentBefore}`:n.commentBefore),n.comment){let r=i.value??i.key;r.comment=r.comment?`${n.comment}
${r.comment}`:n.comment}n=i}s.items[t]=Pt.isPair(n)?n:new Bs.Pair(n)}}else e("Expected a sequence for this tag");return s}function sr(s,e,t){let{replacer:n}=t,i=new Jl.YAMLSeq(s);i.tag="tag:yaml.org,2002:pairs";let r=0;if(e&&Symbol.iterator in Object(e))for(let o of e){typeof n=="function"&&(o=n.call(e,String(r++),o));let a,l;if(Array.isArray(o))if(o.length===2)a=o[0],l=o[1];else throw new TypeError(`Expected [key, value] tuple: ${o}`);else if(o&&o instanceof Object){let c=Object.keys(o);if(c.length===1)a=c[0],l=o[a];else throw new TypeError(`Expected tuple with one key, not ${c.length} keys`)}else a=o;i.items.push(Bs.createPair(a,l,t))}return i}var Yl={collection:"seq",default:!1,tag:"tag:yaml.org,2002:pairs",resolve:tr,createNode:sr};Mt.createPairs=sr;Mt.pairs=Yl;Mt.resolvePairs=tr});var Ks=g(Rs=>{"use strict";var nr=O(),Fs=J(),De=X(),Gl=z(),ir=_t(),ie=class s extends Gl.YAMLSeq{constructor(){super(),this.add=De.YAMLMap.prototype.add.bind(this),this.delete=De.YAMLMap.prototype.delete.bind(this),this.get=De.YAMLMap.prototype.get.bind(this),this.has=De.YAMLMap.prototype.has.bind(this),this.set=De.YAMLMap.prototype.set.bind(this),this.tag=s.tag}toJSON(e,t){if(!t)return super.toJSON(e);let n=new Map;t?.onCreate&&t.onCreate(n);for(let i of this.items){let r,o;if(nr.isPair(i)?(r=Fs.toJS(i.key,"",t),o=Fs.toJS(i.value,r,t)):r=Fs.toJS(i,"",t),n.has(r))throw new Error("Ordered maps must not include duplicate keys");n.set(r,o)}return n}static from(e,t,n){let i=ir.createPairs(e,t,n),r=new this;return r.items=i.items,r}};ie.tag="tag:yaml.org,2002:omap";var Wl={collection:"seq",identify:s=>s instanceof Map,nodeClass:ie,default:!1,tag:"tag:yaml.org,2002:omap",resolve(s,e){let t=ir.resolvePairs(s,e),n=[];for(let{key:i}of t.items)nr.isScalar(i)&&(n.includes(i.value)?e(`Ordered maps must not include duplicate keys: ${i.value}`):n.push(i.value));return Object.assign(new ie,t)},createNode:(s,e,t)=>ie.from(s,e,t)};Rs.YAMLOMap=ie;Rs.omap=Wl});var cr=g(js=>{"use strict";var rr=q();function or({value:s,source:e},t){return e&&(s?ar:lr).test.test(e)?e:s?t.options.trueStr:t.options.falseStr}var ar={identify:s=>s===!0,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,resolve:()=>new rr.Scalar(!0),stringify:or},lr={identify:s=>s===!1,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,resolve:()=>new rr.Scalar(!1),stringify:or};js.falseTag=lr;js.trueTag=ar});var fr=g($t=>{"use strict";var Ql=q(),Vs=me(),Hl={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:s=>s.slice(-3).toLowerCase()==="nan"?NaN:s[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:Vs.stringifyNumber},Xl={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:[0-9][0-9_]*)?(?:\.[0-9_]*)?[eE][-+]?[0-9]+$/,resolve:s=>parseFloat(s.replace(/_/g,"")),stringify(s){let e=Number(s.value);return isFinite(e)?e.toExponential():Vs.stringifyNumber(s)}},zl={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:[0-9][0-9_]*)?\.[0-9_]*$/,resolve(s){let e=new Ql.Scalar(parseFloat(s.replace(/_/g,""))),t=s.indexOf(".");if(t!==-1){let n=s.substring(t+1).replace(/_/g,"");n[n.length-1]==="0"&&(e.minFractionDigits=n.length)}return e},stringify:Vs.stringifyNumber};$t.float=zl;$t.floatExp=Xl;$t.floatNaN=Hl});var hr=g(Fe=>{"use strict";var ur=me(),Be=s=>typeof s=="bigint"||Number.isInteger(s);function Dt(s,e,t,{intAsBigInt:n}){let i=s[0];if((i==="-"||i==="+")&&(e+=1),s=s.substring(e).replace(/_/g,""),n){switch(t){case 2:s=`0b${s}`;break;case 8:s=`0o${s}`;break;case 16:s=`0x${s}`;break}let o=BigInt(s);return i==="-"?BigInt(-1)*o:o}let r=parseInt(s,t);return i==="-"?-1*r:r}function xs(s,e,t){let{value:n}=s;if(Be(n)){let i=n.toString(e);return n<0?"-"+t+i.substr(1):t+i}return ur.stringifyNumber(s)}var Zl={identify:Be,default:!0,tag:"tag:yaml.org,2002:int",format:"BIN",test:/^[-+]?0b[0-1_]+$/,resolve:(s,e,t)=>Dt(s,2,2,t),stringify:s=>xs(s,2,"0b")},ec={identify:Be,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^[-+]?0[0-7_]+$/,resolve:(s,e,t)=>Dt(s,1,8,t),stringify:s=>xs(s,8,"0")},tc={identify:Be,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9][0-9_]*$/,resolve:(s,e,t)=>Dt(s,0,10,t),stringify:ur.stringifyNumber},sc={identify:Be,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^[-+]?0x[0-9a-fA-F_]+$/,resolve:(s,e,t)=>Dt(s,2,16,t),stringify:s=>xs(s,16,"0x")};Fe.int=tc;Fe.intBin=Zl;Fe.intHex=sc;Fe.intOct=ec});var Js=g(Us=>{"use strict";var Rt=O(),Bt=Q(),Ft=X(),re=class s extends Ft.YAMLMap{constructor(e){super(e),this.tag=s.tag}add(e){let t;Rt.isPair(e)?t=e:e&&typeof e=="object"&&"key"in e&&"value"in e&&e.value===null?t=new Bt.Pair(e.key,null):t=new Bt.Pair(e,null),Ft.findPair(this.items,t.key)||this.items.push(t)}get(e,t){let n=Ft.findPair(this.items,e);return!t&&Rt.isPair(n)?Rt.isScalar(n.key)?n.key.value:n.key:n}set(e,t){if(typeof t!="boolean")throw new Error(`Expected boolean value for set(key, value) in a YAML set, not ${typeof t}`);let n=Ft.findPair(this.items,e);n&&!t?this.items.splice(this.items.indexOf(n),1):!n&&t&&this.items.push(new Bt.Pair(e))}toJSON(e,t){return super.toJSON(e,t,Set)}toString(e,t,n){if(!e)return JSON.stringify(this);if(this.hasAllNullValues(!0))return super.toString(Object.assign({},e,{allNullValues:!0}),t,n);throw new Error("Set items must all have null values")}static from(e,t,n){let{replacer:i}=n,r=new this(e);if(t&&Symbol.iterator in Object(t))for(let o of t)typeof i=="function"&&(o=i.call(t,o,o)),r.items.push(Bt.createPair(o,null,n));return r}};re.tag="tag:yaml.org,2002:set";var nc={collection:"map",identify:s=>s instanceof Set,nodeClass:re,default:!1,tag:"tag:yaml.org,2002:set",createNode:(s,e,t)=>re.from(s,e,t),resolve(s,e){if(Rt.isMap(s)){if(s.hasAllNullValues(!0))return Object.assign(new re,s);e("Set items must all have null values")}else e("Expected a mapping for this tag");return s}};Us.YAMLSet=re;Us.set=nc});var Gs=g(Kt=>{"use strict";var ic=me();function Ys(s,e){let t=s[0],n=t==="-"||t==="+"?s.substring(1):s,i=o=>e?BigInt(o):Number(o),r=n.replace(/_/g,"").split(":").reduce((o,a)=>o*i(60)+i(a),i(0));return t==="-"?i(-1)*r:r}function dr(s){let{value:e}=s,t=o=>o;if(typeof e=="bigint")t=o=>BigInt(o);else if(isNaN(e)||!isFinite(e))return ic.stringifyNumber(s);let n="";e<0&&(n="-",e*=t(-1));let i=t(60),r=[e%i];return e<60?r.unshift(0):(e=(e-r[0])/i,r.unshift(e%i),e>=60&&(e=(e-r[0])/i,r.unshift(e))),n+r.map(o=>String(o).padStart(2,"0")).join(":").replace(/000000\d*$/,"")}var rc={identify:s=>typeof s=="bigint"||Number.isInteger(s),default:!0,tag:"tag:yaml.org,2002:int",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,resolve:(s,e,{intAsBigInt:t})=>Ys(s,t),stringify:dr},oc={identify:s=>typeof s=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\.[0-9_]*$/,resolve:s=>Ys(s,!1),stringify:dr},pr={identify:s=>s instanceof Date,default:!0,tag:"tag:yaml.org,2002:timestamp",test:RegExp("^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})(?:(?:t|T|[ \\t]+)([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\.[0-9]+)?)(?:[ \\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?)?$"),resolve(s){let e=s.match(pr.test);if(!e)throw new Error("!!timestamp expects a date, starting with yyyy-mm-dd");let[,t,n,i,r,o,a]=e.map(Number),l=e[7]?Number((e[7]+"00").substr(1,3)):0,c=Date.UTC(t,n-1,i,r||0,o||0,a||0,l),d=e[8];if(d&&d!=="Z"){let f=Ys(d,!1);Math.abs(f)<30&&(f*=60),c-=6e4*f}return new Date(c)},stringify:({value:s})=>s.toISOString().replace(/(T00:00:00)?\.000Z$/,"")};Kt.floatTime=oc;Kt.intTime=rc;Kt.timestamp=pr});var yr=g(gr=>{"use strict";var ac=de(),lc=Tt(),cc=pe(),fc=_e(),uc=Ds(),mr=cr(),Ws=fr(),jt=hr(),hc=St(),dc=Ks(),pc=_t(),mc=Js(),Qs=Gs(),gc=[ac.map,cc.seq,fc.string,lc.nullTag,mr.trueTag,mr.falseTag,jt.intBin,jt.intOct,jt.int,jt.intHex,Ws.floatNaN,Ws.floatExp,Ws.float,uc.binary,hc.merge,dc.omap,pc.pairs,mc.set,Qs.intTime,Qs.floatTime,Qs.timestamp];gr.schema=gc});var Tr=g(zs=>{"use strict";var vr=de(),yc=Tt(),kr=pe(),bc=_e(),Sc=qs(),Hs=Is(),Xs=Ps(),wc=Hi(),vc=Zi(),Nr=Ds(),Re=St(),Ar=Ks(),Or=_t(),br=yr(),Er=Js(),Vt=Gs(),Sr=new Map([["core",wc.schema],["failsafe",[vr.map,kr.seq,bc.string]],["json",vc.schema],["yaml11",br.schema],["yaml-1.1",br.schema]]),wr={binary:Nr.binary,bool:Sc.boolTag,float:Hs.float,floatExp:Hs.floatExp,floatNaN:Hs.floatNaN,floatTime:Vt.floatTime,int:Xs.int,intHex:Xs.intHex,intOct:Xs.intOct,intTime:Vt.intTime,map:vr.map,merge:Re.merge,null:yc.nullTag,omap:Ar.omap,pairs:Or.pairs,seq:kr.seq,set:Er.set,timestamp:Vt.timestamp},kc={"tag:yaml.org,2002:binary":Nr.binary,"tag:yaml.org,2002:merge":Re.merge,"tag:yaml.org,2002:omap":Ar.omap,"tag:yaml.org,2002:pairs":Or.pairs,"tag:yaml.org,2002:set":Er.set,"tag:yaml.org,2002:timestamp":Vt.timestamp};function Nc(s,e,t){let n=Sr.get(e);if(n&&!s)return t&&!n.includes(Re.merge)?n.concat(Re.merge):n.slice();let i=n;if(!i)if(Array.isArray(s))i=[];else{let r=Array.from(Sr.keys()).filter(o=>o!=="yaml11").map(o=>JSON.stringify(o)).join(", ");throw new Error(`Unknown schema "${e}"; use one of ${r} or define customTags array`)}if(Array.isArray(s))for(let r of s)i=i.concat(r);else typeof s=="function"&&(i=s(i.slice()));return t&&(i=i.concat(Re.merge)),i.reduce((r,o)=>{let a=typeof o=="string"?wr[o]:o;if(!a){let l=JSON.stringify(o),c=Object.keys(wr).map(d=>JSON.stringify(d)).join(", ");throw new Error(`Unknown custom tag ${l}; use one of ${c}`)}return r.includes(a)||r.push(a),r},[])}zs.coreKnownTags=kc;zs.getTags=Nc});var tn=g(qr=>{"use strict";var Zs=O(),Ac=de(),Oc=pe(),Ec=_e(),xt=Tr(),Tc=(s,e)=>s.key<e.key?-1:s.key>e.key?1:0,en=class s{constructor({compat:e,customTags:t,merge:n,resolveKnownTags:i,schema:r,sortMapEntries:o,toStringDefaults:a}){this.compat=Array.isArray(e)?xt.getTags(e,"compat"):e?xt.getTags(null,e):null,this.name=typeof r=="string"&&r||"core",this.knownTags=i?xt.coreKnownTags:{},this.tags=xt.getTags(t,this.name,n),this.toStringOptions=a??null,Object.defineProperty(this,Zs.MAP,{value:Ac.map}),Object.defineProperty(this,Zs.SCALAR,{value:Ec.string}),Object.defineProperty(this,Zs.SEQ,{value:Oc.seq}),this.sortMapEntries=typeof o=="function"?o:o===!0?Tc:null}clone(){let e=Object.create(s.prototype,Object.getOwnPropertyDescriptors(this));return e.tags=this.tags.slice(),e}};qr.Schema=en});var Ir=g(Lr=>{"use strict";var qc=O(),sn=Ie(),Ke=Ee();function Lc(s,e){let t=[],n=e.directives===!0;if(e.directives!==!1&&s.directives){let l=s.directives.toString(s);l?(t.push(l),n=!0):s.directives.docStart&&(n=!0)}n&&t.push("---");let i=sn.createStringifyContext(s,e),{commentString:r}=i.options;if(s.commentBefore){t.length!==1&&t.unshift("");let l=r(s.commentBefore);t.unshift(Ke.indentComment(l,""))}let o=!1,a=null;if(s.contents){if(qc.isNode(s.contents)){if(s.contents.spaceBefore&&n&&t.push(""),s.contents.commentBefore){let d=r(s.contents.commentBefore);t.push(Ke.indentComment(d,""))}i.forceBlockIndent=!!s.comment,a=s.contents.comment}let l=a?void 0:()=>o=!0,c=sn.stringify(s.contents,i,()=>a=null,l);a&&(c+=Ke.lineComment(c,"",r(a))),(c[0]==="|"||c[0]===">")&&t[t.length-1]==="---"?t[t.length-1]=`--- ${c}`:t.push(c)}else t.push(sn.stringify(s.contents,i));if(s.directives?.docEnd)if(s.comment){let l=r(s.comment);l.includes(`
`)?(t.push("..."),t.push(Ke.indentComment(l,""))):t.push(`... ${l}`)}else t.push("...");else{let l=s.comment;l&&o&&(l=l.replace(/^\n+/,"")),l&&((!o||a)&&t[t.length-1]!==""&&t.push(""),t.push(Ke.indentComment(r(l),"")))}return t.join(`
`)+`
`}Lr.stringifyDocument=Lc});var je=g(Cr=>{"use strict";var Ic=Ae(),ge=ft(),D=O(),Cc=Q(),Pc=J(),Mc=tn(),_c=Ir(),nn=rt(),$c=as(),Dc=Oe(),rn=os(),on=class s{constructor(e,t,n){this.commentBefore=null,this.comment=null,this.errors=[],this.warnings=[],Object.defineProperty(this,D.NODE_TYPE,{value:D.DOC});let i=null;typeof t=="function"||Array.isArray(t)?i=t:n===void 0&&t&&(n=t,t=void 0);let r=Object.assign({intAsBigInt:!1,keepSourceTokens:!1,logLevel:"warn",prettyErrors:!0,strict:!0,stringKeys:!1,uniqueKeys:!0,version:"1.2"},n);this.options=r;let{version:o}=r;n?._directives?(this.directives=n._directives.atDocument(),this.directives.yaml.explicit&&(o=this.directives.yaml.version)):this.directives=new rn.Directives({version:o}),this.setSchema(o,n),this.contents=e===void 0?null:this.createNode(e,i,n)}clone(){let e=Object.create(s.prototype,{[D.NODE_TYPE]:{value:D.DOC}});return e.commentBefore=this.commentBefore,e.comment=this.comment,e.errors=this.errors.slice(),e.warnings=this.warnings.slice(),e.options=Object.assign({},this.options),this.directives&&(e.directives=this.directives.clone()),e.schema=this.schema.clone(),e.contents=D.isNode(this.contents)?this.contents.clone(e.schema):this.contents,this.range&&(e.range=this.range.slice()),e}add(e){ye(this.contents)&&this.contents.add(e)}addIn(e,t){ye(this.contents)&&this.contents.addIn(e,t)}createAlias(e,t){if(!e.anchor){let n=nn.anchorNames(this);e.anchor=!t||n.has(t)?nn.findNewAnchor(t||"a",n):t}return new Ic.Alias(e.anchor)}createNode(e,t,n){let i;if(typeof t=="function")e=t.call({"":e},"",e),i=t;else if(Array.isArray(t)){let p=w=>typeof w=="number"||w instanceof String||w instanceof Number,S=t.filter(p).map(String);S.length>0&&(t=t.concat(S)),i=t}else n===void 0&&t&&(n=t,t=void 0);let{aliasDuplicateObjects:r,anchorPrefix:o,flow:a,keepUndefined:l,onTagObj:c,tag:d}=n??{},{onAnchor:f,setAnchors:u,sourceObjects:m}=nn.createNodeAnchors(this,o||"a"),y={aliasDuplicateObjects:r??!0,keepUndefined:l??!1,onAnchor:f,onTagObj:c,replacer:i,schema:this.schema,sourceObjects:m},h=Dc.createNode(e,d,y);return a&&D.isCollection(h)&&(h.flow=!0),u(),h}createPair(e,t,n={}){let i=this.createNode(e,null,n),r=this.createNode(t,null,n);return new Cc.Pair(i,r)}delete(e){return ye(this.contents)?this.contents.delete(e):!1}deleteIn(e){return ge.isEmptyPath(e)?this.contents==null?!1:(this.contents=null,!0):ye(this.contents)?this.contents.deleteIn(e):!1}get(e,t){return D.isCollection(this.contents)?this.contents.get(e,t):void 0}getIn(e,t){return ge.isEmptyPath(e)?!t&&D.isScalar(this.contents)?this.contents.value:this.contents:D.isCollection(this.contents)?this.contents.getIn(e,t):void 0}has(e){return D.isCollection(this.contents)?this.contents.has(e):!1}hasIn(e){return ge.isEmptyPath(e)?this.contents!==void 0:D.isCollection(this.contents)?this.contents.hasIn(e):!1}set(e,t){this.contents==null?this.contents=ge.collectionFromPath(this.schema,[e],t):ye(this.contents)&&this.contents.set(e,t)}setIn(e,t){ge.isEmptyPath(e)?this.contents=t:this.contents==null?this.contents=ge.collectionFromPath(this.schema,Array.from(e),t):ye(this.contents)&&this.contents.setIn(e,t)}setSchema(e,t={}){typeof e=="number"&&(e=String(e));let n;switch(e){case"1.1":this.directives?this.directives.yaml.version="1.1":this.directives=new rn.Directives({version:"1.1"}),n={resolveKnownTags:!1,schema:"yaml-1.1"};break;case"1.2":case"next":this.directives?this.directives.yaml.version=e:this.directives=new rn.Directives({version:e}),n={resolveKnownTags:!0,schema:"core"};break;case null:this.directives&&delete this.directives,n=null;break;default:{let i=JSON.stringify(e);throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${i}`)}}if(t.schema instanceof Object)this.schema=t.schema;else if(n)this.schema=new Mc.Schema(Object.assign(n,t));else throw new Error("With a null YAML version, the { schema: Schema } option is required")}toJS({json:e,jsonArg:t,mapAsMap:n,maxAliasCount:i,onAnchor:r,reviver:o}={}){let a={anchors:new Map,doc:this,keep:!e,mapAsMap:n===!0,mapKeyWarned:!1,maxAliasCount:typeof i=="number"?i:100},l=Pc.toJS(this.contents,t??"",a);if(typeof r=="function")for(let{count:c,res:d}of a.anchors.values())r(d,c);return typeof o=="function"?$c.applyReviver(o,{"":l},"",l):l}toJSON(e,t){return this.toJS({json:!0,jsonArg:e,mapAsMap:!1,onAnchor:t})}toString(e={}){if(this.errors.length>0)throw new Error("Document with errors cannot be stringified");if("indent"in e&&(!Number.isInteger(e.indent)||Number(e.indent)<=0)){let t=JSON.stringify(e.indent);throw new Error(`"indent" option must be a positive integer, not ${t}`)}return _c.stringifyDocument(this,e)}};function ye(s){if(D.isCollection(s))return!0;throw new Error("Expected a YAML collection as document contents")}Cr.Document=on});var Ue=g(xe=>{"use strict";var Ve=class extends Error{constructor(e,t,n,i){super(),this.name=e,this.code=n,this.message=i,this.pos=t}},an=class extends Ve{constructor(e,t,n){super("YAMLParseError",e,t,n)}},ln=class extends Ve{constructor(e,t,n){super("YAMLWarning",e,t,n)}},Bc=(s,e)=>t=>{if(t.pos[0]===-1)return;t.linePos=t.pos.map(a=>e.linePos(a));let{line:n,col:i}=t.linePos[0];t.message+=` at line ${n}, column ${i}`;let r=i-1,o=s.substring(e.lineStarts[n-1],e.lineStarts[n]).replace(/[\n\r]+$/,"");if(r>=60&&o.length>80){let a=Math.min(r-39,o.length-79);o="\u2026"+o.substring(a),r-=a-1}if(o.length>80&&(o=o.substring(0,79)+"\u2026"),n>1&&/^ *$/.test(o.substring(0,r))){let a=s.substring(e.lineStarts[n-2],e.lineStarts[n-1]);a.length>80&&(a=a.substring(0,79)+`\u2026
`),o=a+o}if(/[^ ]/.test(o)){let a=1,l=t.linePos[1];l&&l.line===n&&l.col>i&&(a=Math.max(1,Math.min(l.col-i,80-r)));let c=" ".repeat(r)+"^".repeat(a);t.message+=`:

${o}
${c}
`}};xe.YAMLError=Ve;xe.YAMLParseError=an;xe.YAMLWarning=ln;xe.prettifyError=Bc});var Je=g(Pr=>{"use strict";function Fc(s,{flow:e,indicator:t,next:n,offset:i,onError:r,parentIndent:o,startOnNewline:a}){let l=!1,c=a,d=a,f="",u="",m=!1,y=!1,h=null,p=null,S=null,w=null,v=null,k=null,N=null;for(let b of s)switch(y&&(b.type!=="space"&&b.type!=="newline"&&b.type!=="comma"&&r(b.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),y=!1),h&&(c&&b.type!=="comment"&&b.type!=="newline"&&r(h,"TAB_AS_INDENT","Tabs are not allowed as indentation"),h=null),b.type){case"space":!e&&(t!=="doc-start"||n?.type!=="flow-collection")&&b.source.includes("	")&&(h=b),d=!0;break;case"comment":{d||r(b,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");let L=b.source.substring(1)||" ";f?f+=u+L:f=L,u="",c=!1;break}case"newline":c?f?f+=b.source:(!k||t!=="seq-item-ind")&&(l=!0):u+=b.source,c=!0,m=!0,(p||S)&&(w=b),d=!0;break;case"anchor":p&&r(b,"MULTIPLE_ANCHORS","A node can have at most one anchor"),b.source.endsWith(":")&&r(b.offset+b.source.length-1,"BAD_ALIAS","Anchor ending in : is ambiguous",!0),p=b,N===null&&(N=b.offset),c=!1,d=!1,y=!0;break;case"tag":{S&&r(b,"MULTIPLE_TAGS","A node can have at most one tag"),S=b,N===null&&(N=b.offset),c=!1,d=!1,y=!0;break}case t:(p||S)&&r(b,"BAD_PROP_ORDER",`Anchors and tags must be after the ${b.source} indicator`),k&&r(b,"UNEXPECTED_TOKEN",`Unexpected ${b.source} in ${e??"collection"}`),k=b,c=t==="seq-item-ind"||t==="explicit-key-ind",d=!1;break;case"comma":if(e){v&&r(b,"UNEXPECTED_TOKEN",`Unexpected , in ${e}`),v=b,c=!1,d=!1;break}default:r(b,"UNEXPECTED_TOKEN",`Unexpected ${b.type} token`),c=!1,d=!1}let A=s[s.length-1],T=A?A.offset+A.source.length:i;return y&&n&&n.type!=="space"&&n.type!=="newline"&&n.type!=="comma"&&(n.type!=="scalar"||n.source!=="")&&r(n.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),h&&(c&&h.indent<=o||n?.type==="block-map"||n?.type==="block-seq")&&r(h,"TAB_AS_INDENT","Tabs are not allowed as indentation"),{comma:v,found:k,spaceBefore:l,comment:f,hasNewline:m,anchor:p,tag:S,newlineAfterProp:w,end:T,start:N??T}}Pr.resolveProps=Fc});var Ut=g(Mr=>{"use strict";function cn(s){if(!s)return null;switch(s.type){case"alias":case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":if(s.source.includes(`
`))return!0;if(s.end){for(let e of s.end)if(e.type==="newline")return!0}return!1;case"flow-collection":for(let e of s.items){for(let t of e.start)if(t.type==="newline")return!0;if(e.sep){for(let t of e.sep)if(t.type==="newline")return!0}if(cn(e.key)||cn(e.value))return!0}return!1;default:return!0}}Mr.containsNewline=cn});var fn=g(_r=>{"use strict";var Rc=Ut();function Kc(s,e,t){if(e?.type==="flow-collection"){let n=e.end[0];n.indent===s&&(n.source==="]"||n.source==="}")&&Rc.containsNewline(e)&&t(n,"BAD_INDENT","Flow end indicator should be more indented than parent",!0)}}_r.flowIndentCheck=Kc});var un=g(Dr=>{"use strict";var $r=O();function jc(s,e,t){let{uniqueKeys:n}=s.options;if(n===!1)return!1;let i=typeof n=="function"?n:(r,o)=>r===o||$r.isScalar(r)&&$r.isScalar(o)&&r.value===o.value;return e.some(r=>i(r.key,t))}Dr.mapIncludes=jc});var Vr=g(jr=>{"use strict";var Br=Q(),Vc=X(),Fr=Je(),xc=Ut(),Rr=fn(),Uc=un(),Kr="All mapping items must start at the same column";function Jc({composeNode:s,composeEmptyNode:e},t,n,i,r){let o=r?.nodeClass??Vc.YAMLMap,a=new o(t.schema);t.atRoot&&(t.atRoot=!1);let l=n.offset,c=null;for(let d of n.items){let{start:f,key:u,sep:m,value:y}=d,h=Fr.resolveProps(f,{indicator:"explicit-key-ind",next:u??m?.[0],offset:l,onError:i,parentIndent:n.indent,startOnNewline:!0}),p=!h.found;if(p){if(u&&(u.type==="block-seq"?i(l,"BLOCK_AS_IMPLICIT_KEY","A block sequence may not be used as an implicit map key"):"indent"in u&&u.indent!==n.indent&&i(l,"BAD_INDENT",Kr)),!h.anchor&&!h.tag&&!m){c=h.end,h.comment&&(a.comment?a.comment+=`
`+h.comment:a.comment=h.comment);continue}(h.newlineAfterProp||xc.containsNewline(u))&&i(u??f[f.length-1],"MULTILINE_IMPLICIT_KEY","Implicit keys need to be on a single line")}else h.found?.indent!==n.indent&&i(l,"BAD_INDENT",Kr);t.atKey=!0;let S=h.end,w=u?s(t,u,h,i):e(t,S,f,null,h,i);t.schema.compat&&Rr.flowIndentCheck(n.indent,u,i),t.atKey=!1,Uc.mapIncludes(t,a.items,w)&&i(S,"DUPLICATE_KEY","Map keys must be unique");let v=Fr.resolveProps(m??[],{indicator:"map-value-ind",next:y,offset:w.range[2],onError:i,parentIndent:n.indent,startOnNewline:!u||u.type==="block-scalar"});if(l=v.end,v.found){p&&(y?.type==="block-map"&&!v.hasNewline&&i(l,"BLOCK_AS_IMPLICIT_KEY","Nested mappings are not allowed in compact mappings"),t.options.strict&&h.start<v.found.offset-1024&&i(w.range,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit block mapping key"));let k=y?s(t,y,v,i):e(t,l,m,null,v,i);t.schema.compat&&Rr.flowIndentCheck(n.indent,y,i),l=k.range[2];let N=new Br.Pair(w,k);t.options.keepSourceTokens&&(N.srcToken=d),a.items.push(N)}else{p&&i(w.range,"MISSING_CHAR","Implicit map keys need to be followed by map values"),v.comment&&(w.comment?w.comment+=`
`+v.comment:w.comment=v.comment);let k=new Br.Pair(w);t.options.keepSourceTokens&&(k.srcToken=d),a.items.push(k)}}return c&&c<l&&i(c,"IMPOSSIBLE","Map comment with trailing content"),a.range=[n.offset,l,c??l],a}jr.resolveBlockMap=Jc});var Ur=g(xr=>{"use strict";var Yc=z(),Gc=Je(),Wc=fn();function Qc({composeNode:s,composeEmptyNode:e},t,n,i,r){let o=r?.nodeClass??Yc.YAMLSeq,a=new o(t.schema);t.atRoot&&(t.atRoot=!1),t.atKey&&(t.atKey=!1);let l=n.offset,c=null;for(let{start:d,value:f}of n.items){let u=Gc.resolveProps(d,{indicator:"seq-item-ind",next:f,offset:l,onError:i,parentIndent:n.indent,startOnNewline:!0});if(!u.found)if(u.anchor||u.tag||f)f&&f.type==="block-seq"?i(u.end,"BAD_INDENT","All sequence items must start at the same column"):i(l,"MISSING_CHAR","Sequence item without - indicator");else{c=u.end,u.comment&&(a.comment=u.comment);continue}let m=f?s(t,f,u,i):e(t,u.end,d,null,u,i);t.schema.compat&&Wc.flowIndentCheck(n.indent,f,i),l=m.range[2],a.items.push(m)}return a.range=[n.offset,l,c??l],a}xr.resolveBlockSeq=Qc});var be=g(Jr=>{"use strict";function Hc(s,e,t,n){let i="";if(s){let r=!1,o="";for(let a of s){let{source:l,type:c}=a;switch(c){case"space":r=!0;break;case"comment":{t&&!r&&n(a,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");let d=l.substring(1)||" ";i?i+=o+d:i=d,o="";break}case"newline":i&&(o+=l),r=!0;break;default:n(a,"UNEXPECTED_TOKEN",`Unexpected ${c} at node end`)}e+=l.length}}return{comment:i,offset:e}}Jr.resolveEnd=Hc});var Qr=g(Wr=>{"use strict";var Xc=O(),zc=Q(),Yr=X(),Zc=z(),ef=be(),Gr=Je(),tf=Ut(),sf=un(),hn="Block collections are not allowed within flow collections",dn=s=>s&&(s.type==="block-map"||s.type==="block-seq");function nf({composeNode:s,composeEmptyNode:e},t,n,i,r){let o=n.start.source==="{",a=o?"flow map":"flow sequence",l=r?.nodeClass??(o?Yr.YAMLMap:Zc.YAMLSeq),c=new l(t.schema);c.flow=!0;let d=t.atRoot;d&&(t.atRoot=!1),t.atKey&&(t.atKey=!1);let f=n.offset+n.start.source.length;for(let p=0;p<n.items.length;++p){let S=n.items[p],{start:w,key:v,sep:k,value:N}=S,A=Gr.resolveProps(w,{flow:a,indicator:"explicit-key-ind",next:v??k?.[0],offset:f,onError:i,parentIndent:n.indent,startOnNewline:!1});if(!A.found){if(!A.anchor&&!A.tag&&!k&&!N){p===0&&A.comma?i(A.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`):p<n.items.length-1&&i(A.start,"UNEXPECTED_TOKEN",`Unexpected empty item in ${a}`),A.comment&&(c.comment?c.comment+=`
`+A.comment:c.comment=A.comment),f=A.end;continue}!o&&t.options.strict&&tf.containsNewline(v)&&i(v,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line")}if(p===0)A.comma&&i(A.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`);else if(A.comma||i(A.start,"MISSING_CHAR",`Missing , between ${a} items`),A.comment){let T="";e:for(let b of w)switch(b.type){case"comma":case"space":break;case"comment":T=b.source.substring(1);break e;default:break e}if(T){let b=c.items[c.items.length-1];Xc.isPair(b)&&(b=b.value??b.key),b.comment?b.comment+=`
`+T:b.comment=T,A.comment=A.comment.substring(T.length+1)}}if(!o&&!k&&!A.found){let T=N?s(t,N,A,i):e(t,A.end,k,null,A,i);c.items.push(T),f=T.range[2],dn(N)&&i(T.range,"BLOCK_IN_FLOW",hn)}else{t.atKey=!0;let T=A.end,b=v?s(t,v,A,i):e(t,T,w,null,A,i);dn(v)&&i(b.range,"BLOCK_IN_FLOW",hn),t.atKey=!1;let L=Gr.resolveProps(k??[],{flow:a,indicator:"map-value-ind",next:N,offset:b.range[2],onError:i,parentIndent:n.indent,startOnNewline:!1});if(L.found){if(!o&&!A.found&&t.options.strict){if(k)for(let I of k){if(I===L.found)break;if(I.type==="newline"){i(I,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line");break}}A.start<L.found.offset-1024&&i(L.found,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit flow sequence key")}}else N&&("source"in N&&N.source&&N.source[0]===":"?i(N,"MISSING_CHAR",`Missing space after : in ${a}`):i(L.start,"MISSING_CHAR",`Missing , or : between ${a} items`));let U=N?s(t,N,L,i):L.found?e(t,L.end,k,null,L,i):null;U?dn(N)&&i(U.range,"BLOCK_IN_FLOW",hn):L.comment&&(b.comment?b.comment+=`
`+L.comment:b.comment=L.comment);let ce=new zc.Pair(b,U);if(t.options.keepSourceTokens&&(ce.srcToken=S),o){let I=c;sf.mapIncludes(t,I.items,b)&&i(T,"DUPLICATE_KEY","Map keys must be unique"),I.items.push(ce)}else{let I=new Yr.YAMLMap(t.schema);I.flow=!0,I.items.push(ce);let jn=(U??b).range;I.range=[b.range[0],jn[1],jn[2]],c.items.push(I)}f=U?U.range[2]:L.end}}let u=o?"}":"]",[m,...y]=n.end,h=f;if(m&&m.source===u)h=m.offset+m.source.length;else{let p=a[0].toUpperCase()+a.substring(1),S=d?`${p} must end with a ${u}`:`${p} in block collection must be sufficiently indented and end with a ${u}`;i(f,d?"MISSING_CHAR":"BAD_INDENT",S),m&&m.source.length!==1&&y.unshift(m)}if(y.length>0){let p=ef.resolveEnd(y,h,t.options.strict,i);p.comment&&(c.comment?c.comment+=`
`+p.comment:c.comment=p.comment),c.range=[n.offset,h,p.offset]}else c.range=[n.offset,h,h];return c}Wr.resolveFlowCollection=nf});var Xr=g(Hr=>{"use strict";var rf=O(),of=q(),af=X(),lf=z(),cf=Vr(),ff=Ur(),uf=Qr();function pn(s,e,t,n,i,r){let o=t.type==="block-map"?cf.resolveBlockMap(s,e,t,n,r):t.type==="block-seq"?ff.resolveBlockSeq(s,e,t,n,r):uf.resolveFlowCollection(s,e,t,n,r),a=o.constructor;return i==="!"||i===a.tagName?(o.tag=a.tagName,o):(i&&(o.tag=i),o)}function hf(s,e,t,n,i){let r=n.tag,o=r?e.directives.tagName(r.source,u=>i(r,"TAG_RESOLVE_FAILED",u)):null;if(t.type==="block-seq"){let{anchor:u,newlineAfterProp:m}=n,y=u&&r?u.offset>r.offset?u:r:u??r;y&&(!m||m.offset<y.offset)&&i(y,"MISSING_CHAR","Missing newline after block sequence props")}let a=t.type==="block-map"?"map":t.type==="block-seq"?"seq":t.start.source==="{"?"map":"seq";if(!r||!o||o==="!"||o===af.YAMLMap.tagName&&a==="map"||o===lf.YAMLSeq.tagName&&a==="seq")return pn(s,e,t,i,o);let l=e.schema.tags.find(u=>u.tag===o&&u.collection===a);if(!l){let u=e.schema.knownTags[o];if(u&&u.collection===a)e.schema.tags.push(Object.assign({},u,{default:!1})),l=u;else return u?.collection?i(r,"BAD_COLLECTION_TYPE",`${u.tag} used for ${a} collection, but expects ${u.collection}`,!0):i(r,"TAG_RESOLVE_FAILED",`Unresolved tag: ${o}`,!0),pn(s,e,t,i,o)}let c=pn(s,e,t,i,o,l),d=l.resolve?.(c,u=>i(r,"TAG_RESOLVE_FAILED",u),e.options)??c,f=rf.isNode(d)?d:new of.Scalar(d);return f.range=c.range,f.tag=o,l?.format&&(f.format=l.format),f}Hr.composeCollection=hf});var gn=g(zr=>{"use strict";var mn=q();function df(s,e,t){let n=e.offset,i=pf(e,s.options.strict,t);if(!i)return{value:"",type:null,comment:"",range:[n,n,n]};let r=i.mode===">"?mn.Scalar.BLOCK_FOLDED:mn.Scalar.BLOCK_LITERAL,o=e.source?mf(e.source):[],a=o.length;for(let h=o.length-1;h>=0;--h){let p=o[h][1];if(p===""||p==="\r")a=h;else break}if(a===0){let h=i.chomp==="+"&&o.length>0?`
`.repeat(Math.max(1,o.length-1)):"",p=n+i.length;return e.source&&(p+=e.source.length),{value:h,type:r,comment:i.comment,range:[n,p,p]}}let l=e.indent+i.indent,c=e.offset+i.length,d=0;for(let h=0;h<a;++h){let[p,S]=o[h];if(S===""||S==="\r")i.indent===0&&p.length>l&&(l=p.length);else{p.length<l&&t(c+p.length,"MISSING_CHAR","Block scalars with more-indented leading empty lines must use an explicit indentation indicator"),i.indent===0&&(l=p.length),d=h,l===0&&!s.atRoot&&t(c,"BAD_INDENT","Block scalar values in collections must be indented");break}c+=p.length+S.length+1}for(let h=o.length-1;h>=a;--h)o[h][0].length>l&&(a=h+1);let f="",u="",m=!1;for(let h=0;h<d;++h)f+=o[h][0].slice(l)+`
`;for(let h=d;h<a;++h){let[p,S]=o[h];c+=p.length+S.length+1;let w=S[S.length-1]==="\r";if(w&&(S=S.slice(0,-1)),S&&p.length<l){let k=`Block scalar lines must not be less indented than their ${i.indent?"explicit indentation indicator":"first line"}`;t(c-S.length-(w?2:1),"BAD_INDENT",k),p=""}r===mn.Scalar.BLOCK_LITERAL?(f+=u+p.slice(l)+S,u=`
`):p.length>l||S[0]==="	"?(u===" "?u=`
`:!m&&u===`
`&&(u=`

`),f+=u+p.slice(l)+S,u=`
`,m=!0):S===""?u===`
`?f+=`
`:u=`
`:(f+=u+S,u=" ",m=!1)}switch(i.chomp){case"-":break;case"+":for(let h=a;h<o.length;++h)f+=`
`+o[h][0].slice(l);f[f.length-1]!==`
`&&(f+=`
`);break;default:f+=`
`}let y=n+i.length+e.source.length;return{value:f,type:r,comment:i.comment,range:[n,y,y]}}function pf({offset:s,props:e},t,n){if(e[0].type!=="block-scalar-header")return n(e[0],"IMPOSSIBLE","Block scalar header not found"),null;let{source:i}=e[0],r=i[0],o=0,a="",l=-1;for(let u=1;u<i.length;++u){let m=i[u];if(!a&&(m==="-"||m==="+"))a=m;else{let y=Number(m);!o&&y?o=y:l===-1&&(l=s+u)}}l!==-1&&n(l,"UNEXPECTED_TOKEN",`Block scalar header includes extra characters: ${i}`);let c=!1,d="",f=i.length;for(let u=1;u<e.length;++u){let m=e[u];switch(m.type){case"space":c=!0;case"newline":f+=m.source.length;break;case"comment":t&&!c&&n(m,"MISSING_CHAR","Comments must be separated from other tokens by white space characters"),f+=m.source.length,d=m.source.substring(1);break;case"error":n(m,"UNEXPECTED_TOKEN",m.message),f+=m.source.length;break;default:{let y=`Unexpected token in block scalar header: ${m.type}`;n(m,"UNEXPECTED_TOKEN",y);let h=m.source;h&&typeof h=="string"&&(f+=h.length)}}}return{mode:r,indent:o,chomp:a,comment:d,length:f}}function mf(s){let e=s.split(/\n( *)/),t=e[0],n=t.match(/^( *)/),r=[n?.[1]?[n[1],t.slice(n[1].length)]:["",t]];for(let o=1;o<e.length;o+=2)r.push([e[o],e[o+1]]);return r}zr.resolveBlockScalar=df});var bn=g(eo=>{"use strict";var yn=q(),gf=be();function yf(s,e,t){let{offset:n,type:i,source:r,end:o}=s,a,l,c=(u,m,y)=>t(n+u,m,y);switch(i){case"scalar":a=yn.Scalar.PLAIN,l=bf(r,c);break;case"single-quoted-scalar":a=yn.Scalar.QUOTE_SINGLE,l=Sf(r,c);break;case"double-quoted-scalar":a=yn.Scalar.QUOTE_DOUBLE,l=wf(r,c);break;default:return t(s,"UNEXPECTED_TOKEN",`Expected a flow scalar value, but found: ${i}`),{value:"",type:null,comment:"",range:[n,n+r.length,n+r.length]}}let d=n+r.length,f=gf.resolveEnd(o,d,e,t);return{value:l,type:a,comment:f.comment,range:[n,d,f.offset]}}function bf(s,e){let t="";switch(s[0]){case"	":t="a tab character";break;case",":t="flow indicator character ,";break;case"%":t="directive indicator character %";break;case"|":case">":{t=`block scalar indicator ${s[0]}`;break}case"@":case"`":{t=`reserved character ${s[0]}`;break}}return t&&e(0,"BAD_SCALAR_START",`Plain value cannot start with ${t}`),Zr(s)}function Sf(s,e){return(s[s.length-1]!=="'"||s.length===1)&&e(s.length,"MISSING_CHAR","Missing closing 'quote"),Zr(s.slice(1,-1)).replace(/''/g,"'")}function Zr(s){let e,t;try{e=new RegExp(`(.*?)(?<![ 	])[ 	]*\r?
`,"sy"),t=new RegExp(`[ 	]*(.*?)(?:(?<![ 	])[ 	]*)?\r?
`,"sy")}catch{e=/(.*?)[ \t]*\r?\n/sy,t=/[ \t]*(.*?)[ \t]*\r?\n/sy}let n=e.exec(s);if(!n)return s;let i=n[1],r=" ",o=e.lastIndex;for(t.lastIndex=o;n=t.exec(s);)n[1]===""?r===`
`?i+=r:r=`
`:(i+=r+n[1],r=" "),o=t.lastIndex;let a=/[ \t]*(.*)/sy;return a.lastIndex=o,n=a.exec(s),i+r+(n?.[1]??"")}function wf(s,e){let t="";for(let n=1;n<s.length-1;++n){let i=s[n];if(!(i==="\r"&&s[n+1]===`
`))if(i===`
`){let{fold:r,offset:o}=vf(s,n);t+=r,n=o}else if(i==="\\"){let r=s[++n],o=kf[r];if(o)t+=o;else if(r===`
`)for(r=s[n+1];r===" "||r==="	";)r=s[++n+1];else if(r==="\r"&&s[n+1]===`
`)for(r=s[++n+1];r===" "||r==="	";)r=s[++n+1];else if(r==="x"||r==="u"||r==="U"){let a={x:2,u:4,U:8}[r];t+=Nf(s,n+1,a,e),n+=a}else{let a=s.substr(n-1,2);e(n-1,"BAD_DQ_ESCAPE",`Invalid escape sequence ${a}`),t+=a}}else if(i===" "||i==="	"){let r=n,o=s[n+1];for(;o===" "||o==="	";)o=s[++n+1];o!==`
`&&!(o==="\r"&&s[n+2]===`
`)&&(t+=n>r?s.slice(r,n+1):i)}else t+=i}return(s[s.length-1]!=='"'||s.length===1)&&e(s.length,"MISSING_CHAR",'Missing closing "quote'),t}function vf(s,e){let t="",n=s[e+1];for(;(n===" "||n==="	"||n===`
`||n==="\r")&&!(n==="\r"&&s[e+2]!==`
`);)n===`
`&&(t+=`
`),e+=1,n=s[e+1];return t||(t=" "),{fold:t,offset:e}}var kf={0:"\0",a:"\x07",b:"\b",e:"\x1B",f:"\f",n:`
`,r:"\r",t:"	",v:"\v",N:"\x85",_:"\xA0",L:"\u2028",P:"\u2029"," ":" ",'"':'"',"/":"/","\\":"\\","	":"	"};function Nf(s,e,t,n){let i=s.substr(e,t),o=i.length===t&&/^[0-9a-fA-F]+$/.test(i)?parseInt(i,16):NaN;if(isNaN(o)){let a=s.substr(e-2,t+2);return n(e-2,"BAD_DQ_ESCAPE",`Invalid escape sequence ${a}`),a}return String.fromCodePoint(o)}eo.resolveFlowScalar=yf});var no=g(so=>{"use strict";var oe=O(),to=q(),Af=gn(),Of=bn();function Ef(s,e,t,n){let{value:i,type:r,comment:o,range:a}=e.type==="block-scalar"?Af.resolveBlockScalar(s,e,n):Of.resolveFlowScalar(e,s.options.strict,n),l=t?s.directives.tagName(t.source,f=>n(t,"TAG_RESOLVE_FAILED",f)):null,c;s.options.stringKeys&&s.atKey?c=s.schema[oe.SCALAR]:l?c=Tf(s.schema,i,l,t,n):e.type==="scalar"?c=qf(s,i,e,n):c=s.schema[oe.SCALAR];let d;try{let f=c.resolve(i,u=>n(t??e,"TAG_RESOLVE_FAILED",u),s.options);d=oe.isScalar(f)?f:new to.Scalar(f)}catch(f){let u=f instanceof Error?f.message:String(f);n(t??e,"TAG_RESOLVE_FAILED",u),d=new to.Scalar(i)}return d.range=a,d.source=i,r&&(d.type=r),l&&(d.tag=l),c.format&&(d.format=c.format),o&&(d.comment=o),d}function Tf(s,e,t,n,i){if(t==="!")return s[oe.SCALAR];let r=[];for(let a of s.tags)if(!a.collection&&a.tag===t)if(a.default&&a.test)r.push(a);else return a;for(let a of r)if(a.test?.test(e))return a;let o=s.knownTags[t];return o&&!o.collection?(s.tags.push(Object.assign({},o,{default:!1,test:void 0})),o):(i(n,"TAG_RESOLVE_FAILED",`Unresolved tag: ${t}`,t!=="tag:yaml.org,2002:str"),s[oe.SCALAR])}function qf({atKey:s,directives:e,schema:t},n,i,r){let o=t.tags.find(a=>(a.default===!0||s&&a.default==="key")&&a.test?.test(n))||t[oe.SCALAR];if(t.compat){let a=t.compat.find(l=>l.default&&l.test?.test(n))??t[oe.SCALAR];if(o.tag!==a.tag){let l=e.tagString(o.tag),c=e.tagString(a.tag),d=`Value may be parsed as either ${l} or ${c}`;r(i,"TAG_RESOLVE_FAILED",d,!0)}}return o}so.composeScalar=Ef});var ro=g(io=>{"use strict";function Lf(s,e,t){if(e){t===null&&(t=e.length);for(let n=t-1;n>=0;--n){let i=e[n];switch(i.type){case"space":case"comment":case"newline":s-=i.source.length;continue}for(i=e[++n];i?.type==="space";)s+=i.source.length,i=e[++n];break}}return s}io.emptyScalarPosition=Lf});var lo=g(wn=>{"use strict";var If=Ae(),Cf=O(),Pf=Xr(),oo=no(),Mf=be(),_f=ro(),$f={composeNode:ao,composeEmptyNode:Sn};function ao(s,e,t,n){let i=s.atKey,{spaceBefore:r,comment:o,anchor:a,tag:l}=t,c,d=!0;switch(e.type){case"alias":c=Df(s,e,n),(a||l)&&n(e,"ALIAS_PROPS","An alias node must not specify any properties");break;case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"block-scalar":c=oo.composeScalar(s,e,l,n),a&&(c.anchor=a.source.substring(1));break;case"block-map":case"block-seq":case"flow-collection":c=Pf.composeCollection($f,s,e,t,n),a&&(c.anchor=a.source.substring(1));break;default:{let f=e.type==="error"?e.message:`Unsupported token (type: ${e.type})`;n(e,"UNEXPECTED_TOKEN",f),c=Sn(s,e.offset,void 0,null,t,n),d=!1}}return a&&c.anchor===""&&n(a,"BAD_ALIAS","Anchor cannot be an empty string"),i&&s.options.stringKeys&&(!Cf.isScalar(c)||typeof c.value!="string"||c.tag&&c.tag!=="tag:yaml.org,2002:str")&&n(l??e,"NON_STRING_KEY","With stringKeys, all keys must be strings"),r&&(c.spaceBefore=!0),o&&(e.type==="scalar"&&e.source===""?c.comment=o:c.commentBefore=o),s.options.keepSourceTokens&&d&&(c.srcToken=e),c}function Sn(s,e,t,n,{spaceBefore:i,comment:r,anchor:o,tag:a,end:l},c){let d={type:"scalar",offset:_f.emptyScalarPosition(e,t,n),indent:-1,source:""},f=oo.composeScalar(s,d,a,c);return o&&(f.anchor=o.source.substring(1),f.anchor===""&&c(o,"BAD_ALIAS","Anchor cannot be an empty string")),i&&(f.spaceBefore=!0),r&&(f.comment=r,f.range[2]=l),f}function Df({options:s},{offset:e,source:t,end:n},i){let r=new If.Alias(t.substring(1));r.source===""&&i(e,"BAD_ALIAS","Alias cannot be an empty string"),r.source.endsWith(":")&&i(e+t.length-1,"BAD_ALIAS","Alias ending in : is ambiguous",!0);let o=e+t.length,a=Mf.resolveEnd(n,o,s.strict,i);return r.range=[e,o,a.offset],a.comment&&(r.comment=a.comment),r}wn.composeEmptyNode=Sn;wn.composeNode=ao});var uo=g(fo=>{"use strict";var Bf=je(),co=lo(),Ff=be(),Rf=Je();function Kf(s,e,{offset:t,start:n,value:i,end:r},o){let a=Object.assign({_directives:e},s),l=new Bf.Document(void 0,a),c={atKey:!1,atRoot:!0,directives:l.directives,options:l.options,schema:l.schema},d=Rf.resolveProps(n,{indicator:"doc-start",next:i??r?.[0],offset:t,onError:o,parentIndent:0,startOnNewline:!0});d.found&&(l.directives.docStart=!0,i&&(i.type==="block-map"||i.type==="block-seq")&&!d.hasNewline&&o(d.end,"MISSING_CHAR","Block collection cannot start on same line with directives-end marker")),l.contents=i?co.composeNode(c,i,d,o):co.composeEmptyNode(c,d.end,n,null,d,o);let f=l.contents.range[2],u=Ff.resolveEnd(r,f,!1,o);return u.comment&&(l.comment=u.comment),l.range=[t,f,u.offset],l}fo.composeDoc=Kf});var kn=g(mo=>{"use strict";var jf=require("node:process"),Vf=os(),xf=je(),Ye=Ue(),ho=O(),Uf=uo(),Jf=be();function Ge(s){if(typeof s=="number")return[s,s+1];if(Array.isArray(s))return s.length===2?s:[s[0],s[1]];let{offset:e,source:t}=s;return[e,e+(typeof t=="string"?t.length:1)]}function po(s){let e="",t=!1,n=!1;for(let i=0;i<s.length;++i){let r=s[i];switch(r[0]){case"#":e+=(e===""?"":n?`

`:`
`)+(r.substring(1)||" "),t=!0,n=!1;break;case"%":s[i+1]?.[0]!=="#"&&(i+=1),t=!1;break;default:t||(n=!0),t=!1}}return{comment:e,afterEmptyLine:n}}var vn=class{constructor(e={}){this.doc=null,this.atDirectives=!1,this.prelude=[],this.errors=[],this.warnings=[],this.onError=(t,n,i,r)=>{let o=Ge(t);r?this.warnings.push(new Ye.YAMLWarning(o,n,i)):this.errors.push(new Ye.YAMLParseError(o,n,i))},this.directives=new Vf.Directives({version:e.version||"1.2"}),this.options=e}decorate(e,t){let{comment:n,afterEmptyLine:i}=po(this.prelude);if(n){let r=e.contents;if(t)e.comment=e.comment?`${e.comment}
${n}`:n;else if(i||e.directives.docStart||!r)e.commentBefore=n;else if(ho.isCollection(r)&&!r.flow&&r.items.length>0){let o=r.items[0];ho.isPair(o)&&(o=o.key);let a=o.commentBefore;o.commentBefore=a?`${n}
${a}`:n}else{let o=r.commentBefore;r.commentBefore=o?`${n}
${o}`:n}}t?(Array.prototype.push.apply(e.errors,this.errors),Array.prototype.push.apply(e.warnings,this.warnings)):(e.errors=this.errors,e.warnings=this.warnings),this.prelude=[],this.errors=[],this.warnings=[]}streamInfo(){return{comment:po(this.prelude).comment,directives:this.directives,errors:this.errors,warnings:this.warnings}}*compose(e,t=!1,n=-1){for(let i of e)yield*this.next(i);yield*this.end(t,n)}*next(e){switch(jf.env.LOG_STREAM&&console.dir(e,{depth:null}),e.type){case"directive":this.directives.add(e.source,(t,n,i)=>{let r=Ge(e);r[0]+=t,this.onError(r,"BAD_DIRECTIVE",n,i)}),this.prelude.push(e.source),this.atDirectives=!0;break;case"document":{let t=Uf.composeDoc(this.options,this.directives,e,this.onError);this.atDirectives&&!t.directives.docStart&&this.onError(e,"MISSING_CHAR","Missing directives-end/doc-start indicator line"),this.decorate(t,!1),this.doc&&(yield this.doc),this.doc=t,this.atDirectives=!1;break}case"byte-order-mark":case"space":break;case"comment":case"newline":this.prelude.push(e.source);break;case"error":{let t=e.source?`${e.message}: ${JSON.stringify(e.source)}`:e.message,n=new Ye.YAMLParseError(Ge(e),"UNEXPECTED_TOKEN",t);this.atDirectives||!this.doc?this.errors.push(n):this.doc.errors.push(n);break}case"doc-end":{if(!this.doc){let n="Unexpected doc-end without preceding document";this.errors.push(new Ye.YAMLParseError(Ge(e),"UNEXPECTED_TOKEN",n));break}this.doc.directives.docEnd=!0;let t=Jf.resolveEnd(e.end,e.offset+e.source.length,this.doc.options.strict,this.onError);if(this.decorate(this.doc,!0),t.comment){let n=this.doc.comment;this.doc.comment=n?`${n}
${t.comment}`:t.comment}this.doc.range[2]=t.offset;break}default:this.errors.push(new Ye.YAMLParseError(Ge(e),"UNEXPECTED_TOKEN",`Unsupported token ${e.type}`))}}*end(e=!1,t=-1){if(this.doc)this.decorate(this.doc,!0),yield this.doc,this.doc=null;else if(e){let n=Object.assign({_directives:this.directives},this.options),i=new xf.Document(void 0,n);this.atDirectives&&this.onError(t,"MISSING_CHAR","Missing directives-end indicator line"),i.range=[0,t,t],this.decorate(i,!1),yield i}}};mo.Composer=vn});var bo=g(Jt=>{"use strict";var Yf=gn(),Gf=bn(),Wf=Ue(),go=Le();function Qf(s,e=!0,t){if(s){let n=(i,r,o)=>{let a=typeof i=="number"?i:Array.isArray(i)?i[0]:i.offset;if(t)t(a,r,o);else throw new Wf.YAMLParseError([a,a+1],r,o)};switch(s.type){case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return Gf.resolveFlowScalar(s,e,n);case"block-scalar":return Yf.resolveBlockScalar({options:{strict:e}},s,n)}}return null}function Hf(s,e){let{implicitKey:t=!1,indent:n,inFlow:i=!1,offset:r=-1,type:o="PLAIN"}=e,a=go.stringifyString({type:o,value:s},{implicitKey:t,indent:n>0?" ".repeat(n):"",inFlow:i,options:{blockQuote:!0,lineWidth:-1}}),l=e.end??[{type:"newline",offset:-1,indent:n,source:`
`}];switch(a[0]){case"|":case">":{let c=a.indexOf(`
`),d=a.substring(0,c),f=a.substring(c+1)+`
`,u=[{type:"block-scalar-header",offset:r,indent:n,source:d}];return yo(u,l)||u.push({type:"newline",offset:-1,indent:n,source:`
`}),{type:"block-scalar",offset:r,indent:n,props:u,source:f}}case'"':return{type:"double-quoted-scalar",offset:r,indent:n,source:a,end:l};case"'":return{type:"single-quoted-scalar",offset:r,indent:n,source:a,end:l};default:return{type:"scalar",offset:r,indent:n,source:a,end:l}}}function Xf(s,e,t={}){let{afterKey:n=!1,implicitKey:i=!1,inFlow:r=!1,type:o}=t,a="indent"in s?s.indent:null;if(n&&typeof a=="number"&&(a+=2),!o)switch(s.type){case"single-quoted-scalar":o="QUOTE_SINGLE";break;case"double-quoted-scalar":o="QUOTE_DOUBLE";break;case"block-scalar":{let c=s.props[0];if(c.type!=="block-scalar-header")throw new Error("Invalid block scalar header");o=c.source[0]===">"?"BLOCK_FOLDED":"BLOCK_LITERAL";break}default:o="PLAIN"}let l=go.stringifyString({type:o,value:e},{implicitKey:i||a===null,indent:a!==null&&a>0?" ".repeat(a):"",inFlow:r,options:{blockQuote:!0,lineWidth:-1}});switch(l[0]){case"|":case">":zf(s,l);break;case'"':Nn(s,l,"double-quoted-scalar");break;case"'":Nn(s,l,"single-quoted-scalar");break;default:Nn(s,l,"scalar")}}function zf(s,e){let t=e.indexOf(`
`),n=e.substring(0,t),i=e.substring(t+1)+`
`;if(s.type==="block-scalar"){let r=s.props[0];if(r.type!=="block-scalar-header")throw new Error("Invalid block scalar header");r.source=n,s.source=i}else{let{offset:r}=s,o="indent"in s?s.indent:-1,a=[{type:"block-scalar-header",offset:r,indent:o,source:n}];yo(a,"end"in s?s.end:void 0)||a.push({type:"newline",offset:-1,indent:o,source:`
`});for(let l of Object.keys(s))l!=="type"&&l!=="offset"&&delete s[l];Object.assign(s,{type:"block-scalar",indent:o,props:a,source:i})}}function yo(s,e){if(e)for(let t of e)switch(t.type){case"space":case"comment":s.push(t);break;case"newline":return s.push(t),!0}return!1}function Nn(s,e,t){switch(s.type){case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":s.type=t,s.source=e;break;case"block-scalar":{let n=s.props.slice(1),i=e.length;s.props[0].type==="block-scalar-header"&&(i-=s.props[0].source.length);for(let r of n)r.offset+=i;delete s.props,Object.assign(s,{type:t,source:e,end:n});break}case"block-map":case"block-seq":{let i={type:"newline",offset:s.offset+e.length,indent:s.indent,source:`
`};delete s.items,Object.assign(s,{type:t,source:e,end:[i]});break}default:{let n="indent"in s?s.indent:-1,i="end"in s&&Array.isArray(s.end)?s.end.filter(r=>r.type==="space"||r.type==="comment"||r.type==="newline"):[];for(let r of Object.keys(s))r!=="type"&&r!=="offset"&&delete s[r];Object.assign(s,{type:t,indent:n,source:e,end:i})}}}Jt.createScalarToken=Hf;Jt.resolveAsScalar=Qf;Jt.setScalarValue=Xf});var wo=g(So=>{"use strict";var Zf=s=>"type"in s?Gt(s):Yt(s);function Gt(s){switch(s.type){case"block-scalar":{let e="";for(let t of s.props)e+=Gt(t);return e+s.source}case"block-map":case"block-seq":{let e="";for(let t of s.items)e+=Yt(t);return e}case"flow-collection":{let e=s.start.source;for(let t of s.items)e+=Yt(t);for(let t of s.end)e+=t.source;return e}case"document":{let e=Yt(s);if(s.end)for(let t of s.end)e+=t.source;return e}default:{let e=s.source;if("end"in s&&s.end)for(let t of s.end)e+=t.source;return e}}}function Yt({start:s,key:e,sep:t,value:n}){let i="";for(let r of s)i+=r.source;if(e&&(i+=Gt(e)),t)for(let r of t)i+=r.source;return n&&(i+=Gt(n)),i}So.stringify=Zf});var Ao=g(No=>{"use strict";var An=Symbol("break visit"),eu=Symbol("skip children"),vo=Symbol("remove item");function ae(s,e){"type"in s&&s.type==="document"&&(s={start:s.start,value:s.value}),ko(Object.freeze([]),s,e)}ae.BREAK=An;ae.SKIP=eu;ae.REMOVE=vo;ae.itemAtPath=(s,e)=>{let t=s;for(let[n,i]of e){let r=t?.[n];if(r&&"items"in r)t=r.items[i];else return}return t};ae.parentCollection=(s,e)=>{let t=ae.itemAtPath(s,e.slice(0,-1)),n=e[e.length-1][0],i=t?.[n];if(i&&"items"in i)return i;throw new Error("Parent collection not found")};function ko(s,e,t){let n=t(e,s);if(typeof n=="symbol")return n;for(let i of["key","value"]){let r=e[i];if(r&&"items"in r){for(let o=0;o<r.items.length;++o){let a=ko(Object.freeze(s.concat([[i,o]])),r.items[o],t);if(typeof a=="number")o=a-1;else{if(a===An)return An;a===vo&&(r.items.splice(o,1),o-=1)}}typeof n=="function"&&i==="key"&&(n=n(e,s))}}return typeof n=="function"?n(e,s):n}No.visit=ae});var Wt=g(_=>{"use strict";var On=bo(),tu=wo(),su=Ao(),En="\uFEFF",Tn="",qn="",Ln="",nu=s=>!!s&&"items"in s,iu=s=>!!s&&(s.type==="scalar"||s.type==="single-quoted-scalar"||s.type==="double-quoted-scalar"||s.type==="block-scalar");function ru(s){switch(s){case En:return"<BOM>";case Tn:return"<DOC>";case qn:return"<FLOW_END>";case Ln:return"<SCALAR>";default:return JSON.stringify(s)}}function ou(s){switch(s){case En:return"byte-order-mark";case Tn:return"doc-mode";case qn:return"flow-error-end";case Ln:return"scalar";case"---":return"doc-start";case"...":return"doc-end";case"":case`
`:case`\r
`:return"newline";case"-":return"seq-item-ind";case"?":return"explicit-key-ind";case":":return"map-value-ind";case"{":return"flow-map-start";case"}":return"flow-map-end";case"[":return"flow-seq-start";case"]":return"flow-seq-end";case",":return"comma"}switch(s[0]){case" ":case"	":return"space";case"#":return"comment";case"%":return"directive-line";case"*":return"alias";case"&":return"anchor";case"!":return"tag";case"'":return"single-quoted-scalar";case'"':return"double-quoted-scalar";case"|":case">":return"block-scalar-header"}return null}_.createScalarToken=On.createScalarToken;_.resolveAsScalar=On.resolveAsScalar;_.setScalarValue=On.setScalarValue;_.stringify=tu.stringify;_.visit=su.visit;_.BOM=En;_.DOCUMENT=Tn;_.FLOW_END=qn;_.SCALAR=Ln;_.isCollection=nu;_.isScalar=iu;_.prettyToken=ru;_.tokenType=ou});var Pn=g(Eo=>{"use strict";var We=Wt();function F(s){switch(s){case void 0:case" ":case`
`:case"\r":case"	":return!0;default:return!1}}var Oo=new Set("0123456789ABCDEFabcdef"),au=new Set("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-#;/?:@&=+$_.!~*'()"),Qt=new Set(",[]{}"),lu=new Set(` ,[]{}
\r	`),In=s=>!s||lu.has(s),Cn=class{constructor(){this.atEnd=!1,this.blockScalarIndent=-1,this.blockScalarKeep=!1,this.buffer="",this.flowKey=!1,this.flowLevel=0,this.indentNext=0,this.indentValue=0,this.lineEndPos=null,this.next=null,this.pos=0}*lex(e,t=!1){if(e){if(typeof e!="string")throw TypeError("source is not a string");this.buffer=this.buffer?this.buffer+e:e,this.lineEndPos=null}this.atEnd=!t;let n=this.next??"stream";for(;n&&(t||this.hasChars(1));)n=yield*this.parseNext(n)}atLineEnd(){let e=this.pos,t=this.buffer[e];for(;t===" "||t==="	";)t=this.buffer[++e];return!t||t==="#"||t===`
`?!0:t==="\r"?this.buffer[e+1]===`
`:!1}charAt(e){return this.buffer[this.pos+e]}continueScalar(e){let t=this.buffer[e];if(this.indentNext>0){let n=0;for(;t===" ";)t=this.buffer[++n+e];if(t==="\r"){let i=this.buffer[n+e+1];if(i===`
`||!i&&!this.atEnd)return e+n+1}return t===`
`||n>=this.indentNext||!t&&!this.atEnd?e+n:-1}if(t==="-"||t==="."){let n=this.buffer.substr(e,3);if((n==="---"||n==="...")&&F(this.buffer[e+3]))return-1}return e}getLine(){let e=this.lineEndPos;return(typeof e!="number"||e!==-1&&e<this.pos)&&(e=this.buffer.indexOf(`
`,this.pos),this.lineEndPos=e),e===-1?this.atEnd?this.buffer.substring(this.pos):null:(this.buffer[e-1]==="\r"&&(e-=1),this.buffer.substring(this.pos,e))}hasChars(e){return this.pos+e<=this.buffer.length}setNext(e){return this.buffer=this.buffer.substring(this.pos),this.pos=0,this.lineEndPos=null,this.next=e,null}peek(e){return this.buffer.substr(this.pos,e)}*parseNext(e){switch(e){case"stream":return yield*this.parseStream();case"line-start":return yield*this.parseLineStart();case"block-start":return yield*this.parseBlockStart();case"doc":return yield*this.parseDocument();case"flow":return yield*this.parseFlowCollection();case"quoted-scalar":return yield*this.parseQuotedScalar();case"block-scalar":return yield*this.parseBlockScalar();case"plain-scalar":return yield*this.parsePlainScalar()}}*parseStream(){let e=this.getLine();if(e===null)return this.setNext("stream");if(e[0]===We.BOM&&(yield*this.pushCount(1),e=e.substring(1)),e[0]==="%"){let t=e.length,n=e.indexOf("#");for(;n!==-1;){let r=e[n-1];if(r===" "||r==="	"){t=n-1;break}else n=e.indexOf("#",n+1)}for(;;){let r=e[t-1];if(r===" "||r==="	")t-=1;else break}let i=(yield*this.pushCount(t))+(yield*this.pushSpaces(!0));return yield*this.pushCount(e.length-i),this.pushNewline(),"stream"}if(this.atLineEnd()){let t=yield*this.pushSpaces(!0);return yield*this.pushCount(e.length-t),yield*this.pushNewline(),"stream"}return yield We.DOCUMENT,yield*this.parseLineStart()}*parseLineStart(){let e=this.charAt(0);if(!e&&!this.atEnd)return this.setNext("line-start");if(e==="-"||e==="."){if(!this.atEnd&&!this.hasChars(4))return this.setNext("line-start");let t=this.peek(3);if((t==="---"||t==="...")&&F(this.charAt(3)))return yield*this.pushCount(3),this.indentValue=0,this.indentNext=0,t==="---"?"doc":"stream"}return this.indentValue=yield*this.pushSpaces(!1),this.indentNext>this.indentValue&&!F(this.charAt(1))&&(this.indentNext=this.indentValue),yield*this.parseBlockStart()}*parseBlockStart(){let[e,t]=this.peek(2);if(!t&&!this.atEnd)return this.setNext("block-start");if((e==="-"||e==="?"||e===":")&&F(t)){let n=(yield*this.pushCount(1))+(yield*this.pushSpaces(!0));return this.indentNext=this.indentValue+1,this.indentValue+=n,yield*this.parseBlockStart()}return"doc"}*parseDocument(){yield*this.pushSpaces(!0);let e=this.getLine();if(e===null)return this.setNext("doc");let t=yield*this.pushIndicators();switch(e[t]){case"#":yield*this.pushCount(e.length-t);case void 0:return yield*this.pushNewline(),yield*this.parseLineStart();case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel=1,"flow";case"}":case"]":return yield*this.pushCount(1),"doc";case"*":return yield*this.pushUntil(In),"doc";case'"':case"'":return yield*this.parseQuotedScalar();case"|":case">":return t+=yield*this.parseBlockScalarHeader(),t+=yield*this.pushSpaces(!0),yield*this.pushCount(e.length-t),yield*this.pushNewline(),yield*this.parseBlockScalar();default:return yield*this.parsePlainScalar()}}*parseFlowCollection(){let e,t,n=-1;do e=yield*this.pushNewline(),e>0?(t=yield*this.pushSpaces(!1),this.indentValue=n=t):t=0,t+=yield*this.pushSpaces(!0);while(e+t>0);let i=this.getLine();if(i===null)return this.setNext("flow");if((n!==-1&&n<this.indentNext&&i[0]!=="#"||n===0&&(i.startsWith("---")||i.startsWith("..."))&&F(i[3]))&&!(n===this.indentNext-1&&this.flowLevel===1&&(i[0]==="]"||i[0]==="}")))return this.flowLevel=0,yield We.FLOW_END,yield*this.parseLineStart();let r=0;for(;i[r]===",";)r+=yield*this.pushCount(1),r+=yield*this.pushSpaces(!0),this.flowKey=!1;switch(r+=yield*this.pushIndicators(),i[r]){case void 0:return"flow";case"#":return yield*this.pushCount(i.length-r),"flow";case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel+=1,"flow";case"}":case"]":return yield*this.pushCount(1),this.flowKey=!0,this.flowLevel-=1,this.flowLevel?"flow":"doc";case"*":return yield*this.pushUntil(In),"flow";case'"':case"'":return this.flowKey=!0,yield*this.parseQuotedScalar();case":":{let o=this.charAt(1);if(this.flowKey||F(o)||o===",")return this.flowKey=!1,yield*this.pushCount(1),yield*this.pushSpaces(!0),"flow"}default:return this.flowKey=!1,yield*this.parsePlainScalar()}}*parseQuotedScalar(){let e=this.charAt(0),t=this.buffer.indexOf(e,this.pos+1);if(e==="'")for(;t!==-1&&this.buffer[t+1]==="'";)t=this.buffer.indexOf("'",t+2);else for(;t!==-1;){let r=0;for(;this.buffer[t-1-r]==="\\";)r+=1;if(r%2===0)break;t=this.buffer.indexOf('"',t+1)}let n=this.buffer.substring(0,t),i=n.indexOf(`
`,this.pos);if(i!==-1){for(;i!==-1;){let r=this.continueScalar(i+1);if(r===-1)break;i=n.indexOf(`
`,r)}i!==-1&&(t=i-(n[i-1]==="\r"?2:1))}if(t===-1){if(!this.atEnd)return this.setNext("quoted-scalar");t=this.buffer.length}return yield*this.pushToIndex(t+1,!1),this.flowLevel?"flow":"doc"}*parseBlockScalarHeader(){this.blockScalarIndent=-1,this.blockScalarKeep=!1;let e=this.pos;for(;;){let t=this.buffer[++e];if(t==="+")this.blockScalarKeep=!0;else if(t>"0"&&t<="9")this.blockScalarIndent=Number(t)-1;else if(t!=="-")break}return yield*this.pushUntil(t=>F(t)||t==="#")}*parseBlockScalar(){let e=this.pos-1,t=0,n;e:for(let r=this.pos;n=this.buffer[r];++r)switch(n){case" ":t+=1;break;case`
`:e=r,t=0;break;case"\r":{let o=this.buffer[r+1];if(!o&&!this.atEnd)return this.setNext("block-scalar");if(o===`
`)break}default:break e}if(!n&&!this.atEnd)return this.setNext("block-scalar");if(t>=this.indentNext){this.blockScalarIndent===-1?this.indentNext=t:this.indentNext=this.blockScalarIndent+(this.indentNext===0?1:this.indentNext);do{let r=this.continueScalar(e+1);if(r===-1)break;e=this.buffer.indexOf(`
`,r)}while(e!==-1);if(e===-1){if(!this.atEnd)return this.setNext("block-scalar");e=this.buffer.length}}let i=e+1;for(n=this.buffer[i];n===" ";)n=this.buffer[++i];if(n==="	"){for(;n==="	"||n===" "||n==="\r"||n===`
`;)n=this.buffer[++i];e=i-1}else if(!this.blockScalarKeep)do{let r=e-1,o=this.buffer[r];o==="\r"&&(o=this.buffer[--r]);let a=r;for(;o===" ";)o=this.buffer[--r];if(o===`
`&&r>=this.pos&&r+1+t>a)e=r;else break}while(!0);return yield We.SCALAR,yield*this.pushToIndex(e+1,!0),yield*this.parseLineStart()}*parsePlainScalar(){let e=this.flowLevel>0,t=this.pos-1,n=this.pos-1,i;for(;i=this.buffer[++n];)if(i===":"){let r=this.buffer[n+1];if(F(r)||e&&Qt.has(r))break;t=n}else if(F(i)){let r=this.buffer[n+1];if(i==="\r"&&(r===`
`?(n+=1,i=`
`,r=this.buffer[n+1]):t=n),r==="#"||e&&Qt.has(r))break;if(i===`
`){let o=this.continueScalar(n+1);if(o===-1)break;n=Math.max(n,o-2)}}else{if(e&&Qt.has(i))break;t=n}return!i&&!this.atEnd?this.setNext("plain-scalar"):(yield We.SCALAR,yield*this.pushToIndex(t+1,!0),e?"flow":"doc")}*pushCount(e){return e>0?(yield this.buffer.substr(this.pos,e),this.pos+=e,e):0}*pushToIndex(e,t){let n=this.buffer.slice(this.pos,e);return n?(yield n,this.pos+=n.length,n.length):(t&&(yield""),0)}*pushIndicators(){switch(this.charAt(0)){case"!":return(yield*this.pushTag())+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"&":return(yield*this.pushUntil(In))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"-":case"?":case":":{let e=this.flowLevel>0,t=this.charAt(1);if(F(t)||e&&Qt.has(t))return e?this.flowKey&&(this.flowKey=!1):this.indentNext=this.indentValue+1,(yield*this.pushCount(1))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators())}}return 0}*pushTag(){if(this.charAt(1)==="<"){let e=this.pos+2,t=this.buffer[e];for(;!F(t)&&t!==">";)t=this.buffer[++e];return yield*this.pushToIndex(t===">"?e+1:e,!1)}else{let e=this.pos+1,t=this.buffer[e];for(;t;)if(au.has(t))t=this.buffer[++e];else if(t==="%"&&Oo.has(this.buffer[e+1])&&Oo.has(this.buffer[e+2]))t=this.buffer[e+=3];else break;return yield*this.pushToIndex(e,!1)}}*pushNewline(){let e=this.buffer[this.pos];return e===`
`?yield*this.pushCount(1):e==="\r"&&this.charAt(1)===`
`?yield*this.pushCount(2):0}*pushSpaces(e){let t=this.pos-1,n;do n=this.buffer[++t];while(n===" "||e&&n==="	");let i=t-this.pos;return i>0&&(yield this.buffer.substr(this.pos,i),this.pos=t),i}*pushUntil(e){let t=this.pos,n=this.buffer[t];for(;!e(n);)n=this.buffer[++t];return yield*this.pushToIndex(t,!1)}};Eo.Lexer=Cn});var _n=g(To=>{"use strict";var Mn=class{constructor(){this.lineStarts=[],this.addNewLine=e=>this.lineStarts.push(e),this.linePos=e=>{let t=0,n=this.lineStarts.length;for(;t<n;){let r=t+n>>1;this.lineStarts[r]<e?t=r+1:n=r}if(this.lineStarts[t]===e)return{line:t+1,col:1};if(t===0)return{line:0,col:e};let i=this.lineStarts[t-1];return{line:t,col:e-i+1}}}};To.LineCounter=Mn});var Dn=g(Po=>{"use strict";var cu=require("node:process"),qo=Wt(),fu=Pn();function le(s,e){for(let t=0;t<s.length;++t)if(s[t].type===e)return!0;return!1}function Lo(s){for(let e=0;e<s.length;++e)switch(s[e].type){case"space":case"comment":case"newline":break;default:return e}return-1}function Co(s){switch(s?.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"flow-collection":return!0;default:return!1}}function Ht(s){switch(s.type){case"document":return s.start;case"block-map":{let e=s.items[s.items.length-1];return e.sep??e.start}case"block-seq":return s.items[s.items.length-1].start;default:return[]}}function Se(s){if(s.length===0)return[];let e=s.length;e:for(;--e>=0;)switch(s[e].type){case"doc-start":case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":case"newline":break e}for(;s[++e]?.type==="space";);return s.splice(e,s.length)}function Io(s){if(s.start.type==="flow-seq-start")for(let e of s.items)e.sep&&!e.value&&!le(e.start,"explicit-key-ind")&&!le(e.sep,"map-value-ind")&&(e.key&&(e.value=e.key),delete e.key,Co(e.value)?e.value.end?Array.prototype.push.apply(e.value.end,e.sep):e.value.end=e.sep:Array.prototype.push.apply(e.start,e.sep),delete e.sep)}var $n=class{constructor(e){this.atNewLine=!0,this.atScalar=!1,this.indent=0,this.offset=0,this.onKeyLine=!1,this.stack=[],this.source="",this.type="",this.lexer=new fu.Lexer,this.onNewLine=e}*parse(e,t=!1){this.onNewLine&&this.offset===0&&this.onNewLine(0);for(let n of this.lexer.lex(e,t))yield*this.next(n);t||(yield*this.end())}*next(e){if(this.source=e,cu.env.LOG_TOKENS&&console.log("|",qo.prettyToken(e)),this.atScalar){this.atScalar=!1,yield*this.step(),this.offset+=e.length;return}let t=qo.tokenType(e);if(t)if(t==="scalar")this.atNewLine=!1,this.atScalar=!0,this.type="scalar";else{switch(this.type=t,yield*this.step(),t){case"newline":this.atNewLine=!0,this.indent=0,this.onNewLine&&this.onNewLine(this.offset+e.length);break;case"space":this.atNewLine&&e[0]===" "&&(this.indent+=e.length);break;case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":this.atNewLine&&(this.indent+=e.length);break;case"doc-mode":case"flow-error-end":return;default:this.atNewLine=!1}this.offset+=e.length}else{let n=`Not a YAML token: ${e}`;yield*this.pop({type:"error",offset:this.offset,message:n,source:e}),this.offset+=e.length}}*end(){for(;this.stack.length>0;)yield*this.pop()}get sourceToken(){return{type:this.type,offset:this.offset,indent:this.indent,source:this.source}}*step(){let e=this.peek(1);if(this.type==="doc-end"&&(!e||e.type!=="doc-end")){for(;this.stack.length>0;)yield*this.pop();this.stack.push({type:"doc-end",offset:this.offset,source:this.source});return}if(!e)return yield*this.stream();switch(e.type){case"document":return yield*this.document(e);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return yield*this.scalar(e);case"block-scalar":return yield*this.blockScalar(e);case"block-map":return yield*this.blockMap(e);case"block-seq":return yield*this.blockSequence(e);case"flow-collection":return yield*this.flowCollection(e);case"doc-end":return yield*this.documentEnd(e)}yield*this.pop()}peek(e){return this.stack[this.stack.length-e]}*pop(e){let t=e??this.stack.pop();if(!t)yield{type:"error",offset:this.offset,source:"",message:"Tried to pop an empty stack"};else if(this.stack.length===0)yield t;else{let n=this.peek(1);switch(t.type==="block-scalar"?t.indent="indent"in n?n.indent:0:t.type==="flow-collection"&&n.type==="document"&&(t.indent=0),t.type==="flow-collection"&&Io(t),n.type){case"document":n.value=t;break;case"block-scalar":n.props.push(t);break;case"block-map":{let i=n.items[n.items.length-1];if(i.value){n.items.push({start:[],key:t,sep:[]}),this.onKeyLine=!0;return}else if(i.sep)i.value=t;else{Object.assign(i,{key:t,sep:[]}),this.onKeyLine=!i.explicitKey;return}break}case"block-seq":{let i=n.items[n.items.length-1];i.value?n.items.push({start:[],value:t}):i.value=t;break}case"flow-collection":{let i=n.items[n.items.length-1];!i||i.value?n.items.push({start:[],key:t,sep:[]}):i.sep?i.value=t:Object.assign(i,{key:t,sep:[]});return}default:yield*this.pop(),yield*this.pop(t)}if((n.type==="document"||n.type==="block-map"||n.type==="block-seq")&&(t.type==="block-map"||t.type==="block-seq")){let i=t.items[t.items.length-1];i&&!i.sep&&!i.value&&i.start.length>0&&Lo(i.start)===-1&&(t.indent===0||i.start.every(r=>r.type!=="comment"||r.indent<t.indent))&&(n.type==="document"?n.end=i.start:n.items.push({start:i.start}),t.items.splice(-1,1))}}}*stream(){switch(this.type){case"directive-line":yield{type:"directive",offset:this.offset,source:this.source};return;case"byte-order-mark":case"space":case"comment":case"newline":yield this.sourceToken;return;case"doc-mode":case"doc-start":{let e={type:"document",offset:this.offset,start:[]};this.type==="doc-start"&&e.start.push(this.sourceToken),this.stack.push(e);return}}yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML stream`,source:this.source}}*document(e){if(e.value)return yield*this.lineEnd(e);switch(this.type){case"doc-start":{Lo(e.start)!==-1?(yield*this.pop(),yield*this.step()):e.start.push(this.sourceToken);return}case"anchor":case"tag":case"space":case"comment":case"newline":e.start.push(this.sourceToken);return}let t=this.startBlockValue(e);t?this.stack.push(t):yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML document`,source:this.source}}*scalar(e){if(this.type==="map-value-ind"){let t=Ht(this.peek(2)),n=Se(t),i;e.end?(i=e.end,i.push(this.sourceToken),delete e.end):i=[this.sourceToken];let r={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:n,key:e,sep:i}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=r}else yield*this.lineEnd(e)}*blockScalar(e){switch(this.type){case"space":case"comment":case"newline":e.props.push(this.sourceToken);return;case"scalar":if(e.source=this.source,this.atNewLine=!0,this.indent=0,this.onNewLine){let t=this.source.indexOf(`
`)+1;for(;t!==0;)this.onNewLine(this.offset+t),t=this.source.indexOf(`
`,t)+1}yield*this.pop();break;default:yield*this.pop(),yield*this.step()}}*blockMap(e){let t=e.items[e.items.length-1];switch(this.type){case"newline":if(this.onKeyLine=!1,t.value){let n="end"in t.value?t.value.end:void 0;(Array.isArray(n)?n[n.length-1]:void 0)?.type==="comment"?n?.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"space":case"comment":if(t.value)e.items.push({start:[this.sourceToken]});else if(t.sep)t.sep.push(this.sourceToken);else{if(this.atIndentedComment(t.start,e.indent)){let i=e.items[e.items.length-2]?.value?.end;if(Array.isArray(i)){Array.prototype.push.apply(i,t.start),i.push(this.sourceToken),e.items.pop();return}}t.start.push(this.sourceToken)}return}if(this.indent>=e.indent){let n=!this.onKeyLine&&this.indent===e.indent,i=n&&(t.sep||t.explicitKey)&&this.type!=="seq-item-ind",r=[];if(i&&t.sep&&!t.value){let o=[];for(let a=0;a<t.sep.length;++a){let l=t.sep[a];switch(l.type){case"newline":o.push(a);break;case"space":break;case"comment":l.indent>e.indent&&(o.length=0);break;default:o.length=0}}o.length>=2&&(r=t.sep.splice(o[1]))}switch(this.type){case"anchor":case"tag":i||t.value?(r.push(this.sourceToken),e.items.push({start:r}),this.onKeyLine=!0):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"explicit-key-ind":!t.sep&&!t.explicitKey?(t.start.push(this.sourceToken),t.explicitKey=!0):i||t.value?(r.push(this.sourceToken),e.items.push({start:r,explicitKey:!0})):this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken],explicitKey:!0}]}),this.onKeyLine=!0;return;case"map-value-ind":if(t.explicitKey)if(t.sep)if(t.value)e.items.push({start:[],key:null,sep:[this.sourceToken]});else if(le(t.sep,"map-value-ind"))this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:r,key:null,sep:[this.sourceToken]}]});else if(Co(t.key)&&!le(t.sep,"newline")){let o=Se(t.start),a=t.key,l=t.sep;l.push(this.sourceToken),delete t.key,delete t.sep,this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:o,key:a,sep:l}]})}else r.length>0?t.sep=t.sep.concat(r,this.sourceToken):t.sep.push(this.sourceToken);else if(le(t.start,"newline"))Object.assign(t,{key:null,sep:[this.sourceToken]});else{let o=Se(t.start);this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:o,key:null,sep:[this.sourceToken]}]})}else t.sep?t.value||i?e.items.push({start:r,key:null,sep:[this.sourceToken]}):le(t.sep,"map-value-ind")?this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[],key:null,sep:[this.sourceToken]}]}):t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]});this.onKeyLine=!0;return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{let o=this.flowScalar(this.type);i||t.value?(e.items.push({start:r,key:o,sep:[]}),this.onKeyLine=!0):t.sep?this.stack.push(o):(Object.assign(t,{key:o,sep:[]}),this.onKeyLine=!0);return}default:{let o=this.startBlockValue(e);if(o){n&&o.type!=="block-seq"&&e.items.push({start:r}),this.stack.push(o);return}}}}yield*this.pop(),yield*this.step()}*blockSequence(e){let t=e.items[e.items.length-1];switch(this.type){case"newline":if(t.value){let n="end"in t.value?t.value.end:void 0;(Array.isArray(n)?n[n.length-1]:void 0)?.type==="comment"?n?.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else t.start.push(this.sourceToken);return;case"space":case"comment":if(t.value)e.items.push({start:[this.sourceToken]});else{if(this.atIndentedComment(t.start,e.indent)){let i=e.items[e.items.length-2]?.value?.end;if(Array.isArray(i)){Array.prototype.push.apply(i,t.start),i.push(this.sourceToken),e.items.pop();return}}t.start.push(this.sourceToken)}return;case"anchor":case"tag":if(t.value||this.indent<=e.indent)break;t.start.push(this.sourceToken);return;case"seq-item-ind":if(this.indent!==e.indent)break;t.value||le(t.start,"seq-item-ind")?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken);return}if(this.indent>e.indent){let n=this.startBlockValue(e);if(n){this.stack.push(n);return}}yield*this.pop(),yield*this.step()}*flowCollection(e){let t=e.items[e.items.length-1];if(this.type==="flow-error-end"){let n;do yield*this.pop(),n=this.peek(1);while(n&&n.type==="flow-collection")}else if(e.end.length===0){switch(this.type){case"comma":case"explicit-key-ind":!t||t.sep?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken);return;case"map-value-ind":!t||t.value?e.items.push({start:[],key:null,sep:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]});return;case"space":case"comment":case"newline":case"anchor":case"tag":!t||t.value?e.items.push({start:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{let i=this.flowScalar(this.type);!t||t.value?e.items.push({start:[],key:i,sep:[]}):t.sep?this.stack.push(i):Object.assign(t,{key:i,sep:[]});return}case"flow-map-end":case"flow-seq-end":e.end.push(this.sourceToken);return}let n=this.startBlockValue(e);n?this.stack.push(n):(yield*this.pop(),yield*this.step())}else{let n=this.peek(2);if(n.type==="block-map"&&(this.type==="map-value-ind"&&n.indent===e.indent||this.type==="newline"&&!n.items[n.items.length-1].sep))yield*this.pop(),yield*this.step();else if(this.type==="map-value-ind"&&n.type!=="flow-collection"){let i=Ht(n),r=Se(i);Io(e);let o=e.end.splice(1,e.end.length);o.push(this.sourceToken);let a={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:r,key:e,sep:o}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=a}else yield*this.lineEnd(e)}}flowScalar(e){if(this.onNewLine){let t=this.source.indexOf(`
`)+1;for(;t!==0;)this.onNewLine(this.offset+t),t=this.source.indexOf(`
`,t)+1}return{type:e,offset:this.offset,indent:this.indent,source:this.source}}startBlockValue(e){switch(this.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return this.flowScalar(this.type);case"block-scalar-header":return{type:"block-scalar",offset:this.offset,indent:this.indent,props:[this.sourceToken],source:""};case"flow-map-start":case"flow-seq-start":return{type:"flow-collection",offset:this.offset,indent:this.indent,start:this.sourceToken,items:[],end:[]};case"seq-item-ind":return{type:"block-seq",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken]}]};case"explicit-key-ind":{this.onKeyLine=!0;let t=Ht(e),n=Se(t);return n.push(this.sourceToken),{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:n,explicitKey:!0}]}}case"map-value-ind":{this.onKeyLine=!0;let t=Ht(e),n=Se(t);return{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:n,key:null,sep:[this.sourceToken]}]}}}return null}atIndentedComment(e,t){return this.type!=="comment"||this.indent<=t?!1:e.every(n=>n.type==="newline"||n.type==="space")}*documentEnd(e){this.type!=="doc-mode"&&(e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop()))}*lineEnd(e){switch(this.type){case"comma":case"doc-start":case"doc-end":case"flow-seq-end":case"flow-map-end":case"map-value-ind":yield*this.pop(),yield*this.step();break;case"newline":this.onKeyLine=!1;case"space":case"comment":default:e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop())}}};Po.Parser=$n});var Bo=g(He=>{"use strict";var Mo=kn(),uu=je(),Qe=Ue(),hu=Ss(),du=O(),pu=_n(),_o=Dn();function $o(s){let e=s.prettyErrors!==!1;return{lineCounter:s.lineCounter||e&&new pu.LineCounter||null,prettyErrors:e}}function mu(s,e={}){let{lineCounter:t,prettyErrors:n}=$o(e),i=new _o.Parser(t?.addNewLine),r=new Mo.Composer(e),o=Array.from(r.compose(i.parse(s)));if(n&&t)for(let a of o)a.errors.forEach(Qe.prettifyError(s,t)),a.warnings.forEach(Qe.prettifyError(s,t));return o.length>0?o:Object.assign([],{empty:!0},r.streamInfo())}function Do(s,e={}){let{lineCounter:t,prettyErrors:n}=$o(e),i=new _o.Parser(t?.addNewLine),r=new Mo.Composer(e),o=null;for(let a of r.compose(i.parse(s),!0,s.length))if(!o)o=a;else if(o.options.logLevel!=="silent"){o.errors.push(new Qe.YAMLParseError(a.range.slice(0,2),"MULTIPLE_DOCS","Source contains multiple documents; please use YAML.parseAllDocuments()"));break}return n&&t&&(o.errors.forEach(Qe.prettifyError(s,t)),o.warnings.forEach(Qe.prettifyError(s,t))),o}function gu(s,e,t){let n;typeof e=="function"?n=e:t===void 0&&e&&typeof e=="object"&&(t=e);let i=Do(s,t);if(!i)return null;if(i.warnings.forEach(r=>hu.warn(i.options.logLevel,r)),i.errors.length>0){if(i.options.logLevel!=="silent")throw i.errors[0];i.errors=[]}return i.toJS(Object.assign({reviver:n},t))}function yu(s,e,t){let n=null;if(typeof e=="function"||Array.isArray(e)?n=e:t===void 0&&e&&(t=e),typeof t=="string"&&(t=t.length),typeof t=="number"){let i=Math.round(t);t=i<1?void 0:i>8?{indent:8}:{indent:i}}if(s===void 0){let{keepUndefined:i}=t??e??{};if(!i)return}return du.isDocument(s)&&!n?s.toString(t):new uu.Document(s,n,t).toString(t)}He.parse=gu;He.parseAllDocuments=mu;He.parseDocument=Do;He.stringify=yu});var Ro=g(E=>{"use strict";var bu=kn(),Su=je(),wu=tn(),Bn=Ue(),vu=Ae(),Z=O(),ku=Q(),Nu=q(),Au=X(),Ou=z(),Eu=Wt(),Tu=Pn(),qu=_n(),Lu=Dn(),Xt=Bo(),Fo=we();E.Composer=bu.Composer;E.Document=Su.Document;E.Schema=wu.Schema;E.YAMLError=Bn.YAMLError;E.YAMLParseError=Bn.YAMLParseError;E.YAMLWarning=Bn.YAMLWarning;E.Alias=vu.Alias;E.isAlias=Z.isAlias;E.isCollection=Z.isCollection;E.isDocument=Z.isDocument;E.isMap=Z.isMap;E.isNode=Z.isNode;E.isPair=Z.isPair;E.isScalar=Z.isScalar;E.isSeq=Z.isSeq;E.Pair=ku.Pair;E.Scalar=Nu.Scalar;E.YAMLMap=Au.YAMLMap;E.YAMLSeq=Ou.YAMLSeq;E.CST=Eu;E.Lexer=Tu.Lexer;E.LineCounter=qu.LineCounter;E.Parser=Lu.Parser;E.parse=Xt.parse;E.parseAllDocuments=Xt.parseAllDocuments;E.parseDocument=Xt.parseDocument;E.stringify=Xt.stringify;E.visit=Fo.visit;E.visitAsync=Fo.visitAsync});var _u={};ea(_u,{default:()=>Wo});module.exports=ta(_u);var $=require("@raycast/api");var xn=1024,es=xn**2,Du=es**2;var sa=require("@raycast/api");function ts(s){switch(s.type){case"obsidian://open?vault=":return"obsidian://open?vault="+encodeURIComponent(s.vault.name);case"obsidian://open?path=":return"obsidian://open?path="+encodeURIComponent(s.path);case"obsidian://advanced-uri?daily=true&vault=":return"obsidian://advanced-uri?daily=true&vault="+encodeURIComponent(s.vault.name);case"obsidian://advanced-uri?daily=true":{let e=s.heading?"&heading="+encodeURIComponent(s.heading):"";return"obsidian://advanced-uri?daily=true"+(s.prepend?"&mode=prepend":"&mode=append")+"&data="+encodeURIComponent(s.text)+"&vault="+encodeURIComponent(s.vault.name)+e+(s.silent?"&openmode=silent":"")}case"obsidian://new?vault=":return"obsidian://new?vault="+encodeURIComponent(s.vault.name)+"&name="+encodeURIComponent(s.name)+"&content="+encodeURIComponent(s.content||"");case"obsidian://advanced-uri?mode=append&filepath=":{let e=s.heading?"&heading="+encodeURIComponent(s.heading):"";return"obsidian://advanced-uri?mode=append&filepath="+encodeURIComponent(s.path)+"&data="+encodeURIComponent(s.text)+"&vault="+encodeURIComponent(s.vault.name)+e+(s.silent?"&openmode=silent":"")}default:return""}}var Un=require("@raycast/api"),Yn=require("react/jsx-runtime");function Jn(){return(0,Yn.jsx)(Un.Detail,{markdown:`# No vaults found

 Please use Obsidian to create a vault, or set a vault path in the extension's preferences before using this command.`})}var et=require("@raycast/api");function Gn(s){(0,et.showToast)({title:"Vaults without Advanced URI plugin:",message:s.map(e=>e.name).join(", "),style:et.Toast.Style.Failure})}var Wn=require("@raycast/api"),Qn=require("react/jsx-runtime");function ss({vaultName:s}){let t=`# Advanced URI plugin not installed in ${s?`vault "${s}"`:"any vault"}.
This command requires the [Advanced URI plugin](https://obsidian.md/plugins?id=obsidian-advanced-uri) for Obsidian.  
  
 Install it through the community plugins list.`;return(0,Qn.jsx)(Wn.Detail,{navigationTitle:"Advanced URI plugin not installed",markdown:t})}var Zt=require("@raycast/api"),j=require("react");var Fn=require("@raycast/api"),Xe=Ze(require("fs")),Ko=require("fs/promises"),jo=require("os"),zt=Ze(require("path"));var Cu=Ze(Ro());var Pu=require("@raycast/api");var ee=class{constructor(e){this.name=e||"Logger"}timestamp(){return new Date().toISOString()}formatMessage(e){if(typeof e=="string")return e;if(e instanceof Error)return`${e.message}
${e.stack}`;if(typeof e=="object"&&e!==null)try{return JSON.stringify(e,null,2)}catch{return String(e)}return String(e)}info(e){console.log(`[${this.timestamp()}] [${this.name}] [INFO] ${this.formatMessage(e)}`)}success(e){console.log(`[${this.timestamp()}] [${this.name}] [SUCCESS] ${this.formatMessage(e)}`)}warning(e){console.log(`[${this.timestamp()}] [${this.name}] [WARNING] ${this.formatMessage(e)}`)}error(e){console.log(`[${this.timestamp()}] [${this.name}] [ERROR] ${this.formatMessage(e)}`)}debug(e){console.log(`[${this.timestamp()}] [${this.name}] [DEBUG] ${this.formatMessage(e)}`)}trace(e){console.log(`[${this.timestamp()}] [${this.name}] [TRACE] ${this.formatMessage(e)}`)}};var md=new ee("Bookmarks");function Vo(s){let e=s.split(zt.default.sep).filter(t=>{if(t!="")return t}).pop();return e||"Default Vault Name (check your path preferences)"}function Rn(){return(0,Fn.getPreferenceValues)().vaultPath.split(",").filter(t=>t.trim()!=="").filter(t=>Xe.existsSync(t)).map(t=>({name:Vo(t.trim()),key:t.trim(),path:t.trim()}))}async function xo(){let s=zt.default.resolve(`${(0,jo.homedir)()}/Library/Application Support/obsidian/obsidian.json`);try{let e=JSON.parse(await(0,Ko.readFile)(s,"utf8"));return Object.values(e.vaults).map(({path:t})=>({name:Vo(t),key:t,path:t}))}catch{return[]}}var Uo=require("@raycast/api");var Cd=new ee("Cache"),Pd=new Uo.Cache({capacity:es*500});var Mu=new ee("Hooks"),Kd=(0,j.createContext)([]),jd=(0,j.createContext)(()=>{});function Jo(){let s=(0,j.useMemo)(()=>(0,Zt.getPreferenceValues)(),[]),[e,t]=(0,j.useState)(s.vaultPath?{ready:!0,vaults:Rn()}:{ready:!1,vaults:[]});return Mu.info("useObsidianVaults hook called"),(0,j.useEffect)(()=>{e.ready||xo().then(n=>{t({vaults:n,ready:!0})}).catch(()=>t({vaults:Rn(),ready:!0}))},[]),e}var Yo=require("@raycast/api"),Kn=Ze(require("fs"));function Go(s,e){let{configFileName:t}=(0,Yo.getPreferenceValues)(),n=[];return[s.filter(r=>{let o=`${r.path}/${t||".obsidian"}/community-plugins.json`;if(!Kn.default.existsSync(o))return n.push(r),!1;let l=JSON.parse(Kn.default.readFileSync(o,"utf-8")).includes(e);return l||n.push(r),l}),n]}var te=require("react/jsx-runtime");function Wo(){let{vaults:s,ready:e}=Jo();if(e){if(s.length===0)return(0,te.jsx)(Jn,{})}else return(0,te.jsx)($.List,{isLoading:!0});let[t,n]=Go(s,"obsidian-advanced-uri");if(n.length>0&&Gn(n),t.length==0)return(0,te.jsx)(ss,{});if(t.length==1){let i=ts({type:"obsidian://advanced-uri?daily=true&vault=",vault:t[0]});(0,$.open)(i),(0,$.popToRoot)(),(0,$.closeMainWindow)()}return(0,te.jsx)($.List,{isLoading:t===void 0,children:t?.map(i=>(0,te.jsx)($.List.Item,{title:i.name,actions:(0,te.jsx)($.ActionPanel,{children:(0,te.jsx)($.Action.Open,{title:"Daily Note",target:ts({type:"obsidian://advanced-uri?daily=true&vault=",vault:i})})})},i.key))})}
