{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "browser-bookmarks", "title": "Browser Bookmarks", "description": "Integrate bookmarks from Chrome, Brave, Edge, Firefox, Safari, Arc, Vivaldi, Zen or Whale.", "icon": "extension-icon.png", "author": "thoma<PERSON><PERSON><PERSON><PERSON>", "owner": "raycast", "access": "public", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ahp", "juang970", "tleo19", "jum8ys", "Selfish", "theherk", "zhsks311", "danie<PERSON>-nagy"], "pastContributors": ["da<PERSON><PERSON><PERSON>"], "categories": ["Web"], "license": "MIT", "preferences": [{"type": "checkbox", "label": "Show Bookmark Domain", "name": "showDomain", "description": "When enabled, the domain of the bookmark will be displayed as a subtitle.", "default": true, "required": false}, {"type": "checkbox", "label": "Open Bookmark's Browser", "name": "openBookmarkBrowser", "description": "When enabled, the extension will open the browser from which the bookmark originated, instead of your default browser.", "default": true, "required": false}], "commands": [{"name": "index", "title": "Search Browser Bookmarks", "subtitle": "Browser Bookmarks", "description": "Search bookmarks from your browsers.", "mode": "view"}], "dependencies": {"@raycast/api": "^1.64.0", "@raycast/utils": "^1.10.0", "fuse.js": "^7.0.0", "ini": "^4.1.1", "simple-plist": "^1.4.0-0", "sql.js": "^1.8.0"}, "devDependencies": {"@raycast/eslint-config": "^1.0.8", "@types/ini": "^1.3.32", "@types/node": "18.8.3", "@types/plist": "^3.0.4", "@types/react": "18.2.31", "@types/sql.js": "^1.4.7", "eslint": "^8.51.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "3.0.3", "typescript": "^5.2.2"}, "scripts": {"build": "ray build -e dist", "dev": "ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "publish": "ray publish"}}