{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "slack", "title": "<PERSON><PERSON>ck", "description": "Search for chats, see unread messages, snooze notifications, and set your presence status.", "icon": "slack-icon-rounded.png", "author": "mommertf", "contributors": ["stelo", "thoma<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "pastContributors": ["Elliot67"], "categories": ["Communication", "Productivity"], "license": "MIT", "commands": [{"name": "search", "title": "Open Channel", "subtitle": "<PERSON><PERSON>ck", "keywords": ["search"], "description": "Search for all workspace members, group chats, and channels and open them directly in your Slack app.", "mode": "view", "preferences": [{"title": "Channel Metadata", "label": "Display extra metadata in results", "description": "Show team member's titles, time zone, and current time in Open Channel results", "name": "displayExtraMetadata", "type": "checkbox", "required": false, "default": false}]}, {"name": "search-messages", "title": "Search Messages", "subtitle": "<PERSON><PERSON>ck", "description": "Search for Slack messages.", "mode": "view"}, {"name": "send-message", "title": "Send Message", "subtitle": "<PERSON><PERSON>ck", "description": "Send a message to a channel or user in Slack.", "mode": "view"}, {"name": "open-unread-messages", "title": "Open Unread Messages", "subtitle": "<PERSON><PERSON>ck", "description": "Open an overview of unread messages directly in your native Slack app.", "mode": "no-view"}, {"name": "unread-messages", "title": "Unread Messages", "subtitle": "<PERSON><PERSON>ck", "description": "Browse through unread messages within direct messages, channels and groups.", "mode": "view"}, {"name": "set-snooze", "title": "Set <PERSON>nooze", "subtitle": "<PERSON><PERSON>ck", "description": "Set settings for how long you want to snooze notifications.", "mode": "view"}, {"name": "set-presence", "title": "Set Presence", "subtitle": "<PERSON><PERSON>ck", "description": "Let <PERSON><PERSON><PERSON> decide automatically how to show your presence status or force it to be shown as offline.", "mode": "view"}, {"name": "search-emojis", "title": "Search Emojis", "subtitle": "<PERSON><PERSON>ck", "description": "Search custom emojis in your Slack team.", "mode": "view"}], "preferences": [{"description": "View extension's README for instructions.", "name": "accessToken", "placeholder": "xoxp-...", "required": false, "title": "Slack Access Token", "type": "password"}, {"title": "Navigation", "label": "Close Slack right sidebar", "description": "Automatically close Slack right sidebar after navigating to a user chat", "name": "closeRightSidebar", "type": "checkbox", "required": false, "default": true}], "tools": [{"title": "Get Channels", "name": "get-channels", "description": "Lists all channels in the Slack team", "confirmation": false}, {"title": "Get Channel History", "name": "get-channel-history", "description": "Fetches a channel's history of messages and events.", "input": {"type": "object", "properties": {"channelId": {"type": "string", "description": "The id of the channel to fetch history (eg. messages) for. Use `getChannels` to get the list of channels with their ids."}, "after": {"type": "string", "description": "Only messages after this ISO 8601 timestamp will be included in results. If ommited, the last 30 messages of the channel will be returned.\n\n@example \"2025-03-26T00:00:00Z\""}}, "required": ["channelId"]}, "confirmation": false}, {"title": "Get Users", "name": "get-users", "description": "List all users in the Slack team", "confirmation": false}, {"title": "Set Status", "name": "set-status", "description": "Set or unset the status of the current user", "input": {"type": "object", "properties": {"text": {"type": "string", "description": "A string value for status text, should be short and sweet, with no punctuation, e.g. \"Working out\", \"Listening to <PERSON>'s new album\", \"Coffe break\". It should not include the status duration for example \"Working out\" instead of \"Working out for 2 hours\" or \"Working out until tomorrow\".\n\nTo unset the status, provide an empty string."}, "emoji": {"type": "string", "description": "A Slack-compatible string for single emoji matching the text of the status. Emojis should be in the form: :<emoji identifier>:. If the user doesn't specify an emoji, come up with one that matches the text.\n\nYou can call the `get-emojis` tool to get a list of all custom emojis in the workspace. Do this only if you think the user is using a custom emoji.\n\nTo unset the status, provide an empty string."}, "duration": {"type": "number", "description": "An integer representing the duration of the status in seconds. Only provide it if the user has specified a time or the end of status"}}, "required": ["text", "emoji"]}, "confirmation": false}, {"title": "Get Emojis", "name": "get-emojis", "description": "List all custom emojis in the Slack team", "confirmation": false}], "ai": {"evals": [{"input": "@slack set my status to away", "expected": [{"callsTool": {"name": "set-status", "arguments": {"text": {"includes": "away"}}}}], "mocks": {"set-status": "success"}}, {"input": "@slack summarize what happened in #general yesterday", "expected": [{"callsTool": {"name": "get-channels"}}], "mocks": {"get-channels": []}}]}, "dependencies": {"@raycast/api": "^1.94.0", "@raycast/utils": "^1.18.0", "@slack/web-api": "^7.0.4", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "lodash": "^4.17.21", "node-emoji": "^2.1.3"}, "devDependencies": {"@raycast/eslint-config": "^1.0.8", "@types/lodash": "^4.17.5", "@types/node": "^22.13.10", "@types/node-emoji": "^2.1.0", "@types/react": "^19.0.10", "eslint": "^8.57.0", "prettier": "^3.3.2", "react": "^18.2.0", "react-devtools": "^6.1.1", "typescript": "^5.4.5"}, "scripts": {"build": "ray build -e dist", "dev": "ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "publish": "npx @raycast/api@latest publish"}, "volta": {"node": "18.16.0", "npm": "9.6.4"}, "platforms": ["macOS"]}