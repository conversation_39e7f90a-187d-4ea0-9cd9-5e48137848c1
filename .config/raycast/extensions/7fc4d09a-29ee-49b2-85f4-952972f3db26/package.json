{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "quick-event", "title": "Quick Event", "version": "1.0.0", "description": "Create a calendar event using natural language", "icon": "command-icon.png", "author": "mblode", "contributors": ["bascomb", "Whitespace", "ridemountainpig"], "license": "MIT", "commands": [{"name": "index", "title": "Create Quick Event", "subtitle": "Calendar", "description": "Create a new event in your calendar", "mode": "view"}], "preferences": [{"name": "calendars", "type": "textfield", "required": true, "title": "Your calendars", "description": "Specify your calendar or multiple calendars (comma separated)", "placeholder": "calendar1,calendar2,..."}, {"name": "focus", "type": "checkbox", "required": false, "default": false, "label": "Focus Calendar on Completion", "title": "Focus Calendar on Completion", "description": "Checkbox to focus on created event in Calendar application on completion"}], "dependencies": {"@raycast/api": "^1.93.0", "date-fns": "^2.28.0", "dayjs": "^1.10.7", "nanoid": "^5.1.3", "osascript-tag": "^0.1.2", "sherlockjs": "^1.4.2"}, "devDependencies": {"@raycast/eslint-config": "^1.0.11", "@types/lodash.groupby": "^4.6.6", "@types/lodash.map": "^4.6.13", "@types/node": "^20.8.10", "@types/react": "^18.3.3", "eslint": "^7.32.0", "react": "^18.2.0", "react-devtools": "^5.2.0", "typescript": "^4.4.3"}, "scripts": {"build": "ray build -e dist", "dev": "ray develop", "lint": "ray lint"}, "platforms": ["macOS"]}