"use strict";var r=Object.defineProperty;var w=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var g=Object.prototype.hasOwnProperty;var f=(e,n)=>{for(var t in n)r(e,t,{get:n[t],enumerable:!0})},U=(e,n,t,i)=>{if(n&&typeof n=="object"||typeof n=="function")for(let o of m(n))!g.call(e,o)&&o!==t&&r(e,o,{get:()=>n[o],enumerable:!(i=w(n,o))||i.enumerable});return e};var d=e=>U(r({},"__esModule",{value:!0}),e);var u={};f(u,{default:()=>s});module.exports=d(u);var c=require("@raycast/api");var p=require("@raycast/api"),h=e=>{let{warpApp:n}=(0,p.getPreferenceValues)();return`${n==="preview"?"warppreview://":"warp://"}${e}`},a=e=>h(`action/new_tab?path=${encodeURIComponent(e)}`);async function s(){await(0,c.open)(a("~"))}
