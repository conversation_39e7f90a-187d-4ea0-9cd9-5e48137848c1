"use strict";var Hs=Object.create;var vt=Object.defineProperty;var Vs=Object.getOwnPropertyDescriptor;var zs=Object.getOwnPropertyNames;var $s=Object.getPrototypeOf,Ks=Object.prototype.hasOwnProperty;var w=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Ys=(e,t)=>{for(var r in t)vt(e,r,{get:t[r],enumerable:!0})},bn=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of zs(t))!Ks.call(e,i)&&i!==r&&vt(e,i,{get:()=>t[i],enumerable:!(n=Vs(t,i))||n.enumerable});return e};var Js=(e,t,r)=>(r=e!=null?Hs($s(e)):{},bn(t||!e||!e.__esModule?vt(r,"default",{value:e,enumerable:!0}):r,e)),Zs=e=>bn(vt({},"__esModule",{value:!0}),e);var lr=w((Ql,fr)=>{var _n=[],Xs=_n.forEach,Qs=_n.slice;fr.exports.extend=function(e){return Xs.call(Qs.call(arguments,1),function(t){if(t)for(var r in t)e[r]=t[r]}),e};fr.exports.parseJSON=function(e){try{return JSON.parse(e)}catch{return null}}});var kn=w((Tn,wt)=>{var ea=require("https"),ta=require("http"),cr=require("url"),En=lr(),ec=wt.exports.maxRedirects=5,Cn={https:ea,http:ta};for(bt in Cn)te=function(){},te.prototype=Cn[bt],te=new te,te.request=function(e){return function(t,r,n){n=n||{};var i=typeof t=="object"&&"maxRedirects"in t?t.maxRedirects:Tn.maxRedirects,o=En.extend({count:0,max:i,clientRequest:null,userCallback:r},n);if(o.count>o.max){var s=new Error("Max redirects exceeded. To allow more redirects, pass options.maxRedirects property.");return o.clientRequest.emit("error",s),o.clientRequest}o.count++;var a;typeof t=="string"?a=t:a=cr.format(En.extend({protocol:bt},t));var u=Object.getPrototypeOf(e).request(t,d(a,o));o.clientRequest||(o.clientRequest=u);function d(l,f){return function(c){if(c.statusCode<300||c.statusCode>399||!("location"in c.headers))return f.userCallback(c);var h=cr.resolve(l,c.headers.location),g=cr.parse(h).protocol;return g=g.substr(0,g.length-1),wt.exports[g].get(h,d(l,f),f)}}return u}}(te),te.get=function(e){return function(t,r,n){var i=e.request(t,r,n);return i.end(),i}}(te),wt.exports[bt]=te;var te,bt});var dr=w((tc,Rn)=>{Rn.exports=require("stream")});var Ln=w((rc,xn)=>{"use strict";function Pn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function On(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Pn(Object(r),!0).forEach(function(n){ra(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pn(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ra(e,t,r){return t=An(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function na(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function In(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,An(n.key),n)}}function ia(e,t,r){return t&&In(e.prototype,t),r&&In(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function An(e){var t=oa(e,"string");return typeof t=="symbol"?t:String(t)}function oa(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var sa=require("buffer"),St=sa.Buffer,aa=require("util"),hr=aa.inspect,ua=hr&&hr.custom||"inspect";function fa(e,t,r){St.prototype.copy.call(e,t,r)}xn.exports=function(){function e(){na(this,e),this.head=null,this.tail=null,this.length=0}return ia(e,[{key:"push",value:function(r){var n={data:r,next:null};this.length>0?this.tail.next=n:this.head=n,this.tail=n,++this.length}},{key:"unshift",value:function(r){var n={data:r,next:this.head};this.length===0&&(this.tail=n),this.head=n,++this.length}},{key:"shift",value:function(){if(this.length!==0){var r=this.head.data;return this.length===1?this.head=this.tail=null:this.head=this.head.next,--this.length,r}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(r){if(this.length===0)return"";for(var n=this.head,i=""+n.data;n=n.next;)i+=r+n.data;return i}},{key:"concat",value:function(r){if(this.length===0)return St.alloc(0);for(var n=St.allocUnsafe(r>>>0),i=this.head,o=0;i;)fa(i.data,n,o),o+=i.data.length,i=i.next;return n}},{key:"consume",value:function(r,n){var i;return r<this.head.data.length?(i=this.head.data.slice(0,r),this.head.data=this.head.data.slice(r)):r===this.head.data.length?i=this.shift():i=n?this._getString(r):this._getBuffer(r),i}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(r){var n=this.head,i=1,o=n.data;for(r-=o.length;n=n.next;){var s=n.data,a=r>s.length?s.length:r;if(a===s.length?o+=s:o+=s.slice(0,r),r-=a,r===0){a===s.length?(++i,n.next?this.head=n.next:this.head=this.tail=null):(this.head=n,n.data=s.slice(a));break}++i}return this.length-=i,o}},{key:"_getBuffer",value:function(r){var n=St.allocUnsafe(r),i=this.head,o=1;for(i.data.copy(n),r-=i.data.length;i=i.next;){var s=i.data,a=r>s.length?s.length:r;if(s.copy(n,n.length-r,0,a),r-=a,r===0){a===s.length?(++o,i.next?this.head=i.next:this.head=this.tail=null):(this.head=i,i.data=s.slice(a));break}++o}return this.length-=o,n}},{key:ua,value:function(r,n){return hr(this,On(On({},n),{},{depth:0,customInspect:!1}))}}]),e}()});var mr=w((nc,Dn)=>{"use strict";function la(e,t){var r=this,n=this._readableState&&this._readableState.destroyed,i=this._writableState&&this._writableState.destroyed;return n||i?(t?t(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,process.nextTick(pr,this,e)):process.nextTick(pr,this,e)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,function(o){!t&&o?r._writableState?r._writableState.errorEmitted?process.nextTick(_t,r):(r._writableState.errorEmitted=!0,process.nextTick(qn,r,o)):process.nextTick(qn,r,o):t?(process.nextTick(_t,r),t(o)):process.nextTick(_t,r)}),this)}function qn(e,t){pr(e,t),_t(e)}function _t(e){e._writableState&&!e._writableState.emitClose||e._readableState&&!e._readableState.emitClose||e.emit("close")}function ca(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function pr(e,t){e.emit("error",t)}function da(e,t){var r=e._readableState,n=e._writableState;r&&r.autoDestroy||n&&n.autoDestroy?e.destroy(t):e.emit("error",t)}Dn.exports={destroy:la,undestroy:ca,errorOrDestroy:da}});var ae=w((ic,Mn)=>{"use strict";var Fn={};function W(e,t,r){r||(r=Error);function n(o,s,a){return typeof t=="string"?t:t(o,s,a)}class i extends r{constructor(s,a,u){super(n(s,a,u))}}i.prototype.name=r.name,i.prototype.code=e,Fn[e]=i}function Nn(e,t){if(Array.isArray(e)){let r=e.length;return e=e.map(n=>String(n)),r>2?`one of ${t} ${e.slice(0,r-1).join(", ")}, or `+e[r-1]:r===2?`one of ${t} ${e[0]} or ${e[1]}`:`of ${t} ${e[0]}`}else return`of ${t} ${String(e)}`}function ha(e,t,r){return e.substr(!r||r<0?0:+r,t.length)===t}function pa(e,t,r){return(r===void 0||r>e.length)&&(r=e.length),e.substring(r-t.length,r)===t}function ma(e,t,r){return typeof r!="number"&&(r=0),r+t.length>e.length?!1:e.indexOf(t,r)!==-1}W("ERR_INVALID_OPT_VALUE",function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'},TypeError);W("ERR_INVALID_ARG_TYPE",function(e,t,r){let n;typeof t=="string"&&ha(t,"not ")?(n="must not be",t=t.replace(/^not /,"")):n="must be";let i;if(pa(e," argument"))i=`The ${e} ${n} ${Nn(t,"type")}`;else{let o=ma(e,".")?"property":"argument";i=`The "${e}" ${o} ${n} ${Nn(t,"type")}`}return i+=`. Received type ${typeof r}`,i},TypeError);W("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF");W("ERR_METHOD_NOT_IMPLEMENTED",function(e){return"The "+e+" method is not implemented"});W("ERR_STREAM_PREMATURE_CLOSE","Premature close");W("ERR_STREAM_DESTROYED",function(e){return"Cannot call "+e+" after a stream was destroyed"});W("ERR_MULTIPLE_CALLBACK","Callback called multiple times");W("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable");W("ERR_STREAM_WRITE_AFTER_END","write after end");W("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError);W("ERR_UNKNOWN_ENCODING",function(e){return"Unknown encoding: "+e},TypeError);W("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event");Mn.exports.codes=Fn});var gr=w((oc,jn)=>{"use strict";var ga=ae().codes.ERR_INVALID_OPT_VALUE;function va(e,t,r){return e.highWaterMark!=null?e.highWaterMark:t?e[r]:null}function ya(e,t,r,n){var i=va(t,n,r);if(i!=null){if(!(isFinite(i)&&Math.floor(i)===i)||i<0){var o=n?r:"highWaterMark";throw new ga(o,i)}return Math.floor(i)}return e.objectMode?16:16*1024}jn.exports={getHighWaterMark:ya}});var Bn=w((sc,vr)=>{typeof Object.create=="function"?vr.exports=function(t,r){r&&(t.super_=r,t.prototype=Object.create(r.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:vr.exports=function(t,r){if(r){t.super_=r;var n=function(){};n.prototype=r.prototype,t.prototype=new n,t.prototype.constructor=t}}});var ue=w((ac,br)=>{try{if(yr=require("util"),typeof yr.inherits!="function")throw"";br.exports=yr.inherits}catch{br.exports=Bn()}var yr});var Gn=w((uc,Un)=>{Un.exports=require("util").deprecate});var _r=w((fc,Kn)=>{"use strict";Kn.exports=I;function Hn(e){var t=this;this.next=null,this.entry=null,this.finish=function(){Va(t,e)}}var Ae;I.WritableState=Je;var ba={deprecate:Gn()},Vn=dr(),Ct=require("buffer").Buffer,wa=(typeof global<"u"?global:typeof window<"u"?window:typeof self<"u"?self:{}).Uint8Array||function(){};function Sa(e){return Ct.from(e)}function _a(e){return Ct.isBuffer(e)||e instanceof wa}var Sr=mr(),Ea=gr(),Ca=Ea.getHighWaterMark,fe=ae().codes,Ta=fe.ERR_INVALID_ARG_TYPE,ka=fe.ERR_METHOD_NOT_IMPLEMENTED,Ra=fe.ERR_MULTIPLE_CALLBACK,Pa=fe.ERR_STREAM_CANNOT_PIPE,Oa=fe.ERR_STREAM_DESTROYED,Ia=fe.ERR_STREAM_NULL_VALUES,Aa=fe.ERR_STREAM_WRITE_AFTER_END,xa=fe.ERR_UNKNOWN_ENCODING,xe=Sr.errorOrDestroy;ue()(I,Vn);function La(){}function Je(e,t,r){Ae=Ae||we(),e=e||{},typeof r!="boolean"&&(r=t instanceof Ae),this.objectMode=!!e.objectMode,r&&(this.objectMode=this.objectMode||!!e.writableObjectMode),this.highWaterMark=Ca(this,e,"writableHighWaterMark",r),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var n=e.decodeStrings===!1;this.decodeStrings=!n,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(i){Ba(t,i)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=e.emitClose!==!1,this.autoDestroy=!!e.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new Hn(this)}Je.prototype.getBuffer=function(){for(var t=this.bufferedRequest,r=[];t;)r.push(t),t=t.next;return r};(function(){try{Object.defineProperty(Je.prototype,"buffer",{get:ba.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch{}})();var Et;typeof Symbol=="function"&&Symbol.hasInstance&&typeof Function.prototype[Symbol.hasInstance]=="function"?(Et=Function.prototype[Symbol.hasInstance],Object.defineProperty(I,Symbol.hasInstance,{value:function(t){return Et.call(this,t)?!0:this!==I?!1:t&&t._writableState instanceof Je}})):Et=function(t){return t instanceof this};function I(e){Ae=Ae||we();var t=this instanceof Ae;if(!t&&!Et.call(I,this))return new I(e);this._writableState=new Je(e,this,t),this.writable=!0,e&&(typeof e.write=="function"&&(this._write=e.write),typeof e.writev=="function"&&(this._writev=e.writev),typeof e.destroy=="function"&&(this._destroy=e.destroy),typeof e.final=="function"&&(this._final=e.final)),Vn.call(this)}I.prototype.pipe=function(){xe(this,new Pa)};function qa(e,t){var r=new Aa;xe(e,r),process.nextTick(t,r)}function Da(e,t,r,n){var i;return r===null?i=new Ia:typeof r!="string"&&!t.objectMode&&(i=new Ta("chunk",["string","Buffer"],r)),i?(xe(e,i),process.nextTick(n,i),!1):!0}I.prototype.write=function(e,t,r){var n=this._writableState,i=!1,o=!n.objectMode&&_a(e);return o&&!Ct.isBuffer(e)&&(e=Sa(e)),typeof t=="function"&&(r=t,t=null),o?t="buffer":t||(t=n.defaultEncoding),typeof r!="function"&&(r=La),n.ending?qa(this,r):(o||Da(this,n,e,r))&&(n.pendingcb++,i=Fa(this,n,o,e,t,r)),i};I.prototype.cork=function(){this._writableState.corked++};I.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,!e.writing&&!e.corked&&!e.bufferProcessing&&e.bufferedRequest&&zn(this,e))};I.prototype.setDefaultEncoding=function(t){if(typeof t=="string"&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new xa(t);return this._writableState.defaultEncoding=t,this};Object.defineProperty(I.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}});function Na(e,t,r){return!e.objectMode&&e.decodeStrings!==!1&&typeof t=="string"&&(t=Ct.from(t,r)),t}Object.defineProperty(I.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}});function Fa(e,t,r,n,i,o){if(!r){var s=Na(t,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=t.objectMode?1:n.length;t.length+=a;var u=t.length<t.highWaterMark;if(u||(t.needDrain=!0),t.writing||t.corked){var d=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},d?d.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else wr(e,t,!1,a,n,i,o);return u}function wr(e,t,r,n,i,o,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new Oa("write")):r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function Ma(e,t,r,n,i){--t.pendingcb,r?(process.nextTick(i,n),process.nextTick(Ye,e,t),e._writableState.errorEmitted=!0,xe(e,n)):(i(n),e._writableState.errorEmitted=!0,xe(e,n),Ye(e,t))}function ja(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}function Ba(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if(typeof i!="function")throw new Ra;if(ja(r),t)Ma(e,r,n,t,i);else{var o=$n(r)||e.destroyed;!o&&!r.corked&&!r.bufferProcessing&&r.bufferedRequest&&zn(e,r),n?process.nextTick(Wn,e,r,o,i):Wn(e,r,o,i)}}function Wn(e,t,r,n){r||Ua(e,t),t.pendingcb--,n(),Ye(e,t)}function Ua(e,t){t.length===0&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}function zn(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),o=t.corkedRequestsFree;o.entry=r;for(var s=0,a=!0;r;)i[s]=r,r.isBuf||(a=!1),r=r.next,s+=1;i.allBuffers=a,wr(e,t,!0,t.length,i,"",o.finish),t.pendingcb++,t.lastBufferedRequest=null,o.next?(t.corkedRequestsFree=o.next,o.next=null):t.corkedRequestsFree=new Hn(t),t.bufferedRequestCount=0}else{for(;r;){var u=r.chunk,d=r.encoding,l=r.callback,f=t.objectMode?1:u.length;if(wr(e,t,!1,f,u,d,l),r=r.next,t.bufferedRequestCount--,t.writing)break}r===null&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}I.prototype._write=function(e,t,r){r(new ka("_write()"))};I.prototype._writev=null;I.prototype.end=function(e,t,r){var n=this._writableState;return typeof e=="function"?(r=e,e=null,t=null):typeof t=="function"&&(r=t,t=null),e!=null&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||Ha(this,n,r),this};Object.defineProperty(I.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}});function $n(e){return e.ending&&e.length===0&&e.bufferedRequest===null&&!e.finished&&!e.writing}function Ga(e,t){e._final(function(r){t.pendingcb--,r&&xe(e,r),t.prefinished=!0,e.emit("prefinish"),Ye(e,t)})}function Wa(e,t){!t.prefinished&&!t.finalCalled&&(typeof e._final=="function"&&!t.destroyed?(t.pendingcb++,t.finalCalled=!0,process.nextTick(Ga,e,t)):(t.prefinished=!0,e.emit("prefinish")))}function Ye(e,t){var r=$n(t);if(r&&(Wa(e,t),t.pendingcb===0&&(t.finished=!0,e.emit("finish"),t.autoDestroy))){var n=e._readableState;(!n||n.autoDestroy&&n.endEmitted)&&e.destroy()}return r}function Ha(e,t,r){t.ending=!0,Ye(e,t),r&&(t.finished?process.nextTick(r):e.once("finish",r)),t.ended=!0,e.writable=!1}function Va(e,t,r){var n=e.entry;for(e.entry=null;n;){var i=n.callback;t.pendingcb--,i(r),n=n.next}t.corkedRequestsFree.next=e}Object.defineProperty(I.prototype,"destroyed",{enumerable:!1,get:function(){return this._writableState===void 0?!1:this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}});I.prototype.destroy=Sr.destroy;I.prototype._undestroy=Sr.undestroy;I.prototype._destroy=function(e,t){t(e)}});var we=w((lc,Jn)=>{"use strict";var za=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};Jn.exports=Z;var Yn=Tr(),Cr=_r();ue()(Z,Yn);for(Er=za(Cr.prototype),Tt=0;Tt<Er.length;Tt++)kt=Er[Tt],Z.prototype[kt]||(Z.prototype[kt]=Cr.prototype[kt]);var Er,kt,Tt;function Z(e){if(!(this instanceof Z))return new Z(e);Yn.call(this,e),Cr.call(this,e),this.allowHalfOpen=!0,e&&(e.readable===!1&&(this.readable=!1),e.writable===!1&&(this.writable=!1),e.allowHalfOpen===!1&&(this.allowHalfOpen=!1,this.once("end",$a)))}Object.defineProperty(Z.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}});Object.defineProperty(Z.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}});Object.defineProperty(Z.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}});function $a(){this._writableState.ended||process.nextTick(Ka,this)}function Ka(e){e.end()}Object.defineProperty(Z.prototype,"destroyed",{enumerable:!1,get:function(){return this._readableState===void 0||this._writableState===void 0?!1:this._readableState.destroyed&&this._writableState.destroyed},set:function(t){this._readableState===void 0||this._writableState===void 0||(this._readableState.destroyed=t,this._writableState.destroyed=t)}})});var Qn=w((kr,Xn)=>{var Rt=require("buffer"),X=Rt.Buffer;function Zn(e,t){for(var r in e)t[r]=e[r]}X.from&&X.alloc&&X.allocUnsafe&&X.allocUnsafeSlow?Xn.exports=Rt:(Zn(Rt,kr),kr.Buffer=Se);function Se(e,t,r){return X(e,t,r)}Se.prototype=Object.create(X.prototype);Zn(X,Se);Se.from=function(e,t,r){if(typeof e=="number")throw new TypeError("Argument must not be a number");return X(e,t,r)};Se.alloc=function(e,t,r){if(typeof e!="number")throw new TypeError("Argument must be a number");var n=X(e);return t!==void 0?typeof r=="string"?n.fill(t,r):n.fill(t):n.fill(0),n};Se.allocUnsafe=function(e){if(typeof e!="number")throw new TypeError("Argument must be a number");return X(e)};Se.allocUnsafeSlow=function(e){if(typeof e!="number")throw new TypeError("Argument must be a number");return Rt.SlowBuffer(e)}});var Or=w(ti=>{"use strict";var Pr=Qn().Buffer,ei=Pr.isEncoding||function(e){switch(e=""+e,e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function Ya(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}function Ja(e){var t=Ya(e);if(typeof t!="string"&&(Pr.isEncoding===ei||!ei(e)))throw new Error("Unknown encoding: "+e);return t||e}ti.StringDecoder=Ze;function Ze(e){this.encoding=Ja(e);var t;switch(this.encoding){case"utf16le":this.text=ru,this.end=nu,t=4;break;case"utf8":this.fillLast=Qa,t=4;break;case"base64":this.text=iu,this.end=ou,t=3;break;default:this.write=su,this.end=au;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=Pr.allocUnsafe(t)}Ze.prototype.write=function(e){if(e.length===0)return"";var t,r;if(this.lastNeed){if(t=this.fillLast(e),t===void 0)return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""};Ze.prototype.end=tu;Ze.prototype.text=eu;Ze.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length};function Rr(e){return e<=127?0:e>>5===6?2:e>>4===14?3:e>>3===30?4:e>>6===2?-1:-2}function Za(e,t,r){var n=t.length-1;if(n<r)return 0;var i=Rr(t[n]);return i>=0?(i>0&&(e.lastNeed=i-1),i):--n<r||i===-2?0:(i=Rr(t[n]),i>=0?(i>0&&(e.lastNeed=i-2),i):--n<r||i===-2?0:(i=Rr(t[n]),i>=0?(i>0&&(i===2?i=0:e.lastNeed=i-3),i):0))}function Xa(e,t,r){if((t[0]&192)!==128)return e.lastNeed=0,"\uFFFD";if(e.lastNeed>1&&t.length>1){if((t[1]&192)!==128)return e.lastNeed=1,"\uFFFD";if(e.lastNeed>2&&t.length>2&&(t[2]&192)!==128)return e.lastNeed=2,"\uFFFD"}}function Qa(e){var t=this.lastTotal-this.lastNeed,r=Xa(this,e,t);if(r!==void 0)return r;if(this.lastNeed<=e.length)return e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,t,0,e.length),this.lastNeed-=e.length}function eu(e,t){var r=Za(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString("utf8",t,n)}function tu(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"\uFFFD":t}function ru(e,t){if((e.length-t)%2===0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function nu(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function iu(e,t){var r=(e.length-t)%3;return r===0?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,r===1?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function ou(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function su(e){return e.toString(this.encoding)}function au(e){return e&&e.length?this.write(e):""}});var Pt=w((dc,ii)=>{"use strict";var ri=ae().codes.ERR_STREAM_PREMATURE_CLOSE;function uu(e){var t=!1;return function(){if(!t){t=!0;for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];e.apply(this,n)}}}function fu(){}function lu(e){return e.setHeader&&typeof e.abort=="function"}function ni(e,t,r){if(typeof t=="function")return ni(e,null,t);t||(t={}),r=uu(r||fu);var n=t.readable||t.readable!==!1&&e.readable,i=t.writable||t.writable!==!1&&e.writable,o=function(){e.writable||a()},s=e._writableState&&e._writableState.finished,a=function(){i=!1,s=!0,n||r.call(e)},u=e._readableState&&e._readableState.endEmitted,d=function(){n=!1,u=!0,i||r.call(e)},l=function(g){r.call(e,g)},f=function(){var g;if(n&&!u)return(!e._readableState||!e._readableState.ended)&&(g=new ri),r.call(e,g);if(i&&!s)return(!e._writableState||!e._writableState.ended)&&(g=new ri),r.call(e,g)},c=function(){e.req.on("finish",a)};return lu(e)?(e.on("complete",a),e.on("abort",f),e.req?c():e.on("request",c)):i&&!e._writableState&&(e.on("end",o),e.on("close",o)),e.on("end",d),e.on("finish",a),t.error!==!1&&e.on("error",l),e.on("close",f),function(){e.removeListener("complete",a),e.removeListener("abort",f),e.removeListener("request",c),e.req&&e.req.removeListener("finish",a),e.removeListener("end",o),e.removeListener("close",o),e.removeListener("finish",a),e.removeListener("end",d),e.removeListener("error",l),e.removeListener("close",f)}}ii.exports=ni});var si=w((hc,oi)=>{"use strict";var Ot;function le(e,t,r){return t=cu(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cu(e){var t=du(e,"string");return typeof t=="symbol"?t:String(t)}function du(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var hu=Pt(),ce=Symbol("lastResolve"),_e=Symbol("lastReject"),Xe=Symbol("error"),It=Symbol("ended"),Ee=Symbol("lastPromise"),Ir=Symbol("handlePromise"),Ce=Symbol("stream");function de(e,t){return{value:e,done:t}}function pu(e){var t=e[ce];if(t!==null){var r=e[Ce].read();r!==null&&(e[Ee]=null,e[ce]=null,e[_e]=null,t(de(r,!1)))}}function mu(e){process.nextTick(pu,e)}function gu(e,t){return function(r,n){e.then(function(){if(t[It]){r(de(void 0,!0));return}t[Ir](r,n)},n)}}var vu=Object.getPrototypeOf(function(){}),yu=Object.setPrototypeOf((Ot={get stream(){return this[Ce]},next:function(){var t=this,r=this[Xe];if(r!==null)return Promise.reject(r);if(this[It])return Promise.resolve(de(void 0,!0));if(this[Ce].destroyed)return new Promise(function(s,a){process.nextTick(function(){t[Xe]?a(t[Xe]):s(de(void 0,!0))})});var n=this[Ee],i;if(n)i=new Promise(gu(n,this));else{var o=this[Ce].read();if(o!==null)return Promise.resolve(de(o,!1));i=new Promise(this[Ir])}return this[Ee]=i,i}},le(Ot,Symbol.asyncIterator,function(){return this}),le(Ot,"return",function(){var t=this;return new Promise(function(r,n){t[Ce].destroy(null,function(i){if(i){n(i);return}r(de(void 0,!0))})})}),Ot),vu),bu=function(t){var r,n=Object.create(yu,(r={},le(r,Ce,{value:t,writable:!0}),le(r,ce,{value:null,writable:!0}),le(r,_e,{value:null,writable:!0}),le(r,Xe,{value:null,writable:!0}),le(r,It,{value:t._readableState.endEmitted,writable:!0}),le(r,Ir,{value:function(o,s){var a=n[Ce].read();a?(n[Ee]=null,n[ce]=null,n[_e]=null,o(de(a,!1))):(n[ce]=o,n[_e]=s)},writable:!0}),r));return n[Ee]=null,hu(t,function(i){if(i&&i.code!=="ERR_STREAM_PREMATURE_CLOSE"){var o=n[_e];o!==null&&(n[Ee]=null,n[ce]=null,n[_e]=null,o(i)),n[Xe]=i;return}var s=n[ce];s!==null&&(n[Ee]=null,n[ce]=null,n[_e]=null,s(de(void 0,!0))),n[It]=!0}),t.on("readable",mu.bind(null,n)),n};oi.exports=bu});var li=w((pc,fi)=>{"use strict";function ai(e,t,r,n,i,o,s){try{var a=e[o](s),u=a.value}catch(d){r(d);return}a.done?t(u):Promise.resolve(u).then(n,i)}function wu(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){var o=e.apply(t,r);function s(u){ai(o,n,i,s,a,"next",u)}function a(u){ai(o,n,i,s,a,"throw",u)}s(void 0)})}}function ui(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Su(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ui(Object(r),!0).forEach(function(n){_u(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ui(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _u(e,t,r){return t=Eu(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Eu(e){var t=Cu(e,"string");return typeof t=="symbol"?t:String(t)}function Cu(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Tu=ae().codes.ERR_INVALID_ARG_TYPE;function ku(e,t,r){var n;if(t&&typeof t.next=="function")n=t;else if(t&&t[Symbol.asyncIterator])n=t[Symbol.asyncIterator]();else if(t&&t[Symbol.iterator])n=t[Symbol.iterator]();else throw new Tu("iterable",["Iterable"],t);var i=new e(Su({objectMode:!0},r)),o=!1;i._read=function(){o||(o=!0,s())};function s(){return a.apply(this,arguments)}function a(){return a=wu(function*(){try{var u=yield n.next(),d=u.value,l=u.done;l?i.push(null):i.push(yield d)?s():o=!1}catch(f){i.destroy(f)}}),a.apply(this,arguments)}return i}fi.exports=ku});var Tr=w((gc,wi)=>{"use strict";wi.exports=T;var Le;T.ReadableState=pi;var mc=require("events").EventEmitter,hi=function(t,r){return t.listeners(r).length},et=dr(),At=require("buffer").Buffer,Ru=(typeof global<"u"?global:typeof window<"u"?window:typeof self<"u"?self:{}).Uint8Array||function(){};function Pu(e){return At.from(e)}function Ou(e){return At.isBuffer(e)||e instanceof Ru}var Ar=require("util"),_;Ar&&Ar.debuglog?_=Ar.debuglog("stream"):_=function(){};var Iu=Ln(),Mr=mr(),Au=gr(),xu=Au.getHighWaterMark,xt=ae().codes,Lu=xt.ERR_INVALID_ARG_TYPE,qu=xt.ERR_STREAM_PUSH_AFTER_EOF,Du=xt.ERR_METHOD_NOT_IMPLEMENTED,Nu=xt.ERR_STREAM_UNSHIFT_AFTER_END_EVENT,qe,xr,Lr;ue()(T,et);var Qe=Mr.errorOrDestroy,qr=["error","close","destroy","pause","resume"];function Fu(e,t,r){if(typeof e.prependListener=="function")return e.prependListener(t,r);!e._events||!e._events[t]?e.on(t,r):Array.isArray(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]}function pi(e,t,r){Le=Le||we(),e=e||{},typeof r!="boolean"&&(r=t instanceof Le),this.objectMode=!!e.objectMode,r&&(this.objectMode=this.objectMode||!!e.readableObjectMode),this.highWaterMark=xu(this,e,"readableHighWaterMark",r),this.buffer=new Iu,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=e.emitClose!==!1,this.autoDestroy=!!e.autoDestroy,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(qe||(qe=Or().StringDecoder),this.decoder=new qe(e.encoding),this.encoding=e.encoding)}function T(e){if(Le=Le||we(),!(this instanceof T))return new T(e);var t=this instanceof Le;this._readableState=new pi(e,this,t),this.readable=!0,e&&(typeof e.read=="function"&&(this._read=e.read),typeof e.destroy=="function"&&(this._destroy=e.destroy)),et.call(this)}Object.defineProperty(T.prototype,"destroyed",{enumerable:!1,get:function(){return this._readableState===void 0?!1:this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}});T.prototype.destroy=Mr.destroy;T.prototype._undestroy=Mr.undestroy;T.prototype._destroy=function(e,t){t(e)};T.prototype.push=function(e,t){var r=this._readableState,n;return r.objectMode?n=!0:typeof e=="string"&&(t=t||r.defaultEncoding,t!==r.encoding&&(e=At.from(e,t),t=""),n=!0),mi(this,e,t,!1,n)};T.prototype.unshift=function(e){return mi(this,e,null,!0,!1)};function mi(e,t,r,n,i){_("readableAddChunk",t);var o=e._readableState;if(t===null)o.reading=!1,Bu(e,o);else{var s;if(i||(s=Mu(o,t)),s)Qe(e,s);else if(o.objectMode||t&&t.length>0)if(typeof t!="string"&&!o.objectMode&&Object.getPrototypeOf(t)!==At.prototype&&(t=Pu(t)),n)o.endEmitted?Qe(e,new Nu):Dr(e,o,t,!0);else if(o.ended)Qe(e,new qu);else{if(o.destroyed)return!1;o.reading=!1,o.decoder&&!r?(t=o.decoder.write(t),o.objectMode||t.length!==0?Dr(e,o,t,!1):Fr(e,o)):Dr(e,o,t,!1)}else n||(o.reading=!1,Fr(e,o))}return!o.ended&&(o.length<o.highWaterMark||o.length===0)}function Dr(e,t,r,n){t.flowing&&t.length===0&&!t.sync?(t.awaitDrain=0,e.emit("data",r)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&Lt(e)),Fr(e,t)}function Mu(e,t){var r;return!Ou(t)&&typeof t!="string"&&t!==void 0&&!e.objectMode&&(r=new Lu("chunk",["string","Buffer","Uint8Array"],t)),r}T.prototype.isPaused=function(){return this._readableState.flowing===!1};T.prototype.setEncoding=function(e){qe||(qe=Or().StringDecoder);var t=new qe(e);this._readableState.decoder=t,this._readableState.encoding=this._readableState.decoder.encoding;for(var r=this._readableState.buffer.head,n="";r!==null;)n+=t.write(r.data),r=r.next;return this._readableState.buffer.clear(),n!==""&&this._readableState.buffer.push(n),this._readableState.length=n.length,this};var ci=1073741824;function ju(e){return e>=ci?e=ci:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}function di(e,t){return e<=0||t.length===0&&t.ended?0:t.objectMode?1:e!==e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=ju(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}T.prototype.read=function(e){_("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(e!==0&&(t.emittedReadable=!1),e===0&&t.needReadable&&((t.highWaterMark!==0?t.length>=t.highWaterMark:t.length>0)||t.ended))return _("read: emitReadable",t.length,t.ended),t.length===0&&t.ended?Nr(this):Lt(this),null;if(e=di(e,t),e===0&&t.ended)return t.length===0&&Nr(this),null;var n=t.needReadable;_("need readable",n),(t.length===0||t.length-e<t.highWaterMark)&&(n=!0,_("length less than watermark",n)),t.ended||t.reading?(n=!1,_("reading or ended",n)):n&&(_("do read"),t.reading=!0,t.sync=!0,t.length===0&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=di(r,t)));var i;return e>0?i=yi(e,t):i=null,i===null?(t.needReadable=t.length<=t.highWaterMark,e=0):(t.length-=e,t.awaitDrain=0),t.length===0&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&Nr(this)),i!==null&&this.emit("data",i),i};function Bu(e,t){if(_("onEofChunk"),!t.ended){if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,t.sync?Lt(e):(t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,gi(e)))}}function Lt(e){var t=e._readableState;_("emitReadable",t.needReadable,t.emittedReadable),t.needReadable=!1,t.emittedReadable||(_("emitReadable",t.flowing),t.emittedReadable=!0,process.nextTick(gi,e))}function gi(e){var t=e._readableState;_("emitReadable_",t.destroyed,t.length,t.ended),!t.destroyed&&(t.length||t.ended)&&(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,jr(e)}function Fr(e,t){t.readingMore||(t.readingMore=!0,process.nextTick(Uu,e,t))}function Uu(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&t.length===0);){var r=t.length;if(_("maybeReadMore read 0"),e.read(0),r===t.length)break}t.readingMore=!1}T.prototype._read=function(e){Qe(this,new Du("_read()"))};T.prototype.pipe=function(e,t){var r=this,n=this._readableState;switch(n.pipesCount){case 0:n.pipes=e;break;case 1:n.pipes=[n.pipes,e];break;default:n.pipes.push(e);break}n.pipesCount+=1,_("pipe count=%d opts=%j",n.pipesCount,t);var i=(!t||t.end!==!1)&&e!==process.stdout&&e!==process.stderr,o=i?a:y;n.endEmitted?process.nextTick(o):r.once("end",o),e.on("unpipe",s);function s(S,p){_("onunpipe"),S===r&&p&&p.hasUnpiped===!1&&(p.hasUnpiped=!0,l())}function a(){_("onend"),e.end()}var u=Gu(r);e.on("drain",u);var d=!1;function l(){_("cleanup"),e.removeListener("close",h),e.removeListener("finish",g),e.removeListener("drain",u),e.removeListener("error",c),e.removeListener("unpipe",s),r.removeListener("end",a),r.removeListener("end",y),r.removeListener("data",f),d=!0,n.awaitDrain&&(!e._writableState||e._writableState.needDrain)&&u()}r.on("data",f);function f(S){_("ondata");var p=e.write(S);_("dest.write",p),p===!1&&((n.pipesCount===1&&n.pipes===e||n.pipesCount>1&&bi(n.pipes,e)!==-1)&&!d&&(_("false write response, pause",n.awaitDrain),n.awaitDrain++),r.pause())}function c(S){_("onerror",S),y(),e.removeListener("error",c),hi(e,"error")===0&&Qe(e,S)}Fu(e,"error",c);function h(){e.removeListener("finish",g),y()}e.once("close",h);function g(){_("onfinish"),e.removeListener("close",h),y()}e.once("finish",g);function y(){_("unpipe"),r.unpipe(e)}return e.emit("pipe",r),n.flowing||(_("pipe resume"),r.resume()),e};function Gu(e){return function(){var r=e._readableState;_("pipeOnDrain",r.awaitDrain),r.awaitDrain&&r.awaitDrain--,r.awaitDrain===0&&hi(e,"data")&&(r.flowing=!0,jr(e))}}T.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(t.pipesCount===0)return this;if(t.pipesCount===1)return e&&e!==t.pipes?this:(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r),this);if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}var s=bi(t.pipes,e);return s===-1?this:(t.pipes.splice(s,1),t.pipesCount-=1,t.pipesCount===1&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r),this)};T.prototype.on=function(e,t){var r=et.prototype.on.call(this,e,t),n=this._readableState;return e==="data"?(n.readableListening=this.listenerCount("readable")>0,n.flowing!==!1&&this.resume()):e==="readable"&&!n.endEmitted&&!n.readableListening&&(n.readableListening=n.needReadable=!0,n.flowing=!1,n.emittedReadable=!1,_("on readable",n.length,n.reading),n.length?Lt(this):n.reading||process.nextTick(Wu,this)),r};T.prototype.addListener=T.prototype.on;T.prototype.removeListener=function(e,t){var r=et.prototype.removeListener.call(this,e,t);return e==="readable"&&process.nextTick(vi,this),r};T.prototype.removeAllListeners=function(e){var t=et.prototype.removeAllListeners.apply(this,arguments);return(e==="readable"||e===void 0)&&process.nextTick(vi,this),t};function vi(e){var t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!t.paused?t.flowing=!0:e.listenerCount("data")>0&&e.resume()}function Wu(e){_("readable nexttick read 0"),e.read(0)}T.prototype.resume=function(){var e=this._readableState;return e.flowing||(_("resume"),e.flowing=!e.readableListening,Hu(this,e)),e.paused=!1,this};function Hu(e,t){t.resumeScheduled||(t.resumeScheduled=!0,process.nextTick(Vu,e,t))}function Vu(e,t){_("resume",t.reading),t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),jr(e),t.flowing&&!t.reading&&e.read(0)}T.prototype.pause=function(){return _("call pause flowing=%j",this._readableState.flowing),this._readableState.flowing!==!1&&(_("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this};function jr(e){var t=e._readableState;for(_("flow",t.flowing);t.flowing&&e.read()!==null;);}T.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;e.on("end",function(){if(_("wrapped end"),r.decoder&&!r.ended){var s=r.decoder.end();s&&s.length&&t.push(s)}t.push(null)}),e.on("data",function(s){if(_("wrapped data"),r.decoder&&(s=r.decoder.write(s)),!(r.objectMode&&s==null)&&!(!r.objectMode&&(!s||!s.length))){var a=t.push(s);a||(n=!0,e.pause())}});for(var i in e)this[i]===void 0&&typeof e[i]=="function"&&(this[i]=function(a){return function(){return e[a].apply(e,arguments)}}(i));for(var o=0;o<qr.length;o++)e.on(qr[o],this.emit.bind(this,qr[o]));return this._read=function(s){_("wrapped _read",s),n&&(n=!1,e.resume())},this};typeof Symbol=="function"&&(T.prototype[Symbol.asyncIterator]=function(){return xr===void 0&&(xr=si()),xr(this)});Object.defineProperty(T.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}});Object.defineProperty(T.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}});Object.defineProperty(T.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(t){this._readableState&&(this._readableState.flowing=t)}});T._fromList=yi;Object.defineProperty(T.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}});function yi(e,t){if(t.length===0)return null;var r;return t.objectMode?r=t.buffer.shift():!e||e>=t.length?(t.decoder?r=t.buffer.join(""):t.buffer.length===1?r=t.buffer.first():r=t.buffer.concat(t.length),t.buffer.clear()):r=t.buffer.consume(e,t.decoder),r}function Nr(e){var t=e._readableState;_("endReadable",t.endEmitted),t.endEmitted||(t.ended=!0,process.nextTick(zu,t,e))}function zu(e,t){if(_("endReadableNT",e.endEmitted,e.length),!e.endEmitted&&e.length===0&&(e.endEmitted=!0,t.readable=!1,t.emit("end"),e.autoDestroy)){var r=t._writableState;(!r||r.autoDestroy&&r.finished)&&t.destroy()}}typeof Symbol=="function"&&(T.from=function(e,t){return Lr===void 0&&(Lr=li()),Lr(T,e,t)});function bi(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}});var Br=w((vc,_i)=>{"use strict";_i.exports=re;var qt=ae().codes,$u=qt.ERR_METHOD_NOT_IMPLEMENTED,Ku=qt.ERR_MULTIPLE_CALLBACK,Yu=qt.ERR_TRANSFORM_ALREADY_TRANSFORMING,Ju=qt.ERR_TRANSFORM_WITH_LENGTH_0,Dt=we();ue()(re,Dt);function Zu(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(n===null)return this.emit("error",new Ku);r.writechunk=null,r.writecb=null,t!=null&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function re(e){if(!(this instanceof re))return new re(e);Dt.call(this,e),this._transformState={afterTransform:Zu.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&(typeof e.transform=="function"&&(this._transform=e.transform),typeof e.flush=="function"&&(this._flush=e.flush)),this.on("prefinish",Xu)}function Xu(){var e=this;typeof this._flush=="function"&&!this._readableState.destroyed?this._flush(function(t,r){Si(e,t,r)}):Si(this,null,null)}re.prototype.push=function(e,t){return this._transformState.needTransform=!1,Dt.prototype.push.call(this,e,t)};re.prototype._transform=function(e,t,r){r(new $u("_transform()"))};re.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}};re.prototype._read=function(e){var t=this._transformState;t.writechunk!==null&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0};re.prototype._destroy=function(e,t){Dt.prototype._destroy.call(this,e,function(r){t(r)})};function Si(e,t,r){if(t)return e.emit("error",t);if(r!=null&&e.push(r),e._writableState.length)throw new Ju;if(e._transformState.transforming)throw new Yu;return e.push(null)}});var Ti=w((yc,Ci)=>{"use strict";Ci.exports=tt;var Ei=Br();ue()(tt,Ei);function tt(e){if(!(this instanceof tt))return new tt(e);Ei.call(this,e)}tt.prototype._transform=function(e,t,r){r(null,e)}});var Ii=w((bc,Oi)=>{"use strict";var Ur;function Qu(e){var t=!1;return function(){t||(t=!0,e.apply(void 0,arguments))}}var Pi=ae().codes,ef=Pi.ERR_MISSING_ARGS,tf=Pi.ERR_STREAM_DESTROYED;function ki(e){if(e)throw e}function rf(e){return e.setHeader&&typeof e.abort=="function"}function nf(e,t,r,n){n=Qu(n);var i=!1;e.on("close",function(){i=!0}),Ur===void 0&&(Ur=Pt()),Ur(e,{readable:t,writable:r},function(s){if(s)return n(s);i=!0,n()});var o=!1;return function(s){if(!i&&!o){if(o=!0,rf(e))return e.abort();if(typeof e.destroy=="function")return e.destroy();n(s||new tf("pipe"))}}}function Ri(e){e()}function of(e,t){return e.pipe(t)}function sf(e){return!e.length||typeof e[e.length-1]!="function"?ki:e.pop()}function af(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=sf(t);if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw new ef("streams");var i,o=t.map(function(s,a){var u=a<t.length-1,d=a>0;return nf(s,u,d,function(l){i||(i=l),l&&o.forEach(Ri),!u&&(o.forEach(Ri),n(i))})});return t.reduce(of)}Oi.exports=af});var Te=w((H,nt)=>{var rt=require("stream");process.env.READABLE_STREAM==="disable"&&rt?(nt.exports=rt.Readable,Object.assign(nt.exports,rt),nt.exports.Stream=rt):(H=nt.exports=Tr(),H.Stream=rt||H,H.Readable=H,H.Writable=_r(),H.Duplex=we(),H.Transform=Br(),H.PassThrough=Ti(),H.finished=Pt(),H.pipeline=Ii())});var Li=w((wc,xi)=>{xi.exports=ne;var uf=require("util"),Ai=Te();uf.inherits(ne,Ai.Duplex);function ne(e,t,r){var n=this;if(!(n instanceof ne))return new ne(e,t,r);Ai.Duplex.call(n,r),n._output=null,n.connect(e,t)}ne.prototype.connect=function(e,t){var r=this;r.req=e,r._output=t,r.emit("response",t),t.on("data",function(n){r.push(n)||r._output.pause()}),t.on("end",function(){r.push(null)})};ne.prototype._read=function(e){this._output&&this._output.resume()};ne.prototype._write=function(e,t,r){this.req.write(e,t),r()};ne.prototype.end=function(e,t,r){return this._output.socket.destroy(),this.req.end(e,t,r)};ne.prototype.destroy=function(){this.req.destroy(),this._output.socket.destroy()}});var Di=w((Sc,qi)=>{var De=1e3,Ne=De*60,Fe=Ne*60,ke=Fe*24,ff=ke*7,lf=ke*365.25;qi.exports=function(e,t){t=t||{};var r=typeof e;if(r==="string"&&e.length>0)return cf(e);if(r==="number"&&isFinite(e))return t.long?hf(e):df(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function cf(e){if(e=String(e),!(e.length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]),n=(t[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return r*lf;case"weeks":case"week":case"w":return r*ff;case"days":case"day":case"d":return r*ke;case"hours":case"hour":case"hrs":case"hr":case"h":return r*Fe;case"minutes":case"minute":case"mins":case"min":case"m":return r*Ne;case"seconds":case"second":case"secs":case"sec":case"s":return r*De;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}function df(e){var t=Math.abs(e);return t>=ke?Math.round(e/ke)+"d":t>=Fe?Math.round(e/Fe)+"h":t>=Ne?Math.round(e/Ne)+"m":t>=De?Math.round(e/De)+"s":e+"ms"}function hf(e){var t=Math.abs(e);return t>=ke?Nt(e,t,ke,"day"):t>=Fe?Nt(e,t,Fe,"hour"):t>=Ne?Nt(e,t,Ne,"minute"):t>=De?Nt(e,t,De,"second"):e+" ms"}function Nt(e,t,r,n){var i=t>=r*1.5;return Math.round(e/r)+" "+n+(i?"s":"")}});var Gr=w((_c,Ni)=>{function pf(e){r.debug=r,r.default=r,r.coerce=u,r.disable=s,r.enable=i,r.enabled=a,r.humanize=Di(),r.destroy=d,Object.keys(e).forEach(l=>{r[l]=e[l]}),r.names=[],r.skips=[],r.formatters={};function t(l){let f=0;for(let c=0;c<l.length;c++)f=(f<<5)-f+l.charCodeAt(c),f|=0;return r.colors[Math.abs(f)%r.colors.length]}r.selectColor=t;function r(l){let f,c=null,h,g;function y(...S){if(!y.enabled)return;let p=y,b=Number(new Date),m=b-(f||b);p.diff=m,p.prev=f,p.curr=b,f=b,S[0]=r.coerce(S[0]),typeof S[0]!="string"&&S.unshift("%O");let E=0;S[0]=S[0].replace(/%([a-zA-Z%])/g,($,Oe)=>{if($==="%%")return"%";E++;let N=r.formatters[Oe];if(typeof N=="function"){let ye=S[E];$=N.call(p,ye),S.splice(E,1),E--}return $}),r.formatArgs.call(p,S),(p.log||r.log).apply(p,S)}return y.namespace=l,y.useColors=r.useColors(),y.color=r.selectColor(l),y.extend=n,y.destroy=r.destroy,Object.defineProperty(y,"enabled",{enumerable:!0,configurable:!1,get:()=>c!==null?c:(h!==r.namespaces&&(h=r.namespaces,g=r.enabled(l)),g),set:S=>{c=S}}),typeof r.init=="function"&&r.init(y),y}function n(l,f){let c=r(this.namespace+(typeof f>"u"?":":f)+l);return c.log=this.log,c}function i(l){r.save(l),r.namespaces=l,r.names=[],r.skips=[];let f=(typeof l=="string"?l:"").trim().replace(" ",",").split(",").filter(Boolean);for(let c of f)c[0]==="-"?r.skips.push(c.slice(1)):r.names.push(c)}function o(l,f){let c=0,h=0,g=-1,y=0;for(;c<l.length;)if(h<f.length&&(f[h]===l[c]||f[h]==="*"))f[h]==="*"?(g=h,y=c,h++):(c++,h++);else if(g!==-1)h=g+1,y++,c=y;else return!1;for(;h<f.length&&f[h]==="*";)h++;return h===f.length}function s(){let l=[...r.names,...r.skips.map(f=>"-"+f)].join(",");return r.enable(""),l}function a(l){for(let f of r.skips)if(o(l,f))return!1;for(let f of r.names)if(o(l,f))return!0;return!1}function u(l){return l instanceof Error?l.stack||l.message:l}function d(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return r.enable(r.load()),r}Ni.exports=pf});var Fi=w((B,Ft)=>{B.formatArgs=gf;B.save=vf;B.load=yf;B.useColors=mf;B.storage=bf();B.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();B.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function mf(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function gf(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+Ft.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;e.splice(1,0,t,"color: inherit");let r=0,n=0;e[0].replace(/%[a-zA-Z%]/g,i=>{i!=="%%"&&(r++,i==="%c"&&(n=r))}),e.splice(n,0,t)}B.log=console.debug||console.log||(()=>{});function vf(e){try{e?B.storage.setItem("debug",e):B.storage.removeItem("debug")}catch{}}function yf(){let e;try{e=B.storage.getItem("debug")}catch{}return!e&&typeof process<"u"&&"env"in process&&(e=process.env.DEBUG),e}function bf(){try{return localStorage}catch{}}Ft.exports=Gr()(B);var{formatters:wf}=Ft.exports;wf.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}});var ji=w((Ec,Mi)=>{"use strict";Mi.exports=(e,t=process.argv)=>{let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),i=t.indexOf("--");return n!==-1&&(i===-1||n<i)}});var Gi=w((Cc,Ui)=>{"use strict";var Sf=require("os"),Bi=require("tty"),V=ji(),{env:A}=process,Mt;V("no-color")||V("no-colors")||V("color=false")||V("color=never")?Mt=0:(V("color")||V("colors")||V("color=true")||V("color=always"))&&(Mt=1);function _f(){if("FORCE_COLOR"in A)return A.FORCE_COLOR==="true"?1:A.FORCE_COLOR==="false"?0:A.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(A.FORCE_COLOR,10),3)}function Ef(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function Cf(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=_f();n!==void 0&&(Mt=n);let i=r?Mt:n;if(i===0)return 0;if(r){if(V("color=16m")||V("color=full")||V("color=truecolor"))return 3;if(V("color=256"))return 2}if(e&&!t&&i===void 0)return 0;let o=i||0;if(A.TERM==="dumb")return o;if(process.platform==="win32"){let s=Sf.release().split(".");return Number(s[0])>=10&&Number(s[2])>=10586?Number(s[2])>=14931?3:2:1}if("CI"in A)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(s=>s in A)||A.CI_NAME==="codeship"?1:o;if("TEAMCITY_VERSION"in A)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(A.TEAMCITY_VERSION)?1:0;if(A.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in A){let s=Number.parseInt((A.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(A.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(A.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(A.TERM)||"COLORTERM"in A?1:o}function Wr(e,t={}){let r=Cf(e,{streamIsTTY:e&&e.isTTY,...t});return Ef(r)}Ui.exports={supportsColor:Wr,stdout:Wr({isTTY:Bi.isatty(1)}),stderr:Wr({isTTY:Bi.isatty(2)})}});var Hi=w((x,Bt)=>{var Tf=require("tty"),jt=require("util");x.init=xf;x.log=Of;x.formatArgs=Rf;x.save=If;x.load=Af;x.useColors=kf;x.destroy=jt.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");x.colors=[6,2,3,4,5,1];try{let e=Gi();e&&(e.stderr||e).level>=2&&(x.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}x.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(i,o)=>o.toUpperCase()),n=process.env[t];return/^(yes|on|true|enabled)$/i.test(n)?n=!0:/^(no|off|false|disabled)$/i.test(n)?n=!1:n==="null"?n=null:n=Number(n),e[r]=n,e},{});function kf(){return"colors"in x.inspectOpts?!!x.inspectOpts.colors:Tf.isatty(process.stderr.fd)}function Rf(e){let{namespace:t,useColors:r}=this;if(r){let n=this.color,i="\x1B[3"+(n<8?n:"8;5;"+n),o=`  ${i};1m${t} \x1B[0m`;e[0]=o+e[0].split(`
`).join(`
`+o),e.push(i+"m+"+Bt.exports.humanize(this.diff)+"\x1B[0m")}else e[0]=Pf()+t+" "+e[0]}function Pf(){return x.inspectOpts.hideDate?"":new Date().toISOString()+" "}function Of(...e){return process.stderr.write(jt.formatWithOptions(x.inspectOpts,...e)+`
`)}function If(e){e?process.env.DEBUG=e:delete process.env.DEBUG}function Af(){return process.env.DEBUG}function xf(e){e.inspectOpts={};let t=Object.keys(x.inspectOpts);for(let r=0;r<t.length;r++)e.inspectOpts[t[r]]=x.inspectOpts[t[r]]}Bt.exports=Gr()(x);var{formatters:Wi}=Bt.exports;Wi.o=function(e){return this.inspectOpts.colors=this.useColors,jt.inspect(e,this.inspectOpts).split(`
`).map(t=>t.trim()).join(" ")};Wi.O=function(e){return this.inspectOpts.colors=this.useColors,jt.inspect(e,this.inspectOpts)}});var Vi=w((Tc,Hr)=>{typeof process>"u"||process.type==="renderer"||process.browser===!0||process.__nwjs?Hr.exports=Fi():Hr.exports=Hi()});var $i=w((kc,zi)=>{var Lf=require("fs");zi.exports=function(e,t,r){t=typeof t<"u"?t:`
`,r=typeof r<"u"?r:"utf8";var n=[],i=Lf.readFileSync(e,r);if(i.indexOf("-END CERTIFICATE-")<0||i.indexOf("-BEGIN CERTIFICATE-")<0)throw Error("File does not contain 'BEGIN CERTIFICATE' or 'END CERTIFICATE'");i=i.split(t);var o=[],s,a;for(s=0,a=i.length;s<a;s++){var u=i[s];u.length!==0&&(o.push(u),u.match(/-END CERTIFICATE-/)&&(n.push(o.join(t)),o=[]))}return n}});var Ji=w((Rc,Yi)=>{var qf=require("querystring"),Df=kn(),Vr=require("fs"),Gt=require("path"),Me=require("url"),Nf=Li(),Ut=Vi()("modem"),Ki=lr(),Ff=require("util"),Me=require("url"),Mf=$i(),jf=require("os").type()==="Windows_NT",Bf=function(){var e,t={};if(!process.env.DOCKER_HOST)t.socketPath=jf?"//./pipe/docker_engine":"/var/run/docker.sock";else if(process.env.DOCKER_HOST.indexOf("unix://")===0)t.socketPath=process.env.DOCKER_HOST.substring(7)||"/var/run/docker.sock";else if(process.env.DOCKER_HOST.indexOf("npipe://")===0)t.socketPath=process.env.DOCKER_HOST.substring(8)||"//./pipe/docker_engine";else{var r=process.env.DOCKER_HOST;r.indexOf("//")<0&&(r="tcp://"+r);try{e=new Me.URL(r)}catch{throw new Error("DOCKER_HOST env variable should be something like tcp://localhost:1234")}t.port=e.port,process.env.DOCKER_TLS_VERIFY==="1"||t.port==="2376"?t.protocol="https":t.protocol="http",t.host=e.hostname,process.env.DOCKER_CERT_PATH&&(t.ca=Mf(Gt.join(process.env.DOCKER_CERT_PATH,"ca.pem")),t.cert=Vr.readFileSync(Gt.join(process.env.DOCKER_CERT_PATH,"cert.pem")),t.key=Vr.readFileSync(Gt.join(process.env.DOCKER_CERT_PATH,"key.pem"))),process.env.DOCKER_CLIENT_TIMEOUT&&(t.timeout=parseInt(process.env.DOCKER_CLIENT_TIMEOUT,10))}return t},Re=function(e){var t=Bf(),r=Object.assign({},t,e);this.host=r.host,this.host||(this.socketPath=r.socketPath),this.port=r.port,this.username=r.username,this.password=r.password,this.version=r.version,this.key=r.key,this.cert=r.cert,this.ca=r.ca,this.timeout=r.timeout,this.connectionTimeout=r.connectionTimeout,this.checkServerIdentity=r.checkServerIdentity,this.agent=r.agent,this.headers=r.headers||{},this.key&&this.cert&&this.ca&&(this.protocol="https"),this.protocol=r.protocol||this.protocol||"http"};Re.prototype.dial=function(e,t){var r,n,i,o=this;if(e.options&&(r=e.options),r&&r.authconfig&&delete r.authconfig,r&&r.abortSignal&&delete r.abortSignal,this.version&&(e.path="/"+this.version+e.path),this.host){var s=Me.parse(o.host);n=Me.format({protocol:s.protocol||o.protocol,hostname:s.hostname||o.host,port:o.port}),n=Me.resolve(n,e.path)}else n=e.path;e.path.indexOf("?")!==-1&&(r&&Object.keys(r).length>0?n+=this.buildQuerystring(r._query||r):n=n.substring(0,n.length-1));var a={path:n,method:e.method,headers:e.headers||Object.assign({},o.headers),key:o.key,cert:o.cert,ca:o.ca};if(this.checkServerIdentity&&(a.checkServerIdentity=this.checkServerIdentity),this.agent&&(a.agent=this.agent),e.authconfig&&(a.headers["X-Registry-Auth"]=e.authconfig.key||e.authconfig.base64||Buffer.from(JSON.stringify(e.authconfig)).toString("base64")),e.registryconfig&&(a.headers["X-Registry-Config"]=e.registryconfig.base64||Buffer.from(JSON.stringify(e.registryconfig)).toString("base64")),e.abortSignal&&(a.signal=e.abortSignal),e.file?(typeof e.file=="string"?i=Vr.createReadStream(Gt.resolve(e.file)):i=e.file,a.headers["Content-Type"]="application/tar"):r&&e.method==="POST"&&(i=JSON.stringify(r._body||r),e.allowEmpty||i!=="{}"&&i!=='""'?a.headers["Content-Type"]="application/json":i=void 0),typeof i=="string"?a.headers["Content-Length"]=Buffer.byteLength(i):Buffer.isBuffer(i)===!0?a.headers["Content-Length"]=i.length:(a.method==="PUT"||e.hijack||e.openStdin)&&(a.headers["Transfer-Encoding"]="chunked"),e.hijack&&(a.headers.Connection="Upgrade",a.headers.Upgrade="tcp"),this.socketPath)a.socketPath=this.socketPath;else{var u=Me.parse(n);a.hostname=u.hostname,a.port=u.port,a.path=u.path}this.buildRequest(a,e,i,t)};Re.prototype.buildRequest=function(e,t,r,n){var i=this,o,s=Df[i.protocol].request(e,function(){});Ut("Sending: %s",Ff.inspect(e,{showHidden:!0,depth:null})),i.connectionTimeout&&(o=setTimeout(function(){Ut("Connection Timeout of %s ms exceeded",i.connectionTimeout),s.abort()},i.connectionTimeout)),i.timeout&&s.on("socket",function(a){a.setTimeout(i.timeout),a.on("timeout",function(){Ut("Timeout of %s ms exceeded",i.timeout),s.abort()})}),t.hijack===!0&&(clearTimeout(o),s.on("upgrade",function(a,u,d){return n(null,u)})),s.on("connect",function(){clearTimeout(o)}),s.on("disconnect",function(){clearTimeout(o)}),s.on("response",function(a){if(clearTimeout(o),t.isStream===!0)i.buildPayload(null,t.isStream,t.statusCodes,t.openStdin,s,a,null,n);else{var u=[];a.on("data",function(d){u.push(d)}),a.on("end",function(){var d=Buffer.concat(u),l=d.toString();Ut("Received: %s",l);var f=Ki.parseJSON(l)||d;i.buildPayload(null,t.isStream,t.statusCodes,!1,s,a,f,n)})}}),s.on("error",function(a){clearTimeout(o),i.buildPayload(a,t.isStream,t.statusCodes,!1,{},{},null,n)}),typeof r=="string"||Buffer.isBuffer(r)?s.write(r):r&&(r.on("error",function(a){s.destroy(a)}),r.pipe(s)),!t.hijack&&!t.openStdin&&(typeof r=="string"||r===void 0||Buffer.isBuffer(r))&&s.end()};Re.prototype.buildPayload=function(e,t,r,n,i,o,s,a){if(e)return a(e,null);r[o.statusCode]!==!0?u(t,o,s,function(d,l){var f=new Error("(HTTP code "+o.statusCode+") "+(r[o.statusCode]||"unexpected")+" - "+(l.message||l)+" ");f.reason=r[o.statusCode],f.statusCode=o.statusCode,f.json=s,a(f,null)}):n?a(null,new Nf(i,o)):t?a(null,o):a(null,s);function u(d,l,f,c){var h="";d?(l.on("data",function(g){h+=g}),l.on("end",function(){c(null,Ki.parseJSON(h)||h)})):c(null,f)}};Re.prototype.demuxStream=function(e,t,r){var n=null,i=null,o=Buffer.from("");function s(u){if(u&&(o=Buffer.concat([o,u])),n){if(o.length>=i){var l=a(i);n===1?t.write(l):r.write(l),n=null,s()}}else if(o.length>=8){var d=a(8);n=d.readUInt8(0),i=d.readUInt32BE(4),s()}}function a(u){var d=o.slice(0,u);return o=Buffer.from(o.slice(u,o.length)),d}e.on("data",s)};Re.prototype.followProgress=function(e,t,r){var n="",i=[],o=!1;e.on("data",s),e.on("error",a),e.on("end",u),e.on("close",u);function s(d){n+=d.toString(),l();function l(){for(var c;(c=n.indexOf(`
`))>=0;){if(c==0){n=n.slice(1);continue}f(n.slice(0,c)),n=n.slice(c+1)}}function f(c){if(c[c.length-1]=="\r"&&(c=c.substr(0,c.length-1)),c.length>0){var h=JSON.parse(c);i.push(h),r&&r(h)}}}function a(d){o=!0,e.removeListener("data",s),e.removeListener("error",a),e.removeListener("end",u),e.removeListener("close",u),t(d,i)}function u(){o||t(null,i),o=!0}};Re.prototype.buildQuerystring=function(e){var t={};return Object.keys(e).map(function(r,n){e[r]&&typeof e[r]=="object"&&!Array.isArray(e[r])||r==="cachefrom"?t[r]=JSON.stringify(e[r]):t[r]=e[r]}),qf.stringify(t)};Yi.exports=Re});var no=w((Pc,ro)=>{"use strict";var z=require("fs"),Pe=require("path"),Uf=z.lchown?"lchown":"chown",Gf=z.lchownSync?"lchownSync":"chownSync",Xi=z.lchown&&!process.version.match(/v1[1-9]+\./)&&!process.version.match(/v10\.[6-9]/),Zi=(e,t,r)=>{try{return z[Gf](e,t,r)}catch(n){if(n.code!=="ENOENT")throw n}},Wf=(e,t,r)=>{try{return z.chownSync(e,t,r)}catch(n){if(n.code!=="ENOENT")throw n}},Hf=Xi?(e,t,r,n)=>i=>{!i||i.code!=="EISDIR"?n(i):z.chown(e,t,r,n)}:(e,t,r,n)=>n,zr=Xi?(e,t,r)=>{try{return Zi(e,t,r)}catch(n){if(n.code!=="EISDIR")throw n;Wf(e,t,r)}}:(e,t,r)=>Zi(e,t,r),Vf=process.version,Qi=(e,t,r)=>z.readdir(e,t,r),zf=(e,t)=>z.readdirSync(e,t);/^v4\./.test(Vf)&&(Qi=(e,t,r)=>z.readdir(e,r));var Wt=(e,t,r,n)=>{z[Uf](e,t,r,Hf(e,t,r,i=>{n(i&&i.code!=="ENOENT"?i:null)}))},eo=(e,t,r,n,i)=>{if(typeof t=="string")return z.lstat(Pe.resolve(e,t),(o,s)=>{if(o)return i(o.code!=="ENOENT"?o:null);s.name=t,eo(e,s,r,n,i)});if(t.isDirectory())$r(Pe.resolve(e,t.name),r,n,o=>{if(o)return i(o);let s=Pe.resolve(e,t.name);Wt(s,r,n,i)});else{let o=Pe.resolve(e,t.name);Wt(o,r,n,i)}},$r=(e,t,r,n)=>{Qi(e,{withFileTypes:!0},(i,o)=>{if(i){if(i.code==="ENOENT")return n();if(i.code!=="ENOTDIR"&&i.code!=="ENOTSUP")return n(i)}if(i||!o.length)return Wt(e,t,r,n);let s=o.length,a=null,u=d=>{if(!a){if(d)return n(a=d);if(--s===0)return Wt(e,t,r,n)}};o.forEach(d=>eo(e,d,t,r,u))})},$f=(e,t,r,n)=>{if(typeof t=="string")try{let i=z.lstatSync(Pe.resolve(e,t));i.name=t,t=i}catch(i){if(i.code==="ENOENT")return;throw i}t.isDirectory()&&to(Pe.resolve(e,t.name),r,n),zr(Pe.resolve(e,t.name),r,n)},to=(e,t,r)=>{let n;try{n=zf(e,{withFileTypes:!0})}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR"||i.code==="ENOTSUP")return zr(e,t,r);throw i}return n&&n.length&&n.forEach(i=>$f(e,i,t,r)),zr(e,t,r)};ro.exports=$r;$r.sync=to});var so=w((Oc,oo)=>{"use strict";var{Buffer:K}=require("buffer"),io=Symbol.for("BufferList");function O(e){if(!(this instanceof O))return new O(e);O._init.call(this,e)}O._init=function(t){Object.defineProperty(this,io,{value:!0}),this._bufs=[],this.length=0,t&&this.append(t)};O.prototype._new=function(t){return new O(t)};O.prototype._offset=function(t){if(t===0)return[0,0];let r=0;for(let n=0;n<this._bufs.length;n++){let i=r+this._bufs[n].length;if(t<i||n===this._bufs.length-1)return[n,t-r];r=i}};O.prototype._reverseOffset=function(e){let t=e[0],r=e[1];for(let n=0;n<t;n++)r+=this._bufs[n].length;return r};O.prototype.get=function(t){if(t>this.length||t<0)return;let r=this._offset(t);return this._bufs[r[0]][r[1]]};O.prototype.slice=function(t,r){return typeof t=="number"&&t<0&&(t+=this.length),typeof r=="number"&&r<0&&(r+=this.length),this.copy(null,0,t,r)};O.prototype.copy=function(t,r,n,i){if((typeof n!="number"||n<0)&&(n=0),(typeof i!="number"||i>this.length)&&(i=this.length),n>=this.length||i<=0)return t||K.alloc(0);let o=!!t,s=this._offset(n),a=i-n,u=a,d=o&&r||0,l=s[1];if(n===0&&i===this.length){if(!o)return this._bufs.length===1?this._bufs[0]:K.concat(this._bufs,this.length);for(let f=0;f<this._bufs.length;f++)this._bufs[f].copy(t,d),d+=this._bufs[f].length;return t}if(u<=this._bufs[s[0]].length-l)return o?this._bufs[s[0]].copy(t,r,l,l+u):this._bufs[s[0]].slice(l,l+u);o||(t=K.allocUnsafe(a));for(let f=s[0];f<this._bufs.length;f++){let c=this._bufs[f].length-l;if(u>c)this._bufs[f].copy(t,d,l),d+=c;else{this._bufs[f].copy(t,d,l,l+u),d+=c;break}u-=c,l&&(l=0)}return t.length>d?t.slice(0,d):t};O.prototype.shallowSlice=function(t,r){if(t=t||0,r=typeof r!="number"?this.length:r,t<0&&(t+=this.length),r<0&&(r+=this.length),t===r)return this._new();let n=this._offset(t),i=this._offset(r),o=this._bufs.slice(n[0],i[0]+1);return i[1]===0?o.pop():o[o.length-1]=o[o.length-1].slice(0,i[1]),n[1]!==0&&(o[0]=o[0].slice(n[1])),this._new(o)};O.prototype.toString=function(t,r,n){return this.slice(r,n).toString(t)};O.prototype.consume=function(t){if(t=Math.trunc(t),Number.isNaN(t)||t<=0)return this;for(;this._bufs.length;)if(t>=this._bufs[0].length)t-=this._bufs[0].length,this.length-=this._bufs[0].length,this._bufs.shift();else{this._bufs[0]=this._bufs[0].slice(t),this.length-=t;break}return this};O.prototype.duplicate=function(){let t=this._new();for(let r=0;r<this._bufs.length;r++)t.append(this._bufs[r]);return t};O.prototype.append=function(t){if(t==null)return this;if(t.buffer)this._appendBuffer(K.from(t.buffer,t.byteOffset,t.byteLength));else if(Array.isArray(t))for(let r=0;r<t.length;r++)this.append(t[r]);else if(this._isBufferList(t))for(let r=0;r<t._bufs.length;r++)this.append(t._bufs[r]);else typeof t=="number"&&(t=t.toString()),this._appendBuffer(K.from(t));return this};O.prototype._appendBuffer=function(t){this._bufs.push(t),this.length+=t.length};O.prototype.indexOf=function(e,t,r){if(r===void 0&&typeof t=="string"&&(r=t,t=void 0),typeof e=="function"||Array.isArray(e))throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.');if(typeof e=="number"?e=K.from([e]):typeof e=="string"?e=K.from(e,r):this._isBufferList(e)?e=e.slice():Array.isArray(e.buffer)?e=K.from(e.buffer,e.byteOffset,e.byteLength):K.isBuffer(e)||(e=K.from(e)),t=Number(t||0),isNaN(t)&&(t=0),t<0&&(t=this.length+t),t<0&&(t=0),e.length===0)return t>this.length?this.length:t;let n=this._offset(t),i=n[0],o=n[1];for(;i<this._bufs.length;i++){let s=this._bufs[i];for(;o<s.length;)if(s.length-o>=e.length){let u=s.indexOf(e,o);if(u!==-1)return this._reverseOffset([i,u]);o=s.length-e.length+1}else{let u=this._reverseOffset([i,o]);if(this._match(u,e))return u;o++}o=0}return-1};O.prototype._match=function(e,t){if(this.length-e<t.length)return!1;for(let r=0;r<t.length;r++)if(this.get(e+r)!==t[r])return!1;return!0};(function(){let e={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1,readIntBE:null,readIntLE:null,readUIntBE:null,readUIntLE:null};for(let t in e)(function(r){e[r]===null?O.prototype[r]=function(n,i){return this.slice(n,n+i)[r](0,i)}:O.prototype[r]=function(n=0){return this.slice(n,n+e[r])[r](0)}})(t)})();O.prototype._isBufferList=function(t){return t instanceof O||O.isBufferList(t)};O.isBufferList=function(t){return t!=null&&t[io]};oo.exports=O});var ao=w((Ic,Ht)=>{"use strict";var Kr=Te().Duplex,Kf=ue(),it=so();function D(e){if(!(this instanceof D))return new D(e);if(typeof e=="function"){this._callback=e;let t=function(n){this._callback&&(this._callback(n),this._callback=null)}.bind(this);this.on("pipe",function(n){n.on("error",t)}),this.on("unpipe",function(n){n.removeListener("error",t)}),e=null}it._init.call(this,e),Kr.call(this)}Kf(D,Kr);Object.assign(D.prototype,it.prototype);D.prototype._new=function(t){return new D(t)};D.prototype._write=function(t,r,n){this._appendBuffer(t),typeof n=="function"&&n()};D.prototype._read=function(t){if(!this.length)return this.push(null);t=Math.min(t,this.length),this.push(this.slice(0,t)),this.consume(t)};D.prototype.end=function(t){Kr.prototype.end.call(this,t),this._callback&&(this._callback(null,this.slice()),this._callback=null)};D.prototype._destroy=function(t,r){this._bufs.length=0,this.length=0,r(t)};D.prototype._isBufferList=function(t){return t instanceof D||t instanceof it||D.isBufferList(t)};D.isBufferList=it.isBufferList;Ht.exports=D;Ht.exports.BufferListStream=D;Ht.exports.BufferList=it});var Zr=w(Be=>{var Yf=Buffer.alloc,Jf="0000000000000000000",Zf="7777777777777777777",uo=48,fo=Buffer.from("ustar\0","binary"),Xf=Buffer.from("00","binary"),Qf=Buffer.from("ustar ","binary"),el=Buffer.from(" \0","binary"),tl=parseInt("7777",8),ot=257,Jr=263,rl=function(e,t,r){return typeof e!="number"?r:(e=~~e,e>=t?t:e>=0||(e+=t,e>=0)?e:0)},nl=function(e){switch(e){case 0:return"file";case 1:return"link";case 2:return"symlink";case 3:return"character-device";case 4:return"block-device";case 5:return"directory";case 6:return"fifo";case 7:return"contiguous-file";case 72:return"pax-header";case 55:return"pax-global-header";case 27:return"gnu-long-link-path";case 28:case 30:return"gnu-long-path"}return null},il=function(e){switch(e){case"file":return 0;case"link":return 1;case"symlink":return 2;case"character-device":return 3;case"block-device":return 4;case"directory":return 5;case"fifo":return 6;case"contiguous-file":return 7;case"pax-header":return 72}return 0},lo=function(e,t,r,n){for(;r<n;r++)if(e[r]===t)return r;return n},co=function(e){for(var t=256,r=0;r<148;r++)t+=e[r];for(var n=156;n<512;n++)t+=e[n];return t},he=function(e,t){return e=e.toString(8),e.length>t?Zf.slice(0,t)+" ":Jf.slice(0,t-e.length)+e+" "};function ol(e){var t;if(e[0]===128)t=!0;else if(e[0]===255)t=!1;else return null;for(var r=[],n=e.length-1;n>0;n--){var i=e[n];t?r.push(i):r.push(255-i)}var o=0,s=r.length;for(n=0;n<s;n++)o+=r[n]*Math.pow(256,n);return t?o:-1*o}var pe=function(e,t,r){if(e=e.slice(t,t+r),t=0,e[t]&128)return ol(e);for(;t<e.length&&e[t]===32;)t++;for(var n=rl(lo(e,32,t,e.length),e.length,e.length);t<n&&e[t]===0;)t++;return n===t?0:parseInt(e.slice(t,n).toString(),8)},je=function(e,t,r,n){return e.slice(t,lo(e,0,t,t+r)).toString(n)},Yr=function(e){var t=Buffer.byteLength(e),r=Math.floor(Math.log(t)/Math.log(10))+1;return t+r>=Math.pow(10,r)&&r++,t+r+e};Be.decodeLongPath=function(e,t){return je(e,0,e.length,t)};Be.encodePax=function(e){var t="";e.name&&(t+=Yr(" path="+e.name+`
`)),e.linkname&&(t+=Yr(" linkpath="+e.linkname+`
`));var r=e.pax;if(r)for(var n in r)t+=Yr(" "+n+"="+r[n]+`
`);return Buffer.from(t)};Be.decodePax=function(e){for(var t={};e.length;){for(var r=0;r<e.length&&e[r]!==32;)r++;var n=parseInt(e.slice(0,r).toString(),10);if(!n)return t;var i=e.slice(r+1,n-1).toString(),o=i.indexOf("=");if(o===-1)return t;t[i.slice(0,o)]=i.slice(o+1),e=e.slice(n)}return t};Be.encode=function(e){var t=Yf(512),r=e.name,n="";if(e.typeflag===5&&r[r.length-1]!=="/"&&(r+="/"),Buffer.byteLength(r)!==r.length)return null;for(;Buffer.byteLength(r)>100;){var i=r.indexOf("/");if(i===-1)return null;n+=n?"/"+r.slice(0,i):r.slice(0,i),r=r.slice(i+1)}return Buffer.byteLength(r)>100||Buffer.byteLength(n)>155||e.linkname&&Buffer.byteLength(e.linkname)>100?null:(t.write(r),t.write(he(e.mode&tl,6),100),t.write(he(e.uid,6),108),t.write(he(e.gid,6),116),t.write(he(e.size,11),124),t.write(he(e.mtime.getTime()/1e3|0,11),136),t[156]=uo+il(e.type),e.linkname&&t.write(e.linkname,157),fo.copy(t,ot),Xf.copy(t,Jr),e.uname&&t.write(e.uname,265),e.gname&&t.write(e.gname,297),t.write(he(e.devmajor||0,6),329),t.write(he(e.devminor||0,6),337),n&&t.write(n,345),t.write(he(co(t),6),148),t)};Be.decode=function(e,t,r){var n=e[156]===0?0:e[156]-uo,i=je(e,0,100,t),o=pe(e,100,8),s=pe(e,108,8),a=pe(e,116,8),u=pe(e,124,12),d=pe(e,136,12),l=nl(n),f=e[157]===0?null:je(e,157,100,t),c=je(e,265,32),h=je(e,297,32),g=pe(e,329,8),y=pe(e,337,8),S=co(e);if(S===8*32)return null;if(S!==pe(e,148,8))throw new Error("Invalid tar header. Maybe the tar is corrupted or it needs to be gunzipped?");if(fo.compare(e,ot,ot+6)===0)e[345]&&(i=je(e,345,155,t)+"/"+i);else if(!(Qf.compare(e,ot,ot+6)===0&&el.compare(e,Jr,Jr+2)===0)){if(!r)throw new Error("Invalid tar header: unknown format.")}return n===0&&i&&i[i.length-1]==="/"&&(n=5),{name:i,mode:o,uid:s,gid:a,size:u,mtime:new Date(1e3*d),type:l,linkname:f,uname:c,gname:h,devmajor:g,devminor:y}}});var bo=w((xc,yo)=>{var po=require("util"),sl=ao(),st=Zr(),mo=Te().Writable,go=Te().PassThrough,vo=function(){},ho=function(e){return e&=511,e&&512-e},al=function(e,t){var r=new Vt(e,t);return r.end(),r},ul=function(e,t){return t.path&&(e.name=t.path),t.linkpath&&(e.linkname=t.linkpath),t.size&&(e.size=parseInt(t.size,10)),e.pax=t,e},Vt=function(e,t){this._parent=e,this.offset=t,go.call(this,{autoDestroy:!1})};po.inherits(Vt,go);Vt.prototype.destroy=function(e){this._parent.destroy(e)};var ie=function(e){if(!(this instanceof ie))return new ie(e);mo.call(this,e),e=e||{},this._offset=0,this._buffer=sl(),this._missing=0,this._partial=!1,this._onparse=vo,this._header=null,this._stream=null,this._overflow=null,this._cb=null,this._locked=!1,this._destroyed=!1,this._pax=null,this._paxGlobal=null,this._gnuLongPath=null,this._gnuLongLinkPath=null;var t=this,r=t._buffer,n=function(){t._continue()},i=function(c){if(t._locked=!1,c)return t.destroy(c);t._stream||n()},o=function(){t._stream=null;var c=ho(t._header.size);c?t._parse(c,s):t._parse(512,f),t._locked||n()},s=function(){t._buffer.consume(ho(t._header.size)),t._parse(512,f),n()},a=function(){var c=t._header.size;t._paxGlobal=st.decodePax(r.slice(0,c)),r.consume(c),o()},u=function(){var c=t._header.size;t._pax=st.decodePax(r.slice(0,c)),t._paxGlobal&&(t._pax=Object.assign({},t._paxGlobal,t._pax)),r.consume(c),o()},d=function(){var c=t._header.size;this._gnuLongPath=st.decodeLongPath(r.slice(0,c),e.filenameEncoding),r.consume(c),o()},l=function(){var c=t._header.size;this._gnuLongLinkPath=st.decodeLongPath(r.slice(0,c),e.filenameEncoding),r.consume(c),o()},f=function(){var c=t._offset,h;try{h=t._header=st.decode(r.slice(0,512),e.filenameEncoding,e.allowUnknownFormat)}catch(g){t.emit("error",g)}if(r.consume(512),!h){t._parse(512,f),n();return}if(h.type==="gnu-long-path"){t._parse(h.size,d),n();return}if(h.type==="gnu-long-link-path"){t._parse(h.size,l),n();return}if(h.type==="pax-global-header"){t._parse(h.size,a),n();return}if(h.type==="pax-header"){t._parse(h.size,u),n();return}if(t._gnuLongPath&&(h.name=t._gnuLongPath,t._gnuLongPath=null),t._gnuLongLinkPath&&(h.linkname=t._gnuLongLinkPath,t._gnuLongLinkPath=null),t._pax&&(t._header=h=ul(h,t._pax),t._pax=null),t._locked=!0,!h.size||h.type==="directory"){t._parse(512,f),t.emit("entry",h,al(t,c),i);return}t._stream=new Vt(t,c),t.emit("entry",h,t._stream,i),t._parse(h.size,o),n()};this._onheader=f,this._parse(512,f)};po.inherits(ie,mo);ie.prototype.destroy=function(e){this._destroyed||(this._destroyed=!0,e&&this.emit("error",e),this.emit("close"),this._stream&&this._stream.emit("close"))};ie.prototype._parse=function(e,t){this._destroyed||(this._offset+=e,this._missing=e,t===this._onheader&&(this._partial=!1),this._onparse=t)};ie.prototype._continue=function(){if(!this._destroyed){var e=this._cb;this._cb=vo,this._overflow?this._write(this._overflow,void 0,e):e()}};ie.prototype._write=function(e,t,r){if(!this._destroyed){var n=this._stream,i=this._buffer,o=this._missing;if(e.length&&(this._partial=!0),e.length<o)return this._missing-=e.length,this._overflow=null,n?n.write(e,r):(i.append(e),r());this._cb=r,this._missing=0;var s=null;e.length>o&&(s=e.slice(o),e=e.slice(0,o)),n?n.end(e):i.append(e),this._overflow=s,this._onparse()}};ie.prototype._final=function(e){if(this._partial)return this.destroy(new Error("Unexpected end of data"));e()};yo.exports=ie});var So=w((Lc,wo)=>{wo.exports=require("fs").constants||require("constants")});var Co=w((qc,Eo)=>{Eo.exports=_o;function _o(e,t){if(e&&t)return _o(e)(t);if(typeof e!="function")throw new TypeError("need wrapper function");return Object.keys(e).forEach(function(n){r[n]=e[n]}),r;function r(){for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];var o=e.apply(this,n),s=n[n.length-1];return typeof o=="function"&&o!==s&&Object.keys(s).forEach(function(a){o[a]=s[a]}),o}}});var Qr=w((Dc,Xr)=>{var To=Co();Xr.exports=To(zt);Xr.exports.strict=To(ko);zt.proto=zt(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return zt(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return ko(this)},configurable:!0})});function zt(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function ko(e){var t=function(){if(t.called)throw new Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)},r=e.name||"Function wrapped with `once`";return t.onceError=r+" shouldn't be called more than once",t.called=!1,t}});var en=w((Nc,Po)=>{var fl=Qr(),ll=function(){},cl=function(e){return e.setHeader&&typeof e.abort=="function"},dl=function(e){return e.stdio&&Array.isArray(e.stdio)&&e.stdio.length===3},Ro=function(e,t,r){if(typeof t=="function")return Ro(e,null,t);t||(t={}),r=fl(r||ll);var n=e._writableState,i=e._readableState,o=t.readable||t.readable!==!1&&e.readable,s=t.writable||t.writable!==!1&&e.writable,a=!1,u=function(){e.writable||d()},d=function(){s=!1,o||r.call(e)},l=function(){o=!1,s||r.call(e)},f=function(S){r.call(e,S?new Error("exited with error code: "+S):null)},c=function(S){r.call(e,S)},h=function(){process.nextTick(g)},g=function(){if(!a){if(o&&!(i&&i.ended&&!i.destroyed))return r.call(e,new Error("premature close"));if(s&&!(n&&n.ended&&!n.destroyed))return r.call(e,new Error("premature close"))}},y=function(){e.req.on("finish",d)};return cl(e)?(e.on("complete",d),e.on("abort",h),e.req?y():e.on("request",y)):s&&!n&&(e.on("end",u),e.on("close",u)),dl(e)&&e.on("exit",f),e.on("end",l),e.on("finish",d),t.error!==!1&&e.on("error",c),e.on("close",h),function(){a=!0,e.removeListener("complete",d),e.removeListener("abort",h),e.removeListener("request",y),e.req&&e.req.removeListener("finish",d),e.removeListener("end",u),e.removeListener("close",u),e.removeListener("finish",d),e.removeListener("exit",f),e.removeListener("end",l),e.removeListener("error",c),e.removeListener("close",h)}};Po.exports=Ro});var Lo=w((Fc,xo)=>{var Ue=So(),Oo=en(),Kt=ue(),hl=Buffer.alloc,Io=Te().Readable,Ge=Te().Writable,pl=require("string_decoder").StringDecoder,$t=Zr(),ml=parseInt("755",8),gl=parseInt("644",8),Ao=hl(1024),rn=function(){},tn=function(e,t){t&=511,t&&e.push(Ao.slice(0,512-t))};function vl(e){switch(e&Ue.S_IFMT){case Ue.S_IFBLK:return"block-device";case Ue.S_IFCHR:return"character-device";case Ue.S_IFDIR:return"directory";case Ue.S_IFIFO:return"fifo";case Ue.S_IFLNK:return"symlink"}return"file"}var Yt=function(e){Ge.call(this),this.written=0,this._to=e,this._destroyed=!1};Kt(Yt,Ge);Yt.prototype._write=function(e,t,r){if(this.written+=e.length,this._to.push(e))return r();this._to._drain=r};Yt.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this.emit("close"))};var Jt=function(){Ge.call(this),this.linkname="",this._decoder=new pl("utf-8"),this._destroyed=!1};Kt(Jt,Ge);Jt.prototype._write=function(e,t,r){this.linkname+=this._decoder.write(e),r()};Jt.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this.emit("close"))};var at=function(){Ge.call(this),this._destroyed=!1};Kt(at,Ge);at.prototype._write=function(e,t,r){r(new Error("No body allowed for this entry"))};at.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this.emit("close"))};var Q=function(e){if(!(this instanceof Q))return new Q(e);Io.call(this,e),this._drain=rn,this._finalized=!1,this._finalizing=!1,this._destroyed=!1,this._stream=null};Kt(Q,Io);Q.prototype.entry=function(e,t,r){if(this._stream)throw new Error("already piping an entry");if(!(this._finalized||this._destroyed)){typeof t=="function"&&(r=t,t=null),r||(r=rn);var n=this;if((!e.size||e.type==="symlink")&&(e.size=0),e.type||(e.type=vl(e.mode)),e.mode||(e.mode=e.type==="directory"?ml:gl),e.uid||(e.uid=0),e.gid||(e.gid=0),e.mtime||(e.mtime=new Date),typeof t=="string"&&(t=Buffer.from(t)),Buffer.isBuffer(t)){e.size=t.length,this._encode(e);var i=this.push(t);return tn(n,e.size),i?process.nextTick(r):this._drain=r,new at}if(e.type==="symlink"&&!e.linkname){var o=new Jt;return Oo(o,function(a){if(a)return n.destroy(),r(a);e.linkname=o.linkname,n._encode(e),r()}),o}if(this._encode(e),e.type!=="file"&&e.type!=="contiguous-file")return process.nextTick(r),new at;var s=new Yt(this);return this._stream=s,Oo(s,function(a){if(n._stream=null,a)return n.destroy(),r(a);if(s.written!==e.size)return n.destroy(),r(new Error("size mismatch"));tn(n,e.size),n._finalizing&&n.finalize(),r()}),s}};Q.prototype.finalize=function(){if(this._stream){this._finalizing=!0;return}this._finalized||(this._finalized=!0,this.push(Ao),this.push(null))};Q.prototype.destroy=function(e){this._destroyed||(this._destroyed=!0,e&&this.emit("error",e),this.emit("close"),this._stream&&this._stream.destroy&&this._stream.destroy())};Q.prototype._encode=function(e){if(!e.pax){var t=$t.encode(e);if(t){this.push(t);return}}this._encodePax(e)};Q.prototype._encodePax=function(e){var t=$t.encodePax({name:e.name,linkname:e.linkname,pax:e.pax}),r={name:"PaxHeader",mode:e.mode,uid:e.uid,gid:e.gid,size:t.length,mtime:e.mtime,type:"pax-header",linkname:e.linkname&&"PaxHeader",uname:e.uname,gname:e.gname,devmajor:e.devmajor,devminor:e.devminor};this.push($t.encode(r)),this.push(t),tn(this,t.length),r.size=e.size,r.type=e.type,this.push($t.encode(r))};Q.prototype._read=function(e){var t=this._drain;this._drain=rn,t()};xo.exports=Q});var qo=w(nn=>{nn.extract=bo();nn.pack=Lo()});var Fo=w((jc,No)=>{var yl=Qr(),bl=en(),Zt;try{Zt=require("fs")}catch{}var ut=function(){},wl=/^v?\.0/.test(process.version),Xt=function(e){return typeof e=="function"},Sl=function(e){return!wl||!Zt?!1:(e instanceof(Zt.ReadStream||ut)||e instanceof(Zt.WriteStream||ut))&&Xt(e.close)},_l=function(e){return e.setHeader&&Xt(e.abort)},El=function(e,t,r,n){n=yl(n);var i=!1;e.on("close",function(){i=!0}),bl(e,{readable:t,writable:r},function(s){if(s)return n(s);i=!0,n()});var o=!1;return function(s){if(!i&&!o){if(o=!0,Sl(e))return e.close(ut);if(_l(e))return e.abort();if(Xt(e.destroy))return e.destroy();n(s||new Error("stream was destroyed"))}}},Do=function(e){e()},Cl=function(e,t){return e.pipe(t)},Tl=function(){var e=Array.prototype.slice.call(arguments),t=Xt(e[e.length-1]||ut)&&e.pop()||ut;if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new Error("pump requires two streams per minimum");var r,n=e.map(function(i,o){var s=o<e.length-1,a=o>0;return El(i,s,a,function(u){r||(r=u),u&&n.forEach(Do),!s&&(n.forEach(Do),t(r))})});return e.reduce(Cl)};No.exports=Tl});var Uo=w((Bc,Bo)=>{var Qt=require("path"),Mo=require("fs"),jo=parseInt("0777",8);Bo.exports=We.mkdirp=We.mkdirP=We;function We(e,t,r,n){typeof t=="function"?(r=t,t={}):(!t||typeof t!="object")&&(t={mode:t});var i=t.mode,o=t.fs||Mo;i===void 0&&(i=jo&~process.umask()),n||(n=null);var s=r||function(){};e=Qt.resolve(e),o.mkdir(e,i,function(a){if(!a)return n=n||e,s(null,n);switch(a.code){case"ENOENT":We(Qt.dirname(e),t,function(u,d){u?s(u,d):We(e,t,s,d)});break;default:o.stat(e,function(u,d){u||!d.isDirectory()?s(a,n):s(null,n)});break}})}We.sync=function e(t,r,n){(!r||typeof r!="object")&&(r={mode:r});var i=r.mode,o=r.fs||Mo;i===void 0&&(i=jo&~process.umask()),n||(n=null),t=Qt.resolve(t);try{o.mkdirSync(t,i),n=n||t}catch(a){switch(a.code){case"ENOENT":n=e(Qt.dirname(t),r,n),e(t,r,n);break;default:var s;try{s=o.statSync(t)}catch{throw a}if(!s.isDirectory())throw a;break}}return n}});var Yo=w(an=>{var kl=no(),Wo=qo(),Ho=Fo(),Rl=Uo(),Vo=require("fs"),U=require("path"),Pl=require("os"),ft=Pl.platform()==="win32",lt=function(){},sn=function(e){return e},on=ft?function(e){return e.replace(/\\/g,"/").replace(/[:?<>|]/g,"_")}:sn,Ol=function(e,t,r,n,i,o){var s=i||["."];return function(u){if(!s.length)return u();var d=s.shift(),l=U.join(r,d);t(l,function(f,c){if(f)return u(f);if(!c.isDirectory())return u(null,d,c);e.readdir(l,function(h,g){if(h)return u(h);o&&g.sort();for(var y=0;y<g.length;y++)n(U.join(r,d,g[y]))||s.push(U.join(d,g[y]));u(null,d,c)})})}},zo=function(e,t){return function(r){r.name=r.name.split("/").slice(t).join("/");var n=r.linkname;return n&&(r.type==="link"||U.isAbsolute(n))&&(r.linkname=n.split("/").slice(t).join("/")),e(r)}};an.pack=function(e,t){e||(e="."),t||(t={});var r=t.fs||Vo,n=t.ignore||t.filter||lt,i=t.map||lt,o=t.mapStream||sn,s=Ol(r,t.dereference?r.stat:r.lstat,e,n,t.entries,t.sort),a=t.strict!==!1,u=typeof t.umask=="number"?~t.umask:~$o(),d=typeof t.dmode=="number"?t.dmode:0,l=typeof t.fmode=="number"?t.fmode:0,f=t.pack||Wo.pack(),c=t.finish||lt;t.strip&&(i=zo(i,t.strip)),t.readable&&(d|=parseInt(555,8),l|=parseInt(444,8)),t.writable&&(d|=parseInt(333,8),l|=parseInt(222,8));var h=function(S,p){r.readlink(U.join(e,S),function(b,m){if(b)return f.destroy(b);p.linkname=on(m),f.entry(p,y)})},g=function(S,p,b){if(S)return f.destroy(S);if(!p)return t.finalize!==!1&&f.finalize(),c(f);if(b.isSocket())return y();var m={name:on(p),mode:(b.mode|(b.isDirectory()?d:l))&u,mtime:b.mtime,size:b.size,type:"file",uid:b.uid,gid:b.gid};if(b.isDirectory())return m.size=0,m.type="directory",m=i(m)||m,f.entry(m,y);if(b.isSymbolicLink())return m.size=0,m.type="symlink",m=i(m)||m,h(p,m);if(m=i(m)||m,!b.isFile())return a?f.destroy(new Error("unsupported type for "+p)):y();var E=f.entry(m,y);if(E){var G=o(r.createReadStream(U.join(e,p)),m);G.on("error",function($){E.destroy($)}),Ho(G,E)}},y=function(S){if(S)return f.destroy(S);s(g)};return y(),f};var Il=function(e){return e.length?e[e.length-1]:null},Al=function(){return process.getuid?process.getuid():-1},$o=function(){return process.umask?process.umask():0};an.extract=function(e,t){e||(e="."),t||(t={});var r=t.fs||Vo,n=t.ignore||t.filter||lt,i=t.map||lt,o=t.mapStream||sn,s=t.chown!==!1&&!ft&&Al()===0,a=t.extract||Wo.extract(),u=[],d=new Date,l=typeof t.umask=="number"?~t.umask:~$o(),f=typeof t.dmode=="number"?t.dmode:0,c=typeof t.fmode=="number"?t.fmode:0,h=t.strict!==!1;t.strip&&(i=zo(i,t.strip)),t.readable&&(f|=parseInt(555,8),c|=parseInt(444,8)),t.writable&&(f|=parseInt(333,8),c|=parseInt(222,8));var g=function(p,b){for(var m;(m=Il(u))&&p.slice(0,m[0].length)!==m[0];)u.pop();if(!m)return b();r.utimes(m[0],d,m[1],b)},y=function(p,b,m){if(t.utimes===!1)return m();if(b.type==="directory")return r.utimes(p,d,b.mtime,m);if(b.type==="symlink")return g(p,m);r.utimes(p,d,b.mtime,function(E){if(E)return m(E);g(p,m)})},S=function(p,b,m){var E=b.type==="symlink",G=E?r.lchmod:r.chmod,$=E?r.lchown:r.chown;if(!G)return m();var Oe=(b.mode|(b.type==="directory"?f:c))&l;G(p,Oe,function(N){if(N)return m(N);if(!s||!$)return m();$(p,b.uid,b.gid,m)})};return a.on("entry",function(p,b,m){p=i(p)||p,p.name=on(p.name);var E=U.join(e,U.join("/",p.name));if(n(E,p))return b.resume(),m();var G=function(F){if(F)return m(F);y(E,p,function(j){if(j)return m(j);if(ft)return m();S(E,p,m)})},$=function(){if(ft)return m();r.unlink(E,function(){r.symlink(p.linkname,E,G)})},Oe=function(){if(ft)return m();r.unlink(E,function(){var F=U.join(e,U.join("/",p.linkname));r.link(F,E,function(j){if(j&&j.code==="EPERM"&&t.hardlinkAsFilesFallback)return b=r.createReadStream(F),N();G(j)})})},N=function(){var F=r.createWriteStream(E),j=o(b,p);F.on("error",function(be){j.destroy(be)}),Ho(j,F,function(be){if(be)return m(be);F.on("close",G)})};if(p.type==="directory")return u.push([E,p.mtime]),Go(E,{fs:r,own:s,uid:p.uid,gid:p.gid},G);var ye=U.dirname(E);Ko(r,ye,U.join(e,"."),function(F,j){if(F)return m(F);if(!j)return m(new Error(ye+" is not a valid path"));Go(ye,{fs:r,own:s,uid:p.uid,gid:p.gid},function(be){if(be)return m(be);switch(p.type){case"file":return N();case"link":return Oe();case"symlink":return $()}if(h)return m(new Error("unsupported type for "+E+" ("+p.type+")"));b.resume(),m()})})}),t.finish&&a.on("finish",t.finish),a};function Ko(e,t,r,n){if(t===r)return n(null,!0);e.lstat(t,function(i,o){if(i&&i.code!=="ENOENT")return n(i);if(i||o.isDirectory())return Ko(e,U.join(t,".."),r,n);n(null,!1)})}function Go(e,t,r){Rl(e,{fs:t.fs},function(n,i){!n&&i&&t.own?kl(i,t.uid,t.gid,r):r(n)})}});var M=w((Gc,ct)=>{var Jo=[],xl=Jo.forEach,Ll=Jo.slice;ct.exports.extend=function(e){return xl.call(Ll.call(arguments,1),function(t){if(t)for(var r in t)e[r]=t[r]}),e};ct.exports.processArgs=function(e,t,r){return!t&&typeof e=="function"&&(t=e,e=null),{callback:t,opts:ct.exports.extend({},r,e)}};ct.exports.parseRepositoryTag=function(e){var t,r=e.indexOf("@"),n=e.lastIndexOf(":");if(r>=0)t=r;else if(n>=0)t=n;else return{repository:e};var i=e.slice(t+1);return i.indexOf("/")===-1?{repository:e.slice(0,t),tag:i}:{repository:e}}});var fn=w((Wc,Zo)=>{var un=M(),dt=function(e,t){this.modem=e,this.id=t};dt.prototype[require("util").inspect.custom]=function(){return this};dt.prototype.start=function(e,t){var r=this,n=un.processArgs(e,t),i={path:"/exec/"+this.id+"/start",method:"POST",abortSignal:n.opts.abortSignal,isStream:!0,allowEmpty:!0,hijack:n.opts.hijack,openStdin:n.opts.stdin,statusCodes:{200:!0,204:!0,404:"no such exec",409:"container stopped/paused",500:"container not running"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,s)})};dt.prototype.resize=function(e,t){var r=this,n=un.processArgs(e,t),i={path:"/exec/"+this.id+"/resize?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such exec",500:"container not running"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,s)})};dt.prototype.inspect=function(e,t){var r=this,n=un.processArgs(e,t),i={path:"/exec/"+this.id+"/json",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such exec",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return t(o,s);n.callback(o,s)})};Zo.exports=dt});var es=w((Vc,Qo)=>{var Hc=M().extend,Xo=fn(),P=M(),k=function(e,t){this.modem=e,this.id=t,this.defaultOptions={top:{},start:{},commit:{},stop:{},pause:{},unpause:{},restart:{},resize:{},attach:{},remove:{},copy:{},kill:{},exec:{},rename:{},log:{},stats:{},getArchive:{},infoArchive:{},putArchive:{},update:{},wait:{}}};k.prototype[require("util").inspect.custom]=function(){return this};k.prototype.inspect=function(e,t){var r=this,n=P.processArgs(e,t),i={path:"/containers/"+this.id+"/json?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such container",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.rename=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.rename),i={path:"/containers/"+this.id+"/rename?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.update=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.update),i={path:"/containers/"+this.id+"/update",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,400:"bad parameter",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.top=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.top),i={path:"/containers/"+this.id+"/top?",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.changes=function(e,t){var r=this,n=P.processArgs(e,t),i={path:"/containers/"+this.id+"/changes",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such container",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.listCheckpoint=function(e,t){var r=this,n=P.processArgs(e,t),i={path:"/containers/"+this.id+"/checkpoints?",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.deleteCheckpoint=function(e,t,r){var n=this,i=P.processArgs(t,r),o={path:"/containers/"+this.id+"/checkpoints/"+e+"?",method:"DELETE",abortSignal:i.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:i.opts};if(i.callback===void 0)return new this.modem.Promise(function(s,a){n.modem.dial(o,function(u,d){if(u)return a(u);s(d)})});this.modem.dial(o,function(s,a){i.callback(s,a)})};k.prototype.createCheckpoint=function(e,t){var r=this,n=P.processArgs(e,t),i={path:"/containers/"+this.id+"/checkpoints",method:"POST",abortSignal:n.opts.abortSignal,allowEmpty:!0,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.export=function(e,t){var r=this,n=P.processArgs(e,t),i={path:"/containers/"+this.id+"/export",method:"GET",abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,404:"no such container",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.start=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.start),i={path:"/containers/"+this.id+"/start?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,304:"container already started",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.pause=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.pause),i={path:"/containers/"+this.id+"/pause",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.unpause=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.unpause),i={path:"/containers/"+this.id+"/unpause",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.exec=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.exec),i={path:"/containers/"+this.id+"/exec",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"no such container",409:"container stopped/paused",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(new Xo(r.modem,u.Id))})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,new Xo(r.modem,s.Id))})};k.prototype.commit=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.commit);n.opts.container=this.id;var i={path:"/commit?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.stop=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.stop),i={path:"/containers/"+this.id+"/stop?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,304:"container already stopped",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.restart=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.restart),i={path:"/containers/"+this.id+"/restart?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.kill=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.kill),i={path:"/containers/"+this.id+"/kill?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.resize=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.resize),i={path:"/containers/"+this.id+"/resize?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.attach=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.attach),i={path:"/containers/"+this.id+"/attach?",method:"POST",abortSignal:n.opts.abortSignal,isStream:!0,hijack:n.opts.hijack,openStdin:n.opts.stdin,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.wait=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.wait),i={path:"/containers/"+this.id+"/wait?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.remove=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.remove),i={path:"/containers/"+this.id+"?",method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,400:"bad parameter",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.copy=function(e,t){var r=this;console.log("container.copy is deprecated since Docker v1.8.x");var n=P.processArgs(e,t,this.defaultOptions.copy),i={path:"/containers/"+this.id+"/copy",method:"POST",abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.getArchive=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.getArchive),i={path:"/containers/"+this.id+"/archive?",method:"GET",abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,400:"client error, bad parameters",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.infoArchive=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.infoArchive),i={path:"/containers/"+this.id+"/archive?",method:"HEAD",abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,400:"client error, bad parameters",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.putArchive=function(e,t,r){var n=this,i=P.processArgs(t,r,this.defaultOptions.putArchive),o={path:"/containers/"+this.id+"/archive?",method:"PUT",file:e,abortSignal:i.opts.abortSignal,isStream:!0,statusCodes:{200:!0,400:"client error, bad parameters",403:"client error, permission denied",404:"no such container",500:"server error"},options:i.opts};if(i.callback===void 0)return new this.modem.Promise(function(s,a){n.modem.dial(o,function(u,d){if(u)return a(u);s(d)})});this.modem.dial(o,function(s,a){i.callback(s,a)})};k.prototype.logs=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.log),i={path:"/containers/"+this.id+"/logs?",method:"GET",abortSignal:n.opts.abortSignal,isStream:n.opts.follow||!1,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};k.prototype.stats=function(e,t){var r=this,n=P.processArgs(e,t,this.defaultOptions.stats),i=!0;n.opts.stream===!1&&(i=!1);var o={path:"/containers/"+this.id+"/stats?",method:"GET",abortSignal:n.opts.abortSignal,isStream:i,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(s,a){r.modem.dial(o,function(u,d){if(u)return a(u);s(d)})});this.modem.dial(o,function(s,a){n.callback(s,a)})};Qo.exports=k});var rs=w((zc,ts)=>{var ln=M(),oe=function(e,t){this.modem=e,this.name=t};oe.prototype[require("util").inspect.custom]=function(){return this};oe.prototype.inspect=function(e){var t=this,r={path:"/images/"+this.name+"/json",method:"GET",statusCodes:{200:!0,404:"no such image",500:"server error"}};if(e===void 0)return new this.modem.Promise(function(n,i){t.modem.dial(r,function(o,s){if(o)return i(o);n(s)})});this.modem.dial(r,function(n,i){if(n)return e(n,i);e(n,i)})};oe.prototype.distribution=function(e,t){var r=ln.processArgs(e,t),n=this,i={path:"/distribution/"+this.name+"/json",method:"GET",statusCodes:{200:!0,401:"no such image",500:"server error"},authconfig:r.opts?r.opts.authconfig:void 0};if(r.callback===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return r.callback(o,s);r.callback(o,s)})};oe.prototype.history=function(e){var t=this,r={path:"/images/"+this.name+"/history",method:"GET",statusCodes:{200:!0,404:"no such image",500:"server error"}};if(e===void 0)return new this.modem.Promise(function(n,i){t.modem.dial(r,function(o,s){if(o)return i(o);n(s)})});this.modem.dial(r,function(n,i){if(n)return e(n,i);e(n,i)})};oe.prototype.get=function(e){var t=this,r={path:"/images/"+this.name+"/get",method:"GET",isStream:!0,statusCodes:{200:!0,500:"server error"}};if(e===void 0)return new this.modem.Promise(function(n,i){t.modem.dial(r,function(o,s){if(o)return i(o);n(s)})});this.modem.dial(r,function(n,i){if(n)return e(n,i);e(n,i)})};oe.prototype.push=function(e,t,r){var n=this,i=ln.processArgs(e,t),o=!0;i.opts.stream===!1&&(o=!1);var s={path:"/images/"+this.name+"/push?",method:"POST",options:i.opts,authconfig:i.opts.authconfig||r,abortSignal:i.opts.abortSignal,isStream:o,statusCodes:{200:!0,404:"no such image",500:"server error"}};if(delete s.options.authconfig,t===void 0)return new this.modem.Promise(function(a,u){n.modem.dial(s,function(d,l){if(d)return u(d);a(l)})});this.modem.dial(s,function(a,u){t(a,u)})};oe.prototype.tag=function(e,t){var r=this,n={path:"/images/"+this.name+"/tag?",method:"POST",options:e,abortSignal:e&&e.abortSignal,statusCodes:{200:!0,201:!0,400:"bad parameter",404:"no such image",409:"conflict",500:"server error"}};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};oe.prototype.remove=function(e,t){var r=this,n=ln.processArgs(e,t),i={path:"/images/"+this.name+"?",method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such image",409:"conflict",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ts.exports=oe});var os=w(($c,is)=>{var ns=M(),er=function(e,t){this.modem=e,this.name=t};er.prototype[require("util").inspect.custom]=function(){return this};er.prototype.inspect=function(e,t){var r=this,n=ns.processArgs(e,t),i={path:"/volumes/"+this.name,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such volume",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};er.prototype.remove=function(e,t){var r=this,n=ns.processArgs(e,t),i={path:"/volumes/"+this.name,method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{204:!0,404:"no such volume",409:"conflict",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};is.exports=er});var as=w((Kc,ss)=>{var tr=M(),He=function(e,t){this.modem=e,this.id=t};He.prototype[require("util").inspect.custom]=function(){return this};He.prototype.inspect=function(i,t){var r=this,n=tr.processArgs(i,t),i={path:"/networks/"+this.id+"?",method:"GET",statusCodes:{200:!0,404:"no such network",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};He.prototype.remove=function(e,t){var r=this,n=tr.processArgs(e,t),i={path:"/networks/"+this.id,method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such network",409:"conflict",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};He.prototype.connect=function(e,t){var r=this,n=tr.processArgs(e,t),i={path:"/networks/"+this.id+"/connect",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"network or container is not found",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};He.prototype.disconnect=function(e,t){var r=this,n=tr.processArgs(e,t),i={path:"/networks/"+this.id+"/disconnect",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"network or container is not found",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ss.exports=He});var fs=w((Yc,us)=>{var cn=M(),Ve=function(e,t){this.modem=e,this.id=t};Ve.prototype[require("util").inspect.custom]=function(){return this};Ve.prototype.inspect=function(e,t){var r=this,n=cn.processArgs(e,t),i={path:"/services/"+this.id,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such service",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Ve.prototype.remove=function(e,t){var r=this,n=cn.processArgs(e,t),i={path:"/services/"+this.id,method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such service",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Ve.prototype.update=function(e,t,r){var n=this;if(!r){var i=typeof t;i==="function"?(r=t,t=e,e=t.authconfig||void 0):i==="undefined"&&(t=e,e=t.authconfig||void 0)}var o={path:"/services/"+this.id+"/update?",method:"POST",abortSignal:t&&t.abortSignal,statusCodes:{200:!0,404:"no such service",500:"server error"},authconfig:e,options:t};if(r===void 0)return new this.modem.Promise(function(s,a){n.modem.dial(o,function(u,d){if(u)return a(u);s(d)})});this.modem.dial(o,function(s,a){r(s,a)})};Ve.prototype.logs=function(e,t){var r=this,n=cn.processArgs(e,t,{}),i={path:"/services/"+this.id+"/logs?",method:"GET",abortSignal:n.opts.abortSignal,isStream:n.opts.follow||!1,statusCodes:{200:!0,404:"no such service",500:"server error",503:"node is not part of a swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};us.exports=Ve});var cs=w((Jc,ls)=>{var me=M(),Y=function(e,t,r){this.modem=e,this.name=t,this.remote=r||t};Y.prototype[require("util").inspect.custom]=function(){return this};Y.prototype.inspect=function(e,t){var r=this,n=me.processArgs(e,t),i={path:"/plugins/"+this.name,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"plugin is not installed",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Y.prototype.remove=function(e,t){var r=this,n=me.processArgs(e,t),i={path:"/plugins/"+this.name+"?",method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"plugin is not installed",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,s)})};Y.prototype.privileges=function(e,t){var r=this,n=me.processArgs(e,t),i={path:"/plugins/privileges?",method:"GET",options:{remote:this.remote},abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Y.prototype.pull=function(e,t){var r=this,n=me.processArgs(e,t);n.opts._query&&!n.opts._query.name&&(n.opts._query.name=this.name),n.opts._query&&!n.opts._query.remote&&(n.opts._query.remote=this.remote);var i={path:"/plugins/pull?",method:"POST",abortSignal:n.opts.abortSignal,isStream:!0,options:n.opts,statusCodes:{200:!0,204:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Y.prototype.enable=function(e,t){var r=this,n=me.processArgs(e,t),i={path:"/plugins/"+this.name+"/enable?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Y.prototype.disable=function(e,t){var r=this,n=me.processArgs(e,t),i={path:"/plugins/"+this.name+"/disable",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Y.prototype.push=function(e,t){var r=this,n=me.processArgs(e,t),i={path:"/plugins/"+this.name+"/push",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"plugin not installed",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Y.prototype.configure=function(e,t){var r=this,n=me.processArgs(e,t),i={path:"/plugins/"+this.name+"/set",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"plugin not installed",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Y.prototype.upgrade=function(e,t,r){var n=this;!r&&typeof t=="function"&&(r=t,t=e,e=t.authconfig||void 0);var i={path:"/plugins/"+this.name+"/upgrade?",method:"POST",abortSignal:t&&t.abortSignal,statusCodes:{200:!0,204:!0,404:"plugin not installed",500:"server error"},authconfig:e,options:t};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){r(o,s)})};ls.exports=Y});var ps=w((Zc,hs)=>{var ds=M(),ht=function(e,t){this.modem=e,this.id=t};ht.prototype[require("util").inspect.custom]=function(){return this};ht.prototype.inspect=function(e,t){var r=this,n=ds.processArgs(e,t),i={path:"/secrets/"+this.id,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"secret not found",406:"node is not part of a swarm",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ht.prototype.update=function(e,t){var r=this;!t&&typeof e=="function"&&(t=e);var n={path:"/secrets/"+this.id+"/update?",method:"POST",abortSignal:e&&e.abortSignal,statusCodes:{200:!0,404:"secret not found",500:"server error"},options:e};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};ht.prototype.remove=function(e,t){var r=this,n=ds.processArgs(e,t),i={path:"/secrets/"+this.id,method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"secret not found",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};hs.exports=ht});var gs=w((Xc,ms)=>{var dn=M(),pt=function(e,t){this.modem=e,this.id=t};pt.prototype[require("util").inspect.custom]=function(){return this};pt.prototype.inspect=function(e,t){var r=this,n=dn.processArgs(e,t),i={path:"/configs/"+this.id,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"config not found",500:"server error",503:"node is not part of a swarm"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};pt.prototype.update=function(e,t){var r=this,n=dn.processArgs(e,t),i={path:"/configs/"+this.id+"/update?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"config not found",500:"server error",503:"node is not part of a swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};pt.prototype.remove=function(e,t){var r=this,n=dn.processArgs(e,t),i={path:"/configs/"+this.id,method:"DELETE",abortSignal:e.abortSignal,statusCodes:{200:!0,204:!0,404:"config not found",500:"server error",503:"node is not part of a swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ms.exports=pt});var bs=w((Qc,ys)=>{var vs=M(),rr=function(e,t){this.modem=e,this.id=t,this.defaultOptions={log:{}}};rr.prototype[require("util").inspect.custom]=function(){return this};rr.prototype.inspect=function(e,t){var r=this,n=vs.processArgs(e,t),i={path:"/tasks/"+this.id,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"unknown task",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};rr.prototype.logs=function(e,t){var r=this,n=vs.processArgs(e,t,this.defaultOptions.log),i={path:"/tasks/"+this.id+"/logs?",method:"GET",abortSignal:n.opts.abortSignal,isStream:n.opts.follow||!1,statusCodes:{101:!0,200:!0,404:"no such container",500:"server error",503:"node is not part of a swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ys.exports=rr});var _s=w((ed,Ss)=>{var ws=M(),mt=function(e,t){this.modem=e,this.id=t};mt.prototype[require("util").inspect.custom]=function(){return this};mt.prototype.inspect=function(e,t){var r=this,n=ws.processArgs(e,t),i={path:"/nodes/"+this.id,method:"GET",abortSignal:n.abortSignal,statusCodes:{200:!0,404:"no such node",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};mt.prototype.update=function(e,t){var r=this;!t&&typeof e=="function"&&(t=e);var n={path:"/nodes/"+this.id+"/update?",method:"POST",abortSignal:e&&e.abortSignal,statusCodes:{200:!0,404:"no such node",406:"node is not part of a swarm",500:"server error"},options:e};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};mt.prototype.remove=function(e,t){var r=this,n=ws.processArgs(e,t),i={path:"/nodes/"+this.id+"?",method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such node",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Ss.exports=mt});var Ds=w((td,qs)=>{var ql=require("events").EventEmitter,Dl=Ji(),Nl=Yo(),Fl=require("zlib"),Es=es(),Cs=rs(),Ts=os(),ks=as(),Rs=fs(),Ps=cs(),Os=ps(),Ml=gs(),Is=bs(),As=_s(),xs=fn(),C=M(),Ls=C.extend,v=function(e){if(!(this instanceof v))return new v(e);var t=global.Promise;e&&e.Promise&&(t=e.Promise,Object.keys(e).length===1&&(e=void 0)),this.modem=new Dl(e),this.modem.Promise=t};v.prototype.createContainer=function(e,t){var r=this,n={path:"/containers/create?",method:"POST",options:e,authconfig:e.authconfig,abortSignal:e.abortSignal,statusCodes:{200:!0,201:!0,404:"no such container",406:"impossible to attach",500:"server error"}};if(delete e.authconfig,t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(r.getContainer(a.Id))})});this.modem.dial(n,function(i,o){if(i)return t(i,o);t(i,r.getContainer(o.Id))})};v.prototype.createImage=function(e,t,r){var n=this;!r&&typeof t=="function"?(r=t,t=e,e=t.authconfig||void 0):!r&&!t&&(t=e,e=t.authconfig);var i={path:"/images/create?",method:"POST",options:t,authconfig:e,abortSignal:t.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){r(o,s)})};v.prototype.loadImage=function(e,t,r){var n=this;!r&&typeof t=="function"&&(r=t,t=null);var i={path:"/images/load?",method:"POST",options:t,file:e,abortSignal:t&&t.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){r(o,s)})};v.prototype.importImage=function(e,t,r){var n=this;!r&&typeof t=="function"&&(r=t,t=void 0),t||(t={}),t.fromSrc="-";var i={path:"/images/create?",method:"POST",options:t,file:e,abortSignal:t.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){r(o,s)})};v.prototype.checkAuth=function(e,t){var r=this,n={path:"/auth",method:"POST",options:e,abortSignal:e.abortSignal,statusCodes:{200:!0,204:!0,500:"server error"}};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};v.prototype.buildImage=function(e,t,r){var n=this;!r&&typeof t=="function"&&(r=t,t=null);function i(s){var a={path:"/build?",method:"POST",file:s,options:t,abortSignal:t&&t.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(t&&(t.registryconfig&&(a.registryconfig=a.options.registryconfig,delete a.options.registryconfig),t.authconfig&&(a.authconfig=a.options.authconfig,delete a.options.authconfig)),r===void 0)return new n.modem.Promise(function(u,d){n.modem.dial(a,function(l,f){if(l)return d(l);u(f)})});n.modem.dial(a,function(u,d){r(u,d)})}if(e&&e.context){var o=Nl.pack(e.context,{entries:e.src});return i(o.pipe(Fl.createGzip()))}else return i(e)};v.prototype.getContainer=function(e){return new Es(this.modem,e)};v.prototype.getImage=function(e){return new Cs(this.modem,e)};v.prototype.getVolume=function(e){return new Ts(this.modem,e)};v.prototype.getPlugin=function(e,t){return new Ps(this.modem,e,t)};v.prototype.getService=function(e){return new Rs(this.modem,e)};v.prototype.getTask=function(e){return new Is(this.modem,e)};v.prototype.getNode=function(e){return new As(this.modem,e)};v.prototype.getNetwork=function(e){return new ks(this.modem,e)};v.prototype.getSecret=function(e){return new Os(this.modem,e)};v.prototype.getConfig=function(e){return new Ml(this.modem,e)};v.prototype.getExec=function(e){return new xs(this.modem,e)};v.prototype.listContainers=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/containers/json?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.listImages=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/images/json?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.getImages=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/images/get?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.listServices=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/services?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.listNodes=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/nodes?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",404:"no such node",500:"server error",503:"node is not part of a swarm"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.listTasks=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/tasks?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.createSecret=function(e,t){var r=C.processArgs(e,t),n=this,i={path:"/secrets/create?",method:"POST",options:r.opts,abortSignal:r.opts.abortSignal,statusCodes:{200:!0,201:!0,406:"server error or node is not part of a swarm",409:"name conflicts with an existing object",500:"server error"}};if(r.callback===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(n.getSecret(u.ID))})});this.modem.dial(i,function(o,s){if(o)return r.callback(o,s);r.callback(o,n.getSecret(s.ID))})};v.prototype.createConfig=function(e,t){var r=C.processArgs(e,t),n=this,i={path:"/configs/create?",method:"POST",options:r.opts,abortSignal:r.opts.abortSignal,statusCodes:{200:!0,201:!0,406:"server error or node is not part of a swarm",409:"name conflicts with an existing object",500:"server error"}};if(r.callback===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(n.getConfig(u.ID))})});this.modem.dial(i,function(o,s){if(o)return r.callback(o,s);r.callback(o,n.getConfig(s.ID))})};v.prototype.listSecrets=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/secrets?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.listConfigs=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/configs?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.createPlugin=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/plugins/create?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(r.getPlugin(n.opts.name))})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,r.getPlugin(n.opts.name))})};v.prototype.listPlugins=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/plugins?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.pruneImages=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/images/prune?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.pruneBuilder=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/build/prune",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.pruneContainers=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/containers/prune?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.pruneVolumes=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/volumes/prune?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.pruneNetworks=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/networks/prune?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.createVolume=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/volumes/create?",method:"POST",allowEmpty:!0,options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(r.getVolume(u.Name))})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,r.getVolume(s.Name))})};v.prototype.createService=function(e,t,r){!r&&typeof t=="function"?(r=t,t=e,e=t.authconfig||void 0):!t&&!r&&(t=e);var n=this,i={path:"/services/create",method:"POST",options:t,authconfig:e,abortSignal:t&&t.abortSignal,statusCodes:{200:!0,201:!0,500:"server error"}};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(n.getService(u.ID||u.Id))})});this.modem.dial(i,function(o,s){if(o)return r(o,s);r(o,n.getService(s.ID||s.Id))})};v.prototype.listVolumes=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/volumes?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.createNetwork=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/networks/create?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"driver not found",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(r.getNetwork(u.Id))})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,r.getNetwork(s.Id))})};v.prototype.listNetworks=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/networks?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.searchImages=function(e,t){var r=this,n={path:"/images/search?",method:"GET",options:e,authconfig:e.authconfig,abortSignal:e.abortSignal,statusCodes:{200:!0,500:"server error"}};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};v.prototype.info=function(i,t){var r=this,n=C.processArgs(i,t),i={path:"/info",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.version=function(i,t){var r=this,n=C.processArgs(i,t),i={path:"/version",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.ping=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/_ping",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.df=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/system/df",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.getEvents=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/events?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.pull=function(e,t,r,n){var i=C.processArgs(t,r),o=C.parseRepositoryTag(e);i.opts.fromImage=o.repository,i.opts.tag=o.tag||"latest";var s=[i.opts,i.callback];return n&&(s=[n,i.opts,i.callback]),this.createImage.apply(this,s)};v.prototype.run=function(e,t,r,n,i,o){return typeof arguments[arguments.length-1]=="function"?this.runCallback(e,t,r,n,i,o):this.runPromise(e,t,r,n,i)};v.prototype.runCallback=function(e,t,r,n,i,o){!o&&typeof n=="function"?(o=n,n={},i={}):!o&&typeof i=="function"&&(o=i,i={});var s=new ql;function a(d,l){if(d)return o(d,null,l);s.emit("container",l),l.attach({stream:!0,stdout:!0,stderr:!0},function(c,h){if(c)return o(c,null,l);s.emit("stream",h),r&&(r instanceof Array?(h.on("end",function(){try{r[0].end()}catch{}try{r[1].end()}catch{}}),l.modem.demuxStream(h,r[0],r[1])):(h.setEncoding("utf8"),h.pipe(r,{end:!0}))),l.start(i,function(g,y){if(g)return o(g,y,l);s.emit("start",l),l.wait(function(S,p){s.emit("data",p),o(S,p,l)})})})}var u={Hostname:"",User:"",AttachStdin:!1,AttachStdout:!0,AttachStderr:!0,Tty:!0,OpenStdin:!1,StdinOnce:!1,Env:null,Cmd:t,Image:e,Volumes:{},VolumesFrom:[]};return Ls(u,n),this.createContainer(u,a),s};v.prototype.runPromise=function(e,t,r,n,i){var o=this;n=n||{},i=i||{};var s={Hostname:"",User:"",AttachStdin:!1,AttachStdout:!0,AttachStderr:!0,Tty:!0,OpenStdin:!1,StdinOnce:!1,Env:null,Cmd:t,Image:e,Volumes:{},VolumesFrom:[]};Ls(s,n);var a;return new this.modem.Promise(function(u,d){o.createContainer(s).then(function(l){return a=l,l.attach({stream:!0,stdout:!0,stderr:!0})}).then(function(l){return r&&(r instanceof Array?(l.on("end",function(){try{r[0].end()}catch{}try{r[1].end()}catch{}}),a.modem.demuxStream(l,r[0],r[1])):(l.setEncoding("utf8"),l.pipe(r,{end:!0}))),a.start(i)}).then(function(l){return a.wait()}).then(function(l){u([l,a])}).catch(function(l){d(l)})})};v.prototype.swarmInit=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/swarm/init",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",406:"node is already part of a Swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.swarmJoin=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/swarm/join",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",406:"node is already part of a Swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.swarmLeave=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/swarm/leave?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,406:"node is not part of a Swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.swarmUpdate=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/swarm/update?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",406:"node is already part of a Swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.prototype.swarmInspect=function(e,t){var r=this,n=C.processArgs(e,t),i={path:"/swarm",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,406:"This node is not a swarm manager",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};v.Container=Es;v.Image=Cs;v.Volume=Ts;v.Network=ks;v.Service=Rs;v.Plugin=Ps;v.Secret=Os;v.Task=Is;v.Node=As;v.Exec=xs;qs.exports=v});var Hl={};Ys(Hl,{default:()=>Ws});module.exports=Zs(Hl);var L=require("@raycast/api");var R=require("react");var yt=e=>e.reduce((t,r)=>{let n=r.Labels["com.docker.compose.project"],i=r.Labels["com.docker.compose.config_files"],o=r.Labels["com.docker.compose.working_dir"];if(n===void 0)return t;let s=t.find(({name:a})=>a===n);return s!==void 0?(s.containers=[...s.containers,r],t):[...t,{name:n,configFiles:i,workingDir:o,containers:[r]}]},[]);var wn=e=>e.map(([t,r])=>`**${t}:** ${r}`).join(`

`),sr=e=>"```\n"+e+"\n```",ar=e=>"`"+e+"`";var ur=e=>e.State==="running";var Ke=1e3,Ie=e=>{let t=({Id:f})=>e.getContainer(f).stop(),r=({Id:f})=>e.getContainer(f).start(),n=({Id:f})=>e.getContainer(f).restart(),i=({Id:f})=>e.getContainer(f).remove();return{useImages:()=>{let[f,c]=(0,R.useState)(),[h,g]=(0,R.useState)(!1),[y,S]=(0,R.useState)(),p=(0,R.useRef)();return(0,R.useEffect)(()=>{async function b(){try{let m=await e.listImages();c(m)}catch(m){m instanceof Error&&S(m)}}return q(g,b)(),p.current=setInterval(b,Ke),()=>p.current&&clearInterval(p.current)},[]),{images:f,error:y,isLoading:h,removeImage:q(g,({Id:b})=>e.getImage(b).remove())}},useImageInfo:({Id:f})=>{let[c,h]=(0,R.useState)(),[g,y]=(0,R.useState)(!1),S=(0,R.useRef)();return(0,R.useEffect)(()=>{async function p(){let b=await e.getImage(f).inspect();h(b)}return q(y,p)(),S.current=setInterval(p,Ke),()=>S.current&&clearInterval(S.current)},[f]),{imageInfo:c,isLoading:g}},useContainers:()=>{let[f,c]=(0,R.useState)(),[h,g]=(0,R.useState)(!1),[y,S]=(0,R.useState)(),p=(0,R.useRef)();return(0,R.useEffect)(()=>{async function b(){try{let m=await e.listContainers({all:!0});c(m)}catch(m){m instanceof Error&&S(m)}}return q(g,b)(),p.current=setInterval(b,Ke),()=>p.current&&clearInterval(p.current)},[]),{containers:f,isLoading:h,error:y,startContainer:q(g,r),stopContainer:q(g,t),restartContainer:q(g,n),removeContainer:q(g,i)}},useContainerInfo:f=>{let[c,h]=(0,R.useState)(),[g,y]=(0,R.useState)(!1),S=(0,R.useRef)();return(0,R.useEffect)(()=>{async function p(){let b=await e.getContainer(f).inspect();h(b)}return q(y,p)(),S.current=setInterval(p,Ke),()=>S.current&&clearInterval(S.current)},[f]),{containerInfo:c,isLoading:g,startContainer:q(y,r),restartContainer:q(y,n),stopContainer:q(y,t),removeContainer:q(y,i)}},useProjects:()=>{let[f,c]=(0,R.useState)(),[h,g]=(0,R.useState)(!1),[y,S]=(0,R.useState)(),p=(0,R.useRef)();return(0,R.useEffect)(()=>{async function b(){try{let m=await e.listContainers({all:!0});c(yt(m))}catch(m){m instanceof Error&&S(m)}}return q(g,b)(),p.current=setInterval(b,Ke),()=>p.current&&clearInterval(p.current)},[]),{projects:f,isLoading:h,error:y,startProject:q(g,async b=>{await Promise.all(b.containers.filter(E=>!ur(E)).map(E=>e.getContainer(E.Id).start()));let m=await e.listContainers({all:!0});c(yt(m))}),stopProject:q(g,async b=>{await Promise.all(b.containers.filter(E=>ur(E)).map(E=>e.getContainer(E.Id).stop()));let m=await e.listContainers({all:!0});c(yt(m))})}},useCreateContainer:()=>{let[f,c]=(0,R.useState)(!1),[h,g]=(0,R.useState)();return{createContainer:async S=>{c(!0);try{await(await e.createContainer(S)).start()}catch(p){p instanceof Error&&g(p)}finally{c(!1)}},isLoading:f,error:h}}}};function q(e,t){return async r=>{e(!0);try{let n=await t(r);return e(!1),n}finally{e(!1)}}}var Ns=require("@raycast/api"),hn=Js(Ds()),Fs=require("react"),Ms=require("node:url"),ze=()=>{let{socketPath:e}=(0,Ns.getPreferenceValues)(),t=["http://","https://","tcp://"];return(0,Fs.useMemo)(()=>{if(t.some(r=>e?.startsWith(r))){let r=new Ms.URL(e);return new hn.default({host:r.hostname,port:r.port||2375})}return new hn.default(e?{socketPath:e}:void 0)},[e])};var gt=e=>(e.RepoTags??e.RepoDigests??e.Id).join(" "),jl=(e,t)=>{let r=e.replace("sha256:","");return t!==void 0?r.slice(0,t):r},js=e=>e!==void 0?`## ${gt(e)}`+wn([["ID",jl(e.Id,12)],["Size",pn(e.Size)],["OS",e.Os],["Architecture",e.Architecture],["Command",ar(Ul(e.Config.Cmd??[]))],["Entrypoint",ar(Gl(e.Config.Entrypoint))]])+Bl(e.Config.Env??[])+`
`:"",Bl=e=>e.length===0?"":[`

## Environment`,sr(e.join(`
`))].join(`
`),Ul=e=>e.join(" "),Gl=e=>e==null?"-":Array.isArray(e)?e.join(" "):e,pn=(e,t=2)=>{if(e===0)return"0 Bytes";let r=1024,n=t<0?0:t,i=["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"],o=Math.floor(Math.log(e)/Math.log(r));return parseFloat((e/Math.pow(r,o)).toFixed(n))+" "+i[o]};var ge=require("@raycast/api"),ir=require("react");var mn=e=>e.errno===-61&&e.code==="ECONNREFUSED";var nr=require("react/jsx-runtime");function gn({error:e}){let[t,r]=(0,ir.useState)([]);(0,ir.useEffect)(()=>{async function i(){let o=await(0,ge.getApplications)();r(o)}i()},[]);let n=t.find(({bundleId:i})=>i==="com.docker.docker");return(0,nr.jsx)(ge.Detail,{markdown:Wl(e),actions:mn(e)&&n!==void 0?(0,nr.jsx)(ge.ActionPanel,{children:(0,nr.jsx)(ge.Action.Open,{title:"Launch Docker",target:n.path})}):null})}var Wl=e=>{let t="Error message:\n\n```\n"+e.message+"\n```";return mn(e)?["## \u26A0\uFE0F Error connecting to Docker",t].join(`
`):`## An Error Occurred:

${t}`};var Bs=require("@raycast/api");var Us=require("react/jsx-runtime");function vn({imageId:e}){let t=ze(),{useImageInfo:r}=Ie(t),{imageInfo:n,isLoading:i}=r({Id:e});return(0,Us.jsx)(Bs.Detail,{navigationTitle:"Image Details",isLoading:i,markdown:js(n)})}var $e=require("@raycast/api"),or=({action:e,onSuccess:t,onFailure:r})=>async()=>{try{await e(),t!==void 0&&await(0,$e.showToast)($e.Toast.Style.Success,...Gs(t()))}catch(n){n instanceof Error&&r!==void 0&&await(0,$e.showToast)($e.Toast.Style.Failure,...Gs(r(n)))}},Gs=e=>Array.isArray(e)?e:[e];var ve=require("react"),ee=require("@raycast/api");var se=require("react/jsx-runtime");function yn({imageId:e}){let t=ze(),{useImageInfo:r,useCreateContainer:n}=Ie(t),{imageInfo:i,isLoading:o}=r({Id:e}),{createContainer:s}=n(),[a,u]=(0,ve.useState)({name:"",port:"",volume:"",env:""}),d=(0,ve.useCallback)((h,g)=>{u(y=>({...y,[h]:g}))},[]),l=(0,ve.useMemo)(()=>Object.keys(i?.Config?.ExposedPorts||{}),[i]),f=(0,ve.useCallback)(h=>{let{name:g,port:y,volume:S,env:p}=h,b=N=>N.split(","),m=N=>l.slice(0,N.length).reduce((ye,F,j)=>({...ye,[F]:[{HostPort:N[j]}]}),{}),E={Image:i?.RepoTags[0],name:g,HostConfig:{PortBindings:m(y?b(y):l),Binds:S?b(S):void 0},Env:p?b(p):void 0};or({action:()=>s(E),onSuccess:()=>["Container Creation","The container was created successfully!"],onFailure:N=>["Container Creation Failed",N.message]})(),u({name:"",port:"",volume:"",env:""})},[s,i?.RepoTags]),c=(0,ve.useMemo)(()=>(0,se.jsx)(ee.Form.TextField,{title:"Port",id:"port",placeholder:l.join(","),info:"default ports are used if no ports are provided",value:a.port,onChange:h=>d("port",h)}),[i,d,a.port]);return(0,se.jsxs)(ee.Form,{isLoading:o,navigationTitle:`${i?.RepoTags[0]}`,actions:(0,se.jsx)(ee.ActionPanel,{children:(0,se.jsx)(ee.Action.SubmitForm,{onSubmit:f})}),children:[c,(0,se.jsx)(ee.Form.TextField,{title:"Name",id:"name",placeholder:"Container name",info:"If no name is provided, a random name will be generated",value:a.name,autoFocus:!0,onChange:h=>d("name",h)}),(0,se.jsx)(ee.Form.TextField,{title:"Volume",id:"volume",placeholder:"/data:/data",info:'multiple volumes can be separated by ","',value:a.volume,onChange:h=>d("volume",h)}),(0,se.jsx)(ee.Form.TextField,{title:"Env",id:"env",placeholder:"key=value",info:'multiple env variables can be separated by ","',value:a.env,onChange:h=>d("env",h)})]})}var J=require("react/jsx-runtime");function Ws(){let e=ze(),{useImages:t}=Ie(e),{images:r,isLoading:n,error:i,removeImage:o}=t();return i?(0,J.jsx)(gn,{error:i}):(0,J.jsx)(L.List,{isLoading:n,children:r?.map(s=>(0,J.jsx)(L.List.Item,{title:gt(s),icon:{source:"icon-image.png",tintColor:L.Color.SecondaryText},accessories:[{text:{value:pn(s.Size)??""}}],actions:(0,J.jsxs)(L.ActionPanel,{title:gt(s),children:[(0,J.jsx)(L.Action.Push,{title:"Inspect",icon:{source:L.Icon.Binoculars},shortcut:{modifiers:["cmd"],key:"i"},target:(0,J.jsx)(vn,{imageId:s.Id})}),(0,J.jsx)(L.Action,{title:"Remove Image",icon:L.Icon.Trash,style:L.Action.Style.Destructive,shortcut:L.Keyboard.Shortcut.Common.Remove,onAction:or({action:()=>o(s),onSuccess:()=>`Image ${gt(s)} removed`,onFailure:({message:a})=>a})}),(0,J.jsx)(L.Action.Push,{target:(0,J.jsx)(yn,{imageId:s.Id}),title:"Create Container",icon:{source:L.Icon.Plus},shortcut:{modifiers:["cmd","shift"],key:"c"}})]})},s.Id))})}
/*! Bundled license information:

safe-buffer/index.js:
  (*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> *)
*/
