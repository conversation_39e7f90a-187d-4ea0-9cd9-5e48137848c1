"use strict";var Bs=Object.create;var wt=Object.defineProperty;var Us=Object.getOwnPropertyDescriptor;var Gs=Object.getOwnPropertyNames;var Ws=Object.getPrototypeOf,Hs=Object.prototype.hasOwnProperty;var y=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Vs=(e,t)=>{for(var r in t)wt(e,r,{get:t[r],enumerable:!0})},yn=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Gs(t))!Hs.call(e,i)&&i!==r&&wt(e,i,{get:()=>t[i],enumerable:!(n=Us(t,i))||n.enumerable});return e};var zs=(e,t,r)=>(r=e!=null?Bs(Ws(e)):{},yn(t||!e||!e.__esModule?wt(r,"default",{value:e,enumerable:!0}):r,e)),$s=e=>yn(wt({},"__esModule",{value:!0}),e);var hr=y((nc,dr)=>{var _n=[],Js=_n.forEach,Zs=_n.slice;dr.exports.extend=function(e){return Js.call(Zs.call(arguments,1),function(t){if(t)for(var r in t)e[r]=t[r]}),e};dr.exports.parseJSON=function(e){try{return JSON.parse(e)}catch{return null}}});var kn=y((Tn,Et)=>{var Xs=require("https"),Qs=require("http"),pr=require("url"),Cn=hr(),ic=Et.exports.maxRedirects=5,En={https:Xs,http:Qs};for(Ct in En)ae=function(){},ae.prototype=En[Ct],ae=new ae,ae.request=function(e){return function(t,r,n){n=n||{};var i=typeof t=="object"&&"maxRedirects"in t?t.maxRedirects:Tn.maxRedirects,o=Cn.extend({count:0,max:i,clientRequest:null,userCallback:r},n);if(o.count>o.max){var s=new Error("Max redirects exceeded. To allow more redirects, pass options.maxRedirects property.");return o.clientRequest.emit("error",s),o.clientRequest}o.count++;var a;typeof t=="string"?a=t:a=pr.format(Cn.extend({protocol:Ct},t));var u=Object.getPrototypeOf(e).request(t,c(a,o));o.clientRequest||(o.clientRequest=u);function c(l,f){return function(d){if(d.statusCode<300||d.statusCode>399||!("location"in d.headers))return f.userCallback(d);var h=pr.resolve(l,d.headers.location),v=pr.parse(h).protocol;return v=v.substr(0,v.length-1),Et.exports[v].get(h,c(l,f),f)}}return u}}(ae),ae.get=function(e){return function(t,r,n){var i=e.request(t,r,n);return i.end(),i}}(ae),Et.exports[Ct]=ae;var ae,Ct});var mr=y((oc,Pn)=>{Pn.exports=require("stream")});var Ln=y((sc,In)=>{"use strict";function Rn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function On(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rn(Object(r),!0).forEach(function(n){ea(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rn(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ea(e,t,r){return t=xn(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ta(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function An(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,xn(n.key),n)}}function ra(e,t,r){return t&&An(e.prototype,t),r&&An(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function xn(e){var t=na(e,"string");return typeof t=="symbol"?t:String(t)}function na(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ia=require("buffer"),Tt=ia.Buffer,oa=require("util"),gr=oa.inspect,sa=gr&&gr.custom||"inspect";function aa(e,t,r){Tt.prototype.copy.call(e,t,r)}In.exports=function(){function e(){ta(this,e),this.head=null,this.tail=null,this.length=0}return ra(e,[{key:"push",value:function(r){var n={data:r,next:null};this.length>0?this.tail.next=n:this.head=n,this.tail=n,++this.length}},{key:"unshift",value:function(r){var n={data:r,next:this.head};this.length===0&&(this.tail=n),this.head=n,++this.length}},{key:"shift",value:function(){if(this.length!==0){var r=this.head.data;return this.length===1?this.head=this.tail=null:this.head=this.head.next,--this.length,r}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(r){if(this.length===0)return"";for(var n=this.head,i=""+n.data;n=n.next;)i+=r+n.data;return i}},{key:"concat",value:function(r){if(this.length===0)return Tt.alloc(0);for(var n=Tt.allocUnsafe(r>>>0),i=this.head,o=0;i;)aa(i.data,n,o),o+=i.data.length,i=i.next;return n}},{key:"consume",value:function(r,n){var i;return r<this.head.data.length?(i=this.head.data.slice(0,r),this.head.data=this.head.data.slice(r)):r===this.head.data.length?i=this.shift():i=n?this._getString(r):this._getBuffer(r),i}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(r){var n=this.head,i=1,o=n.data;for(r-=o.length;n=n.next;){var s=n.data,a=r>s.length?s.length:r;if(a===s.length?o+=s:o+=s.slice(0,r),r-=a,r===0){a===s.length?(++i,n.next?this.head=n.next:this.head=this.tail=null):(this.head=n,n.data=s.slice(a));break}++i}return this.length-=i,o}},{key:"_getBuffer",value:function(r){var n=Tt.allocUnsafe(r),i=this.head,o=1;for(i.data.copy(n),r-=i.data.length;i=i.next;){var s=i.data,a=r>s.length?s.length:r;if(s.copy(n,n.length-r,0,a),r-=a,r===0){a===s.length?(++o,i.next?this.head=i.next:this.head=this.tail=null):(this.head=i,i.data=s.slice(a));break}++o}return this.length-=o,n}},{key:sa,value:function(r,n){return gr(this,On(On({},n),{},{depth:0,customInspect:!1}))}}]),e}()});var yr=y((ac,Dn)=>{"use strict";function ua(e,t){var r=this,n=this._readableState&&this._readableState.destroyed,i=this._writableState&&this._writableState.destroyed;return n||i?(t?t(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,process.nextTick(vr,this,e)):process.nextTick(vr,this,e)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,function(o){!t&&o?r._writableState?r._writableState.errorEmitted?process.nextTick(kt,r):(r._writableState.errorEmitted=!0,process.nextTick(qn,r,o)):process.nextTick(qn,r,o):t?(process.nextTick(kt,r),t(o)):process.nextTick(kt,r)}),this)}function qn(e,t){vr(e,t),kt(e)}function kt(e){e._writableState&&!e._writableState.emitClose||e._readableState&&!e._readableState.emitClose||e.emit("close")}function fa(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function vr(e,t){e.emit("error",t)}function la(e,t){var r=e._readableState,n=e._writableState;r&&r.autoDestroy||n&&n.autoDestroy?e.destroy(t):e.emit("error",t)}Dn.exports={destroy:ua,undestroy:fa,errorOrDestroy:la}});var he=y((uc,Fn)=>{"use strict";var Mn={};function $(e,t,r){r||(r=Error);function n(o,s,a){return typeof t=="string"?t:t(o,s,a)}class i extends r{constructor(s,a,u){super(n(s,a,u))}}i.prototype.name=r.name,i.prototype.code=e,Mn[e]=i}function Nn(e,t){if(Array.isArray(e)){let r=e.length;return e=e.map(n=>String(n)),r>2?`one of ${t} ${e.slice(0,r-1).join(", ")}, or `+e[r-1]:r===2?`one of ${t} ${e[0]} or ${e[1]}`:`of ${t} ${e[0]}`}else return`of ${t} ${String(e)}`}function ca(e,t,r){return e.substr(!r||r<0?0:+r,t.length)===t}function da(e,t,r){return(r===void 0||r>e.length)&&(r=e.length),e.substring(r-t.length,r)===t}function ha(e,t,r){return typeof r!="number"&&(r=0),r+t.length>e.length?!1:e.indexOf(t,r)!==-1}$("ERR_INVALID_OPT_VALUE",function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'},TypeError);$("ERR_INVALID_ARG_TYPE",function(e,t,r){let n;typeof t=="string"&&ca(t,"not ")?(n="must not be",t=t.replace(/^not /,"")):n="must be";let i;if(da(e," argument"))i=`The ${e} ${n} ${Nn(t,"type")}`;else{let o=ha(e,".")?"property":"argument";i=`The "${e}" ${o} ${n} ${Nn(t,"type")}`}return i+=`. Received type ${typeof r}`,i},TypeError);$("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF");$("ERR_METHOD_NOT_IMPLEMENTED",function(e){return"The "+e+" method is not implemented"});$("ERR_STREAM_PREMATURE_CLOSE","Premature close");$("ERR_STREAM_DESTROYED",function(e){return"Cannot call "+e+" after a stream was destroyed"});$("ERR_MULTIPLE_CALLBACK","Callback called multiple times");$("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable");$("ERR_STREAM_WRITE_AFTER_END","write after end");$("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError);$("ERR_UNKNOWN_ENCODING",function(e){return"Unknown encoding: "+e},TypeError);$("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event");Fn.exports.codes=Mn});var br=y((fc,jn)=>{"use strict";var pa=he().codes.ERR_INVALID_OPT_VALUE;function ma(e,t,r){return e.highWaterMark!=null?e.highWaterMark:t?e[r]:null}function ga(e,t,r,n){var i=ma(t,n,r);if(i!=null){if(!(isFinite(i)&&Math.floor(i)===i)||i<0){var o=n?r:"highWaterMark";throw new pa(o,i)}return Math.floor(i)}return e.objectMode?16:16*1024}jn.exports={getHighWaterMark:ga}});var Bn=y((lc,wr)=>{typeof Object.create=="function"?wr.exports=function(t,r){r&&(t.super_=r,t.prototype=Object.create(r.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:wr.exports=function(t,r){if(r){t.super_=r;var n=function(){};n.prototype=r.prototype,t.prototype=new n,t.prototype.constructor=t}}});var pe=y((cc,_r)=>{try{if(Sr=require("util"),typeof Sr.inherits!="function")throw"";_r.exports=Sr.inherits}catch{_r.exports=Bn()}var Sr});var Gn=y((dc,Un)=>{Un.exports=require("util").deprecate});var Tr=y((hc,Kn)=>{"use strict";Kn.exports=I;function Hn(e){var t=this;this.next=null,this.entry=null,this.finish=function(){Wa(t,e)}}var qe;I.WritableState=Qe;var va={deprecate:Gn()},Vn=mr(),Rt=require("buffer").Buffer,ya=(typeof global<"u"?global:typeof window<"u"?window:typeof self<"u"?self:{}).Uint8Array||function(){};function ba(e){return Rt.from(e)}function wa(e){return Rt.isBuffer(e)||e instanceof ya}var Er=yr(),Sa=br(),_a=Sa.getHighWaterMark,me=he().codes,Ca=me.ERR_INVALID_ARG_TYPE,Ea=me.ERR_METHOD_NOT_IMPLEMENTED,Ta=me.ERR_MULTIPLE_CALLBACK,ka=me.ERR_STREAM_CANNOT_PIPE,Pa=me.ERR_STREAM_DESTROYED,Ra=me.ERR_STREAM_NULL_VALUES,Oa=me.ERR_STREAM_WRITE_AFTER_END,Aa=me.ERR_UNKNOWN_ENCODING,De=Er.errorOrDestroy;pe()(I,Vn);function xa(){}function Qe(e,t,r){qe=qe||Ee(),e=e||{},typeof r!="boolean"&&(r=t instanceof qe),this.objectMode=!!e.objectMode,r&&(this.objectMode=this.objectMode||!!e.writableObjectMode),this.highWaterMark=_a(this,e,"writableHighWaterMark",r),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var n=e.decodeStrings===!1;this.decodeStrings=!n,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(i){Fa(t,i)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=e.emitClose!==!1,this.autoDestroy=!!e.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new Hn(this)}Qe.prototype.getBuffer=function(){for(var t=this.bufferedRequest,r=[];t;)r.push(t),t=t.next;return r};(function(){try{Object.defineProperty(Qe.prototype,"buffer",{get:va.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch{}})();var Pt;typeof Symbol=="function"&&Symbol.hasInstance&&typeof Function.prototype[Symbol.hasInstance]=="function"?(Pt=Function.prototype[Symbol.hasInstance],Object.defineProperty(I,Symbol.hasInstance,{value:function(t){return Pt.call(this,t)?!0:this!==I?!1:t&&t._writableState instanceof Qe}})):Pt=function(t){return t instanceof this};function I(e){qe=qe||Ee();var t=this instanceof qe;if(!t&&!Pt.call(I,this))return new I(e);this._writableState=new Qe(e,this,t),this.writable=!0,e&&(typeof e.write=="function"&&(this._write=e.write),typeof e.writev=="function"&&(this._writev=e.writev),typeof e.destroy=="function"&&(this._destroy=e.destroy),typeof e.final=="function"&&(this._final=e.final)),Vn.call(this)}I.prototype.pipe=function(){De(this,new ka)};function Ia(e,t){var r=new Oa;De(e,r),process.nextTick(t,r)}function La(e,t,r,n){var i;return r===null?i=new Ra:typeof r!="string"&&!t.objectMode&&(i=new Ca("chunk",["string","Buffer"],r)),i?(De(e,i),process.nextTick(n,i),!1):!0}I.prototype.write=function(e,t,r){var n=this._writableState,i=!1,o=!n.objectMode&&wa(e);return o&&!Rt.isBuffer(e)&&(e=ba(e)),typeof t=="function"&&(r=t,t=null),o?t="buffer":t||(t=n.defaultEncoding),typeof r!="function"&&(r=xa),n.ending?Ia(this,r):(o||La(this,n,e,r))&&(n.pendingcb++,i=Da(this,n,o,e,t,r)),i};I.prototype.cork=function(){this._writableState.corked++};I.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,!e.writing&&!e.corked&&!e.bufferProcessing&&e.bufferedRequest&&zn(this,e))};I.prototype.setDefaultEncoding=function(t){if(typeof t=="string"&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new Aa(t);return this._writableState.defaultEncoding=t,this};Object.defineProperty(I.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}});function qa(e,t,r){return!e.objectMode&&e.decodeStrings!==!1&&typeof t=="string"&&(t=Rt.from(t,r)),t}Object.defineProperty(I.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}});function Da(e,t,r,n,i,o){if(!r){var s=qa(t,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=t.objectMode?1:n.length;t.length+=a;var u=t.length<t.highWaterMark;if(u||(t.needDrain=!0),t.writing||t.corked){var c=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},c?c.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else Cr(e,t,!1,a,n,i,o);return u}function Cr(e,t,r,n,i,o,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new Pa("write")):r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function Na(e,t,r,n,i){--t.pendingcb,r?(process.nextTick(i,n),process.nextTick(Xe,e,t),e._writableState.errorEmitted=!0,De(e,n)):(i(n),e._writableState.errorEmitted=!0,De(e,n),Xe(e,t))}function Ma(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}function Fa(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if(typeof i!="function")throw new Ta;if(Ma(r),t)Na(e,r,n,t,i);else{var o=$n(r)||e.destroyed;!o&&!r.corked&&!r.bufferProcessing&&r.bufferedRequest&&zn(e,r),n?process.nextTick(Wn,e,r,o,i):Wn(e,r,o,i)}}function Wn(e,t,r,n){r||ja(e,t),t.pendingcb--,n(),Xe(e,t)}function ja(e,t){t.length===0&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}function zn(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),o=t.corkedRequestsFree;o.entry=r;for(var s=0,a=!0;r;)i[s]=r,r.isBuf||(a=!1),r=r.next,s+=1;i.allBuffers=a,Cr(e,t,!0,t.length,i,"",o.finish),t.pendingcb++,t.lastBufferedRequest=null,o.next?(t.corkedRequestsFree=o.next,o.next=null):t.corkedRequestsFree=new Hn(t),t.bufferedRequestCount=0}else{for(;r;){var u=r.chunk,c=r.encoding,l=r.callback,f=t.objectMode?1:u.length;if(Cr(e,t,!1,f,u,c,l),r=r.next,t.bufferedRequestCount--,t.writing)break}r===null&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}I.prototype._write=function(e,t,r){r(new Ea("_write()"))};I.prototype._writev=null;I.prototype.end=function(e,t,r){var n=this._writableState;return typeof e=="function"?(r=e,e=null,t=null):typeof t=="function"&&(r=t,t=null),e!=null&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||Ga(this,n,r),this};Object.defineProperty(I.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}});function $n(e){return e.ending&&e.length===0&&e.bufferedRequest===null&&!e.finished&&!e.writing}function Ba(e,t){e._final(function(r){t.pendingcb--,r&&De(e,r),t.prefinished=!0,e.emit("prefinish"),Xe(e,t)})}function Ua(e,t){!t.prefinished&&!t.finalCalled&&(typeof e._final=="function"&&!t.destroyed?(t.pendingcb++,t.finalCalled=!0,process.nextTick(Ba,e,t)):(t.prefinished=!0,e.emit("prefinish")))}function Xe(e,t){var r=$n(t);if(r&&(Ua(e,t),t.pendingcb===0&&(t.finished=!0,e.emit("finish"),t.autoDestroy))){var n=e._readableState;(!n||n.autoDestroy&&n.endEmitted)&&e.destroy()}return r}function Ga(e,t,r){t.ending=!0,Xe(e,t),r&&(t.finished?process.nextTick(r):e.once("finish",r)),t.ended=!0,e.writable=!1}function Wa(e,t,r){var n=e.entry;for(e.entry=null;n;){var i=n.callback;t.pendingcb--,i(r),n=n.next}t.corkedRequestsFree.next=e}Object.defineProperty(I.prototype,"destroyed",{enumerable:!1,get:function(){return this._writableState===void 0?!1:this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}});I.prototype.destroy=Er.destroy;I.prototype._undestroy=Er.undestroy;I.prototype._destroy=function(e,t){t(e)}});var Ee=y((pc,Jn)=>{"use strict";var Ha=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};Jn.exports=ne;var Yn=Rr(),Pr=Tr();pe()(ne,Yn);for(kr=Ha(Pr.prototype),Ot=0;Ot<kr.length;Ot++)At=kr[Ot],ne.prototype[At]||(ne.prototype[At]=Pr.prototype[At]);var kr,At,Ot;function ne(e){if(!(this instanceof ne))return new ne(e);Yn.call(this,e),Pr.call(this,e),this.allowHalfOpen=!0,e&&(e.readable===!1&&(this.readable=!1),e.writable===!1&&(this.writable=!1),e.allowHalfOpen===!1&&(this.allowHalfOpen=!1,this.once("end",Va)))}Object.defineProperty(ne.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}});Object.defineProperty(ne.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}});Object.defineProperty(ne.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}});function Va(){this._writableState.ended||process.nextTick(za,this)}function za(e){e.end()}Object.defineProperty(ne.prototype,"destroyed",{enumerable:!1,get:function(){return this._readableState===void 0||this._writableState===void 0?!1:this._readableState.destroyed&&this._writableState.destroyed},set:function(t){this._readableState===void 0||this._writableState===void 0||(this._readableState.destroyed=t,this._writableState.destroyed=t)}})});var Qn=y((Or,Xn)=>{var xt=require("buffer"),ie=xt.Buffer;function Zn(e,t){for(var r in e)t[r]=e[r]}ie.from&&ie.alloc&&ie.allocUnsafe&&ie.allocUnsafeSlow?Xn.exports=xt:(Zn(xt,Or),Or.Buffer=Te);function Te(e,t,r){return ie(e,t,r)}Te.prototype=Object.create(ie.prototype);Zn(ie,Te);Te.from=function(e,t,r){if(typeof e=="number")throw new TypeError("Argument must not be a number");return ie(e,t,r)};Te.alloc=function(e,t,r){if(typeof e!="number")throw new TypeError("Argument must be a number");var n=ie(e);return t!==void 0?typeof r=="string"?n.fill(t,r):n.fill(t):n.fill(0),n};Te.allocUnsafe=function(e){if(typeof e!="number")throw new TypeError("Argument must be a number");return ie(e)};Te.allocUnsafeSlow=function(e){if(typeof e!="number")throw new TypeError("Argument must be a number");return xt.SlowBuffer(e)}});var Ir=y(ti=>{"use strict";var xr=Qn().Buffer,ei=xr.isEncoding||function(e){switch(e=""+e,e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function $a(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}function Ka(e){var t=$a(e);if(typeof t!="string"&&(xr.isEncoding===ei||!ei(e)))throw new Error("Unknown encoding: "+e);return t||e}ti.StringDecoder=et;function et(e){this.encoding=Ka(e);var t;switch(this.encoding){case"utf16le":this.text=eu,this.end=tu,t=4;break;case"utf8":this.fillLast=Za,t=4;break;case"base64":this.text=ru,this.end=nu,t=3;break;default:this.write=iu,this.end=ou;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=xr.allocUnsafe(t)}et.prototype.write=function(e){if(e.length===0)return"";var t,r;if(this.lastNeed){if(t=this.fillLast(e),t===void 0)return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""};et.prototype.end=Qa;et.prototype.text=Xa;et.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length};function Ar(e){return e<=127?0:e>>5===6?2:e>>4===14?3:e>>3===30?4:e>>6===2?-1:-2}function Ya(e,t,r){var n=t.length-1;if(n<r)return 0;var i=Ar(t[n]);return i>=0?(i>0&&(e.lastNeed=i-1),i):--n<r||i===-2?0:(i=Ar(t[n]),i>=0?(i>0&&(e.lastNeed=i-2),i):--n<r||i===-2?0:(i=Ar(t[n]),i>=0?(i>0&&(i===2?i=0:e.lastNeed=i-3),i):0))}function Ja(e,t,r){if((t[0]&192)!==128)return e.lastNeed=0,"\uFFFD";if(e.lastNeed>1&&t.length>1){if((t[1]&192)!==128)return e.lastNeed=1,"\uFFFD";if(e.lastNeed>2&&t.length>2&&(t[2]&192)!==128)return e.lastNeed=2,"\uFFFD"}}function Za(e){var t=this.lastTotal-this.lastNeed,r=Ja(this,e,t);if(r!==void 0)return r;if(this.lastNeed<=e.length)return e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,t,0,e.length),this.lastNeed-=e.length}function Xa(e,t){var r=Ya(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString("utf8",t,n)}function Qa(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"\uFFFD":t}function eu(e,t){if((e.length-t)%2===0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function tu(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function ru(e,t){var r=(e.length-t)%3;return r===0?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,r===1?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function nu(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function iu(e){return e.toString(this.encoding)}function ou(e){return e&&e.length?this.write(e):""}});var It=y((gc,ii)=>{"use strict";var ri=he().codes.ERR_STREAM_PREMATURE_CLOSE;function su(e){var t=!1;return function(){if(!t){t=!0;for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];e.apply(this,n)}}}function au(){}function uu(e){return e.setHeader&&typeof e.abort=="function"}function ni(e,t,r){if(typeof t=="function")return ni(e,null,t);t||(t={}),r=su(r||au);var n=t.readable||t.readable!==!1&&e.readable,i=t.writable||t.writable!==!1&&e.writable,o=function(){e.writable||a()},s=e._writableState&&e._writableState.finished,a=function(){i=!1,s=!0,n||r.call(e)},u=e._readableState&&e._readableState.endEmitted,c=function(){n=!1,u=!0,i||r.call(e)},l=function(v){r.call(e,v)},f=function(){var v;if(n&&!u)return(!e._readableState||!e._readableState.ended)&&(v=new ri),r.call(e,v);if(i&&!s)return(!e._writableState||!e._writableState.ended)&&(v=new ri),r.call(e,v)},d=function(){e.req.on("finish",a)};return uu(e)?(e.on("complete",a),e.on("abort",f),e.req?d():e.on("request",d)):i&&!e._writableState&&(e.on("end",o),e.on("close",o)),e.on("end",c),e.on("finish",a),t.error!==!1&&e.on("error",l),e.on("close",f),function(){e.removeListener("complete",a),e.removeListener("abort",f),e.removeListener("request",d),e.req&&e.req.removeListener("finish",a),e.removeListener("end",o),e.removeListener("close",o),e.removeListener("finish",a),e.removeListener("end",c),e.removeListener("error",l),e.removeListener("close",f)}}ii.exports=ni});var si=y((vc,oi)=>{"use strict";var Lt;function ge(e,t,r){return t=fu(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fu(e){var t=lu(e,"string");return typeof t=="symbol"?t:String(t)}function lu(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var cu=It(),ve=Symbol("lastResolve"),ke=Symbol("lastReject"),tt=Symbol("error"),qt=Symbol("ended"),Pe=Symbol("lastPromise"),Lr=Symbol("handlePromise"),Re=Symbol("stream");function ye(e,t){return{value:e,done:t}}function du(e){var t=e[ve];if(t!==null){var r=e[Re].read();r!==null&&(e[Pe]=null,e[ve]=null,e[ke]=null,t(ye(r,!1)))}}function hu(e){process.nextTick(du,e)}function pu(e,t){return function(r,n){e.then(function(){if(t[qt]){r(ye(void 0,!0));return}t[Lr](r,n)},n)}}var mu=Object.getPrototypeOf(function(){}),gu=Object.setPrototypeOf((Lt={get stream(){return this[Re]},next:function(){var t=this,r=this[tt];if(r!==null)return Promise.reject(r);if(this[qt])return Promise.resolve(ye(void 0,!0));if(this[Re].destroyed)return new Promise(function(s,a){process.nextTick(function(){t[tt]?a(t[tt]):s(ye(void 0,!0))})});var n=this[Pe],i;if(n)i=new Promise(pu(n,this));else{var o=this[Re].read();if(o!==null)return Promise.resolve(ye(o,!1));i=new Promise(this[Lr])}return this[Pe]=i,i}},ge(Lt,Symbol.asyncIterator,function(){return this}),ge(Lt,"return",function(){var t=this;return new Promise(function(r,n){t[Re].destroy(null,function(i){if(i){n(i);return}r(ye(void 0,!0))})})}),Lt),mu),vu=function(t){var r,n=Object.create(gu,(r={},ge(r,Re,{value:t,writable:!0}),ge(r,ve,{value:null,writable:!0}),ge(r,ke,{value:null,writable:!0}),ge(r,tt,{value:null,writable:!0}),ge(r,qt,{value:t._readableState.endEmitted,writable:!0}),ge(r,Lr,{value:function(o,s){var a=n[Re].read();a?(n[Pe]=null,n[ve]=null,n[ke]=null,o(ye(a,!1))):(n[ve]=o,n[ke]=s)},writable:!0}),r));return n[Pe]=null,cu(t,function(i){if(i&&i.code!=="ERR_STREAM_PREMATURE_CLOSE"){var o=n[ke];o!==null&&(n[Pe]=null,n[ve]=null,n[ke]=null,o(i)),n[tt]=i;return}var s=n[ve];s!==null&&(n[Pe]=null,n[ve]=null,n[ke]=null,s(ye(void 0,!0))),n[qt]=!0}),t.on("readable",hu.bind(null,n)),n};oi.exports=vu});var li=y((yc,fi)=>{"use strict";function ai(e,t,r,n,i,o,s){try{var a=e[o](s),u=a.value}catch(c){r(c);return}a.done?t(u):Promise.resolve(u).then(n,i)}function yu(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){var o=e.apply(t,r);function s(u){ai(o,n,i,s,a,"next",u)}function a(u){ai(o,n,i,s,a,"throw",u)}s(void 0)})}}function ui(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function bu(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ui(Object(r),!0).forEach(function(n){wu(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ui(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wu(e,t,r){return t=Su(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Su(e){var t=_u(e,"string");return typeof t=="symbol"?t:String(t)}function _u(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Cu=he().codes.ERR_INVALID_ARG_TYPE;function Eu(e,t,r){var n;if(t&&typeof t.next=="function")n=t;else if(t&&t[Symbol.asyncIterator])n=t[Symbol.asyncIterator]();else if(t&&t[Symbol.iterator])n=t[Symbol.iterator]();else throw new Cu("iterable",["Iterable"],t);var i=new e(bu({objectMode:!0},r)),o=!1;i._read=function(){o||(o=!0,s())};function s(){return a.apply(this,arguments)}function a(){return a=yu(function*(){try{var u=yield n.next(),c=u.value,l=u.done;l?i.push(null):i.push(yield c)?s():o=!1}catch(f){i.destroy(f)}}),a.apply(this,arguments)}return i}fi.exports=Eu});var Rr=y((wc,wi)=>{"use strict";wi.exports=k;var Ne;k.ReadableState=pi;var bc=require("events").EventEmitter,hi=function(t,r){return t.listeners(r).length},nt=mr(),Dt=require("buffer").Buffer,Tu=(typeof global<"u"?global:typeof window<"u"?window:typeof self<"u"?self:{}).Uint8Array||function(){};function ku(e){return Dt.from(e)}function Pu(e){return Dt.isBuffer(e)||e instanceof Tu}var qr=require("util"),_;qr&&qr.debuglog?_=qr.debuglog("stream"):_=function(){};var Ru=Ln(),Ur=yr(),Ou=br(),Au=Ou.getHighWaterMark,Nt=he().codes,xu=Nt.ERR_INVALID_ARG_TYPE,Iu=Nt.ERR_STREAM_PUSH_AFTER_EOF,Lu=Nt.ERR_METHOD_NOT_IMPLEMENTED,qu=Nt.ERR_STREAM_UNSHIFT_AFTER_END_EVENT,Me,Dr,Nr;pe()(k,nt);var rt=Ur.errorOrDestroy,Mr=["error","close","destroy","pause","resume"];function Du(e,t,r){if(typeof e.prependListener=="function")return e.prependListener(t,r);!e._events||!e._events[t]?e.on(t,r):Array.isArray(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]}function pi(e,t,r){Ne=Ne||Ee(),e=e||{},typeof r!="boolean"&&(r=t instanceof Ne),this.objectMode=!!e.objectMode,r&&(this.objectMode=this.objectMode||!!e.readableObjectMode),this.highWaterMark=Au(this,e,"readableHighWaterMark",r),this.buffer=new Ru,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=e.emitClose!==!1,this.autoDestroy=!!e.autoDestroy,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(Me||(Me=Ir().StringDecoder),this.decoder=new Me(e.encoding),this.encoding=e.encoding)}function k(e){if(Ne=Ne||Ee(),!(this instanceof k))return new k(e);var t=this instanceof Ne;this._readableState=new pi(e,this,t),this.readable=!0,e&&(typeof e.read=="function"&&(this._read=e.read),typeof e.destroy=="function"&&(this._destroy=e.destroy)),nt.call(this)}Object.defineProperty(k.prototype,"destroyed",{enumerable:!1,get:function(){return this._readableState===void 0?!1:this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}});k.prototype.destroy=Ur.destroy;k.prototype._undestroy=Ur.undestroy;k.prototype._destroy=function(e,t){t(e)};k.prototype.push=function(e,t){var r=this._readableState,n;return r.objectMode?n=!0:typeof e=="string"&&(t=t||r.defaultEncoding,t!==r.encoding&&(e=Dt.from(e,t),t=""),n=!0),mi(this,e,t,!1,n)};k.prototype.unshift=function(e){return mi(this,e,null,!0,!1)};function mi(e,t,r,n,i){_("readableAddChunk",t);var o=e._readableState;if(t===null)o.reading=!1,Fu(e,o);else{var s;if(i||(s=Nu(o,t)),s)rt(e,s);else if(o.objectMode||t&&t.length>0)if(typeof t!="string"&&!o.objectMode&&Object.getPrototypeOf(t)!==Dt.prototype&&(t=ku(t)),n)o.endEmitted?rt(e,new qu):Fr(e,o,t,!0);else if(o.ended)rt(e,new Iu);else{if(o.destroyed)return!1;o.reading=!1,o.decoder&&!r?(t=o.decoder.write(t),o.objectMode||t.length!==0?Fr(e,o,t,!1):Br(e,o)):Fr(e,o,t,!1)}else n||(o.reading=!1,Br(e,o))}return!o.ended&&(o.length<o.highWaterMark||o.length===0)}function Fr(e,t,r,n){t.flowing&&t.length===0&&!t.sync?(t.awaitDrain=0,e.emit("data",r)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&Mt(e)),Br(e,t)}function Nu(e,t){var r;return!Pu(t)&&typeof t!="string"&&t!==void 0&&!e.objectMode&&(r=new xu("chunk",["string","Buffer","Uint8Array"],t)),r}k.prototype.isPaused=function(){return this._readableState.flowing===!1};k.prototype.setEncoding=function(e){Me||(Me=Ir().StringDecoder);var t=new Me(e);this._readableState.decoder=t,this._readableState.encoding=this._readableState.decoder.encoding;for(var r=this._readableState.buffer.head,n="";r!==null;)n+=t.write(r.data),r=r.next;return this._readableState.buffer.clear(),n!==""&&this._readableState.buffer.push(n),this._readableState.length=n.length,this};var ci=1073741824;function Mu(e){return e>=ci?e=ci:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}function di(e,t){return e<=0||t.length===0&&t.ended?0:t.objectMode?1:e!==e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=Mu(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}k.prototype.read=function(e){_("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(e!==0&&(t.emittedReadable=!1),e===0&&t.needReadable&&((t.highWaterMark!==0?t.length>=t.highWaterMark:t.length>0)||t.ended))return _("read: emitReadable",t.length,t.ended),t.length===0&&t.ended?jr(this):Mt(this),null;if(e=di(e,t),e===0&&t.ended)return t.length===0&&jr(this),null;var n=t.needReadable;_("need readable",n),(t.length===0||t.length-e<t.highWaterMark)&&(n=!0,_("length less than watermark",n)),t.ended||t.reading?(n=!1,_("reading or ended",n)):n&&(_("do read"),t.reading=!0,t.sync=!0,t.length===0&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=di(r,t)));var i;return e>0?i=yi(e,t):i=null,i===null?(t.needReadable=t.length<=t.highWaterMark,e=0):(t.length-=e,t.awaitDrain=0),t.length===0&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&jr(this)),i!==null&&this.emit("data",i),i};function Fu(e,t){if(_("onEofChunk"),!t.ended){if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,t.sync?Mt(e):(t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,gi(e)))}}function Mt(e){var t=e._readableState;_("emitReadable",t.needReadable,t.emittedReadable),t.needReadable=!1,t.emittedReadable||(_("emitReadable",t.flowing),t.emittedReadable=!0,process.nextTick(gi,e))}function gi(e){var t=e._readableState;_("emitReadable_",t.destroyed,t.length,t.ended),!t.destroyed&&(t.length||t.ended)&&(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,Gr(e)}function Br(e,t){t.readingMore||(t.readingMore=!0,process.nextTick(ju,e,t))}function ju(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&t.length===0);){var r=t.length;if(_("maybeReadMore read 0"),e.read(0),r===t.length)break}t.readingMore=!1}k.prototype._read=function(e){rt(this,new Lu("_read()"))};k.prototype.pipe=function(e,t){var r=this,n=this._readableState;switch(n.pipesCount){case 0:n.pipes=e;break;case 1:n.pipes=[n.pipes,e];break;default:n.pipes.push(e);break}n.pipesCount+=1,_("pipe count=%d opts=%j",n.pipesCount,t);var i=(!t||t.end!==!1)&&e!==process.stdout&&e!==process.stderr,o=i?a:b;n.endEmitted?process.nextTick(o):r.once("end",o),e.on("unpipe",s);function s(S,p){_("onunpipe"),S===r&&p&&p.hasUnpiped===!1&&(p.hasUnpiped=!0,l())}function a(){_("onend"),e.end()}var u=Bu(r);e.on("drain",u);var c=!1;function l(){_("cleanup"),e.removeListener("close",h),e.removeListener("finish",v),e.removeListener("drain",u),e.removeListener("error",d),e.removeListener("unpipe",s),r.removeListener("end",a),r.removeListener("end",b),r.removeListener("data",f),c=!0,n.awaitDrain&&(!e._writableState||e._writableState.needDrain)&&u()}r.on("data",f);function f(S){_("ondata");var p=e.write(S);_("dest.write",p),p===!1&&((n.pipesCount===1&&n.pipes===e||n.pipesCount>1&&bi(n.pipes,e)!==-1)&&!c&&(_("false write response, pause",n.awaitDrain),n.awaitDrain++),r.pause())}function d(S){_("onerror",S),b(),e.removeListener("error",d),hi(e,"error")===0&&rt(e,S)}Du(e,"error",d);function h(){e.removeListener("finish",v),b()}e.once("close",h);function v(){_("onfinish"),e.removeListener("close",h),b()}e.once("finish",v);function b(){_("unpipe"),r.unpipe(e)}return e.emit("pipe",r),n.flowing||(_("pipe resume"),r.resume()),e};function Bu(e){return function(){var r=e._readableState;_("pipeOnDrain",r.awaitDrain),r.awaitDrain&&r.awaitDrain--,r.awaitDrain===0&&hi(e,"data")&&(r.flowing=!0,Gr(e))}}k.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(t.pipesCount===0)return this;if(t.pipesCount===1)return e&&e!==t.pipes?this:(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r),this);if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}var s=bi(t.pipes,e);return s===-1?this:(t.pipes.splice(s,1),t.pipesCount-=1,t.pipesCount===1&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r),this)};k.prototype.on=function(e,t){var r=nt.prototype.on.call(this,e,t),n=this._readableState;return e==="data"?(n.readableListening=this.listenerCount("readable")>0,n.flowing!==!1&&this.resume()):e==="readable"&&!n.endEmitted&&!n.readableListening&&(n.readableListening=n.needReadable=!0,n.flowing=!1,n.emittedReadable=!1,_("on readable",n.length,n.reading),n.length?Mt(this):n.reading||process.nextTick(Uu,this)),r};k.prototype.addListener=k.prototype.on;k.prototype.removeListener=function(e,t){var r=nt.prototype.removeListener.call(this,e,t);return e==="readable"&&process.nextTick(vi,this),r};k.prototype.removeAllListeners=function(e){var t=nt.prototype.removeAllListeners.apply(this,arguments);return(e==="readable"||e===void 0)&&process.nextTick(vi,this),t};function vi(e){var t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!t.paused?t.flowing=!0:e.listenerCount("data")>0&&e.resume()}function Uu(e){_("readable nexttick read 0"),e.read(0)}k.prototype.resume=function(){var e=this._readableState;return e.flowing||(_("resume"),e.flowing=!e.readableListening,Gu(this,e)),e.paused=!1,this};function Gu(e,t){t.resumeScheduled||(t.resumeScheduled=!0,process.nextTick(Wu,e,t))}function Wu(e,t){_("resume",t.reading),t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),Gr(e),t.flowing&&!t.reading&&e.read(0)}k.prototype.pause=function(){return _("call pause flowing=%j",this._readableState.flowing),this._readableState.flowing!==!1&&(_("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this};function Gr(e){var t=e._readableState;for(_("flow",t.flowing);t.flowing&&e.read()!==null;);}k.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;e.on("end",function(){if(_("wrapped end"),r.decoder&&!r.ended){var s=r.decoder.end();s&&s.length&&t.push(s)}t.push(null)}),e.on("data",function(s){if(_("wrapped data"),r.decoder&&(s=r.decoder.write(s)),!(r.objectMode&&s==null)&&!(!r.objectMode&&(!s||!s.length))){var a=t.push(s);a||(n=!0,e.pause())}});for(var i in e)this[i]===void 0&&typeof e[i]=="function"&&(this[i]=function(a){return function(){return e[a].apply(e,arguments)}}(i));for(var o=0;o<Mr.length;o++)e.on(Mr[o],this.emit.bind(this,Mr[o]));return this._read=function(s){_("wrapped _read",s),n&&(n=!1,e.resume())},this};typeof Symbol=="function"&&(k.prototype[Symbol.asyncIterator]=function(){return Dr===void 0&&(Dr=si()),Dr(this)});Object.defineProperty(k.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}});Object.defineProperty(k.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}});Object.defineProperty(k.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(t){this._readableState&&(this._readableState.flowing=t)}});k._fromList=yi;Object.defineProperty(k.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}});function yi(e,t){if(t.length===0)return null;var r;return t.objectMode?r=t.buffer.shift():!e||e>=t.length?(t.decoder?r=t.buffer.join(""):t.buffer.length===1?r=t.buffer.first():r=t.buffer.concat(t.length),t.buffer.clear()):r=t.buffer.consume(e,t.decoder),r}function jr(e){var t=e._readableState;_("endReadable",t.endEmitted),t.endEmitted||(t.ended=!0,process.nextTick(Hu,t,e))}function Hu(e,t){if(_("endReadableNT",e.endEmitted,e.length),!e.endEmitted&&e.length===0&&(e.endEmitted=!0,t.readable=!1,t.emit("end"),e.autoDestroy)){var r=t._writableState;(!r||r.autoDestroy&&r.finished)&&t.destroy()}}typeof Symbol=="function"&&(k.from=function(e,t){return Nr===void 0&&(Nr=li()),Nr(k,e,t)});function bi(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}});var Wr=y((Sc,_i)=>{"use strict";_i.exports=ue;var Ft=he().codes,Vu=Ft.ERR_METHOD_NOT_IMPLEMENTED,zu=Ft.ERR_MULTIPLE_CALLBACK,$u=Ft.ERR_TRANSFORM_ALREADY_TRANSFORMING,Ku=Ft.ERR_TRANSFORM_WITH_LENGTH_0,jt=Ee();pe()(ue,jt);function Yu(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(n===null)return this.emit("error",new zu);r.writechunk=null,r.writecb=null,t!=null&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function ue(e){if(!(this instanceof ue))return new ue(e);jt.call(this,e),this._transformState={afterTransform:Yu.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&(typeof e.transform=="function"&&(this._transform=e.transform),typeof e.flush=="function"&&(this._flush=e.flush)),this.on("prefinish",Ju)}function Ju(){var e=this;typeof this._flush=="function"&&!this._readableState.destroyed?this._flush(function(t,r){Si(e,t,r)}):Si(this,null,null)}ue.prototype.push=function(e,t){return this._transformState.needTransform=!1,jt.prototype.push.call(this,e,t)};ue.prototype._transform=function(e,t,r){r(new Vu("_transform()"))};ue.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}};ue.prototype._read=function(e){var t=this._transformState;t.writechunk!==null&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0};ue.prototype._destroy=function(e,t){jt.prototype._destroy.call(this,e,function(r){t(r)})};function Si(e,t,r){if(t)return e.emit("error",t);if(r!=null&&e.push(r),e._writableState.length)throw new Ku;if(e._transformState.transforming)throw new $u;return e.push(null)}});var Ti=y((_c,Ei)=>{"use strict";Ei.exports=it;var Ci=Wr();pe()(it,Ci);function it(e){if(!(this instanceof it))return new it(e);Ci.call(this,e)}it.prototype._transform=function(e,t,r){r(null,e)}});var Ai=y((Cc,Oi)=>{"use strict";var Hr;function Zu(e){var t=!1;return function(){t||(t=!0,e.apply(void 0,arguments))}}var Ri=he().codes,Xu=Ri.ERR_MISSING_ARGS,Qu=Ri.ERR_STREAM_DESTROYED;function ki(e){if(e)throw e}function ef(e){return e.setHeader&&typeof e.abort=="function"}function tf(e,t,r,n){n=Zu(n);var i=!1;e.on("close",function(){i=!0}),Hr===void 0&&(Hr=It()),Hr(e,{readable:t,writable:r},function(s){if(s)return n(s);i=!0,n()});var o=!1;return function(s){if(!i&&!o){if(o=!0,ef(e))return e.abort();if(typeof e.destroy=="function")return e.destroy();n(s||new Qu("pipe"))}}}function Pi(e){e()}function rf(e,t){return e.pipe(t)}function nf(e){return!e.length||typeof e[e.length-1]!="function"?ki:e.pop()}function of(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=nf(t);if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw new Xu("streams");var i,o=t.map(function(s,a){var u=a<t.length-1,c=a>0;return tf(s,u,c,function(l){i||(i=l),l&&o.forEach(Pi),!u&&(o.forEach(Pi),n(i))})});return t.reduce(rf)}Oi.exports=of});var Oe=y((K,st)=>{var ot=require("stream");process.env.READABLE_STREAM==="disable"&&ot?(st.exports=ot.Readable,Object.assign(st.exports,ot),st.exports.Stream=ot):(K=st.exports=Rr(),K.Stream=ot||K,K.Readable=K,K.Writable=Tr(),K.Duplex=Ee(),K.Transform=Wr(),K.PassThrough=Ti(),K.finished=It(),K.pipeline=Ai())});var Li=y((Ec,Ii)=>{Ii.exports=fe;var sf=require("util"),xi=Oe();sf.inherits(fe,xi.Duplex);function fe(e,t,r){var n=this;if(!(n instanceof fe))return new fe(e,t,r);xi.Duplex.call(n,r),n._output=null,n.connect(e,t)}fe.prototype.connect=function(e,t){var r=this;r.req=e,r._output=t,r.emit("response",t),t.on("data",function(n){r.push(n)||r._output.pause()}),t.on("end",function(){r.push(null)})};fe.prototype._read=function(e){this._output&&this._output.resume()};fe.prototype._write=function(e,t,r){this.req.write(e,t),r()};fe.prototype.end=function(e,t,r){return this._output.socket.destroy(),this.req.end(e,t,r)};fe.prototype.destroy=function(){this.req.destroy(),this._output.socket.destroy()}});var Di=y((Tc,qi)=>{var Fe=1e3,je=Fe*60,Be=je*60,Ae=Be*24,af=Ae*7,uf=Ae*365.25;qi.exports=function(e,t){t=t||{};var r=typeof e;if(r==="string"&&e.length>0)return ff(e);if(r==="number"&&isFinite(e))return t.long?cf(e):lf(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function ff(e){if(e=String(e),!(e.length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]),n=(t[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return r*uf;case"weeks":case"week":case"w":return r*af;case"days":case"day":case"d":return r*Ae;case"hours":case"hour":case"hrs":case"hr":case"h":return r*Be;case"minutes":case"minute":case"mins":case"min":case"m":return r*je;case"seconds":case"second":case"secs":case"sec":case"s":return r*Fe;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}function lf(e){var t=Math.abs(e);return t>=Ae?Math.round(e/Ae)+"d":t>=Be?Math.round(e/Be)+"h":t>=je?Math.round(e/je)+"m":t>=Fe?Math.round(e/Fe)+"s":e+"ms"}function cf(e){var t=Math.abs(e);return t>=Ae?Bt(e,t,Ae,"day"):t>=Be?Bt(e,t,Be,"hour"):t>=je?Bt(e,t,je,"minute"):t>=Fe?Bt(e,t,Fe,"second"):e+" ms"}function Bt(e,t,r,n){var i=t>=r*1.5;return Math.round(e/r)+" "+n+(i?"s":"")}});var Vr=y((kc,Ni)=>{function df(e){r.debug=r,r.default=r,r.coerce=u,r.disable=s,r.enable=i,r.enabled=a,r.humanize=Di(),r.destroy=c,Object.keys(e).forEach(l=>{r[l]=e[l]}),r.names=[],r.skips=[],r.formatters={};function t(l){let f=0;for(let d=0;d<l.length;d++)f=(f<<5)-f+l.charCodeAt(d),f|=0;return r.colors[Math.abs(f)%r.colors.length]}r.selectColor=t;function r(l){let f,d=null,h,v;function b(...S){if(!b.enabled)return;let p=b,w=Number(new Date),m=w-(f||w);p.diff=m,p.prev=f,p.curr=w,f=w,S[0]=r.coerce(S[0]),typeof S[0]!="string"&&S.unshift("%O");let C=0;S[0]=S[0].replace(/%([a-zA-Z%])/g,(te,Ye)=>{if(te==="%%")return"%";C++;let de=r.formatters[Ye];if(typeof de=="function"){let Je=S[C];te=de.call(p,Je),S.splice(C,1),C--}return te}),r.formatArgs.call(p,S),(p.log||r.log).apply(p,S)}return b.namespace=l,b.useColors=r.useColors(),b.color=r.selectColor(l),b.extend=n,b.destroy=r.destroy,Object.defineProperty(b,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(h!==r.namespaces&&(h=r.namespaces,v=r.enabled(l)),v),set:S=>{d=S}}),typeof r.init=="function"&&r.init(b),b}function n(l,f){let d=r(this.namespace+(typeof f>"u"?":":f)+l);return d.log=this.log,d}function i(l){r.save(l),r.namespaces=l,r.names=[],r.skips=[];let f=(typeof l=="string"?l:"").trim().replace(" ",",").split(",").filter(Boolean);for(let d of f)d[0]==="-"?r.skips.push(d.slice(1)):r.names.push(d)}function o(l,f){let d=0,h=0,v=-1,b=0;for(;d<l.length;)if(h<f.length&&(f[h]===l[d]||f[h]==="*"))f[h]==="*"?(v=h,b=d,h++):(d++,h++);else if(v!==-1)h=v+1,b++,d=b;else return!1;for(;h<f.length&&f[h]==="*";)h++;return h===f.length}function s(){let l=[...r.names,...r.skips.map(f=>"-"+f)].join(",");return r.enable(""),l}function a(l){for(let f of r.skips)if(o(l,f))return!1;for(let f of r.names)if(o(l,f))return!0;return!1}function u(l){return l instanceof Error?l.stack||l.message:l}function c(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return r.enable(r.load()),r}Ni.exports=df});var Mi=y((U,Ut)=>{U.formatArgs=pf;U.save=mf;U.load=gf;U.useColors=hf;U.storage=vf();U.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();U.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function hf(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function pf(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+Ut.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;e.splice(1,0,t,"color: inherit");let r=0,n=0;e[0].replace(/%[a-zA-Z%]/g,i=>{i!=="%%"&&(r++,i==="%c"&&(n=r))}),e.splice(n,0,t)}U.log=console.debug||console.log||(()=>{});function mf(e){try{e?U.storage.setItem("debug",e):U.storage.removeItem("debug")}catch{}}function gf(){let e;try{e=U.storage.getItem("debug")}catch{}return!e&&typeof process<"u"&&"env"in process&&(e=process.env.DEBUG),e}function vf(){try{return localStorage}catch{}}Ut.exports=Vr()(U);var{formatters:yf}=Ut.exports;yf.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}});var ji=y((Pc,Fi)=>{"use strict";Fi.exports=(e,t=process.argv)=>{let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),i=t.indexOf("--");return n!==-1&&(i===-1||n<i)}});var Gi=y((Rc,Ui)=>{"use strict";var bf=require("os"),Bi=require("tty"),Y=ji(),{env:q}=process,Gt;Y("no-color")||Y("no-colors")||Y("color=false")||Y("color=never")?Gt=0:(Y("color")||Y("colors")||Y("color=true")||Y("color=always"))&&(Gt=1);function wf(){if("FORCE_COLOR"in q)return q.FORCE_COLOR==="true"?1:q.FORCE_COLOR==="false"?0:q.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(q.FORCE_COLOR,10),3)}function Sf(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function _f(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=wf();n!==void 0&&(Gt=n);let i=r?Gt:n;if(i===0)return 0;if(r){if(Y("color=16m")||Y("color=full")||Y("color=truecolor"))return 3;if(Y("color=256"))return 2}if(e&&!t&&i===void 0)return 0;let o=i||0;if(q.TERM==="dumb")return o;if(process.platform==="win32"){let s=bf.release().split(".");return Number(s[0])>=10&&Number(s[2])>=10586?Number(s[2])>=14931?3:2:1}if("CI"in q)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(s=>s in q)||q.CI_NAME==="codeship"?1:o;if("TEAMCITY_VERSION"in q)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(q.TEAMCITY_VERSION)?1:0;if(q.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in q){let s=Number.parseInt((q.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(q.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(q.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(q.TERM)||"COLORTERM"in q?1:o}function zr(e,t={}){let r=_f(e,{streamIsTTY:e&&e.isTTY,...t});return Sf(r)}Ui.exports={supportsColor:zr,stdout:zr({isTTY:Bi.isatty(1)}),stderr:zr({isTTY:Bi.isatty(2)})}});var Hi=y((D,Ht)=>{var Cf=require("tty"),Wt=require("util");D.init=Af;D.log=Pf;D.formatArgs=Tf;D.save=Rf;D.load=Of;D.useColors=Ef;D.destroy=Wt.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");D.colors=[6,2,3,4,5,1];try{let e=Gi();e&&(e.stderr||e).level>=2&&(D.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}D.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(i,o)=>o.toUpperCase()),n=process.env[t];return/^(yes|on|true|enabled)$/i.test(n)?n=!0:/^(no|off|false|disabled)$/i.test(n)?n=!1:n==="null"?n=null:n=Number(n),e[r]=n,e},{});function Ef(){return"colors"in D.inspectOpts?!!D.inspectOpts.colors:Cf.isatty(process.stderr.fd)}function Tf(e){let{namespace:t,useColors:r}=this;if(r){let n=this.color,i="\x1B[3"+(n<8?n:"8;5;"+n),o=`  ${i};1m${t} \x1B[0m`;e[0]=o+e[0].split(`
`).join(`
`+o),e.push(i+"m+"+Ht.exports.humanize(this.diff)+"\x1B[0m")}else e[0]=kf()+t+" "+e[0]}function kf(){return D.inspectOpts.hideDate?"":new Date().toISOString()+" "}function Pf(...e){return process.stderr.write(Wt.formatWithOptions(D.inspectOpts,...e)+`
`)}function Rf(e){e?process.env.DEBUG=e:delete process.env.DEBUG}function Of(){return process.env.DEBUG}function Af(e){e.inspectOpts={};let t=Object.keys(D.inspectOpts);for(let r=0;r<t.length;r++)e.inspectOpts[t[r]]=D.inspectOpts[t[r]]}Ht.exports=Vr()(D);var{formatters:Wi}=Ht.exports;Wi.o=function(e){return this.inspectOpts.colors=this.useColors,Wt.inspect(e,this.inspectOpts).split(`
`).map(t=>t.trim()).join(" ")};Wi.O=function(e){return this.inspectOpts.colors=this.useColors,Wt.inspect(e,this.inspectOpts)}});var Vi=y((Oc,$r)=>{typeof process>"u"||process.type==="renderer"||process.browser===!0||process.__nwjs?$r.exports=Mi():$r.exports=Hi()});var $i=y((Ac,zi)=>{var xf=require("fs");zi.exports=function(e,t,r){t=typeof t<"u"?t:`
`,r=typeof r<"u"?r:"utf8";var n=[],i=xf.readFileSync(e,r);if(i.indexOf("-END CERTIFICATE-")<0||i.indexOf("-BEGIN CERTIFICATE-")<0)throw Error("File does not contain 'BEGIN CERTIFICATE' or 'END CERTIFICATE'");i=i.split(t);var o=[],s,a;for(s=0,a=i.length;s<a;s++){var u=i[s];u.length!==0&&(o.push(u),u.match(/-END CERTIFICATE-/)&&(n.push(o.join(t)),o=[]))}return n}});var Ji=y((xc,Yi)=>{var If=require("querystring"),Lf=kn(),Kr=require("fs"),zt=require("path"),Ue=require("url"),qf=Li(),Vt=Vi()("modem"),Ki=hr(),Df=require("util"),Ue=require("url"),Nf=$i(),Mf=require("os").type()==="Windows_NT",Ff=function(){var e,t={};if(!process.env.DOCKER_HOST)t.socketPath=Mf?"//./pipe/docker_engine":"/var/run/docker.sock";else if(process.env.DOCKER_HOST.indexOf("unix://")===0)t.socketPath=process.env.DOCKER_HOST.substring(7)||"/var/run/docker.sock";else if(process.env.DOCKER_HOST.indexOf("npipe://")===0)t.socketPath=process.env.DOCKER_HOST.substring(8)||"//./pipe/docker_engine";else{var r=process.env.DOCKER_HOST;r.indexOf("//")<0&&(r="tcp://"+r);try{e=new Ue.URL(r)}catch{throw new Error("DOCKER_HOST env variable should be something like tcp://localhost:1234")}t.port=e.port,process.env.DOCKER_TLS_VERIFY==="1"||t.port==="2376"?t.protocol="https":t.protocol="http",t.host=e.hostname,process.env.DOCKER_CERT_PATH&&(t.ca=Nf(zt.join(process.env.DOCKER_CERT_PATH,"ca.pem")),t.cert=Kr.readFileSync(zt.join(process.env.DOCKER_CERT_PATH,"cert.pem")),t.key=Kr.readFileSync(zt.join(process.env.DOCKER_CERT_PATH,"key.pem"))),process.env.DOCKER_CLIENT_TIMEOUT&&(t.timeout=parseInt(process.env.DOCKER_CLIENT_TIMEOUT,10))}return t},xe=function(e){var t=Ff(),r=Object.assign({},t,e);this.host=r.host,this.host||(this.socketPath=r.socketPath),this.port=r.port,this.username=r.username,this.password=r.password,this.version=r.version,this.key=r.key,this.cert=r.cert,this.ca=r.ca,this.timeout=r.timeout,this.connectionTimeout=r.connectionTimeout,this.checkServerIdentity=r.checkServerIdentity,this.agent=r.agent,this.headers=r.headers||{},this.key&&this.cert&&this.ca&&(this.protocol="https"),this.protocol=r.protocol||this.protocol||"http"};xe.prototype.dial=function(e,t){var r,n,i,o=this;if(e.options&&(r=e.options),r&&r.authconfig&&delete r.authconfig,r&&r.abortSignal&&delete r.abortSignal,this.version&&(e.path="/"+this.version+e.path),this.host){var s=Ue.parse(o.host);n=Ue.format({protocol:s.protocol||o.protocol,hostname:s.hostname||o.host,port:o.port}),n=Ue.resolve(n,e.path)}else n=e.path;e.path.indexOf("?")!==-1&&(r&&Object.keys(r).length>0?n+=this.buildQuerystring(r._query||r):n=n.substring(0,n.length-1));var a={path:n,method:e.method,headers:e.headers||Object.assign({},o.headers),key:o.key,cert:o.cert,ca:o.ca};if(this.checkServerIdentity&&(a.checkServerIdentity=this.checkServerIdentity),this.agent&&(a.agent=this.agent),e.authconfig&&(a.headers["X-Registry-Auth"]=e.authconfig.key||e.authconfig.base64||Buffer.from(JSON.stringify(e.authconfig)).toString("base64")),e.registryconfig&&(a.headers["X-Registry-Config"]=e.registryconfig.base64||Buffer.from(JSON.stringify(e.registryconfig)).toString("base64")),e.abortSignal&&(a.signal=e.abortSignal),e.file?(typeof e.file=="string"?i=Kr.createReadStream(zt.resolve(e.file)):i=e.file,a.headers["Content-Type"]="application/tar"):r&&e.method==="POST"&&(i=JSON.stringify(r._body||r),e.allowEmpty||i!=="{}"&&i!=='""'?a.headers["Content-Type"]="application/json":i=void 0),typeof i=="string"?a.headers["Content-Length"]=Buffer.byteLength(i):Buffer.isBuffer(i)===!0?a.headers["Content-Length"]=i.length:(a.method==="PUT"||e.hijack||e.openStdin)&&(a.headers["Transfer-Encoding"]="chunked"),e.hijack&&(a.headers.Connection="Upgrade",a.headers.Upgrade="tcp"),this.socketPath)a.socketPath=this.socketPath;else{var u=Ue.parse(n);a.hostname=u.hostname,a.port=u.port,a.path=u.path}this.buildRequest(a,e,i,t)};xe.prototype.buildRequest=function(e,t,r,n){var i=this,o,s=Lf[i.protocol].request(e,function(){});Vt("Sending: %s",Df.inspect(e,{showHidden:!0,depth:null})),i.connectionTimeout&&(o=setTimeout(function(){Vt("Connection Timeout of %s ms exceeded",i.connectionTimeout),s.abort()},i.connectionTimeout)),i.timeout&&s.on("socket",function(a){a.setTimeout(i.timeout),a.on("timeout",function(){Vt("Timeout of %s ms exceeded",i.timeout),s.abort()})}),t.hijack===!0&&(clearTimeout(o),s.on("upgrade",function(a,u,c){return n(null,u)})),s.on("connect",function(){clearTimeout(o)}),s.on("disconnect",function(){clearTimeout(o)}),s.on("response",function(a){if(clearTimeout(o),t.isStream===!0)i.buildPayload(null,t.isStream,t.statusCodes,t.openStdin,s,a,null,n);else{var u=[];a.on("data",function(c){u.push(c)}),a.on("end",function(){var c=Buffer.concat(u),l=c.toString();Vt("Received: %s",l);var f=Ki.parseJSON(l)||c;i.buildPayload(null,t.isStream,t.statusCodes,!1,s,a,f,n)})}}),s.on("error",function(a){clearTimeout(o),i.buildPayload(a,t.isStream,t.statusCodes,!1,{},{},null,n)}),typeof r=="string"||Buffer.isBuffer(r)?s.write(r):r&&(r.on("error",function(a){s.destroy(a)}),r.pipe(s)),!t.hijack&&!t.openStdin&&(typeof r=="string"||r===void 0||Buffer.isBuffer(r))&&s.end()};xe.prototype.buildPayload=function(e,t,r,n,i,o,s,a){if(e)return a(e,null);r[o.statusCode]!==!0?u(t,o,s,function(c,l){var f=new Error("(HTTP code "+o.statusCode+") "+(r[o.statusCode]||"unexpected")+" - "+(l.message||l)+" ");f.reason=r[o.statusCode],f.statusCode=o.statusCode,f.json=s,a(f,null)}):n?a(null,new qf(i,o)):t?a(null,o):a(null,s);function u(c,l,f,d){var h="";c?(l.on("data",function(v){h+=v}),l.on("end",function(){d(null,Ki.parseJSON(h)||h)})):d(null,f)}};xe.prototype.demuxStream=function(e,t,r){var n=null,i=null,o=Buffer.from("");function s(u){if(u&&(o=Buffer.concat([o,u])),n){if(o.length>=i){var l=a(i);n===1?t.write(l):r.write(l),n=null,s()}}else if(o.length>=8){var c=a(8);n=c.readUInt8(0),i=c.readUInt32BE(4),s()}}function a(u){var c=o.slice(0,u);return o=Buffer.from(o.slice(u,o.length)),c}e.on("data",s)};xe.prototype.followProgress=function(e,t,r){var n="",i=[],o=!1;e.on("data",s),e.on("error",a),e.on("end",u),e.on("close",u);function s(c){n+=c.toString(),l();function l(){for(var d;(d=n.indexOf(`
`))>=0;){if(d==0){n=n.slice(1);continue}f(n.slice(0,d)),n=n.slice(d+1)}}function f(d){if(d[d.length-1]=="\r"&&(d=d.substr(0,d.length-1)),d.length>0){var h=JSON.parse(d);i.push(h),r&&r(h)}}}function a(c){o=!0,e.removeListener("data",s),e.removeListener("error",a),e.removeListener("end",u),e.removeListener("close",u),t(c,i)}function u(){o||t(null,i),o=!0}};xe.prototype.buildQuerystring=function(e){var t={};return Object.keys(e).map(function(r,n){e[r]&&typeof e[r]=="object"&&!Array.isArray(e[r])||r==="cachefrom"?t[r]=JSON.stringify(e[r]):t[r]=e[r]}),If.stringify(t)};Yi.exports=xe});var no=y((Ic,ro)=>{"use strict";var J=require("fs"),Ie=require("path"),jf=J.lchown?"lchown":"chown",Bf=J.lchownSync?"lchownSync":"chownSync",Xi=J.lchown&&!process.version.match(/v1[1-9]+\./)&&!process.version.match(/v10\.[6-9]/),Zi=(e,t,r)=>{try{return J[Bf](e,t,r)}catch(n){if(n.code!=="ENOENT")throw n}},Uf=(e,t,r)=>{try{return J.chownSync(e,t,r)}catch(n){if(n.code!=="ENOENT")throw n}},Gf=Xi?(e,t,r,n)=>i=>{!i||i.code!=="EISDIR"?n(i):J.chown(e,t,r,n)}:(e,t,r,n)=>n,Yr=Xi?(e,t,r)=>{try{return Zi(e,t,r)}catch(n){if(n.code!=="EISDIR")throw n;Uf(e,t,r)}}:(e,t,r)=>Zi(e,t,r),Wf=process.version,Qi=(e,t,r)=>J.readdir(e,t,r),Hf=(e,t)=>J.readdirSync(e,t);/^v4\./.test(Wf)&&(Qi=(e,t,r)=>J.readdir(e,r));var $t=(e,t,r,n)=>{J[jf](e,t,r,Gf(e,t,r,i=>{n(i&&i.code!=="ENOENT"?i:null)}))},eo=(e,t,r,n,i)=>{if(typeof t=="string")return J.lstat(Ie.resolve(e,t),(o,s)=>{if(o)return i(o.code!=="ENOENT"?o:null);s.name=t,eo(e,s,r,n,i)});if(t.isDirectory())Jr(Ie.resolve(e,t.name),r,n,o=>{if(o)return i(o);let s=Ie.resolve(e,t.name);$t(s,r,n,i)});else{let o=Ie.resolve(e,t.name);$t(o,r,n,i)}},Jr=(e,t,r,n)=>{Qi(e,{withFileTypes:!0},(i,o)=>{if(i){if(i.code==="ENOENT")return n();if(i.code!=="ENOTDIR"&&i.code!=="ENOTSUP")return n(i)}if(i||!o.length)return $t(e,t,r,n);let s=o.length,a=null,u=c=>{if(!a){if(c)return n(a=c);if(--s===0)return $t(e,t,r,n)}};o.forEach(c=>eo(e,c,t,r,u))})},Vf=(e,t,r,n)=>{if(typeof t=="string")try{let i=J.lstatSync(Ie.resolve(e,t));i.name=t,t=i}catch(i){if(i.code==="ENOENT")return;throw i}t.isDirectory()&&to(Ie.resolve(e,t.name),r,n),Yr(Ie.resolve(e,t.name),r,n)},to=(e,t,r)=>{let n;try{n=Hf(e,{withFileTypes:!0})}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR"||i.code==="ENOTSUP")return Yr(e,t,r);throw i}return n&&n.length&&n.forEach(i=>Vf(e,i,t,r)),Yr(e,t,r)};ro.exports=Jr;Jr.sync=to});var so=y((Lc,oo)=>{"use strict";var{Buffer:Q}=require("buffer"),io=Symbol.for("BufferList");function A(e){if(!(this instanceof A))return new A(e);A._init.call(this,e)}A._init=function(t){Object.defineProperty(this,io,{value:!0}),this._bufs=[],this.length=0,t&&this.append(t)};A.prototype._new=function(t){return new A(t)};A.prototype._offset=function(t){if(t===0)return[0,0];let r=0;for(let n=0;n<this._bufs.length;n++){let i=r+this._bufs[n].length;if(t<i||n===this._bufs.length-1)return[n,t-r];r=i}};A.prototype._reverseOffset=function(e){let t=e[0],r=e[1];for(let n=0;n<t;n++)r+=this._bufs[n].length;return r};A.prototype.get=function(t){if(t>this.length||t<0)return;let r=this._offset(t);return this._bufs[r[0]][r[1]]};A.prototype.slice=function(t,r){return typeof t=="number"&&t<0&&(t+=this.length),typeof r=="number"&&r<0&&(r+=this.length),this.copy(null,0,t,r)};A.prototype.copy=function(t,r,n,i){if((typeof n!="number"||n<0)&&(n=0),(typeof i!="number"||i>this.length)&&(i=this.length),n>=this.length||i<=0)return t||Q.alloc(0);let o=!!t,s=this._offset(n),a=i-n,u=a,c=o&&r||0,l=s[1];if(n===0&&i===this.length){if(!o)return this._bufs.length===1?this._bufs[0]:Q.concat(this._bufs,this.length);for(let f=0;f<this._bufs.length;f++)this._bufs[f].copy(t,c),c+=this._bufs[f].length;return t}if(u<=this._bufs[s[0]].length-l)return o?this._bufs[s[0]].copy(t,r,l,l+u):this._bufs[s[0]].slice(l,l+u);o||(t=Q.allocUnsafe(a));for(let f=s[0];f<this._bufs.length;f++){let d=this._bufs[f].length-l;if(u>d)this._bufs[f].copy(t,c,l),c+=d;else{this._bufs[f].copy(t,c,l,l+u),c+=d;break}u-=d,l&&(l=0)}return t.length>c?t.slice(0,c):t};A.prototype.shallowSlice=function(t,r){if(t=t||0,r=typeof r!="number"?this.length:r,t<0&&(t+=this.length),r<0&&(r+=this.length),t===r)return this._new();let n=this._offset(t),i=this._offset(r),o=this._bufs.slice(n[0],i[0]+1);return i[1]===0?o.pop():o[o.length-1]=o[o.length-1].slice(0,i[1]),n[1]!==0&&(o[0]=o[0].slice(n[1])),this._new(o)};A.prototype.toString=function(t,r,n){return this.slice(r,n).toString(t)};A.prototype.consume=function(t){if(t=Math.trunc(t),Number.isNaN(t)||t<=0)return this;for(;this._bufs.length;)if(t>=this._bufs[0].length)t-=this._bufs[0].length,this.length-=this._bufs[0].length,this._bufs.shift();else{this._bufs[0]=this._bufs[0].slice(t),this.length-=t;break}return this};A.prototype.duplicate=function(){let t=this._new();for(let r=0;r<this._bufs.length;r++)t.append(this._bufs[r]);return t};A.prototype.append=function(t){if(t==null)return this;if(t.buffer)this._appendBuffer(Q.from(t.buffer,t.byteOffset,t.byteLength));else if(Array.isArray(t))for(let r=0;r<t.length;r++)this.append(t[r]);else if(this._isBufferList(t))for(let r=0;r<t._bufs.length;r++)this.append(t._bufs[r]);else typeof t=="number"&&(t=t.toString()),this._appendBuffer(Q.from(t));return this};A.prototype._appendBuffer=function(t){this._bufs.push(t),this.length+=t.length};A.prototype.indexOf=function(e,t,r){if(r===void 0&&typeof t=="string"&&(r=t,t=void 0),typeof e=="function"||Array.isArray(e))throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.');if(typeof e=="number"?e=Q.from([e]):typeof e=="string"?e=Q.from(e,r):this._isBufferList(e)?e=e.slice():Array.isArray(e.buffer)?e=Q.from(e.buffer,e.byteOffset,e.byteLength):Q.isBuffer(e)||(e=Q.from(e)),t=Number(t||0),isNaN(t)&&(t=0),t<0&&(t=this.length+t),t<0&&(t=0),e.length===0)return t>this.length?this.length:t;let n=this._offset(t),i=n[0],o=n[1];for(;i<this._bufs.length;i++){let s=this._bufs[i];for(;o<s.length;)if(s.length-o>=e.length){let u=s.indexOf(e,o);if(u!==-1)return this._reverseOffset([i,u]);o=s.length-e.length+1}else{let u=this._reverseOffset([i,o]);if(this._match(u,e))return u;o++}o=0}return-1};A.prototype._match=function(e,t){if(this.length-e<t.length)return!1;for(let r=0;r<t.length;r++)if(this.get(e+r)!==t[r])return!1;return!0};(function(){let e={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1,readIntBE:null,readIntLE:null,readUIntBE:null,readUIntLE:null};for(let t in e)(function(r){e[r]===null?A.prototype[r]=function(n,i){return this.slice(n,n+i)[r](0,i)}:A.prototype[r]=function(n=0){return this.slice(n,n+e[r])[r](0)}})(t)})();A.prototype._isBufferList=function(t){return t instanceof A||A.isBufferList(t)};A.isBufferList=function(t){return t!=null&&t[io]};oo.exports=A});var ao=y((qc,Kt)=>{"use strict";var Zr=Oe().Duplex,zf=pe(),at=so();function M(e){if(!(this instanceof M))return new M(e);if(typeof e=="function"){this._callback=e;let t=function(n){this._callback&&(this._callback(n),this._callback=null)}.bind(this);this.on("pipe",function(n){n.on("error",t)}),this.on("unpipe",function(n){n.removeListener("error",t)}),e=null}at._init.call(this,e),Zr.call(this)}zf(M,Zr);Object.assign(M.prototype,at.prototype);M.prototype._new=function(t){return new M(t)};M.prototype._write=function(t,r,n){this._appendBuffer(t),typeof n=="function"&&n()};M.prototype._read=function(t){if(!this.length)return this.push(null);t=Math.min(t,this.length),this.push(this.slice(0,t)),this.consume(t)};M.prototype.end=function(t){Zr.prototype.end.call(this,t),this._callback&&(this._callback(null,this.slice()),this._callback=null)};M.prototype._destroy=function(t,r){this._bufs.length=0,this.length=0,r(t)};M.prototype._isBufferList=function(t){return t instanceof M||t instanceof at||M.isBufferList(t)};M.isBufferList=at.isBufferList;Kt.exports=M;Kt.exports.BufferListStream=M;Kt.exports.BufferList=at});var en=y(We=>{var $f=Buffer.alloc,Kf="0000000000000000000",Yf="7777777777777777777",uo=48,fo=Buffer.from("ustar\0","binary"),Jf=Buffer.from("00","binary"),Zf=Buffer.from("ustar ","binary"),Xf=Buffer.from(" \0","binary"),Qf=parseInt("7777",8),ut=257,Qr=263,el=function(e,t,r){return typeof e!="number"?r:(e=~~e,e>=t?t:e>=0||(e+=t,e>=0)?e:0)},tl=function(e){switch(e){case 0:return"file";case 1:return"link";case 2:return"symlink";case 3:return"character-device";case 4:return"block-device";case 5:return"directory";case 6:return"fifo";case 7:return"contiguous-file";case 72:return"pax-header";case 55:return"pax-global-header";case 27:return"gnu-long-link-path";case 28:case 30:return"gnu-long-path"}return null},rl=function(e){switch(e){case"file":return 0;case"link":return 1;case"symlink":return 2;case"character-device":return 3;case"block-device":return 4;case"directory":return 5;case"fifo":return 6;case"contiguous-file":return 7;case"pax-header":return 72}return 0},lo=function(e,t,r,n){for(;r<n;r++)if(e[r]===t)return r;return n},co=function(e){for(var t=256,r=0;r<148;r++)t+=e[r];for(var n=156;n<512;n++)t+=e[n];return t},be=function(e,t){return e=e.toString(8),e.length>t?Yf.slice(0,t)+" ":Kf.slice(0,t-e.length)+e+" "};function nl(e){var t;if(e[0]===128)t=!0;else if(e[0]===255)t=!1;else return null;for(var r=[],n=e.length-1;n>0;n--){var i=e[n];t?r.push(i):r.push(255-i)}var o=0,s=r.length;for(n=0;n<s;n++)o+=r[n]*Math.pow(256,n);return t?o:-1*o}var we=function(e,t,r){if(e=e.slice(t,t+r),t=0,e[t]&128)return nl(e);for(;t<e.length&&e[t]===32;)t++;for(var n=el(lo(e,32,t,e.length),e.length,e.length);t<n&&e[t]===0;)t++;return n===t?0:parseInt(e.slice(t,n).toString(),8)},Ge=function(e,t,r,n){return e.slice(t,lo(e,0,t,t+r)).toString(n)},Xr=function(e){var t=Buffer.byteLength(e),r=Math.floor(Math.log(t)/Math.log(10))+1;return t+r>=Math.pow(10,r)&&r++,t+r+e};We.decodeLongPath=function(e,t){return Ge(e,0,e.length,t)};We.encodePax=function(e){var t="";e.name&&(t+=Xr(" path="+e.name+`
`)),e.linkname&&(t+=Xr(" linkpath="+e.linkname+`
`));var r=e.pax;if(r)for(var n in r)t+=Xr(" "+n+"="+r[n]+`
`);return Buffer.from(t)};We.decodePax=function(e){for(var t={};e.length;){for(var r=0;r<e.length&&e[r]!==32;)r++;var n=parseInt(e.slice(0,r).toString(),10);if(!n)return t;var i=e.slice(r+1,n-1).toString(),o=i.indexOf("=");if(o===-1)return t;t[i.slice(0,o)]=i.slice(o+1),e=e.slice(n)}return t};We.encode=function(e){var t=$f(512),r=e.name,n="";if(e.typeflag===5&&r[r.length-1]!=="/"&&(r+="/"),Buffer.byteLength(r)!==r.length)return null;for(;Buffer.byteLength(r)>100;){var i=r.indexOf("/");if(i===-1)return null;n+=n?"/"+r.slice(0,i):r.slice(0,i),r=r.slice(i+1)}return Buffer.byteLength(r)>100||Buffer.byteLength(n)>155||e.linkname&&Buffer.byteLength(e.linkname)>100?null:(t.write(r),t.write(be(e.mode&Qf,6),100),t.write(be(e.uid,6),108),t.write(be(e.gid,6),116),t.write(be(e.size,11),124),t.write(be(e.mtime.getTime()/1e3|0,11),136),t[156]=uo+rl(e.type),e.linkname&&t.write(e.linkname,157),fo.copy(t,ut),Jf.copy(t,Qr),e.uname&&t.write(e.uname,265),e.gname&&t.write(e.gname,297),t.write(be(e.devmajor||0,6),329),t.write(be(e.devminor||0,6),337),n&&t.write(n,345),t.write(be(co(t),6),148),t)};We.decode=function(e,t,r){var n=e[156]===0?0:e[156]-uo,i=Ge(e,0,100,t),o=we(e,100,8),s=we(e,108,8),a=we(e,116,8),u=we(e,124,12),c=we(e,136,12),l=tl(n),f=e[157]===0?null:Ge(e,157,100,t),d=Ge(e,265,32),h=Ge(e,297,32),v=we(e,329,8),b=we(e,337,8),S=co(e);if(S===8*32)return null;if(S!==we(e,148,8))throw new Error("Invalid tar header. Maybe the tar is corrupted or it needs to be gunzipped?");if(fo.compare(e,ut,ut+6)===0)e[345]&&(i=Ge(e,345,155,t)+"/"+i);else if(!(Zf.compare(e,ut,ut+6)===0&&Xf.compare(e,Qr,Qr+2)===0)){if(!r)throw new Error("Invalid tar header: unknown format.")}return n===0&&i&&i[i.length-1]==="/"&&(n=5),{name:i,mode:o,uid:s,gid:a,size:u,mtime:new Date(1e3*c),type:l,linkname:f,uname:d,gname:h,devmajor:v,devminor:b}}});var bo=y((Nc,yo)=>{var po=require("util"),il=ao(),ft=en(),mo=Oe().Writable,go=Oe().PassThrough,vo=function(){},ho=function(e){return e&=511,e&&512-e},ol=function(e,t){var r=new Yt(e,t);return r.end(),r},sl=function(e,t){return t.path&&(e.name=t.path),t.linkpath&&(e.linkname=t.linkpath),t.size&&(e.size=parseInt(t.size,10)),e.pax=t,e},Yt=function(e,t){this._parent=e,this.offset=t,go.call(this,{autoDestroy:!1})};po.inherits(Yt,go);Yt.prototype.destroy=function(e){this._parent.destroy(e)};var le=function(e){if(!(this instanceof le))return new le(e);mo.call(this,e),e=e||{},this._offset=0,this._buffer=il(),this._missing=0,this._partial=!1,this._onparse=vo,this._header=null,this._stream=null,this._overflow=null,this._cb=null,this._locked=!1,this._destroyed=!1,this._pax=null,this._paxGlobal=null,this._gnuLongPath=null,this._gnuLongLinkPath=null;var t=this,r=t._buffer,n=function(){t._continue()},i=function(d){if(t._locked=!1,d)return t.destroy(d);t._stream||n()},o=function(){t._stream=null;var d=ho(t._header.size);d?t._parse(d,s):t._parse(512,f),t._locked||n()},s=function(){t._buffer.consume(ho(t._header.size)),t._parse(512,f),n()},a=function(){var d=t._header.size;t._paxGlobal=ft.decodePax(r.slice(0,d)),r.consume(d),o()},u=function(){var d=t._header.size;t._pax=ft.decodePax(r.slice(0,d)),t._paxGlobal&&(t._pax=Object.assign({},t._paxGlobal,t._pax)),r.consume(d),o()},c=function(){var d=t._header.size;this._gnuLongPath=ft.decodeLongPath(r.slice(0,d),e.filenameEncoding),r.consume(d),o()},l=function(){var d=t._header.size;this._gnuLongLinkPath=ft.decodeLongPath(r.slice(0,d),e.filenameEncoding),r.consume(d),o()},f=function(){var d=t._offset,h;try{h=t._header=ft.decode(r.slice(0,512),e.filenameEncoding,e.allowUnknownFormat)}catch(v){t.emit("error",v)}if(r.consume(512),!h){t._parse(512,f),n();return}if(h.type==="gnu-long-path"){t._parse(h.size,c),n();return}if(h.type==="gnu-long-link-path"){t._parse(h.size,l),n();return}if(h.type==="pax-global-header"){t._parse(h.size,a),n();return}if(h.type==="pax-header"){t._parse(h.size,u),n();return}if(t._gnuLongPath&&(h.name=t._gnuLongPath,t._gnuLongPath=null),t._gnuLongLinkPath&&(h.linkname=t._gnuLongLinkPath,t._gnuLongLinkPath=null),t._pax&&(t._header=h=sl(h,t._pax),t._pax=null),t._locked=!0,!h.size||h.type==="directory"){t._parse(512,f),t.emit("entry",h,ol(t,d),i);return}t._stream=new Yt(t,d),t.emit("entry",h,t._stream,i),t._parse(h.size,o),n()};this._onheader=f,this._parse(512,f)};po.inherits(le,mo);le.prototype.destroy=function(e){this._destroyed||(this._destroyed=!0,e&&this.emit("error",e),this.emit("close"),this._stream&&this._stream.emit("close"))};le.prototype._parse=function(e,t){this._destroyed||(this._offset+=e,this._missing=e,t===this._onheader&&(this._partial=!1),this._onparse=t)};le.prototype._continue=function(){if(!this._destroyed){var e=this._cb;this._cb=vo,this._overflow?this._write(this._overflow,void 0,e):e()}};le.prototype._write=function(e,t,r){if(!this._destroyed){var n=this._stream,i=this._buffer,o=this._missing;if(e.length&&(this._partial=!0),e.length<o)return this._missing-=e.length,this._overflow=null,n?n.write(e,r):(i.append(e),r());this._cb=r,this._missing=0;var s=null;e.length>o&&(s=e.slice(o),e=e.slice(0,o)),n?n.end(e):i.append(e),this._overflow=s,this._onparse()}};le.prototype._final=function(e){if(this._partial)return this.destroy(new Error("Unexpected end of data"));e()};yo.exports=le});var So=y((Mc,wo)=>{wo.exports=require("fs").constants||require("constants")});var Eo=y((Fc,Co)=>{Co.exports=_o;function _o(e,t){if(e&&t)return _o(e)(t);if(typeof e!="function")throw new TypeError("need wrapper function");return Object.keys(e).forEach(function(n){r[n]=e[n]}),r;function r(){for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];var o=e.apply(this,n),s=n[n.length-1];return typeof o=="function"&&o!==s&&Object.keys(s).forEach(function(a){o[a]=s[a]}),o}}});var rn=y((jc,tn)=>{var To=Eo();tn.exports=To(Jt);tn.exports.strict=To(ko);Jt.proto=Jt(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return Jt(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return ko(this)},configurable:!0})});function Jt(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function ko(e){var t=function(){if(t.called)throw new Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)},r=e.name||"Function wrapped with `once`";return t.onceError=r+" shouldn't be called more than once",t.called=!1,t}});var nn=y((Bc,Ro)=>{var al=rn(),ul=function(){},fl=function(e){return e.setHeader&&typeof e.abort=="function"},ll=function(e){return e.stdio&&Array.isArray(e.stdio)&&e.stdio.length===3},Po=function(e,t,r){if(typeof t=="function")return Po(e,null,t);t||(t={}),r=al(r||ul);var n=e._writableState,i=e._readableState,o=t.readable||t.readable!==!1&&e.readable,s=t.writable||t.writable!==!1&&e.writable,a=!1,u=function(){e.writable||c()},c=function(){s=!1,o||r.call(e)},l=function(){o=!1,s||r.call(e)},f=function(S){r.call(e,S?new Error("exited with error code: "+S):null)},d=function(S){r.call(e,S)},h=function(){process.nextTick(v)},v=function(){if(!a){if(o&&!(i&&i.ended&&!i.destroyed))return r.call(e,new Error("premature close"));if(s&&!(n&&n.ended&&!n.destroyed))return r.call(e,new Error("premature close"))}},b=function(){e.req.on("finish",c)};return fl(e)?(e.on("complete",c),e.on("abort",h),e.req?b():e.on("request",b)):s&&!n&&(e.on("end",u),e.on("close",u)),ll(e)&&e.on("exit",f),e.on("end",l),e.on("finish",c),t.error!==!1&&e.on("error",d),e.on("close",h),function(){a=!0,e.removeListener("complete",c),e.removeListener("abort",h),e.removeListener("request",b),e.req&&e.req.removeListener("finish",c),e.removeListener("end",u),e.removeListener("close",u),e.removeListener("finish",c),e.removeListener("exit",f),e.removeListener("end",l),e.removeListener("error",d),e.removeListener("close",h)}};Ro.exports=Po});var Lo=y((Uc,Io)=>{var He=So(),Oo=nn(),Xt=pe(),cl=Buffer.alloc,Ao=Oe().Readable,Ve=Oe().Writable,dl=require("string_decoder").StringDecoder,Zt=en(),hl=parseInt("755",8),pl=parseInt("644",8),xo=cl(1024),sn=function(){},on=function(e,t){t&=511,t&&e.push(xo.slice(0,512-t))};function ml(e){switch(e&He.S_IFMT){case He.S_IFBLK:return"block-device";case He.S_IFCHR:return"character-device";case He.S_IFDIR:return"directory";case He.S_IFIFO:return"fifo";case He.S_IFLNK:return"symlink"}return"file"}var Qt=function(e){Ve.call(this),this.written=0,this._to=e,this._destroyed=!1};Xt(Qt,Ve);Qt.prototype._write=function(e,t,r){if(this.written+=e.length,this._to.push(e))return r();this._to._drain=r};Qt.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this.emit("close"))};var er=function(){Ve.call(this),this.linkname="",this._decoder=new dl("utf-8"),this._destroyed=!1};Xt(er,Ve);er.prototype._write=function(e,t,r){this.linkname+=this._decoder.write(e),r()};er.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this.emit("close"))};var lt=function(){Ve.call(this),this._destroyed=!1};Xt(lt,Ve);lt.prototype._write=function(e,t,r){r(new Error("No body allowed for this entry"))};lt.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this.emit("close"))};var oe=function(e){if(!(this instanceof oe))return new oe(e);Ao.call(this,e),this._drain=sn,this._finalized=!1,this._finalizing=!1,this._destroyed=!1,this._stream=null};Xt(oe,Ao);oe.prototype.entry=function(e,t,r){if(this._stream)throw new Error("already piping an entry");if(!(this._finalized||this._destroyed)){typeof t=="function"&&(r=t,t=null),r||(r=sn);var n=this;if((!e.size||e.type==="symlink")&&(e.size=0),e.type||(e.type=ml(e.mode)),e.mode||(e.mode=e.type==="directory"?hl:pl),e.uid||(e.uid=0),e.gid||(e.gid=0),e.mtime||(e.mtime=new Date),typeof t=="string"&&(t=Buffer.from(t)),Buffer.isBuffer(t)){e.size=t.length,this._encode(e);var i=this.push(t);return on(n,e.size),i?process.nextTick(r):this._drain=r,new lt}if(e.type==="symlink"&&!e.linkname){var o=new er;return Oo(o,function(a){if(a)return n.destroy(),r(a);e.linkname=o.linkname,n._encode(e),r()}),o}if(this._encode(e),e.type!=="file"&&e.type!=="contiguous-file")return process.nextTick(r),new lt;var s=new Qt(this);return this._stream=s,Oo(s,function(a){if(n._stream=null,a)return n.destroy(),r(a);if(s.written!==e.size)return n.destroy(),r(new Error("size mismatch"));on(n,e.size),n._finalizing&&n.finalize(),r()}),s}};oe.prototype.finalize=function(){if(this._stream){this._finalizing=!0;return}this._finalized||(this._finalized=!0,this.push(xo),this.push(null))};oe.prototype.destroy=function(e){this._destroyed||(this._destroyed=!0,e&&this.emit("error",e),this.emit("close"),this._stream&&this._stream.destroy&&this._stream.destroy())};oe.prototype._encode=function(e){if(!e.pax){var t=Zt.encode(e);if(t){this.push(t);return}}this._encodePax(e)};oe.prototype._encodePax=function(e){var t=Zt.encodePax({name:e.name,linkname:e.linkname,pax:e.pax}),r={name:"PaxHeader",mode:e.mode,uid:e.uid,gid:e.gid,size:t.length,mtime:e.mtime,type:"pax-header",linkname:e.linkname&&"PaxHeader",uname:e.uname,gname:e.gname,devmajor:e.devmajor,devminor:e.devminor};this.push(Zt.encode(r)),this.push(t),on(this,t.length),r.size=e.size,r.type=e.type,this.push(Zt.encode(r))};oe.prototype._read=function(e){var t=this._drain;this._drain=sn,t()};Io.exports=oe});var qo=y(an=>{an.extract=bo();an.pack=Lo()});var Mo=y((Wc,No)=>{var gl=rn(),vl=nn(),tr;try{tr=require("fs")}catch{}var ct=function(){},yl=/^v?\.0/.test(process.version),rr=function(e){return typeof e=="function"},bl=function(e){return!yl||!tr?!1:(e instanceof(tr.ReadStream||ct)||e instanceof(tr.WriteStream||ct))&&rr(e.close)},wl=function(e){return e.setHeader&&rr(e.abort)},Sl=function(e,t,r,n){n=gl(n);var i=!1;e.on("close",function(){i=!0}),vl(e,{readable:t,writable:r},function(s){if(s)return n(s);i=!0,n()});var o=!1;return function(s){if(!i&&!o){if(o=!0,bl(e))return e.close(ct);if(wl(e))return e.abort();if(rr(e.destroy))return e.destroy();n(s||new Error("stream was destroyed"))}}},Do=function(e){e()},_l=function(e,t){return e.pipe(t)},Cl=function(){var e=Array.prototype.slice.call(arguments),t=rr(e[e.length-1]||ct)&&e.pop()||ct;if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new Error("pump requires two streams per minimum");var r,n=e.map(function(i,o){var s=o<e.length-1,a=o>0;return Sl(i,s,a,function(u){r||(r=u),u&&n.forEach(Do),!s&&(n.forEach(Do),t(r))})});return e.reduce(_l)};No.exports=Cl});var Uo=y((Hc,Bo)=>{var nr=require("path"),Fo=require("fs"),jo=parseInt("0777",8);Bo.exports=ze.mkdirp=ze.mkdirP=ze;function ze(e,t,r,n){typeof t=="function"?(r=t,t={}):(!t||typeof t!="object")&&(t={mode:t});var i=t.mode,o=t.fs||Fo;i===void 0&&(i=jo&~process.umask()),n||(n=null);var s=r||function(){};e=nr.resolve(e),o.mkdir(e,i,function(a){if(!a)return n=n||e,s(null,n);switch(a.code){case"ENOENT":ze(nr.dirname(e),t,function(u,c){u?s(u,c):ze(e,t,s,c)});break;default:o.stat(e,function(u,c){u||!c.isDirectory()?s(a,n):s(null,n)});break}})}ze.sync=function e(t,r,n){(!r||typeof r!="object")&&(r={mode:r});var i=r.mode,o=r.fs||Fo;i===void 0&&(i=jo&~process.umask()),n||(n=null),t=nr.resolve(t);try{o.mkdirSync(t,i),n=n||t}catch(a){switch(a.code){case"ENOENT":n=e(nr.dirname(t),r,n),e(t,r,n);break;default:var s;try{s=o.statSync(t)}catch{throw a}if(!s.isDirectory())throw a;break}}return n}});var Yo=y(ln=>{var El=no(),Wo=qo(),Ho=Mo(),Tl=Uo(),Vo=require("fs"),G=require("path"),kl=require("os"),dt=kl.platform()==="win32",ht=function(){},fn=function(e){return e},un=dt?function(e){return e.replace(/\\/g,"/").replace(/[:?<>|]/g,"_")}:fn,Pl=function(e,t,r,n,i,o){var s=i||["."];return function(u){if(!s.length)return u();var c=s.shift(),l=G.join(r,c);t(l,function(f,d){if(f)return u(f);if(!d.isDirectory())return u(null,c,d);e.readdir(l,function(h,v){if(h)return u(h);o&&v.sort();for(var b=0;b<v.length;b++)n(G.join(r,c,v[b]))||s.push(G.join(c,v[b]));u(null,c,d)})})}},zo=function(e,t){return function(r){r.name=r.name.split("/").slice(t).join("/");var n=r.linkname;return n&&(r.type==="link"||G.isAbsolute(n))&&(r.linkname=n.split("/").slice(t).join("/")),e(r)}};ln.pack=function(e,t){e||(e="."),t||(t={});var r=t.fs||Vo,n=t.ignore||t.filter||ht,i=t.map||ht,o=t.mapStream||fn,s=Pl(r,t.dereference?r.stat:r.lstat,e,n,t.entries,t.sort),a=t.strict!==!1,u=typeof t.umask=="number"?~t.umask:~$o(),c=typeof t.dmode=="number"?t.dmode:0,l=typeof t.fmode=="number"?t.fmode:0,f=t.pack||Wo.pack(),d=t.finish||ht;t.strip&&(i=zo(i,t.strip)),t.readable&&(c|=parseInt(555,8),l|=parseInt(444,8)),t.writable&&(c|=parseInt(333,8),l|=parseInt(222,8));var h=function(S,p){r.readlink(G.join(e,S),function(w,m){if(w)return f.destroy(w);p.linkname=un(m),f.entry(p,b)})},v=function(S,p,w){if(S)return f.destroy(S);if(!p)return t.finalize!==!1&&f.finalize(),d(f);if(w.isSocket())return b();var m={name:un(p),mode:(w.mode|(w.isDirectory()?c:l))&u,mtime:w.mtime,size:w.size,type:"file",uid:w.uid,gid:w.gid};if(w.isDirectory())return m.size=0,m.type="directory",m=i(m)||m,f.entry(m,b);if(w.isSymbolicLink())return m.size=0,m.type="symlink",m=i(m)||m,h(p,m);if(m=i(m)||m,!w.isFile())return a?f.destroy(new Error("unsupported type for "+p)):b();var C=f.entry(m,b);if(C){var Z=o(r.createReadStream(G.join(e,p)),m);Z.on("error",function(te){C.destroy(te)}),Ho(Z,C)}},b=function(S){if(S)return f.destroy(S);s(v)};return b(),f};var Rl=function(e){return e.length?e[e.length-1]:null},Ol=function(){return process.getuid?process.getuid():-1},$o=function(){return process.umask?process.umask():0};ln.extract=function(e,t){e||(e="."),t||(t={});var r=t.fs||Vo,n=t.ignore||t.filter||ht,i=t.map||ht,o=t.mapStream||fn,s=t.chown!==!1&&!dt&&Ol()===0,a=t.extract||Wo.extract(),u=[],c=new Date,l=typeof t.umask=="number"?~t.umask:~$o(),f=typeof t.dmode=="number"?t.dmode:0,d=typeof t.fmode=="number"?t.fmode:0,h=t.strict!==!1;t.strip&&(i=zo(i,t.strip)),t.readable&&(f|=parseInt(555,8),d|=parseInt(444,8)),t.writable&&(f|=parseInt(333,8),d|=parseInt(222,8));var v=function(p,w){for(var m;(m=Rl(u))&&p.slice(0,m[0].length)!==m[0];)u.pop();if(!m)return w();r.utimes(m[0],c,m[1],w)},b=function(p,w,m){if(t.utimes===!1)return m();if(w.type==="directory")return r.utimes(p,c,w.mtime,m);if(w.type==="symlink")return v(p,m);r.utimes(p,c,w.mtime,function(C){if(C)return m(C);v(p,m)})},S=function(p,w,m){var C=w.type==="symlink",Z=C?r.lchmod:r.chmod,te=C?r.lchown:r.chown;if(!Z)return m();var Ye=(w.mode|(w.type==="directory"?f:d))&l;Z(p,Ye,function(de){if(de)return m(de);if(!s||!te)return m();te(p,w.uid,w.gid,m)})};return a.on("entry",function(p,w,m){p=i(p)||p,p.name=un(p.name);var C=G.join(e,G.join("/",p.name));if(n(C,p))return w.resume(),m();var Z=function(H){if(H)return m(H);b(C,p,function(X){if(X)return m(X);if(dt)return m();S(C,p,m)})},te=function(){if(dt)return m();r.unlink(C,function(){r.symlink(p.linkname,C,Z)})},Ye=function(){if(dt)return m();r.unlink(C,function(){var H=G.join(e,G.join("/",p.linkname));r.link(H,C,function(X){if(X&&X.code==="EPERM"&&t.hardlinkAsFilesFallback)return w=r.createReadStream(H),de();Z(X)})})},de=function(){var H=r.createWriteStream(C),X=o(w,p);H.on("error",function(Ce){X.destroy(Ce)}),Ho(X,H,function(Ce){if(Ce)return m(Ce);H.on("close",Z)})};if(p.type==="directory")return u.push([C,p.mtime]),Go(C,{fs:r,own:s,uid:p.uid,gid:p.gid},Z);var Je=G.dirname(C);Ko(r,Je,G.join(e,"."),function(H,X){if(H)return m(H);if(!X)return m(new Error(Je+" is not a valid path"));Go(Je,{fs:r,own:s,uid:p.uid,gid:p.gid},function(Ce){if(Ce)return m(Ce);switch(p.type){case"file":return de();case"link":return Ye();case"symlink":return te()}if(h)return m(new Error("unsupported type for "+C+" ("+p.type+")"));w.resume(),m()})})}),t.finish&&a.on("finish",t.finish),a};function Ko(e,t,r,n){if(t===r)return n(null,!0);e.lstat(t,function(i,o){if(i&&i.code!=="ENOENT")return n(i);if(i||o.isDirectory())return Ko(e,G.join(t,".."),r,n);n(null,!1)})}function Go(e,t,r){Tl(e,{fs:t.fs},function(n,i){!n&&i&&t.own?El(i,t.uid,t.gid,r):r(n)})}});var j=y((zc,pt)=>{var Jo=[],Al=Jo.forEach,xl=Jo.slice;pt.exports.extend=function(e){return Al.call(xl.call(arguments,1),function(t){if(t)for(var r in t)e[r]=t[r]}),e};pt.exports.processArgs=function(e,t,r){return!t&&typeof e=="function"&&(t=e,e=null),{callback:t,opts:pt.exports.extend({},r,e)}};pt.exports.parseRepositoryTag=function(e){var t,r=e.indexOf("@"),n=e.lastIndexOf(":");if(r>=0)t=r;else if(n>=0)t=n;else return{repository:e};var i=e.slice(t+1);return i.indexOf("/")===-1?{repository:e.slice(0,t),tag:i}:{repository:e}}});var dn=y(($c,Zo)=>{var cn=j(),mt=function(e,t){this.modem=e,this.id=t};mt.prototype[require("util").inspect.custom]=function(){return this};mt.prototype.start=function(e,t){var r=this,n=cn.processArgs(e,t),i={path:"/exec/"+this.id+"/start",method:"POST",abortSignal:n.opts.abortSignal,isStream:!0,allowEmpty:!0,hijack:n.opts.hijack,openStdin:n.opts.stdin,statusCodes:{200:!0,204:!0,404:"no such exec",409:"container stopped/paused",500:"container not running"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,s)})};mt.prototype.resize=function(e,t){var r=this,n=cn.processArgs(e,t),i={path:"/exec/"+this.id+"/resize?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such exec",500:"container not running"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,s)})};mt.prototype.inspect=function(e,t){var r=this,n=cn.processArgs(e,t),i={path:"/exec/"+this.id+"/json",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such exec",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return t(o,s);n.callback(o,s)})};Zo.exports=mt});var es=y((Yc,Qo)=>{var Kc=j().extend,Xo=dn(),O=j(),P=function(e,t){this.modem=e,this.id=t,this.defaultOptions={top:{},start:{},commit:{},stop:{},pause:{},unpause:{},restart:{},resize:{},attach:{},remove:{},copy:{},kill:{},exec:{},rename:{},log:{},stats:{},getArchive:{},infoArchive:{},putArchive:{},update:{},wait:{}}};P.prototype[require("util").inspect.custom]=function(){return this};P.prototype.inspect=function(e,t){var r=this,n=O.processArgs(e,t),i={path:"/containers/"+this.id+"/json?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such container",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.rename=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.rename),i={path:"/containers/"+this.id+"/rename?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.update=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.update),i={path:"/containers/"+this.id+"/update",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,400:"bad parameter",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.top=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.top),i={path:"/containers/"+this.id+"/top?",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.changes=function(e,t){var r=this,n=O.processArgs(e,t),i={path:"/containers/"+this.id+"/changes",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such container",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.listCheckpoint=function(e,t){var r=this,n=O.processArgs(e,t),i={path:"/containers/"+this.id+"/checkpoints?",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.deleteCheckpoint=function(e,t,r){var n=this,i=O.processArgs(t,r),o={path:"/containers/"+this.id+"/checkpoints/"+e+"?",method:"DELETE",abortSignal:i.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:i.opts};if(i.callback===void 0)return new this.modem.Promise(function(s,a){n.modem.dial(o,function(u,c){if(u)return a(u);s(c)})});this.modem.dial(o,function(s,a){i.callback(s,a)})};P.prototype.createCheckpoint=function(e,t){var r=this,n=O.processArgs(e,t),i={path:"/containers/"+this.id+"/checkpoints",method:"POST",abortSignal:n.opts.abortSignal,allowEmpty:!0,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.export=function(e,t){var r=this,n=O.processArgs(e,t),i={path:"/containers/"+this.id+"/export",method:"GET",abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,404:"no such container",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.start=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.start),i={path:"/containers/"+this.id+"/start?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,304:"container already started",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.pause=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.pause),i={path:"/containers/"+this.id+"/pause",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.unpause=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.unpause),i={path:"/containers/"+this.id+"/unpause",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.exec=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.exec),i={path:"/containers/"+this.id+"/exec",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"no such container",409:"container stopped/paused",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(new Xo(r.modem,u.Id))})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,new Xo(r.modem,s.Id))})};P.prototype.commit=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.commit);n.opts.container=this.id;var i={path:"/commit?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.stop=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.stop),i={path:"/containers/"+this.id+"/stop?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,304:"container already stopped",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.restart=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.restart),i={path:"/containers/"+this.id+"/restart?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.kill=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.kill),i={path:"/containers/"+this.id+"/kill?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.resize=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.resize),i={path:"/containers/"+this.id+"/resize?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.attach=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.attach),i={path:"/containers/"+this.id+"/attach?",method:"POST",abortSignal:n.opts.abortSignal,isStream:!0,hijack:n.opts.hijack,openStdin:n.opts.stdin,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.wait=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.wait),i={path:"/containers/"+this.id+"/wait?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.remove=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.remove),i={path:"/containers/"+this.id+"?",method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,400:"bad parameter",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.copy=function(e,t){var r=this;console.log("container.copy is deprecated since Docker v1.8.x");var n=O.processArgs(e,t,this.defaultOptions.copy),i={path:"/containers/"+this.id+"/copy",method:"POST",abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.getArchive=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.getArchive),i={path:"/containers/"+this.id+"/archive?",method:"GET",abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,400:"client error, bad parameters",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.infoArchive=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.infoArchive),i={path:"/containers/"+this.id+"/archive?",method:"HEAD",abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,400:"client error, bad parameters",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.putArchive=function(e,t,r){var n=this,i=O.processArgs(t,r,this.defaultOptions.putArchive),o={path:"/containers/"+this.id+"/archive?",method:"PUT",file:e,abortSignal:i.opts.abortSignal,isStream:!0,statusCodes:{200:!0,400:"client error, bad parameters",403:"client error, permission denied",404:"no such container",500:"server error"},options:i.opts};if(i.callback===void 0)return new this.modem.Promise(function(s,a){n.modem.dial(o,function(u,c){if(u)return a(u);s(c)})});this.modem.dial(o,function(s,a){i.callback(s,a)})};P.prototype.logs=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.log),i={path:"/containers/"+this.id+"/logs?",method:"GET",abortSignal:n.opts.abortSignal,isStream:n.opts.follow||!1,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};P.prototype.stats=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.stats),i=!0;n.opts.stream===!1&&(i=!1);var o={path:"/containers/"+this.id+"/stats?",method:"GET",abortSignal:n.opts.abortSignal,isStream:i,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(s,a){r.modem.dial(o,function(u,c){if(u)return a(u);s(c)})});this.modem.dial(o,function(s,a){n.callback(s,a)})};Qo.exports=P});var rs=y((Jc,ts)=>{var hn=j(),ce=function(e,t){this.modem=e,this.name=t};ce.prototype[require("util").inspect.custom]=function(){return this};ce.prototype.inspect=function(e){var t=this,r={path:"/images/"+this.name+"/json",method:"GET",statusCodes:{200:!0,404:"no such image",500:"server error"}};if(e===void 0)return new this.modem.Promise(function(n,i){t.modem.dial(r,function(o,s){if(o)return i(o);n(s)})});this.modem.dial(r,function(n,i){if(n)return e(n,i);e(n,i)})};ce.prototype.distribution=function(e,t){var r=hn.processArgs(e,t),n=this,i={path:"/distribution/"+this.name+"/json",method:"GET",statusCodes:{200:!0,401:"no such image",500:"server error"},authconfig:r.opts?r.opts.authconfig:void 0};if(r.callback===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return r.callback(o,s);r.callback(o,s)})};ce.prototype.history=function(e){var t=this,r={path:"/images/"+this.name+"/history",method:"GET",statusCodes:{200:!0,404:"no such image",500:"server error"}};if(e===void 0)return new this.modem.Promise(function(n,i){t.modem.dial(r,function(o,s){if(o)return i(o);n(s)})});this.modem.dial(r,function(n,i){if(n)return e(n,i);e(n,i)})};ce.prototype.get=function(e){var t=this,r={path:"/images/"+this.name+"/get",method:"GET",isStream:!0,statusCodes:{200:!0,500:"server error"}};if(e===void 0)return new this.modem.Promise(function(n,i){t.modem.dial(r,function(o,s){if(o)return i(o);n(s)})});this.modem.dial(r,function(n,i){if(n)return e(n,i);e(n,i)})};ce.prototype.push=function(e,t,r){var n=this,i=hn.processArgs(e,t),o=!0;i.opts.stream===!1&&(o=!1);var s={path:"/images/"+this.name+"/push?",method:"POST",options:i.opts,authconfig:i.opts.authconfig||r,abortSignal:i.opts.abortSignal,isStream:o,statusCodes:{200:!0,404:"no such image",500:"server error"}};if(delete s.options.authconfig,t===void 0)return new this.modem.Promise(function(a,u){n.modem.dial(s,function(c,l){if(c)return u(c);a(l)})});this.modem.dial(s,function(a,u){t(a,u)})};ce.prototype.tag=function(e,t){var r=this,n={path:"/images/"+this.name+"/tag?",method:"POST",options:e,abortSignal:e&&e.abortSignal,statusCodes:{200:!0,201:!0,400:"bad parameter",404:"no such image",409:"conflict",500:"server error"}};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};ce.prototype.remove=function(e,t){var r=this,n=hn.processArgs(e,t),i={path:"/images/"+this.name+"?",method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such image",409:"conflict",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ts.exports=ce});var os=y((Zc,is)=>{var ns=j(),ir=function(e,t){this.modem=e,this.name=t};ir.prototype[require("util").inspect.custom]=function(){return this};ir.prototype.inspect=function(e,t){var r=this,n=ns.processArgs(e,t),i={path:"/volumes/"+this.name,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such volume",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ir.prototype.remove=function(e,t){var r=this,n=ns.processArgs(e,t),i={path:"/volumes/"+this.name,method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{204:!0,404:"no such volume",409:"conflict",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};is.exports=ir});var as=y((Xc,ss)=>{var or=j(),$e=function(e,t){this.modem=e,this.id=t};$e.prototype[require("util").inspect.custom]=function(){return this};$e.prototype.inspect=function(i,t){var r=this,n=or.processArgs(i,t),i={path:"/networks/"+this.id+"?",method:"GET",statusCodes:{200:!0,404:"no such network",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};$e.prototype.remove=function(e,t){var r=this,n=or.processArgs(e,t),i={path:"/networks/"+this.id,method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such network",409:"conflict",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};$e.prototype.connect=function(e,t){var r=this,n=or.processArgs(e,t),i={path:"/networks/"+this.id+"/connect",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"network or container is not found",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};$e.prototype.disconnect=function(e,t){var r=this,n=or.processArgs(e,t),i={path:"/networks/"+this.id+"/disconnect",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"network or container is not found",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ss.exports=$e});var fs=y((Qc,us)=>{var pn=j(),Ke=function(e,t){this.modem=e,this.id=t};Ke.prototype[require("util").inspect.custom]=function(){return this};Ke.prototype.inspect=function(e,t){var r=this,n=pn.processArgs(e,t),i={path:"/services/"+this.id,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such service",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Ke.prototype.remove=function(e,t){var r=this,n=pn.processArgs(e,t),i={path:"/services/"+this.id,method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such service",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Ke.prototype.update=function(e,t,r){var n=this;if(!r){var i=typeof t;i==="function"?(r=t,t=e,e=t.authconfig||void 0):i==="undefined"&&(t=e,e=t.authconfig||void 0)}var o={path:"/services/"+this.id+"/update?",method:"POST",abortSignal:t&&t.abortSignal,statusCodes:{200:!0,404:"no such service",500:"server error"},authconfig:e,options:t};if(r===void 0)return new this.modem.Promise(function(s,a){n.modem.dial(o,function(u,c){if(u)return a(u);s(c)})});this.modem.dial(o,function(s,a){r(s,a)})};Ke.prototype.logs=function(e,t){var r=this,n=pn.processArgs(e,t,{}),i={path:"/services/"+this.id+"/logs?",method:"GET",abortSignal:n.opts.abortSignal,isStream:n.opts.follow||!1,statusCodes:{200:!0,404:"no such service",500:"server error",503:"node is not part of a swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};us.exports=Ke});var cs=y((ed,ls)=>{var Se=j(),ee=function(e,t,r){this.modem=e,this.name=t,this.remote=r||t};ee.prototype[require("util").inspect.custom]=function(){return this};ee.prototype.inspect=function(e,t){var r=this,n=Se.processArgs(e,t),i={path:"/plugins/"+this.name,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"plugin is not installed",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ee.prototype.remove=function(e,t){var r=this,n=Se.processArgs(e,t),i={path:"/plugins/"+this.name+"?",method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"plugin is not installed",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,s)})};ee.prototype.privileges=function(e,t){var r=this,n=Se.processArgs(e,t),i={path:"/plugins/privileges?",method:"GET",options:{remote:this.remote},abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ee.prototype.pull=function(e,t){var r=this,n=Se.processArgs(e,t);n.opts._query&&!n.opts._query.name&&(n.opts._query.name=this.name),n.opts._query&&!n.opts._query.remote&&(n.opts._query.remote=this.remote);var i={path:"/plugins/pull?",method:"POST",abortSignal:n.opts.abortSignal,isStream:!0,options:n.opts,statusCodes:{200:!0,204:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ee.prototype.enable=function(e,t){var r=this,n=Se.processArgs(e,t),i={path:"/plugins/"+this.name+"/enable?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ee.prototype.disable=function(e,t){var r=this,n=Se.processArgs(e,t),i={path:"/plugins/"+this.name+"/disable",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ee.prototype.push=function(e,t){var r=this,n=Se.processArgs(e,t),i={path:"/plugins/"+this.name+"/push",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"plugin not installed",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ee.prototype.configure=function(e,t){var r=this,n=Se.processArgs(e,t),i={path:"/plugins/"+this.name+"/set",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"plugin not installed",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ee.prototype.upgrade=function(e,t,r){var n=this;!r&&typeof t=="function"&&(r=t,t=e,e=t.authconfig||void 0);var i={path:"/plugins/"+this.name+"/upgrade?",method:"POST",abortSignal:t&&t.abortSignal,statusCodes:{200:!0,204:!0,404:"plugin not installed",500:"server error"},authconfig:e,options:t};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){r(o,s)})};ls.exports=ee});var ps=y((td,hs)=>{var ds=j(),gt=function(e,t){this.modem=e,this.id=t};gt.prototype[require("util").inspect.custom]=function(){return this};gt.prototype.inspect=function(e,t){var r=this,n=ds.processArgs(e,t),i={path:"/secrets/"+this.id,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"secret not found",406:"node is not part of a swarm",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};gt.prototype.update=function(e,t){var r=this;!t&&typeof e=="function"&&(t=e);var n={path:"/secrets/"+this.id+"/update?",method:"POST",abortSignal:e&&e.abortSignal,statusCodes:{200:!0,404:"secret not found",500:"server error"},options:e};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};gt.prototype.remove=function(e,t){var r=this,n=ds.processArgs(e,t),i={path:"/secrets/"+this.id,method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"secret not found",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};hs.exports=gt});var gs=y((rd,ms)=>{var mn=j(),vt=function(e,t){this.modem=e,this.id=t};vt.prototype[require("util").inspect.custom]=function(){return this};vt.prototype.inspect=function(e,t){var r=this,n=mn.processArgs(e,t),i={path:"/configs/"+this.id,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"config not found",500:"server error",503:"node is not part of a swarm"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};vt.prototype.update=function(e,t){var r=this,n=mn.processArgs(e,t),i={path:"/configs/"+this.id+"/update?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"config not found",500:"server error",503:"node is not part of a swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};vt.prototype.remove=function(e,t){var r=this,n=mn.processArgs(e,t),i={path:"/configs/"+this.id,method:"DELETE",abortSignal:e.abortSignal,statusCodes:{200:!0,204:!0,404:"config not found",500:"server error",503:"node is not part of a swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ms.exports=vt});var bs=y((nd,ys)=>{var vs=j(),sr=function(e,t){this.modem=e,this.id=t,this.defaultOptions={log:{}}};sr.prototype[require("util").inspect.custom]=function(){return this};sr.prototype.inspect=function(e,t){var r=this,n=vs.processArgs(e,t),i={path:"/tasks/"+this.id,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"unknown task",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};sr.prototype.logs=function(e,t){var r=this,n=vs.processArgs(e,t,this.defaultOptions.log),i={path:"/tasks/"+this.id+"/logs?",method:"GET",abortSignal:n.opts.abortSignal,isStream:n.opts.follow||!1,statusCodes:{101:!0,200:!0,404:"no such container",500:"server error",503:"node is not part of a swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ys.exports=sr});var _s=y((id,Ss)=>{var ws=j(),yt=function(e,t){this.modem=e,this.id=t};yt.prototype[require("util").inspect.custom]=function(){return this};yt.prototype.inspect=function(e,t){var r=this,n=ws.processArgs(e,t),i={path:"/nodes/"+this.id,method:"GET",abortSignal:n.abortSignal,statusCodes:{200:!0,404:"no such node",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};yt.prototype.update=function(e,t){var r=this;!t&&typeof e=="function"&&(t=e);var n={path:"/nodes/"+this.id+"/update?",method:"POST",abortSignal:e&&e.abortSignal,statusCodes:{200:!0,404:"no such node",406:"node is not part of a swarm",500:"server error"},options:e};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};yt.prototype.remove=function(e,t){var r=this,n=ws.processArgs(e,t),i={path:"/nodes/"+this.id+"?",method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such node",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Ss.exports=yt});var Ds=y((od,qs)=>{var Il=require("events").EventEmitter,Ll=Ji(),ql=Yo(),Dl=require("zlib"),Cs=es(),Es=rs(),Ts=os(),ks=as(),Ps=fs(),Rs=cs(),Os=ps(),Nl=gs(),As=bs(),xs=_s(),Is=dn(),T=j(),Ls=T.extend,g=function(e){if(!(this instanceof g))return new g(e);var t=global.Promise;e&&e.Promise&&(t=e.Promise,Object.keys(e).length===1&&(e=void 0)),this.modem=new Ll(e),this.modem.Promise=t};g.prototype.createContainer=function(e,t){var r=this,n={path:"/containers/create?",method:"POST",options:e,authconfig:e.authconfig,abortSignal:e.abortSignal,statusCodes:{200:!0,201:!0,404:"no such container",406:"impossible to attach",500:"server error"}};if(delete e.authconfig,t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(r.getContainer(a.Id))})});this.modem.dial(n,function(i,o){if(i)return t(i,o);t(i,r.getContainer(o.Id))})};g.prototype.createImage=function(e,t,r){var n=this;!r&&typeof t=="function"?(r=t,t=e,e=t.authconfig||void 0):!r&&!t&&(t=e,e=t.authconfig);var i={path:"/images/create?",method:"POST",options:t,authconfig:e,abortSignal:t.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){r(o,s)})};g.prototype.loadImage=function(e,t,r){var n=this;!r&&typeof t=="function"&&(r=t,t=null);var i={path:"/images/load?",method:"POST",options:t,file:e,abortSignal:t&&t.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){r(o,s)})};g.prototype.importImage=function(e,t,r){var n=this;!r&&typeof t=="function"&&(r=t,t=void 0),t||(t={}),t.fromSrc="-";var i={path:"/images/create?",method:"POST",options:t,file:e,abortSignal:t.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){r(o,s)})};g.prototype.checkAuth=function(e,t){var r=this,n={path:"/auth",method:"POST",options:e,abortSignal:e.abortSignal,statusCodes:{200:!0,204:!0,500:"server error"}};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};g.prototype.buildImage=function(e,t,r){var n=this;!r&&typeof t=="function"&&(r=t,t=null);function i(s){var a={path:"/build?",method:"POST",file:s,options:t,abortSignal:t&&t.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(t&&(t.registryconfig&&(a.registryconfig=a.options.registryconfig,delete a.options.registryconfig),t.authconfig&&(a.authconfig=a.options.authconfig,delete a.options.authconfig)),r===void 0)return new n.modem.Promise(function(u,c){n.modem.dial(a,function(l,f){if(l)return c(l);u(f)})});n.modem.dial(a,function(u,c){r(u,c)})}if(e&&e.context){var o=ql.pack(e.context,{entries:e.src});return i(o.pipe(Dl.createGzip()))}else return i(e)};g.prototype.getContainer=function(e){return new Cs(this.modem,e)};g.prototype.getImage=function(e){return new Es(this.modem,e)};g.prototype.getVolume=function(e){return new Ts(this.modem,e)};g.prototype.getPlugin=function(e,t){return new Rs(this.modem,e,t)};g.prototype.getService=function(e){return new Ps(this.modem,e)};g.prototype.getTask=function(e){return new As(this.modem,e)};g.prototype.getNode=function(e){return new xs(this.modem,e)};g.prototype.getNetwork=function(e){return new ks(this.modem,e)};g.prototype.getSecret=function(e){return new Os(this.modem,e)};g.prototype.getConfig=function(e){return new Nl(this.modem,e)};g.prototype.getExec=function(e){return new Is(this.modem,e)};g.prototype.listContainers=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/containers/json?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.listImages=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/images/json?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.getImages=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/images/get?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.listServices=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/services?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.listNodes=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/nodes?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",404:"no such node",500:"server error",503:"node is not part of a swarm"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.listTasks=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/tasks?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.createSecret=function(e,t){var r=T.processArgs(e,t),n=this,i={path:"/secrets/create?",method:"POST",options:r.opts,abortSignal:r.opts.abortSignal,statusCodes:{200:!0,201:!0,406:"server error or node is not part of a swarm",409:"name conflicts with an existing object",500:"server error"}};if(r.callback===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(n.getSecret(u.ID))})});this.modem.dial(i,function(o,s){if(o)return r.callback(o,s);r.callback(o,n.getSecret(s.ID))})};g.prototype.createConfig=function(e,t){var r=T.processArgs(e,t),n=this,i={path:"/configs/create?",method:"POST",options:r.opts,abortSignal:r.opts.abortSignal,statusCodes:{200:!0,201:!0,406:"server error or node is not part of a swarm",409:"name conflicts with an existing object",500:"server error"}};if(r.callback===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(n.getConfig(u.ID))})});this.modem.dial(i,function(o,s){if(o)return r.callback(o,s);r.callback(o,n.getConfig(s.ID))})};g.prototype.listSecrets=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/secrets?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.listConfigs=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/configs?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.createPlugin=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/plugins/create?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(r.getPlugin(n.opts.name))})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,r.getPlugin(n.opts.name))})};g.prototype.listPlugins=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/plugins?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.pruneImages=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/images/prune?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.pruneBuilder=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/build/prune",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.pruneContainers=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/containers/prune?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.pruneVolumes=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/volumes/prune?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.pruneNetworks=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/networks/prune?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.createVolume=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/volumes/create?",method:"POST",allowEmpty:!0,options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(r.getVolume(u.Name))})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,r.getVolume(s.Name))})};g.prototype.createService=function(e,t,r){!r&&typeof t=="function"?(r=t,t=e,e=t.authconfig||void 0):!t&&!r&&(t=e);var n=this,i={path:"/services/create",method:"POST",options:t,authconfig:e,abortSignal:t&&t.abortSignal,statusCodes:{200:!0,201:!0,500:"server error"}};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(n.getService(u.ID||u.Id))})});this.modem.dial(i,function(o,s){if(o)return r(o,s);r(o,n.getService(s.ID||s.Id))})};g.prototype.listVolumes=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/volumes?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.createNetwork=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/networks/create?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"driver not found",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(r.getNetwork(u.Id))})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,r.getNetwork(s.Id))})};g.prototype.listNetworks=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/networks?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.searchImages=function(e,t){var r=this,n={path:"/images/search?",method:"GET",options:e,authconfig:e.authconfig,abortSignal:e.abortSignal,statusCodes:{200:!0,500:"server error"}};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};g.prototype.info=function(i,t){var r=this,n=T.processArgs(i,t),i={path:"/info",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.version=function(i,t){var r=this,n=T.processArgs(i,t),i={path:"/version",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.ping=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/_ping",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.df=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/system/df",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.getEvents=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/events?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.pull=function(e,t,r,n){var i=T.processArgs(t,r),o=T.parseRepositoryTag(e);i.opts.fromImage=o.repository,i.opts.tag=o.tag||"latest";var s=[i.opts,i.callback];return n&&(s=[n,i.opts,i.callback]),this.createImage.apply(this,s)};g.prototype.run=function(e,t,r,n,i,o){return typeof arguments[arguments.length-1]=="function"?this.runCallback(e,t,r,n,i,o):this.runPromise(e,t,r,n,i)};g.prototype.runCallback=function(e,t,r,n,i,o){!o&&typeof n=="function"?(o=n,n={},i={}):!o&&typeof i=="function"&&(o=i,i={});var s=new Il;function a(c,l){if(c)return o(c,null,l);s.emit("container",l),l.attach({stream:!0,stdout:!0,stderr:!0},function(d,h){if(d)return o(d,null,l);s.emit("stream",h),r&&(r instanceof Array?(h.on("end",function(){try{r[0].end()}catch{}try{r[1].end()}catch{}}),l.modem.demuxStream(h,r[0],r[1])):(h.setEncoding("utf8"),h.pipe(r,{end:!0}))),l.start(i,function(v,b){if(v)return o(v,b,l);s.emit("start",l),l.wait(function(S,p){s.emit("data",p),o(S,p,l)})})})}var u={Hostname:"",User:"",AttachStdin:!1,AttachStdout:!0,AttachStderr:!0,Tty:!0,OpenStdin:!1,StdinOnce:!1,Env:null,Cmd:t,Image:e,Volumes:{},VolumesFrom:[]};return Ls(u,n),this.createContainer(u,a),s};g.prototype.runPromise=function(e,t,r,n,i){var o=this;n=n||{},i=i||{};var s={Hostname:"",User:"",AttachStdin:!1,AttachStdout:!0,AttachStderr:!0,Tty:!0,OpenStdin:!1,StdinOnce:!1,Env:null,Cmd:t,Image:e,Volumes:{},VolumesFrom:[]};Ls(s,n);var a;return new this.modem.Promise(function(u,c){o.createContainer(s).then(function(l){return a=l,l.attach({stream:!0,stdout:!0,stderr:!0})}).then(function(l){return r&&(r instanceof Array?(l.on("end",function(){try{r[0].end()}catch{}try{r[1].end()}catch{}}),a.modem.demuxStream(l,r[0],r[1])):(l.setEncoding("utf8"),l.pipe(r,{end:!0}))),a.start(i)}).then(function(l){return a.wait()}).then(function(l){u([l,a])}).catch(function(l){c(l)})})};g.prototype.swarmInit=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/swarm/init",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",406:"node is already part of a Swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.swarmJoin=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/swarm/join",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",406:"node is already part of a Swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.swarmLeave=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/swarm/leave?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,406:"node is not part of a Swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.swarmUpdate=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/swarm/update?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",406:"node is already part of a Swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.swarmInspect=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/swarm",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,406:"This node is not a swarm manager",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.Container=Cs;g.Image=Es;g.Volume=Ts;g.Network=ks;g.Service=Ps;g.Plugin=Rs;g.Secret=Os;g.Task=As;g.Node=xs;g.Exec=Is;qs.exports=g});var Ul={};Vs(Ul,{default:()=>js});module.exports=$s(Ul);var F=require("@raycast/api");var x=require("@raycast/api");var E=require("@raycast/api");var bn=e=>"```\n"+e+"\n```";var V=e=>e.State==="running",z=({Names:e,Name:t})=>e!==void 0?e.map(r=>r.replace(/^\//,"")).join(", "):t!==void 0?t.replace(/^\//,""):"-",wn=e=>e!==void 0?`# ${z(e)}

`+Ys(e.Config.Env)+`
`:"",Ys=e=>e.length===0?"":[`

## Environment`,bn(e.join(`
`))].join(`
`);var lr=e=>e.errno===-61&&e.code==="ECONNREFUSED",re=(e,t)=>e.message.includes("container already stopped")?`Container ${z(t)} already stopped`:e.message.includes("container already started")?`Container ${z(t)} already started`:e.message.includes("cannot remove a running container")?["Cannot remove running container",`Please stop ${z(t)} first`]:e.message;var Le=require("@raycast/api"),B=({action:e,onSuccess:t,onFailure:r})=>async()=>{try{await e(),t!==void 0&&await(0,Le.showToast)(Le.Toast.Style.Success,...Sn(t()))}catch(n){n instanceof Error&&r!==void 0&&await(0,Le.showToast)(Le.Toast.Style.Failure,...Sn(r(n)))}},Sn=e=>Array.isArray(e)?e:[e];var L=require("react/jsx-runtime");function cr({docker:e,containerId:t}){let{isLoading:r,containerInfo:n,startContainer:i,restartContainer:o,stopContainer:s,removeContainer:a}=e.useContainerInfo(t),{pop:u}=(0,E.useNavigation)();return(0,L.jsx)(E.Detail,{isLoading:r,markdown:wn(n),metadata:n&&(0,L.jsxs)(E.Detail.Metadata,{children:[(0,L.jsx)(E.Detail.Metadata.Label,{title:"Image",text:n?.Config.Image}),(0,L.jsx)(E.Detail.Metadata.TagList,{title:"Status",children:(0,L.jsx)(E.Detail.Metadata.TagList.Item,{text:n.State.Status,color:n.State.Running?E.Color.Green:E.Color.Yellow})}),(0,L.jsx)(E.Detail.Metadata.TagList,{title:"Command",children:(0,L.jsx)(E.Detail.Metadata.TagList.Item,{text:n.Config.Cmd?.join(" ")})}),n.Config.ExposedPorts&&(0,L.jsx)(E.Detail.Metadata.TagList,{title:"Ports",children:Object.keys(n.Config.ExposedPorts).map((c,l)=>(0,L.jsx)(E.Detail.Metadata.TagList.Item,{text:c,color:E.Color.PrimaryText},l))}),n.NetworkSettings.Ports&&(0,L.jsx)(E.Detail.Metadata.TagList,{title:"Host Ports",children:Object.keys(n.NetworkSettings.Ports).map((c,l)=>(0,L.jsx)(E.Detail.Metadata.TagList.Item,{text:n.NetworkSettings.Ports[c]?.map(f=>f.HostPort).join(", ")||"None",color:E.Color.PrimaryText},l))}),(0,L.jsx)(E.Detail.Metadata.Separator,{}),n.Created&&(0,L.jsx)(E.Detail.Metadata.Label,{title:"Created at",text:new Date(n.Created).toLocaleString()})]}),actions:(0,L.jsxs)(E.ActionPanel,{children:[n?.State.Running===!0&&(0,L.jsx)(E.Action,{title:"Stop Container",shortcut:{modifiers:["cmd","shift"],key:"w"},onAction:B({action:()=>s(n),onSuccess:()=>`Container ${z(n)} stopped`,onFailure:c=>re(c,n)})}),n?.State.Running===!0&&(0,L.jsx)(E.Action,{title:"Restart Container",icon:E.Icon.ArrowClockwise,shortcut:{modifiers:["opt"],key:"r"},onAction:B({action:()=>o(n),onSuccess:()=>`Container ${z(n)} restarted`,onFailure:c=>re(c,n)})}),n?.State.Running===!1&&(0,L.jsx)(E.Action,{title:"Start Container",shortcut:{modifiers:["cmd","shift"],key:"r"},onAction:B({action:()=>i(n),onSuccess:()=>`Container ${z(n)} started`,onFailure:c=>re(c,n)})}),n!==void 0&&(0,L.jsx)(E.Action,{title:"Remove Container",icon:E.Icon.Trash,style:E.Action.Style.Destructive,shortcut:E.Keyboard.Shortcut.Common.Remove,onAction:B({action:async()=>{await a(n),u()},onSuccess:()=>`Container ${z(n)} removed`,onFailure:c=>re(c,n)})})]})},t)}var R=require("react");var St=e=>e.reduce((t,r)=>{let n=r.Labels["com.docker.compose.project"],i=r.Labels["com.docker.compose.config_files"],o=r.Labels["com.docker.compose.working_dir"];if(n===void 0)return t;let s=t.find(({name:a})=>a===n);return s!==void 0?(s.containers=[...s.containers,r],t):[...t,{name:n,configFiles:i,workingDir:o,containers:[r]}]},[]);var Ze=1e3,_t=e=>{let t=({Id:f})=>e.getContainer(f).stop(),r=({Id:f})=>e.getContainer(f).start(),n=({Id:f})=>e.getContainer(f).restart(),i=({Id:f})=>e.getContainer(f).remove();return{useImages:()=>{let[f,d]=(0,R.useState)(),[h,v]=(0,R.useState)(!1),[b,S]=(0,R.useState)(),p=(0,R.useRef)();return(0,R.useEffect)(()=>{async function w(){try{let m=await e.listImages();d(m)}catch(m){m instanceof Error&&S(m)}}return N(v,w)(),p.current=setInterval(w,Ze),()=>p.current&&clearInterval(p.current)},[]),{images:f,error:b,isLoading:h,removeImage:N(v,({Id:w})=>e.getImage(w).remove())}},useImageInfo:({Id:f})=>{let[d,h]=(0,R.useState)(),[v,b]=(0,R.useState)(!1),S=(0,R.useRef)();return(0,R.useEffect)(()=>{async function p(){let w=await e.getImage(f).inspect();h(w)}return N(b,p)(),S.current=setInterval(p,Ze),()=>S.current&&clearInterval(S.current)},[f]),{imageInfo:d,isLoading:v}},useContainers:()=>{let[f,d]=(0,R.useState)(),[h,v]=(0,R.useState)(!1),[b,S]=(0,R.useState)(),p=(0,R.useRef)();return(0,R.useEffect)(()=>{async function w(){try{let m=await e.listContainers({all:!0});d(m)}catch(m){m instanceof Error&&S(m)}}return N(v,w)(),p.current=setInterval(w,Ze),()=>p.current&&clearInterval(p.current)},[]),{containers:f,isLoading:h,error:b,startContainer:N(v,r),stopContainer:N(v,t),restartContainer:N(v,n),removeContainer:N(v,i)}},useContainerInfo:f=>{let[d,h]=(0,R.useState)(),[v,b]=(0,R.useState)(!1),S=(0,R.useRef)();return(0,R.useEffect)(()=>{async function p(){let w=await e.getContainer(f).inspect();h(w)}return N(b,p)(),S.current=setInterval(p,Ze),()=>S.current&&clearInterval(S.current)},[f]),{containerInfo:d,isLoading:v,startContainer:N(b,r),restartContainer:N(b,n),stopContainer:N(b,t),removeContainer:N(b,i)}},useProjects:()=>{let[f,d]=(0,R.useState)(),[h,v]=(0,R.useState)(!1),[b,S]=(0,R.useState)(),p=(0,R.useRef)();return(0,R.useEffect)(()=>{async function w(){try{let m=await e.listContainers({all:!0});d(St(m))}catch(m){m instanceof Error&&S(m)}}return N(v,w)(),p.current=setInterval(w,Ze),()=>p.current&&clearInterval(p.current)},[]),{projects:f,isLoading:h,error:b,startProject:N(v,async w=>{await Promise.all(w.containers.filter(C=>!V(C)).map(C=>e.getContainer(C.Id).start()));let m=await e.listContainers({all:!0});d(St(m))}),stopProject:N(v,async w=>{await Promise.all(w.containers.filter(C=>V(C)).map(C=>e.getContainer(C.Id).stop()));let m=await e.listContainers({all:!0});d(St(m))})}},useCreateContainer:()=>{let[f,d]=(0,R.useState)(!1),[h,v]=(0,R.useState)();return{createContainer:async S=>{d(!0);try{await(await e.createContainer(S)).start()}catch(p){p instanceof Error&&v(p)}finally{d(!1)}},isLoading:f,error:h}}}};function N(e,t){return async r=>{e(!0);try{let n=await t(r);return e(!1),n}finally{e(!1)}}}var Ns=require("@raycast/api"),gn=zs(Ds()),Ms=require("react"),Fs=require("node:url"),ar=()=>{let{socketPath:e}=(0,Ns.getPreferenceValues)(),t=["http://","https://","tcp://"];return(0,Ms.useMemo)(()=>{if(t.some(r=>e?.startsWith(r))){let r=new Fs.URL(e);return new gn.default({host:r.hostname,port:r.port||2375})}return new gn.default(e?{socketPath:e}:void 0)},[e])};var _e=require("@raycast/api"),fr=require("react");var ur=require("react/jsx-runtime");function bt({error:e}){let[t,r]=(0,fr.useState)([]);(0,fr.useEffect)(()=>{async function i(){let o=await(0,_e.getApplications)();r(o)}i()},[]);let n=t.find(({bundleId:i})=>i==="com.docker.docker");return(0,ur.jsx)(_e.Detail,{markdown:Ml(e),actions:lr(e)&&n!==void 0?(0,ur.jsx)(_e.ActionPanel,{children:(0,ur.jsx)(_e.Action.Open,{title:"Launch Docker",target:n.path})}):null})}var Ml=e=>{let t="Error message:\n\n```\n"+e.message+"\n```";return lr(e)?["## \u26A0\uFE0F Error connecting to Docker",t].join(`
`):`## An Error Occurred:

${t}`};var W=require("react/jsx-runtime");function vn(e){let t=ar(),r=_t(t),{useContainers:n}=r,{containers:i,isLoading:o,error:s,startContainer:a,restartContainer:u,stopContainer:c,removeContainer:l}=n();return s?(0,W.jsx)(bt,{error:s}):(0,W.jsx)(x.List,{isLoading:o,children:Fl(i,e.projectFilter)?.map(f=>{let d=z(f);return(0,W.jsx)(x.List.Item,{keywords:[f.Id,d,f.Image],title:d,subtitle:f.Image,accessories:[{text:{value:f.State}}],icon:V(f)?{source:"icon-container-running.png",tintColor:x.Color.Green}:{source:"icon-container.png",tintColor:x.Color.SecondaryText},actions:(0,W.jsxs)(x.ActionPanel,{children:[V(f)&&(0,W.jsx)(x.Action,{title:"Stop Container",shortcut:{modifiers:["cmd","shift"],key:"w"},icon:{source:"icon-stop.png",tintColor:x.Color.PrimaryText},onAction:B({action:()=>c(f),onSuccess:()=>`Container ${d} stopped`,onFailure:h=>re(h,f)})}),V(f)&&(0,W.jsx)(x.Action,{title:"Restart Container",icon:x.Icon.ArrowClockwise,shortcut:{modifiers:["opt"],key:"r"},onAction:B({action:()=>u(f),onSuccess:()=>`Container ${d} restarted`,onFailure:h=>re(h,f)})}),V(f)&&(0,W.jsx)(x.Action.CopyToClipboard,{title:"Copy Container Id",shortcut:{modifiers:["cmd","shift"],key:"c"},content:f.Id}),!V(f)&&(0,W.jsx)(x.Action,{title:"Start Container",shortcut:{modifiers:["cmd","shift"],key:"r"},icon:{source:"icon-start.png",tintColor:x.Color.PrimaryText},onAction:B({action:()=>a(f),onSuccess:()=>`Container ${d} started`,onFailure:h=>re(h,f)})}),(0,W.jsx)(x.Action.Push,{title:"Inspect",icon:{source:x.Icon.Binoculars},shortcut:{modifiers:["cmd"],key:"i"},target:(0,W.jsx)(cr,{docker:r,containerId:f.Id})}),(0,W.jsx)(x.Action,{title:"Remove Container",icon:x.Icon.Trash,style:x.Action.Style.Destructive,shortcut:x.Keyboard.Shortcut.Common.Remove,onAction:B({action:()=>l(f),onSuccess:()=>`Container ${d} removed`,onFailure:h=>re(h,f)})})]})},f.Id)})})}var Fl=(e,t)=>t===void 0||e===void 0?e:e.filter(r=>r.Labels["com.docker.compose.project"]===t);var se=require("react/jsx-runtime");function js(){let e=ar(),{useProjects:t}=_t(e),{projects:r,isLoading:n,error:i,startProject:o,stopProject:s}=t();return i?(0,se.jsx)(bt,{error:i}):(0,se.jsx)(F.List,{isLoading:n,children:r?.map(a=>(0,se.jsx)(F.List.Item,{icon:{source:"icon-compose.png",tintColor:Bl(a)?F.Color.Green:F.Color.SecondaryText},title:a.name,subtitle:jl(a),actions:(0,se.jsxs)(F.ActionPanel,{children:[(0,se.jsx)(F.Action.Push,{title:"Show Containers",icon:{source:F.Icon.Binoculars},target:(0,se.jsx)(vn,{projectFilter:a.name})}),(0,se.jsx)(F.Action,{title:"Start All Containers",shortcut:{modifiers:["cmd","shift"],key:"r"},icon:{source:"icon-startall.png",tintColor:F.Color.PrimaryText},onAction:B({action:()=>o(a),onSuccess:()=>`Started ${a.name}`,onFailure:({message:u})=>u})}),(0,se.jsx)(F.Action,{title:"Stop All Containers",shortcut:{modifiers:["cmd","shift"],key:"w"},icon:{source:"icon-stopall.png",tintColor:F.Color.PrimaryText},onAction:B({action:()=>s(a),onSuccess:()=>`Stopped ${a.name}`,onFailure:({message:u})=>u})})]})},a.name))})}var jl=({containers:e})=>{let t=e.reduce((n,i)=>n+=V(i)?1:0,0),r=e.length-t;return`${t} Running, ${r} Stopped`},Bl=({containers:e})=>e.every(V);
/*! Bundled license information:

safe-buffer/index.js:
  (*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> *)
*/
