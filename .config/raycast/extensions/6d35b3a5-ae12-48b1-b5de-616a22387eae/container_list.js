"use strict";var Ms=Object.create;var vt=Object.defineProperty;var Fs=Object.getOwnPropertyDescriptor;var js=Object.getOwnPropertyNames;var Bs=Object.getPrototypeOf,Us=Object.prototype.hasOwnProperty;var y=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Gs=(e,t)=>{for(var r in t)vt(e,r,{get:t[r],enumerable:!0})},hn=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of js(t))!Us.call(e,i)&&i!==r&&vt(e,i,{get:()=>t[i],enumerable:!(n=Fs(t,i))||n.enumerable});return e};var Ws=(e,t,r)=>(r=e!=null?Ms(Bs(e)):{},hn(t||!e||!e.__esModule?vt(r,"default",{value:e,enumerable:!0}):r,e)),Hs=e=>hn(vt({},"__esModule",{value:!0}),e);var ur=y((Xl,ar)=>{var yn=[],$s=yn.forEach,Ks=yn.slice;ar.exports.extend=function(e){return $s.call(Ks.call(arguments,1),function(t){if(t)for(var r in t)e[r]=t[r]}),e};ar.exports.parseJSON=function(e){try{return JSON.parse(e)}catch{return null}}});var _n=y((Sn,wt)=>{var Ys=require("https"),Js=require("http"),fr=require("url"),bn=ur(),Ql=wt.exports.maxRedirects=5,wn={https:Ys,http:Js};for(bt in wn)oe=function(){},oe.prototype=wn[bt],oe=new oe,oe.request=function(e){return function(t,r,n){n=n||{};var i=typeof t=="object"&&"maxRedirects"in t?t.maxRedirects:Sn.maxRedirects,o=bn.extend({count:0,max:i,clientRequest:null,userCallback:r},n);if(o.count>o.max){var s=new Error("Max redirects exceeded. To allow more redirects, pass options.maxRedirects property.");return o.clientRequest.emit("error",s),o.clientRequest}o.count++;var a;typeof t=="string"?a=t:a=fr.format(bn.extend({protocol:bt},t));var u=Object.getPrototypeOf(e).request(t,c(a,o));o.clientRequest||(o.clientRequest=u);function c(l,f){return function(d){if(d.statusCode<300||d.statusCode>399||!("location"in d.headers))return f.userCallback(d);var h=fr.resolve(l,d.headers.location),v=fr.parse(h).protocol;return v=v.substr(0,v.length-1),wt.exports[v].get(h,c(l,f),f)}}return u}}(oe),oe.get=function(e){return function(t,r,n){var i=e.request(t,r,n);return i.end(),i}}(oe),wt.exports[bt]=oe;var oe,bt});var lr=y((ec,Cn)=>{Cn.exports=require("stream")});var On=y((tc,Pn)=>{"use strict";function En(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Tn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?En(Object(r),!0).forEach(function(n){Zs(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):En(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Zs(e,t,r){return t=Rn(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xs(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function kn(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Rn(n.key),n)}}function Qs(e,t,r){return t&&kn(e.prototype,t),r&&kn(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Rn(e){var t=ea(e,"string");return typeof t=="symbol"?t:String(t)}function ea(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ta=require("buffer"),St=ta.Buffer,ra=require("util"),cr=ra.inspect,na=cr&&cr.custom||"inspect";function ia(e,t,r){St.prototype.copy.call(e,t,r)}Pn.exports=function(){function e(){Xs(this,e),this.head=null,this.tail=null,this.length=0}return Qs(e,[{key:"push",value:function(r){var n={data:r,next:null};this.length>0?this.tail.next=n:this.head=n,this.tail=n,++this.length}},{key:"unshift",value:function(r){var n={data:r,next:this.head};this.length===0&&(this.tail=n),this.head=n,++this.length}},{key:"shift",value:function(){if(this.length!==0){var r=this.head.data;return this.length===1?this.head=this.tail=null:this.head=this.head.next,--this.length,r}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(r){if(this.length===0)return"";for(var n=this.head,i=""+n.data;n=n.next;)i+=r+n.data;return i}},{key:"concat",value:function(r){if(this.length===0)return St.alloc(0);for(var n=St.allocUnsafe(r>>>0),i=this.head,o=0;i;)ia(i.data,n,o),o+=i.data.length,i=i.next;return n}},{key:"consume",value:function(r,n){var i;return r<this.head.data.length?(i=this.head.data.slice(0,r),this.head.data=this.head.data.slice(r)):r===this.head.data.length?i=this.shift():i=n?this._getString(r):this._getBuffer(r),i}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(r){var n=this.head,i=1,o=n.data;for(r-=o.length;n=n.next;){var s=n.data,a=r>s.length?s.length:r;if(a===s.length?o+=s:o+=s.slice(0,r),r-=a,r===0){a===s.length?(++i,n.next?this.head=n.next:this.head=this.tail=null):(this.head=n,n.data=s.slice(a));break}++i}return this.length-=i,o}},{key:"_getBuffer",value:function(r){var n=St.allocUnsafe(r),i=this.head,o=1;for(i.data.copy(n),r-=i.data.length;i=i.next;){var s=i.data,a=r>s.length?s.length:r;if(s.copy(n,n.length-r,0,a),r-=a,r===0){a===s.length?(++o,i.next?this.head=i.next:this.head=this.tail=null):(this.head=i,i.data=s.slice(a));break}++o}return this.length-=o,n}},{key:na,value:function(r,n){return cr(this,Tn(Tn({},n),{},{depth:0,customInspect:!1}))}}]),e}()});var hr=y((rc,xn)=>{"use strict";function oa(e,t){var r=this,n=this._readableState&&this._readableState.destroyed,i=this._writableState&&this._writableState.destroyed;return n||i?(t?t(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,process.nextTick(dr,this,e)):process.nextTick(dr,this,e)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,function(o){!t&&o?r._writableState?r._writableState.errorEmitted?process.nextTick(_t,r):(r._writableState.errorEmitted=!0,process.nextTick(An,r,o)):process.nextTick(An,r,o):t?(process.nextTick(_t,r),t(o)):process.nextTick(_t,r)}),this)}function An(e,t){dr(e,t),_t(e)}function _t(e){e._writableState&&!e._writableState.emitClose||e._readableState&&!e._readableState.emitClose||e.emit("close")}function sa(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function dr(e,t){e.emit("error",t)}function aa(e,t){var r=e._readableState,n=e._writableState;r&&r.autoDestroy||n&&n.autoDestroy?e.destroy(t):e.emit("error",t)}xn.exports={destroy:oa,undestroy:sa,errorOrDestroy:aa}});var ce=y((nc,qn)=>{"use strict";var Ln={};function H(e,t,r){r||(r=Error);function n(o,s,a){return typeof t=="string"?t:t(o,s,a)}class i extends r{constructor(s,a,u){super(n(s,a,u))}}i.prototype.name=r.name,i.prototype.code=e,Ln[e]=i}function In(e,t){if(Array.isArray(e)){let r=e.length;return e=e.map(n=>String(n)),r>2?`one of ${t} ${e.slice(0,r-1).join(", ")}, or `+e[r-1]:r===2?`one of ${t} ${e[0]} or ${e[1]}`:`of ${t} ${e[0]}`}else return`of ${t} ${String(e)}`}function ua(e,t,r){return e.substr(!r||r<0?0:+r,t.length)===t}function fa(e,t,r){return(r===void 0||r>e.length)&&(r=e.length),e.substring(r-t.length,r)===t}function la(e,t,r){return typeof r!="number"&&(r=0),r+t.length>e.length?!1:e.indexOf(t,r)!==-1}H("ERR_INVALID_OPT_VALUE",function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'},TypeError);H("ERR_INVALID_ARG_TYPE",function(e,t,r){let n;typeof t=="string"&&ua(t,"not ")?(n="must not be",t=t.replace(/^not /,"")):n="must be";let i;if(fa(e," argument"))i=`The ${e} ${n} ${In(t,"type")}`;else{let o=la(e,".")?"property":"argument";i=`The "${e}" ${o} ${n} ${In(t,"type")}`}return i+=`. Received type ${typeof r}`,i},TypeError);H("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF");H("ERR_METHOD_NOT_IMPLEMENTED",function(e){return"The "+e+" method is not implemented"});H("ERR_STREAM_PREMATURE_CLOSE","Premature close");H("ERR_STREAM_DESTROYED",function(e){return"Cannot call "+e+" after a stream was destroyed"});H("ERR_MULTIPLE_CALLBACK","Callback called multiple times");H("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable");H("ERR_STREAM_WRITE_AFTER_END","write after end");H("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError);H("ERR_UNKNOWN_ENCODING",function(e){return"Unknown encoding: "+e},TypeError);H("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event");qn.exports.codes=Ln});var pr=y((ic,Nn)=>{"use strict";var ca=ce().codes.ERR_INVALID_OPT_VALUE;function da(e,t,r){return e.highWaterMark!=null?e.highWaterMark:t?e[r]:null}function ha(e,t,r,n){var i=da(t,n,r);if(i!=null){if(!(isFinite(i)&&Math.floor(i)===i)||i<0){var o=n?r:"highWaterMark";throw new ca(o,i)}return Math.floor(i)}return e.objectMode?16:16*1024}Nn.exports={getHighWaterMark:ha}});var Dn=y((oc,mr)=>{typeof Object.create=="function"?mr.exports=function(t,r){r&&(t.super_=r,t.prototype=Object.create(r.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:mr.exports=function(t,r){if(r){t.super_=r;var n=function(){};n.prototype=r.prototype,t.prototype=new n,t.prototype.constructor=t}}});var de=y((sc,vr)=>{try{if(gr=require("util"),typeof gr.inherits!="function")throw"";vr.exports=gr.inherits}catch{vr.exports=Dn()}var gr});var Fn=y((ac,Mn)=>{Mn.exports=require("util").deprecate});var wr=y((uc,Hn)=>{"use strict";Hn.exports=I;function Bn(e){var t=this;this.next=null,this.entry=null,this.finish=function(){Ba(t,e)}}var Ie;I.WritableState=Ze;var pa={deprecate:Fn()},Un=lr(),Et=require("buffer").Buffer,ma=(typeof global<"u"?global:typeof window<"u"?window:typeof self<"u"?self:{}).Uint8Array||function(){};function ga(e){return Et.from(e)}function va(e){return Et.isBuffer(e)||e instanceof ma}var br=hr(),ya=pr(),ba=ya.getHighWaterMark,he=ce().codes,wa=he.ERR_INVALID_ARG_TYPE,Sa=he.ERR_METHOD_NOT_IMPLEMENTED,_a=he.ERR_MULTIPLE_CALLBACK,Ca=he.ERR_STREAM_CANNOT_PIPE,Ea=he.ERR_STREAM_DESTROYED,Ta=he.ERR_STREAM_NULL_VALUES,ka=he.ERR_STREAM_WRITE_AFTER_END,Ra=he.ERR_UNKNOWN_ENCODING,Le=br.errorOrDestroy;de()(I,Un);function Pa(){}function Ze(e,t,r){Ie=Ie||_e(),e=e||{},typeof r!="boolean"&&(r=t instanceof Ie),this.objectMode=!!e.objectMode,r&&(this.objectMode=this.objectMode||!!e.writableObjectMode),this.highWaterMark=ba(this,e,"writableHighWaterMark",r),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var n=e.decodeStrings===!1;this.decodeStrings=!n,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(i){Na(t,i)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=e.emitClose!==!1,this.autoDestroy=!!e.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new Bn(this)}Ze.prototype.getBuffer=function(){for(var t=this.bufferedRequest,r=[];t;)r.push(t),t=t.next;return r};(function(){try{Object.defineProperty(Ze.prototype,"buffer",{get:pa.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch{}})();var Ct;typeof Symbol=="function"&&Symbol.hasInstance&&typeof Function.prototype[Symbol.hasInstance]=="function"?(Ct=Function.prototype[Symbol.hasInstance],Object.defineProperty(I,Symbol.hasInstance,{value:function(t){return Ct.call(this,t)?!0:this!==I?!1:t&&t._writableState instanceof Ze}})):Ct=function(t){return t instanceof this};function I(e){Ie=Ie||_e();var t=this instanceof Ie;if(!t&&!Ct.call(I,this))return new I(e);this._writableState=new Ze(e,this,t),this.writable=!0,e&&(typeof e.write=="function"&&(this._write=e.write),typeof e.writev=="function"&&(this._writev=e.writev),typeof e.destroy=="function"&&(this._destroy=e.destroy),typeof e.final=="function"&&(this._final=e.final)),Un.call(this)}I.prototype.pipe=function(){Le(this,new Ca)};function Oa(e,t){var r=new ka;Le(e,r),process.nextTick(t,r)}function Aa(e,t,r,n){var i;return r===null?i=new Ta:typeof r!="string"&&!t.objectMode&&(i=new wa("chunk",["string","Buffer"],r)),i?(Le(e,i),process.nextTick(n,i),!1):!0}I.prototype.write=function(e,t,r){var n=this._writableState,i=!1,o=!n.objectMode&&va(e);return o&&!Et.isBuffer(e)&&(e=ga(e)),typeof t=="function"&&(r=t,t=null),o?t="buffer":t||(t=n.defaultEncoding),typeof r!="function"&&(r=Pa),n.ending?Oa(this,r):(o||Aa(this,n,e,r))&&(n.pendingcb++,i=Ia(this,n,o,e,t,r)),i};I.prototype.cork=function(){this._writableState.corked++};I.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,!e.writing&&!e.corked&&!e.bufferProcessing&&e.bufferedRequest&&Gn(this,e))};I.prototype.setDefaultEncoding=function(t){if(typeof t=="string"&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new Ra(t);return this._writableState.defaultEncoding=t,this};Object.defineProperty(I.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}});function xa(e,t,r){return!e.objectMode&&e.decodeStrings!==!1&&typeof t=="string"&&(t=Et.from(t,r)),t}Object.defineProperty(I.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}});function Ia(e,t,r,n,i,o){if(!r){var s=xa(t,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=t.objectMode?1:n.length;t.length+=a;var u=t.length<t.highWaterMark;if(u||(t.needDrain=!0),t.writing||t.corked){var c=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},c?c.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else yr(e,t,!1,a,n,i,o);return u}function yr(e,t,r,n,i,o,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new Ea("write")):r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function La(e,t,r,n,i){--t.pendingcb,r?(process.nextTick(i,n),process.nextTick(Je,e,t),e._writableState.errorEmitted=!0,Le(e,n)):(i(n),e._writableState.errorEmitted=!0,Le(e,n),Je(e,t))}function qa(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}function Na(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if(typeof i!="function")throw new _a;if(qa(r),t)La(e,r,n,t,i);else{var o=Wn(r)||e.destroyed;!o&&!r.corked&&!r.bufferProcessing&&r.bufferedRequest&&Gn(e,r),n?process.nextTick(jn,e,r,o,i):jn(e,r,o,i)}}function jn(e,t,r,n){r||Da(e,t),t.pendingcb--,n(),Je(e,t)}function Da(e,t){t.length===0&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}function Gn(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),o=t.corkedRequestsFree;o.entry=r;for(var s=0,a=!0;r;)i[s]=r,r.isBuf||(a=!1),r=r.next,s+=1;i.allBuffers=a,yr(e,t,!0,t.length,i,"",o.finish),t.pendingcb++,t.lastBufferedRequest=null,o.next?(t.corkedRequestsFree=o.next,o.next=null):t.corkedRequestsFree=new Bn(t),t.bufferedRequestCount=0}else{for(;r;){var u=r.chunk,c=r.encoding,l=r.callback,f=t.objectMode?1:u.length;if(yr(e,t,!1,f,u,c,l),r=r.next,t.bufferedRequestCount--,t.writing)break}r===null&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}I.prototype._write=function(e,t,r){r(new Sa("_write()"))};I.prototype._writev=null;I.prototype.end=function(e,t,r){var n=this._writableState;return typeof e=="function"?(r=e,e=null,t=null):typeof t=="function"&&(r=t,t=null),e!=null&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||ja(this,n,r),this};Object.defineProperty(I.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}});function Wn(e){return e.ending&&e.length===0&&e.bufferedRequest===null&&!e.finished&&!e.writing}function Ma(e,t){e._final(function(r){t.pendingcb--,r&&Le(e,r),t.prefinished=!0,e.emit("prefinish"),Je(e,t)})}function Fa(e,t){!t.prefinished&&!t.finalCalled&&(typeof e._final=="function"&&!t.destroyed?(t.pendingcb++,t.finalCalled=!0,process.nextTick(Ma,e,t)):(t.prefinished=!0,e.emit("prefinish")))}function Je(e,t){var r=Wn(t);if(r&&(Fa(e,t),t.pendingcb===0&&(t.finished=!0,e.emit("finish"),t.autoDestroy))){var n=e._readableState;(!n||n.autoDestroy&&n.endEmitted)&&e.destroy()}return r}function ja(e,t,r){t.ending=!0,Je(e,t),r&&(t.finished?process.nextTick(r):e.once("finish",r)),t.ended=!0,e.writable=!1}function Ba(e,t,r){var n=e.entry;for(e.entry=null;n;){var i=n.callback;t.pendingcb--,i(r),n=n.next}t.corkedRequestsFree.next=e}Object.defineProperty(I.prototype,"destroyed",{enumerable:!1,get:function(){return this._writableState===void 0?!1:this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}});I.prototype.destroy=br.destroy;I.prototype._undestroy=br.undestroy;I.prototype._destroy=function(e,t){t(e)}});var _e=y((fc,zn)=>{"use strict";var Ua=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};zn.exports=te;var Vn=Cr(),_r=wr();de()(te,Vn);for(Sr=Ua(_r.prototype),Tt=0;Tt<Sr.length;Tt++)kt=Sr[Tt],te.prototype[kt]||(te.prototype[kt]=_r.prototype[kt]);var Sr,kt,Tt;function te(e){if(!(this instanceof te))return new te(e);Vn.call(this,e),_r.call(this,e),this.allowHalfOpen=!0,e&&(e.readable===!1&&(this.readable=!1),e.writable===!1&&(this.writable=!1),e.allowHalfOpen===!1&&(this.allowHalfOpen=!1,this.once("end",Ga)))}Object.defineProperty(te.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}});Object.defineProperty(te.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}});Object.defineProperty(te.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}});function Ga(){this._writableState.ended||process.nextTick(Wa,this)}function Wa(e){e.end()}Object.defineProperty(te.prototype,"destroyed",{enumerable:!1,get:function(){return this._readableState===void 0||this._writableState===void 0?!1:this._readableState.destroyed&&this._writableState.destroyed},set:function(t){this._readableState===void 0||this._writableState===void 0||(this._readableState.destroyed=t,this._writableState.destroyed=t)}})});var Yn=y((Er,Kn)=>{var Rt=require("buffer"),re=Rt.Buffer;function $n(e,t){for(var r in e)t[r]=e[r]}re.from&&re.alloc&&re.allocUnsafe&&re.allocUnsafeSlow?Kn.exports=Rt:($n(Rt,Er),Er.Buffer=Ce);function Ce(e,t,r){return re(e,t,r)}Ce.prototype=Object.create(re.prototype);$n(re,Ce);Ce.from=function(e,t,r){if(typeof e=="number")throw new TypeError("Argument must not be a number");return re(e,t,r)};Ce.alloc=function(e,t,r){if(typeof e!="number")throw new TypeError("Argument must be a number");var n=re(e);return t!==void 0?typeof r=="string"?n.fill(t,r):n.fill(t):n.fill(0),n};Ce.allocUnsafe=function(e){if(typeof e!="number")throw new TypeError("Argument must be a number");return re(e)};Ce.allocUnsafeSlow=function(e){if(typeof e!="number")throw new TypeError("Argument must be a number");return Rt.SlowBuffer(e)}});var Rr=y(Zn=>{"use strict";var kr=Yn().Buffer,Jn=kr.isEncoding||function(e){switch(e=""+e,e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function Ha(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}function Va(e){var t=Ha(e);if(typeof t!="string"&&(kr.isEncoding===Jn||!Jn(e)))throw new Error("Unknown encoding: "+e);return t||e}Zn.StringDecoder=Xe;function Xe(e){this.encoding=Va(e);var t;switch(this.encoding){case"utf16le":this.text=Za,this.end=Xa,t=4;break;case"utf8":this.fillLast=Ka,t=4;break;case"base64":this.text=Qa,this.end=eu,t=3;break;default:this.write=tu,this.end=ru;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=kr.allocUnsafe(t)}Xe.prototype.write=function(e){if(e.length===0)return"";var t,r;if(this.lastNeed){if(t=this.fillLast(e),t===void 0)return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""};Xe.prototype.end=Ja;Xe.prototype.text=Ya;Xe.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length};function Tr(e){return e<=127?0:e>>5===6?2:e>>4===14?3:e>>3===30?4:e>>6===2?-1:-2}function za(e,t,r){var n=t.length-1;if(n<r)return 0;var i=Tr(t[n]);return i>=0?(i>0&&(e.lastNeed=i-1),i):--n<r||i===-2?0:(i=Tr(t[n]),i>=0?(i>0&&(e.lastNeed=i-2),i):--n<r||i===-2?0:(i=Tr(t[n]),i>=0?(i>0&&(i===2?i=0:e.lastNeed=i-3),i):0))}function $a(e,t,r){if((t[0]&192)!==128)return e.lastNeed=0,"\uFFFD";if(e.lastNeed>1&&t.length>1){if((t[1]&192)!==128)return e.lastNeed=1,"\uFFFD";if(e.lastNeed>2&&t.length>2&&(t[2]&192)!==128)return e.lastNeed=2,"\uFFFD"}}function Ka(e){var t=this.lastTotal-this.lastNeed,r=$a(this,e,t);if(r!==void 0)return r;if(this.lastNeed<=e.length)return e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,t,0,e.length),this.lastNeed-=e.length}function Ya(e,t){var r=za(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString("utf8",t,n)}function Ja(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"\uFFFD":t}function Za(e,t){if((e.length-t)%2===0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function Xa(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function Qa(e,t){var r=(e.length-t)%3;return r===0?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,r===1?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function eu(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function tu(e){return e.toString(this.encoding)}function ru(e){return e&&e.length?this.write(e):""}});var Pt=y((cc,ei)=>{"use strict";var Xn=ce().codes.ERR_STREAM_PREMATURE_CLOSE;function nu(e){var t=!1;return function(){if(!t){t=!0;for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];e.apply(this,n)}}}function iu(){}function ou(e){return e.setHeader&&typeof e.abort=="function"}function Qn(e,t,r){if(typeof t=="function")return Qn(e,null,t);t||(t={}),r=nu(r||iu);var n=t.readable||t.readable!==!1&&e.readable,i=t.writable||t.writable!==!1&&e.writable,o=function(){e.writable||a()},s=e._writableState&&e._writableState.finished,a=function(){i=!1,s=!0,n||r.call(e)},u=e._readableState&&e._readableState.endEmitted,c=function(){n=!1,u=!0,i||r.call(e)},l=function(v){r.call(e,v)},f=function(){var v;if(n&&!u)return(!e._readableState||!e._readableState.ended)&&(v=new Xn),r.call(e,v);if(i&&!s)return(!e._writableState||!e._writableState.ended)&&(v=new Xn),r.call(e,v)},d=function(){e.req.on("finish",a)};return ou(e)?(e.on("complete",a),e.on("abort",f),e.req?d():e.on("request",d)):i&&!e._writableState&&(e.on("end",o),e.on("close",o)),e.on("end",c),e.on("finish",a),t.error!==!1&&e.on("error",l),e.on("close",f),function(){e.removeListener("complete",a),e.removeListener("abort",f),e.removeListener("request",d),e.req&&e.req.removeListener("finish",a),e.removeListener("end",o),e.removeListener("close",o),e.removeListener("finish",a),e.removeListener("end",c),e.removeListener("error",l),e.removeListener("close",f)}}ei.exports=Qn});var ri=y((dc,ti)=>{"use strict";var Ot;function pe(e,t,r){return t=su(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function su(e){var t=au(e,"string");return typeof t=="symbol"?t:String(t)}function au(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var uu=Pt(),me=Symbol("lastResolve"),Ee=Symbol("lastReject"),Qe=Symbol("error"),At=Symbol("ended"),Te=Symbol("lastPromise"),Pr=Symbol("handlePromise"),ke=Symbol("stream");function ge(e,t){return{value:e,done:t}}function fu(e){var t=e[me];if(t!==null){var r=e[ke].read();r!==null&&(e[Te]=null,e[me]=null,e[Ee]=null,t(ge(r,!1)))}}function lu(e){process.nextTick(fu,e)}function cu(e,t){return function(r,n){e.then(function(){if(t[At]){r(ge(void 0,!0));return}t[Pr](r,n)},n)}}var du=Object.getPrototypeOf(function(){}),hu=Object.setPrototypeOf((Ot={get stream(){return this[ke]},next:function(){var t=this,r=this[Qe];if(r!==null)return Promise.reject(r);if(this[At])return Promise.resolve(ge(void 0,!0));if(this[ke].destroyed)return new Promise(function(s,a){process.nextTick(function(){t[Qe]?a(t[Qe]):s(ge(void 0,!0))})});var n=this[Te],i;if(n)i=new Promise(cu(n,this));else{var o=this[ke].read();if(o!==null)return Promise.resolve(ge(o,!1));i=new Promise(this[Pr])}return this[Te]=i,i}},pe(Ot,Symbol.asyncIterator,function(){return this}),pe(Ot,"return",function(){var t=this;return new Promise(function(r,n){t[ke].destroy(null,function(i){if(i){n(i);return}r(ge(void 0,!0))})})}),Ot),du),pu=function(t){var r,n=Object.create(hu,(r={},pe(r,ke,{value:t,writable:!0}),pe(r,me,{value:null,writable:!0}),pe(r,Ee,{value:null,writable:!0}),pe(r,Qe,{value:null,writable:!0}),pe(r,At,{value:t._readableState.endEmitted,writable:!0}),pe(r,Pr,{value:function(o,s){var a=n[ke].read();a?(n[Te]=null,n[me]=null,n[Ee]=null,o(ge(a,!1))):(n[me]=o,n[Ee]=s)},writable:!0}),r));return n[Te]=null,uu(t,function(i){if(i&&i.code!=="ERR_STREAM_PREMATURE_CLOSE"){var o=n[Ee];o!==null&&(n[Te]=null,n[me]=null,n[Ee]=null,o(i)),n[Qe]=i;return}var s=n[me];s!==null&&(n[Te]=null,n[me]=null,n[Ee]=null,s(ge(void 0,!0))),n[At]=!0}),t.on("readable",lu.bind(null,n)),n};ti.exports=pu});var si=y((hc,oi)=>{"use strict";function ni(e,t,r,n,i,o,s){try{var a=e[o](s),u=a.value}catch(c){r(c);return}a.done?t(u):Promise.resolve(u).then(n,i)}function mu(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){var o=e.apply(t,r);function s(u){ni(o,n,i,s,a,"next",u)}function a(u){ni(o,n,i,s,a,"throw",u)}s(void 0)})}}function ii(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function gu(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ii(Object(r),!0).forEach(function(n){vu(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ii(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vu(e,t,r){return t=yu(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yu(e){var t=bu(e,"string");return typeof t=="symbol"?t:String(t)}function bu(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var wu=ce().codes.ERR_INVALID_ARG_TYPE;function Su(e,t,r){var n;if(t&&typeof t.next=="function")n=t;else if(t&&t[Symbol.asyncIterator])n=t[Symbol.asyncIterator]();else if(t&&t[Symbol.iterator])n=t[Symbol.iterator]();else throw new wu("iterable",["Iterable"],t);var i=new e(gu({objectMode:!0},r)),o=!1;i._read=function(){o||(o=!0,s())};function s(){return a.apply(this,arguments)}function a(){return a=mu(function*(){try{var u=yield n.next(),c=u.value,l=u.done;l?i.push(null):i.push(yield c)?s():o=!1}catch(f){i.destroy(f)}}),a.apply(this,arguments)}return i}oi.exports=Su});var Cr=y((mc,gi)=>{"use strict";gi.exports=k;var qe;k.ReadableState=li;var pc=require("events").EventEmitter,fi=function(t,r){return t.listeners(r).length},tt=lr(),xt=require("buffer").Buffer,_u=(typeof global<"u"?global:typeof window<"u"?window:typeof self<"u"?self:{}).Uint8Array||function(){};function Cu(e){return xt.from(e)}function Eu(e){return xt.isBuffer(e)||e instanceof _u}var Or=require("util"),_;Or&&Or.debuglog?_=Or.debuglog("stream"):_=function(){};var Tu=On(),Dr=hr(),ku=pr(),Ru=ku.getHighWaterMark,It=ce().codes,Pu=It.ERR_INVALID_ARG_TYPE,Ou=It.ERR_STREAM_PUSH_AFTER_EOF,Au=It.ERR_METHOD_NOT_IMPLEMENTED,xu=It.ERR_STREAM_UNSHIFT_AFTER_END_EVENT,Ne,Ar,xr;de()(k,tt);var et=Dr.errorOrDestroy,Ir=["error","close","destroy","pause","resume"];function Iu(e,t,r){if(typeof e.prependListener=="function")return e.prependListener(t,r);!e._events||!e._events[t]?e.on(t,r):Array.isArray(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]}function li(e,t,r){qe=qe||_e(),e=e||{},typeof r!="boolean"&&(r=t instanceof qe),this.objectMode=!!e.objectMode,r&&(this.objectMode=this.objectMode||!!e.readableObjectMode),this.highWaterMark=Ru(this,e,"readableHighWaterMark",r),this.buffer=new Tu,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=e.emitClose!==!1,this.autoDestroy=!!e.autoDestroy,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(Ne||(Ne=Rr().StringDecoder),this.decoder=new Ne(e.encoding),this.encoding=e.encoding)}function k(e){if(qe=qe||_e(),!(this instanceof k))return new k(e);var t=this instanceof qe;this._readableState=new li(e,this,t),this.readable=!0,e&&(typeof e.read=="function"&&(this._read=e.read),typeof e.destroy=="function"&&(this._destroy=e.destroy)),tt.call(this)}Object.defineProperty(k.prototype,"destroyed",{enumerable:!1,get:function(){return this._readableState===void 0?!1:this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}});k.prototype.destroy=Dr.destroy;k.prototype._undestroy=Dr.undestroy;k.prototype._destroy=function(e,t){t(e)};k.prototype.push=function(e,t){var r=this._readableState,n;return r.objectMode?n=!0:typeof e=="string"&&(t=t||r.defaultEncoding,t!==r.encoding&&(e=xt.from(e,t),t=""),n=!0),ci(this,e,t,!1,n)};k.prototype.unshift=function(e){return ci(this,e,null,!0,!1)};function ci(e,t,r,n,i){_("readableAddChunk",t);var o=e._readableState;if(t===null)o.reading=!1,Nu(e,o);else{var s;if(i||(s=Lu(o,t)),s)et(e,s);else if(o.objectMode||t&&t.length>0)if(typeof t!="string"&&!o.objectMode&&Object.getPrototypeOf(t)!==xt.prototype&&(t=Cu(t)),n)o.endEmitted?et(e,new xu):Lr(e,o,t,!0);else if(o.ended)et(e,new Ou);else{if(o.destroyed)return!1;o.reading=!1,o.decoder&&!r?(t=o.decoder.write(t),o.objectMode||t.length!==0?Lr(e,o,t,!1):Nr(e,o)):Lr(e,o,t,!1)}else n||(o.reading=!1,Nr(e,o))}return!o.ended&&(o.length<o.highWaterMark||o.length===0)}function Lr(e,t,r,n){t.flowing&&t.length===0&&!t.sync?(t.awaitDrain=0,e.emit("data",r)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&Lt(e)),Nr(e,t)}function Lu(e,t){var r;return!Eu(t)&&typeof t!="string"&&t!==void 0&&!e.objectMode&&(r=new Pu("chunk",["string","Buffer","Uint8Array"],t)),r}k.prototype.isPaused=function(){return this._readableState.flowing===!1};k.prototype.setEncoding=function(e){Ne||(Ne=Rr().StringDecoder);var t=new Ne(e);this._readableState.decoder=t,this._readableState.encoding=this._readableState.decoder.encoding;for(var r=this._readableState.buffer.head,n="";r!==null;)n+=t.write(r.data),r=r.next;return this._readableState.buffer.clear(),n!==""&&this._readableState.buffer.push(n),this._readableState.length=n.length,this};var ai=1073741824;function qu(e){return e>=ai?e=ai:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}function ui(e,t){return e<=0||t.length===0&&t.ended?0:t.objectMode?1:e!==e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=qu(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}k.prototype.read=function(e){_("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(e!==0&&(t.emittedReadable=!1),e===0&&t.needReadable&&((t.highWaterMark!==0?t.length>=t.highWaterMark:t.length>0)||t.ended))return _("read: emitReadable",t.length,t.ended),t.length===0&&t.ended?qr(this):Lt(this),null;if(e=ui(e,t),e===0&&t.ended)return t.length===0&&qr(this),null;var n=t.needReadable;_("need readable",n),(t.length===0||t.length-e<t.highWaterMark)&&(n=!0,_("length less than watermark",n)),t.ended||t.reading?(n=!1,_("reading or ended",n)):n&&(_("do read"),t.reading=!0,t.sync=!0,t.length===0&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=ui(r,t)));var i;return e>0?i=pi(e,t):i=null,i===null?(t.needReadable=t.length<=t.highWaterMark,e=0):(t.length-=e,t.awaitDrain=0),t.length===0&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&qr(this)),i!==null&&this.emit("data",i),i};function Nu(e,t){if(_("onEofChunk"),!t.ended){if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,t.sync?Lt(e):(t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,di(e)))}}function Lt(e){var t=e._readableState;_("emitReadable",t.needReadable,t.emittedReadable),t.needReadable=!1,t.emittedReadable||(_("emitReadable",t.flowing),t.emittedReadable=!0,process.nextTick(di,e))}function di(e){var t=e._readableState;_("emitReadable_",t.destroyed,t.length,t.ended),!t.destroyed&&(t.length||t.ended)&&(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,Mr(e)}function Nr(e,t){t.readingMore||(t.readingMore=!0,process.nextTick(Du,e,t))}function Du(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&t.length===0);){var r=t.length;if(_("maybeReadMore read 0"),e.read(0),r===t.length)break}t.readingMore=!1}k.prototype._read=function(e){et(this,new Au("_read()"))};k.prototype.pipe=function(e,t){var r=this,n=this._readableState;switch(n.pipesCount){case 0:n.pipes=e;break;case 1:n.pipes=[n.pipes,e];break;default:n.pipes.push(e);break}n.pipesCount+=1,_("pipe count=%d opts=%j",n.pipesCount,t);var i=(!t||t.end!==!1)&&e!==process.stdout&&e!==process.stderr,o=i?a:b;n.endEmitted?process.nextTick(o):r.once("end",o),e.on("unpipe",s);function s(S,p){_("onunpipe"),S===r&&p&&p.hasUnpiped===!1&&(p.hasUnpiped=!0,l())}function a(){_("onend"),e.end()}var u=Mu(r);e.on("drain",u);var c=!1;function l(){_("cleanup"),e.removeListener("close",h),e.removeListener("finish",v),e.removeListener("drain",u),e.removeListener("error",d),e.removeListener("unpipe",s),r.removeListener("end",a),r.removeListener("end",b),r.removeListener("data",f),c=!0,n.awaitDrain&&(!e._writableState||e._writableState.needDrain)&&u()}r.on("data",f);function f(S){_("ondata");var p=e.write(S);_("dest.write",p),p===!1&&((n.pipesCount===1&&n.pipes===e||n.pipesCount>1&&mi(n.pipes,e)!==-1)&&!c&&(_("false write response, pause",n.awaitDrain),n.awaitDrain++),r.pause())}function d(S){_("onerror",S),b(),e.removeListener("error",d),fi(e,"error")===0&&et(e,S)}Iu(e,"error",d);function h(){e.removeListener("finish",v),b()}e.once("close",h);function v(){_("onfinish"),e.removeListener("close",h),b()}e.once("finish",v);function b(){_("unpipe"),r.unpipe(e)}return e.emit("pipe",r),n.flowing||(_("pipe resume"),r.resume()),e};function Mu(e){return function(){var r=e._readableState;_("pipeOnDrain",r.awaitDrain),r.awaitDrain&&r.awaitDrain--,r.awaitDrain===0&&fi(e,"data")&&(r.flowing=!0,Mr(e))}}k.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(t.pipesCount===0)return this;if(t.pipesCount===1)return e&&e!==t.pipes?this:(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r),this);if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}var s=mi(t.pipes,e);return s===-1?this:(t.pipes.splice(s,1),t.pipesCount-=1,t.pipesCount===1&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r),this)};k.prototype.on=function(e,t){var r=tt.prototype.on.call(this,e,t),n=this._readableState;return e==="data"?(n.readableListening=this.listenerCount("readable")>0,n.flowing!==!1&&this.resume()):e==="readable"&&!n.endEmitted&&!n.readableListening&&(n.readableListening=n.needReadable=!0,n.flowing=!1,n.emittedReadable=!1,_("on readable",n.length,n.reading),n.length?Lt(this):n.reading||process.nextTick(Fu,this)),r};k.prototype.addListener=k.prototype.on;k.prototype.removeListener=function(e,t){var r=tt.prototype.removeListener.call(this,e,t);return e==="readable"&&process.nextTick(hi,this),r};k.prototype.removeAllListeners=function(e){var t=tt.prototype.removeAllListeners.apply(this,arguments);return(e==="readable"||e===void 0)&&process.nextTick(hi,this),t};function hi(e){var t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!t.paused?t.flowing=!0:e.listenerCount("data")>0&&e.resume()}function Fu(e){_("readable nexttick read 0"),e.read(0)}k.prototype.resume=function(){var e=this._readableState;return e.flowing||(_("resume"),e.flowing=!e.readableListening,ju(this,e)),e.paused=!1,this};function ju(e,t){t.resumeScheduled||(t.resumeScheduled=!0,process.nextTick(Bu,e,t))}function Bu(e,t){_("resume",t.reading),t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),Mr(e),t.flowing&&!t.reading&&e.read(0)}k.prototype.pause=function(){return _("call pause flowing=%j",this._readableState.flowing),this._readableState.flowing!==!1&&(_("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this};function Mr(e){var t=e._readableState;for(_("flow",t.flowing);t.flowing&&e.read()!==null;);}k.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;e.on("end",function(){if(_("wrapped end"),r.decoder&&!r.ended){var s=r.decoder.end();s&&s.length&&t.push(s)}t.push(null)}),e.on("data",function(s){if(_("wrapped data"),r.decoder&&(s=r.decoder.write(s)),!(r.objectMode&&s==null)&&!(!r.objectMode&&(!s||!s.length))){var a=t.push(s);a||(n=!0,e.pause())}});for(var i in e)this[i]===void 0&&typeof e[i]=="function"&&(this[i]=function(a){return function(){return e[a].apply(e,arguments)}}(i));for(var o=0;o<Ir.length;o++)e.on(Ir[o],this.emit.bind(this,Ir[o]));return this._read=function(s){_("wrapped _read",s),n&&(n=!1,e.resume())},this};typeof Symbol=="function"&&(k.prototype[Symbol.asyncIterator]=function(){return Ar===void 0&&(Ar=ri()),Ar(this)});Object.defineProperty(k.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}});Object.defineProperty(k.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}});Object.defineProperty(k.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(t){this._readableState&&(this._readableState.flowing=t)}});k._fromList=pi;Object.defineProperty(k.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}});function pi(e,t){if(t.length===0)return null;var r;return t.objectMode?r=t.buffer.shift():!e||e>=t.length?(t.decoder?r=t.buffer.join(""):t.buffer.length===1?r=t.buffer.first():r=t.buffer.concat(t.length),t.buffer.clear()):r=t.buffer.consume(e,t.decoder),r}function qr(e){var t=e._readableState;_("endReadable",t.endEmitted),t.endEmitted||(t.ended=!0,process.nextTick(Uu,t,e))}function Uu(e,t){if(_("endReadableNT",e.endEmitted,e.length),!e.endEmitted&&e.length===0&&(e.endEmitted=!0,t.readable=!1,t.emit("end"),e.autoDestroy)){var r=t._writableState;(!r||r.autoDestroy&&r.finished)&&t.destroy()}}typeof Symbol=="function"&&(k.from=function(e,t){return xr===void 0&&(xr=si()),xr(k,e,t)});function mi(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}});var Fr=y((gc,yi)=>{"use strict";yi.exports=se;var qt=ce().codes,Gu=qt.ERR_METHOD_NOT_IMPLEMENTED,Wu=qt.ERR_MULTIPLE_CALLBACK,Hu=qt.ERR_TRANSFORM_ALREADY_TRANSFORMING,Vu=qt.ERR_TRANSFORM_WITH_LENGTH_0,Nt=_e();de()(se,Nt);function zu(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(n===null)return this.emit("error",new Wu);r.writechunk=null,r.writecb=null,t!=null&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function se(e){if(!(this instanceof se))return new se(e);Nt.call(this,e),this._transformState={afterTransform:zu.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&(typeof e.transform=="function"&&(this._transform=e.transform),typeof e.flush=="function"&&(this._flush=e.flush)),this.on("prefinish",$u)}function $u(){var e=this;typeof this._flush=="function"&&!this._readableState.destroyed?this._flush(function(t,r){vi(e,t,r)}):vi(this,null,null)}se.prototype.push=function(e,t){return this._transformState.needTransform=!1,Nt.prototype.push.call(this,e,t)};se.prototype._transform=function(e,t,r){r(new Gu("_transform()"))};se.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}};se.prototype._read=function(e){var t=this._transformState;t.writechunk!==null&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0};se.prototype._destroy=function(e,t){Nt.prototype._destroy.call(this,e,function(r){t(r)})};function vi(e,t,r){if(t)return e.emit("error",t);if(r!=null&&e.push(r),e._writableState.length)throw new Vu;if(e._transformState.transforming)throw new Hu;return e.push(null)}});var Si=y((vc,wi)=>{"use strict";wi.exports=rt;var bi=Fr();de()(rt,bi);function rt(e){if(!(this instanceof rt))return new rt(e);bi.call(this,e)}rt.prototype._transform=function(e,t,r){r(null,e)}});var ki=y((yc,Ti)=>{"use strict";var jr;function Ku(e){var t=!1;return function(){t||(t=!0,e.apply(void 0,arguments))}}var Ei=ce().codes,Yu=Ei.ERR_MISSING_ARGS,Ju=Ei.ERR_STREAM_DESTROYED;function _i(e){if(e)throw e}function Zu(e){return e.setHeader&&typeof e.abort=="function"}function Xu(e,t,r,n){n=Ku(n);var i=!1;e.on("close",function(){i=!0}),jr===void 0&&(jr=Pt()),jr(e,{readable:t,writable:r},function(s){if(s)return n(s);i=!0,n()});var o=!1;return function(s){if(!i&&!o){if(o=!0,Zu(e))return e.abort();if(typeof e.destroy=="function")return e.destroy();n(s||new Ju("pipe"))}}}function Ci(e){e()}function Qu(e,t){return e.pipe(t)}function ef(e){return!e.length||typeof e[e.length-1]!="function"?_i:e.pop()}function tf(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=ef(t);if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw new Yu("streams");var i,o=t.map(function(s,a){var u=a<t.length-1,c=a>0;return Xu(s,u,c,function(l){i||(i=l),l&&o.forEach(Ci),!u&&(o.forEach(Ci),n(i))})});return t.reduce(Qu)}Ti.exports=tf});var Re=y((V,it)=>{var nt=require("stream");process.env.READABLE_STREAM==="disable"&&nt?(it.exports=nt.Readable,Object.assign(it.exports,nt),it.exports.Stream=nt):(V=it.exports=Cr(),V.Stream=nt||V,V.Readable=V,V.Writable=wr(),V.Duplex=_e(),V.Transform=Fr(),V.PassThrough=Si(),V.finished=Pt(),V.pipeline=ki())});var Oi=y((bc,Pi)=>{Pi.exports=ae;var rf=require("util"),Ri=Re();rf.inherits(ae,Ri.Duplex);function ae(e,t,r){var n=this;if(!(n instanceof ae))return new ae(e,t,r);Ri.Duplex.call(n,r),n._output=null,n.connect(e,t)}ae.prototype.connect=function(e,t){var r=this;r.req=e,r._output=t,r.emit("response",t),t.on("data",function(n){r.push(n)||r._output.pause()}),t.on("end",function(){r.push(null)})};ae.prototype._read=function(e){this._output&&this._output.resume()};ae.prototype._write=function(e,t,r){this.req.write(e,t),r()};ae.prototype.end=function(e,t,r){return this._output.socket.destroy(),this.req.end(e,t,r)};ae.prototype.destroy=function(){this.req.destroy(),this._output.socket.destroy()}});var xi=y((wc,Ai)=>{var De=1e3,Me=De*60,Fe=Me*60,Pe=Fe*24,nf=Pe*7,of=Pe*365.25;Ai.exports=function(e,t){t=t||{};var r=typeof e;if(r==="string"&&e.length>0)return sf(e);if(r==="number"&&isFinite(e))return t.long?uf(e):af(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function sf(e){if(e=String(e),!(e.length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]),n=(t[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return r*of;case"weeks":case"week":case"w":return r*nf;case"days":case"day":case"d":return r*Pe;case"hours":case"hour":case"hrs":case"hr":case"h":return r*Fe;case"minutes":case"minute":case"mins":case"min":case"m":return r*Me;case"seconds":case"second":case"secs":case"sec":case"s":return r*De;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}function af(e){var t=Math.abs(e);return t>=Pe?Math.round(e/Pe)+"d":t>=Fe?Math.round(e/Fe)+"h":t>=Me?Math.round(e/Me)+"m":t>=De?Math.round(e/De)+"s":e+"ms"}function uf(e){var t=Math.abs(e);return t>=Pe?Dt(e,t,Pe,"day"):t>=Fe?Dt(e,t,Fe,"hour"):t>=Me?Dt(e,t,Me,"minute"):t>=De?Dt(e,t,De,"second"):e+" ms"}function Dt(e,t,r,n){var i=t>=r*1.5;return Math.round(e/r)+" "+n+(i?"s":"")}});var Br=y((Sc,Ii)=>{function ff(e){r.debug=r,r.default=r,r.coerce=u,r.disable=s,r.enable=i,r.enabled=a,r.humanize=xi(),r.destroy=c,Object.keys(e).forEach(l=>{r[l]=e[l]}),r.names=[],r.skips=[],r.formatters={};function t(l){let f=0;for(let d=0;d<l.length;d++)f=(f<<5)-f+l.charCodeAt(d),f|=0;return r.colors[Math.abs(f)%r.colors.length]}r.selectColor=t;function r(l){let f,d=null,h,v;function b(...S){if(!b.enabled)return;let p=b,w=Number(new Date),m=w-(f||w);p.diff=m,p.prev=f,p.curr=w,f=w,S[0]=r.coerce(S[0]),typeof S[0]!="string"&&S.unshift("%O");let C=0;S[0]=S[0].replace(/%([a-zA-Z%])/g,(X,$e)=>{if(X==="%%")return"%";C++;let le=r.formatters[$e];if(typeof le=="function"){let Ke=S[C];X=le.call(p,Ke),S.splice(C,1),C--}return X}),r.formatArgs.call(p,S),(p.log||r.log).apply(p,S)}return b.namespace=l,b.useColors=r.useColors(),b.color=r.selectColor(l),b.extend=n,b.destroy=r.destroy,Object.defineProperty(b,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(h!==r.namespaces&&(h=r.namespaces,v=r.enabled(l)),v),set:S=>{d=S}}),typeof r.init=="function"&&r.init(b),b}function n(l,f){let d=r(this.namespace+(typeof f>"u"?":":f)+l);return d.log=this.log,d}function i(l){r.save(l),r.namespaces=l,r.names=[],r.skips=[];let f=(typeof l=="string"?l:"").trim().replace(" ",",").split(",").filter(Boolean);for(let d of f)d[0]==="-"?r.skips.push(d.slice(1)):r.names.push(d)}function o(l,f){let d=0,h=0,v=-1,b=0;for(;d<l.length;)if(h<f.length&&(f[h]===l[d]||f[h]==="*"))f[h]==="*"?(v=h,b=d,h++):(d++,h++);else if(v!==-1)h=v+1,b++,d=b;else return!1;for(;h<f.length&&f[h]==="*";)h++;return h===f.length}function s(){let l=[...r.names,...r.skips.map(f=>"-"+f)].join(",");return r.enable(""),l}function a(l){for(let f of r.skips)if(o(l,f))return!1;for(let f of r.names)if(o(l,f))return!0;return!1}function u(l){return l instanceof Error?l.stack||l.message:l}function c(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return r.enable(r.load()),r}Ii.exports=ff});var Li=y((j,Mt)=>{j.formatArgs=cf;j.save=df;j.load=hf;j.useColors=lf;j.storage=pf();j.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();j.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function lf(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function cf(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+Mt.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;e.splice(1,0,t,"color: inherit");let r=0,n=0;e[0].replace(/%[a-zA-Z%]/g,i=>{i!=="%%"&&(r++,i==="%c"&&(n=r))}),e.splice(n,0,t)}j.log=console.debug||console.log||(()=>{});function df(e){try{e?j.storage.setItem("debug",e):j.storage.removeItem("debug")}catch{}}function hf(){let e;try{e=j.storage.getItem("debug")}catch{}return!e&&typeof process<"u"&&"env"in process&&(e=process.env.DEBUG),e}function pf(){try{return localStorage}catch{}}Mt.exports=Br()(j);var{formatters:mf}=Mt.exports;mf.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}});var Ni=y((_c,qi)=>{"use strict";qi.exports=(e,t=process.argv)=>{let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),i=t.indexOf("--");return n!==-1&&(i===-1||n<i)}});var Fi=y((Cc,Mi)=>{"use strict";var gf=require("os"),Di=require("tty"),z=Ni(),{env:q}=process,Ft;z("no-color")||z("no-colors")||z("color=false")||z("color=never")?Ft=0:(z("color")||z("colors")||z("color=true")||z("color=always"))&&(Ft=1);function vf(){if("FORCE_COLOR"in q)return q.FORCE_COLOR==="true"?1:q.FORCE_COLOR==="false"?0:q.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(q.FORCE_COLOR,10),3)}function yf(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function bf(e,{streamIsTTY:t,sniffFlags:r=!0}={}){let n=vf();n!==void 0&&(Ft=n);let i=r?Ft:n;if(i===0)return 0;if(r){if(z("color=16m")||z("color=full")||z("color=truecolor"))return 3;if(z("color=256"))return 2}if(e&&!t&&i===void 0)return 0;let o=i||0;if(q.TERM==="dumb")return o;if(process.platform==="win32"){let s=gf.release().split(".");return Number(s[0])>=10&&Number(s[2])>=10586?Number(s[2])>=14931?3:2:1}if("CI"in q)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(s=>s in q)||q.CI_NAME==="codeship"?1:o;if("TEAMCITY_VERSION"in q)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(q.TEAMCITY_VERSION)?1:0;if(q.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in q){let s=Number.parseInt((q.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(q.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(q.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(q.TERM)||"COLORTERM"in q?1:o}function Ur(e,t={}){let r=bf(e,{streamIsTTY:e&&e.isTTY,...t});return yf(r)}Mi.exports={supportsColor:Ur,stdout:Ur({isTTY:Di.isatty(1)}),stderr:Ur({isTTY:Di.isatty(2)})}});var Bi=y((N,Bt)=>{var wf=require("tty"),jt=require("util");N.init=Rf;N.log=Ef;N.formatArgs=_f;N.save=Tf;N.load=kf;N.useColors=Sf;N.destroy=jt.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");N.colors=[6,2,3,4,5,1];try{let e=Fi();e&&(e.stderr||e).level>=2&&(N.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}N.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(i,o)=>o.toUpperCase()),n=process.env[t];return/^(yes|on|true|enabled)$/i.test(n)?n=!0:/^(no|off|false|disabled)$/i.test(n)?n=!1:n==="null"?n=null:n=Number(n),e[r]=n,e},{});function Sf(){return"colors"in N.inspectOpts?!!N.inspectOpts.colors:wf.isatty(process.stderr.fd)}function _f(e){let{namespace:t,useColors:r}=this;if(r){let n=this.color,i="\x1B[3"+(n<8?n:"8;5;"+n),o=`  ${i};1m${t} \x1B[0m`;e[0]=o+e[0].split(`
`).join(`
`+o),e.push(i+"m+"+Bt.exports.humanize(this.diff)+"\x1B[0m")}else e[0]=Cf()+t+" "+e[0]}function Cf(){return N.inspectOpts.hideDate?"":new Date().toISOString()+" "}function Ef(...e){return process.stderr.write(jt.formatWithOptions(N.inspectOpts,...e)+`
`)}function Tf(e){e?process.env.DEBUG=e:delete process.env.DEBUG}function kf(){return process.env.DEBUG}function Rf(e){e.inspectOpts={};let t=Object.keys(N.inspectOpts);for(let r=0;r<t.length;r++)e.inspectOpts[t[r]]=N.inspectOpts[t[r]]}Bt.exports=Br()(N);var{formatters:ji}=Bt.exports;ji.o=function(e){return this.inspectOpts.colors=this.useColors,jt.inspect(e,this.inspectOpts).split(`
`).map(t=>t.trim()).join(" ")};ji.O=function(e){return this.inspectOpts.colors=this.useColors,jt.inspect(e,this.inspectOpts)}});var Ui=y((Ec,Gr)=>{typeof process>"u"||process.type==="renderer"||process.browser===!0||process.__nwjs?Gr.exports=Li():Gr.exports=Bi()});var Wi=y((Tc,Gi)=>{var Pf=require("fs");Gi.exports=function(e,t,r){t=typeof t<"u"?t:`
`,r=typeof r<"u"?r:"utf8";var n=[],i=Pf.readFileSync(e,r);if(i.indexOf("-END CERTIFICATE-")<0||i.indexOf("-BEGIN CERTIFICATE-")<0)throw Error("File does not contain 'BEGIN CERTIFICATE' or 'END CERTIFICATE'");i=i.split(t);var o=[],s,a;for(s=0,a=i.length;s<a;s++){var u=i[s];u.length!==0&&(o.push(u),u.match(/-END CERTIFICATE-/)&&(n.push(o.join(t)),o=[]))}return n}});var zi=y((kc,Vi)=>{var Of=require("querystring"),Af=_n(),Wr=require("fs"),Gt=require("path"),je=require("url"),xf=Oi(),Ut=Ui()("modem"),Hi=ur(),If=require("util"),je=require("url"),Lf=Wi(),qf=require("os").type()==="Windows_NT",Nf=function(){var e,t={};if(!process.env.DOCKER_HOST)t.socketPath=qf?"//./pipe/docker_engine":"/var/run/docker.sock";else if(process.env.DOCKER_HOST.indexOf("unix://")===0)t.socketPath=process.env.DOCKER_HOST.substring(7)||"/var/run/docker.sock";else if(process.env.DOCKER_HOST.indexOf("npipe://")===0)t.socketPath=process.env.DOCKER_HOST.substring(8)||"//./pipe/docker_engine";else{var r=process.env.DOCKER_HOST;r.indexOf("//")<0&&(r="tcp://"+r);try{e=new je.URL(r)}catch{throw new Error("DOCKER_HOST env variable should be something like tcp://localhost:1234")}t.port=e.port,process.env.DOCKER_TLS_VERIFY==="1"||t.port==="2376"?t.protocol="https":t.protocol="http",t.host=e.hostname,process.env.DOCKER_CERT_PATH&&(t.ca=Lf(Gt.join(process.env.DOCKER_CERT_PATH,"ca.pem")),t.cert=Wr.readFileSync(Gt.join(process.env.DOCKER_CERT_PATH,"cert.pem")),t.key=Wr.readFileSync(Gt.join(process.env.DOCKER_CERT_PATH,"key.pem"))),process.env.DOCKER_CLIENT_TIMEOUT&&(t.timeout=parseInt(process.env.DOCKER_CLIENT_TIMEOUT,10))}return t},Oe=function(e){var t=Nf(),r=Object.assign({},t,e);this.host=r.host,this.host||(this.socketPath=r.socketPath),this.port=r.port,this.username=r.username,this.password=r.password,this.version=r.version,this.key=r.key,this.cert=r.cert,this.ca=r.ca,this.timeout=r.timeout,this.connectionTimeout=r.connectionTimeout,this.checkServerIdentity=r.checkServerIdentity,this.agent=r.agent,this.headers=r.headers||{},this.key&&this.cert&&this.ca&&(this.protocol="https"),this.protocol=r.protocol||this.protocol||"http"};Oe.prototype.dial=function(e,t){var r,n,i,o=this;if(e.options&&(r=e.options),r&&r.authconfig&&delete r.authconfig,r&&r.abortSignal&&delete r.abortSignal,this.version&&(e.path="/"+this.version+e.path),this.host){var s=je.parse(o.host);n=je.format({protocol:s.protocol||o.protocol,hostname:s.hostname||o.host,port:o.port}),n=je.resolve(n,e.path)}else n=e.path;e.path.indexOf("?")!==-1&&(r&&Object.keys(r).length>0?n+=this.buildQuerystring(r._query||r):n=n.substring(0,n.length-1));var a={path:n,method:e.method,headers:e.headers||Object.assign({},o.headers),key:o.key,cert:o.cert,ca:o.ca};if(this.checkServerIdentity&&(a.checkServerIdentity=this.checkServerIdentity),this.agent&&(a.agent=this.agent),e.authconfig&&(a.headers["X-Registry-Auth"]=e.authconfig.key||e.authconfig.base64||Buffer.from(JSON.stringify(e.authconfig)).toString("base64")),e.registryconfig&&(a.headers["X-Registry-Config"]=e.registryconfig.base64||Buffer.from(JSON.stringify(e.registryconfig)).toString("base64")),e.abortSignal&&(a.signal=e.abortSignal),e.file?(typeof e.file=="string"?i=Wr.createReadStream(Gt.resolve(e.file)):i=e.file,a.headers["Content-Type"]="application/tar"):r&&e.method==="POST"&&(i=JSON.stringify(r._body||r),e.allowEmpty||i!=="{}"&&i!=='""'?a.headers["Content-Type"]="application/json":i=void 0),typeof i=="string"?a.headers["Content-Length"]=Buffer.byteLength(i):Buffer.isBuffer(i)===!0?a.headers["Content-Length"]=i.length:(a.method==="PUT"||e.hijack||e.openStdin)&&(a.headers["Transfer-Encoding"]="chunked"),e.hijack&&(a.headers.Connection="Upgrade",a.headers.Upgrade="tcp"),this.socketPath)a.socketPath=this.socketPath;else{var u=je.parse(n);a.hostname=u.hostname,a.port=u.port,a.path=u.path}this.buildRequest(a,e,i,t)};Oe.prototype.buildRequest=function(e,t,r,n){var i=this,o,s=Af[i.protocol].request(e,function(){});Ut("Sending: %s",If.inspect(e,{showHidden:!0,depth:null})),i.connectionTimeout&&(o=setTimeout(function(){Ut("Connection Timeout of %s ms exceeded",i.connectionTimeout),s.abort()},i.connectionTimeout)),i.timeout&&s.on("socket",function(a){a.setTimeout(i.timeout),a.on("timeout",function(){Ut("Timeout of %s ms exceeded",i.timeout),s.abort()})}),t.hijack===!0&&(clearTimeout(o),s.on("upgrade",function(a,u,c){return n(null,u)})),s.on("connect",function(){clearTimeout(o)}),s.on("disconnect",function(){clearTimeout(o)}),s.on("response",function(a){if(clearTimeout(o),t.isStream===!0)i.buildPayload(null,t.isStream,t.statusCodes,t.openStdin,s,a,null,n);else{var u=[];a.on("data",function(c){u.push(c)}),a.on("end",function(){var c=Buffer.concat(u),l=c.toString();Ut("Received: %s",l);var f=Hi.parseJSON(l)||c;i.buildPayload(null,t.isStream,t.statusCodes,!1,s,a,f,n)})}}),s.on("error",function(a){clearTimeout(o),i.buildPayload(a,t.isStream,t.statusCodes,!1,{},{},null,n)}),typeof r=="string"||Buffer.isBuffer(r)?s.write(r):r&&(r.on("error",function(a){s.destroy(a)}),r.pipe(s)),!t.hijack&&!t.openStdin&&(typeof r=="string"||r===void 0||Buffer.isBuffer(r))&&s.end()};Oe.prototype.buildPayload=function(e,t,r,n,i,o,s,a){if(e)return a(e,null);r[o.statusCode]!==!0?u(t,o,s,function(c,l){var f=new Error("(HTTP code "+o.statusCode+") "+(r[o.statusCode]||"unexpected")+" - "+(l.message||l)+" ");f.reason=r[o.statusCode],f.statusCode=o.statusCode,f.json=s,a(f,null)}):n?a(null,new xf(i,o)):t?a(null,o):a(null,s);function u(c,l,f,d){var h="";c?(l.on("data",function(v){h+=v}),l.on("end",function(){d(null,Hi.parseJSON(h)||h)})):d(null,f)}};Oe.prototype.demuxStream=function(e,t,r){var n=null,i=null,o=Buffer.from("");function s(u){if(u&&(o=Buffer.concat([o,u])),n){if(o.length>=i){var l=a(i);n===1?t.write(l):r.write(l),n=null,s()}}else if(o.length>=8){var c=a(8);n=c.readUInt8(0),i=c.readUInt32BE(4),s()}}function a(u){var c=o.slice(0,u);return o=Buffer.from(o.slice(u,o.length)),c}e.on("data",s)};Oe.prototype.followProgress=function(e,t,r){var n="",i=[],o=!1;e.on("data",s),e.on("error",a),e.on("end",u),e.on("close",u);function s(c){n+=c.toString(),l();function l(){for(var d;(d=n.indexOf(`
`))>=0;){if(d==0){n=n.slice(1);continue}f(n.slice(0,d)),n=n.slice(d+1)}}function f(d){if(d[d.length-1]=="\r"&&(d=d.substr(0,d.length-1)),d.length>0){var h=JSON.parse(d);i.push(h),r&&r(h)}}}function a(c){o=!0,e.removeListener("data",s),e.removeListener("error",a),e.removeListener("end",u),e.removeListener("close",u),t(c,i)}function u(){o||t(null,i),o=!0}};Oe.prototype.buildQuerystring=function(e){var t={};return Object.keys(e).map(function(r,n){e[r]&&typeof e[r]=="object"&&!Array.isArray(e[r])||r==="cachefrom"?t[r]=JSON.stringify(e[r]):t[r]=e[r]}),Of.stringify(t)};Vi.exports=Oe});var Qi=y((Rc,Xi)=>{"use strict";var $=require("fs"),Ae=require("path"),Df=$.lchown?"lchown":"chown",Mf=$.lchownSync?"lchownSync":"chownSync",Ki=$.lchown&&!process.version.match(/v1[1-9]+\./)&&!process.version.match(/v10\.[6-9]/),$i=(e,t,r)=>{try{return $[Mf](e,t,r)}catch(n){if(n.code!=="ENOENT")throw n}},Ff=(e,t,r)=>{try{return $.chownSync(e,t,r)}catch(n){if(n.code!=="ENOENT")throw n}},jf=Ki?(e,t,r,n)=>i=>{!i||i.code!=="EISDIR"?n(i):$.chown(e,t,r,n)}:(e,t,r,n)=>n,Hr=Ki?(e,t,r)=>{try{return $i(e,t,r)}catch(n){if(n.code!=="EISDIR")throw n;Ff(e,t,r)}}:(e,t,r)=>$i(e,t,r),Bf=process.version,Yi=(e,t,r)=>$.readdir(e,t,r),Uf=(e,t)=>$.readdirSync(e,t);/^v4\./.test(Bf)&&(Yi=(e,t,r)=>$.readdir(e,r));var Wt=(e,t,r,n)=>{$[Df](e,t,r,jf(e,t,r,i=>{n(i&&i.code!=="ENOENT"?i:null)}))},Ji=(e,t,r,n,i)=>{if(typeof t=="string")return $.lstat(Ae.resolve(e,t),(o,s)=>{if(o)return i(o.code!=="ENOENT"?o:null);s.name=t,Ji(e,s,r,n,i)});if(t.isDirectory())Vr(Ae.resolve(e,t.name),r,n,o=>{if(o)return i(o);let s=Ae.resolve(e,t.name);Wt(s,r,n,i)});else{let o=Ae.resolve(e,t.name);Wt(o,r,n,i)}},Vr=(e,t,r,n)=>{Yi(e,{withFileTypes:!0},(i,o)=>{if(i){if(i.code==="ENOENT")return n();if(i.code!=="ENOTDIR"&&i.code!=="ENOTSUP")return n(i)}if(i||!o.length)return Wt(e,t,r,n);let s=o.length,a=null,u=c=>{if(!a){if(c)return n(a=c);if(--s===0)return Wt(e,t,r,n)}};o.forEach(c=>Ji(e,c,t,r,u))})},Gf=(e,t,r,n)=>{if(typeof t=="string")try{let i=$.lstatSync(Ae.resolve(e,t));i.name=t,t=i}catch(i){if(i.code==="ENOENT")return;throw i}t.isDirectory()&&Zi(Ae.resolve(e,t.name),r,n),Hr(Ae.resolve(e,t.name),r,n)},Zi=(e,t,r)=>{let n;try{n=Uf(e,{withFileTypes:!0})}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR"||i.code==="ENOTSUP")return Hr(e,t,r);throw i}return n&&n.length&&n.forEach(i=>Gf(e,i,t,r)),Hr(e,t,r)};Xi.exports=Vr;Vr.sync=Zi});var ro=y((Pc,to)=>{"use strict";var{Buffer:J}=require("buffer"),eo=Symbol.for("BufferList");function A(e){if(!(this instanceof A))return new A(e);A._init.call(this,e)}A._init=function(t){Object.defineProperty(this,eo,{value:!0}),this._bufs=[],this.length=0,t&&this.append(t)};A.prototype._new=function(t){return new A(t)};A.prototype._offset=function(t){if(t===0)return[0,0];let r=0;for(let n=0;n<this._bufs.length;n++){let i=r+this._bufs[n].length;if(t<i||n===this._bufs.length-1)return[n,t-r];r=i}};A.prototype._reverseOffset=function(e){let t=e[0],r=e[1];for(let n=0;n<t;n++)r+=this._bufs[n].length;return r};A.prototype.get=function(t){if(t>this.length||t<0)return;let r=this._offset(t);return this._bufs[r[0]][r[1]]};A.prototype.slice=function(t,r){return typeof t=="number"&&t<0&&(t+=this.length),typeof r=="number"&&r<0&&(r+=this.length),this.copy(null,0,t,r)};A.prototype.copy=function(t,r,n,i){if((typeof n!="number"||n<0)&&(n=0),(typeof i!="number"||i>this.length)&&(i=this.length),n>=this.length||i<=0)return t||J.alloc(0);let o=!!t,s=this._offset(n),a=i-n,u=a,c=o&&r||0,l=s[1];if(n===0&&i===this.length){if(!o)return this._bufs.length===1?this._bufs[0]:J.concat(this._bufs,this.length);for(let f=0;f<this._bufs.length;f++)this._bufs[f].copy(t,c),c+=this._bufs[f].length;return t}if(u<=this._bufs[s[0]].length-l)return o?this._bufs[s[0]].copy(t,r,l,l+u):this._bufs[s[0]].slice(l,l+u);o||(t=J.allocUnsafe(a));for(let f=s[0];f<this._bufs.length;f++){let d=this._bufs[f].length-l;if(u>d)this._bufs[f].copy(t,c,l),c+=d;else{this._bufs[f].copy(t,c,l,l+u),c+=d;break}u-=d,l&&(l=0)}return t.length>c?t.slice(0,c):t};A.prototype.shallowSlice=function(t,r){if(t=t||0,r=typeof r!="number"?this.length:r,t<0&&(t+=this.length),r<0&&(r+=this.length),t===r)return this._new();let n=this._offset(t),i=this._offset(r),o=this._bufs.slice(n[0],i[0]+1);return i[1]===0?o.pop():o[o.length-1]=o[o.length-1].slice(0,i[1]),n[1]!==0&&(o[0]=o[0].slice(n[1])),this._new(o)};A.prototype.toString=function(t,r,n){return this.slice(r,n).toString(t)};A.prototype.consume=function(t){if(t=Math.trunc(t),Number.isNaN(t)||t<=0)return this;for(;this._bufs.length;)if(t>=this._bufs[0].length)t-=this._bufs[0].length,this.length-=this._bufs[0].length,this._bufs.shift();else{this._bufs[0]=this._bufs[0].slice(t),this.length-=t;break}return this};A.prototype.duplicate=function(){let t=this._new();for(let r=0;r<this._bufs.length;r++)t.append(this._bufs[r]);return t};A.prototype.append=function(t){if(t==null)return this;if(t.buffer)this._appendBuffer(J.from(t.buffer,t.byteOffset,t.byteLength));else if(Array.isArray(t))for(let r=0;r<t.length;r++)this.append(t[r]);else if(this._isBufferList(t))for(let r=0;r<t._bufs.length;r++)this.append(t._bufs[r]);else typeof t=="number"&&(t=t.toString()),this._appendBuffer(J.from(t));return this};A.prototype._appendBuffer=function(t){this._bufs.push(t),this.length+=t.length};A.prototype.indexOf=function(e,t,r){if(r===void 0&&typeof t=="string"&&(r=t,t=void 0),typeof e=="function"||Array.isArray(e))throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.');if(typeof e=="number"?e=J.from([e]):typeof e=="string"?e=J.from(e,r):this._isBufferList(e)?e=e.slice():Array.isArray(e.buffer)?e=J.from(e.buffer,e.byteOffset,e.byteLength):J.isBuffer(e)||(e=J.from(e)),t=Number(t||0),isNaN(t)&&(t=0),t<0&&(t=this.length+t),t<0&&(t=0),e.length===0)return t>this.length?this.length:t;let n=this._offset(t),i=n[0],o=n[1];for(;i<this._bufs.length;i++){let s=this._bufs[i];for(;o<s.length;)if(s.length-o>=e.length){let u=s.indexOf(e,o);if(u!==-1)return this._reverseOffset([i,u]);o=s.length-e.length+1}else{let u=this._reverseOffset([i,o]);if(this._match(u,e))return u;o++}o=0}return-1};A.prototype._match=function(e,t){if(this.length-e<t.length)return!1;for(let r=0;r<t.length;r++)if(this.get(e+r)!==t[r])return!1;return!0};(function(){let e={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1,readIntBE:null,readIntLE:null,readUIntBE:null,readUIntLE:null};for(let t in e)(function(r){e[r]===null?A.prototype[r]=function(n,i){return this.slice(n,n+i)[r](0,i)}:A.prototype[r]=function(n=0){return this.slice(n,n+e[r])[r](0)}})(t)})();A.prototype._isBufferList=function(t){return t instanceof A||A.isBufferList(t)};A.isBufferList=function(t){return t!=null&&t[eo]};to.exports=A});var no=y((Oc,Ht)=>{"use strict";var zr=Re().Duplex,Wf=de(),ot=ro();function M(e){if(!(this instanceof M))return new M(e);if(typeof e=="function"){this._callback=e;let t=function(n){this._callback&&(this._callback(n),this._callback=null)}.bind(this);this.on("pipe",function(n){n.on("error",t)}),this.on("unpipe",function(n){n.removeListener("error",t)}),e=null}ot._init.call(this,e),zr.call(this)}Wf(M,zr);Object.assign(M.prototype,ot.prototype);M.prototype._new=function(t){return new M(t)};M.prototype._write=function(t,r,n){this._appendBuffer(t),typeof n=="function"&&n()};M.prototype._read=function(t){if(!this.length)return this.push(null);t=Math.min(t,this.length),this.push(this.slice(0,t)),this.consume(t)};M.prototype.end=function(t){zr.prototype.end.call(this,t),this._callback&&(this._callback(null,this.slice()),this._callback=null)};M.prototype._destroy=function(t,r){this._bufs.length=0,this.length=0,r(t)};M.prototype._isBufferList=function(t){return t instanceof M||t instanceof ot||M.isBufferList(t)};M.isBufferList=ot.isBufferList;Ht.exports=M;Ht.exports.BufferListStream=M;Ht.exports.BufferList=ot});var Yr=y(Ue=>{var Hf=Buffer.alloc,Vf="0000000000000000000",zf="7777777777777777777",io=48,oo=Buffer.from("ustar\0","binary"),$f=Buffer.from("00","binary"),Kf=Buffer.from("ustar ","binary"),Yf=Buffer.from(" \0","binary"),Jf=parseInt("7777",8),st=257,Kr=263,Zf=function(e,t,r){return typeof e!="number"?r:(e=~~e,e>=t?t:e>=0||(e+=t,e>=0)?e:0)},Xf=function(e){switch(e){case 0:return"file";case 1:return"link";case 2:return"symlink";case 3:return"character-device";case 4:return"block-device";case 5:return"directory";case 6:return"fifo";case 7:return"contiguous-file";case 72:return"pax-header";case 55:return"pax-global-header";case 27:return"gnu-long-link-path";case 28:case 30:return"gnu-long-path"}return null},Qf=function(e){switch(e){case"file":return 0;case"link":return 1;case"symlink":return 2;case"character-device":return 3;case"block-device":return 4;case"directory":return 5;case"fifo":return 6;case"contiguous-file":return 7;case"pax-header":return 72}return 0},so=function(e,t,r,n){for(;r<n;r++)if(e[r]===t)return r;return n},ao=function(e){for(var t=256,r=0;r<148;r++)t+=e[r];for(var n=156;n<512;n++)t+=e[n];return t},ve=function(e,t){return e=e.toString(8),e.length>t?zf.slice(0,t)+" ":Vf.slice(0,t-e.length)+e+" "};function el(e){var t;if(e[0]===128)t=!0;else if(e[0]===255)t=!1;else return null;for(var r=[],n=e.length-1;n>0;n--){var i=e[n];t?r.push(i):r.push(255-i)}var o=0,s=r.length;for(n=0;n<s;n++)o+=r[n]*Math.pow(256,n);return t?o:-1*o}var ye=function(e,t,r){if(e=e.slice(t,t+r),t=0,e[t]&128)return el(e);for(;t<e.length&&e[t]===32;)t++;for(var n=Zf(so(e,32,t,e.length),e.length,e.length);t<n&&e[t]===0;)t++;return n===t?0:parseInt(e.slice(t,n).toString(),8)},Be=function(e,t,r,n){return e.slice(t,so(e,0,t,t+r)).toString(n)},$r=function(e){var t=Buffer.byteLength(e),r=Math.floor(Math.log(t)/Math.log(10))+1;return t+r>=Math.pow(10,r)&&r++,t+r+e};Ue.decodeLongPath=function(e,t){return Be(e,0,e.length,t)};Ue.encodePax=function(e){var t="";e.name&&(t+=$r(" path="+e.name+`
`)),e.linkname&&(t+=$r(" linkpath="+e.linkname+`
`));var r=e.pax;if(r)for(var n in r)t+=$r(" "+n+"="+r[n]+`
`);return Buffer.from(t)};Ue.decodePax=function(e){for(var t={};e.length;){for(var r=0;r<e.length&&e[r]!==32;)r++;var n=parseInt(e.slice(0,r).toString(),10);if(!n)return t;var i=e.slice(r+1,n-1).toString(),o=i.indexOf("=");if(o===-1)return t;t[i.slice(0,o)]=i.slice(o+1),e=e.slice(n)}return t};Ue.encode=function(e){var t=Hf(512),r=e.name,n="";if(e.typeflag===5&&r[r.length-1]!=="/"&&(r+="/"),Buffer.byteLength(r)!==r.length)return null;for(;Buffer.byteLength(r)>100;){var i=r.indexOf("/");if(i===-1)return null;n+=n?"/"+r.slice(0,i):r.slice(0,i),r=r.slice(i+1)}return Buffer.byteLength(r)>100||Buffer.byteLength(n)>155||e.linkname&&Buffer.byteLength(e.linkname)>100?null:(t.write(r),t.write(ve(e.mode&Jf,6),100),t.write(ve(e.uid,6),108),t.write(ve(e.gid,6),116),t.write(ve(e.size,11),124),t.write(ve(e.mtime.getTime()/1e3|0,11),136),t[156]=io+Qf(e.type),e.linkname&&t.write(e.linkname,157),oo.copy(t,st),$f.copy(t,Kr),e.uname&&t.write(e.uname,265),e.gname&&t.write(e.gname,297),t.write(ve(e.devmajor||0,6),329),t.write(ve(e.devminor||0,6),337),n&&t.write(n,345),t.write(ve(ao(t),6),148),t)};Ue.decode=function(e,t,r){var n=e[156]===0?0:e[156]-io,i=Be(e,0,100,t),o=ye(e,100,8),s=ye(e,108,8),a=ye(e,116,8),u=ye(e,124,12),c=ye(e,136,12),l=Xf(n),f=e[157]===0?null:Be(e,157,100,t),d=Be(e,265,32),h=Be(e,297,32),v=ye(e,329,8),b=ye(e,337,8),S=ao(e);if(S===8*32)return null;if(S!==ye(e,148,8))throw new Error("Invalid tar header. Maybe the tar is corrupted or it needs to be gunzipped?");if(oo.compare(e,st,st+6)===0)e[345]&&(i=Be(e,345,155,t)+"/"+i);else if(!(Kf.compare(e,st,st+6)===0&&Yf.compare(e,Kr,Kr+2)===0)){if(!r)throw new Error("Invalid tar header: unknown format.")}return n===0&&i&&i[i.length-1]==="/"&&(n=5),{name:i,mode:o,uid:s,gid:a,size:u,mtime:new Date(1e3*c),type:l,linkname:f,uname:d,gname:h,devmajor:v,devminor:b}}});var mo=y((xc,po)=>{var fo=require("util"),tl=no(),at=Yr(),lo=Re().Writable,co=Re().PassThrough,ho=function(){},uo=function(e){return e&=511,e&&512-e},rl=function(e,t){var r=new Vt(e,t);return r.end(),r},nl=function(e,t){return t.path&&(e.name=t.path),t.linkpath&&(e.linkname=t.linkpath),t.size&&(e.size=parseInt(t.size,10)),e.pax=t,e},Vt=function(e,t){this._parent=e,this.offset=t,co.call(this,{autoDestroy:!1})};fo.inherits(Vt,co);Vt.prototype.destroy=function(e){this._parent.destroy(e)};var ue=function(e){if(!(this instanceof ue))return new ue(e);lo.call(this,e),e=e||{},this._offset=0,this._buffer=tl(),this._missing=0,this._partial=!1,this._onparse=ho,this._header=null,this._stream=null,this._overflow=null,this._cb=null,this._locked=!1,this._destroyed=!1,this._pax=null,this._paxGlobal=null,this._gnuLongPath=null,this._gnuLongLinkPath=null;var t=this,r=t._buffer,n=function(){t._continue()},i=function(d){if(t._locked=!1,d)return t.destroy(d);t._stream||n()},o=function(){t._stream=null;var d=uo(t._header.size);d?t._parse(d,s):t._parse(512,f),t._locked||n()},s=function(){t._buffer.consume(uo(t._header.size)),t._parse(512,f),n()},a=function(){var d=t._header.size;t._paxGlobal=at.decodePax(r.slice(0,d)),r.consume(d),o()},u=function(){var d=t._header.size;t._pax=at.decodePax(r.slice(0,d)),t._paxGlobal&&(t._pax=Object.assign({},t._paxGlobal,t._pax)),r.consume(d),o()},c=function(){var d=t._header.size;this._gnuLongPath=at.decodeLongPath(r.slice(0,d),e.filenameEncoding),r.consume(d),o()},l=function(){var d=t._header.size;this._gnuLongLinkPath=at.decodeLongPath(r.slice(0,d),e.filenameEncoding),r.consume(d),o()},f=function(){var d=t._offset,h;try{h=t._header=at.decode(r.slice(0,512),e.filenameEncoding,e.allowUnknownFormat)}catch(v){t.emit("error",v)}if(r.consume(512),!h){t._parse(512,f),n();return}if(h.type==="gnu-long-path"){t._parse(h.size,c),n();return}if(h.type==="gnu-long-link-path"){t._parse(h.size,l),n();return}if(h.type==="pax-global-header"){t._parse(h.size,a),n();return}if(h.type==="pax-header"){t._parse(h.size,u),n();return}if(t._gnuLongPath&&(h.name=t._gnuLongPath,t._gnuLongPath=null),t._gnuLongLinkPath&&(h.linkname=t._gnuLongLinkPath,t._gnuLongLinkPath=null),t._pax&&(t._header=h=nl(h,t._pax),t._pax=null),t._locked=!0,!h.size||h.type==="directory"){t._parse(512,f),t.emit("entry",h,rl(t,d),i);return}t._stream=new Vt(t,d),t.emit("entry",h,t._stream,i),t._parse(h.size,o),n()};this._onheader=f,this._parse(512,f)};fo.inherits(ue,lo);ue.prototype.destroy=function(e){this._destroyed||(this._destroyed=!0,e&&this.emit("error",e),this.emit("close"),this._stream&&this._stream.emit("close"))};ue.prototype._parse=function(e,t){this._destroyed||(this._offset+=e,this._missing=e,t===this._onheader&&(this._partial=!1),this._onparse=t)};ue.prototype._continue=function(){if(!this._destroyed){var e=this._cb;this._cb=ho,this._overflow?this._write(this._overflow,void 0,e):e()}};ue.prototype._write=function(e,t,r){if(!this._destroyed){var n=this._stream,i=this._buffer,o=this._missing;if(e.length&&(this._partial=!0),e.length<o)return this._missing-=e.length,this._overflow=null,n?n.write(e,r):(i.append(e),r());this._cb=r,this._missing=0;var s=null;e.length>o&&(s=e.slice(o),e=e.slice(0,o)),n?n.end(e):i.append(e),this._overflow=s,this._onparse()}};ue.prototype._final=function(e){if(this._partial)return this.destroy(new Error("Unexpected end of data"));e()};po.exports=ue});var vo=y((Ic,go)=>{go.exports=require("fs").constants||require("constants")});var wo=y((Lc,bo)=>{bo.exports=yo;function yo(e,t){if(e&&t)return yo(e)(t);if(typeof e!="function")throw new TypeError("need wrapper function");return Object.keys(e).forEach(function(n){r[n]=e[n]}),r;function r(){for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];var o=e.apply(this,n),s=n[n.length-1];return typeof o=="function"&&o!==s&&Object.keys(s).forEach(function(a){o[a]=s[a]}),o}}});var Zr=y((qc,Jr)=>{var So=wo();Jr.exports=So(zt);Jr.exports.strict=So(_o);zt.proto=zt(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return zt(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return _o(this)},configurable:!0})});function zt(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function _o(e){var t=function(){if(t.called)throw new Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)},r=e.name||"Function wrapped with `once`";return t.onceError=r+" shouldn't be called more than once",t.called=!1,t}});var Xr=y((Nc,Eo)=>{var il=Zr(),ol=function(){},sl=function(e){return e.setHeader&&typeof e.abort=="function"},al=function(e){return e.stdio&&Array.isArray(e.stdio)&&e.stdio.length===3},Co=function(e,t,r){if(typeof t=="function")return Co(e,null,t);t||(t={}),r=il(r||ol);var n=e._writableState,i=e._readableState,o=t.readable||t.readable!==!1&&e.readable,s=t.writable||t.writable!==!1&&e.writable,a=!1,u=function(){e.writable||c()},c=function(){s=!1,o||r.call(e)},l=function(){o=!1,s||r.call(e)},f=function(S){r.call(e,S?new Error("exited with error code: "+S):null)},d=function(S){r.call(e,S)},h=function(){process.nextTick(v)},v=function(){if(!a){if(o&&!(i&&i.ended&&!i.destroyed))return r.call(e,new Error("premature close"));if(s&&!(n&&n.ended&&!n.destroyed))return r.call(e,new Error("premature close"))}},b=function(){e.req.on("finish",c)};return sl(e)?(e.on("complete",c),e.on("abort",h),e.req?b():e.on("request",b)):s&&!n&&(e.on("end",u),e.on("close",u)),al(e)&&e.on("exit",f),e.on("end",l),e.on("finish",c),t.error!==!1&&e.on("error",d),e.on("close",h),function(){a=!0,e.removeListener("complete",c),e.removeListener("abort",h),e.removeListener("request",b),e.req&&e.req.removeListener("finish",c),e.removeListener("end",u),e.removeListener("close",u),e.removeListener("finish",c),e.removeListener("exit",f),e.removeListener("end",l),e.removeListener("error",d),e.removeListener("close",h)}};Eo.exports=Co});var Oo=y((Dc,Po)=>{var Ge=vo(),To=Xr(),Kt=de(),ul=Buffer.alloc,ko=Re().Readable,We=Re().Writable,fl=require("string_decoder").StringDecoder,$t=Yr(),ll=parseInt("755",8),cl=parseInt("644",8),Ro=ul(1024),en=function(){},Qr=function(e,t){t&=511,t&&e.push(Ro.slice(0,512-t))};function dl(e){switch(e&Ge.S_IFMT){case Ge.S_IFBLK:return"block-device";case Ge.S_IFCHR:return"character-device";case Ge.S_IFDIR:return"directory";case Ge.S_IFIFO:return"fifo";case Ge.S_IFLNK:return"symlink"}return"file"}var Yt=function(e){We.call(this),this.written=0,this._to=e,this._destroyed=!1};Kt(Yt,We);Yt.prototype._write=function(e,t,r){if(this.written+=e.length,this._to.push(e))return r();this._to._drain=r};Yt.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this.emit("close"))};var Jt=function(){We.call(this),this.linkname="",this._decoder=new fl("utf-8"),this._destroyed=!1};Kt(Jt,We);Jt.prototype._write=function(e,t,r){this.linkname+=this._decoder.write(e),r()};Jt.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this.emit("close"))};var ut=function(){We.call(this),this._destroyed=!1};Kt(ut,We);ut.prototype._write=function(e,t,r){r(new Error("No body allowed for this entry"))};ut.prototype.destroy=function(){this._destroyed||(this._destroyed=!0,this.emit("close"))};var ne=function(e){if(!(this instanceof ne))return new ne(e);ko.call(this,e),this._drain=en,this._finalized=!1,this._finalizing=!1,this._destroyed=!1,this._stream=null};Kt(ne,ko);ne.prototype.entry=function(e,t,r){if(this._stream)throw new Error("already piping an entry");if(!(this._finalized||this._destroyed)){typeof t=="function"&&(r=t,t=null),r||(r=en);var n=this;if((!e.size||e.type==="symlink")&&(e.size=0),e.type||(e.type=dl(e.mode)),e.mode||(e.mode=e.type==="directory"?ll:cl),e.uid||(e.uid=0),e.gid||(e.gid=0),e.mtime||(e.mtime=new Date),typeof t=="string"&&(t=Buffer.from(t)),Buffer.isBuffer(t)){e.size=t.length,this._encode(e);var i=this.push(t);return Qr(n,e.size),i?process.nextTick(r):this._drain=r,new ut}if(e.type==="symlink"&&!e.linkname){var o=new Jt;return To(o,function(a){if(a)return n.destroy(),r(a);e.linkname=o.linkname,n._encode(e),r()}),o}if(this._encode(e),e.type!=="file"&&e.type!=="contiguous-file")return process.nextTick(r),new ut;var s=new Yt(this);return this._stream=s,To(s,function(a){if(n._stream=null,a)return n.destroy(),r(a);if(s.written!==e.size)return n.destroy(),r(new Error("size mismatch"));Qr(n,e.size),n._finalizing&&n.finalize(),r()}),s}};ne.prototype.finalize=function(){if(this._stream){this._finalizing=!0;return}this._finalized||(this._finalized=!0,this.push(Ro),this.push(null))};ne.prototype.destroy=function(e){this._destroyed||(this._destroyed=!0,e&&this.emit("error",e),this.emit("close"),this._stream&&this._stream.destroy&&this._stream.destroy())};ne.prototype._encode=function(e){if(!e.pax){var t=$t.encode(e);if(t){this.push(t);return}}this._encodePax(e)};ne.prototype._encodePax=function(e){var t=$t.encodePax({name:e.name,linkname:e.linkname,pax:e.pax}),r={name:"PaxHeader",mode:e.mode,uid:e.uid,gid:e.gid,size:t.length,mtime:e.mtime,type:"pax-header",linkname:e.linkname&&"PaxHeader",uname:e.uname,gname:e.gname,devmajor:e.devmajor,devminor:e.devminor};this.push($t.encode(r)),this.push(t),Qr(this,t.length),r.size=e.size,r.type=e.type,this.push($t.encode(r))};ne.prototype._read=function(e){var t=this._drain;this._drain=en,t()};Po.exports=ne});var Ao=y(tn=>{tn.extract=mo();tn.pack=Oo()});var Lo=y((Fc,Io)=>{var hl=Zr(),pl=Xr(),Zt;try{Zt=require("fs")}catch{}var ft=function(){},ml=/^v?\.0/.test(process.version),Xt=function(e){return typeof e=="function"},gl=function(e){return!ml||!Zt?!1:(e instanceof(Zt.ReadStream||ft)||e instanceof(Zt.WriteStream||ft))&&Xt(e.close)},vl=function(e){return e.setHeader&&Xt(e.abort)},yl=function(e,t,r,n){n=hl(n);var i=!1;e.on("close",function(){i=!0}),pl(e,{readable:t,writable:r},function(s){if(s)return n(s);i=!0,n()});var o=!1;return function(s){if(!i&&!o){if(o=!0,gl(e))return e.close(ft);if(vl(e))return e.abort();if(Xt(e.destroy))return e.destroy();n(s||new Error("stream was destroyed"))}}},xo=function(e){e()},bl=function(e,t){return e.pipe(t)},wl=function(){var e=Array.prototype.slice.call(arguments),t=Xt(e[e.length-1]||ft)&&e.pop()||ft;if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new Error("pump requires two streams per minimum");var r,n=e.map(function(i,o){var s=o<e.length-1,a=o>0;return yl(i,s,a,function(u){r||(r=u),u&&n.forEach(xo),!s&&(n.forEach(xo),t(r))})});return e.reduce(bl)};Io.exports=wl});var Mo=y((jc,Do)=>{var Qt=require("path"),qo=require("fs"),No=parseInt("0777",8);Do.exports=He.mkdirp=He.mkdirP=He;function He(e,t,r,n){typeof t=="function"?(r=t,t={}):(!t||typeof t!="object")&&(t={mode:t});var i=t.mode,o=t.fs||qo;i===void 0&&(i=No&~process.umask()),n||(n=null);var s=r||function(){};e=Qt.resolve(e),o.mkdir(e,i,function(a){if(!a)return n=n||e,s(null,n);switch(a.code){case"ENOENT":He(Qt.dirname(e),t,function(u,c){u?s(u,c):He(e,t,s,c)});break;default:o.stat(e,function(u,c){u||!c.isDirectory()?s(a,n):s(null,n)});break}})}He.sync=function e(t,r,n){(!r||typeof r!="object")&&(r={mode:r});var i=r.mode,o=r.fs||qo;i===void 0&&(i=No&~process.umask()),n||(n=null),t=Qt.resolve(t);try{o.mkdirSync(t,i),n=n||t}catch(a){switch(a.code){case"ENOENT":n=e(Qt.dirname(t),r,n),e(t,r,n);break;default:var s;try{s=o.statSync(t)}catch{throw a}if(!s.isDirectory())throw a;break}}return n}});var Vo=y(on=>{var Sl=Qi(),jo=Ao(),Bo=Lo(),_l=Mo(),Uo=require("fs"),B=require("path"),Cl=require("os"),lt=Cl.platform()==="win32",ct=function(){},nn=function(e){return e},rn=lt?function(e){return e.replace(/\\/g,"/").replace(/[:?<>|]/g,"_")}:nn,El=function(e,t,r,n,i,o){var s=i||["."];return function(u){if(!s.length)return u();var c=s.shift(),l=B.join(r,c);t(l,function(f,d){if(f)return u(f);if(!d.isDirectory())return u(null,c,d);e.readdir(l,function(h,v){if(h)return u(h);o&&v.sort();for(var b=0;b<v.length;b++)n(B.join(r,c,v[b]))||s.push(B.join(c,v[b]));u(null,c,d)})})}},Go=function(e,t){return function(r){r.name=r.name.split("/").slice(t).join("/");var n=r.linkname;return n&&(r.type==="link"||B.isAbsolute(n))&&(r.linkname=n.split("/").slice(t).join("/")),e(r)}};on.pack=function(e,t){e||(e="."),t||(t={});var r=t.fs||Uo,n=t.ignore||t.filter||ct,i=t.map||ct,o=t.mapStream||nn,s=El(r,t.dereference?r.stat:r.lstat,e,n,t.entries,t.sort),a=t.strict!==!1,u=typeof t.umask=="number"?~t.umask:~Wo(),c=typeof t.dmode=="number"?t.dmode:0,l=typeof t.fmode=="number"?t.fmode:0,f=t.pack||jo.pack(),d=t.finish||ct;t.strip&&(i=Go(i,t.strip)),t.readable&&(c|=parseInt(555,8),l|=parseInt(444,8)),t.writable&&(c|=parseInt(333,8),l|=parseInt(222,8));var h=function(S,p){r.readlink(B.join(e,S),function(w,m){if(w)return f.destroy(w);p.linkname=rn(m),f.entry(p,b)})},v=function(S,p,w){if(S)return f.destroy(S);if(!p)return t.finalize!==!1&&f.finalize(),d(f);if(w.isSocket())return b();var m={name:rn(p),mode:(w.mode|(w.isDirectory()?c:l))&u,mtime:w.mtime,size:w.size,type:"file",uid:w.uid,gid:w.gid};if(w.isDirectory())return m.size=0,m.type="directory",m=i(m)||m,f.entry(m,b);if(w.isSymbolicLink())return m.size=0,m.type="symlink",m=i(m)||m,h(p,m);if(m=i(m)||m,!w.isFile())return a?f.destroy(new Error("unsupported type for "+p)):b();var C=f.entry(m,b);if(C){var K=o(r.createReadStream(B.join(e,p)),m);K.on("error",function(X){C.destroy(X)}),Bo(K,C)}},b=function(S){if(S)return f.destroy(S);s(v)};return b(),f};var Tl=function(e){return e.length?e[e.length-1]:null},kl=function(){return process.getuid?process.getuid():-1},Wo=function(){return process.umask?process.umask():0};on.extract=function(e,t){e||(e="."),t||(t={});var r=t.fs||Uo,n=t.ignore||t.filter||ct,i=t.map||ct,o=t.mapStream||nn,s=t.chown!==!1&&!lt&&kl()===0,a=t.extract||jo.extract(),u=[],c=new Date,l=typeof t.umask=="number"?~t.umask:~Wo(),f=typeof t.dmode=="number"?t.dmode:0,d=typeof t.fmode=="number"?t.fmode:0,h=t.strict!==!1;t.strip&&(i=Go(i,t.strip)),t.readable&&(f|=parseInt(555,8),d|=parseInt(444,8)),t.writable&&(f|=parseInt(333,8),d|=parseInt(222,8));var v=function(p,w){for(var m;(m=Tl(u))&&p.slice(0,m[0].length)!==m[0];)u.pop();if(!m)return w();r.utimes(m[0],c,m[1],w)},b=function(p,w,m){if(t.utimes===!1)return m();if(w.type==="directory")return r.utimes(p,c,w.mtime,m);if(w.type==="symlink")return v(p,m);r.utimes(p,c,w.mtime,function(C){if(C)return m(C);v(p,m)})},S=function(p,w,m){var C=w.type==="symlink",K=C?r.lchmod:r.chmod,X=C?r.lchown:r.chown;if(!K)return m();var $e=(w.mode|(w.type==="directory"?f:d))&l;K(p,$e,function(le){if(le)return m(le);if(!s||!X)return m();X(p,w.uid,w.gid,m)})};return a.on("entry",function(p,w,m){p=i(p)||p,p.name=rn(p.name);var C=B.join(e,B.join("/",p.name));if(n(C,p))return w.resume(),m();var K=function(G){if(G)return m(G);b(C,p,function(Y){if(Y)return m(Y);if(lt)return m();S(C,p,m)})},X=function(){if(lt)return m();r.unlink(C,function(){r.symlink(p.linkname,C,K)})},$e=function(){if(lt)return m();r.unlink(C,function(){var G=B.join(e,B.join("/",p.linkname));r.link(G,C,function(Y){if(Y&&Y.code==="EPERM"&&t.hardlinkAsFilesFallback)return w=r.createReadStream(G),le();K(Y)})})},le=function(){var G=r.createWriteStream(C),Y=o(w,p);G.on("error",function(Se){Y.destroy(Se)}),Bo(Y,G,function(Se){if(Se)return m(Se);G.on("close",K)})};if(p.type==="directory")return u.push([C,p.mtime]),Fo(C,{fs:r,own:s,uid:p.uid,gid:p.gid},K);var Ke=B.dirname(C);Ho(r,Ke,B.join(e,"."),function(G,Y){if(G)return m(G);if(!Y)return m(new Error(Ke+" is not a valid path"));Fo(Ke,{fs:r,own:s,uid:p.uid,gid:p.gid},function(Se){if(Se)return m(Se);switch(p.type){case"file":return le();case"link":return $e();case"symlink":return X()}if(h)return m(new Error("unsupported type for "+C+" ("+p.type+")"));w.resume(),m()})})}),t.finish&&a.on("finish",t.finish),a};function Ho(e,t,r,n){if(t===r)return n(null,!0);e.lstat(t,function(i,o){if(i&&i.code!=="ENOENT")return n(i);if(i||o.isDirectory())return Ho(e,B.join(t,".."),r,n);n(null,!1)})}function Fo(e,t,r){_l(e,{fs:t.fs},function(n,i){!n&&i&&t.own?Sl(i,t.uid,t.gid,r):r(n)})}});var F=y((Uc,dt)=>{var zo=[],Rl=zo.forEach,Pl=zo.slice;dt.exports.extend=function(e){return Rl.call(Pl.call(arguments,1),function(t){if(t)for(var r in t)e[r]=t[r]}),e};dt.exports.processArgs=function(e,t,r){return!t&&typeof e=="function"&&(t=e,e=null),{callback:t,opts:dt.exports.extend({},r,e)}};dt.exports.parseRepositoryTag=function(e){var t,r=e.indexOf("@"),n=e.lastIndexOf(":");if(r>=0)t=r;else if(n>=0)t=n;else return{repository:e};var i=e.slice(t+1);return i.indexOf("/")===-1?{repository:e.slice(0,t),tag:i}:{repository:e}}});var an=y((Gc,$o)=>{var sn=F(),ht=function(e,t){this.modem=e,this.id=t};ht.prototype[require("util").inspect.custom]=function(){return this};ht.prototype.start=function(e,t){var r=this,n=sn.processArgs(e,t),i={path:"/exec/"+this.id+"/start",method:"POST",abortSignal:n.opts.abortSignal,isStream:!0,allowEmpty:!0,hijack:n.opts.hijack,openStdin:n.opts.stdin,statusCodes:{200:!0,204:!0,404:"no such exec",409:"container stopped/paused",500:"container not running"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,s)})};ht.prototype.resize=function(e,t){var r=this,n=sn.processArgs(e,t),i={path:"/exec/"+this.id+"/resize?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such exec",500:"container not running"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,s)})};ht.prototype.inspect=function(e,t){var r=this,n=sn.processArgs(e,t),i={path:"/exec/"+this.id+"/json",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such exec",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return t(o,s);n.callback(o,s)})};$o.exports=ht});var Jo=y((Hc,Yo)=>{var Wc=F().extend,Ko=an(),O=F(),R=function(e,t){this.modem=e,this.id=t,this.defaultOptions={top:{},start:{},commit:{},stop:{},pause:{},unpause:{},restart:{},resize:{},attach:{},remove:{},copy:{},kill:{},exec:{},rename:{},log:{},stats:{},getArchive:{},infoArchive:{},putArchive:{},update:{},wait:{}}};R.prototype[require("util").inspect.custom]=function(){return this};R.prototype.inspect=function(e,t){var r=this,n=O.processArgs(e,t),i={path:"/containers/"+this.id+"/json?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such container",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.rename=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.rename),i={path:"/containers/"+this.id+"/rename?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.update=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.update),i={path:"/containers/"+this.id+"/update",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,400:"bad parameter",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.top=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.top),i={path:"/containers/"+this.id+"/top?",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.changes=function(e,t){var r=this,n=O.processArgs(e,t),i={path:"/containers/"+this.id+"/changes",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such container",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.listCheckpoint=function(e,t){var r=this,n=O.processArgs(e,t),i={path:"/containers/"+this.id+"/checkpoints?",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.deleteCheckpoint=function(e,t,r){var n=this,i=O.processArgs(t,r),o={path:"/containers/"+this.id+"/checkpoints/"+e+"?",method:"DELETE",abortSignal:i.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:i.opts};if(i.callback===void 0)return new this.modem.Promise(function(s,a){n.modem.dial(o,function(u,c){if(u)return a(u);s(c)})});this.modem.dial(o,function(s,a){i.callback(s,a)})};R.prototype.createCheckpoint=function(e,t){var r=this,n=O.processArgs(e,t),i={path:"/containers/"+this.id+"/checkpoints",method:"POST",abortSignal:n.opts.abortSignal,allowEmpty:!0,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.export=function(e,t){var r=this,n=O.processArgs(e,t),i={path:"/containers/"+this.id+"/export",method:"GET",abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,404:"no such container",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.start=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.start),i={path:"/containers/"+this.id+"/start?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,304:"container already started",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.pause=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.pause),i={path:"/containers/"+this.id+"/pause",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.unpause=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.unpause),i={path:"/containers/"+this.id+"/unpause",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.exec=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.exec),i={path:"/containers/"+this.id+"/exec",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"no such container",409:"container stopped/paused",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(new Ko(r.modem,u.Id))})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,new Ko(r.modem,s.Id))})};R.prototype.commit=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.commit);n.opts.container=this.id;var i={path:"/commit?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.stop=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.stop),i={path:"/containers/"+this.id+"/stop?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,304:"container already stopped",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.restart=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.restart),i={path:"/containers/"+this.id+"/restart?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.kill=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.kill),i={path:"/containers/"+this.id+"/kill?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.resize=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.resize),i={path:"/containers/"+this.id+"/resize?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.attach=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.attach),i={path:"/containers/"+this.id+"/attach?",method:"POST",abortSignal:n.opts.abortSignal,isStream:!0,hijack:n.opts.hijack,openStdin:n.opts.stdin,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.wait=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.wait),i={path:"/containers/"+this.id+"/wait?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.remove=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.remove),i={path:"/containers/"+this.id+"?",method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,400:"bad parameter",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.copy=function(e,t){var r=this;console.log("container.copy is deprecated since Docker v1.8.x");var n=O.processArgs(e,t,this.defaultOptions.copy),i={path:"/containers/"+this.id+"/copy",method:"POST",abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.getArchive=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.getArchive),i={path:"/containers/"+this.id+"/archive?",method:"GET",abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,400:"client error, bad parameters",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.infoArchive=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.infoArchive),i={path:"/containers/"+this.id+"/archive?",method:"HEAD",abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,400:"client error, bad parameters",404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.putArchive=function(e,t,r){var n=this,i=O.processArgs(t,r,this.defaultOptions.putArchive),o={path:"/containers/"+this.id+"/archive?",method:"PUT",file:e,abortSignal:i.opts.abortSignal,isStream:!0,statusCodes:{200:!0,400:"client error, bad parameters",403:"client error, permission denied",404:"no such container",500:"server error"},options:i.opts};if(i.callback===void 0)return new this.modem.Promise(function(s,a){n.modem.dial(o,function(u,c){if(u)return a(u);s(c)})});this.modem.dial(o,function(s,a){i.callback(s,a)})};R.prototype.logs=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.log),i={path:"/containers/"+this.id+"/logs?",method:"GET",abortSignal:n.opts.abortSignal,isStream:n.opts.follow||!1,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};R.prototype.stats=function(e,t){var r=this,n=O.processArgs(e,t,this.defaultOptions.stats),i=!0;n.opts.stream===!1&&(i=!1);var o={path:"/containers/"+this.id+"/stats?",method:"GET",abortSignal:n.opts.abortSignal,isStream:i,statusCodes:{200:!0,404:"no such container",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(s,a){r.modem.dial(o,function(u,c){if(u)return a(u);s(c)})});this.modem.dial(o,function(s,a){n.callback(s,a)})};Yo.exports=R});var Xo=y((Vc,Zo)=>{var un=F(),fe=function(e,t){this.modem=e,this.name=t};fe.prototype[require("util").inspect.custom]=function(){return this};fe.prototype.inspect=function(e){var t=this,r={path:"/images/"+this.name+"/json",method:"GET",statusCodes:{200:!0,404:"no such image",500:"server error"}};if(e===void 0)return new this.modem.Promise(function(n,i){t.modem.dial(r,function(o,s){if(o)return i(o);n(s)})});this.modem.dial(r,function(n,i){if(n)return e(n,i);e(n,i)})};fe.prototype.distribution=function(e,t){var r=un.processArgs(e,t),n=this,i={path:"/distribution/"+this.name+"/json",method:"GET",statusCodes:{200:!0,401:"no such image",500:"server error"},authconfig:r.opts?r.opts.authconfig:void 0};if(r.callback===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return r.callback(o,s);r.callback(o,s)})};fe.prototype.history=function(e){var t=this,r={path:"/images/"+this.name+"/history",method:"GET",statusCodes:{200:!0,404:"no such image",500:"server error"}};if(e===void 0)return new this.modem.Promise(function(n,i){t.modem.dial(r,function(o,s){if(o)return i(o);n(s)})});this.modem.dial(r,function(n,i){if(n)return e(n,i);e(n,i)})};fe.prototype.get=function(e){var t=this,r={path:"/images/"+this.name+"/get",method:"GET",isStream:!0,statusCodes:{200:!0,500:"server error"}};if(e===void 0)return new this.modem.Promise(function(n,i){t.modem.dial(r,function(o,s){if(o)return i(o);n(s)})});this.modem.dial(r,function(n,i){if(n)return e(n,i);e(n,i)})};fe.prototype.push=function(e,t,r){var n=this,i=un.processArgs(e,t),o=!0;i.opts.stream===!1&&(o=!1);var s={path:"/images/"+this.name+"/push?",method:"POST",options:i.opts,authconfig:i.opts.authconfig||r,abortSignal:i.opts.abortSignal,isStream:o,statusCodes:{200:!0,404:"no such image",500:"server error"}};if(delete s.options.authconfig,t===void 0)return new this.modem.Promise(function(a,u){n.modem.dial(s,function(c,l){if(c)return u(c);a(l)})});this.modem.dial(s,function(a,u){t(a,u)})};fe.prototype.tag=function(e,t){var r=this,n={path:"/images/"+this.name+"/tag?",method:"POST",options:e,abortSignal:e&&e.abortSignal,statusCodes:{200:!0,201:!0,400:"bad parameter",404:"no such image",409:"conflict",500:"server error"}};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};fe.prototype.remove=function(e,t){var r=this,n=un.processArgs(e,t),i={path:"/images/"+this.name+"?",method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such image",409:"conflict",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Zo.exports=fe});var ts=y((zc,es)=>{var Qo=F(),er=function(e,t){this.modem=e,this.name=t};er.prototype[require("util").inspect.custom]=function(){return this};er.prototype.inspect=function(e,t){var r=this,n=Qo.processArgs(e,t),i={path:"/volumes/"+this.name,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such volume",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};er.prototype.remove=function(e,t){var r=this,n=Qo.processArgs(e,t),i={path:"/volumes/"+this.name,method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{204:!0,404:"no such volume",409:"conflict",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};es.exports=er});var ns=y(($c,rs)=>{var tr=F(),Ve=function(e,t){this.modem=e,this.id=t};Ve.prototype[require("util").inspect.custom]=function(){return this};Ve.prototype.inspect=function(i,t){var r=this,n=tr.processArgs(i,t),i={path:"/networks/"+this.id+"?",method:"GET",statusCodes:{200:!0,404:"no such network",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Ve.prototype.remove=function(e,t){var r=this,n=tr.processArgs(e,t),i={path:"/networks/"+this.id,method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such network",409:"conflict",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Ve.prototype.connect=function(e,t){var r=this,n=tr.processArgs(e,t),i={path:"/networks/"+this.id+"/connect",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"network or container is not found",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Ve.prototype.disconnect=function(e,t){var r=this,n=tr.processArgs(e,t),i={path:"/networks/"+this.id+"/disconnect",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"network or container is not found",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};rs.exports=Ve});var os=y((Kc,is)=>{var fn=F(),ze=function(e,t){this.modem=e,this.id=t};ze.prototype[require("util").inspect.custom]=function(){return this};ze.prototype.inspect=function(e,t){var r=this,n=fn.processArgs(e,t),i={path:"/services/"+this.id,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such service",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ze.prototype.remove=function(e,t){var r=this,n=fn.processArgs(e,t),i={path:"/services/"+this.id,method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"no such service",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ze.prototype.update=function(e,t,r){var n=this;if(!r){var i=typeof t;i==="function"?(r=t,t=e,e=t.authconfig||void 0):i==="undefined"&&(t=e,e=t.authconfig||void 0)}var o={path:"/services/"+this.id+"/update?",method:"POST",abortSignal:t&&t.abortSignal,statusCodes:{200:!0,404:"no such service",500:"server error"},authconfig:e,options:t};if(r===void 0)return new this.modem.Promise(function(s,a){n.modem.dial(o,function(u,c){if(u)return a(u);s(c)})});this.modem.dial(o,function(s,a){r(s,a)})};ze.prototype.logs=function(e,t){var r=this,n=fn.processArgs(e,t,{}),i={path:"/services/"+this.id+"/logs?",method:"GET",abortSignal:n.opts.abortSignal,isStream:n.opts.follow||!1,statusCodes:{200:!0,404:"no such service",500:"server error",503:"node is not part of a swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};is.exports=ze});var as=y((Yc,ss)=>{var be=F(),Z=function(e,t,r){this.modem=e,this.name=t,this.remote=r||t};Z.prototype[require("util").inspect.custom]=function(){return this};Z.prototype.inspect=function(e,t){var r=this,n=be.processArgs(e,t),i={path:"/plugins/"+this.name,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"plugin is not installed",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Z.prototype.remove=function(e,t){var r=this,n=be.processArgs(e,t),i={path:"/plugins/"+this.name+"?",method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"plugin is not installed",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,s)})};Z.prototype.privileges=function(e,t){var r=this,n=be.processArgs(e,t),i={path:"/plugins/privileges?",method:"GET",options:{remote:this.remote},abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Z.prototype.pull=function(e,t){var r=this,n=be.processArgs(e,t);n.opts._query&&!n.opts._query.name&&(n.opts._query.name=this.name),n.opts._query&&!n.opts._query.remote&&(n.opts._query.remote=this.remote);var i={path:"/plugins/pull?",method:"POST",abortSignal:n.opts.abortSignal,isStream:!0,options:n.opts,statusCodes:{200:!0,204:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Z.prototype.enable=function(e,t){var r=this,n=be.processArgs(e,t),i={path:"/plugins/"+this.name+"/enable?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Z.prototype.disable=function(e,t){var r=this,n=be.processArgs(e,t),i={path:"/plugins/"+this.name+"/disable",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Z.prototype.push=function(e,t){var r=this,n=be.processArgs(e,t),i={path:"/plugins/"+this.name+"/push",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"plugin not installed",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Z.prototype.configure=function(e,t){var r=this,n=be.processArgs(e,t),i={path:"/plugins/"+this.name+"/set",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"plugin not installed",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};Z.prototype.upgrade=function(e,t,r){var n=this;!r&&typeof t=="function"&&(r=t,t=e,e=t.authconfig||void 0);var i={path:"/plugins/"+this.name+"/upgrade?",method:"POST",abortSignal:t&&t.abortSignal,statusCodes:{200:!0,204:!0,404:"plugin not installed",500:"server error"},authconfig:e,options:t};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){r(o,s)})};ss.exports=Z});var ls=y((Jc,fs)=>{var us=F(),pt=function(e,t){this.modem=e,this.id=t};pt.prototype[require("util").inspect.custom]=function(){return this};pt.prototype.inspect=function(e,t){var r=this,n=us.processArgs(e,t),i={path:"/secrets/"+this.id,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"secret not found",406:"node is not part of a swarm",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};pt.prototype.update=function(e,t){var r=this;!t&&typeof e=="function"&&(t=e);var n={path:"/secrets/"+this.id+"/update?",method:"POST",abortSignal:e&&e.abortSignal,statusCodes:{200:!0,404:"secret not found",500:"server error"},options:e};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};pt.prototype.remove=function(e,t){var r=this,n=us.processArgs(e,t),i={path:"/secrets/"+this.id,method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,404:"secret not found",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};fs.exports=pt});var ds=y((Zc,cs)=>{var ln=F(),mt=function(e,t){this.modem=e,this.id=t};mt.prototype[require("util").inspect.custom]=function(){return this};mt.prototype.inspect=function(e,t){var r=this,n=ln.processArgs(e,t),i={path:"/configs/"+this.id,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"config not found",500:"server error",503:"node is not part of a swarm"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};mt.prototype.update=function(e,t){var r=this,n=ln.processArgs(e,t),i={path:"/configs/"+this.id+"/update?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"config not found",500:"server error",503:"node is not part of a swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};mt.prototype.remove=function(e,t){var r=this,n=ln.processArgs(e,t),i={path:"/configs/"+this.id,method:"DELETE",abortSignal:e.abortSignal,statusCodes:{200:!0,204:!0,404:"config not found",500:"server error",503:"node is not part of a swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};cs.exports=mt});var ms=y((Xc,ps)=>{var hs=F(),rr=function(e,t){this.modem=e,this.id=t,this.defaultOptions={log:{}}};rr.prototype[require("util").inspect.custom]=function(){return this};rr.prototype.inspect=function(e,t){var r=this,n=hs.processArgs(e,t),i={path:"/tasks/"+this.id,method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"unknown task",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};rr.prototype.logs=function(e,t){var r=this,n=hs.processArgs(e,t,this.defaultOptions.log),i={path:"/tasks/"+this.id+"/logs?",method:"GET",abortSignal:n.opts.abortSignal,isStream:n.opts.follow||!1,statusCodes:{101:!0,200:!0,404:"no such container",500:"server error",503:"node is not part of a swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};ps.exports=rr});var ys=y((Qc,vs)=>{var gs=F(),gt=function(e,t){this.modem=e,this.id=t};gt.prototype[require("util").inspect.custom]=function(){return this};gt.prototype.inspect=function(e,t){var r=this,n=gs.processArgs(e,t),i={path:"/nodes/"+this.id,method:"GET",abortSignal:n.abortSignal,statusCodes:{200:!0,404:"no such node",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};gt.prototype.update=function(e,t){var r=this;!t&&typeof e=="function"&&(t=e);var n={path:"/nodes/"+this.id+"/update?",method:"POST",abortSignal:e&&e.abortSignal,statusCodes:{200:!0,404:"no such node",406:"node is not part of a swarm",500:"server error"},options:e};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};gt.prototype.remove=function(e,t){var r=this,n=gs.processArgs(e,t),i={path:"/nodes/"+this.id+"?",method:"DELETE",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,404:"no such node",500:"server error"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};vs.exports=gt});var xs=y((ed,As)=>{var Ol=require("events").EventEmitter,Al=zi(),xl=Vo(),Il=require("zlib"),bs=Jo(),ws=Xo(),Ss=ts(),_s=ns(),Cs=os(),Es=as(),Ts=ls(),Ll=ds(),ks=ms(),Rs=ys(),Ps=an(),T=F(),Os=T.extend,g=function(e){if(!(this instanceof g))return new g(e);var t=global.Promise;e&&e.Promise&&(t=e.Promise,Object.keys(e).length===1&&(e=void 0)),this.modem=new Al(e),this.modem.Promise=t};g.prototype.createContainer=function(e,t){var r=this,n={path:"/containers/create?",method:"POST",options:e,authconfig:e.authconfig,abortSignal:e.abortSignal,statusCodes:{200:!0,201:!0,404:"no such container",406:"impossible to attach",500:"server error"}};if(delete e.authconfig,t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(r.getContainer(a.Id))})});this.modem.dial(n,function(i,o){if(i)return t(i,o);t(i,r.getContainer(o.Id))})};g.prototype.createImage=function(e,t,r){var n=this;!r&&typeof t=="function"?(r=t,t=e,e=t.authconfig||void 0):!r&&!t&&(t=e,e=t.authconfig);var i={path:"/images/create?",method:"POST",options:t,authconfig:e,abortSignal:t.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){r(o,s)})};g.prototype.loadImage=function(e,t,r){var n=this;!r&&typeof t=="function"&&(r=t,t=null);var i={path:"/images/load?",method:"POST",options:t,file:e,abortSignal:t&&t.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){r(o,s)})};g.prototype.importImage=function(e,t,r){var n=this;!r&&typeof t=="function"&&(r=t,t=void 0),t||(t={}),t.fromSrc="-";var i={path:"/images/create?",method:"POST",options:t,file:e,abortSignal:t.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){r(o,s)})};g.prototype.checkAuth=function(e,t){var r=this,n={path:"/auth",method:"POST",options:e,abortSignal:e.abortSignal,statusCodes:{200:!0,204:!0,500:"server error"}};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};g.prototype.buildImage=function(e,t,r){var n=this;!r&&typeof t=="function"&&(r=t,t=null);function i(s){var a={path:"/build?",method:"POST",file:s,options:t,abortSignal:t&&t.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(t&&(t.registryconfig&&(a.registryconfig=a.options.registryconfig,delete a.options.registryconfig),t.authconfig&&(a.authconfig=a.options.authconfig,delete a.options.authconfig)),r===void 0)return new n.modem.Promise(function(u,c){n.modem.dial(a,function(l,f){if(l)return c(l);u(f)})});n.modem.dial(a,function(u,c){r(u,c)})}if(e&&e.context){var o=xl.pack(e.context,{entries:e.src});return i(o.pipe(Il.createGzip()))}else return i(e)};g.prototype.getContainer=function(e){return new bs(this.modem,e)};g.prototype.getImage=function(e){return new ws(this.modem,e)};g.prototype.getVolume=function(e){return new Ss(this.modem,e)};g.prototype.getPlugin=function(e,t){return new Es(this.modem,e,t)};g.prototype.getService=function(e){return new Cs(this.modem,e)};g.prototype.getTask=function(e){return new ks(this.modem,e)};g.prototype.getNode=function(e){return new Rs(this.modem,e)};g.prototype.getNetwork=function(e){return new _s(this.modem,e)};g.prototype.getSecret=function(e){return new Ts(this.modem,e)};g.prototype.getConfig=function(e){return new Ll(this.modem,e)};g.prototype.getExec=function(e){return new Ps(this.modem,e)};g.prototype.listContainers=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/containers/json?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.listImages=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/images/json?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.getImages=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/images/get?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.listServices=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/services?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.listNodes=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/nodes?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",404:"no such node",500:"server error",503:"node is not part of a swarm"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.listTasks=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/tasks?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.createSecret=function(e,t){var r=T.processArgs(e,t),n=this,i={path:"/secrets/create?",method:"POST",options:r.opts,abortSignal:r.opts.abortSignal,statusCodes:{200:!0,201:!0,406:"server error or node is not part of a swarm",409:"name conflicts with an existing object",500:"server error"}};if(r.callback===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(n.getSecret(u.ID))})});this.modem.dial(i,function(o,s){if(o)return r.callback(o,s);r.callback(o,n.getSecret(s.ID))})};g.prototype.createConfig=function(e,t){var r=T.processArgs(e,t),n=this,i={path:"/configs/create?",method:"POST",options:r.opts,abortSignal:r.opts.abortSignal,statusCodes:{200:!0,201:!0,406:"server error or node is not part of a swarm",409:"name conflicts with an existing object",500:"server error"}};if(r.callback===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(n.getConfig(u.ID))})});this.modem.dial(i,function(o,s){if(o)return r.callback(o,s);r.callback(o,n.getConfig(s.ID))})};g.prototype.listSecrets=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/secrets?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.listConfigs=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/configs?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.createPlugin=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/plugins/create?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,204:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(r.getPlugin(n.opts.name))})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,r.getPlugin(n.opts.name))})};g.prototype.listPlugins=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/plugins?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.pruneImages=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/images/prune?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.pruneBuilder=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/build/prune",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.pruneContainers=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/containers/prune?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.pruneVolumes=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/volumes/prune?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.pruneNetworks=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/networks/prune?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.createVolume=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/volumes/create?",method:"POST",allowEmpty:!0,options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(r.getVolume(u.Name))})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,r.getVolume(s.Name))})};g.prototype.createService=function(e,t,r){!r&&typeof t=="function"?(r=t,t=e,e=t.authconfig||void 0):!t&&!r&&(t=e);var n=this,i={path:"/services/create",method:"POST",options:t,authconfig:e,abortSignal:t&&t.abortSignal,statusCodes:{200:!0,201:!0,500:"server error"}};if(r===void 0)return new this.modem.Promise(function(o,s){n.modem.dial(i,function(a,u){if(a)return s(a);o(n.getService(u.ID||u.Id))})});this.modem.dial(i,function(o,s){if(o)return r(o,s);r(o,n.getService(s.ID||s.Id))})};g.prototype.listVolumes=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/volumes?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.createNetwork=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/networks/create?",method:"POST",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,201:!0,404:"driver not found",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(r.getNetwork(u.Id))})});this.modem.dial(i,function(o,s){if(o)return n.callback(o,s);n.callback(o,r.getNetwork(s.Id))})};g.prototype.listNetworks=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/networks?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.searchImages=function(e,t){var r=this,n={path:"/images/search?",method:"GET",options:e,authconfig:e.authconfig,abortSignal:e.abortSignal,statusCodes:{200:!0,500:"server error"}};if(t===void 0)return new this.modem.Promise(function(i,o){r.modem.dial(n,function(s,a){if(s)return o(s);i(a)})});this.modem.dial(n,function(i,o){t(i,o)})};g.prototype.info=function(i,t){var r=this,n=T.processArgs(i,t),i={path:"/info",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.version=function(i,t){var r=this,n=T.processArgs(i,t),i={path:"/version",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.ping=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/_ping",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.df=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/system/df",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.getEvents=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/events?",method:"GET",options:n.opts,abortSignal:n.opts.abortSignal,isStream:!0,statusCodes:{200:!0,500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.pull=function(e,t,r,n){var i=T.processArgs(t,r),o=T.parseRepositoryTag(e);i.opts.fromImage=o.repository,i.opts.tag=o.tag||"latest";var s=[i.opts,i.callback];return n&&(s=[n,i.opts,i.callback]),this.createImage.apply(this,s)};g.prototype.run=function(e,t,r,n,i,o){return typeof arguments[arguments.length-1]=="function"?this.runCallback(e,t,r,n,i,o):this.runPromise(e,t,r,n,i)};g.prototype.runCallback=function(e,t,r,n,i,o){!o&&typeof n=="function"?(o=n,n={},i={}):!o&&typeof i=="function"&&(o=i,i={});var s=new Ol;function a(c,l){if(c)return o(c,null,l);s.emit("container",l),l.attach({stream:!0,stdout:!0,stderr:!0},function(d,h){if(d)return o(d,null,l);s.emit("stream",h),r&&(r instanceof Array?(h.on("end",function(){try{r[0].end()}catch{}try{r[1].end()}catch{}}),l.modem.demuxStream(h,r[0],r[1])):(h.setEncoding("utf8"),h.pipe(r,{end:!0}))),l.start(i,function(v,b){if(v)return o(v,b,l);s.emit("start",l),l.wait(function(S,p){s.emit("data",p),o(S,p,l)})})})}var u={Hostname:"",User:"",AttachStdin:!1,AttachStdout:!0,AttachStderr:!0,Tty:!0,OpenStdin:!1,StdinOnce:!1,Env:null,Cmd:t,Image:e,Volumes:{},VolumesFrom:[]};return Os(u,n),this.createContainer(u,a),s};g.prototype.runPromise=function(e,t,r,n,i){var o=this;n=n||{},i=i||{};var s={Hostname:"",User:"",AttachStdin:!1,AttachStdout:!0,AttachStderr:!0,Tty:!0,OpenStdin:!1,StdinOnce:!1,Env:null,Cmd:t,Image:e,Volumes:{},VolumesFrom:[]};Os(s,n);var a;return new this.modem.Promise(function(u,c){o.createContainer(s).then(function(l){return a=l,l.attach({stream:!0,stdout:!0,stderr:!0})}).then(function(l){return r&&(r instanceof Array?(l.on("end",function(){try{r[0].end()}catch{}try{r[1].end()}catch{}}),a.modem.demuxStream(l,r[0],r[1])):(l.setEncoding("utf8"),l.pipe(r,{end:!0}))),a.start(i)}).then(function(l){return a.wait()}).then(function(l){u([l,a])}).catch(function(l){c(l)})})};g.prototype.swarmInit=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/swarm/init",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",406:"node is already part of a Swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.swarmJoin=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/swarm/join",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",406:"node is already part of a Swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.swarmLeave=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/swarm/leave?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,406:"node is not part of a Swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.swarmUpdate=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/swarm/update?",method:"POST",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,400:"bad parameter",406:"node is already part of a Swarm"},options:n.opts};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.prototype.swarmInspect=function(e,t){var r=this,n=T.processArgs(e,t),i={path:"/swarm",method:"GET",abortSignal:n.opts.abortSignal,statusCodes:{200:!0,406:"This node is not a swarm manager",500:"server error"}};if(n.callback===void 0)return new this.modem.Promise(function(o,s){r.modem.dial(i,function(a,u){if(a)return s(a);o(u)})});this.modem.dial(i,function(o,s){n.callback(o,s)})};g.Container=bs;g.Image=ws;g.Volume=Ss;g.Network=_s;g.Service=Cs;g.Plugin=Es;g.Secret=Ts;g.Task=ks;g.Node=Rs;g.Exec=Ps;As.exports=g});var Dl={};Gs(Dl,{default:()=>Ds});module.exports=Hs(Dl);var x=require("@raycast/api");var E=require("@raycast/api");var pn=e=>"```\n"+e+"\n```";var ie=e=>e.State==="running",W=({Names:e,Name:t})=>e!==void 0?e.map(r=>r.replace(/^\//,"")).join(", "):t!==void 0?t.replace(/^\//,""):"-",mn=e=>e!==void 0?`# ${W(e)}

`+zs(e.Config.Env)+`
`:"",zs=e=>e.length===0?"":[`

## Environment`,pn(e.join(`
`))].join(`
`);var or=e=>e.errno===-61&&e.code==="ECONNREFUSED",Q=(e,t)=>e.message.includes("container already stopped")?`Container ${W(t)} already stopped`:e.message.includes("container already started")?`Container ${W(t)} already started`:e.message.includes("cannot remove a running container")?["Cannot remove running container",`Please stop ${W(t)} first`]:e.message;var xe=require("@raycast/api"),ee=({action:e,onSuccess:t,onFailure:r})=>async()=>{try{await e(),t!==void 0&&await(0,xe.showToast)(xe.Toast.Style.Success,...gn(t()))}catch(n){n instanceof Error&&r!==void 0&&await(0,xe.showToast)(xe.Toast.Style.Failure,...gn(r(n)))}},gn=e=>Array.isArray(e)?e:[e];var L=require("react/jsx-runtime");function sr({docker:e,containerId:t}){let{isLoading:r,containerInfo:n,startContainer:i,restartContainer:o,stopContainer:s,removeContainer:a}=e.useContainerInfo(t),{pop:u}=(0,E.useNavigation)();return(0,L.jsx)(E.Detail,{isLoading:r,markdown:mn(n),metadata:n&&(0,L.jsxs)(E.Detail.Metadata,{children:[(0,L.jsx)(E.Detail.Metadata.Label,{title:"Image",text:n?.Config.Image}),(0,L.jsx)(E.Detail.Metadata.TagList,{title:"Status",children:(0,L.jsx)(E.Detail.Metadata.TagList.Item,{text:n.State.Status,color:n.State.Running?E.Color.Green:E.Color.Yellow})}),(0,L.jsx)(E.Detail.Metadata.TagList,{title:"Command",children:(0,L.jsx)(E.Detail.Metadata.TagList.Item,{text:n.Config.Cmd?.join(" ")})}),n.Config.ExposedPorts&&(0,L.jsx)(E.Detail.Metadata.TagList,{title:"Ports",children:Object.keys(n.Config.ExposedPorts).map((c,l)=>(0,L.jsx)(E.Detail.Metadata.TagList.Item,{text:c,color:E.Color.PrimaryText},l))}),n.NetworkSettings.Ports&&(0,L.jsx)(E.Detail.Metadata.TagList,{title:"Host Ports",children:Object.keys(n.NetworkSettings.Ports).map((c,l)=>(0,L.jsx)(E.Detail.Metadata.TagList.Item,{text:n.NetworkSettings.Ports[c]?.map(f=>f.HostPort).join(", ")||"None",color:E.Color.PrimaryText},l))}),(0,L.jsx)(E.Detail.Metadata.Separator,{}),n.Created&&(0,L.jsx)(E.Detail.Metadata.Label,{title:"Created at",text:new Date(n.Created).toLocaleString()})]}),actions:(0,L.jsxs)(E.ActionPanel,{children:[n?.State.Running===!0&&(0,L.jsx)(E.Action,{title:"Stop Container",shortcut:{modifiers:["cmd","shift"],key:"w"},onAction:ee({action:()=>s(n),onSuccess:()=>`Container ${W(n)} stopped`,onFailure:c=>Q(c,n)})}),n?.State.Running===!0&&(0,L.jsx)(E.Action,{title:"Restart Container",icon:E.Icon.ArrowClockwise,shortcut:{modifiers:["opt"],key:"r"},onAction:ee({action:()=>o(n),onSuccess:()=>`Container ${W(n)} restarted`,onFailure:c=>Q(c,n)})}),n?.State.Running===!1&&(0,L.jsx)(E.Action,{title:"Start Container",shortcut:{modifiers:["cmd","shift"],key:"r"},onAction:ee({action:()=>i(n),onSuccess:()=>`Container ${W(n)} started`,onFailure:c=>Q(c,n)})}),n!==void 0&&(0,L.jsx)(E.Action,{title:"Remove Container",icon:E.Icon.Trash,style:E.Action.Style.Destructive,shortcut:E.Keyboard.Shortcut.Common.Remove,onAction:ee({action:async()=>{await a(n),u()},onSuccess:()=>`Container ${W(n)} removed`,onFailure:c=>Q(c,n)})})]})},t)}var P=require("react");var yt=e=>e.reduce((t,r)=>{let n=r.Labels["com.docker.compose.project"],i=r.Labels["com.docker.compose.config_files"],o=r.Labels["com.docker.compose.working_dir"];if(n===void 0)return t;let s=t.find(({name:a})=>a===n);return s!==void 0?(s.containers=[...s.containers,r],t):[...t,{name:n,configFiles:i,workingDir:o,containers:[r]}]},[]);var Ye=1e3,vn=e=>{let t=({Id:f})=>e.getContainer(f).stop(),r=({Id:f})=>e.getContainer(f).start(),n=({Id:f})=>e.getContainer(f).restart(),i=({Id:f})=>e.getContainer(f).remove();return{useImages:()=>{let[f,d]=(0,P.useState)(),[h,v]=(0,P.useState)(!1),[b,S]=(0,P.useState)(),p=(0,P.useRef)();return(0,P.useEffect)(()=>{async function w(){try{let m=await e.listImages();d(m)}catch(m){m instanceof Error&&S(m)}}return D(v,w)(),p.current=setInterval(w,Ye),()=>p.current&&clearInterval(p.current)},[]),{images:f,error:b,isLoading:h,removeImage:D(v,({Id:w})=>e.getImage(w).remove())}},useImageInfo:({Id:f})=>{let[d,h]=(0,P.useState)(),[v,b]=(0,P.useState)(!1),S=(0,P.useRef)();return(0,P.useEffect)(()=>{async function p(){let w=await e.getImage(f).inspect();h(w)}return D(b,p)(),S.current=setInterval(p,Ye),()=>S.current&&clearInterval(S.current)},[f]),{imageInfo:d,isLoading:v}},useContainers:()=>{let[f,d]=(0,P.useState)(),[h,v]=(0,P.useState)(!1),[b,S]=(0,P.useState)(),p=(0,P.useRef)();return(0,P.useEffect)(()=>{async function w(){try{let m=await e.listContainers({all:!0});d(m)}catch(m){m instanceof Error&&S(m)}}return D(v,w)(),p.current=setInterval(w,Ye),()=>p.current&&clearInterval(p.current)},[]),{containers:f,isLoading:h,error:b,startContainer:D(v,r),stopContainer:D(v,t),restartContainer:D(v,n),removeContainer:D(v,i)}},useContainerInfo:f=>{let[d,h]=(0,P.useState)(),[v,b]=(0,P.useState)(!1),S=(0,P.useRef)();return(0,P.useEffect)(()=>{async function p(){let w=await e.getContainer(f).inspect();h(w)}return D(b,p)(),S.current=setInterval(p,Ye),()=>S.current&&clearInterval(S.current)},[f]),{containerInfo:d,isLoading:v,startContainer:D(b,r),restartContainer:D(b,n),stopContainer:D(b,t),removeContainer:D(b,i)}},useProjects:()=>{let[f,d]=(0,P.useState)(),[h,v]=(0,P.useState)(!1),[b,S]=(0,P.useState)(),p=(0,P.useRef)();return(0,P.useEffect)(()=>{async function w(){try{let m=await e.listContainers({all:!0});d(yt(m))}catch(m){m instanceof Error&&S(m)}}return D(v,w)(),p.current=setInterval(w,Ye),()=>p.current&&clearInterval(p.current)},[]),{projects:f,isLoading:h,error:b,startProject:D(v,async w=>{await Promise.all(w.containers.filter(C=>!ie(C)).map(C=>e.getContainer(C.Id).start()));let m=await e.listContainers({all:!0});d(yt(m))}),stopProject:D(v,async w=>{await Promise.all(w.containers.filter(C=>ie(C)).map(C=>e.getContainer(C.Id).stop()));let m=await e.listContainers({all:!0});d(yt(m))})}},useCreateContainer:()=>{let[f,d]=(0,P.useState)(!1),[h,v]=(0,P.useState)();return{createContainer:async S=>{d(!0);try{await(await e.createContainer(S)).start()}catch(p){p instanceof Error&&v(p)}finally{d(!1)}},isLoading:f,error:h}}}};function D(e,t){return async r=>{e(!0);try{let n=await t(r);return e(!1),n}finally{e(!1)}}}var Is=require("@raycast/api"),cn=Ws(xs()),Ls=require("react"),qs=require("node:url"),Ns=()=>{let{socketPath:e}=(0,Is.getPreferenceValues)(),t=["http://","https://","tcp://"];return(0,Ls.useMemo)(()=>{if(t.some(r=>e?.startsWith(r))){let r=new qs.URL(e);return new cn.default({host:r.hostname,port:r.port||2375})}return new cn.default(e?{socketPath:e}:void 0)},[e])};var we=require("@raycast/api"),ir=require("react");var nr=require("react/jsx-runtime");function dn({error:e}){let[t,r]=(0,ir.useState)([]);(0,ir.useEffect)(()=>{async function i(){let o=await(0,we.getApplications)();r(o)}i()},[]);let n=t.find(({bundleId:i})=>i==="com.docker.docker");return(0,nr.jsx)(we.Detail,{markdown:ql(e),actions:or(e)&&n!==void 0?(0,nr.jsx)(we.ActionPanel,{children:(0,nr.jsx)(we.Action.Open,{title:"Launch Docker",target:n.path})}):null})}var ql=e=>{let t="Error message:\n\n```\n"+e.message+"\n```";return or(e)?["## \u26A0\uFE0F Error connecting to Docker",t].join(`
`):`## An Error Occurred:

${t}`};var U=require("react/jsx-runtime");function Ds(e){let t=Ns(),r=vn(t),{useContainers:n}=r,{containers:i,isLoading:o,error:s,startContainer:a,restartContainer:u,stopContainer:c,removeContainer:l}=n();return s?(0,U.jsx)(dn,{error:s}):(0,U.jsx)(x.List,{isLoading:o,children:Nl(i,e.projectFilter)?.map(f=>{let d=W(f);return(0,U.jsx)(x.List.Item,{keywords:[f.Id,d,f.Image],title:d,subtitle:f.Image,accessories:[{text:{value:f.State}}],icon:ie(f)?{source:"icon-container-running.png",tintColor:x.Color.Green}:{source:"icon-container.png",tintColor:x.Color.SecondaryText},actions:(0,U.jsxs)(x.ActionPanel,{children:[ie(f)&&(0,U.jsx)(x.Action,{title:"Stop Container",shortcut:{modifiers:["cmd","shift"],key:"w"},icon:{source:"icon-stop.png",tintColor:x.Color.PrimaryText},onAction:ee({action:()=>c(f),onSuccess:()=>`Container ${d} stopped`,onFailure:h=>Q(h,f)})}),ie(f)&&(0,U.jsx)(x.Action,{title:"Restart Container",icon:x.Icon.ArrowClockwise,shortcut:{modifiers:["opt"],key:"r"},onAction:ee({action:()=>u(f),onSuccess:()=>`Container ${d} restarted`,onFailure:h=>Q(h,f)})}),ie(f)&&(0,U.jsx)(x.Action.CopyToClipboard,{title:"Copy Container Id",shortcut:{modifiers:["cmd","shift"],key:"c"},content:f.Id}),!ie(f)&&(0,U.jsx)(x.Action,{title:"Start Container",shortcut:{modifiers:["cmd","shift"],key:"r"},icon:{source:"icon-start.png",tintColor:x.Color.PrimaryText},onAction:ee({action:()=>a(f),onSuccess:()=>`Container ${d} started`,onFailure:h=>Q(h,f)})}),(0,U.jsx)(x.Action.Push,{title:"Inspect",icon:{source:x.Icon.Binoculars},shortcut:{modifiers:["cmd"],key:"i"},target:(0,U.jsx)(sr,{docker:r,containerId:f.Id})}),(0,U.jsx)(x.Action,{title:"Remove Container",icon:x.Icon.Trash,style:x.Action.Style.Destructive,shortcut:x.Keyboard.Shortcut.Common.Remove,onAction:ee({action:()=>l(f),onSuccess:()=>`Container ${d} removed`,onFailure:h=>Q(h,f)})})]})},f.Id)})})}var Nl=(e,t)=>t===void 0||e===void 0?e:e.filter(r=>r.Labels["com.docker.compose.project"]===t);
/*! Bundled license information:

safe-buffer/index.js:
  (*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> *)
*/
