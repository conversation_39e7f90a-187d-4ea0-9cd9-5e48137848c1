{"version": 3, "sources": ["../node_modules/one-time/index.js", "../node_modules/say/platform/base.js", "../node_modules/say/platform/linux.js", "../node_modules/say/platform/darwin.js", "../node_modules/say/platform/win32.js", "../node_modules/say/index.js", "../src/history.tsx", "../src/actions/index.tsx", "../src/actions/copy.tsx", "../src/actions/preferences.tsx", "../src/actions/save.tsx", "../src/hooks/useHistory.tsx", "../src/hooks/useSavedChat.tsx", "../src/views/answer-detail.tsx"], "sourcesContent": ["'use strict';\n\n/**\n * Wrap callbacks to prevent double execution.\n *\n * @param {Function} fn Function that should only be called once.\n * @returns {Function} A wrapped callback which prevents execution.\n * @api public\n */\nmodule.exports = function one(fn) {\n  var called = 0\n    , value;\n\n  /**\n   * The function that prevents double execution.\n   *\n   * @api private\n   */\n  function onetime() {\n    if (called) return value;\n\n    called = 1;\n    value = fn.apply(this, arguments);\n    fn = null;\n\n    return value;\n  }\n\n  //\n  // To make debugging more easy we want to use the name of the supplied\n  // function. So when you look at the functions that are assigned to event\n  // listeners you don't see a load of `onetime` functions but actually the\n  // names of the functions that this module will call.\n  //\n  onetime.displayName = fn.displayName || fn.name || onetime.displayName || onetime.name;\n  return onetime;\n};\n", "const childProcess = require('child_process')\nconst once = require('one-time')\n\nclass SayPlatformBase {\n  constructor () {\n    this.child = null\n    this.baseSpeed = 0\n  }\n\n  /**\n   * Uses system libraries to speak text via the speakers.\n   *\n   * @param {string} text Text to be spoken\n   * @param {string|null} voice Name of voice to be spoken with\n   * @param {number|null} speed Speed of text (e.g. 1.0 for normal, 0.5 half, 2.0 double)\n   * @param {Function|null} callback A callback of type function(err) to return.\n   */\n  speak (text, voice, speed, callback) {\n    if (typeof callback !== 'function') {\n      callback = () => {}\n    }\n\n    callback = once(callback)\n\n    if (!text) {\n      return setImmediate(() => {\n        callback(new TypeError('say.speak(): must provide text parameter'))\n      })\n    }\n\n    let { command, args, pipedData, options } = this.buildSpeakCommand({ text, voice, speed })\n\n    this.child = childProcess.spawn(command, args, options)\n\n    this.child.stdin.setEncoding('ascii')\n    this.child.stderr.setEncoding('ascii')\n\n    if (pipedData) {\n      this.child.stdin.end(pipedData)\n    }\n\n    this.child.stderr.once('data', (data) => {\n      // we can't stop execution from this function\n      callback(new Error(data))\n    })\n\n    this.child.addListener('exit', (code, signal) => {\n      if (code === null || signal !== null) {\n        return callback(new Error(`say.speak(): could not talk, had an error [code: ${code}] [signal: ${signal}]`))\n      }\n\n      this.child = null\n\n      callback(null)\n    })\n  }\n\n  /**\n   * Uses system libraries to speak text via the speakers.\n   *\n   * @param {string} text Text to be spoken\n   * @param {string|null} voice Name of voice to be spoken with\n   * @param {number|null} speed Speed of text (e.g. 1.0 for normal, 0.5 half, 2.0 double)\n   * @param {string} filename Path to file to write audio to, e.g. \"greeting.wav\"\n   * @param {Function|null} callback A callback of type function(err) to return.\n   */\n  export (text, voice, speed, filename, callback) {\n    if (typeof callback !== 'function') {\n      callback = () => {}\n    }\n\n    callback = once(callback)\n\n    if (!text) {\n      return setImmediate(() => {\n        callback(new TypeError('say.export(): must provide text parameter'))\n      })\n    }\n\n    if (!filename) {\n      return setImmediate(() => {\n        callback(new TypeError('say.export(): must provide filename parameter'))\n      })\n    }\n\n    try {\n      var { command, args, pipedData, options } = this.buildExportCommand({ text, voice, speed, filename })\n    } catch (error) {\n      return setImmediate(() => {\n        callback(error)\n      })\n    }\n\n    this.child = childProcess.spawn(command, args, options)\n\n    this.child.stdin.setEncoding('ascii')\n    this.child.stderr.setEncoding('ascii')\n\n    if (pipedData) {\n      this.child.stdin.end(pipedData)\n    }\n\n    this.child.stderr.once('data', (data) => {\n      // we can't stop execution from this function\n      callback(new Error(data))\n    })\n\n    this.child.addListener('exit', (code, signal) => {\n      if (code === null || signal !== null) {\n        return callback(new Error(`say.export(): could not talk, had an error [code: ${code}] [signal: ${signal}]`))\n      }\n\n      this.child = null\n\n      callback(null)\n    })\n  }\n\n  /**\n   * Stops currently playing audio. There will be unexpected results if multiple audios are being played at once\n   *\n   * TODO: If two messages are being spoken simultaneously, childD points to new instance, no way to kill previous\n   *\n   * @param {Function|null} callback A callback of type function(err) to return.\n   */\n  stop (callback) {\n    if (typeof callback !== 'function') {\n      callback = () => {}\n    }\n\n    callback = once(callback)\n\n    if (!this.child) {\n      return setImmediate(() => {\n        callback(new Error('say.stop(): no speech to kill'))\n      })\n    }\n\n    this.runStopCommand()\n\n    this.child = null\n\n    callback(null)\n  }\n\n  convertSpeed (speed) {\n    return Math.ceil(this.baseSpeed * speed)\n  }\n\n  /**\n   * Get Installed voices on system\n   * @param {Function} callback A callback of type function(err,voices) to return.\n   */\n  getInstalledVoices (callback) {\n    if (typeof callback !== 'function') {\n      callback = () => {}\n    }\n    callback = once(callback)\n\n    let { command, args } = this.getVoices()\n    var voices = []\n    this.child = childProcess.spawn(command, args)\n\n    this.child.stdin.setEncoding('ascii')\n    this.child.stderr.setEncoding('ascii')\n\n    this.child.stderr.once('data', (data) => {\n      // we can't stop execution from this function\n      callback(new Error(data))\n    })\n    this.child.stdout.on('data', function (data) {\n      voices += data\n    })\n\n    this.child.addListener('exit', (code, signal) => {\n      if (code === null || signal !== null) {\n        return callback(new Error(`say.getInstalledVoices(): could not get installed voices, had an error [code: ${code}] [signal: ${signal}]`))\n      }\n      if (voices.length > 0) {\n        voices = voices.split('\\r\\n')\n        voices = (voices[voices.length - 1] === '') ? voices.slice(0, voices.length - 1) : voices\n      }\n      this.child = null\n\n      callback(null, voices)\n    })\n\n    this.child.stdin.end()\n  }\n}\n\nmodule.exports = SayPlatformBase\n", "const SayPlatformBase = require('./base.js')\n\nconst BASE_SPEED = 100\nconst COMMAND = 'festival'\n\nclass SayPlatformLinux extends SayPlatformBase {\n  constructor () {\n    super()\n    this.baseSpeed = BASE_SPEED\n  }\n\n  buildSpeakCommand ({ text, voice, speed }) {\n    let args = []\n    let pipedData = ''\n    let options = {}\n\n    args.push('--pipe')\n\n    if (speed) {\n      pipedData += `(Parameter.set 'Audio_Command \"aplay -q -c 1 -t raw -f s16 -r $(($SR*${this.convertSpeed(speed)}/100)) $FILE\") `\n    }\n\n    if (voice) {\n      pipedData += `(${voice}) `\n    }\n\n    pipedData += `(SayText \"${text}\")`\n\n    return { command: COMMAND, args, pipedData, options }\n  }\n\n  buildExportCommand ({ text, voice, speed, filename }) {\n    throw new Error(`say.export(): does not support platform ${this.platform}`)\n  }\n\n  runStopCommand () {\n    // TODO: Need to ensure the following is true for all users, not just me. Danger Zone!\n    // On my machine, original childD.pid process is completely gone. Instead there is now a\n    // childD.pid + 1 sh process. Kill it and nothing happens. There's also a childD.pid + 2\n    // aplay process. Kill that and the audio actually stops.\n    process.kill(this.child.pid + 2)\n  }\n\n  getVoices () {\n    throw new Error(`say.export(): does not support platform ${this.platform}`)\n  }\n}\n\nmodule.exports = SayPlatformLinux\n", "const SayPlatformBase = require('./base.js')\n\nconst BASE_SPEED = 175\nconst COMMAND = 'say'\n\nclass SayPlatformDarwin extends SayPlatformBase {\n  constructor () {\n    super()\n    this.baseSpeed = BASE_SPEED\n  }\n\n  buildSpeakCommand ({ text, voice, speed }) {\n    let args = []\n    let pipedData = ''\n    let options = {}\n\n    if (!voice) {\n      args.push(text)\n    } else {\n      args.push('-v', voice, text)\n    }\n\n    if (speed) {\n      args.push('-r', this.convertSpeed(speed))\n    }\n\n    return { command: COMMAND, args, pipedData, options }\n  }\n\n  buildExportCommand ({ text, voice, speed, filename }) {\n    let args = []\n    let pipedData = ''\n    let options = {}\n\n    if (!voice) {\n      args.push(text)\n    } else {\n      args.push('-v', voice, text)\n    }\n\n    if (speed) {\n      args.push('-r', this.convertSpeed(speed))\n    }\n\n    if (filename) {\n      args.push('-o', filename, '--data-format=LEF32@32000')\n    }\n\n    return { command: COMMAND, args, pipedData, options }\n  }\n\n  runStopCommand () {\n    this.child.stdin.pause()\n    this.child.kill()\n  }\n\n  getVoices () {\n    throw new Error(`say.export(): does not support platform ${this.platform}`)\n  }\n}\n\nmodule.exports = SayPlatformDarwin\n", "const childProcess = require('child_process')\n\nconst SayPlatformBase = require('./base.js')\n\nconst BASE_SPEED = 0 // Unsupported\nconst COMMAND = 'powershell'\n\nclass SayPlatformWin32 extends SayPlatformBase {\n  constructor () {\n    super()\n    this.baseSpeed = BASE_SPEED\n  }\n\n  buildSpeakCommand ({ text, voice, speed }) {\n    let args = []\n    let pipedData = ''\n    let options = {}\n\n    let psCommand = `Add-Type -AssemblyName System.speech;$speak = New-Object System.Speech.Synthesis.SpeechSynthesizer;`\n\n    if (voice) {\n      psCommand += `$speak.SelectVoice('${voice}');`\n    }\n\n    if (speed) {\n      let adjustedSpeed = this.convertSpeed(speed || 1)\n      psCommand += `$speak.Rate = ${adjustedSpeed};`\n    }\n\n    psCommand += `$speak.Speak([Console]::In.ReadToEnd())`\n\n    pipedData += text\n    args.push(psCommand)\n    options.shell = true\n\n    return { command: COMMAND, args, pipedData, options }\n  }\n\n  buildExportCommand ({ text, voice, speed, filename }) {\n    let args = []\n    let pipedData = ''\n    let options = {}\n\n    let psCommand = `Add-Type -AssemblyName System.speech;$speak = New-Object System.Speech.Synthesis.SpeechSynthesizer;`\n\n    if (voice) {\n      psCommand += `$speak.SelectVoice('${voice}');`\n    }\n\n    if (speed) {\n      let adjustedSpeed = this.convertSpeed(speed || 1)\n      psCommand += `$speak.Rate = ${adjustedSpeed};`\n    }\n\n    if (!filename) throw new Error('Filename must be provided in export();')\n    else {\n      psCommand += `$speak.SetOutputToWaveFile('${filename}');`\n    }\n\n    psCommand += `$speak.Speak([Console]::In.ReadToEnd());$speak.Dispose()`\n\n    pipedData += text\n    args.push(psCommand)\n    options.shell = true\n\n    return { command: COMMAND, args, pipedData, options }\n  }\n\n  runStopCommand () {\n    this.child.stdin.pause()\n    childProcess.exec(`taskkill /pid ${this.child.pid} /T /F`)\n  }\n\n  convertSpeed (speed) {\n    // Overriden to map playback speed (as a ratio) to Window's values (-10 to 10, zero meaning x1.0)\n    return Math.max(-10, Math.min(Math.round((9.0686 * Math.log(speed)) - 0.1806), 10))\n  }\n\n  getVoices () {\n    let args = []\n    let psCommand = 'Add-Type -AssemblyName System.speech;$speak = New-Object System.Speech.Synthesis.SpeechSynthesizer;$speak.GetInstalledVoices() | % {$_.VoiceInfo.Name}'\n    args.push(psCommand)\n    return { command: COMMAND, args }\n  }\n}\n\nmodule.exports = SayPlatformWin32\n", "const SayLinux = require('./platform/linux.js')\nconst SayMacos = require('./platform/darwin.js')\nconst SayWin32 = require('./platform/win32.js')\n\nconst MACOS = 'darwin'\nconst LINUX = 'linux'\nconst WIN32 = 'win32'\n\nclass Say {\n  constructor (platform) {\n    if (!platform) {\n      platform = process.platform\n    }\n\n    if (platform === MACOS) {\n      return new SayMacos()\n    } else if (platform === LINUX) {\n      return new SayLinux()\n    } else if (platform === WIN32) {\n      return new SayWin32()\n    }\n\n    throw new Error(`new Say(): unsupported platorm! ${platform}`)\n  }\n}\n\nmodule.exports = new Say() // Create a singleton automatically for backwards compatability\nmodule.exports.Say = Say // Allow users to `say = new Say.Say(platform)`\nmodule.exports.platforms = {\n  WIN32: WIN32,\n  MACOS: MACOS,\n  LINUX: LINUX\n}\n", "import { ActionPanel, Icon, List } from \"@raycast/api\";\nimport { useState } from \"react\";\nimport { DestructiveAction, TextToSpeechAction } from \"./actions\";\nimport { CopyActionSection } from \"./actions/copy\";\nimport { PreferencesActionSection } from \"./actions/preferences\";\nimport { SaveActionSection } from \"./actions/save\";\nimport { useHistory } from \"./hooks/useHistory\";\nimport { useSavedChat } from \"./hooks/useSavedChat\";\nimport { Chat } from \"./type\";\nimport { AnswerDetailView } from \"./views/answer-detail\";\n\nexport default function History() {\n  const savedChat = useSavedChat();\n  const history = useHistory();\n  const [searchText, setSearchText] = useState<string>(\"\");\n  const [selectedAnswerId, setSelectedAnswerId] = useState<string | null>(null);\n\n  const getActionPanel = (chat: Chat) => (\n    <ActionPanel>\n      <CopyActionSection answer={chat.answer} question={chat.question} />\n      <SaveActionSection onSaveAnswerAction={() => savedChat.add(chat)} />\n      <ActionPanel.Section title=\"Output\">\n        <TextToSpeechAction content={chat.answer} />\n      </ActionPanel.Section>\n      <ActionPanel.Section title=\"Delete\">\n        <DestructiveAction\n          title=\"Remove\"\n          dialog={{\n            title: \"Are you sure you want to remove this answer from your history?\",\n          }}\n          onAction={() => history.remove(chat)}\n        />\n        <DestructiveAction\n          title=\"Clear History\"\n          dialog={{\n            title: \"Are you sure you want to clear your history?\",\n          }}\n          onAction={() => history.clear()}\n          shortcut={{ modifiers: [\"cmd\", \"shift\"], key: \"delete\" }}\n        />\n      </ActionPanel.Section>\n      <PreferencesActionSection />\n    </ActionPanel>\n  );\n\n  const sortedHistory = history.data.sort(\n    (a, b) => new Date(b.created_at ?? 0).getTime() - new Date(a.created_at ?? 0).getTime(),\n  );\n\n  const filteredHistory = sortedHistory\n    .filter((value, index, self) => index === self.findIndex((history) => history.id === value.id))\n    .filter((answer) => {\n      if (searchText === \"\") {\n        return true;\n      }\n      return (\n        answer.question.toLowerCase().includes(searchText.toLowerCase()) ||\n        answer.answer.toLowerCase().includes(searchText.toLowerCase())\n      );\n    });\n\n  return (\n    <List\n      isShowingDetail={filteredHistory.length === 0 ? false : true}\n      isLoading={history.isLoading}\n      filtering={false}\n      throttle={false}\n      selectedItemId={selectedAnswerId || undefined}\n      onSelectionChange={(id) => {\n        if (id !== selectedAnswerId) {\n          setSelectedAnswerId(id);\n        }\n      }}\n      searchBarPlaceholder=\"Search answer/question...\"\n      searchText={searchText}\n      onSearchTextChange={setSearchText}\n    >\n      {history.data.length === 0 ? (\n        <List.EmptyView\n          title=\"No history\"\n          description=\"Your recent questions will be showed up here\"\n          icon={Icon.Stars}\n        />\n      ) : (\n        <List.Section title=\"Recent\" subtitle={filteredHistory.length.toLocaleString()}>\n          {filteredHistory.map((answer) => (\n            <List.Item\n              id={answer.id}\n              key={answer.id}\n              title={answer.question}\n              accessories={[{ text: new Date(answer.created_at ?? 0).toLocaleDateString() }]}\n              detail={<AnswerDetailView chat={answer} />}\n              actions={answer && selectedAnswerId === answer.id ? getActionPanel(answer) : undefined}\n            />\n          ))}\n        </List.Section>\n      )}\n    </List>\n  );\n}\n", "import { Action, Alert, confirmA<PERSON>t, Icon, Image, Keyboard } from \"@raycast/api\";\nimport say from \"say\";\n\nexport const PrimaryAction = ({ title, onAction }: { title: string; onAction: () => void }) => (\n  <Action title={title} icon={Icon.ArrowRight} onAction={onAction} />\n);\n\nexport const PinAction = ({\n  title,\n  isPinned,\n  onAction,\n}: {\n  title: string;\n  isPinned: boolean;\n  onAction: () => void;\n}) => <Action title={title} icon={isPinned ? Icon.PinDisabled : Icon.Pin} onAction={onAction} />;\n\nexport const CopyToClipboardAction = (props: Action.CopyToClipboard.Props) => (\n  <Action.CopyToClipboard icon={Icon.CopyClipboard} {...props} />\n);\n\nexport const TextToSpeechAction = ({ content }: { content: string }) => (\n  <Action\n    icon={Icon.SpeechBubble}\n    title=\"Speak\"\n    onAction={() => {\n      say.stop();\n      say.speak(content);\n    }}\n    shortcut={{ modifiers: [\"cmd\", \"shift\"], key: \"p\" }}\n  />\n);\n\nexport const SaveAction = ({\n  onAction,\n  title,\n  modifiers,\n}: {\n  onAction: () => void;\n  title: string;\n  modifiers: Keyboard.KeyModifier[];\n}) => <Action icon={Icon.Star} title={title} onAction={onAction} shortcut={{ modifiers, key: \"s\" }} />;\n\nexport const SaveAsSnippetAction = ({ text, name }: { text: string; name: string }) => (\n  <Action.CreateSnippet\n    icon={Icon.Snippets}\n    title=\"Save as a Snippet\"\n    snippet={{ text, name }}\n    shortcut={{ modifiers: [\"cmd\"], key: \"n\" }}\n  />\n);\n\nexport const DestructiveAction = ({\n  icon = Icon.Trash,\n  title,\n  dialog,\n  onAction,\n  shortcut = { modifiers: [\"ctrl\"], key: \"x\" },\n}: {\n  icon?: Image.ImageLike;\n  title: string;\n  dialog: { title?: string; message?: string; primaryButton?: string };\n  onAction: () => void;\n  shortcut?: Keyboard.Shortcut | null;\n}) => (\n  <Action\n    style={Action.Style.Destructive}\n    icon={icon}\n    title={title}\n    onAction={async () => {\n      await confirmAlert({\n        title: dialog.title ?? title,\n        message: dialog.message ?? \"This action cannot be undone\",\n        icon,\n        primaryAction: {\n          title: dialog.primaryButton ?? title,\n          style: Alert.ActionStyle.Destructive,\n          onAction,\n        },\n      });\n    }}\n    shortcut={shortcut}\n  />\n);\n", "import { ActionPanel } from \"@raycast/api\";\nimport { CopyToClipboardAction } from \"./index\";\n\nexport const CopyActionSection = ({ question, answer }: { question: string; answer: string }) => (\n  <ActionPanel.Section title=\"Copy\">\n    <CopyToClipboardAction title=\"Copy Answer\" content={answer} />\n    <CopyToClipboardAction title=\"Copy Question\" content={question} />\n  </ActionPanel.Section>\n);\n", "import { Action, ActionPanel, Icon, openExtensionPreferences } from \"@raycast/api\";\n\nexport const PreferencesActionSection = () => (\n  <ActionPanel.Section title=\"Preferences\">\n    <Action icon={Icon.Gear} title=\"Open Extension Preferences\" onAction={openExtensionPreferences} />\n  </ActionPanel.Section>\n);\n", "import { ActionPanel } from \"@raycast/api\";\nimport { SaveAction, SaveAsSnippetAction } from \"./index\";\n\nexport const SaveActionSection = ({\n  onSaveConversationAction,\n  onSaveAnswerAction,\n  snippet,\n}: {\n  onSaveConversationAction?: () => void;\n  onSaveAnswerAction: () => void;\n  snippet?: { text: string; name: string };\n}) => (\n  <ActionPanel.Section title=\"Save\">\n    {onSaveConversationAction && (\n      <SaveAction title=\"Save Conversation\" onAction={onSaveConversationAction} modifiers={[\"cmd\"]} />\n    )}\n    <SaveAction\n      title=\"Save Answer\"\n      onAction={onSaveAnswerAction}\n      modifiers={onSaveConversationAction ? [\"cmd\", \"shift\"] : [\"cmd\"]}\n    />\n    {snippet && <SaveAsSnippetAction text={snippet.text} name={snippet.name} />}\n  </ActionPanel.Section>\n);\n", "import { LocalStorage, showToast, Toast } from \"@raycast/api\";\nimport { useCallback, useEffect, useMemo, useState } from \"react\";\nimport { Chat, HistoryHook } from \"../type\";\n\nexport function useHistory(): HistoryHook {\n  const [data, setData] = useState<Chat[]>([]);\n  const [isLoading, setLoading] = useState<boolean>(true);\n\n  useEffect(() => {\n    (async () => {\n      const storedHistory = await LocalStorage.getItem<string>(\"history\");\n\n      if (storedHistory) {\n        setData((previous) => [...previous, ...JSON.parse(storedHistory)]);\n      }\n      setLoading(false);\n    })();\n  }, []);\n\n  useEffect(() => {\n    LocalStorage.setItem(\"history\", JSON.stringify(data));\n  }, [data]);\n\n  const add = useCallback(\n    async (chat: Chat) => {\n      setData([...data, chat]);\n    },\n    [setData, data],\n  );\n\n  const remove = useCallback(\n    async (answer: Chat) => {\n      const toast = await showToast({\n        title: \"Removing answer...\",\n        style: Toast.Style.Animated,\n      });\n      const newHistory: Chat[] = data.filter((item) => item.id !== answer.id);\n      setData(newHistory);\n      toast.title = \"Answer removed!\";\n      toast.style = Toast.Style.Success;\n    },\n    [setData, data],\n  );\n\n  const clear = useCallback(async () => {\n    const toast = await showToast({\n      title: \"Clearing history...\",\n      style: Toast.Style.Animated,\n    });\n    setData([]);\n    toast.title = \"History cleared!\";\n    toast.style = Toast.Style.Success;\n  }, [setData]);\n\n  return useMemo(() => ({ data, isLoading, add, remove, clear }), [data, isLoading, add, remove, clear]);\n}\n", "import { LocalStorage, showToast, Toast } from \"@raycast/api\";\nimport { useCallback, useEffect, useMemo, useState } from \"react\";\nimport { Chat, SavedChat, SavedChatHook } from \"../type\";\n\nexport function useSavedChat(): SavedChatHook {\n  const [data, setData] = useState<SavedChat[]>([]);\n  const [isLoading, setLoading] = useState<boolean>(true);\n\n  useEffect(() => {\n    (async () => {\n      const storedSavedChats = await LocalStorage.getItem<string>(\"savedChats\");\n\n      if (storedSavedChats) {\n        setData((previous) => [...previous, ...JSON.parse(storedSavedChats)]);\n      }\n      setLoading(false);\n    })();\n  }, []);\n\n  useEffect(() => {\n    LocalStorage.setItem(\"savedChats\", JSON.stringify(data));\n  }, [data]);\n\n  const add = useCallback(\n    async (chat: Chat) => {\n      const toast = await showToast({\n        title: \"Saving your answer...\",\n        style: Toast.Style.Animated,\n      });\n      const newSavedChat: SavedChat = { ...chat, saved_at: new Date().toISOString() };\n      setData([...data, newSavedChat]);\n      toast.title = \"Answer saved!\";\n      toast.style = Toast.Style.Success;\n    },\n    [setData, data],\n  );\n\n  const remove = useCallback(\n    async (chat: Chat) => {\n      const toast = await showToast({\n        title: \"Unsaving your answer...\",\n        style: Toast.Style.Animated,\n      });\n      const newSavedChats = data.filter((savedAnswer) => savedAnswer.id !== chat.id);\n      setData(newSavedChats);\n      toast.title = \"Answer unsaved!\";\n      toast.style = Toast.Style.Success;\n    },\n    [setData, data],\n  );\n\n  const clear = useCallback(async () => {\n    const toast = await showToast({\n      title: \"Clearing your saved answers...\",\n      style: Toast.Style.Animated,\n    });\n    setData([]);\n    toast.title = \"Saved answers cleared!\";\n    toast.style = Toast.Style.Success;\n  }, [setData]);\n\n  return useMemo(() => ({ data, isLoading, add, remove, clear }), [data, isLoading, add, remove, clear]);\n}\n", "import { List } from \"@raycast/api\";\nimport { Chat } from \"../type\";\n\nexport const AnswerDetailView = (props: { chat: Chat; streamData?: Chat | undefined }) => {\n  const { chat, streamData } = props;\n  const isStreaming = streamData && streamData.id === chat.id;\n\n  const width = Math.floor(430 / Math.min(Math.max(chat.files?.length ?? 0, 1), 2));\n\n  const images: string =\n    chat.files\n      ?.map((file) => {\n        const fileURI = encodeURI(`file://${file}?raycast-width=${width}`);\n        return `![](${fileURI})`;\n      })\n      .join(\"\\n\") || \"\";\n\n  const markdown = `${\n    isStreaming ? streamData?.answer : chat.answer\n  }\\n\\`\\`\\`\\n${chat.question.trimEnd()}\\n\\`\\`\\`\\n${images}\\n\\n`;\n\n  return <List.Item.Detail markdown={markdown} />;\n};\n"], "mappings": "ioBAAA,IAAAA,EAAAC,EAAA,CAAAC,GAAAC,IAAA,cASAA,EAAO,QAAU,SAAaC,EAAI,CAChC,IAAIC,EAAS,EACTC,EAOJ,SAASC,GAAU,CACjB,OAAIF,IAEJA,EAAS,EACTC,EAAQF,EAAG,MAAM,KAAM,SAAS,EAChCA,EAAK,MAEEE,CACT,CAQA,OAAAC,EAAQ,YAAcH,EAAG,aAAeA,EAAG,MAAQG,EAAQ,aAAeA,EAAQ,KAC3EA,CACT,ICpCA,IAAAC,EAAAC,EAAA,CAAAC,GAAAC,IAAA,KAAMC,EAAe,QAAQ,eAAe,EACtCC,EAAO,IAEPC,EAAN,KAAsB,CACpB,aAAe,CACb,KAAK,MAAQ,KACb,KAAK,UAAY,CACnB,CAUA,MAAOC,EAAMC,EAAOC,EAAOC,EAAU,CAOnC,GANI,OAAOA,GAAa,aACtBA,EAAW,IAAM,CAAC,GAGpBA,EAAWL,EAAKK,CAAQ,EAEpB,CAACH,EACH,OAAO,aAAa,IAAM,CACxBG,EAAS,IAAI,UAAU,0CAA0C,CAAC,CACpE,CAAC,EAGH,GAAI,CAAE,QAAAC,EAAS,KAAAC,EAAM,UAAAC,EAAW,QAAAC,CAAQ,EAAI,KAAK,kBAAkB,CAAE,KAAAP,EAAM,MAAAC,EAAO,MAAAC,CAAM,CAAC,EAEzF,KAAK,MAAQL,EAAa,MAAMO,EAASC,EAAME,CAAO,EAEtD,KAAK,MAAM,MAAM,YAAY,OAAO,EACpC,KAAK,MAAM,OAAO,YAAY,OAAO,EAEjCD,GACF,KAAK,MAAM,MAAM,IAAIA,CAAS,EAGhC,KAAK,MAAM,OAAO,KAAK,OAASE,GAAS,CAEvCL,EAAS,IAAI,MAAMK,CAAI,CAAC,CAC1B,CAAC,EAED,KAAK,MAAM,YAAY,OAAQ,CAACC,EAAMC,IAAW,CAC/C,GAAID,IAAS,MAAQC,IAAW,KAC9B,OAAOP,EAAS,IAAI,MAAM,oDAAoDM,CAAI,cAAcC,CAAM,GAAG,CAAC,EAG5G,KAAK,MAAQ,KAEbP,EAAS,IAAI,CACf,CAAC,CACH,CAWA,OAAQH,EAAMC,EAAOC,EAAOS,EAAUR,EAAU,CAO9C,GANI,OAAOA,GAAa,aACtBA,EAAW,IAAM,CAAC,GAGpBA,EAAWL,EAAKK,CAAQ,EAEpB,CAACH,EACH,OAAO,aAAa,IAAM,CACxBG,EAAS,IAAI,UAAU,2CAA2C,CAAC,CACrE,CAAC,EAGH,GAAI,CAACQ,EACH,OAAO,aAAa,IAAM,CACxBR,EAAS,IAAI,UAAU,+CAA+C,CAAC,CACzE,CAAC,EAGH,GAAI,CACF,GAAI,CAAE,QAAAC,EAAS,KAAAC,EAAM,UAAAC,EAAW,QAAAC,CAAQ,EAAI,KAAK,mBAAmB,CAAE,KAAAP,EAAM,MAAAC,EAAO,MAAAC,EAAO,SAAAS,CAAS,CAAC,CACtG,OAASC,EAAO,CACd,OAAO,aAAa,IAAM,CACxBT,EAASS,CAAK,CAChB,CAAC,CACH,CAEA,KAAK,MAAQf,EAAa,MAAMO,EAASC,EAAME,CAAO,EAEtD,KAAK,MAAM,MAAM,YAAY,OAAO,EACpC,KAAK,MAAM,OAAO,YAAY,OAAO,EAEjCD,GACF,KAAK,MAAM,MAAM,IAAIA,CAAS,EAGhC,KAAK,MAAM,OAAO,KAAK,OAASE,GAAS,CAEvCL,EAAS,IAAI,MAAMK,CAAI,CAAC,CAC1B,CAAC,EAED,KAAK,MAAM,YAAY,OAAQ,CAACC,EAAMC,IAAW,CAC/C,GAAID,IAAS,MAAQC,IAAW,KAC9B,OAAOP,EAAS,IAAI,MAAM,qDAAqDM,CAAI,cAAcC,CAAM,GAAG,CAAC,EAG7G,KAAK,MAAQ,KAEbP,EAAS,IAAI,CACf,CAAC,CACH,CASA,KAAMA,EAAU,CAOd,GANI,OAAOA,GAAa,aACtBA,EAAW,IAAM,CAAC,GAGpBA,EAAWL,EAAKK,CAAQ,EAEpB,CAAC,KAAK,MACR,OAAO,aAAa,IAAM,CACxBA,EAAS,IAAI,MAAM,+BAA+B,CAAC,CACrD,CAAC,EAGH,KAAK,eAAe,EAEpB,KAAK,MAAQ,KAEbA,EAAS,IAAI,CACf,CAEA,aAAcD,EAAO,CACnB,OAAO,KAAK,KAAK,KAAK,UAAYA,CAAK,CACzC,CAMA,mBAAoBC,EAAU,CACxB,OAAOA,GAAa,aACtBA,EAAW,IAAM,CAAC,GAEpBA,EAAWL,EAAKK,CAAQ,EAExB,GAAI,CAAE,QAAAC,EAAS,KAAAC,CAAK,EAAI,KAAK,UAAU,EACvC,IAAIQ,EAAS,CAAC,EACd,KAAK,MAAQhB,EAAa,MAAMO,EAASC,CAAI,EAE7C,KAAK,MAAM,MAAM,YAAY,OAAO,EACpC,KAAK,MAAM,OAAO,YAAY,OAAO,EAErC,KAAK,MAAM,OAAO,KAAK,OAASG,GAAS,CAEvCL,EAAS,IAAI,MAAMK,CAAI,CAAC,CAC1B,CAAC,EACD,KAAK,MAAM,OAAO,GAAG,OAAQ,SAAUA,EAAM,CAC3CK,GAAUL,CACZ,CAAC,EAED,KAAK,MAAM,YAAY,OAAQ,CAACC,EAAMC,IAAW,CAC/C,GAAID,IAAS,MAAQC,IAAW,KAC9B,OAAOP,EAAS,IAAI,MAAM,iFAAiFM,CAAI,cAAcC,CAAM,GAAG,CAAC,EAErIG,EAAO,OAAS,IAClBA,EAASA,EAAO,MAAM;AAAA,CAAM,EAC5BA,EAAUA,EAAOA,EAAO,OAAS,CAAC,IAAM,GAAMA,EAAO,MAAM,EAAGA,EAAO,OAAS,CAAC,EAAIA,GAErF,KAAK,MAAQ,KAEbV,EAAS,KAAMU,CAAM,CACvB,CAAC,EAED,KAAK,MAAM,MAAM,IAAI,CACvB,CACF,EAEAjB,EAAO,QAAUG,IC/LjB,IAAAe,EAAAC,EAAA,CAAAC,GAAAC,IAAA,KAAMC,GAAkB,IAElBC,GAAa,IACbC,GAAU,WAEVC,EAAN,cAA+BH,EAAgB,CAC7C,aAAe,CACb,MAAM,EACN,KAAK,UAAYC,EACnB,CAEA,kBAAmB,CAAE,KAAAG,EAAM,MAAAC,EAAO,MAAAC,CAAM,EAAG,CACzC,IAAIC,EAAO,CAAC,EACRC,EAAY,GACZC,EAAU,CAAC,EAEf,OAAAF,EAAK,KAAK,QAAQ,EAEdD,IACFE,GAAa,wEAAwE,KAAK,aAAaF,CAAK,CAAC,mBAG3GD,IACFG,GAAa,IAAIH,CAAK,MAGxBG,GAAa,aAAaJ,CAAI,KAEvB,CAAE,QAASF,GAAS,KAAAK,EAAM,UAAAC,EAAW,QAAAC,CAAQ,CACtD,CAEA,mBAAoB,CAAE,KAAAL,EAAM,MAAAC,EAAO,MAAAC,EAAO,SAAAI,CAAS,EAAG,CACpD,MAAM,IAAI,MAAM,2CAA2C,KAAK,QAAQ,EAAE,CAC5E,CAEA,gBAAkB,CAKhB,QAAQ,KAAK,KAAK,MAAM,IAAM,CAAC,CACjC,CAEA,WAAa,CACX,MAAM,IAAI,MAAM,2CAA2C,KAAK,QAAQ,EAAE,CAC5E,CACF,EAEAX,EAAO,QAAUI,IChDjB,IAAAQ,EAAAC,EAAA,CAAAC,GAAAC,IAAA,KAAMC,GAAkB,IAElBC,GAAa,IACbC,EAAU,MAEVC,EAAN,cAAgCH,EAAgB,CAC9C,aAAe,CACb,MAAM,EACN,KAAK,UAAYC,EACnB,CAEA,kBAAmB,CAAE,KAAAG,EAAM,MAAAC,EAAO,MAAAC,CAAM,EAAG,CACzC,IAAIC,EAAO,CAAC,EACRC,EAAY,GACZC,EAAU,CAAC,EAEf,OAAKJ,EAGHE,EAAK,KAAK,KAAMF,EAAOD,CAAI,EAF3BG,EAAK,KAAKH,CAAI,EAKZE,GACFC,EAAK,KAAK,KAAM,KAAK,aAAaD,CAAK,CAAC,EAGnC,CAAE,QAASJ,EAAS,KAAAK,EAAM,UAAAC,EAAW,QAAAC,CAAQ,CACtD,CAEA,mBAAoB,CAAE,KAAAL,EAAM,MAAAC,EAAO,MAAAC,EAAO,SAAAI,CAAS,EAAG,CACpD,IAAIH,EAAO,CAAC,EACRC,EAAY,GACZC,EAAU,CAAC,EAEf,OAAKJ,EAGHE,EAAK,KAAK,KAAMF,EAAOD,CAAI,EAF3BG,EAAK,KAAKH,CAAI,EAKZE,GACFC,EAAK,KAAK,KAAM,KAAK,aAAaD,CAAK,CAAC,EAGtCI,GACFH,EAAK,KAAK,KAAMG,EAAU,2BAA2B,EAGhD,CAAE,QAASR,EAAS,KAAAK,EAAM,UAAAC,EAAW,QAAAC,CAAQ,CACtD,CAEA,gBAAkB,CAChB,KAAK,MAAM,MAAM,MAAM,EACvB,KAAK,MAAM,KAAK,CAClB,CAEA,WAAa,CACX,MAAM,IAAI,MAAM,2CAA2C,KAAK,QAAQ,EAAE,CAC5E,CACF,EAEAV,EAAO,QAAUI,IC7DjB,IAAAQ,EAAAC,EAAA,CAAAC,GAAAC,IAAA,KAAMC,GAAe,QAAQ,eAAe,EAEtCC,GAAkB,IAElBC,GAAa,EACbC,EAAU,aAEVC,EAAN,cAA+BH,EAAgB,CAC7C,aAAe,CACb,MAAM,EACN,KAAK,UAAYC,EACnB,CAEA,kBAAmB,CAAE,KAAAG,EAAM,MAAAC,EAAO,MAAAC,CAAM,EAAG,CACzC,IAAIC,EAAO,CAAC,EACRC,EAAY,GACZC,EAAU,CAAC,EAEXC,EAAY,sGAMhB,GAJIL,IACFK,GAAa,uBAAuBL,CAAK,OAGvCC,EAAO,CACT,IAAIK,EAAgB,KAAK,aAAaL,GAAS,CAAC,EAChDI,GAAa,iBAAiBC,CAAa,GAC7C,CAEA,OAAAD,GAAa,0CAEbF,GAAaJ,EACbG,EAAK,KAAKG,CAAS,EACnBD,EAAQ,MAAQ,GAET,CAAE,QAASP,EAAS,KAAAK,EAAM,UAAAC,EAAW,QAAAC,CAAQ,CACtD,CAEA,mBAAoB,CAAE,KAAAL,EAAM,MAAAC,EAAO,MAAAC,EAAO,SAAAM,CAAS,EAAG,CACpD,IAAIL,EAAO,CAAC,EACRC,EAAY,GACZC,EAAU,CAAC,EAEXC,EAAY,sGAMhB,GAJIL,IACFK,GAAa,uBAAuBL,CAAK,OAGvCC,EAAO,CACT,IAAIK,EAAgB,KAAK,aAAaL,GAAS,CAAC,EAChDI,GAAa,iBAAiBC,CAAa,GAC7C,CAEA,GAAKC,EAEHF,GAAa,+BAA+BE,CAAQ,UAFvC,OAAM,IAAI,MAAM,wCAAwC,EAKvE,OAAAF,GAAa,2DAEbF,GAAaJ,EACbG,EAAK,KAAKG,CAAS,EACnBD,EAAQ,MAAQ,GAET,CAAE,QAASP,EAAS,KAAAK,EAAM,UAAAC,EAAW,QAAAC,CAAQ,CACtD,CAEA,gBAAkB,CAChB,KAAK,MAAM,MAAM,MAAM,EACvBV,GAAa,KAAK,iBAAiB,KAAK,MAAM,GAAG,QAAQ,CAC3D,CAEA,aAAcO,EAAO,CAEnB,OAAO,KAAK,IAAI,IAAK,KAAK,IAAI,KAAK,MAAO,OAAS,KAAK,IAAIA,CAAK,EAAK,KAAM,EAAG,EAAE,CAAC,CACpF,CAEA,WAAa,CACX,IAAIC,EAAO,CAAC,EAEZ,OAAAA,EAAK,KADW,wJACG,EACZ,CAAE,QAASL,EAAS,KAAAK,CAAK,CAClC,CACF,EAEAT,EAAO,QAAUK,ICtFjB,IAAAU,GAAAC,EAAA,CAAAC,GAAAC,IAAA,KAAMC,GAAW,IACXC,GAAW,IACXC,GAAW,IAEXC,EAAQ,SACRC,EAAQ,QACRC,GAAQ,QAERC,EAAN,KAAU,CACR,YAAaC,EAAU,CAKrB,GAJKA,IACHA,EAAW,QAAQ,UAGjBA,IAAaJ,EACf,OAAO,IAAIF,GACN,GAAIM,IAAaH,EACtB,OAAO,IAAIJ,GACN,GAAIO,IAAaF,GACtB,OAAO,IAAIH,GAGb,MAAM,IAAI,MAAM,mCAAmCK,CAAQ,EAAE,CAC/D,CACF,EAEAR,EAAO,QAAU,IAAIO,EACrBP,EAAO,QAAQ,IAAMO,EACrBP,EAAO,QAAQ,UAAY,CACzB,MAAOM,GACP,MAAOF,EACP,MAAOC,CACT,IChCA,IAAAI,GAAA,GAAAC,GAAAD,GAAA,aAAAE,KAAA,eAAAC,GAAAH,IAAA,IAAAI,EAAwC,wBACxCC,EAAyB,iBCDzB,IAAAC,EAAmE,wBACnEC,EAAgB,SAGdC,EAAA,6BAaK,IAAMC,EAAyBC,MACpC,OAAC,SAAO,gBAAP,CAAuB,KAAM,OAAK,cAAgB,GAAGA,EAAO,EAGlDC,GAAqB,CAAC,CAAE,QAAAC,CAAQ,OAC3C,OAAC,UACC,KAAM,OAAK,aACX,MAAM,QACN,SAAU,IAAM,CACd,EAAAC,QAAI,KAAK,EACT,EAAAA,QAAI,MAAMD,CAAO,CACnB,EACA,SAAU,CAAE,UAAW,CAAC,MAAO,OAAO,EAAG,IAAK,GAAI,EACpD,EAGWE,EAAa,CAAC,CACzB,SAAAC,EACA,MAAAC,EACA,UAAAC,CACF,OAIM,OAAC,UAAO,KAAM,OAAK,KAAM,MAAOD,EAAO,SAAUD,EAAU,SAAU,CAAE,UAAAE,EAAW,IAAK,GAAI,EAAG,EAEvFC,GAAsB,CAAC,CAAE,KAAAC,EAAM,KAAAC,CAAK,OAC/C,OAAC,SAAO,cAAP,CACC,KAAM,OAAK,SACX,MAAM,oBACN,QAAS,CAAE,KAAAD,EAAM,KAAAC,CAAK,EACtB,SAAU,CAAE,UAAW,CAAC,KAAK,EAAG,IAAK,GAAI,EAC3C,EAGWC,EAAoB,CAAC,CAChC,KAAAC,EAAO,OAAK,MACZ,MAAAN,EACA,OAAAO,EACA,SAAAR,EACA,SAAAS,EAAW,CAAE,UAAW,CAAC,MAAM,EAAG,IAAK,GAAI,CAC7C,OAOE,OAAC,UACC,MAAO,SAAO,MAAM,YACpB,KAAMF,EACN,MAAON,EACP,SAAU,SAAY,CACpB,QAAM,gBAAa,CACjB,MAAOO,EAAO,OAASP,EACvB,QAASO,EAAO,SAAW,+BAC3B,KAAAD,EACA,cAAe,CACb,MAAOC,EAAO,eAAiBP,EAC/B,MAAO,QAAM,YAAY,YACzB,SAAAD,CACF,CACF,CAAC,CACH,EACA,SAAUS,EACZ,EClFF,IAAAC,GAA4B,wBAI1B,IAAAC,EAAA,6BADWC,GAAoB,CAAC,CAAE,SAAAC,EAAU,OAAAC,CAAO,OACnD,QAAC,eAAY,QAAZ,CAAoB,MAAM,OACzB,oBAACC,EAAA,CAAsB,MAAM,cAAc,QAASD,EAAQ,KAC5D,OAACC,EAAA,CAAsB,MAAM,gBAAgB,QAASF,EAAU,GAClE,ECPF,IAAAG,EAAoE,wBAIhEC,EAAA,6BAFSC,GAA2B,OACtC,OAAC,cAAY,QAAZ,CAAoB,MAAM,cACzB,mBAAC,UAAO,KAAM,OAAK,KAAM,MAAM,6BAA6B,SAAU,2BAA0B,EAClG,ECLF,IAAAC,GAA4B,wBAY1B,IAAAC,EAAA,6BATWC,GAAoB,CAAC,CAChC,yBAAAC,EACA,mBAAAC,EACA,QAAAC,CACF,OAKE,QAAC,eAAY,QAAZ,CAAoB,MAAM,OACxB,UAAAF,MACC,OAACG,EAAA,CAAW,MAAM,oBAAoB,SAAUH,EAA0B,UAAW,CAAC,KAAK,EAAG,KAEhG,OAACG,EAAA,CACC,MAAM,cACN,SAAUF,EACV,UAAWD,EAA2B,CAAC,MAAO,OAAO,EAAI,CAAC,KAAK,EACjE,EACCE,MAAW,OAACE,GAAA,CAAoB,KAAMF,EAAQ,KAAM,KAAMA,EAAQ,KAAM,GAC3E,ECtBF,IAAAG,EAA+C,wBAC/CC,EAA0D,iBAGnD,SAASC,IAA0B,CACxC,GAAM,CAACC,EAAMC,CAAO,KAAI,YAAiB,CAAC,CAAC,EACrC,CAACC,EAAWC,CAAU,KAAI,YAAkB,EAAI,KAEtD,aAAU,IAAM,EACb,SAAY,CACX,IAAMC,EAAgB,MAAM,eAAa,QAAgB,SAAS,EAE9DA,GACFH,EAASI,GAAa,CAAC,GAAGA,EAAU,GAAG,KAAK,MAAMD,CAAa,CAAC,CAAC,EAEnED,EAAW,EAAK,CAClB,GAAG,CACL,EAAG,CAAC,CAAC,KAEL,aAAU,IAAM,CACd,eAAa,QAAQ,UAAW,KAAK,UAAUH,CAAI,CAAC,CACtD,EAAG,CAACA,CAAI,CAAC,EAET,IAAMM,KAAM,eACV,MAAOC,GAAe,CACpBN,EAAQ,CAAC,GAAGD,EAAMO,CAAI,CAAC,CACzB,EACA,CAACN,EAASD,CAAI,CAChB,EAEMQ,KAAS,eACb,MAAOC,GAAiB,CACtB,IAAMC,EAAQ,QAAM,aAAU,CAC5B,MAAO,qBACP,MAAO,QAAM,MAAM,QACrB,CAAC,EACKC,EAAqBX,EAAK,OAAQY,GAASA,EAAK,KAAOH,EAAO,EAAE,EACtER,EAAQU,CAAU,EAClBD,EAAM,MAAQ,kBACdA,EAAM,MAAQ,QAAM,MAAM,OAC5B,EACA,CAACT,EAASD,CAAI,CAChB,EAEMa,KAAQ,eAAY,SAAY,CACpC,IAAMH,EAAQ,QAAM,aAAU,CAC5B,MAAO,sBACP,MAAO,QAAM,MAAM,QACrB,CAAC,EACDT,EAAQ,CAAC,CAAC,EACVS,EAAM,MAAQ,mBACdA,EAAM,MAAQ,QAAM,MAAM,OAC5B,EAAG,CAACT,CAAO,CAAC,EAEZ,SAAO,WAAQ,KAAO,CAAE,KAAAD,EAAM,UAAAE,EAAW,IAAAI,EAAK,OAAAE,EAAQ,MAAAK,CAAM,GAAI,CAACb,EAAME,EAAWI,EAAKE,EAAQK,CAAK,CAAC,CACvG,CCvDA,IAAAC,EAA+C,wBAC/CC,EAA0D,iBAGnD,SAASC,IAA8B,CAC5C,GAAM,CAACC,EAAMC,CAAO,KAAI,YAAsB,CAAC,CAAC,EAC1C,CAACC,EAAWC,CAAU,KAAI,YAAkB,EAAI,KAEtD,aAAU,IAAM,EACb,SAAY,CACX,IAAMC,EAAmB,MAAM,eAAa,QAAgB,YAAY,EAEpEA,GACFH,EAASI,GAAa,CAAC,GAAGA,EAAU,GAAG,KAAK,MAAMD,CAAgB,CAAC,CAAC,EAEtED,EAAW,EAAK,CAClB,GAAG,CACL,EAAG,CAAC,CAAC,KAEL,aAAU,IAAM,CACd,eAAa,QAAQ,aAAc,KAAK,UAAUH,CAAI,CAAC,CACzD,EAAG,CAACA,CAAI,CAAC,EAET,IAAMM,KAAM,eACV,MAAOC,GAAe,CACpB,IAAMC,EAAQ,QAAM,aAAU,CAC5B,MAAO,wBACP,MAAO,QAAM,MAAM,QACrB,CAAC,EACKC,EAA0B,CAAE,GAAGF,EAAM,SAAU,IAAI,KAAK,EAAE,YAAY,CAAE,EAC9EN,EAAQ,CAAC,GAAGD,EAAMS,CAAY,CAAC,EAC/BD,EAAM,MAAQ,gBACdA,EAAM,MAAQ,QAAM,MAAM,OAC5B,EACA,CAACP,EAASD,CAAI,CAChB,EAEMU,KAAS,eACb,MAAOH,GAAe,CACpB,IAAMC,EAAQ,QAAM,aAAU,CAC5B,MAAO,0BACP,MAAO,QAAM,MAAM,QACrB,CAAC,EACKG,EAAgBX,EAAK,OAAQY,GAAgBA,EAAY,KAAOL,EAAK,EAAE,EAC7EN,EAAQU,CAAa,EACrBH,EAAM,MAAQ,kBACdA,EAAM,MAAQ,QAAM,MAAM,OAC5B,EACA,CAACP,EAASD,CAAI,CAChB,EAEMa,KAAQ,eAAY,SAAY,CACpC,IAAML,EAAQ,QAAM,aAAU,CAC5B,MAAO,iCACP,MAAO,QAAM,MAAM,QACrB,CAAC,EACDP,EAAQ,CAAC,CAAC,EACVO,EAAM,MAAQ,yBACdA,EAAM,MAAQ,QAAM,MAAM,OAC5B,EAAG,CAACP,CAAO,CAAC,EAEZ,SAAO,WAAQ,KAAO,CAAE,KAAAD,EAAM,UAAAE,EAAW,IAAAI,EAAK,OAAAI,EAAQ,MAAAG,CAAM,GAAI,CAACb,EAAME,EAAWI,EAAKI,EAAQG,CAAK,CAAC,CACvG,CC9DA,IAAAC,GAAqB,wBAqBZC,GAAA,6BAlBIC,GAAoBC,GAAyD,CACxF,GAAM,CAAE,KAAAC,EAAM,WAAAC,CAAW,EAAIF,EACvBG,EAAcD,GAAcA,EAAW,KAAOD,EAAK,GAEnDG,EAAQ,KAAK,MAAM,IAAM,KAAK,IAAI,KAAK,IAAIH,EAAK,OAAO,QAAU,EAAG,CAAC,EAAG,CAAC,CAAC,EAE1EI,EACJJ,EAAK,OACD,IAAKK,GAEE,OADS,UAAU,UAAUA,CAAI,kBAAkBF,CAAK,EAAE,CAC5C,GACtB,EACA,KAAK;AAAA,CAAI,GAAK,GAEbG,EAAW,GACfJ,EAAcD,GAAY,OAASD,EAAK,MAC1C;AAAA;AAAA,EAAaA,EAAK,SAAS,QAAQ,CAAC;AAAA;AAAA,EAAaI,CAAM;AAAA;AAAA,EAEvD,SAAO,QAAC,QAAK,KAAK,OAAV,CAAiB,SAAUE,EAAU,CAC/C,EPHM,IAAAC,EAAA,6BARS,SAARC,IAA2B,CAChC,IAAMC,EAAYC,GAAa,EACzBC,EAAUC,GAAW,EACrB,CAACC,EAAYC,CAAa,KAAI,YAAiB,EAAE,EACjD,CAACC,EAAkBC,CAAmB,KAAI,YAAwB,IAAI,EAEtEC,EAAkBC,MACtB,QAAC,eACC,oBAACC,GAAA,CAAkB,OAAQD,EAAK,OAAQ,SAAUA,EAAK,SAAU,KACjE,OAACE,GAAA,CAAkB,mBAAoB,IAAMX,EAAU,IAAIS,CAAI,EAAG,KAClE,OAAC,cAAY,QAAZ,CAAoB,MAAM,SACzB,mBAACG,GAAA,CAAmB,QAASH,EAAK,OAAQ,EAC5C,KACA,QAAC,cAAY,QAAZ,CAAoB,MAAM,SACzB,oBAACI,EAAA,CACC,MAAM,SACN,OAAQ,CACN,MAAO,gEACT,EACA,SAAU,IAAMX,EAAQ,OAAOO,CAAI,EACrC,KACA,OAACI,EAAA,CACC,MAAM,gBACN,OAAQ,CACN,MAAO,8CACT,EACA,SAAU,IAAMX,EAAQ,MAAM,EAC9B,SAAU,CAAE,UAAW,CAAC,MAAO,OAAO,EAAG,IAAK,QAAS,EACzD,GACF,KACA,OAACY,GAAA,EAAyB,GAC5B,EAOIC,EAJgBb,EAAQ,KAAK,KACjC,CAACc,EAAGC,IAAM,IAAI,KAAKA,EAAE,YAAc,CAAC,EAAE,QAAQ,EAAI,IAAI,KAAKD,EAAE,YAAc,CAAC,EAAE,QAAQ,CACxF,EAGG,OAAO,CAACE,EAAOC,EAAOC,IAASD,IAAUC,EAAK,UAAWlB,IAAYA,GAAQ,KAAOgB,EAAM,EAAE,CAAC,EAC7F,OAAQG,GACHjB,IAAe,GACV,GAGPiB,EAAO,SAAS,YAAY,EAAE,SAASjB,EAAW,YAAY,CAAC,GAC/DiB,EAAO,OAAO,YAAY,EAAE,SAASjB,EAAW,YAAY,CAAC,CAEhE,EAEH,SACE,OAAC,QACC,gBAAiBW,EAAgB,SAAW,EAC5C,UAAWb,EAAQ,UACnB,UAAW,GACX,SAAU,GACV,eAAgBI,GAAoB,OACpC,kBAAoBgB,GAAO,CACrBA,IAAOhB,GACTC,EAAoBe,CAAE,CAE1B,EACA,qBAAqB,4BACrB,WAAYlB,EACZ,mBAAoBC,EAEnB,SAAAH,EAAQ,KAAK,SAAW,KACvB,OAAC,OAAK,UAAL,CACC,MAAM,aACN,YAAY,+CACZ,KAAM,OAAK,MACb,KAEA,OAAC,OAAK,QAAL,CAAa,MAAM,SAAS,SAAUa,EAAgB,OAAO,eAAe,EAC1E,SAAAA,EAAgB,IAAKM,MACpB,OAAC,OAAK,KAAL,CACC,GAAIA,EAAO,GAEX,MAAOA,EAAO,SACd,YAAa,CAAC,CAAE,KAAM,IAAI,KAAKA,EAAO,YAAc,CAAC,EAAE,mBAAmB,CAAE,CAAC,EAC7E,UAAQ,OAACE,GAAA,CAAiB,KAAMF,EAAQ,EACxC,QAASA,GAAUf,IAAqBe,EAAO,GAAKb,EAAea,CAAM,EAAI,QAJxEA,EAAO,EAKd,CACD,EACH,EAEJ,CAEJ", "names": ["require_one_time", "__commonJSMin", "exports", "module", "fn", "called", "value", "onetime", "require_base", "__commonJSMin", "exports", "module", "childProcess", "once", "SayPlatformBase", "text", "voice", "speed", "callback", "command", "args", "pipedData", "options", "data", "code", "signal", "filename", "error", "voices", "require_linux", "__commonJSMin", "exports", "module", "SayPlatformBase", "BASE_SPEED", "COMMAND", "SayPlatformLinux", "text", "voice", "speed", "args", "pipedData", "options", "filename", "require_darwin", "__commonJSMin", "exports", "module", "SayPlatformBase", "BASE_SPEED", "COMMAND", "SayPlatformDarwin", "text", "voice", "speed", "args", "pipedData", "options", "filename", "require_win32", "__commonJSMin", "exports", "module", "childProcess", "SayPlatformBase", "BASE_SPEED", "COMMAND", "SayPlatformWin32", "text", "voice", "speed", "args", "pipedData", "options", "psCommand", "adjustedSpeed", "filename", "require_say", "__commonJSMin", "exports", "module", "SayLinux", "SayMacos", "SayWin32", "MACOS", "LINUX", "WIN32", "Say", "platform", "history_exports", "__export", "History", "__toCommonJS", "import_api", "import_react", "import_api", "import_say", "import_jsx_runtime", "CopyToClipboardAction", "props", "TextToSpeechAction", "content", "say", "SaveAction", "onAction", "title", "modifiers", "SaveAsSnippetAction", "text", "name", "DestructiveAction", "icon", "dialog", "shortcut", "import_api", "import_jsx_runtime", "CopyActionSection", "question", "answer", "CopyToClipboardAction", "import_api", "import_jsx_runtime", "PreferencesActionSection", "import_api", "import_jsx_runtime", "SaveActionSection", "onSaveConversationAction", "onSaveAnswerAction", "snippet", "SaveAction", "SaveAsSnippetAction", "import_api", "import_react", "useHistory", "data", "setData", "isLoading", "setLoading", "storedHistory", "previous", "add", "chat", "remove", "answer", "toast", "newHistory", "item", "clear", "import_api", "import_react", "useSavedChat", "data", "setData", "isLoading", "setLoading", "storedSavedChats", "previous", "add", "chat", "toast", "newSavedChat", "remove", "newSavedChats", "savedAnswer", "clear", "import_api", "import_jsx_runtime", "AnswerDetailView", "props", "chat", "streamData", "isStreaming", "width", "images", "file", "markdown", "import_jsx_runtime", "History", "savedChat", "useSavedChat", "history", "useHistory", "searchText", "setSearchText", "selectedAnswerId", "setSelectedAnswerId", "getActionPanel", "chat", "CopyActionSection", "SaveActionSection", "TextToSpeechAction", "DestructiveAction", "PreferencesActionSection", "filteredHistory", "a", "b", "value", "index", "self", "answer", "id", "AnswerDetailView"]}