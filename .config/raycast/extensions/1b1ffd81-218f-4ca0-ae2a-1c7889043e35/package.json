{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "perplexity", "title": "Perplexity", "description": "Query Perplexity from within Raycast", "icon": "perplexity-logo.png", "author": "third774", "categories": ["Developer Tools"], "license": "MIT", "commands": [{"name": "ask-perplexity", "title": "Ask Perplexity", "description": "Opens Perplexity in a browser tab with your search query in the URL", "mode": "view", "arguments": [{"name": "query", "type": "text", "description": "The query to search for", "placeholder": "Ask Anything..."}, {"name": "copilot", "type": "text", "description": "Use Copilot Mode?", "placeholder": "Use Copilot? (y/n)"}], "preferences": [{"label": "Fallback with Copilot enabled", "description": "This will enable Perplexity's Copilot mode when using this command as a Fallback", "default": false, "type": "checkbox", "name": "fallback<PERSON><PERSON><PERSON><PERSON>", "required": false}]}], "dependencies": {"@raycast/api": "^1.60.1", "@raycast/utils": "^1.10.1"}, "devDependencies": {"@raycast/eslint-config": "^1.0.6", "@types/node": "18.18.4", "@types/react": "18.2.27", "eslint": "^8.51.0", "prettier": "^3.0.3", "typescript": "^5.2.2"}, "scripts": {"build": "ray build -e dist", "dev": "ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "publish": "npx @raycast/api@latest publish"}}