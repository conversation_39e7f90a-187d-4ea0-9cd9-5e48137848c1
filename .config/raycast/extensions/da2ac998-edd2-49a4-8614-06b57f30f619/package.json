{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "pomodoro", "title": "Pomodoro", "description": "Pomodoro extension with menu-bar timer", "icon": "icon.png", "crossExtensions": ["yakitrak/do-not-disturb"], "author": "<PERSON><PERSON><PERSON><PERSON>", "contributors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brodelp", "<PERSON>san_thapa", "mikikiv", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nathan<PERSON><PERSON>", "cchalop1", "hayden_barnes", "j3lte", "litomore", "ridemountainpig", "<PERSON><PERSON><PERSON><PERSON>"], "keywords": ["pomodoro", "timer"], "categories": ["Productivity"], "license": "MIT", "commands": [{"name": "pomodoro-menu-bar", "title": "Show Pomodoro in Menu Bar", "subtitle": "Control timers from menu-bar", "description": "Start, pause, stop or continue pomodoro intervals", "mode": "menu-bar", "interval": "10s"}, {"name": "pomodoro-control-timer", "title": "Control Pomodoro Timer", "subtitle": "Start / Stop Pomodoro Timer", "description": "Start, pause, stop or continue pomodoro intervals", "mode": "view"}, {"name": "stats-pomodoro-timer", "title": "View Pomodoro Stats", "mode": "view", "description": "Shows the stats of the Pomodoro timer", "preferences": [{"name": "showWeeklyStats", "type": "checkbox", "required": false, "title": "Weekly Stats", "label": "Show weekly stats", "description": "Shows the weekly stats of the Pomodoro timer", "default": false}, {"name": "showDailyStats", "type": "checkbox", "required": false, "title": "Daily Stats", "label": "Show Daily stats", "description": "Shows the daily stats of the Pomodoro timer", "default": false}]}, {"name": "slack-pomodoro-menu-bar", "title": "<PERSON><PERSON>ck Pomodoro in Menu Bar", "subtitle": "Control timers from menu-bar and updates Slack status", "description": "Start, pause, stop or continue pomodoro intervals and update Slack status", "mode": "menu-bar", "interval": "10s", "disabledByDefault": true}, {"name": "slack-pomodoro-control-timer", "title": "Slack Control Pomodoro Timer", "subtitle": "Start / Stop Pomodoro Timer and update Slack status", "description": "Start, pause, stop or continue pomodoro intervals and update Slack status", "mode": "view", "disabledByDefault": true}], "tools": [{"name": "start-timer", "title": "Start Timer", "description": "Starts a new timer", "input": {"type": "object", "properties": {"type": {"type": "string", "description": "Interval type. Use `short-break` by default", "enum": ["focus", "short-break", "long-break"]}, "duration": {"type": "number", "description": "Duration in seconds.\nThere are default durations for each type set by the user, use this argument only to set custom duration"}}, "required": ["type"]}, "confirmation": false}, {"name": "stop-timer", "title": "Stop Timer", "description": "Stops any running timer", "confirmation": false}, {"name": "pause-timer", "title": "Pause Timer", "description": "Pauses the pomodoro timer", "confirmation": false}, {"name": "continue-timer", "title": "Continue Timer", "description": "Continues the paused pomodoro timer", "confirmation": false}], "ai": {"evals": [{"input": "@pomodoro start a long timer", "mocks": {"start-timer": "Started long-break timer"}, "expected": [{"callsTool": {"arguments": {"type": "long-break"}, "name": "start-timer"}}]}, {"input": "@pomodoro start a focus timer", "mocks": {"start-timer": "Started focus timer"}, "expected": [{"callsTool": {"arguments": {"type": "focus"}, "name": "start-timer"}}]}, {"input": "@pomodoro start a break", "mocks": {"start-timer": "Started short-break timer"}, "expected": [{"callsTool": {"name": "start-timer", "arguments": {"type": "short-break"}}}]}, {"expected": [{"callsTool": {"name": "stop-timer"}}], "input": "@pomodoro stop timer", "mocks": {"stop-timer": "Timer stopped"}}, {"mocks": {"pause-timer": "Timer paused"}, "input": "@pomodoro pause break", "expected": [{"callsTool": {"name": "pause-timer"}}]}, {"mocks": {"continue-timer": "Timer continued"}, "expected": [{"callsTool": {"name": "continue-timer"}}], "input": "@pomodoro continue the timer"}, {"input": "@pomodoro start a 4min 30 sec timer", "mocks": {"start-timer": {"id": 17, "length": 270, "parts": [{"startedAt": 1737715952}], "type": "short-break"}}, "expected": [{"callsTool": {"name": "start-timer", "arguments": {"duration": 270, "type": "short-break"}}}]}]}, "preferences": [{"name": "enableTimeOnMenuBar", "type": "checkbox", "required": false, "title": "<PERSON><PERSON>", "description": "Shows time on the Menu Bar", "default": true, "label": "Show time on Menu Bar"}, {"name": "hideTimeWhenStopped", "type": "checkbox", "required": false, "title": "Hide Time When Stopped", "description": "Hide time on the Menu Bar when the timer is stopped", "default": true, "label": "Hide the time when stopped"}, {"name": "enableFocusWhileFocused", "title": "Enable <PERSON> Do Not Disturb while Focused", "type": "checkbox", "label": "Do Not Disturb while focused", "required": false, "description": "Uses the do-not-disturb extension to enable Do Not Disturb mode when starting a focus interval. Disables Do Not Disturb mode when the focus interval ends."}, {"name": "focusIntervalDuration", "type": "dropdown", "required": true, "title": "Focus Interval Duration", "description": "Interval duration, minutes", "default": "25", "data": [{"value": "1", "title": "1:00"}, {"value": "3", "title": "3:00"}, {"value": "5", "title": "5:00"}, {"value": "10", "title": "10:00"}, {"value": "15", "title": "15:00"}, {"value": "20", "title": "20:00"}, {"value": "25", "title": "25:00"}, {"value": "30", "title": "30:00"}, {"value": "35", "title": "35:00"}, {"value": "40", "title": "40:00"}, {"value": "45", "title": "45:00"}, {"value": "50", "title": "50:00"}, {"value": "55", "title": "55:00"}, {"value": "60", "title": "60:00"}, {"value": "90", "title": "90:00"}]}, {"name": "shortBreakIntervalDuration", "type": "dropdown", "required": true, "title": "Short Break Duration", "description": "Interval duration, minutes", "default": "5", "data": [{"value": "1", "title": "1:00"}, {"value": "3", "title": "3:00"}, {"value": "5", "title": "5:00"}, {"value": "10", "title": "10:00"}, {"value": "15", "title": "15:00"}, {"value": "20", "title": "20:00"}, {"value": "25", "title": "25:00"}, {"value": "30", "title": "30:00"}, {"value": "35", "title": "35:00"}, {"value": "40", "title": "40:00"}, {"value": "45", "title": "45:00"}, {"value": "50", "title": "50:00"}, {"value": "55", "title": "55:00"}, {"value": "60", "title": "60:00"}]}, {"name": "longBreakIntervalDuration", "type": "dropdown", "required": true, "title": "Long Break Duration", "description": "Interval duration, minutes", "default": "20", "data": [{"value": "1", "title": "1:00"}, {"value": "3", "title": "3:00"}, {"value": "5", "title": "5:00"}, {"value": "10", "title": "10:00"}, {"value": "15", "title": "15:00"}, {"value": "20", "title": "20:00"}, {"value": "25", "title": "25:00"}, {"value": "30", "title": "30:00"}, {"value": "35", "title": "35:00"}, {"value": "40", "title": "40:00"}, {"value": "45", "title": "45:00"}, {"value": "50", "title": "50:00"}, {"value": "55", "title": "55:00"}, {"value": "60", "title": "60:00"}]}, {"name": "longBreakStartThreshold", "type": "dropdown", "required": false, "title": "Long Break Start Threshold", "description": "Pomodor<PERSON> cycles after which you want to take a long break", "default": "4", "data": [{"title": "2", "value": "2"}, {"title": "3", "value": "3"}, {"title": "4", "value": "4"}, {"title": "5", "value": "5"}, {"title": "6", "value": "6"}, {"title": "7", "value": "7"}, {"title": "8", "value": "8"}]}, {"name": "enable<PERSON>on<PERSON>tti", "type": "checkbox", "required": false, "title": "Show Confetti (Deprecated)", "description": "Shows confetti when interval finishes", "default": false, "label": "Shows confetti when interval finishes"}, {"name": "enableQuote", "type": "checkbox", "required": false, "title": "Show Quote", "description": "Shows a random quote from zenquotes.io when interval finishes", "default": false, "label": "Shows quote when interval finishes"}, {"name": "sound", "description": "Play sound", "type": "dropdown", "required": false, "title": "Play Completion Sound", "default": "", "data": [{"title": "No Sound", "value": ""}, {"title": "Submarine", "value": "Submarine"}, {"title": "Tink", "value": "Tink"}, {"title": "<PERSON>", "value": "<PERSON>"}]}, {"name": "enableImage", "type": "checkbox", "required": false, "title": "Show Image", "description": "Shows the image configured below when interval finishes", "default": true, "label": "Shows image when interval finishes"}, {"name": "completionImage", "type": "textfield", "required": false, "title": "Default Image Link", "description": "Image on interval completion, URL", "default": "https://media0.giphy.com/media/ZBn3ZRvCbWz2PS3Rbg/200.gif"}, {"name": "giphy<PERSON>ey", "type": "textfield", "required": false, "title": "Giphy API Key", "description": "Use an API key from <PERSON><PERSON><PERSON> to get random images on interval completion. Get your API key here: https://developers.giphy.com/docs/api#quick-start-guide"}, {"name": "giphyTag", "type": "textfield", "required": false, "title": "<PERSON><PERSON><PERSON>", "description": "Tag to use for Giphy API. Default is 'success'.", "default": "success"}, {"name": "giphy<PERSON>ating", "type": "dropdown", "required": false, "title": "<PERSON><PERSON><PERSON>", "description": "Rating to use for Giphy API. Default is 'g'.", "default": "g", "data": [{"value": "g", "title": "G"}, {"value": "pg", "title": "PG"}, {"value": "pg-13", "title": "PG-13"}, {"value": "r", "title": "R"}]}], "dependencies": {"@raycast/api": "^1.86.1", "@raycast/utils": "^1.18.1", "node-fetch": "^3.3.2", "raycast-cross-extension": "^0.2.3"}, "devDependencies": {"@raycast/eslint-config": "^1.0.11", "@types/node": "^22.10.1", "@types/react": "^18.3.12", "eslint": "^8.57.1", "prettier": "^3.4.1", "react": "^18.3.1", "typescript": "^5.7.2"}, "scripts": {"build": "ray build -e dist", "dev": "ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "publish": "ray publish"}}