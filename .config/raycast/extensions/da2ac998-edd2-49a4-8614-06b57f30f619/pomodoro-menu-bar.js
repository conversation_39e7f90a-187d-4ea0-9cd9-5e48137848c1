"use strict";var Hs=Object.create;var lr=Object.defineProperty;var Ys=Object.getOwnPropertyDescriptor;var zs=Object.getOwnPropertyNames;var Vs=Object.getPrototypeOf,Js=Object.prototype.hasOwnProperty;var p=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports),Xs=(e,r)=>{for(var t in r)lr(e,t,{get:r[t],enumerable:!0})},nn=(e,r,t,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let i of zs(r))!Js.call(e,i)&&i!==t&&lr(e,i,{get:()=>r[i],enumerable:!(n=Ys(r,i))||n.enumerable});return e};var Ks=(e,r,t)=>(t=e!=null?Hs(Vs(e)):{},nn(r||!e||!e.__esModule?lr(t,"default",{value:e,enumerable:!0}):t,e)),Zs=e=>nn(lr({},"__esModule",{value:!0}),e);var sn=p((Wl,Yr)=>{"use strict";var an=(e,...r)=>new Promise(t=>{t(e(...r))});Yr.exports=an;Yr.exports.default=an});var un=p((Hl,zr)=>{"use strict";var Qs=sn(),on=e=>{if(!((Number.isInteger(e)||e===1/0)&&e>0))return Promise.reject(new TypeError("Expected `concurrency` to be a number from 1 and up"));let r=[],t=0,n=()=>{t--,r.length>0&&r.shift()()},i=(o,u,...f)=>{t++;let l=Qs(o,...f);u(l),l.then(n,n)},a=(o,u,...f)=>{t<e?i(o,u,...f):r.push(i.bind(null,o,u,...f))},s=(o,...u)=>new Promise(f=>a(o,f,...u));return Object.defineProperties(s,{activeCount:{get:()=>t},pendingCount:{get:()=>r.length},clearQueue:{value:()=>{r.length=0}}}),s};zr.exports=on;zr.exports.default=on});var fn=p((Yl,Vr)=>{"use strict";var cn=un(),fr=class extends Error{constructor(r){super(),this.value=r}},eo=async(e,r)=>r(await e),ro=async e=>{let r=await Promise.all(e);if(r[1]===!0)throw new fr(r[0]);return!1},ln=async(e,r,t)=>{t={concurrency:1/0,preserveOrder:!0,...t};let n=cn(t.concurrency),i=[...e].map(s=>[s,n(eo,s,r)]),a=cn(t.preserveOrder?1:1/0);try{await Promise.all(i.map(s=>a(ro,s)))}catch(s){if(s instanceof fr)return s.value;throw s}};Vr.exports=ln;Vr.exports.default=ln});var gn=p((zl,Jr)=>{"use strict";var pn=require("path"),pr=require("fs"),{promisify:dn}=require("util"),to=fn(),no=dn(pr.stat),io=dn(pr.lstat),hn={directory:"isDirectory",file:"isFile"};function mn({type:e}){if(!(e in hn))throw new Error(`Invalid type specified: ${e}`)}var vn=(e,r)=>e===void 0||r[hn[e]]();Jr.exports=async(e,r)=>{r={cwd:process.cwd(),type:"file",allowSymlinks:!0,...r},mn(r);let t=r.allowSymlinks?no:io;return to(e,async n=>{try{let i=await t(pn.resolve(r.cwd,n));return vn(r.type,i)}catch{return!1}},r)};Jr.exports.sync=(e,r)=>{r={cwd:process.cwd(),allowSymlinks:!0,type:"file",...r},mn(r);let t=r.allowSymlinks?pr.statSync:pr.lstatSync;for(let n of e)try{let i=t(pn.resolve(r.cwd,n));if(vn(r.type,i))return n}catch{}}});var yn=p((Vl,Xr)=>{"use strict";var bn=require("fs"),{promisify:ao}=require("util"),so=ao(bn.access);Xr.exports=async e=>{try{return await so(e),!0}catch{return!1}};Xr.exports.sync=e=>{try{return bn.accessSync(e),!0}catch{return!1}}});var Cn=p((Jl,Oe)=>{"use strict";var pe=require("path"),dr=gn(),Ln=yn(),Kr=Symbol("findUp.stop");Oe.exports=async(e,r={})=>{let t=pe.resolve(r.cwd||""),{root:n}=pe.parse(t),i=[].concat(e),a=async s=>{if(typeof e!="function")return dr(i,s);let o=await e(s.cwd);return typeof o=="string"?dr([o],s):o};for(;;){let s=await a({...r,cwd:t});if(s===Kr)return;if(s)return pe.resolve(t,s);if(t===n)return;t=pe.dirname(t)}};Oe.exports.sync=(e,r={})=>{let t=pe.resolve(r.cwd||""),{root:n}=pe.parse(t),i=[].concat(e),a=s=>{if(typeof e!="function")return dr.sync(i,s);let o=e(s.cwd);return typeof o=="string"?dr.sync([o],s):o};for(;;){let s=a({...r,cwd:t});if(s===Kr)return;if(s)return pe.resolve(t,s);if(t===n)return;t=pe.dirname(t)}};Oe.exports.exists=Ln;Oe.exports.sync.exists=Ln.sync;Oe.exports.stop=Kr});var Sn=p((Xl,wn)=>{"use strict";wn.exports=function(r){return r?r instanceof Array||Array.isArray(r)||r.length>=0&&r.splice instanceof Function:!1}});var xn=p((Kl,Pn)=>{"use strict";var oo=require("util"),uo=Sn(),Zr=function(r,t){(!r||r.constructor!==String)&&(t=r||{},r=Error.name);var n=function i(a){if(!this)return new i(a);a=a instanceof Error?a.message:a||this.message,Error.call(this,a),Error.captureStackTrace(this,n),this.name=r,Object.defineProperty(this,"message",{configurable:!0,enumerable:!1,get:function(){var l=a.split(/\r?\n/g);for(var h in t)if(t.hasOwnProperty(h)){var b=t[h];"message"in b&&(l=b.message(this[h],l)||l,uo(l)||(l=[l]))}return l.join(`
`)},set:function(l){a=l}});var s=null,o=Object.getOwnPropertyDescriptor(this,"stack"),u=o.get,f=o.value;delete o.value,delete o.writable,o.set=function(l){s=l},o.get=function(){var l=(s||(u?u.call(this):f)).split(/\r?\n+/g);s||(l[0]=this.name+": "+this.message);var h=1;for(var b in t)if(t.hasOwnProperty(b)){var x=t[b];if("line"in x){var m=x.line(this[b]);m&&l.splice(h++,0,"    "+m)}"stack"in x&&x.stack(this[b],l)}return l.join(`
`)},Object.defineProperty(this,"stack",o)};return Object.setPrototypeOf?(Object.setPrototypeOf(n.prototype,Error.prototype),Object.setPrototypeOf(n,Error)):oo.inherits(n,Error),n};Zr.append=function(e,r){return{message:function(t,n){return t=t||r,t&&(n[0]+=" "+e.replace("%s",t.toString())),n}}};Zr.line=function(e,r){return{line:function(t){return t=t||r,t?e.replace("%s",t.toString()):null}}};Pn.exports=Zr});var Dn=p((Zl,On)=>{"use strict";var co=e=>{let r=e.charCodeAt(0).toString(16).toUpperCase();return"0x"+(r.length%2?"0":"")+r},lo=(e,r,t)=>{if(!r)return{message:e.message+" while parsing empty string",position:0};let n=e.message.match(/^Unexpected token (.) .*position\s+(\d+)/i),i=n?+n[2]:e.message.match(/^Unexpected end of JSON.*/i)?r.length-1:null,a=n?e.message.replace(/^Unexpected token ./,`Unexpected token ${JSON.stringify(n[1])} (${co(n[1])})`):e.message;if(i!=null){let s=i<=t?0:i-t,o=i+t>=r.length?r.length:i+t,u=(s===0?"":"...")+r.slice(s,o)+(o===r.length?"":"...");return{message:a+` while parsing ${r===u?"":"near "}${JSON.stringify(u)}`,position:i}}else return{message:a+` while parsing '${r.slice(0,t*2)}'`,position:0}},hr=class extends SyntaxError{constructor(r,t,n,i){n=n||20;let a=lo(r,t,n);super(a.message),Object.assign(this,a),this.code="EJSONPARSE",this.systemError=r,Error.captureStackTrace(this,i||this.constructor)}get name(){return this.constructor.name}set name(r){}get[Symbol.toStringTag](){return this.constructor.name}},fo=Symbol.for("indent"),po=Symbol.for("newline"),ho=/^\s*[{\[]((?:\r?\n)+)([\s\t]*)/,mo=/^(?:\{\}|\[\])((?:\r?\n)+)?$/,mr=(e,r,t)=>{let n=En(e);t=t||20;try{let[,i=`
`,a="  "]=n.match(mo)||n.match(ho)||[,"",""],s=JSON.parse(n,r);return s&&typeof s=="object"&&(s[po]=i,s[fo]=a),s}catch(i){if(typeof e!="string"&&!Buffer.isBuffer(e)){let a=Array.isArray(e)&&e.length===0;throw Object.assign(new TypeError(`Cannot parse ${a?"an empty array":String(e)}`),{code:"EJSONPARSE",systemError:i})}throw new hr(i,n,t,mr)}},En=e=>String(e).replace(/^\uFEFF/,"");On.exports=mr;mr.JSONParseError=hr;mr.noExceptions=(e,r)=>{try{return JSON.parse(En(e),r)}catch{}}});var An=p($e=>{"use strict";$e.__esModule=!0;$e.LinesAndColumns=void 0;var vr=`
`,In="\r",Nn=function(){function e(r){this.string=r;for(var t=[0],n=0;n<r.length;)switch(r[n]){case vr:n+=vr.length,t.push(n);break;case In:n+=In.length,r[n]===vr&&(n+=vr.length),t.push(n);break;default:n++;break}this.offsets=t}return e.prototype.locationForIndex=function(r){if(r<0||r>this.string.length)return null;for(var t=0,n=this.offsets;n[t+1]<=r;)t++;var i=r-n[t];return{line:t,column:i}},e.prototype.indexForLocation=function(r){var t=r.line,n=r.column;return t<0||t>=this.offsets.length||n<0||n>this.lengthOfLine(t)?null:this.offsets[t]+n},e.prototype.lengthOfLine=function(r){var t=this.offsets[r],n=r===this.offsets.length-1?this.string.length:this.offsets[r+1];return n-t},e}();$e.LinesAndColumns=Nn;$e.default=Nn});var Rn=p(gr=>{Object.defineProperty(gr,"__esModule",{value:!0});gr.default=/((['"])(?:(?!\2|\\).|\\(?:\r\n|[\s\S]))*(\2)?|`(?:[^`\\$]|\\[\s\S]|\$(?!\{)|\$\{(?:[^{}]|\{[^}]*\}?)*\}?)*(`)?)|(\/\/.*)|(\/\*(?:[^*]|\*(?!\/))*(\*\/)?)|(\/(?!\*)(?:\[(?:(?![\]\\]).|\\.)*\]|(?![\/\]\\]).|\\.)+\/(?:(?!\s*(?:\b|[\u0080-\uFFFF$\\'"~({]|[+\-!](?!=)|\.?\d))|[gmiyus]{1,6}\b(?![\u0080-\uFFFF$\\]|\s*(?:[+\-*%&|^<>!=?({]|\/(?![\/*])))))|(0[xX][\da-fA-F]+|0[oO][0-7]+|0[bB][01]+|(?:\d*\.\d+|\d+\.?)(?:[eE][+-]?\d+)?)|((?!\d)(?:(?!\s)[$\w\u0080-\uFFFF]|\\u[\da-fA-F]{4}|\\u\{[\da-fA-F]+\})+)|(--|\+\+|&&|\|\||=>|\.{3}|(?:[+\-\/%&|^]|\*{1,2}|<{1,2}|>{1,3}|!=?|={1,2})=?|[?~.,:;[\](){}])|(\s+)|(^$|[\s\S])/g;gr.matchToToken=function(e){var r={type:"invalid",value:e[0],closed:void 0};return e[1]?(r.type="string",r.closed=!!(e[3]||e[4])):e[5]?r.type="comment":e[6]?(r.type="comment",r.closed=!!e[7]):e[8]?r.type="regex":e[9]?r.type="number":e[10]?r.type="name":e[11]?r.type="punctuator":e[12]&&(r.type="whitespace"),r}});var Bn=p(We=>{"use strict";Object.defineProperty(We,"__esModule",{value:!0});We.isIdentifierChar=Fn;We.isIdentifierName=yo;We.isIdentifierStart=Tn;var et="\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C8A\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CD\uA7D0\uA7D1\uA7D3\uA7D5-\uA7DC\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC",kn="\xB7\u0300-\u036F\u0387\u0483-\u0487\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u0669\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u06F0-\u06F9\u0711\u0730-\u074A\u07A6-\u07B0\u07C0-\u07C9\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0897-\u089F\u08CA-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0966-\u096F\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09E6-\u09EF\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A66-\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AE6-\u0AEF\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B66-\u0B6F\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0BE6-\u0BEF\u0C00-\u0C04\u0C3C\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0CE6-\u0CEF\u0CF3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D66-\u0D6F\u0D81-\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0E50-\u0E59\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0ED0-\u0ED9\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1040-\u1049\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F-\u109D\u135D-\u135F\u1369-\u1371\u1712-\u1715\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u17E0-\u17E9\u180B-\u180D\u180F-\u1819\u18A9\u1920-\u192B\u1930-\u193B\u1946-\u194F\u19D0-\u19DA\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AB0-\u1ABD\u1ABF-\u1ACE\u1B00-\u1B04\u1B34-\u1B44\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BB0-\u1BB9\u1BE6-\u1BF3\u1C24-\u1C37\u1C40-\u1C49\u1C50-\u1C59\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DFF\u200C\u200D\u203F\u2040\u2054\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\u30FB\uA620-\uA629\uA66F\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA82C\uA880\uA881\uA8B4-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F1\uA8FF-\uA909\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9D0-\uA9D9\uA9E5\uA9F0-\uA9F9\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA50-\uAA59\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uABF0-\uABF9\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFF10-\uFF19\uFF3F\uFF65",vo=new RegExp("["+et+"]"),go=new RegExp("["+et+kn+"]");et=kn=null;var _n=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191],bo=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239];function Qr(e,r){let t=65536;for(let n=0,i=r.length;n<i;n+=2){if(t+=r[n],t>e)return!1;if(t+=r[n+1],t>=e)return!0}return!1}function Tn(e){return e<65?e===36:e<=90?!0:e<97?e===95:e<=122?!0:e<=65535?e>=170&&vo.test(String.fromCharCode(e)):Qr(e,_n)}function Fn(e){return e<48?e===36:e<58?!0:e<65?!1:e<=90?!0:e<97?e===95:e<=122?!0:e<=65535?e>=170&&go.test(String.fromCharCode(e)):Qr(e,_n)||Qr(e,bo)}function yo(e){let r=!0;for(let t=0;t<e.length;t++){let n=e.charCodeAt(t);if((n&64512)===55296&&t+1<e.length){let i=e.charCodeAt(++t);(i&64512)===56320&&(n=65536+((n&1023)<<10)+(i&1023))}if(r){if(r=!1,!Tn(n))return!1}else if(!Fn(n))return!1}return!r}});var Un=p(be=>{"use strict";Object.defineProperty(be,"__esModule",{value:!0});be.isKeyword=Po;be.isReservedWord=Gn;be.isStrictBindOnlyReservedWord=jn;be.isStrictBindReservedWord=So;be.isStrictReservedWord=Mn;var rt={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]},Lo=new Set(rt.keyword),Co=new Set(rt.strict),wo=new Set(rt.strictBind);function Gn(e,r){return r&&e==="await"||e==="enum"}function Mn(e,r){return Gn(e,r)||Co.has(e)}function jn(e){return wo.has(e)}function So(e,r){return Mn(e,r)||jn(e)}function Po(e){return Lo.has(e)}});var qn=p(te=>{"use strict";Object.defineProperty(te,"__esModule",{value:!0});Object.defineProperty(te,"isIdentifierChar",{enumerable:!0,get:function(){return tt.isIdentifierChar}});Object.defineProperty(te,"isIdentifierName",{enumerable:!0,get:function(){return tt.isIdentifierName}});Object.defineProperty(te,"isIdentifierStart",{enumerable:!0,get:function(){return tt.isIdentifierStart}});Object.defineProperty(te,"isKeyword",{enumerable:!0,get:function(){return He.isKeyword}});Object.defineProperty(te,"isReservedWord",{enumerable:!0,get:function(){return He.isReservedWord}});Object.defineProperty(te,"isStrictBindOnlyReservedWord",{enumerable:!0,get:function(){return He.isStrictBindOnlyReservedWord}});Object.defineProperty(te,"isStrictBindReservedWord",{enumerable:!0,get:function(){return He.isStrictBindReservedWord}});Object.defineProperty(te,"isStrictReservedWord",{enumerable:!0,get:function(){return He.isStrictReservedWord}});var tt=Bn(),He=Un()});var Hn=p((i1,nt)=>{var yr=process||{},$n=yr.argv||[],br=yr.env||{},xo=!(br.NO_COLOR||$n.includes("--no-color"))&&(!!br.FORCE_COLOR||$n.includes("--color")||yr.platform==="win32"||(yr.stdout||{}).isTTY&&br.TERM!=="dumb"||!!br.CI),Eo=(e,r,t=e)=>n=>{let i=""+n,a=i.indexOf(r,e.length);return~a?e+Oo(i,r,t,a)+r:e+i+r},Oo=(e,r,t,n)=>{let i="",a=0;do i+=e.substring(a,n)+t,a=n+r.length,n=e.indexOf(r,a);while(~n);return i+e.substring(a)},Wn=(e=xo)=>{let r=e?Eo:()=>String;return{isColorSupported:e,reset:r("\x1B[0m","\x1B[0m"),bold:r("\x1B[1m","\x1B[22m","\x1B[22m\x1B[1m"),dim:r("\x1B[2m","\x1B[22m","\x1B[22m\x1B[2m"),italic:r("\x1B[3m","\x1B[23m"),underline:r("\x1B[4m","\x1B[24m"),inverse:r("\x1B[7m","\x1B[27m"),hidden:r("\x1B[8m","\x1B[28m"),strikethrough:r("\x1B[9m","\x1B[29m"),black:r("\x1B[30m","\x1B[39m"),red:r("\x1B[31m","\x1B[39m"),green:r("\x1B[32m","\x1B[39m"),yellow:r("\x1B[33m","\x1B[39m"),blue:r("\x1B[34m","\x1B[39m"),magenta:r("\x1B[35m","\x1B[39m"),cyan:r("\x1B[36m","\x1B[39m"),white:r("\x1B[37m","\x1B[39m"),gray:r("\x1B[90m","\x1B[39m"),bgBlack:r("\x1B[40m","\x1B[49m"),bgRed:r("\x1B[41m","\x1B[49m"),bgGreen:r("\x1B[42m","\x1B[49m"),bgYellow:r("\x1B[43m","\x1B[49m"),bgBlue:r("\x1B[44m","\x1B[49m"),bgMagenta:r("\x1B[45m","\x1B[49m"),bgCyan:r("\x1B[46m","\x1B[49m"),bgWhite:r("\x1B[47m","\x1B[49m"),blackBright:r("\x1B[90m","\x1B[39m"),redBright:r("\x1B[91m","\x1B[39m"),greenBright:r("\x1B[92m","\x1B[39m"),yellowBright:r("\x1B[93m","\x1B[39m"),blueBright:r("\x1B[94m","\x1B[39m"),magentaBright:r("\x1B[95m","\x1B[39m"),cyanBright:r("\x1B[96m","\x1B[39m"),whiteBright:r("\x1B[97m","\x1B[39m"),bgBlackBright:r("\x1B[100m","\x1B[49m"),bgRedBright:r("\x1B[101m","\x1B[49m"),bgGreenBright:r("\x1B[102m","\x1B[49m"),bgYellowBright:r("\x1B[103m","\x1B[49m"),bgBlueBright:r("\x1B[104m","\x1B[49m"),bgMagentaBright:r("\x1B[105m","\x1B[49m"),bgCyanBright:r("\x1B[106m","\x1B[49m"),bgWhiteBright:r("\x1B[107m","\x1B[49m")}};nt.exports=Wn();nt.exports.createColors=Wn});var zn=p((a1,Yn)=>{"use strict";var Do=/[|\\{}()[\]^$+*?.]/g;Yn.exports=function(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(Do,"\\$&")}});var Jn=p((s1,Vn)=>{"use strict";Vn.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}});var it=p((o1,Qn)=>{var ye=Jn(),Zn={};for(Lr in ye)ye.hasOwnProperty(Lr)&&(Zn[ye[Lr]]=Lr);var Lr,d=Qn.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(H in d)if(d.hasOwnProperty(H)){if(!("channels"in d[H]))throw new Error("missing channels property: "+H);if(!("labels"in d[H]))throw new Error("missing channel labels property: "+H);if(d[H].labels.length!==d[H].channels)throw new Error("channel and label counts mismatch: "+H);Xn=d[H].channels,Kn=d[H].labels,delete d[H].channels,delete d[H].labels,Object.defineProperty(d[H],"channels",{value:Xn}),Object.defineProperty(d[H],"labels",{value:Kn})}var Xn,Kn,H;d.rgb.hsl=function(e){var r=e[0]/255,t=e[1]/255,n=e[2]/255,i=Math.min(r,t,n),a=Math.max(r,t,n),s=a-i,o,u,f;return a===i?o=0:r===a?o=(t-n)/s:t===a?o=2+(n-r)/s:n===a&&(o=4+(r-t)/s),o=Math.min(o*60,360),o<0&&(o+=360),f=(i+a)/2,a===i?u=0:f<=.5?u=s/(a+i):u=s/(2-a-i),[o,u*100,f*100]};d.rgb.hsv=function(e){var r,t,n,i,a,s=e[0]/255,o=e[1]/255,u=e[2]/255,f=Math.max(s,o,u),l=f-Math.min(s,o,u),h=function(b){return(f-b)/6/l+1/2};return l===0?i=a=0:(a=l/f,r=h(s),t=h(o),n=h(u),s===f?i=n-t:o===f?i=1/3+r-n:u===f&&(i=2/3+t-r),i<0?i+=1:i>1&&(i-=1)),[i*360,a*100,f*100]};d.rgb.hwb=function(e){var r=e[0],t=e[1],n=e[2],i=d.rgb.hsl(e)[0],a=1/255*Math.min(r,Math.min(t,n));return n=1-1/255*Math.max(r,Math.max(t,n)),[i,a*100,n*100]};d.rgb.cmyk=function(e){var r=e[0]/255,t=e[1]/255,n=e[2]/255,i,a,s,o;return o=Math.min(1-r,1-t,1-n),i=(1-r-o)/(1-o)||0,a=(1-t-o)/(1-o)||0,s=(1-n-o)/(1-o)||0,[i*100,a*100,s*100,o*100]};function Io(e,r){return Math.pow(e[0]-r[0],2)+Math.pow(e[1]-r[1],2)+Math.pow(e[2]-r[2],2)}d.rgb.keyword=function(e){var r=Zn[e];if(r)return r;var t=1/0,n;for(var i in ye)if(ye.hasOwnProperty(i)){var a=ye[i],s=Io(e,a);s<t&&(t=s,n=i)}return n};d.keyword.rgb=function(e){return ye[e]};d.rgb.xyz=function(e){var r=e[0]/255,t=e[1]/255,n=e[2]/255;r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92,t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92,n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92;var i=r*.4124+t*.3576+n*.1805,a=r*.2126+t*.7152+n*.0722,s=r*.0193+t*.1192+n*.9505;return[i*100,a*100,s*100]};d.rgb.lab=function(e){var r=d.rgb.xyz(e),t=r[0],n=r[1],i=r[2],a,s,o;return t/=95.047,n/=100,i/=108.883,t=t>.008856?Math.pow(t,1/3):7.787*t+16/116,n=n>.008856?Math.pow(n,1/3):7.787*n+16/116,i=i>.008856?Math.pow(i,1/3):7.787*i+16/116,a=116*n-16,s=500*(t-n),o=200*(n-i),[a,s,o]};d.hsl.rgb=function(e){var r=e[0]/360,t=e[1]/100,n=e[2]/100,i,a,s,o,u;if(t===0)return u=n*255,[u,u,u];n<.5?a=n*(1+t):a=n+t-n*t,i=2*n-a,o=[0,0,0];for(var f=0;f<3;f++)s=r+1/3*-(f-1),s<0&&s++,s>1&&s--,6*s<1?u=i+(a-i)*6*s:2*s<1?u=a:3*s<2?u=i+(a-i)*(2/3-s)*6:u=i,o[f]=u*255;return o};d.hsl.hsv=function(e){var r=e[0],t=e[1]/100,n=e[2]/100,i=t,a=Math.max(n,.01),s,o;return n*=2,t*=n<=1?n:2-n,i*=a<=1?a:2-a,o=(n+t)/2,s=n===0?2*i/(a+i):2*t/(n+t),[r,s*100,o*100]};d.hsv.rgb=function(e){var r=e[0]/60,t=e[1]/100,n=e[2]/100,i=Math.floor(r)%6,a=r-Math.floor(r),s=255*n*(1-t),o=255*n*(1-t*a),u=255*n*(1-t*(1-a));switch(n*=255,i){case 0:return[n,u,s];case 1:return[o,n,s];case 2:return[s,n,u];case 3:return[s,o,n];case 4:return[u,s,n];case 5:return[n,s,o]}};d.hsv.hsl=function(e){var r=e[0],t=e[1]/100,n=e[2]/100,i=Math.max(n,.01),a,s,o;return o=(2-t)*n,a=(2-t)*i,s=t*i,s/=a<=1?a:2-a,s=s||0,o/=2,[r,s*100,o*100]};d.hwb.rgb=function(e){var r=e[0]/360,t=e[1]/100,n=e[2]/100,i=t+n,a,s,o,u;i>1&&(t/=i,n/=i),a=Math.floor(6*r),s=1-n,o=6*r-a,a&1&&(o=1-o),u=t+o*(s-t);var f,l,h;switch(a){default:case 6:case 0:f=s,l=u,h=t;break;case 1:f=u,l=s,h=t;break;case 2:f=t,l=s,h=u;break;case 3:f=t,l=u,h=s;break;case 4:f=u,l=t,h=s;break;case 5:f=s,l=t,h=u;break}return[f*255,l*255,h*255]};d.cmyk.rgb=function(e){var r=e[0]/100,t=e[1]/100,n=e[2]/100,i=e[3]/100,a,s,o;return a=1-Math.min(1,r*(1-i)+i),s=1-Math.min(1,t*(1-i)+i),o=1-Math.min(1,n*(1-i)+i),[a*255,s*255,o*255]};d.xyz.rgb=function(e){var r=e[0]/100,t=e[1]/100,n=e[2]/100,i,a,s;return i=r*3.2406+t*-1.5372+n*-.4986,a=r*-.9689+t*1.8758+n*.0415,s=r*.0557+t*-.204+n*1.057,i=i>.0031308?1.055*Math.pow(i,1/2.4)-.055:i*12.92,a=a>.0031308?1.055*Math.pow(a,1/2.4)-.055:a*12.92,s=s>.0031308?1.055*Math.pow(s,1/2.4)-.055:s*12.92,i=Math.min(Math.max(0,i),1),a=Math.min(Math.max(0,a),1),s=Math.min(Math.max(0,s),1),[i*255,a*255,s*255]};d.xyz.lab=function(e){var r=e[0],t=e[1],n=e[2],i,a,s;return r/=95.047,t/=100,n/=108.883,r=r>.008856?Math.pow(r,1/3):7.787*r+16/116,t=t>.008856?Math.pow(t,1/3):7.787*t+16/116,n=n>.008856?Math.pow(n,1/3):7.787*n+16/116,i=116*t-16,a=500*(r-t),s=200*(t-n),[i,a,s]};d.lab.xyz=function(e){var r=e[0],t=e[1],n=e[2],i,a,s;a=(r+16)/116,i=t/500+a,s=a-n/200;var o=Math.pow(a,3),u=Math.pow(i,3),f=Math.pow(s,3);return a=o>.008856?o:(a-16/116)/7.787,i=u>.008856?u:(i-16/116)/7.787,s=f>.008856?f:(s-16/116)/7.787,i*=95.047,a*=100,s*=108.883,[i,a,s]};d.lab.lch=function(e){var r=e[0],t=e[1],n=e[2],i,a,s;return i=Math.atan2(n,t),a=i*360/2/Math.PI,a<0&&(a+=360),s=Math.sqrt(t*t+n*n),[r,s,a]};d.lch.lab=function(e){var r=e[0],t=e[1],n=e[2],i,a,s;return s=n/360*2*Math.PI,i=t*Math.cos(s),a=t*Math.sin(s),[r,i,a]};d.rgb.ansi16=function(e){var r=e[0],t=e[1],n=e[2],i=1 in arguments?arguments[1]:d.rgb.hsv(e)[2];if(i=Math.round(i/50),i===0)return 30;var a=30+(Math.round(n/255)<<2|Math.round(t/255)<<1|Math.round(r/255));return i===2&&(a+=60),a};d.hsv.ansi16=function(e){return d.rgb.ansi16(d.hsv.rgb(e),e[2])};d.rgb.ansi256=function(e){var r=e[0],t=e[1],n=e[2];if(r===t&&t===n)return r<8?16:r>248?231:Math.round((r-8)/247*24)+232;var i=16+36*Math.round(r/255*5)+6*Math.round(t/255*5)+Math.round(n/255*5);return i};d.ansi16.rgb=function(e){var r=e%10;if(r===0||r===7)return e>50&&(r+=3.5),r=r/10.5*255,[r,r,r];var t=(~~(e>50)+1)*.5,n=(r&1)*t*255,i=(r>>1&1)*t*255,a=(r>>2&1)*t*255;return[n,i,a]};d.ansi256.rgb=function(e){if(e>=232){var r=(e-232)*10+8;return[r,r,r]}e-=16;var t,n=Math.floor(e/36)/5*255,i=Math.floor((t=e%36)/6)/5*255,a=t%6/5*255;return[n,i,a]};d.rgb.hex=function(e){var r=((Math.round(e[0])&255)<<16)+((Math.round(e[1])&255)<<8)+(Math.round(e[2])&255),t=r.toString(16).toUpperCase();return"000000".substring(t.length)+t};d.hex.rgb=function(e){var r=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!r)return[0,0,0];var t=r[0];r[0].length===3&&(t=t.split("").map(function(o){return o+o}).join(""));var n=parseInt(t,16),i=n>>16&255,a=n>>8&255,s=n&255;return[i,a,s]};d.rgb.hcg=function(e){var r=e[0]/255,t=e[1]/255,n=e[2]/255,i=Math.max(Math.max(r,t),n),a=Math.min(Math.min(r,t),n),s=i-a,o,u;return s<1?o=a/(1-s):o=0,s<=0?u=0:i===r?u=(t-n)/s%6:i===t?u=2+(n-r)/s:u=4+(r-t)/s+4,u/=6,u%=1,[u*360,s*100,o*100]};d.hsl.hcg=function(e){var r=e[1]/100,t=e[2]/100,n=1,i=0;return t<.5?n=2*r*t:n=2*r*(1-t),n<1&&(i=(t-.5*n)/(1-n)),[e[0],n*100,i*100]};d.hsv.hcg=function(e){var r=e[1]/100,t=e[2]/100,n=r*t,i=0;return n<1&&(i=(t-n)/(1-n)),[e[0],n*100,i*100]};d.hcg.rgb=function(e){var r=e[0]/360,t=e[1]/100,n=e[2]/100;if(t===0)return[n*255,n*255,n*255];var i=[0,0,0],a=r%1*6,s=a%1,o=1-s,u=0;switch(Math.floor(a)){case 0:i[0]=1,i[1]=s,i[2]=0;break;case 1:i[0]=o,i[1]=1,i[2]=0;break;case 2:i[0]=0,i[1]=1,i[2]=s;break;case 3:i[0]=0,i[1]=o,i[2]=1;break;case 4:i[0]=s,i[1]=0,i[2]=1;break;default:i[0]=1,i[1]=0,i[2]=o}return u=(1-t)*n,[(t*i[0]+u)*255,(t*i[1]+u)*255,(t*i[2]+u)*255]};d.hcg.hsv=function(e){var r=e[1]/100,t=e[2]/100,n=r+t*(1-r),i=0;return n>0&&(i=r/n),[e[0],i*100,n*100]};d.hcg.hsl=function(e){var r=e[1]/100,t=e[2]/100,n=t*(1-r)+.5*r,i=0;return n>0&&n<.5?i=r/(2*n):n>=.5&&n<1&&(i=r/(2*(1-n))),[e[0],i*100,n*100]};d.hcg.hwb=function(e){var r=e[1]/100,t=e[2]/100,n=r+t*(1-r);return[e[0],(n-r)*100,(1-n)*100]};d.hwb.hcg=function(e){var r=e[1]/100,t=e[2]/100,n=1-t,i=n-r,a=0;return i<1&&(a=(n-i)/(1-i)),[e[0],i*100,a*100]};d.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]};d.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]};d.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]};d.gray.hsl=d.gray.hsv=function(e){return[0,0,e[0]]};d.gray.hwb=function(e){return[0,100,e[0]]};d.gray.cmyk=function(e){return[0,0,0,e[0]]};d.gray.lab=function(e){return[e[0],0,0]};d.gray.hex=function(e){var r=Math.round(e[0]/100*255)&255,t=(r<<16)+(r<<8)+r,n=t.toString(16).toUpperCase();return"000000".substring(n.length)+n};d.rgb.gray=function(e){var r=(e[0]+e[1]+e[2])/3;return[r/255*100]}});var ri=p((u1,ei)=>{var Cr=it();function No(){for(var e={},r=Object.keys(Cr),t=r.length,n=0;n<t;n++)e[r[n]]={distance:-1,parent:null};return e}function Ao(e){var r=No(),t=[e];for(r[e].distance=0;t.length;)for(var n=t.pop(),i=Object.keys(Cr[n]),a=i.length,s=0;s<a;s++){var o=i[s],u=r[o];u.distance===-1&&(u.distance=r[n].distance+1,u.parent=n,t.unshift(o))}return r}function Ro(e,r){return function(t){return r(e(t))}}function ko(e,r){for(var t=[r[e].parent,e],n=Cr[r[e].parent][e],i=r[e].parent;r[i].parent;)t.unshift(r[i].parent),n=Ro(Cr[r[i].parent][i],n),i=r[i].parent;return n.conversion=t,n}ei.exports=function(e){for(var r=Ao(e),t={},n=Object.keys(r),i=n.length,a=0;a<i;a++){var s=n[a],o=r[s];o.parent!==null&&(t[s]=ko(s,r))}return t}});var ni=p((c1,ti)=>{var at=it(),_o=ri(),De={},To=Object.keys(at);function Fo(e){var r=function(t){return t==null?t:(arguments.length>1&&(t=Array.prototype.slice.call(arguments)),e(t))};return"conversion"in e&&(r.conversion=e.conversion),r}function Bo(e){var r=function(t){if(t==null)return t;arguments.length>1&&(t=Array.prototype.slice.call(arguments));var n=e(t);if(typeof n=="object")for(var i=n.length,a=0;a<i;a++)n[a]=Math.round(n[a]);return n};return"conversion"in e&&(r.conversion=e.conversion),r}To.forEach(function(e){De[e]={},Object.defineProperty(De[e],"channels",{value:at[e].channels}),Object.defineProperty(De[e],"labels",{value:at[e].labels});var r=_o(e),t=Object.keys(r);t.forEach(function(n){var i=r[n];De[e][n]=Bo(i),De[e][n].raw=Fo(i)})});ti.exports=De});var ai=p((l1,ii)=>{"use strict";var Ie=ni(),wr=(e,r)=>function(){return`\x1B[${e.apply(Ie,arguments)+r}m`},Sr=(e,r)=>function(){let t=e.apply(Ie,arguments);return`\x1B[${38+r};5;${t}m`},Pr=(e,r)=>function(){let t=e.apply(Ie,arguments);return`\x1B[${38+r};2;${t[0]};${t[1]};${t[2]}m`};function Go(){let e=new Map,r={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};r.color.grey=r.color.gray;for(let i of Object.keys(r)){let a=r[i];for(let s of Object.keys(a)){let o=a[s];r[s]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},a[s]=r[s],e.set(o[0],o[1])}Object.defineProperty(r,i,{value:a,enumerable:!1}),Object.defineProperty(r,"codes",{value:e,enumerable:!1})}let t=i=>i,n=(i,a,s)=>[i,a,s];r.color.close="\x1B[39m",r.bgColor.close="\x1B[49m",r.color.ansi={ansi:wr(t,0)},r.color.ansi256={ansi256:Sr(t,0)},r.color.ansi16m={rgb:Pr(n,0)},r.bgColor.ansi={ansi:wr(t,10)},r.bgColor.ansi256={ansi256:Sr(t,10)},r.bgColor.ansi16m={rgb:Pr(n,10)};for(let i of Object.keys(Ie)){if(typeof Ie[i]!="object")continue;let a=Ie[i];i==="ansi16"&&(i="ansi"),"ansi16"in a&&(r.color.ansi[i]=wr(a.ansi16,0),r.bgColor.ansi[i]=wr(a.ansi16,10)),"ansi256"in a&&(r.color.ansi256[i]=Sr(a.ansi256,0),r.bgColor.ansi256[i]=Sr(a.ansi256,10)),"rgb"in a&&(r.color.ansi16m[i]=Pr(a.rgb,0),r.bgColor.ansi16m[i]=Pr(a.rgb,10))}return r}Object.defineProperty(ii,"exports",{enumerable:!0,get:Go})});var oi=p((f1,si)=>{"use strict";si.exports=(e,r)=>{r=r||process.argv;let t=e.startsWith("-")?"":e.length===1?"-":"--",n=r.indexOf(t+e),i=r.indexOf("--");return n!==-1&&(i===-1?!0:n<i)}});var ci=p((p1,ui)=>{"use strict";var Mo=require("os"),Z=oi(),U=process.env,Ne;Z("no-color")||Z("no-colors")||Z("color=false")?Ne=!1:(Z("color")||Z("colors")||Z("color=true")||Z("color=always"))&&(Ne=!0);"FORCE_COLOR"in U&&(Ne=U.FORCE_COLOR.length===0||parseInt(U.FORCE_COLOR,10)!==0);function jo(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function Uo(e){if(Ne===!1)return 0;if(Z("color=16m")||Z("color=full")||Z("color=truecolor"))return 3;if(Z("color=256"))return 2;if(e&&!e.isTTY&&Ne!==!0)return 0;let r=Ne?1:0;if(process.platform==="win32"){let t=Mo.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(t[0])>=10&&Number(t[2])>=10586?Number(t[2])>=14931?3:2:1}if("CI"in U)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(t=>t in U)||U.CI_NAME==="codeship"?1:r;if("TEAMCITY_VERSION"in U)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(U.TEAMCITY_VERSION)?1:0;if(U.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in U){let t=parseInt((U.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(U.TERM_PROGRAM){case"iTerm.app":return t>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(U.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(U.TERM)||"COLORTERM"in U?1:(U.TERM==="dumb",r)}function st(e){let r=Uo(e);return jo(r)}ui.exports={supportsColor:st,stdout:st(process.stdout),stderr:st(process.stderr)}});var hi=p((d1,di)=>{"use strict";var qo=/(?:\\(u[a-f\d]{4}|x[a-f\d]{2}|.))|(?:\{(~)?(\w+(?:\([^)]*\))?(?:\.\w+(?:\([^)]*\))?)*)(?:[ \t]|(?=\r?\n)))|(\})|((?:.|[\r\n\f])+?)/gi,li=/(?:^|\.)(\w+)(?:\(([^)]*)\))?/g,$o=/^(['"])((?:\\.|(?!\1)[^\\])*)\1$/,Wo=/\\(u[a-f\d]{4}|x[a-f\d]{2}|.)|([^\\])/gi,Ho=new Map([["n",`
`],["r","\r"],["t","	"],["b","\b"],["f","\f"],["v","\v"],["0","\0"],["\\","\\"],["e","\x1B"],["a","\x07"]]);function pi(e){return e[0]==="u"&&e.length===5||e[0]==="x"&&e.length===3?String.fromCharCode(parseInt(e.slice(1),16)):Ho.get(e)||e}function Yo(e,r){let t=[],n=r.trim().split(/\s*,\s*/g),i;for(let a of n)if(!isNaN(a))t.push(Number(a));else if(i=a.match($o))t.push(i[2].replace(Wo,(s,o,u)=>o?pi(o):u));else throw new Error(`Invalid Chalk template style argument: ${a} (in style '${e}')`);return t}function zo(e){li.lastIndex=0;let r=[],t;for(;(t=li.exec(e))!==null;){let n=t[1];if(t[2]){let i=Yo(n,t[2]);r.push([n].concat(i))}else r.push([n])}return r}function fi(e,r){let t={};for(let i of r)for(let a of i.styles)t[a[0]]=i.inverse?null:a.slice(1);let n=e;for(let i of Object.keys(t))if(Array.isArray(t[i])){if(!(i in n))throw new Error(`Unknown Chalk style: ${i}`);t[i].length>0?n=n[i].apply(n,t[i]):n=n[i]}return n}di.exports=(e,r)=>{let t=[],n=[],i=[];if(r.replace(qo,(a,s,o,u,f,l)=>{if(s)i.push(pi(s));else if(u){let h=i.join("");i=[],n.push(t.length===0?h:fi(e,t)(h)),t.push({inverse:o,styles:zo(u)})}else if(f){if(t.length===0)throw new Error("Found extraneous } in Chalk template literal");n.push(fi(e,t)(i.join(""))),i=[],t.pop()}else i.push(l)}),n.push(i.join("")),t.length>0){let a=`Chalk template literal is missing ${t.length} closing bracket${t.length===1?"":"s"} (\`}\`)`;throw new Error(a)}return n.join("")}});var yi=p((h1,ze)=>{"use strict";var ut=zn(),B=ai(),ot=ci().stdout,Vo=hi(),vi=process.platform==="win32"&&!(process.env.TERM||"").toLowerCase().startsWith("xterm"),gi=["ansi","ansi","ansi256","ansi16m"],bi=new Set(["gray"]),Ae=Object.create(null);function mi(e,r){r=r||{};let t=ot?ot.level:0;e.level=r.level===void 0?t:r.level,e.enabled="enabled"in r?r.enabled:e.level>0}function Ye(e){if(!this||!(this instanceof Ye)||this.template){let r={};return mi(r,e),r.template=function(){let t=[].slice.call(arguments);return Ko.apply(null,[r.template].concat(t))},Object.setPrototypeOf(r,Ye.prototype),Object.setPrototypeOf(r.template,r),r.template.constructor=Ye,r.template}mi(this,e)}vi&&(B.blue.open="\x1B[94m");for(let e of Object.keys(B))B[e].closeRe=new RegExp(ut(B[e].close),"g"),Ae[e]={get(){let r=B[e];return xr.call(this,this._styles?this._styles.concat(r):[r],this._empty,e)}};Ae.visible={get(){return xr.call(this,this._styles||[],!0,"visible")}};B.color.closeRe=new RegExp(ut(B.color.close),"g");for(let e of Object.keys(B.color.ansi))bi.has(e)||(Ae[e]={get(){let r=this.level;return function(){let n={open:B.color[gi[r]][e].apply(null,arguments),close:B.color.close,closeRe:B.color.closeRe};return xr.call(this,this._styles?this._styles.concat(n):[n],this._empty,e)}}});B.bgColor.closeRe=new RegExp(ut(B.bgColor.close),"g");for(let e of Object.keys(B.bgColor.ansi)){if(bi.has(e))continue;let r="bg"+e[0].toUpperCase()+e.slice(1);Ae[r]={get(){let t=this.level;return function(){let i={open:B.bgColor[gi[t]][e].apply(null,arguments),close:B.bgColor.close,closeRe:B.bgColor.closeRe};return xr.call(this,this._styles?this._styles.concat(i):[i],this._empty,e)}}}}var Jo=Object.defineProperties(()=>{},Ae);function xr(e,r,t){let n=function(){return Xo.apply(n,arguments)};n._styles=e,n._empty=r;let i=this;return Object.defineProperty(n,"level",{enumerable:!0,get(){return i.level},set(a){i.level=a}}),Object.defineProperty(n,"enabled",{enumerable:!0,get(){return i.enabled},set(a){i.enabled=a}}),n.hasGrey=this.hasGrey||t==="gray"||t==="grey",n.__proto__=Jo,n}function Xo(){let e=arguments,r=e.length,t=String(arguments[0]);if(r===0)return"";if(r>1)for(let i=1;i<r;i++)t+=" "+e[i];if(!this.enabled||this.level<=0||!t)return this._empty?"":t;let n=B.dim.open;vi&&this.hasGrey&&(B.dim.open="");for(let i of this._styles.slice().reverse())t=i.open+t.replace(i.closeRe,i.open)+i.close,t=t.replace(/\r?\n/g,`${i.close}$&${i.open}`);return B.dim.open=n,t}function Ko(e,r){if(!Array.isArray(r))return[].slice.call(arguments,1).join(" ");let t=[].slice.call(arguments,2),n=[r.raw[0]];for(let i=1;i<r.length;i++)n.push(String(t[i-1]).replace(/[{}\\]/g,"\\$&")),n.push(String(r.raw[i]));return Vo(e,n.join(""))}Object.defineProperties(Ye.prototype,Ae);ze.exports=Ye();ze.exports.supportsColor=ot;ze.exports.default=ze.exports});var Oi=p(Ve=>{"use strict";Object.defineProperty(Ve,"__esModule",{value:!0});Ve.default=au;Ve.shouldHighlight=Ei;var Li=Rn(),Ci=qn(),lt=Zo(Hn(),!0);function Si(e){if(typeof WeakMap!="function")return null;var r=new WeakMap,t=new WeakMap;return(Si=function(n){return n?t:r})(e)}function Zo(e,r){if(!r&&e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var t=Si(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if(a!=="default"&&{}.hasOwnProperty.call(e,a)){var s=i?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,t&&t.set(e,n),n}var Pi=typeof process=="object"&&(process.env.FORCE_COLOR==="0"||process.env.FORCE_COLOR==="false")?(0,lt.createColors)(!1):lt.default,wi=(e,r)=>t=>e(r(t)),Qo=new Set(["as","async","from","get","of","set"]);function eu(e){return{keyword:e.cyan,capitalized:e.yellow,jsxIdentifier:e.yellow,punctuator:e.yellow,number:e.magenta,string:e.green,regex:e.magenta,comment:e.gray,invalid:wi(wi(e.white,e.bgRed),e.bold)}}var ru=/\r\n|[\n\r\u2028\u2029]/,tu=/^[()[\]{}]$/,xi;{let e=/^[a-z][\w-]*$/i,r=function(t,n,i){if(t.type==="name"){if((0,Ci.isKeyword)(t.value)||(0,Ci.isStrictReservedWord)(t.value,!0)||Qo.has(t.value))return"keyword";if(e.test(t.value)&&(i[n-1]==="<"||i.slice(n-2,n)==="</"))return"jsxIdentifier";if(t.value[0]!==t.value[0].toLowerCase())return"capitalized"}return t.type==="punctuator"&&tu.test(t.value)?"bracket":t.type==="invalid"&&(t.value==="@"||t.value==="#")?"punctuator":t.type};xi=function*(t){let n;for(;n=Li.default.exec(t);){let i=Li.matchToToken(n);yield{type:r(i,n.index,t),value:i.value}}}}function nu(e,r){let t="";for(let{type:n,value:i}of xi(r)){let a=e[n];a?t+=i.split(ru).map(s=>a(s)).join(`
`):t+=i}return t}function Ei(e){return Pi.isColorSupported||e.forceColor}var ct;function iu(e){if(e){var r;return(r=ct)!=null||(ct=(0,lt.createColors)(!0)),ct}return Pi}function au(e,r={}){if(e!==""&&Ei(r)){let t=eu(iu(r.forceColor));return nu(t,e)}else return e}{let e,r;Ve.getChalk=({forceColor:t})=>{var n;if((n=e)!=null||(e=yi()),t){var i;return(i=r)!=null||(r=new e.constructor({enabled:!0,level:1})),r}return e}}});var Ri=p(Er=>{"use strict";Object.defineProperty(Er,"__esModule",{value:!0});Er.codeFrameColumns=Ai;Er.default=cu;var ft=su(Oi());function Ni(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return Ni=function(){return e},e}function su(e){if(e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var r=Ni();if(r&&r.has(e))return r.get(e);var t={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var a=n?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(t,i,a):t[i]=e[i]}return t.default=e,r&&r.set(e,t),t}var Di=!1;function ou(e){return{gutter:e.grey,marker:e.red.bold,message:e.red.bold}}var Ii=/\r\n|[\n\r\u2028\u2029]/;function uu(e,r,t){let n=Object.assign({column:0,line:-1},e.start),i=Object.assign({},n,e.end),{linesAbove:a=2,linesBelow:s=3}=t||{},o=n.line,u=n.column,f=i.line,l=i.column,h=Math.max(o-(a+1),0),b=Math.min(r.length,f+s);o===-1&&(h=0),f===-1&&(b=r.length);let x=f-o,m={};if(x)for(let g=0;g<=x;g++){let L=g+o;if(!u)m[L]=!0;else if(g===0){let N=r[L-1].length;m[L]=[u,N-u+1]}else if(g===x)m[L]=[0,l];else{let N=r[L-g].length;m[L]=[0,N]}}else u===l?u?m[o]=[u,0]:m[o]=!0:m[o]=[u,l-u];return{start:h,end:b,markerLines:m}}function Ai(e,r,t={}){let n=(t.highlightCode||t.forceColor)&&(0,ft.shouldHighlight)(t),i=(0,ft.getChalk)(t),a=ou(i),s=(g,L)=>n?g(L):L,o=e.split(Ii),{start:u,end:f,markerLines:l}=uu(r,o,t),h=r.start&&typeof r.start.column=="number",b=String(f).length,m=(n?(0,ft.default)(e,t):e).split(Ii).slice(u,f).map((g,L)=>{let N=u+1+L,M=` ${` ${N}`.slice(-b)} | `,se=l[N],le=!l[N+1];if(se){let J="";if(Array.isArray(se)){let E=g.slice(0,Math.max(se[0]-1,0)).replace(/[^\t]/g," "),R=se[1]||1;J=[`
 `,s(a.gutter,M.replace(/\d/g," ")),E,s(a.marker,"^").repeat(R)].join(""),le&&t.message&&(J+=" "+s(a.message,t.message))}return[s(a.marker,">"),s(a.gutter,M),g,J].join("")}else return` ${s(a.gutter,M)}${g}`}).join(`
`);return t.message&&!h&&(m=`${" ".repeat(b+1)}${t.message}
${m}`),n?i.reset(m):m}function cu(e,r,t,n={}){if(!Di){Di=!0;let a="Passing lineNumber and colNumber is deprecated to @babel/code-frame. Please use `codeFrameColumns`.";if(process.emitWarning)process.emitWarning(a,"DeprecationWarning");else{let s=new Error(a);s.name="DeprecationWarning",console.warn(new Error(a))}}return t=Math.max(t,0),Ai(e,{start:{column:t,line:r}},n)}});var Fi=p((g1,Ti)=>{"use strict";var pt=xn(),lu=Dn(),{default:fu}=An(),{codeFrameColumns:pu}=Ri(),ki=pt("JSONError",{fileName:pt.append("in %s"),codeFrame:pt.append(`

%s
`)}),_i=(e,r,t)=>{typeof r=="string"&&(t=r,r=null);try{try{return JSON.parse(e,r)}catch(n){throw lu(e,r),n}}catch(n){n.message=n.message.replace(/\n/g,"");let i=n.message.match(/in JSON at position (\d+) while parsing/),a=new ki(n);if(t&&(a.fileName=t),i&&i.length>0){let s=new fu(e),o=Number(i[1]),u=s.locationForIndex(o),f=pu(e,{start:{line:u.line+1,column:u.column+1}},{highlightCode:!0});a.codeFrame=f}throw a}};_i.JSONError=ki;Ti.exports=_i});var Qi=p((y,Zi)=>{y=Zi.exports=S;var O;typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?O=function(){var e=Array.prototype.slice.call(arguments,0);e.unshift("SEMVER"),console.log.apply(console,e)}:O=function(){};y.SEMVER_SPEC_VERSION="2.0.0";var Je=256,Or=Number.MAX_SAFE_INTEGER||9007199254740991,dt=16,du=Je-6,Xe=y.re=[],I=y.safeRe=[],c=y.src=[],w=0,bt="[a-zA-Z0-9-]",ht=[["\\s",1],["\\d",Je],[bt,du]];function kr(e){for(var r=0;r<ht.length;r++){var t=ht[r][0],n=ht[r][1];e=e.split(t+"*").join(t+"{0,"+n+"}").split(t+"+").join(t+"{1,"+n+"}")}return e}var Re=w++;c[Re]="0|[1-9]\\d*";var ke=w++;c[ke]="\\d+";var yt=w++;c[yt]="\\d*[a-zA-Z-]"+bt+"*";var Gi=w++;c[Gi]="("+c[Re]+")\\.("+c[Re]+")\\.("+c[Re]+")";var Mi=w++;c[Mi]="("+c[ke]+")\\.("+c[ke]+")\\.("+c[ke]+")";var mt=w++;c[mt]="(?:"+c[Re]+"|"+c[yt]+")";var vt=w++;c[vt]="(?:"+c[ke]+"|"+c[yt]+")";var Lt=w++;c[Lt]="(?:-("+c[mt]+"(?:\\."+c[mt]+")*))";var Ct=w++;c[Ct]="(?:-?("+c[vt]+"(?:\\."+c[vt]+")*))";var gt=w++;c[gt]=bt+"+";var Ze=w++;c[Ze]="(?:\\+("+c[gt]+"(?:\\."+c[gt]+")*))";var wt=w++,ji="v?"+c[Gi]+c[Lt]+"?"+c[Ze]+"?";c[wt]="^"+ji+"$";var St="[v=\\s]*"+c[Mi]+c[Ct]+"?"+c[Ze]+"?",Pt=w++;c[Pt]="^"+St+"$";var Ge=w++;c[Ge]="((?:<|>)?=?)";var Dr=w++;c[Dr]=c[ke]+"|x|X|\\*";var Ir=w++;c[Ir]=c[Re]+"|x|X|\\*";var Le=w++;c[Le]="[v=\\s]*("+c[Ir]+")(?:\\.("+c[Ir]+")(?:\\.("+c[Ir]+")(?:"+c[Lt]+")?"+c[Ze]+"?)?)?";var Te=w++;c[Te]="[v=\\s]*("+c[Dr]+")(?:\\.("+c[Dr]+")(?:\\.("+c[Dr]+")(?:"+c[Ct]+")?"+c[Ze]+"?)?)?";var Ui=w++;c[Ui]="^"+c[Ge]+"\\s*"+c[Le]+"$";var qi=w++;c[qi]="^"+c[Ge]+"\\s*"+c[Te]+"$";var $i=w++;c[$i]="(?:^|[^\\d])(\\d{1,"+dt+"})(?:\\.(\\d{1,"+dt+"}))?(?:\\.(\\d{1,"+dt+"}))?(?:$|[^\\d])";var _r=w++;c[_r]="(?:~>?)";var Fe=w++;c[Fe]="(\\s*)"+c[_r]+"\\s+";Xe[Fe]=new RegExp(c[Fe],"g");I[Fe]=new RegExp(kr(c[Fe]),"g");var hu="$1~",Wi=w++;c[Wi]="^"+c[_r]+c[Le]+"$";var Hi=w++;c[Hi]="^"+c[_r]+c[Te]+"$";var Tr=w++;c[Tr]="(?:\\^)";var Be=w++;c[Be]="(\\s*)"+c[Tr]+"\\s+";Xe[Be]=new RegExp(c[Be],"g");I[Be]=new RegExp(kr(c[Be]),"g");var mu="$1^",Yi=w++;c[Yi]="^"+c[Tr]+c[Le]+"$";var zi=w++;c[zi]="^"+c[Tr]+c[Te]+"$";var xt=w++;c[xt]="^"+c[Ge]+"\\s*("+St+")$|^$";var Et=w++;c[Et]="^"+c[Ge]+"\\s*("+ji+")$|^$";var Ce=w++;c[Ce]="(\\s*)"+c[Ge]+"\\s*("+St+"|"+c[Le]+")";Xe[Ce]=new RegExp(c[Ce],"g");I[Ce]=new RegExp(kr(c[Ce]),"g");var vu="$1$2$3",Vi=w++;c[Vi]="^\\s*("+c[Le]+")\\s+-\\s+("+c[Le]+")\\s*$";var Ji=w++;c[Ji]="^\\s*("+c[Te]+")\\s+-\\s+("+c[Te]+")\\s*$";var Xi=w++;c[Xi]="(<|>)?=?\\s*\\*";for(ne=0;ne<w;ne++)O(ne,c[ne]),Xe[ne]||(Xe[ne]=new RegExp(c[ne]),I[ne]=new RegExp(kr(c[ne])));var ne;y.parse=we;function we(e,r){if((!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1}),e instanceof S)return e;if(typeof e!="string"||e.length>Je)return null;var t=r.loose?I[Pt]:I[wt];if(!t.test(e))return null;try{return new S(e,r)}catch{return null}}y.valid=gu;function gu(e,r){var t=we(e,r);return t?t.version:null}y.clean=bu;function bu(e,r){var t=we(e.trim().replace(/^[=v]+/,""),r);return t?t.version:null}y.SemVer=S;function S(e,r){if((!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1}),e instanceof S){if(e.loose===r.loose)return e;e=e.version}else if(typeof e!="string")throw new TypeError("Invalid Version: "+e);if(e.length>Je)throw new TypeError("version is longer than "+Je+" characters");if(!(this instanceof S))return new S(e,r);O("SemVer",e,r),this.options=r,this.loose=!!r.loose;var t=e.trim().match(r.loose?I[Pt]:I[wt]);if(!t)throw new TypeError("Invalid Version: "+e);if(this.raw=e,this.major=+t[1],this.minor=+t[2],this.patch=+t[3],this.major>Or||this.major<0)throw new TypeError("Invalid major version");if(this.minor>Or||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>Or||this.patch<0)throw new TypeError("Invalid patch version");t[4]?this.prerelease=t[4].split(".").map(function(n){if(/^[0-9]+$/.test(n)){var i=+n;if(i>=0&&i<Or)return i}return n}):this.prerelease=[],this.build=t[5]?t[5].split("."):[],this.format()}S.prototype.format=function(){return this.version=this.major+"."+this.minor+"."+this.patch,this.prerelease.length&&(this.version+="-"+this.prerelease.join(".")),this.version};S.prototype.toString=function(){return this.version};S.prototype.compare=function(e){return O("SemVer.compare",this.version,this.options,e),e instanceof S||(e=new S(e,this.options)),this.compareMain(e)||this.comparePre(e)};S.prototype.compareMain=function(e){return e instanceof S||(e=new S(e,this.options)),_e(this.major,e.major)||_e(this.minor,e.minor)||_e(this.patch,e.patch)};S.prototype.comparePre=function(e){if(e instanceof S||(e=new S(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;var r=0;do{var t=this.prerelease[r],n=e.prerelease[r];if(O("prerelease compare",r,t,n),t===void 0&&n===void 0)return 0;if(n===void 0)return 1;if(t===void 0)return-1;if(t===n)continue;return _e(t,n)}while(++r)};S.prototype.inc=function(e,r){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r),this.inc("pre",r);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",r),this.inc("pre",r);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":if(this.prerelease.length===0)this.prerelease=[0];else{for(var t=this.prerelease.length;--t>=0;)typeof this.prerelease[t]=="number"&&(this.prerelease[t]++,t=-2);t===-1&&this.prerelease.push(0)}r&&(this.prerelease[0]===r?isNaN(this.prerelease[1])&&(this.prerelease=[r,0]):this.prerelease=[r,0]);break;default:throw new Error("invalid increment argument: "+e)}return this.format(),this.raw=this.version,this};y.inc=yu;function yu(e,r,t,n){typeof t=="string"&&(n=t,t=void 0);try{return new S(e,t).inc(r,n).version}catch{return null}}y.diff=Lu;function Lu(e,r){if(Ot(e,r))return null;var t=we(e),n=we(r),i="";if(t.prerelease.length||n.prerelease.length){i="pre";var a="prerelease"}for(var s in t)if((s==="major"||s==="minor"||s==="patch")&&t[s]!==n[s])return i+s;return a}y.compareIdentifiers=_e;var Bi=/^[0-9]+$/;function _e(e,r){var t=Bi.test(e),n=Bi.test(r);return t&&n&&(e=+e,r=+r),e===r?0:t&&!n?-1:n&&!t?1:e<r?-1:1}y.rcompareIdentifiers=Cu;function Cu(e,r){return _e(r,e)}y.major=wu;function wu(e,r){return new S(e,r).major}y.minor=Su;function Su(e,r){return new S(e,r).minor}y.patch=Pu;function Pu(e,r){return new S(e,r).patch}y.compare=ce;function ce(e,r,t){return new S(e,t).compare(new S(r,t))}y.compareLoose=xu;function xu(e,r){return ce(e,r,!0)}y.rcompare=Eu;function Eu(e,r,t){return ce(r,e,t)}y.sort=Ou;function Ou(e,r){return e.sort(function(t,n){return y.compare(t,n,r)})}y.rsort=Du;function Du(e,r){return e.sort(function(t,n){return y.rcompare(t,n,r)})}y.gt=Ke;function Ke(e,r,t){return ce(e,r,t)>0}y.lt=Nr;function Nr(e,r,t){return ce(e,r,t)<0}y.eq=Ot;function Ot(e,r,t){return ce(e,r,t)===0}y.neq=Ki;function Ki(e,r,t){return ce(e,r,t)!==0}y.gte=Dt;function Dt(e,r,t){return ce(e,r,t)>=0}y.lte=It;function It(e,r,t){return ce(e,r,t)<=0}y.cmp=Ar;function Ar(e,r,t,n){switch(r){case"===":return typeof e=="object"&&(e=e.version),typeof t=="object"&&(t=t.version),e===t;case"!==":return typeof e=="object"&&(e=e.version),typeof t=="object"&&(t=t.version),e!==t;case"":case"=":case"==":return Ot(e,t,n);case"!=":return Ki(e,t,n);case">":return Ke(e,t,n);case">=":return Dt(e,t,n);case"<":return Nr(e,t,n);case"<=":return It(e,t,n);default:throw new TypeError("Invalid operator: "+r)}}y.Comparator=K;function K(e,r){if((!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1}),e instanceof K){if(e.loose===!!r.loose)return e;e=e.value}if(!(this instanceof K))return new K(e,r);e=e.trim().split(/\s+/).join(" "),O("comparator",e,r),this.options=r,this.loose=!!r.loose,this.parse(e),this.semver===Qe?this.value="":this.value=this.operator+this.semver.version,O("comp",this)}var Qe={};K.prototype.parse=function(e){var r=this.options.loose?I[xt]:I[Et],t=e.match(r);if(!t)throw new TypeError("Invalid comparator: "+e);this.operator=t[1],this.operator==="="&&(this.operator=""),t[2]?this.semver=new S(t[2],this.options.loose):this.semver=Qe};K.prototype.toString=function(){return this.value};K.prototype.test=function(e){return O("Comparator.test",e,this.options.loose),this.semver===Qe?!0:(typeof e=="string"&&(e=new S(e,this.options)),Ar(e,this.operator,this.semver,this.options))};K.prototype.intersects=function(e,r){if(!(e instanceof K))throw new TypeError("a Comparator is required");(!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1});var t;if(this.operator==="")return t=new A(e.value,r),Rr(this.value,t,r);if(e.operator==="")return t=new A(this.value,r),Rr(e.semver,t,r);var n=(this.operator===">="||this.operator===">")&&(e.operator===">="||e.operator===">"),i=(this.operator==="<="||this.operator==="<")&&(e.operator==="<="||e.operator==="<"),a=this.semver.version===e.semver.version,s=(this.operator===">="||this.operator==="<=")&&(e.operator===">="||e.operator==="<="),o=Ar(this.semver,"<",e.semver,r)&&(this.operator===">="||this.operator===">")&&(e.operator==="<="||e.operator==="<"),u=Ar(this.semver,">",e.semver,r)&&(this.operator==="<="||this.operator==="<")&&(e.operator===">="||e.operator===">");return n||i||a&&s||o||u};y.Range=A;function A(e,r){if((!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1}),e instanceof A)return e.loose===!!r.loose&&e.includePrerelease===!!r.includePrerelease?e:new A(e.raw,r);if(e instanceof K)return new A(e.value,r);if(!(this instanceof A))return new A(e,r);if(this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease,this.raw=e.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(function(t){return this.parseRange(t.trim())},this).filter(function(t){return t.length}),!this.set.length)throw new TypeError("Invalid SemVer Range: "+this.raw);this.format()}A.prototype.format=function(){return this.range=this.set.map(function(e){return e.join(" ").trim()}).join("||").trim(),this.range};A.prototype.toString=function(){return this.range};A.prototype.parseRange=function(e){var r=this.options.loose,t=r?I[Ji]:I[Vi];e=e.replace(t,Gu),O("hyphen replace",e),e=e.replace(I[Ce],vu),O("comparator trim",e,I[Ce]),e=e.replace(I[Fe],hu),e=e.replace(I[Be],mu);var n=r?I[xt]:I[Et],i=e.split(" ").map(function(a){return Nu(a,this.options)},this).join(" ").split(/\s+/);return this.options.loose&&(i=i.filter(function(a){return!!a.match(n)})),i=i.map(function(a){return new K(a,this.options)},this),i};A.prototype.intersects=function(e,r){if(!(e instanceof A))throw new TypeError("a Range is required");return this.set.some(function(t){return t.every(function(n){return e.set.some(function(i){return i.every(function(a){return n.intersects(a,r)})})})})};y.toComparators=Iu;function Iu(e,r){return new A(e,r).set.map(function(t){return t.map(function(n){return n.value}).join(" ").trim().split(" ")})}function Nu(e,r){return O("comp",e,r),e=ku(e,r),O("caret",e),e=Au(e,r),O("tildes",e),e=Tu(e,r),O("xrange",e),e=Bu(e,r),O("stars",e),e}function Y(e){return!e||e.toLowerCase()==="x"||e==="*"}function Au(e,r){return e.trim().split(/\s+/).map(function(t){return Ru(t,r)}).join(" ")}function Ru(e,r){var t=r.loose?I[Hi]:I[Wi];return e.replace(t,function(n,i,a,s,o){O("tilde",e,n,i,a,s,o);var u;return Y(i)?u="":Y(a)?u=">="+i+".0.0 <"+(+i+1)+".0.0":Y(s)?u=">="+i+"."+a+".0 <"+i+"."+(+a+1)+".0":o?(O("replaceTilde pr",o),u=">="+i+"."+a+"."+s+"-"+o+" <"+i+"."+(+a+1)+".0"):u=">="+i+"."+a+"."+s+" <"+i+"."+(+a+1)+".0",O("tilde return",u),u})}function ku(e,r){return e.trim().split(/\s+/).map(function(t){return _u(t,r)}).join(" ")}function _u(e,r){O("caret",e,r);var t=r.loose?I[zi]:I[Yi];return e.replace(t,function(n,i,a,s,o){O("caret",e,n,i,a,s,o);var u;return Y(i)?u="":Y(a)?u=">="+i+".0.0 <"+(+i+1)+".0.0":Y(s)?i==="0"?u=">="+i+"."+a+".0 <"+i+"."+(+a+1)+".0":u=">="+i+"."+a+".0 <"+(+i+1)+".0.0":o?(O("replaceCaret pr",o),i==="0"?a==="0"?u=">="+i+"."+a+"."+s+"-"+o+" <"+i+"."+a+"."+(+s+1):u=">="+i+"."+a+"."+s+"-"+o+" <"+i+"."+(+a+1)+".0":u=">="+i+"."+a+"."+s+"-"+o+" <"+(+i+1)+".0.0"):(O("no pr"),i==="0"?a==="0"?u=">="+i+"."+a+"."+s+" <"+i+"."+a+"."+(+s+1):u=">="+i+"."+a+"."+s+" <"+i+"."+(+a+1)+".0":u=">="+i+"."+a+"."+s+" <"+(+i+1)+".0.0"),O("caret return",u),u})}function Tu(e,r){return O("replaceXRanges",e,r),e.split(/\s+/).map(function(t){return Fu(t,r)}).join(" ")}function Fu(e,r){e=e.trim();var t=r.loose?I[qi]:I[Ui];return e.replace(t,function(n,i,a,s,o,u){O("xRange",e,n,i,a,s,o,u);var f=Y(a),l=f||Y(s),h=l||Y(o),b=h;return i==="="&&b&&(i=""),f?i===">"||i==="<"?n="<0.0.0":n="*":i&&b?(l&&(s=0),o=0,i===">"?(i=">=",l?(a=+a+1,s=0,o=0):(s=+s+1,o=0)):i==="<="&&(i="<",l?a=+a+1:s=+s+1),n=i+a+"."+s+"."+o):l?n=">="+a+".0.0 <"+(+a+1)+".0.0":h&&(n=">="+a+"."+s+".0 <"+a+"."+(+s+1)+".0"),O("xRange return",n),n})}function Bu(e,r){return O("replaceStars",e,r),e.trim().replace(I[Xi],"")}function Gu(e,r,t,n,i,a,s,o,u,f,l,h,b){return Y(t)?r="":Y(n)?r=">="+t+".0.0":Y(i)?r=">="+t+"."+n+".0":r=">="+r,Y(u)?o="":Y(f)?o="<"+(+u+1)+".0.0":Y(l)?o="<"+u+"."+(+f+1)+".0":h?o="<="+u+"."+f+"."+l+"-"+h:o="<="+o,(r+" "+o).trim()}A.prototype.test=function(e){if(!e)return!1;typeof e=="string"&&(e=new S(e,this.options));for(var r=0;r<this.set.length;r++)if(Mu(this.set[r],e,this.options))return!0;return!1};function Mu(e,r,t){for(var n=0;n<e.length;n++)if(!e[n].test(r))return!1;if(r.prerelease.length&&!t.includePrerelease){for(n=0;n<e.length;n++)if(O(e[n].semver),e[n].semver!==Qe&&e[n].semver.prerelease.length>0){var i=e[n].semver;if(i.major===r.major&&i.minor===r.minor&&i.patch===r.patch)return!0}return!1}return!0}y.satisfies=Rr;function Rr(e,r,t){try{r=new A(r,t)}catch{return!1}return r.test(e)}y.maxSatisfying=ju;function ju(e,r,t){var n=null,i=null;try{var a=new A(r,t)}catch{return null}return e.forEach(function(s){a.test(s)&&(!n||i.compare(s)===-1)&&(n=s,i=new S(n,t))}),n}y.minSatisfying=Uu;function Uu(e,r,t){var n=null,i=null;try{var a=new A(r,t)}catch{return null}return e.forEach(function(s){a.test(s)&&(!n||i.compare(s)===1)&&(n=s,i=new S(n,t))}),n}y.minVersion=qu;function qu(e,r){e=new A(e,r);var t=new S("0.0.0");if(e.test(t)||(t=new S("0.0.0-0"),e.test(t)))return t;t=null;for(var n=0;n<e.set.length;++n){var i=e.set[n];i.forEach(function(a){var s=new S(a.semver.version);switch(a.operator){case">":s.prerelease.length===0?s.patch++:s.prerelease.push(0),s.raw=s.format();case"":case">=":(!t||Ke(t,s))&&(t=s);break;case"<":case"<=":break;default:throw new Error("Unexpected operation: "+a.operator)}})}return t&&e.test(t)?t:null}y.validRange=$u;function $u(e,r){try{return new A(e,r).range||"*"}catch{return null}}y.ltr=Wu;function Wu(e,r,t){return Nt(e,r,"<",t)}y.gtr=Hu;function Hu(e,r,t){return Nt(e,r,">",t)}y.outside=Nt;function Nt(e,r,t,n){e=new S(e,n),r=new A(r,n);var i,a,s,o,u;switch(t){case">":i=Ke,a=It,s=Nr,o=">",u=">=";break;case"<":i=Nr,a=Dt,s=Ke,o="<",u="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(Rr(e,r,n))return!1;for(var f=0;f<r.set.length;++f){var l=r.set[f],h=null,b=null;if(l.forEach(function(x){x.semver===Qe&&(x=new K(">=0.0.0")),h=h||x,b=b||x,i(x.semver,h.semver,n)?h=x:s(x.semver,b.semver,n)&&(b=x)}),h.operator===o||h.operator===u||(!b.operator||b.operator===o)&&a(e,b.semver))return!1;if(b.operator===u&&s(e,b.semver))return!1}return!0}y.prerelease=Yu;function Yu(e,r){var t=we(e,r);return t&&t.prerelease.length?t.prerelease:null}y.intersects=zu;function zu(e,r,t){return e=new A(e,t),r=new A(r,t),e.intersects(r)}y.coerce=Vu;function Vu(e){if(e instanceof S)return e;if(typeof e!="string")return null;var r=e.match(I[$i]);return r==null?null:we(r[1]+"."+(r[2]||"0")+"."+(r[3]||"0"))}});var At=p((b1,Ju)=>{Ju.exports=["0BSD","3D-Slicer-1.0","AAL","ADSL","AFL-1.1","AFL-1.2","AFL-2.0","AFL-2.1","AFL-3.0","AGPL-1.0-only","AGPL-1.0-or-later","AGPL-3.0-only","AGPL-3.0-or-later","AMD-newlib","AMDPLPA","AML","AML-glslang","AMPAS","ANTLR-PD","ANTLR-PD-fallback","APAFML","APL-1.0","APSL-1.0","APSL-1.1","APSL-1.2","APSL-2.0","ASWF-Digital-Assets-1.0","ASWF-Digital-Assets-1.1","Abstyles","AdaCore-doc","Adobe-2006","Adobe-Display-PostScript","Adobe-Glyph","Adobe-Utopia","Afmparse","Aladdin","Apache-1.0","Apache-1.1","Apache-2.0","App-s2p","Arphic-1999","Artistic-1.0","Artistic-1.0-Perl","Artistic-1.0-cl8","Artistic-2.0","BSD-1-Clause","BSD-2-Clause","BSD-2-Clause-Darwin","BSD-2-Clause-Patent","BSD-2-Clause-Views","BSD-2-Clause-first-lines","BSD-3-Clause","BSD-3-Clause-Attribution","BSD-3-Clause-Clear","BSD-3-Clause-HP","BSD-3-Clause-LBNL","BSD-3-Clause-Modification","BSD-3-Clause-No-Military-License","BSD-3-Clause-No-Nuclear-License","BSD-3-Clause-No-Nuclear-License-2014","BSD-3-Clause-No-Nuclear-Warranty","BSD-3-Clause-Open-MPI","BSD-3-Clause-Sun","BSD-3-Clause-acpica","BSD-3-Clause-flex","BSD-4-Clause","BSD-4-Clause-Shortened","BSD-4-Clause-UC","BSD-4.3RENO","BSD-4.3TAHOE","BSD-Advertising-Acknowledgement","BSD-Attribution-HPND-disclaimer","BSD-Inferno-Nettverk","BSD-Protection","BSD-Source-Code","BSD-Source-beginning-file","BSD-Systemics","BSD-Systemics-W3Works","BSL-1.0","BUSL-1.1","Baekmuk","Bahyph","Barr","Beerware","BitTorrent-1.0","BitTorrent-1.1","Bitstream-Charter","Bitstream-Vera","BlueOak-1.0.0","Boehm-GC","Borceux","Brian-Gladman-2-Clause","Brian-Gladman-3-Clause","C-UDA-1.0","CAL-1.0","CAL-1.0-Combined-Work-Exception","CATOSL-1.1","CC-BY-1.0","CC-BY-2.0","CC-BY-2.5","CC-BY-2.5-AU","CC-BY-3.0","CC-BY-3.0-AT","CC-BY-3.0-AU","CC-BY-3.0-DE","CC-BY-3.0-IGO","CC-BY-3.0-NL","CC-BY-3.0-US","CC-BY-4.0","CC-BY-NC-1.0","CC-BY-NC-2.0","CC-BY-NC-2.5","CC-BY-NC-3.0","CC-BY-NC-3.0-DE","CC-BY-NC-4.0","CC-BY-NC-ND-1.0","CC-BY-NC-ND-2.0","CC-BY-NC-ND-2.5","CC-BY-NC-ND-3.0","CC-BY-NC-ND-3.0-DE","CC-BY-NC-ND-3.0-IGO","CC-BY-NC-ND-4.0","CC-BY-NC-SA-1.0","CC-BY-NC-SA-2.0","CC-BY-NC-SA-2.0-DE","CC-BY-NC-SA-2.0-FR","CC-BY-NC-SA-2.0-UK","CC-BY-NC-SA-2.5","CC-BY-NC-SA-3.0","CC-BY-NC-SA-3.0-DE","CC-BY-NC-SA-3.0-IGO","CC-BY-NC-SA-4.0","CC-BY-ND-1.0","CC-BY-ND-2.0","CC-BY-ND-2.5","CC-BY-ND-3.0","CC-BY-ND-3.0-DE","CC-BY-ND-4.0","CC-BY-SA-1.0","CC-BY-SA-2.0","CC-BY-SA-2.0-UK","CC-BY-SA-2.1-JP","CC-BY-SA-2.5","CC-BY-SA-3.0","CC-BY-SA-3.0-AT","CC-BY-SA-3.0-DE","CC-BY-SA-3.0-IGO","CC-BY-SA-4.0","CC-PDDC","CC0-1.0","CDDL-1.0","CDDL-1.1","CDL-1.0","CDLA-Permissive-1.0","CDLA-Permissive-2.0","CDLA-Sharing-1.0","CECILL-1.0","CECILL-1.1","CECILL-2.0","CECILL-2.1","CECILL-B","CECILL-C","CERN-OHL-1.1","CERN-OHL-1.2","CERN-OHL-P-2.0","CERN-OHL-S-2.0","CERN-OHL-W-2.0","CFITSIO","CMU-Mach","CMU-Mach-nodoc","CNRI-Jython","CNRI-Python","CNRI-Python-GPL-Compatible","COIL-1.0","CPAL-1.0","CPL-1.0","CPOL-1.02","CUA-OPL-1.0","Caldera","Caldera-no-preamble","Catharon","ClArtistic","Clips","Community-Spec-1.0","Condor-1.1","Cornell-Lossless-JPEG","Cronyx","Crossword","CrystalStacker","Cube","D-FSL-1.0","DEC-3-Clause","DL-DE-BY-2.0","DL-DE-ZERO-2.0","DOC","DRL-1.0","DRL-1.1","DSDP","DocBook-Schema","DocBook-XML","Dotseqn","ECL-1.0","ECL-2.0","EFL-1.0","EFL-2.0","EPICS","EPL-1.0","EPL-2.0","EUDatagrid","EUPL-1.0","EUPL-1.1","EUPL-1.2","Elastic-2.0","Entessa","ErlPL-1.1","Eurosym","FBM","FDK-AAC","FSFAP","FSFAP-no-warranty-disclaimer","FSFUL","FSFULLR","FSFULLRWD","FTL","Fair","Ferguson-Twofish","Frameworx-1.0","FreeBSD-DOC","FreeImage","Furuseth","GCR-docs","GD","GFDL-1.1-invariants-only","GFDL-1.1-invariants-or-later","GFDL-1.1-no-invariants-only","GFDL-1.1-no-invariants-or-later","GFDL-1.1-only","GFDL-1.1-or-later","GFDL-1.2-invariants-only","GFDL-1.2-invariants-or-later","GFDL-1.2-no-invariants-only","GFDL-1.2-no-invariants-or-later","GFDL-1.2-only","GFDL-1.2-or-later","GFDL-1.3-invariants-only","GFDL-1.3-invariants-or-later","GFDL-1.3-no-invariants-only","GFDL-1.3-no-invariants-or-later","GFDL-1.3-only","GFDL-1.3-or-later","GL2PS","GLWTPL","GPL-1.0-only","GPL-1.0-or-later","GPL-2.0-only","GPL-2.0-or-later","GPL-3.0-only","GPL-3.0-or-later","Giftware","Glide","Glulxe","Graphics-Gems","Gutmann","HIDAPI","HP-1986","HP-1989","HPND","HPND-DEC","HPND-Fenneberg-Livingston","HPND-INRIA-IMAG","HPND-Intel","HPND-Kevlin-Henney","HPND-MIT-disclaimer","HPND-Markus-Kuhn","HPND-Netrek","HPND-Pbmplus","HPND-UC","HPND-UC-export-US","HPND-doc","HPND-doc-sell","HPND-export-US","HPND-export-US-acknowledgement","HPND-export-US-modify","HPND-export2-US","HPND-merchantability-variant","HPND-sell-MIT-disclaimer-xserver","HPND-sell-regexpr","HPND-sell-variant","HPND-sell-variant-MIT-disclaimer","HPND-sell-variant-MIT-disclaimer-rev","HTMLTIDY","HaskellReport","Hippocratic-2.1","IBM-pibs","ICU","IEC-Code-Components-EULA","IJG","IJG-short","IPA","IPL-1.0","ISC","ISC-Veillard","ImageMagick","Imlib2","Info-ZIP","Inner-Net-2.0","Intel","Intel-ACPI","Interbase-1.0","JPL-image","JPNIC","JSON","Jam","JasPer-2.0","Kastrup","Kazlib","Knuth-CTAN","LAL-1.2","LAL-1.3","LGPL-2.0-only","LGPL-2.0-or-later","LGPL-2.1-only","LGPL-2.1-or-later","LGPL-3.0-only","LGPL-3.0-or-later","LGPLLR","LOOP","LPD-document","LPL-1.0","LPL-1.02","LPPL-1.0","LPPL-1.1","LPPL-1.2","LPPL-1.3a","LPPL-1.3c","LZMA-SDK-9.11-to-9.20","LZMA-SDK-9.22","Latex2e","Latex2e-translated-notice","Leptonica","LiLiQ-P-1.1","LiLiQ-R-1.1","LiLiQ-Rplus-1.1","Libpng","Linux-OpenIB","Linux-man-pages-1-para","Linux-man-pages-copyleft","Linux-man-pages-copyleft-2-para","Linux-man-pages-copyleft-var","Lucida-Bitmap-Fonts","MIT","MIT-0","MIT-CMU","MIT-Festival","MIT-Khronos-old","MIT-Modern-Variant","MIT-Wu","MIT-advertising","MIT-enna","MIT-feh","MIT-open-group","MIT-testregex","MITNFA","MMIXware","MPEG-SSG","MPL-1.0","MPL-1.1","MPL-2.0","MPL-2.0-no-copyleft-exception","MS-LPL","MS-PL","MS-RL","MTLL","Mackerras-3-Clause","Mackerras-3-Clause-acknowledgment","MakeIndex","Martin-Birgmeier","McPhee-slideshow","Minpack","MirOS","Motosoto","MulanPSL-1.0","MulanPSL-2.0","Multics","Mup","NAIST-2003","NASA-1.3","NBPL-1.0","NCBI-PD","NCGL-UK-2.0","NCL","NCSA","NGPL","NICTA-1.0","NIST-PD","NIST-PD-fallback","NIST-Software","NLOD-1.0","NLOD-2.0","NLPL","NOSL","NPL-1.0","NPL-1.1","NPOSL-3.0","NRL","NTP","NTP-0","Naumen","NetCDF","Newsletr","Nokia","Noweb","O-UDA-1.0","OAR","OCCT-PL","OCLC-2.0","ODC-By-1.0","ODbL-1.0","OFFIS","OFL-1.0","OFL-1.0-RFN","OFL-1.0-no-RFN","OFL-1.1","OFL-1.1-RFN","OFL-1.1-no-RFN","OGC-1.0","OGDL-Taiwan-1.0","OGL-Canada-2.0","OGL-UK-1.0","OGL-UK-2.0","OGL-UK-3.0","OGTSL","OLDAP-1.1","OLDAP-1.2","OLDAP-1.3","OLDAP-1.4","OLDAP-2.0","OLDAP-2.0.1","OLDAP-2.1","OLDAP-2.2","OLDAP-2.2.1","OLDAP-2.2.2","OLDAP-2.3","OLDAP-2.4","OLDAP-2.5","OLDAP-2.6","OLDAP-2.7","OLDAP-2.8","OLFL-1.3","OML","OPL-1.0","OPL-UK-3.0","OPUBL-1.0","OSET-PL-2.1","OSL-1.0","OSL-1.1","OSL-2.0","OSL-2.1","OSL-3.0","OpenPBS-2.3","OpenSSL","OpenSSL-standalone","OpenVision","PADL","PDDL-1.0","PHP-3.0","PHP-3.01","PPL","PSF-2.0","Parity-6.0.0","Parity-7.0.0","Pixar","Plexus","PolyForm-Noncommercial-1.0.0","PolyForm-Small-Business-1.0.0","PostgreSQL","Python-2.0","Python-2.0.1","QPL-1.0","QPL-1.0-INRIA-2004","Qhull","RHeCos-1.1","RPL-1.1","RPL-1.5","RPSL-1.0","RSA-MD","RSCPL","Rdisc","Ruby","Ruby-pty","SAX-PD","SAX-PD-2.0","SCEA","SGI-B-1.0","SGI-B-1.1","SGI-B-2.0","SGI-OpenGL","SGP4","SHL-0.5","SHL-0.51","SISSL","SISSL-1.2","SL","SMLNJ","SMPPL","SNIA","SPL-1.0","SSH-OpenSSH","SSH-short","SSLeay-standalone","SSPL-1.0","SWL","Saxpath","SchemeReport","Sendmail","Sendmail-8.23","SimPL-2.0","Sleepycat","Soundex","Spencer-86","Spencer-94","Spencer-99","SugarCRM-1.1.3","Sun-PPP","Sun-PPP-2000","SunPro","Symlinks","TAPR-OHL-1.0","TCL","TCP-wrappers","TGPPL-1.0","TMate","TORQUE-1.1","TOSL","TPDL","TPL-1.0","TTWL","TTYP0","TU-Berlin-1.0","TU-Berlin-2.0","TermReadKey","UCAR","UCL-1.0","UMich-Merit","UPL-1.0","URT-RLE","Ubuntu-font-1.0","Unicode-3.0","Unicode-DFS-2015","Unicode-DFS-2016","Unicode-TOU","UnixCrypt","Unlicense","VOSTROM","VSL-1.0","Vim","W3C","W3C-19980720","W3C-20150513","WTFPL","Watcom-1.0","Widget-Workshop","Wsuipa","X11","X11-distribute-modifications-variant","X11-swapped","XFree86-1.1","XSkat","Xdebug-1.03","Xerox","Xfig","Xnet","YPL-1.0","YPL-1.1","ZPL-1.1","ZPL-2.0","ZPL-2.1","Zed","Zeeff","Zend-2.0","Zimbra-1.3","Zimbra-1.4","Zlib","any-OSI","bcrypt-Solar-Designer","blessing","bzip2-1.0.6","check-cvs","checkmk","copyleft-next-0.3.0","copyleft-next-0.3.1","curl","cve-tou","diffmark","dtoa","dvipdfm","eGenix","etalab-2.0","fwlw","gSOAP-1.3b","gnuplot","gtkbook","hdparm","iMatix","libpng-2.0","libselinux-1.0","libtiff","libutil-David-Nugent","lsof","magaz","mailprio","metamail","mpi-permissive","mpich2","mplus","pkgconf","pnmstitch","psfrag","psutils","python-ldap","radvd","snprintf","softSurfer","ssh-keyscan","swrule","threeparttable","ulem","w3m","xinetd","xkeyboard-config-Zinoviev","xlock","xpp","xzoom","zlib-acknowledgement"]});var ea=p((y1,Xu)=>{Xu.exports=["AGPL-1.0","AGPL-3.0","BSD-2-Clause-FreeBSD","BSD-2-Clause-NetBSD","GFDL-1.1","GFDL-1.2","GFDL-1.3","GPL-1.0","GPL-2.0","GPL-2.0-with-GCC-exception","GPL-2.0-with-autoconf-exception","GPL-2.0-with-bison-exception","GPL-2.0-with-classpath-exception","GPL-2.0-with-font-exception","GPL-3.0","GPL-3.0-with-GCC-exception","GPL-3.0-with-autoconf-exception","LGPL-2.0","LGPL-2.1","LGPL-3.0","Net-SNMP","Nunit","StandardML-NJ","bzip2-1.0.5","eCos-2.0","wxWindows"]});var ra=p((L1,Ku)=>{Ku.exports=["389-exception","Asterisk-exception","Autoconf-exception-2.0","Autoconf-exception-3.0","Autoconf-exception-generic","Autoconf-exception-generic-3.0","Autoconf-exception-macro","Bison-exception-1.24","Bison-exception-2.2","Bootloader-exception","Classpath-exception-2.0","CLISP-exception-2.0","cryptsetup-OpenSSL-exception","DigiRule-FOSS-exception","eCos-exception-2.0","Fawkes-Runtime-exception","FLTK-exception","fmt-exception","Font-exception-2.0","freertos-exception-2.0","GCC-exception-2.0","GCC-exception-2.0-note","GCC-exception-3.1","Gmsh-exception","GNAT-exception","GNOME-examples-exception","GNU-compiler-exception","gnu-javamail-exception","GPL-3.0-interface-exception","GPL-3.0-linking-exception","GPL-3.0-linking-source-exception","GPL-CC-1.0","GStreamer-exception-2005","GStreamer-exception-2008","i2p-gpl-java-exception","KiCad-libraries-exception","LGPL-3.0-linking-exception","libpri-OpenH323-exception","Libtool-exception","Linux-syscall-note","LLGPL","LLVM-exception","LZMA-exception","mif-exception","OCaml-LGPL-linking-exception","OCCT-exception-1.0","OpenJDK-assembly-exception-1.0","openvpn-openssl-exception","PS-or-PDF-font-exception-20170817","QPL-1.0-INRIA-2004-exception","Qt-GPL-exception-1.0","Qt-LGPL-exception-1.1","Qwt-exception-1.0","SANE-exception","SHL-2.0","SHL-2.1","stunnel-exception","SWI-exception","Swift-exception","Texinfo-exception","u-boot-exception-2.0","UBDL-exception","Universal-FOSS-exception-1.0","vsftpd-openssl-exception","WxWindows-exception-3.1","x11vnc-openssl-exception"]});var na=p((C1,ta)=>{"use strict";var Zu=[].concat(At()).concat(ea()),Qu=ra();ta.exports=function(e){var r=0;function t(){return r<e.length}function n(m){if(m instanceof RegExp){var g=e.slice(r),L=g.match(m);if(L)return r+=L[0].length,L[0]}else if(e.indexOf(m,r)===r)return r+=m.length,m}function i(){n(/[ ]*/)}function a(){for(var m,g=["WITH","AND","OR","(",")",":","+"],L=0;L<g.length&&(m=n(g[L]),!m);L++);if(m==="+"&&r>1&&e[r-2]===" ")throw new Error("Space before `+`");return m&&{type:"OPERATOR",string:m}}function s(){return n(/[A-Za-z0-9-.]+/)}function o(){var m=s();if(!m)throw new Error("Expected idstring at offset "+r);return m}function u(){if(n("DocumentRef-")){var m=o();return{type:"DOCUMENTREF",string:m}}}function f(){if(n("LicenseRef-")){var m=o();return{type:"LICENSEREF",string:m}}}function l(){var m=r,g=s();if(Zu.indexOf(g)!==-1)return{type:"LICENSE",string:g};if(Qu.indexOf(g)!==-1)return{type:"EXCEPTION",string:g};r=m}function h(){return a()||u()||f()||l()}for(var b=[];t()&&(i(),!!t());){var x=h();if(!x)throw new Error("Unexpected `"+e[r]+"` at offset "+r);b.push(x)}return b}});var aa=p((w1,ia)=>{"use strict";ia.exports=function(e){var r=0;function t(){return r<e.length}function n(){return t()?e[r]:null}function i(){if(!t())throw new Error;r++}function a(g){var L=n();if(L&&L.type==="OPERATOR"&&g===L.string)return i(),L.string}function s(){if(a("WITH")){var g=n();if(g&&g.type==="EXCEPTION")return i(),g.string;throw new Error("Expected exception after `WITH`")}}function o(){var g=r,L="",N=n();if(N.type==="DOCUMENTREF"&&(i(),L+="DocumentRef-"+N.string+":",!a(":")))throw new Error("Expected `:` after `DocumentRef-...`");if(N=n(),N.type==="LICENSEREF")return i(),L+="LicenseRef-"+N.string,{license:L};r=g}function u(){var g=n();if(g&&g.type==="LICENSE"){i();var L={license:g.string};a("+")&&(L.plus=!0);var N=s();return N&&(L.exception=N),L}}function f(){var g=a("(");if(g){var L=x();if(!a(")"))throw new Error("Expected `)`");return L}}function l(){return f()||o()||u()}function h(g,L){return function N(){var ae=L();if(ae){if(!a(g))return ae;var M=N();if(!M)throw new Error("Expected expression");return{left:ae,conjunction:g.toLowerCase(),right:M}}}}var b=h("AND",l),x=h("OR",b),m=x();if(!m||t())throw new Error("Syntax error");return m}});var Rt=p((S1,sa)=>{"use strict";var ec=na(),rc=aa();sa.exports=function(e){return rc(ec(e))}});var ma=p((P1,ha)=>{var tc=Rt(),nc=At();function Fr(e){try{return tc(e),!0}catch{return!1}}function da(e,r){var t=r[0].length-e[0].length;return t!==0?t:e[0].toUpperCase().localeCompare(r[0].toUpperCase())}var oa=[["APGL","AGPL"],["Gpl","GPL"],["GLP","GPL"],["APL","Apache"],["ISD","ISC"],["GLP","GPL"],["IST","ISC"],["Claude","Clause"],[" or later","+"],[" International",""],["GNU","GPL"],["GUN","GPL"],["+",""],["GNU GPL","GPL"],["GNU LGPL","LGPL"],["GNU/GPL","GPL"],["GNU GLP","GPL"],["GNU LESSER GENERAL PUBLIC LICENSE","LGPL"],["GNU Lesser General Public License","LGPL"],["GNU LESSER GENERAL PUBLIC LICENSE","LGPL-2.1"],["GNU Lesser General Public License","LGPL-2.1"],["LESSER GENERAL PUBLIC LICENSE","LGPL"],["Lesser General Public License","LGPL"],["LESSER GENERAL PUBLIC LICENSE","LGPL-2.1"],["Lesser General Public License","LGPL-2.1"],["GNU General Public License","GPL"],["Gnu public license","GPL"],["GNU Public License","GPL"],["GNU GENERAL PUBLIC LICENSE","GPL"],["MTI","MIT"],["Mozilla Public License","MPL"],["Universal Permissive License","UPL"],["WTH","WTF"],["WTFGPL","WTFPL"],["-License",""]].sort(da),ic=0,ac=1,ua=[function(e){return e.toUpperCase()},function(e){return e.trim()},function(e){return e.replace(/\./g,"")},function(e){return e.replace(/\s+/g,"")},function(e){return e.replace(/\s+/g,"-")},function(e){return e.replace("v","-")},function(e){return e.replace(/,?\s*(\d)/,"-$1")},function(e){return e.replace(/,?\s*(\d)/,"-$1.0")},function(e){return e.replace(/,?\s*(V\.|v\.|V|v|Version|version)\s*(\d)/,"-$2")},function(e){return e.replace(/,?\s*(V\.|v\.|V|v|Version|version)\s*(\d)/,"-$2.0")},function(e){return e[0].toUpperCase()+e.slice(1)},function(e){return e.replace("/","-")},function(e){return e.replace(/\s*V\s*(\d)/,"-$1").replace(/(\d)$/,"$1.0")},function(e){return e.indexOf("3.0")!==-1?e+"-or-later":e+"-only"},function(e){return e+"only"},function(e){return e.replace(/(\d)$/,"-$1.0")},function(e){return e.replace(/(-| )?(\d)$/,"-$2-Clause")},function(e){return e.replace(/(-| )clause(-| )(\d)/,"-$3-Clause")},function(e){return e.replace(/\b(Modified|New|Revised)(-| )?BSD((-| )License)?/i,"BSD-3-Clause")},function(e){return e.replace(/\bSimplified(-| )?BSD((-| )License)?/i,"BSD-2-Clause")},function(e){return e.replace(/\b(Free|Net)(-| )?BSD((-| )License)?/i,"BSD-2-Clause-$1BSD")},function(e){return e.replace(/\bClear(-| )?BSD((-| )License)?/i,"BSD-3-Clause-Clear")},function(e){return e.replace(/\b(Old|Original)(-| )?BSD((-| )License)?/i,"BSD-4-Clause")},function(e){return"CC-"+e},function(e){return"CC-"+e+"-4.0"},function(e){return e.replace("Attribution","BY").replace("NonCommercial","NC").replace("NoDerivatives","ND").replace(/ (\d)/,"-$1").replace(/ ?International/,"")},function(e){return"CC-"+e.replace("Attribution","BY").replace("NonCommercial","NC").replace("NoDerivatives","ND").replace(/ (\d)/,"-$1").replace(/ ?International/,"")+"-4.0"}],kt=nc.map(function(e){var r=/^(.*)-\d+\.\d+$/.exec(e);return r?[r[0],r[1]]:[e,null]}).reduce(function(e,r){var t=r[1];return e[t]=e[t]||[],e[t].push(r[0]),e},{}),sc=Object.keys(kt).map(function(r){return[r,kt[r]]}).filter(function(r){return r[1].length===1&&r[0]!==null&&r[0]!=="APL"}).map(function(r){return[r[0],r[1][0]]});kt=void 0;var ca=[["UNLI","Unlicense"],["WTF","WTFPL"],["2 CLAUSE","BSD-2-Clause"],["2-CLAUSE","BSD-2-Clause"],["3 CLAUSE","BSD-3-Clause"],["3-CLAUSE","BSD-3-Clause"],["AFFERO","AGPL-3.0-or-later"],["AGPL","AGPL-3.0-or-later"],["APACHE","Apache-2.0"],["ARTISTIC","Artistic-2.0"],["Affero","AGPL-3.0-or-later"],["BEER","Beerware"],["BOOST","BSL-1.0"],["BSD","BSD-2-Clause"],["CDDL","CDDL-1.1"],["ECLIPSE","EPL-1.0"],["FUCK","WTFPL"],["GNU","GPL-3.0-or-later"],["LGPL","LGPL-3.0-or-later"],["GPLV1","GPL-1.0-only"],["GPL-1","GPL-1.0-only"],["GPLV2","GPL-2.0-only"],["GPL-2","GPL-2.0-only"],["GPL","GPL-3.0-or-later"],["MIT +NO-FALSE-ATTRIBS","MITNFA"],["MIT","MIT"],["MPL","MPL-2.0"],["X11","X11"],["ZLIB","Zlib"]].concat(sc).sort(da),oc=0,uc=1,la=function(e){for(var r=0;r<ua.length;r++){var t=ua[r](e).trim();if(t!==e&&Fr(t))return t}return null},fa=function(e){for(var r=e.toUpperCase(),t=0;t<ca.length;t++){var n=ca[t];if(r.indexOf(n[oc])>-1)return n[uc]}return null},pa=function(e,r){for(var t=0;t<oa.length;t++){var n=oa[t],i=n[ic];if(e.indexOf(i)>-1){var a=e.replace(i,n[ac]),s=r(a);if(s!==null)return s}}return null};ha.exports=function(e,r){r=r||{};var t=r.upgrade===void 0?!0:!!r.upgrade;function n(o){return t?cc(o):o}var i=typeof e=="string"&&e.trim().length!==0;if(!i)throw Error("Invalid argument. Expected non-empty string.");if(e=e.trim(),Fr(e))return n(e);var a=e.replace(/\+$/,"").trim();if(Fr(a))return n(a);var s=la(e);return s!==null||(s=pa(e,function(o){return Fr(o)?o:la(o)}),s!==null)||(s=fa(e),s!==null)||(s=pa(e,fa),s!==null)?n(s):null};function cc(e){return["GPL-1.0","LGPL-1.0","AGPL-1.0","GPL-2.0","LGPL-2.0","AGPL-2.0","LGPL-2.1"].indexOf(e)!==-1?e+"-only":["GPL-1.0+","GPL-2.0+","GPL-3.0+","LGPL-2.0+","LGPL-2.1+","LGPL-3.0+","AGPL-1.0+","AGPL-3.0+"].indexOf(e)!==-1?e.replace(/\+$/,"-or-later"):["GPL-3.0","LGPL-3.0","AGPL-3.0"].indexOf(e)!==-1?e+"-or-later":e}});var ya=p((x1,ba)=>{var lc=Rt(),fc=ma(),va='license should be a valid SPDX license expression (without "LicenseRef"), "UNLICENSED", or "SEE LICENSE IN <filename>"',pc=/^SEE LICEN[CS]E IN (.+)$/;function ga(e,r){return r.slice(0,e.length)===e}function _t(e){if(e.hasOwnProperty("license")){var r=e.license;return ga("LicenseRef",r)||ga("DocumentRef",r)}else return _t(e.left)||_t(e.right)}ba.exports=function(e){var r;try{r=lc(e)}catch{var t;if(e==="UNLICENSED"||e==="UNLICENCED")return{validForOldPackages:!0,validForNewPackages:!0,unlicensed:!0};if(t=pc.exec(e))return{validForOldPackages:!0,validForNewPackages:!0,inFile:t[1]};var n={validForOldPackages:!1,validForNewPackages:!1,warnings:[va]};if(e.trim().length!==0){var i=fc(e);i&&n.warnings.push('license is similar to the valid expression "'+i+'"')}return n}return _t(r)?{validForNewPackages:!1,validForOldPackages:!1,spdx:!0,warnings:[va]}:{validForNewPackages:!0,validForOldPackages:!0,spdx:!0}}});var Tt=p((E1,wa)=>{"use strict";var er=wa.exports={github:{protocols:["git","http","git+ssh","git+https","ssh","https"],domain:"github.com",treepath:"tree",filetemplate:"https://{auth@}raw.githubusercontent.com/{user}/{project}/{committish}/{path}",bugstemplate:"https://{domain}/{user}/{project}/issues",gittemplate:"git://{auth@}{domain}/{user}/{project}.git{#committish}",tarballtemplate:"https://codeload.{domain}/{user}/{project}/tar.gz/{committish}"},bitbucket:{protocols:["git+ssh","git+https","ssh","https"],domain:"bitbucket.org",treepath:"src",tarballtemplate:"https://{domain}/{user}/{project}/get/{committish}.tar.gz"},gitlab:{protocols:["git+ssh","git+https","ssh","https"],domain:"gitlab.com",treepath:"tree",bugstemplate:"https://{domain}/{user}/{project}/issues",httpstemplate:"git+https://{auth@}{domain}/{user}/{projectPath}.git{#committish}",tarballtemplate:"https://{domain}/{user}/{project}/repository/archive.tar.gz?ref={committish}",pathmatch:/^[/]([^/]+)[/]((?!.*(\/-\/|\/repository\/archive\.tar\.gz\?=.*|\/repository\/[^/]+\/archive.tar.gz$)).*?)(?:[.]git|[/])?$/},gist:{protocols:["git","git+ssh","git+https","ssh","https"],domain:"gist.github.com",pathmatch:/^[/](?:([^/]+)[/])?([a-z0-9]{32,})(?:[.]git)?$/,filetemplate:"https://gist.githubusercontent.com/{user}/{project}/raw{/committish}/{path}",bugstemplate:"https://{domain}/{project}",gittemplate:"git://{domain}/{project}.git{#committish}",sshtemplate:"git@{domain}:/{project}.git{#committish}",sshurltemplate:"git+ssh://git@{domain}/{project}.git{#committish}",browsetemplate:"https://{domain}/{project}{/committish}",browsefiletemplate:"https://{domain}/{project}{/committish}{#path}",docstemplate:"https://{domain}/{project}{/committish}",httpstemplate:"git+https://{domain}/{project}.git{#committish}",shortcuttemplate:"{type}:{project}{#committish}",pathtemplate:"{project}{#committish}",tarballtemplate:"https://codeload.github.com/gist/{project}/tar.gz/{committish}",hashformat:function(e){return"file-"+Ca(e)}}},La={sshtemplate:"git@{domain}:{user}/{project}.git{#committish}",sshurltemplate:"git+ssh://git@{domain}/{user}/{project}.git{#committish}",browsetemplate:"https://{domain}/{user}/{project}{/tree/committish}",browsefiletemplate:"https://{domain}/{user}/{project}/{treepath}/{committish}/{path}{#fragment}",docstemplate:"https://{domain}/{user}/{project}{/tree/committish}#readme",httpstemplate:"git+https://{auth@}{domain}/{user}/{project}.git{#committish}",filetemplate:"https://{domain}/{user}/{project}/raw/{committish}/{path}",shortcuttemplate:"{type}:{user}/{project}{#committish}",pathtemplate:"{user}/{project}{#committish}",pathmatch:/^[/]([^/]+)[/]([^/]+?)(?:[.]git|[/])?$/,hashformat:Ca};Object.keys(er).forEach(function(e){Object.keys(La).forEach(function(r){er[e][r]||(er[e][r]=La[r])}),er[e].protocols_re=RegExp("^("+er[e].protocols.map(function(r){return r.replace(/([\\+*{}()[\]$^|])/g,"\\$1")}).join("|")+"):$")});function Ca(e){return e.toLowerCase().replace(/^\W+|\/|\W+$/g,"").replace(/\W+/g,"-")}});var xa=p((O1,Pa)=>{"use strict";var Sa=Tt(),Me=Object.assign||function(r,t){if(t===null||typeof t!="object")return r;for(var n=Object.keys(t),i=n.length;i--;)r[n[i]]=t[n[i]];return r};Pa.exports=q;function q(e,r,t,n,i,a,s){var o=this;o.type=e,Object.keys(Sa[e]).forEach(function(u){o[u]=Sa[e][u]}),o.user=r,o.auth=t,o.project=n,o.committish=i,o.default=a,o.opts=s||{}}q.prototype.hash=function(){return this.committish?"#"+this.committish:""};q.prototype._fill=function(e,r){if(e){var t=Me({},r);t.path=t.path?t.path.replace(/^[/]+/g,""):"",r=Me(Me({},this.opts),r);var n=this;Object.keys(this).forEach(function(l){n[l]!=null&&t[l]==null&&(t[l]=n[l])});var i=t.auth,a=t.committish,s=t.fragment,o=t.path,u=t.project;Object.keys(t).forEach(function(l){var h=t[l];(l==="path"||l==="project")&&typeof h=="string"?t[l]=h.split("/").map(function(b){return encodeURIComponent(b)}).join("/"):t[l]=encodeURIComponent(h)}),t["auth@"]=i?i+"@":"",t["#fragment"]=s?"#"+this.hashformat(s):"",t.fragment=t.fragment?t.fragment:"",t["#path"]=o?"#"+this.hashformat(o):"",t["/path"]=t.path?"/"+t.path:"",t.projectPath=u.split("/").map(encodeURIComponent).join("/"),r.noCommittish?(t["#committish"]="",t["/tree/committish"]="",t["/committish"]="",t.committish=""):(t["#committish"]=a?"#"+a:"",t["/tree/committish"]=t.committish?"/"+t.treepath+"/"+t.committish:"",t["/committish"]=t.committish?"/"+t.committish:"",t.committish=t.committish||"master");var f=e;return Object.keys(t).forEach(function(l){f=f.replace(new RegExp("[{]"+l+"[}]","g"),t[l])}),r.noGitPlus?f.replace(/^git[+]/,""):f}};q.prototype.ssh=function(e){return this._fill(this.sshtemplate,e)};q.prototype.sshurl=function(e){return this._fill(this.sshurltemplate,e)};q.prototype.browse=function(e,r,t){return typeof e=="string"?(typeof r!="string"&&(t=r,r=null),this._fill(this.browsefiletemplate,Me({fragment:r,path:e},t))):this._fill(this.browsetemplate,e)};q.prototype.docs=function(e){return this._fill(this.docstemplate,e)};q.prototype.bugs=function(e){return this._fill(this.bugstemplate,e)};q.prototype.https=function(e){return this._fill(this.httpstemplate,e)};q.prototype.git=function(e){return this._fill(this.gittemplate,e)};q.prototype.shortcut=function(e){return this._fill(this.shortcuttemplate,e)};q.prototype.path=function(e){return this._fill(this.pathtemplate,e)};q.prototype.tarball=function(e){var r=Me({},e,{noCommittish:!1});return this._fill(this.tarballtemplate,r)};q.prototype.file=function(e,r){return this._fill(this.filetemplate,Me({path:e},r))};q.prototype.getDefaultRepresentation=function(){return this.default};q.prototype.toString=function(e){return this.default&&typeof this[this.default]=="function"?this[this.default](e):this.sshurl(e)}});var Oa=p((D1,Bt)=>{"use strict";var Br=require("url"),Ea=Tt(),dc=Bt.exports=xa(),hc={"git+ssh:":"sshurl","git+https:":"https","ssh:":"sshurl","git:":"git"};function mc(e){return hc[e]||e.slice(0,-1)}var vc={"git:":!0,"https:":!0,"git+https:":!0,"http:":!0,"git+http:":!0},Ft={};Bt.exports.fromUrl=function(e,r){if(typeof e=="string"){var t=e+JSON.stringify(r||{});return t in Ft||(Ft[t]=gc(e,r)),Ft[t]}};function gc(e,r){if(!(e==null||e==="")){var t=yc(bc(e)?"github:"+e:e),n=Lc(t),i=t.match(/^([^:]+):(?:[^@]+@)?(?:([^/]*)\/)?([^#]+)/),a=Object.keys(Ea).map(function(s){try{var o=Ea[s],u=null;n.auth&&vc[n.protocol]&&(u=n.auth);var f=n.hash?decodeURIComponent(n.hash.substr(1)):null,l=null,h=null,b=null;if(i&&i[1]===s)l=i[2]&&decodeURIComponent(i[2]),h=decodeURIComponent(i[3].replace(/\.git$/,"")),b="shortcut";else{if(n.host&&n.host!==o.domain&&n.host.replace(/^www[.]/,"")!==o.domain||!o.protocols_re.test(n.protocol)||!n.path)return;var x=o.pathmatch,m=n.path.match(x);if(!m)return;m[1]!==null&&m[1]!==void 0&&(l=decodeURIComponent(m[1].replace(/^:/,""))),h=decodeURIComponent(m[2]),b=mc(n.protocol)}return new dc(s,l,u,h,f,b,r)}catch(g){if(!(g instanceof URIError))throw g}}).filter(function(s){return s});if(a.length===1)return a[0]}}function bc(e){return/^[^:@%/\s.-][^:@%/\s]*[/][^:@\s/%]+(?:#.*)?$/.test(e)}function yc(e){var r=Br.parse(e);return r.protocol==="gist:"&&r.host&&!r.path?r.protocol+"/"+r.host:e}function Lc(e){var r=e.match(/^([^@]+)@([^:/]+):[/]?((?:[^/]+[/])?[^/]+?)(?:[.]git)?(#.*)?$/);if(!r){var t=Br.parse(e);if(t.auth&&typeof Br.URL=="function"){var n=e.match(/[^@]+@[^:/]+/);if(n){var i=new Br.URL(n[0]);t.auth=i.username||"",i.password&&(t.auth+=":"+i.password)}}return t}return{protocol:"git+ssh:",slashes:!0,auth:r[1],host:r[2],port:null,hostname:r[2],hash:r[4],search:null,query:null,pathname:"/"+r[3],path:"/"+r[3],href:"git+ssh://"+r[1]+"@"+r[2]+"/"+r[3]+(r[4]||"")}}});var Gt=p((I1,Da)=>{"use strict";var Cc=require("os");Da.exports=Cc.homedir||function(){var r=process.env.HOME,t=process.env.LOGNAME||process.env.USER||process.env.LNAME||process.env.USERNAME;return process.platform==="win32"?process.env.USERPROFILE||process.env.HOMEDRIVE+process.env.HOMEPATH||r||null:process.platform==="darwin"?r||(t?"/Users/"+t:null):process.platform==="linux"?r||(process.getuid()===0?"/root":t?"/home/"+t:null):r||null}});var Mt=p((N1,Ia)=>{Ia.exports=function(){var e=Error.prepareStackTrace;Error.prepareStackTrace=function(t,n){return n};var r=new Error().stack;return Error.prepareStackTrace=e,r[2].getFileName()}});var Na=p((A1,rr)=>{"use strict";var wc=process.platform==="win32",Sc=/^(((?:[a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?[\\\/]?)(?:[^\\\/]*[\\\/])*)((\.{1,2}|[^\\\/]+?|)(\.[^.\/\\]*|))[\\\/]*$/,jt={};function Pc(e){return Sc.exec(e).slice(1)}jt.parse=function(e){if(typeof e!="string")throw new TypeError("Parameter 'pathString' must be a string, not "+typeof e);var r=Pc(e);if(!r||r.length!==5)throw new TypeError("Invalid path '"+e+"'");return{root:r[1],dir:r[0]===r[1]?r[0]:r[0].slice(0,-1),base:r[2],ext:r[4],name:r[3]}};var xc=/^((\/?)(?:[^\/]*\/)*)((\.{1,2}|[^\/]+?|)(\.[^.\/]*|))[\/]*$/,Ut={};function Ec(e){return xc.exec(e).slice(1)}Ut.parse=function(e){if(typeof e!="string")throw new TypeError("Parameter 'pathString' must be a string, not "+typeof e);var r=Ec(e);if(!r||r.length!==5)throw new TypeError("Invalid path '"+e+"'");return{root:r[1],dir:r[0].slice(0,-1),base:r[2],ext:r[4],name:r[3]}};wc?rr.exports=jt.parse:rr.exports=Ut.parse;rr.exports.posix=Ut.parse;rr.exports.win32=jt.parse});var qt=p((R1,_a)=>{var ka=require("path"),Aa=ka.parse||Na(),Ra=function(r,t){var n="/";/^([A-Za-z]:)/.test(r)?n="":/^\\\\/.test(r)&&(n="\\\\");for(var i=[r],a=Aa(r);a.dir!==i[i.length-1];)i.push(a.dir),a=Aa(a.dir);return i.reduce(function(s,o){return s.concat(t.map(function(u){return ka.resolve(n,o,u)}))},[])};_a.exports=function(r,t,n){var i=t&&t.moduleDirectory?[].concat(t.moduleDirectory):["node_modules"];if(t&&typeof t.paths=="function")return t.paths(n,r,function(){return Ra(r,i)},t);var a=Ra(r,i);return t&&t.paths?a.concat(t.paths):a}});var $t=p((k1,Ta)=>{Ta.exports=function(e,r){return r||{}}});var Ga=p((_1,Ba)=>{"use strict";var Oc="Function.prototype.bind called on incompatible ",Dc=Object.prototype.toString,Ic=Math.max,Nc="[object Function]",Fa=function(r,t){for(var n=[],i=0;i<r.length;i+=1)n[i]=r[i];for(var a=0;a<t.length;a+=1)n[a+r.length]=t[a];return n},Ac=function(r,t){for(var n=[],i=t||0,a=0;i<r.length;i+=1,a+=1)n[a]=r[i];return n},Rc=function(e,r){for(var t="",n=0;n<e.length;n+=1)t+=e[n],n+1<e.length&&(t+=r);return t};Ba.exports=function(r){var t=this;if(typeof t!="function"||Dc.apply(t)!==Nc)throw new TypeError(Oc+t);for(var n=Ac(arguments,1),i,a=function(){if(this instanceof i){var l=t.apply(this,Fa(n,arguments));return Object(l)===l?l:this}return t.apply(r,Fa(n,arguments))},s=Ic(0,t.length-n.length),o=[],u=0;u<s;u++)o[u]="$"+u;if(i=Function("binder","return function ("+Rc(o,",")+"){ return binder.apply(this,arguments); }")(a),t.prototype){var f=function(){};f.prototype=t.prototype,i.prototype=new f,f.prototype=null}return i}});var ja=p((T1,Ma)=>{"use strict";var kc=Ga();Ma.exports=Function.prototype.bind||kc});var qa=p((F1,Ua)=>{"use strict";var _c=Function.prototype.call,Tc=Object.prototype.hasOwnProperty,Fc=ja();Ua.exports=Fc.call(_c,Tc)});var $a=p((B1,Bc)=>{Bc.exports={assert:!0,"node:assert":[">= 14.18 && < 15",">= 16"],"assert/strict":">= 15","node:assert/strict":">= 16",async_hooks:">= 8","node:async_hooks":[">= 14.18 && < 15",">= 16"],buffer_ieee754:">= 0.5 && < 0.9.7",buffer:!0,"node:buffer":[">= 14.18 && < 15",">= 16"],child_process:!0,"node:child_process":[">= 14.18 && < 15",">= 16"],cluster:">= 0.5","node:cluster":[">= 14.18 && < 15",">= 16"],console:!0,"node:console":[">= 14.18 && < 15",">= 16"],constants:!0,"node:constants":[">= 14.18 && < 15",">= 16"],crypto:!0,"node:crypto":[">= 14.18 && < 15",">= 16"],_debug_agent:">= 1 && < 8",_debugger:"< 8",dgram:!0,"node:dgram":[">= 14.18 && < 15",">= 16"],diagnostics_channel:[">= 14.17 && < 15",">= 15.1"],"node:diagnostics_channel":[">= 14.18 && < 15",">= 16"],dns:!0,"node:dns":[">= 14.18 && < 15",">= 16"],"dns/promises":">= 15","node:dns/promises":">= 16",domain:">= 0.7.12","node:domain":[">= 14.18 && < 15",">= 16"],events:!0,"node:events":[">= 14.18 && < 15",">= 16"],freelist:"< 6",fs:!0,"node:fs":[">= 14.18 && < 15",">= 16"],"fs/promises":[">= 10 && < 10.1",">= 14"],"node:fs/promises":[">= 14.18 && < 15",">= 16"],_http_agent:">= 0.11.1","node:_http_agent":[">= 14.18 && < 15",">= 16"],_http_client:">= 0.11.1","node:_http_client":[">= 14.18 && < 15",">= 16"],_http_common:">= 0.11.1","node:_http_common":[">= 14.18 && < 15",">= 16"],_http_incoming:">= 0.11.1","node:_http_incoming":[">= 14.18 && < 15",">= 16"],_http_outgoing:">= 0.11.1","node:_http_outgoing":[">= 14.18 && < 15",">= 16"],_http_server:">= 0.11.1","node:_http_server":[">= 14.18 && < 15",">= 16"],http:!0,"node:http":[">= 14.18 && < 15",">= 16"],http2:">= 8.8","node:http2":[">= 14.18 && < 15",">= 16"],https:!0,"node:https":[">= 14.18 && < 15",">= 16"],inspector:">= 8","node:inspector":[">= 14.18 && < 15",">= 16"],"inspector/promises":[">= 19"],"node:inspector/promises":[">= 19"],_linklist:"< 8",module:!0,"node:module":[">= 14.18 && < 15",">= 16"],net:!0,"node:net":[">= 14.18 && < 15",">= 16"],"node-inspect/lib/_inspect":">= 7.6 && < 12","node-inspect/lib/internal/inspect_client":">= 7.6 && < 12","node-inspect/lib/internal/inspect_repl":">= 7.6 && < 12",os:!0,"node:os":[">= 14.18 && < 15",">= 16"],path:!0,"node:path":[">= 14.18 && < 15",">= 16"],"path/posix":">= 15.3","node:path/posix":">= 16","path/win32":">= 15.3","node:path/win32":">= 16",perf_hooks:">= 8.5","node:perf_hooks":[">= 14.18 && < 15",">= 16"],process:">= 1","node:process":[">= 14.18 && < 15",">= 16"],punycode:">= 0.5","node:punycode":[">= 14.18 && < 15",">= 16"],querystring:!0,"node:querystring":[">= 14.18 && < 15",">= 16"],readline:!0,"node:readline":[">= 14.18 && < 15",">= 16"],"readline/promises":">= 17","node:readline/promises":">= 17",repl:!0,"node:repl":[">= 14.18 && < 15",">= 16"],"node:sea":[">= 20.12 && < 21",">= 21.7"],smalloc:">= 0.11.5 && < 3",_stream_duplex:">= 0.9.4","node:_stream_duplex":[">= 14.18 && < 15",">= 16"],_stream_transform:">= 0.9.4","node:_stream_transform":[">= 14.18 && < 15",">= 16"],_stream_wrap:">= 1.4.1","node:_stream_wrap":[">= 14.18 && < 15",">= 16"],_stream_passthrough:">= 0.9.4","node:_stream_passthrough":[">= 14.18 && < 15",">= 16"],_stream_readable:">= 0.9.4","node:_stream_readable":[">= 14.18 && < 15",">= 16"],_stream_writable:">= 0.9.4","node:_stream_writable":[">= 14.18 && < 15",">= 16"],stream:!0,"node:stream":[">= 14.18 && < 15",">= 16"],"stream/consumers":">= 16.7","node:stream/consumers":">= 16.7","stream/promises":">= 15","node:stream/promises":">= 16","stream/web":">= 16.5","node:stream/web":">= 16.5",string_decoder:!0,"node:string_decoder":[">= 14.18 && < 15",">= 16"],sys:[">= 0.4 && < 0.7",">= 0.8"],"node:sys":[">= 14.18 && < 15",">= 16"],"test/reporters":">= 19.9 && < 20.2","node:test/reporters":[">= 18.17 && < 19",">= 19.9",">= 20"],"test/mock_loader":">= 22.3 && < 22.7","node:test/mock_loader":">= 22.3 && < 22.7","node:test":[">= 16.17 && < 17",">= 18"],timers:!0,"node:timers":[">= 14.18 && < 15",">= 16"],"timers/promises":">= 15","node:timers/promises":">= 16",_tls_common:">= 0.11.13","node:_tls_common":[">= 14.18 && < 15",">= 16"],_tls_legacy:">= 0.11.3 && < 10",_tls_wrap:">= 0.11.3","node:_tls_wrap":[">= 14.18 && < 15",">= 16"],tls:!0,"node:tls":[">= 14.18 && < 15",">= 16"],trace_events:">= 10","node:trace_events":[">= 14.18 && < 15",">= 16"],tty:!0,"node:tty":[">= 14.18 && < 15",">= 16"],url:!0,"node:url":[">= 14.18 && < 15",">= 16"],util:!0,"node:util":[">= 14.18 && < 15",">= 16"],"util/types":">= 15.3","node:util/types":">= 16","v8/tools/arguments":">= 10 && < 12","v8/tools/codemap":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/consarray":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/csvparser":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/logreader":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/profile_view":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/splaytree":[">= 4.4 && < 5",">= 5.2 && < 12"],v8:">= 1","node:v8":[">= 14.18 && < 15",">= 16"],vm:!0,"node:vm":[">= 14.18 && < 15",">= 16"],wasi:[">= 13.4 && < 13.5",">= 18.17 && < 19",">= 20"],"node:wasi":[">= 18.17 && < 19",">= 20"],worker_threads:">= 11.7","node:worker_threads":[">= 14.18 && < 15",">= 16"],zlib:">= 0.5","node:zlib":[">= 14.18 && < 15",">= 16"]}});var tr=p((G1,Ya)=>{"use strict";var Gc=qa();function Mc(e,r){for(var t=e.split("."),n=r.split(" "),i=n.length>1?n[0]:"=",a=(n.length>1?n[1]:n[0]).split("."),s=0;s<3;++s){var o=parseInt(t[s]||0,10),u=parseInt(a[s]||0,10);if(o!==u)return i==="<"?o<u:i===">="?o>=u:!1}return i===">="}function Wa(e,r){var t=r.split(/ ?&& ?/);if(t.length===0)return!1;for(var n=0;n<t.length;++n)if(!Mc(e,t[n]))return!1;return!0}function jc(e,r){if(typeof r=="boolean")return r;var t=typeof e>"u"?process.versions&&process.versions.node:e;if(typeof t!="string")throw new TypeError(typeof e>"u"?"Unable to determine current node version":"If provided, a valid node version is required");if(r&&typeof r=="object"){for(var n=0;n<r.length;++n)if(Wa(t,r[n]))return!0;return!1}return Wa(t,r)}var Ha=$a();Ya.exports=function(r,t){return Gc(Ha,r)&&jc(t,Ha[r])}});var Ja=p((M1,Va)=>{var Se=require("fs"),Uc=Gt(),G=require("path"),qc=Mt(),$c=qt(),Wc=$t(),Hc=tr(),Yc=process.platform!=="win32"&&Se.realpath&&typeof Se.realpath.native=="function"?Se.realpath.native:Se.realpath,za=Uc(),zc=function(){return[G.join(za,".node_modules"),G.join(za,".node_libraries")]},Vc=function(r,t){Se.stat(r,function(n,i){return n?n.code==="ENOENT"||n.code==="ENOTDIR"?t(null,!1):t(n):t(null,i.isFile()||i.isFIFO())})},Jc=function(r,t){Se.stat(r,function(n,i){return n?n.code==="ENOENT"||n.code==="ENOTDIR"?t(null,!1):t(n):t(null,i.isDirectory())})},Xc=function(r,t){Yc(r,function(n,i){n&&n.code!=="ENOENT"?t(n):t(null,n?r:i)})},nr=function(r,t,n,i){n&&n.preserveSymlinks===!1?r(t,i):i(null,t)},Kc=function(r,t,n){r(t,function(i,a){if(i)n(i);else try{var s=JSON.parse(a);n(null,s)}catch{n(null)}})},Zc=function(r,t,n){for(var i=$c(t,n,r),a=0;a<i.length;a++)i[a]=G.join(i[a],r);return i};Va.exports=function(r,t,n){var i=n,a=t;if(typeof t=="function"&&(i=a,a={}),typeof r!="string"){var s=new TypeError("Path must be a string.");return process.nextTick(function(){i(s)})}a=Wc(r,a);var o=a.isFile||Vc,u=a.isDirectory||Jc,f=a.readFile||Se.readFile,l=a.realpath||Xc,h=a.readPackage||Kc;if(a.readFile&&a.readPackage){var b=new TypeError("`readFile` and `readPackage` are mutually exclusive.");return process.nextTick(function(){i(b)})}var x=a.packageIterator,m=a.extensions||[".js"],g=a.includeCoreModules!==!1,L=a.basedir||G.dirname(qc()),N=a.filename||L;a.paths=a.paths||zc();var ae=G.resolve(L);nr(l,ae,a,function(v,C){v?i(v):se(C)});var M;function se(v){if(/^(?:\.\.?(?:\/|$)|\/|([A-Za-z]:)?[/\\])/.test(r))M=G.resolve(v,r),(r==="."||r===".."||r.slice(-1)==="/")&&(M+="/"),/\/$/.test(r)&&M===v?R(M,a.package,le):J(M,a.package,le);else{if(g&&Hc(r))return i(null,r);ee(r,v,function(C,T,P){if(C)i(C);else{if(T)return nr(l,T,a,function(X,F){X?i(X):i(null,F,P)});var k=new Error("Cannot find module '"+r+"' from '"+N+"'");k.code="MODULE_NOT_FOUND",i(k)}})}}function le(v,C,T){v?i(v):C?i(null,C,T):R(M,function(P,k,X){if(P)i(P);else if(k)nr(l,k,a,function(_,V){_?i(_):i(null,V,X)});else{var F=new Error("Cannot find module '"+r+"' from '"+N+"'");F.code="MODULE_NOT_FOUND",i(F)}})}function J(v,C,T){var P=C,k=T;typeof P=="function"&&(k=P,P=void 0);var X=[""].concat(m);F(X,v,P);function F(_,V,oe){if(_.length===0)return k(null,void 0,oe);var ge=V+_[0],re=oe;re?j(null,re):E(G.dirname(ge),j);function j(fe,Ee,ue){if(re=Ee,fe)return k(fe);if(ue&&re&&a.pathFilter){var ur=G.relative(ue,ge),cr=ur.slice(0,ur.length-_[0].length),qe=a.pathFilter(re,V,cr);if(qe)return F([""].concat(m.slice()),G.resolve(ue,qe),re)}o(ge,or)}function or(fe,Ee){if(fe)return k(fe);if(Ee)return k(null,ge,re);F(_.slice(1),V,re)}}}function E(v,C){if(v===""||v==="/"||process.platform==="win32"&&/^\w:[/\\]*$/.test(v)||/[/\\]node_modules[/\\]*$/.test(v))return C(null);nr(l,v,a,function(T,P){if(T)return E(G.dirname(v),C);var k=G.join(P,"package.json");o(k,function(X,F){if(!F)return E(G.dirname(v),C);h(f,k,function(_,V){_&&C(_);var oe=V;oe&&a.packageFilter&&(oe=a.packageFilter(oe,k)),C(null,oe,v)})})})}function R(v,C,T){var P=T,k=C;typeof k=="function"&&(P=k,k=a.package),nr(l,v,a,function(X,F){if(X)return P(X);var _=G.join(F,"package.json");o(_,function(V,oe){if(V)return P(V);if(!oe)return J(G.join(v,"index"),k,P);h(f,_,function(ge,re){if(ge)return P(ge);var j=re;if(j&&a.packageFilter&&(j=a.packageFilter(j,_)),j&&j.main){if(typeof j.main!="string"){var or=new TypeError("package \u201C"+j.name+"\u201D `main` must be a string");return or.code="INVALID_PACKAGE_MAIN",P(or)}(j.main==="."||j.main==="./")&&(j.main="index"),J(G.resolve(v,j.main),j,function(fe,Ee,ue){if(fe)return P(fe);if(Ee)return P(null,Ee,ue);if(!ue)return J(G.join(v,"index"),ue,P);var ur=G.resolve(v,ue.main);R(ur,ue,function(cr,qe,tn){if(cr)return P(cr);if(qe)return P(null,qe,tn);J(G.join(v,"index"),tn,P)})});return}J(G.join(v,"/index"),j,P)})})})}function D(v,C){if(C.length===0)return v(null,void 0);var T=C[0];u(G.dirname(T),P);function P(F,_){if(F)return v(F);if(!_)return D(v,C.slice(1));J(T,a.package,k)}function k(F,_,V){if(F)return v(F);if(_)return v(null,_,V);R(T,a.package,X)}function X(F,_,V){if(F)return v(F);if(_)return v(null,_,V);D(v,C.slice(1))}}function ee(v,C,T){var P=function(){return Zc(v,C,a)};D(T,x?x(v,C,P,a):P())}}});var Xa=p((j1,Qc)=>{Qc.exports={assert:!0,"node:assert":[">= 14.18 && < 15",">= 16"],"assert/strict":">= 15","node:assert/strict":">= 16",async_hooks:">= 8","node:async_hooks":[">= 14.18 && < 15",">= 16"],buffer_ieee754:">= 0.5 && < 0.9.7",buffer:!0,"node:buffer":[">= 14.18 && < 15",">= 16"],child_process:!0,"node:child_process":[">= 14.18 && < 15",">= 16"],cluster:">= 0.5","node:cluster":[">= 14.18 && < 15",">= 16"],console:!0,"node:console":[">= 14.18 && < 15",">= 16"],constants:!0,"node:constants":[">= 14.18 && < 15",">= 16"],crypto:!0,"node:crypto":[">= 14.18 && < 15",">= 16"],_debug_agent:">= 1 && < 8",_debugger:"< 8",dgram:!0,"node:dgram":[">= 14.18 && < 15",">= 16"],diagnostics_channel:[">= 14.17 && < 15",">= 15.1"],"node:diagnostics_channel":[">= 14.18 && < 15",">= 16"],dns:!0,"node:dns":[">= 14.18 && < 15",">= 16"],"dns/promises":">= 15","node:dns/promises":">= 16",domain:">= 0.7.12","node:domain":[">= 14.18 && < 15",">= 16"],events:!0,"node:events":[">= 14.18 && < 15",">= 16"],freelist:"< 6",fs:!0,"node:fs":[">= 14.18 && < 15",">= 16"],"fs/promises":[">= 10 && < 10.1",">= 14"],"node:fs/promises":[">= 14.18 && < 15",">= 16"],_http_agent:">= 0.11.1","node:_http_agent":[">= 14.18 && < 15",">= 16"],_http_client:">= 0.11.1","node:_http_client":[">= 14.18 && < 15",">= 16"],_http_common:">= 0.11.1","node:_http_common":[">= 14.18 && < 15",">= 16"],_http_incoming:">= 0.11.1","node:_http_incoming":[">= 14.18 && < 15",">= 16"],_http_outgoing:">= 0.11.1","node:_http_outgoing":[">= 14.18 && < 15",">= 16"],_http_server:">= 0.11.1","node:_http_server":[">= 14.18 && < 15",">= 16"],http:!0,"node:http":[">= 14.18 && < 15",">= 16"],http2:">= 8.8","node:http2":[">= 14.18 && < 15",">= 16"],https:!0,"node:https":[">= 14.18 && < 15",">= 16"],inspector:">= 8","node:inspector":[">= 14.18 && < 15",">= 16"],"inspector/promises":[">= 19"],"node:inspector/promises":[">= 19"],_linklist:"< 8",module:!0,"node:module":[">= 14.18 && < 15",">= 16"],net:!0,"node:net":[">= 14.18 && < 15",">= 16"],"node-inspect/lib/_inspect":">= 7.6 && < 12","node-inspect/lib/internal/inspect_client":">= 7.6 && < 12","node-inspect/lib/internal/inspect_repl":">= 7.6 && < 12",os:!0,"node:os":[">= 14.18 && < 15",">= 16"],path:!0,"node:path":[">= 14.18 && < 15",">= 16"],"path/posix":">= 15.3","node:path/posix":">= 16","path/win32":">= 15.3","node:path/win32":">= 16",perf_hooks:">= 8.5","node:perf_hooks":[">= 14.18 && < 15",">= 16"],process:">= 1","node:process":[">= 14.18 && < 15",">= 16"],punycode:">= 0.5","node:punycode":[">= 14.18 && < 15",">= 16"],querystring:!0,"node:querystring":[">= 14.18 && < 15",">= 16"],readline:!0,"node:readline":[">= 14.18 && < 15",">= 16"],"readline/promises":">= 17","node:readline/promises":">= 17",repl:!0,"node:repl":[">= 14.18 && < 15",">= 16"],smalloc:">= 0.11.5 && < 3",_stream_duplex:">= 0.9.4","node:_stream_duplex":[">= 14.18 && < 15",">= 16"],_stream_transform:">= 0.9.4","node:_stream_transform":[">= 14.18 && < 15",">= 16"],_stream_wrap:">= 1.4.1","node:_stream_wrap":[">= 14.18 && < 15",">= 16"],_stream_passthrough:">= 0.9.4","node:_stream_passthrough":[">= 14.18 && < 15",">= 16"],_stream_readable:">= 0.9.4","node:_stream_readable":[">= 14.18 && < 15",">= 16"],_stream_writable:">= 0.9.4","node:_stream_writable":[">= 14.18 && < 15",">= 16"],stream:!0,"node:stream":[">= 14.18 && < 15",">= 16"],"stream/consumers":">= 16.7","node:stream/consumers":">= 16.7","stream/promises":">= 15","node:stream/promises":">= 16","stream/web":">= 16.5","node:stream/web":">= 16.5",string_decoder:!0,"node:string_decoder":[">= 14.18 && < 15",">= 16"],sys:[">= 0.4 && < 0.7",">= 0.8"],"node:sys":[">= 14.18 && < 15",">= 16"],"test/reporters":">= 19.9 && < 20.2","node:test/reporters":[">= 18.17 && < 19",">= 19.9",">= 20"],"node:test":[">= 16.17 && < 17",">= 18"],timers:!0,"node:timers":[">= 14.18 && < 15",">= 16"],"timers/promises":">= 15","node:timers/promises":">= 16",_tls_common:">= 0.11.13","node:_tls_common":[">= 14.18 && < 15",">= 16"],_tls_legacy:">= 0.11.3 && < 10",_tls_wrap:">= 0.11.3","node:_tls_wrap":[">= 14.18 && < 15",">= 16"],tls:!0,"node:tls":[">= 14.18 && < 15",">= 16"],trace_events:">= 10","node:trace_events":[">= 14.18 && < 15",">= 16"],tty:!0,"node:tty":[">= 14.18 && < 15",">= 16"],url:!0,"node:url":[">= 14.18 && < 15",">= 16"],util:!0,"node:util":[">= 14.18 && < 15",">= 16"],"util/types":">= 15.3","node:util/types":">= 16","v8/tools/arguments":">= 10 && < 12","v8/tools/codemap":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/consarray":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/csvparser":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/logreader":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/profile_view":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/splaytree":[">= 4.4 && < 5",">= 5.2 && < 12"],v8:">= 1","node:v8":[">= 14.18 && < 15",">= 16"],vm:!0,"node:vm":[">= 14.18 && < 15",">= 16"],wasi:[">= 13.4 && < 13.5",">= 18.17 && < 19",">= 20"],"node:wasi":[">= 18.17 && < 19",">= 20"],worker_threads:">= 11.7","node:worker_threads":[">= 14.18 && < 15",">= 16"],zlib:">= 0.5","node:zlib":[">= 14.18 && < 15",">= 16"]}});var es=p((U1,Qa)=>{"use strict";var el=tr(),Ka=Xa(),Za={};for(Gr in Ka)Object.prototype.hasOwnProperty.call(Ka,Gr)&&(Za[Gr]=el(Gr));var Gr;Qa.exports=Za});var ts=p((q1,rs)=>{var rl=tr();rs.exports=function(r){return rl(r)}});var as=p(($1,is)=>{var tl=tr(),Pe=require("fs"),$=require("path"),nl=Gt(),il=Mt(),al=qt(),sl=$t(),ol=process.platform!=="win32"&&Pe.realpathSync&&typeof Pe.realpathSync.native=="function"?Pe.realpathSync.native:Pe.realpathSync,ns=nl(),ul=function(){return[$.join(ns,".node_modules"),$.join(ns,".node_libraries")]},cl=function(r){try{var t=Pe.statSync(r,{throwIfNoEntry:!1})}catch(n){if(n&&(n.code==="ENOENT"||n.code==="ENOTDIR"))return!1;throw n}return!!t&&(t.isFile()||t.isFIFO())},ll=function(r){try{var t=Pe.statSync(r,{throwIfNoEntry:!1})}catch(n){if(n&&(n.code==="ENOENT"||n.code==="ENOTDIR"))return!1;throw n}return!!t&&t.isDirectory()},fl=function(r){try{return ol(r)}catch(t){if(t.code!=="ENOENT")throw t}return r},ir=function(r,t,n){return n&&n.preserveSymlinks===!1?r(t):t},pl=function(r,t){var n=r(t);try{var i=JSON.parse(n);return i}catch{}},dl=function(r,t,n){for(var i=al(t,n,r),a=0;a<i.length;a++)i[a]=$.join(i[a],r);return i};is.exports=function(r,t){if(typeof r!="string")throw new TypeError("Path must be a string.");var n=sl(r,t),i=n.isFile||cl,a=n.readFileSync||Pe.readFileSync,s=n.isDirectory||ll,o=n.realpathSync||fl,u=n.readPackageSync||pl;if(n.readFileSync&&n.readPackageSync)throw new TypeError("`readFileSync` and `readPackageSync` are mutually exclusive.");var f=n.packageIterator,l=n.extensions||[".js"],h=n.includeCoreModules!==!1,b=n.basedir||$.dirname(il()),x=n.filename||b;n.paths=n.paths||ul();var m=ir(o,$.resolve(b),n);if(/^(?:\.\.?(?:\/|$)|\/|([A-Za-z]:)?[/\\])/.test(r)){var g=$.resolve(m,r);(r==="."||r===".."||r.slice(-1)==="/")&&(g+="/");var L=M(g)||le(g);if(L)return ir(o,L,n)}else{if(h&&tl(r))return r;var N=J(r,m);if(N)return ir(o,N,n)}var ae=new Error("Cannot find module '"+r+"' from '"+x+"'");throw ae.code="MODULE_NOT_FOUND",ae;function M(E){var R=se($.dirname(E));if(R&&R.dir&&R.pkg&&n.pathFilter){var D=$.relative(R.dir,E),ee=n.pathFilter(R.pkg,E,D);ee&&(E=$.resolve(R.dir,ee))}if(i(E))return E;for(var v=0;v<l.length;v++){var C=E+l[v];if(i(C))return C}}function se(E){if(!(E===""||E==="/")&&!(process.platform==="win32"&&/^\w:[/\\]*$/.test(E))&&!/[/\\]node_modules[/\\]*$/.test(E)){var R=$.join(ir(o,E,n),"package.json");if(!i(R))return se($.dirname(E));var D=u(a,R);return D&&n.packageFilter&&(D=n.packageFilter(D,E)),{pkg:D,dir:E}}}function le(E){var R=$.join(ir(o,E,n),"/package.json");if(i(R)){try{var D=u(a,R)}catch{}if(D&&n.packageFilter&&(D=n.packageFilter(D,E)),D&&D.main){if(typeof D.main!="string"){var ee=new TypeError("package \u201C"+D.name+"\u201D `main` must be a string");throw ee.code="INVALID_PACKAGE_MAIN",ee}(D.main==="."||D.main==="./")&&(D.main="index");try{var v=M($.resolve(E,D.main));if(v)return v;var C=le($.resolve(E,D.main));if(C)return C}catch{}}}return M($.join(E,"/index"))}function J(E,R){for(var D=function(){return dl(E,R,n)},ee=f?f(E,R,D,n):D(),v=0;v<ee.length;v++){var C=ee[v];if(s($.dirname(C))){var T=M(C);if(T)return T;var P=le(C);if(P)return P}}}}});var os=p((W1,ss)=>{var Mr=Ja();Mr.core=es();Mr.isCore=ts();Mr.sync=as();ss.exports=Mr});var cs=p((H1,us)=>{us.exports=hl;function hl(e){if(e&&e!=="ERROR: No README data found!"){e=e.trim().split(`
`);for(var r=0;e[r]&&e[r].trim().match(/^(#|$)/);r++);for(var t=e.length,n=r+1;n<t&&e[n].trim();n++);return e.slice(r,n).join(" ").trim()}}});var ls=p((Y1,ml)=>{ml.exports={topLevel:{dependancies:"dependencies",dependecies:"dependencies",depdenencies:"dependencies",devEependencies:"devDependencies",depends:"dependencies","dev-dependencies":"devDependencies",devDependences:"devDependencies",devDepenencies:"devDependencies",devdependencies:"devDependencies",repostitory:"repository",repo:"repository",prefereGlobal:"preferGlobal",hompage:"homepage",hampage:"homepage",autohr:"author",autor:"author",contributers:"contributors",publicationConfig:"publishConfig",script:"scripts"},bugs:{web:"url",name:"url"},script:{server:"start",tests:"test"}}});var hs=p((V1,ds)=>{var fs=Qi(),vl=ya(),jr=Oa(),gl=os().isCore,bl=["dependencies","devDependencies","optionalDependencies"],yl=cs(),Wt=require("url"),de=ls(),z1=ds.exports={warn:function(){},fixRepositoryField:function(e){if(e.repositories&&(this.warn("repositories"),e.repository=e.repositories[0]),!e.repository)return this.warn("missingRepository");typeof e.repository=="string"&&(e.repository={type:"git",url:e.repository});var r=e.repository.url||"";if(r){var t=jr.fromUrl(r);t&&(r=e.repository.url=t.getDefaultRepresentation()=="shortcut"?t.https():t.toString())}r.match(/github.com\/[^\/]+\/[^\/]+\.git\.git$/)&&this.warn("brokenGitUrl",r)},fixTypos:function(e){Object.keys(de.topLevel).forEach(function(r){e.hasOwnProperty(r)&&this.warn("typo",r,de.topLevel[r])},this)},fixScriptsField:function(e){if(e.scripts){if(typeof e.scripts!="object"){this.warn("nonObjectScripts"),delete e.scripts;return}Object.keys(e.scripts).forEach(function(r){typeof e.scripts[r]!="string"?(this.warn("nonStringScript"),delete e.scripts[r]):de.script[r]&&!e.scripts[de.script[r]]&&this.warn("typo",r,de.script[r],"scripts")},this)}},fixFilesField:function(e){var r=e.files;r&&!Array.isArray(r)?(this.warn("nonArrayFiles"),delete e.files):e.files&&(e.files=e.files.filter(function(t){return!t||typeof t!="string"?(this.warn("invalidFilename",t),!1):!0},this))},fixBinField:function(e){if(e.bin&&typeof e.bin=="string"){var r={},t;(t=e.name.match(/^@[^/]+[/](.*)$/))?r[t[1]]=e.bin:r[e.name]=e.bin,e.bin=r}},fixManField:function(e){e.man&&typeof e.man=="string"&&(e.man=[e.man])},fixBundleDependenciesField:function(e){var r="bundledDependencies",t="bundleDependencies";e[r]&&!e[t]&&(e[t]=e[r],delete e[r]),e[t]&&!Array.isArray(e[t])?(this.warn("nonArrayBundleDependencies"),delete e[t]):e[t]&&(e[t]=e[t].filter(function(n){return!n||typeof n!="string"?(this.warn("nonStringBundleDependency",n),!1):(e.dependencies||(e.dependencies={}),e.dependencies.hasOwnProperty(n)||(this.warn("nonDependencyBundleDependency",n),e.dependencies[n]="*"),!0)},this))},fixDependencies:function(e,r){var t=!r;Ol(e,this.warn),xl(e,this.warn),this.fixBundleDependenciesField(e),["dependencies","devDependencies"].forEach(function(n){if(n in e){if(!e[n]||typeof e[n]!="object"){this.warn("nonObjectDependencies",n),delete e[n];return}Object.keys(e[n]).forEach(function(i){var a=e[n][i];typeof a!="string"&&(this.warn("nonStringDependency",i,JSON.stringify(a)),delete e[n][i]);var s=jr.fromUrl(e[n][i]);s&&(e[n][i]=s.toString())},this)}},this)},fixModulesField:function(e){e.modules&&(this.warn("deprecatedModules"),delete e.modules)},fixKeywordsField:function(e){typeof e.keywords=="string"&&(e.keywords=e.keywords.split(/,\s+/)),e.keywords&&!Array.isArray(e.keywords)?(delete e.keywords,this.warn("nonArrayKeywords")):e.keywords&&(e.keywords=e.keywords.filter(function(r){return typeof r!="string"||!r?(this.warn("nonStringKeyword"),!1):!0},this))},fixVersionField:function(e,r){var t=!r;if(!e.version)return e.version="",!0;if(!fs.valid(e.version,t))throw new Error('Invalid version: "'+e.version+'"');return e.version=fs.clean(e.version,t),!0},fixPeople:function(e){ps(e,Sl),ps(e,Pl)},fixNameField:function(e,r){typeof r=="boolean"?r={strict:r}:typeof r>"u"&&(r={});var t=r.strict;if(!e.name&&!t){e.name="";return}if(typeof e.name!="string")throw new Error("name field must be a string.");t||(e.name=e.name.trim()),wl(e.name,t,r.allowLegacyCase),gl(e.name)&&this.warn("conflictingName",e.name)},fixDescriptionField:function(e){e.description&&typeof e.description!="string"&&(this.warn("nonStringDescription"),delete e.description),e.readme&&!e.description&&(e.description=yl(e.readme)),e.description===void 0&&delete e.description,e.description||this.warn("missingDescription")},fixReadmeField:function(e){e.readme||(this.warn("missingReadme"),e.readme="ERROR: No README data found!")},fixBugsField:function(e){if(!e.bugs&&e.repository&&e.repository.url){var r=jr.fromUrl(e.repository.url);r&&r.bugs()&&(e.bugs={url:r.bugs()})}else if(e.bugs){var t=/^.+@.*\..+$/;if(typeof e.bugs=="string")t.test(e.bugs)?e.bugs={email:e.bugs}:Wt.parse(e.bugs).protocol?e.bugs={url:e.bugs}:this.warn("nonEmailUrlBugsString");else{Dl(e.bugs,this.warn);var n=e.bugs;e.bugs={},n.url&&(typeof n.url=="string"&&Wt.parse(n.url).protocol?e.bugs.url=n.url:this.warn("nonUrlBugsUrlField")),n.email&&(typeof n.email=="string"&&t.test(n.email)?e.bugs.email=n.email:this.warn("nonEmailBugsEmailField"))}!e.bugs.email&&!e.bugs.url&&(delete e.bugs,this.warn("emptyNormalizedBugs"))}},fixHomepageField:function(e){if(!e.homepage&&e.repository&&e.repository.url){var r=jr.fromUrl(e.repository.url);r&&r.docs()&&(e.homepage=r.docs())}if(e.homepage){if(typeof e.homepage!="string")return this.warn("nonUrlHomepage"),delete e.homepage;Wt.parse(e.homepage).protocol||(e.homepage="http://"+e.homepage)}},fixLicenseField:function(e){if(e.license)typeof e.license!="string"||e.license.length<1||e.license.trim()===""?this.warn("invalidLicense"):vl(e.license).validForNewPackages||this.warn("invalidLicense");else return this.warn("missingLicense")}};function Ll(e){if(e.charAt(0)!=="@")return!1;var r=e.slice(1).split("/");return r.length!==2?!1:r[0]&&r[1]&&r[0]===encodeURIComponent(r[0])&&r[1]===encodeURIComponent(r[1])}function Cl(e){return!e.match(/[\/@\s\+%:]/)&&e===encodeURIComponent(e)}function wl(e,r,t){if(e.charAt(0)==="."||!(Ll(e)||Cl(e))||r&&!t&&e!==e.toLowerCase()||e.toLowerCase()==="node_modules"||e.toLowerCase()==="favicon.ico")throw new Error("Invalid name: "+JSON.stringify(e))}function ps(e,r){return e.author&&(e.author=r(e.author)),["maintainers","contributors"].forEach(function(t){Array.isArray(e[t])&&(e[t]=e[t].map(r))}),e}function Sl(e){if(typeof e=="string")return e;var r=e.name||"",t=e.url||e.web,n=t?" ("+t+")":"",i=e.email||e.mail,a=i?" <"+i+">":"";return r+a+n}function Pl(e){if(typeof e!="string")return e;var r=e.match(/^([^\(<]+)/),t=e.match(/\(([^\)]+)\)/),n=e.match(/<([^>]+)>/),i={};return r&&r[0].trim()&&(i.name=r[0].trim()),n&&(i.email=n[1]),t&&(i.url=t[1]),i}function xl(e,r){var t=e.optionalDependencies;if(t){var n=e.dependencies||{};Object.keys(t).forEach(function(i){n[i]=t[i]}),e.dependencies=n}}function El(e,r,t){if(!e)return{};if(typeof e=="string"&&(e=e.trim().split(/[\n\r\s\t ,]+/)),!Array.isArray(e))return e;t("deprecatedArrayDependencies",r);var n={};return e.filter(function(i){return typeof i=="string"}).forEach(function(i){i=i.trim().split(/(:?[@\s><=])/);var a=i.shift(),s=i.join("");s=s.trim(),s=s.replace(/^@/,""),n[a]=s}),n}function Ol(e,r){bl.forEach(function(t){e[t]&&(e[t]=El(e[t],t,r))})}function Dl(e,r){e&&Object.keys(e).forEach(function(t){de.bugs[t]&&(r("typo",t,de.bugs[t],"bugs"),e[de.bugs[t]]=e[t],delete e[t])})}});var ms=p((J1,Il)=>{Il.exports={repositories:"'repositories' (plural) Not supported. Please pick one as the 'repository' field",missingRepository:"No repository field.",brokenGitUrl:"Probably broken git url: %s",nonObjectScripts:"scripts must be an object",nonStringScript:"script values must be string commands",nonArrayFiles:"Invalid 'files' member",invalidFilename:"Invalid filename in 'files' list: %s",nonArrayBundleDependencies:"Invalid 'bundleDependencies' list. Must be array of package names",nonStringBundleDependency:"Invalid bundleDependencies member: %s",nonDependencyBundleDependency:"Non-dependency in bundleDependencies: %s",nonObjectDependencies:"%s field must be an object",nonStringDependency:"Invalid dependency: %s %s",deprecatedArrayDependencies:"specifying %s as array is deprecated",deprecatedModules:"modules field is deprecated",nonArrayKeywords:"keywords should be an array of strings",nonStringKeyword:"keywords should be an array of strings",conflictingName:"%s is also the name of a node core module.",nonStringDescription:"'description' field should be a string",missingDescription:"No description",missingReadme:"No README data",missingLicense:"No license field.",nonEmailUrlBugsString:"Bug string field must be url, email, or {email,url}",nonUrlBugsUrlField:"bugs.url field must be a string url. Deleted.",nonEmailBugsEmailField:"bugs.email field must be a string email. Deleted.",emptyNormalizedBugs:"Normalized value of bugs field is an empty object. Deleted.",nonUrlHomepage:"homepage field must be a string url. Deleted.",invalidLicense:"license should be a valid SPDX license expression",typo:"%s should probably be %s."}});var bs=p((X1,gs)=>{var vs=require("util"),Ht=ms();gs.exports=function(){var e=Array.prototype.slice.call(arguments,0),r=e.shift();if(r=="typo")return Nl.apply(null,e);var t=Ht[r]?Ht[r]:r+": '%s'";return e.unshift(t),vs.format.apply(null,e)};function Nl(e,r,t){return t&&(e=t+"['"+e+"']",r=t+"['"+r+"']"),vs.format(Ht.typo,e,r)}});var Vt=p((K1,Cs)=>{Cs.exports=ys;var Yt=hs();ys.fixer=Yt;var Al=bs(),Rl=["name","version","description","repository","modules","scripts","files","bin","man","bugs","keywords","readme","homepage","license"],kl=["dependencies","people","typos"],zt=Rl.map(function(e){return Ls(e)+"Field"});zt=zt.concat(kl);function ys(e,r,t){r===!0&&(r=null,t=!0),t||(t=!1),(!r||e.private)&&(r=function(n){}),e.scripts&&e.scripts.install==="node-gyp rebuild"&&!e.scripts.preinstall&&(e.gypfile=!0),Yt.warn=function(){r(Al.apply(null,arguments))},zt.forEach(function(n){Yt["fix"+Ls(n)](e,t)}),e._id=e.name+"@"+e.version}function Ls(e){return e.charAt(0).toUpperCase()+e.slice(1)}});var xs=p((Z1,Jt)=>{"use strict";var{promisify:_l}=require("util"),ws=require("fs"),Ss=require("path"),Ps=Fi(),Tl=_l(ws.readFile);Jt.exports=async e=>{e={cwd:process.cwd(),normalize:!0,...e};let r=Ss.resolve(e.cwd,"package.json"),t=Ps(await Tl(r,"utf8"));return e.normalize&&Vt()(t),t};Jt.exports.sync=e=>{e={cwd:process.cwd(),normalize:!0,...e};let r=Ss.resolve(e.cwd,"package.json"),t=Ps(ws.readFileSync(r,"utf8"));return e.normalize&&Vt()(t),t}});var Is=p((Q1,Xt)=>{"use strict";var Es=require("path"),Os=Cn(),Ds=xs();Xt.exports=async e=>{let r=await Os("package.json",e);if(r)return{packageJson:await Ds({...e,cwd:Es.dirname(r)}),path:r}};Xt.exports.sync=e=>{let r=Os.sync("package.json",e);if(r)return{packageJson:Ds.sync({...e,cwd:Es.dirname(r)}),path:r}}});var Ns=p(je=>{"use strict";Object.defineProperty(je,"__esModule",{value:!0});je.crossLaunchCommand=je.callbackLaunchCommand=void 0;var xe=require("@raycast/api"),Fl=Is(),Bl=async(e,r)=>(0,xe.launchCommand)({...e,context:{...e.context,...r}});je.callbackLaunchCommand=Bl;var Gl=async(e,r)=>{if(r===!1)return(0,xe.launchCommand)(e);let t=(0,Fl.sync)({cwd:__dirname,normalize:!1}),n=xe.environment.ownerOrAuthorName??t?.packageJson?.owner??t?.packageJson?.author;if("ownerOrAuthorName"in e){let i=`${e.ownerOrAuthorName}/${e.extensionName}`;if(!t?.packageJson?.crossExtensions?.includes(i)){let a=`Target extension '${i}' should be listed in 'crossExtensions' of package.json.`;console.error(a);return}}return(0,xe.launchCommand)({...e,context:{...e.context,callbackLaunchOptions:{name:xe.environment.commandName,extensionName:xe.environment.extensionName,ownerOrAuthorName:n,type:xe.LaunchType.UserInitiated,...r}}})};je.crossLaunchCommand=Gl});var Ul={};Xs(Ul,{default:()=>Ws});module.exports=Zs(Ul);var z=require("@raycast/api"),$s=require("react");var qr="Focus",$r="Short Break",Wr="Long Break",Hr="--:--";var Q=require("@raycast/api");var he=require("@raycast/api"),Kt=Ks(Ns()),{enableFocusWhileFocused:Ur}=(0,he.getPreferenceValues)(),As={type:he.LaunchType.Background,extensionName:"do-not-disturb",ownerOrAuthorName:"yakitrak"};async function me(e,r){if(Ur)return(0,Kt.crossLaunchCommand)({...As,name:e?"on":"off",context:{supressHUD:!e}},r||!1).catch(()=>{})}async function Rs(e){Ur&&await(0,Kt.crossLaunchCommand)({...As,name:"status",context:{suppressHUD:!0}},e||!1).catch(async()=>{await(0,he.confirmAlert)({title:"Need to Install Additional Extension",message:'The "Enable Do Not Disturb mode while focused" feature requires the "Do Not Distrub" extension, do you want to move to the install page now?'})&&await(0,he.open)("raycast://extensions/yakitrak/do-not-disturb")})}var ve=new Q.Cache,ar="pomodoro-interval/1.1",Zt="pomodoro-interval/completed-pomodoro-count",ks="pomodoro-interval/history",sr=()=>Math.round(new Date().valueOf()/1e3);async function Ml(){let e=await Q.LocalStorage.getItem(ks);return typeof e!="string"||e===null?[]:JSON.parse(e)}async function _s(e){let r=await Ml(),t=r.findIndex(n=>n.id===e.id);t!==-1?r[t]=e:r.push(e),await Q.LocalStorage.setItem(ks,JSON.stringify(r))}function Qt({parts:e}){return e.reduce((r,t)=>(typeof t.pausedAt<"u"?t.pausedAt-t.startedAt:sr()-t.startedAt)+r,0)}function en(e){return Qt(e)/e.length*100}function Ts({parts:e}){return!!e[e.length-1].pausedAt}function rn(e,r,t){let n=0;r||(n=parseInt(ve.get(Zt)??"0",10),n++),ve.set(Zt,n.toString());let i={type:e,id:n,length:t||jl[e],parts:[{startedAt:sr()}]};return ve.set(ar,JSON.stringify(i)),_s(i).then(),e==="focus"&&me(!0),i}function Fs(){let e=Ue();if(e?.type==="focus"&&me(!1),e){let r=[...e.parts];r[r.length-1].pausedAt=sr(),e={...e,parts:r},ve.set(ar,JSON.stringify(e))}return e}function Bs(){let e=Ue();if(e){let r=[...e.parts,{startedAt:sr()}];e={...e,parts:r},ve.set(ar,JSON.stringify(e)),e.type==="focus"&&me(!0)}return e}function Gs(){ve.remove(ar)}function Ms(){let e=Ue();if(e){let{type:r}=e;r==="focus"&&me(!0),rn(r,!1)}}function Ue(){let e=ve.get(ar);if(e)return JSON.parse(e)}function js(e){try{e.parts[e.parts.length-1].endAt=sr(),_s(e).then(),e.type==="focus"&&Ur?me(!1,{name:"pomodoro-control-timer",context:{currentInterval:e}}):(0,Q.launchCommand)({name:"pomodoro-control-timer",type:Q.LaunchType.UserInitiated,context:{currentInterval:e}})}catch(r){console.error(r)}}var ie=(0,Q.getPreferenceValues)(),jl={focus:parseFloat(ie.focusIntervalDuration)*60,"short-break":parseFloat(ie.shortBreakIntervalDuration)*60,"long-break":parseFloat(ie.longBreakIntervalDuration)*60};function Us(e){if(e<=0)return"00:00";let r=new Date(e*1e3),t=r.getUTCMinutes(),n=r.getUTCSeconds();return`${t.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`}var W=require("react/jsx-runtime"),qs={light:"#000000",dark:"#FFFFFF",adjustContrast:!1};function Ws(){let[e,r]=(0,$s.useState)(Ue());e&&en(e)>=100&&js(e);async function t(l){await Rs(),r(rn(l))}function n(){r(Fs())}function i(){r(Bs())}function a(){Gs(),r(void 0),me(!1)}function s(){Ms(),r(Ue())}let o;o={source:"tomato-0.png",tintColor:qs},e&&(o={source:`tomato-${100-Math.floor(en(e)/10)*10}.png`,tintColor:qs});let u=ie.hideTimeWhenStopped?void 0:Hr,f=e?Us(e.length-Qt(e)):u;return(0,W.jsxs)(z.MenuBarExtra,{icon:o,title:ie.enableTimeOnMenuBar?f:void 0,tooltip:"Pomodoro",children:[ie.enableTimeOnMenuBar?null:(0,W.jsx)(z.MenuBarExtra.Item,{icon:"\u23F0",title:Hr}),e?(0,W.jsxs)(W.Fragment,{children:[Ts(e)?(0,W.jsx)(z.MenuBarExtra.Item,{title:"Continue",icon:z.Icon.Play,onAction:i,shortcut:{modifiers:["cmd"],key:"c"}}):(0,W.jsx)(z.MenuBarExtra.Item,{title:"Pause",icon:z.Icon.Pause,onAction:n,shortcut:{modifiers:["cmd"],key:"p"}}),(0,W.jsx)(z.MenuBarExtra.Item,{title:"Reset",icon:z.Icon.Stop,onAction:a,shortcut:{modifiers:["cmd"],key:"r"}}),(0,W.jsx)(z.MenuBarExtra.Item,{title:"Restart Current",icon:z.Icon.Repeat,onAction:s,shortcut:{modifiers:["cmd"],key:"t"}})]}):(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(z.MenuBarExtra.Item,{title:qr,subtitle:`${ie.focusIntervalDuration}:00`,icon:"\u{1F3AF}",onAction:async()=>await t("focus"),shortcut:{modifiers:["cmd"],key:"f"}}),(0,W.jsx)(z.MenuBarExtra.Item,{title:$r,subtitle:`${ie.shortBreakIntervalDuration}:00`,icon:"\u{1F9D8}\u200D\u2642\uFE0F",onAction:async()=>await t("short-break"),shortcut:{modifiers:["cmd"],key:"s"}}),(0,W.jsx)(z.MenuBarExtra.Item,{title:Wr,subtitle:`${ie.longBreakIntervalDuration}:00`,icon:"\u{1F6B6}",onAction:async()=>await t("long-break"),shortcut:{modifiers:["cmd"],key:"l"}})]})]})}
