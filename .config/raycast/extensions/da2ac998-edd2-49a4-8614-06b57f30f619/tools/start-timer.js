"use strict";var xs=Object.create;var tr=Object.defineProperty;var Es=Object.getOwnPropertyDescriptor;var Os=Object.getOwnPropertyNames;var Ds=Object.getPrototypeOf,Ns=Object.prototype.hasOwnProperty;var p=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports),Is=(e,r)=>{for(var t in r)tr(e,t,{get:r[t],enumerable:!0})},Yt=(e,r,t,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let i of Os(r))!Ns.call(e,i)&&i!==t&&tr(e,i,{get:()=>r[i],enumerable:!(n=Es(r,i))||n.enumerable});return e};var As=(e,r,t)=>(t=e!=null?xs(Ds(e)):{},Yt(r||!e||!e.__esModule?tr(t,"default",{value:e,enumerable:!0}):t,e)),Rs=e=>Yt(tr({},"__esModule",{value:!0}),e);var Vt=p((Dl,Tr)=>{"use strict";var zt=(e,...r)=>new Promise(t=>{t(e(...r))});Tr.exports=zt;Tr.exports.default=zt});var Xt=p((Nl,Fr)=>{"use strict";var ks=Vt(),Jt=e=>{if(!((Number.isInteger(e)||e===1/0)&&e>0))return Promise.reject(new TypeError("Expected `concurrency` to be a number from 1 and up"));let r=[],t=0,n=()=>{t--,r.length>0&&r.shift()()},i=(o,u,...f)=>{t++;let l=ks(o,...f);u(l),l.then(n,n)},a=(o,u,...f)=>{t<e?i(o,u,...f):r.push(i.bind(null,o,u,...f))},s=(o,...u)=>new Promise(f=>a(o,f,...u));return Object.defineProperties(s,{activeCount:{get:()=>t},pendingCount:{get:()=>r.length},clearQueue:{value:()=>{r.length=0}}}),s};Fr.exports=Jt;Fr.exports.default=Jt});var Qt=p((Il,Br)=>{"use strict";var Kt=Xt(),nr=class extends Error{constructor(r){super(),this.value=r}},_s=async(e,r)=>r(await e),Ts=async e=>{let r=await Promise.all(e);if(r[1]===!0)throw new nr(r[0]);return!1},Zt=async(e,r,t)=>{t={concurrency:1/0,preserveOrder:!0,...t};let n=Kt(t.concurrency),i=[...e].map(s=>[s,n(_s,s,r)]),a=Kt(t.preserveOrder?1:1/0);try{await Promise.all(i.map(s=>a(Ts,s)))}catch(s){if(s instanceof nr)return s.value;throw s}};Br.exports=Zt;Br.exports.default=Zt});var sn=p((Al,Gr)=>{"use strict";var en=require("path"),ir=require("fs"),{promisify:rn}=require("util"),Fs=Qt(),Bs=rn(ir.stat),Gs=rn(ir.lstat),tn={directory:"isDirectory",file:"isFile"};function nn({type:e}){if(!(e in tn))throw new Error(`Invalid type specified: ${e}`)}var an=(e,r)=>e===void 0||r[tn[e]]();Gr.exports=async(e,r)=>{r={cwd:process.cwd(),type:"file",allowSymlinks:!0,...r},nn(r);let t=r.allowSymlinks?Bs:Gs;return Fs(e,async n=>{try{let i=await t(en.resolve(r.cwd,n));return an(r.type,i)}catch{return!1}},r)};Gr.exports.sync=(e,r)=>{r={cwd:process.cwd(),allowSymlinks:!0,type:"file",...r},nn(r);let t=r.allowSymlinks?ir.statSync:ir.lstatSync;for(let n of e)try{let i=t(en.resolve(r.cwd,n));if(an(r.type,i))return n}catch{}}});var un=p((Rl,Mr)=>{"use strict";var on=require("fs"),{promisify:Ms}=require("util"),js=Ms(on.access);Mr.exports=async e=>{try{return await js(e),!0}catch{return!1}};Mr.exports.sync=e=>{try{return on.accessSync(e),!0}catch{return!1}}});var ln=p((kl,we)=>{"use strict";var ce=require("path"),ar=sn(),cn=un(),jr=Symbol("findUp.stop");we.exports=async(e,r={})=>{let t=ce.resolve(r.cwd||""),{root:n}=ce.parse(t),i=[].concat(e),a=async s=>{if(typeof e!="function")return ar(i,s);let o=await e(s.cwd);return typeof o=="string"?ar([o],s):o};for(;;){let s=await a({...r,cwd:t});if(s===jr)return;if(s)return ce.resolve(t,s);if(t===n)return;t=ce.dirname(t)}};we.exports.sync=(e,r={})=>{let t=ce.resolve(r.cwd||""),{root:n}=ce.parse(t),i=[].concat(e),a=s=>{if(typeof e!="function")return ar.sync(i,s);let o=e(s.cwd);return typeof o=="string"?ar.sync([o],s):o};for(;;){let s=a({...r,cwd:t});if(s===jr)return;if(s)return ce.resolve(t,s);if(t===n)return;t=ce.dirname(t)}};we.exports.exists=cn;we.exports.sync.exists=cn.sync;we.exports.stop=jr});var pn=p((_l,fn)=>{"use strict";fn.exports=function(r){return r?r instanceof Array||Array.isArray(r)||r.length>=0&&r.splice instanceof Function:!1}});var hn=p((Tl,dn)=>{"use strict";var Us=require("util"),qs=pn(),Ur=function(r,t){(!r||r.constructor!==String)&&(t=r||{},r=Error.name);var n=function i(a){if(!this)return new i(a);a=a instanceof Error?a.message:a||this.message,Error.call(this,a),Error.captureStackTrace(this,n),this.name=r,Object.defineProperty(this,"message",{configurable:!0,enumerable:!1,get:function(){var l=a.split(/\r?\n/g);for(var h in t)if(t.hasOwnProperty(h)){var b=t[h];"message"in b&&(l=b.message(this[h],l)||l,qs(l)||(l=[l]))}return l.join(`
`)},set:function(l){a=l}});var s=null,o=Object.getOwnPropertyDescriptor(this,"stack"),u=o.get,f=o.value;delete o.value,delete o.writable,o.set=function(l){s=l},o.get=function(){var l=(s||(u?u.call(this):f)).split(/\r?\n+/g);s||(l[0]=this.name+": "+this.message);var h=1;for(var b in t)if(t.hasOwnProperty(b)){var x=t[b];if("line"in x){var m=x.line(this[b]);m&&l.splice(h++,0,"    "+m)}"stack"in x&&x.stack(this[b],l)}return l.join(`
`)},Object.defineProperty(this,"stack",o)};return Object.setPrototypeOf?(Object.setPrototypeOf(n.prototype,Error.prototype),Object.setPrototypeOf(n,Error)):Us.inherits(n,Error),n};Ur.append=function(e,r){return{message:function(t,n){return t=t||r,t&&(n[0]+=" "+e.replace("%s",t.toString())),n}}};Ur.line=function(e,r){return{line:function(t){return t=t||r,t?e.replace("%s",t.toString()):null}}};dn.exports=Ur});var gn=p((Fl,vn)=>{"use strict";var $s=e=>{let r=e.charCodeAt(0).toString(16).toUpperCase();return"0x"+(r.length%2?"0":"")+r},Ws=(e,r,t)=>{if(!r)return{message:e.message+" while parsing empty string",position:0};let n=e.message.match(/^Unexpected token (.) .*position\s+(\d+)/i),i=n?+n[2]:e.message.match(/^Unexpected end of JSON.*/i)?r.length-1:null,a=n?e.message.replace(/^Unexpected token ./,`Unexpected token ${JSON.stringify(n[1])} (${$s(n[1])})`):e.message;if(i!=null){let s=i<=t?0:i-t,o=i+t>=r.length?r.length:i+t,u=(s===0?"":"...")+r.slice(s,o)+(o===r.length?"":"...");return{message:a+` while parsing ${r===u?"":"near "}${JSON.stringify(u)}`,position:i}}else return{message:a+` while parsing '${r.slice(0,t*2)}'`,position:0}},sr=class extends SyntaxError{constructor(r,t,n,i){n=n||20;let a=Ws(r,t,n);super(a.message),Object.assign(this,a),this.code="EJSONPARSE",this.systemError=r,Error.captureStackTrace(this,i||this.constructor)}get name(){return this.constructor.name}set name(r){}get[Symbol.toStringTag](){return this.constructor.name}},Hs=Symbol.for("indent"),Ys=Symbol.for("newline"),zs=/^\s*[{\[]((?:\r?\n)+)([\s\t]*)/,Vs=/^(?:\{\}|\[\])((?:\r?\n)+)?$/,or=(e,r,t)=>{let n=mn(e);t=t||20;try{let[,i=`
`,a="  "]=n.match(Vs)||n.match(zs)||[,"",""],s=JSON.parse(n,r);return s&&typeof s=="object"&&(s[Ys]=i,s[Hs]=a),s}catch(i){if(typeof e!="string"&&!Buffer.isBuffer(e)){let a=Array.isArray(e)&&e.length===0;throw Object.assign(new TypeError(`Cannot parse ${a?"an empty array":String(e)}`),{code:"EJSONPARSE",systemError:i})}throw new sr(i,n,t,or)}},mn=e=>String(e).replace(/^\uFEFF/,"");vn.exports=or;or.JSONParseError=sr;or.noExceptions=(e,r)=>{try{return JSON.parse(mn(e),r)}catch{}}});var Ln=p(Be=>{"use strict";Be.__esModule=!0;Be.LinesAndColumns=void 0;var ur=`
`,bn="\r",yn=function(){function e(r){this.string=r;for(var t=[0],n=0;n<r.length;)switch(r[n]){case ur:n+=ur.length,t.push(n);break;case bn:n+=bn.length,r[n]===ur&&(n+=ur.length),t.push(n);break;default:n++;break}this.offsets=t}return e.prototype.locationForIndex=function(r){if(r<0||r>this.string.length)return null;for(var t=0,n=this.offsets;n[t+1]<=r;)t++;var i=r-n[t];return{line:t,column:i}},e.prototype.indexForLocation=function(r){var t=r.line,n=r.column;return t<0||t>=this.offsets.length||n<0||n>this.lengthOfLine(t)?null:this.offsets[t]+n},e.prototype.lengthOfLine=function(r){var t=this.offsets[r],n=r===this.offsets.length-1?this.string.length:this.offsets[r+1];return n-t},e}();Be.LinesAndColumns=yn;Be.default=yn});var Cn=p(cr=>{Object.defineProperty(cr,"__esModule",{value:!0});cr.default=/((['"])(?:(?!\2|\\).|\\(?:\r\n|[\s\S]))*(\2)?|`(?:[^`\\$]|\\[\s\S]|\$(?!\{)|\$\{(?:[^{}]|\{[^}]*\}?)*\}?)*(`)?)|(\/\/.*)|(\/\*(?:[^*]|\*(?!\/))*(\*\/)?)|(\/(?!\*)(?:\[(?:(?![\]\\]).|\\.)*\]|(?![\/\]\\]).|\\.)+\/(?:(?!\s*(?:\b|[\u0080-\uFFFF$\\'"~({]|[+\-!](?!=)|\.?\d))|[gmiyus]{1,6}\b(?![\u0080-\uFFFF$\\]|\s*(?:[+\-*%&|^<>!=?({]|\/(?![\/*])))))|(0[xX][\da-fA-F]+|0[oO][0-7]+|0[bB][01]+|(?:\d*\.\d+|\d+\.?)(?:[eE][+-]?\d+)?)|((?!\d)(?:(?!\s)[$\w\u0080-\uFFFF]|\\u[\da-fA-F]{4}|\\u\{[\da-fA-F]+\})+)|(--|\+\+|&&|\|\||=>|\.{3}|(?:[+\-\/%&|^]|\*{1,2}|<{1,2}|>{1,3}|!=?|={1,2})=?|[?~.,:;[\](){}])|(\s+)|(^$|[\s\S])/g;cr.matchToToken=function(e){var r={type:"invalid",value:e[0],closed:void 0};return e[1]?(r.type="string",r.closed=!!(e[3]||e[4])):e[5]?r.type="comment":e[6]?(r.type="comment",r.closed=!!e[7]):e[8]?r.type="regex":e[9]?r.type="number":e[10]?r.type="name":e[11]?r.type="punctuator":e[12]&&(r.type="whitespace"),r}});var En=p(Ge=>{"use strict";Object.defineProperty(Ge,"__esModule",{value:!0});Ge.isIdentifierChar=xn;Ge.isIdentifierName=Zs;Ge.isIdentifierStart=Sn;var $r="\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C8A\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CD\uA7D0\uA7D1\uA7D3\uA7D5-\uA7DC\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC",wn="\xB7\u0300-\u036F\u0387\u0483-\u0487\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u0669\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u06F0-\u06F9\u0711\u0730-\u074A\u07A6-\u07B0\u07C0-\u07C9\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0897-\u089F\u08CA-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0966-\u096F\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09E6-\u09EF\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A66-\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AE6-\u0AEF\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B66-\u0B6F\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0BE6-\u0BEF\u0C00-\u0C04\u0C3C\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0CE6-\u0CEF\u0CF3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D66-\u0D6F\u0D81-\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0E50-\u0E59\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0ED0-\u0ED9\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1040-\u1049\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F-\u109D\u135D-\u135F\u1369-\u1371\u1712-\u1715\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u17E0-\u17E9\u180B-\u180D\u180F-\u1819\u18A9\u1920-\u192B\u1930-\u193B\u1946-\u194F\u19D0-\u19DA\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AB0-\u1ABD\u1ABF-\u1ACE\u1B00-\u1B04\u1B34-\u1B44\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BB0-\u1BB9\u1BE6-\u1BF3\u1C24-\u1C37\u1C40-\u1C49\u1C50-\u1C59\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DFF\u200C\u200D\u203F\u2040\u2054\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\u30FB\uA620-\uA629\uA66F\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA82C\uA880\uA881\uA8B4-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F1\uA8FF-\uA909\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9D0-\uA9D9\uA9E5\uA9F0-\uA9F9\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA50-\uAA59\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uABF0-\uABF9\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFF10-\uFF19\uFF3F\uFF65",Js=new RegExp("["+$r+"]"),Xs=new RegExp("["+$r+wn+"]");$r=wn=null;var Pn=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191],Ks=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239];function qr(e,r){let t=65536;for(let n=0,i=r.length;n<i;n+=2){if(t+=r[n],t>e)return!1;if(t+=r[n+1],t>=e)return!0}return!1}function Sn(e){return e<65?e===36:e<=90?!0:e<97?e===95:e<=122?!0:e<=65535?e>=170&&Js.test(String.fromCharCode(e)):qr(e,Pn)}function xn(e){return e<48?e===36:e<58?!0:e<65?!1:e<=90?!0:e<97?e===95:e<=122?!0:e<=65535?e>=170&&Xs.test(String.fromCharCode(e)):qr(e,Pn)||qr(e,Ks)}function Zs(e){let r=!0;for(let t=0;t<e.length;t++){let n=e.charCodeAt(t);if((n&64512)===55296&&t+1<e.length){let i=e.charCodeAt(++t);(i&64512)===56320&&(n=65536+((n&1023)<<10)+(i&1023))}if(r){if(r=!1,!Sn(n))return!1}else if(!xn(n))return!1}return!r}});var In=p(de=>{"use strict";Object.defineProperty(de,"__esModule",{value:!0});de.isKeyword=no;de.isReservedWord=On;de.isStrictBindOnlyReservedWord=Nn;de.isStrictBindReservedWord=to;de.isStrictReservedWord=Dn;var Wr={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]},Qs=new Set(Wr.keyword),eo=new Set(Wr.strict),ro=new Set(Wr.strictBind);function On(e,r){return r&&e==="await"||e==="enum"}function Dn(e,r){return On(e,r)||eo.has(e)}function Nn(e){return ro.has(e)}function to(e,r){return Dn(e,r)||Nn(e)}function no(e){return Qs.has(e)}});var An=p(Q=>{"use strict";Object.defineProperty(Q,"__esModule",{value:!0});Object.defineProperty(Q,"isIdentifierChar",{enumerable:!0,get:function(){return Hr.isIdentifierChar}});Object.defineProperty(Q,"isIdentifierName",{enumerable:!0,get:function(){return Hr.isIdentifierName}});Object.defineProperty(Q,"isIdentifierStart",{enumerable:!0,get:function(){return Hr.isIdentifierStart}});Object.defineProperty(Q,"isKeyword",{enumerable:!0,get:function(){return Me.isKeyword}});Object.defineProperty(Q,"isReservedWord",{enumerable:!0,get:function(){return Me.isReservedWord}});Object.defineProperty(Q,"isStrictBindOnlyReservedWord",{enumerable:!0,get:function(){return Me.isStrictBindOnlyReservedWord}});Object.defineProperty(Q,"isStrictBindReservedWord",{enumerable:!0,get:function(){return Me.isStrictBindReservedWord}});Object.defineProperty(Q,"isStrictReservedWord",{enumerable:!0,get:function(){return Me.isStrictReservedWord}});var Hr=En(),Me=In()});var _n=p((ql,Yr)=>{var fr=process||{},Rn=fr.argv||[],lr=fr.env||{},io=!(lr.NO_COLOR||Rn.includes("--no-color"))&&(!!lr.FORCE_COLOR||Rn.includes("--color")||fr.platform==="win32"||(fr.stdout||{}).isTTY&&lr.TERM!=="dumb"||!!lr.CI),ao=(e,r,t=e)=>n=>{let i=""+n,a=i.indexOf(r,e.length);return~a?e+so(i,r,t,a)+r:e+i+r},so=(e,r,t,n)=>{let i="",a=0;do i+=e.substring(a,n)+t,a=n+r.length,n=e.indexOf(r,a);while(~n);return i+e.substring(a)},kn=(e=io)=>{let r=e?ao:()=>String;return{isColorSupported:e,reset:r("\x1B[0m","\x1B[0m"),bold:r("\x1B[1m","\x1B[22m","\x1B[22m\x1B[1m"),dim:r("\x1B[2m","\x1B[22m","\x1B[22m\x1B[2m"),italic:r("\x1B[3m","\x1B[23m"),underline:r("\x1B[4m","\x1B[24m"),inverse:r("\x1B[7m","\x1B[27m"),hidden:r("\x1B[8m","\x1B[28m"),strikethrough:r("\x1B[9m","\x1B[29m"),black:r("\x1B[30m","\x1B[39m"),red:r("\x1B[31m","\x1B[39m"),green:r("\x1B[32m","\x1B[39m"),yellow:r("\x1B[33m","\x1B[39m"),blue:r("\x1B[34m","\x1B[39m"),magenta:r("\x1B[35m","\x1B[39m"),cyan:r("\x1B[36m","\x1B[39m"),white:r("\x1B[37m","\x1B[39m"),gray:r("\x1B[90m","\x1B[39m"),bgBlack:r("\x1B[40m","\x1B[49m"),bgRed:r("\x1B[41m","\x1B[49m"),bgGreen:r("\x1B[42m","\x1B[49m"),bgYellow:r("\x1B[43m","\x1B[49m"),bgBlue:r("\x1B[44m","\x1B[49m"),bgMagenta:r("\x1B[45m","\x1B[49m"),bgCyan:r("\x1B[46m","\x1B[49m"),bgWhite:r("\x1B[47m","\x1B[49m"),blackBright:r("\x1B[90m","\x1B[39m"),redBright:r("\x1B[91m","\x1B[39m"),greenBright:r("\x1B[92m","\x1B[39m"),yellowBright:r("\x1B[93m","\x1B[39m"),blueBright:r("\x1B[94m","\x1B[39m"),magentaBright:r("\x1B[95m","\x1B[39m"),cyanBright:r("\x1B[96m","\x1B[39m"),whiteBright:r("\x1B[97m","\x1B[39m"),bgBlackBright:r("\x1B[100m","\x1B[49m"),bgRedBright:r("\x1B[101m","\x1B[49m"),bgGreenBright:r("\x1B[102m","\x1B[49m"),bgYellowBright:r("\x1B[103m","\x1B[49m"),bgBlueBright:r("\x1B[104m","\x1B[49m"),bgMagentaBright:r("\x1B[105m","\x1B[49m"),bgCyanBright:r("\x1B[106m","\x1B[49m"),bgWhiteBright:r("\x1B[107m","\x1B[49m")}};Yr.exports=kn();Yr.exports.createColors=kn});var Fn=p(($l,Tn)=>{"use strict";var oo=/[|\\{}()[\]^$+*?.]/g;Tn.exports=function(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(oo,"\\$&")}});var Gn=p((Wl,Bn)=>{"use strict";Bn.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}});var zr=p((Hl,qn)=>{var he=Gn(),Un={};for(pr in he)he.hasOwnProperty(pr)&&(Un[he[pr]]=pr);var pr,d=qn.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(W in d)if(d.hasOwnProperty(W)){if(!("channels"in d[W]))throw new Error("missing channels property: "+W);if(!("labels"in d[W]))throw new Error("missing channel labels property: "+W);if(d[W].labels.length!==d[W].channels)throw new Error("channel and label counts mismatch: "+W);Mn=d[W].channels,jn=d[W].labels,delete d[W].channels,delete d[W].labels,Object.defineProperty(d[W],"channels",{value:Mn}),Object.defineProperty(d[W],"labels",{value:jn})}var Mn,jn,W;d.rgb.hsl=function(e){var r=e[0]/255,t=e[1]/255,n=e[2]/255,i=Math.min(r,t,n),a=Math.max(r,t,n),s=a-i,o,u,f;return a===i?o=0:r===a?o=(t-n)/s:t===a?o=2+(n-r)/s:n===a&&(o=4+(r-t)/s),o=Math.min(o*60,360),o<0&&(o+=360),f=(i+a)/2,a===i?u=0:f<=.5?u=s/(a+i):u=s/(2-a-i),[o,u*100,f*100]};d.rgb.hsv=function(e){var r,t,n,i,a,s=e[0]/255,o=e[1]/255,u=e[2]/255,f=Math.max(s,o,u),l=f-Math.min(s,o,u),h=function(b){return(f-b)/6/l+1/2};return l===0?i=a=0:(a=l/f,r=h(s),t=h(o),n=h(u),s===f?i=n-t:o===f?i=1/3+r-n:u===f&&(i=2/3+t-r),i<0?i+=1:i>1&&(i-=1)),[i*360,a*100,f*100]};d.rgb.hwb=function(e){var r=e[0],t=e[1],n=e[2],i=d.rgb.hsl(e)[0],a=1/255*Math.min(r,Math.min(t,n));return n=1-1/255*Math.max(r,Math.max(t,n)),[i,a*100,n*100]};d.rgb.cmyk=function(e){var r=e[0]/255,t=e[1]/255,n=e[2]/255,i,a,s,o;return o=Math.min(1-r,1-t,1-n),i=(1-r-o)/(1-o)||0,a=(1-t-o)/(1-o)||0,s=(1-n-o)/(1-o)||0,[i*100,a*100,s*100,o*100]};function uo(e,r){return Math.pow(e[0]-r[0],2)+Math.pow(e[1]-r[1],2)+Math.pow(e[2]-r[2],2)}d.rgb.keyword=function(e){var r=Un[e];if(r)return r;var t=1/0,n;for(var i in he)if(he.hasOwnProperty(i)){var a=he[i],s=uo(e,a);s<t&&(t=s,n=i)}return n};d.keyword.rgb=function(e){return he[e]};d.rgb.xyz=function(e){var r=e[0]/255,t=e[1]/255,n=e[2]/255;r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92,t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92,n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92;var i=r*.4124+t*.3576+n*.1805,a=r*.2126+t*.7152+n*.0722,s=r*.0193+t*.1192+n*.9505;return[i*100,a*100,s*100]};d.rgb.lab=function(e){var r=d.rgb.xyz(e),t=r[0],n=r[1],i=r[2],a,s,o;return t/=95.047,n/=100,i/=108.883,t=t>.008856?Math.pow(t,1/3):7.787*t+16/116,n=n>.008856?Math.pow(n,1/3):7.787*n+16/116,i=i>.008856?Math.pow(i,1/3):7.787*i+16/116,a=116*n-16,s=500*(t-n),o=200*(n-i),[a,s,o]};d.hsl.rgb=function(e){var r=e[0]/360,t=e[1]/100,n=e[2]/100,i,a,s,o,u;if(t===0)return u=n*255,[u,u,u];n<.5?a=n*(1+t):a=n+t-n*t,i=2*n-a,o=[0,0,0];for(var f=0;f<3;f++)s=r+1/3*-(f-1),s<0&&s++,s>1&&s--,6*s<1?u=i+(a-i)*6*s:2*s<1?u=a:3*s<2?u=i+(a-i)*(2/3-s)*6:u=i,o[f]=u*255;return o};d.hsl.hsv=function(e){var r=e[0],t=e[1]/100,n=e[2]/100,i=t,a=Math.max(n,.01),s,o;return n*=2,t*=n<=1?n:2-n,i*=a<=1?a:2-a,o=(n+t)/2,s=n===0?2*i/(a+i):2*t/(n+t),[r,s*100,o*100]};d.hsv.rgb=function(e){var r=e[0]/60,t=e[1]/100,n=e[2]/100,i=Math.floor(r)%6,a=r-Math.floor(r),s=255*n*(1-t),o=255*n*(1-t*a),u=255*n*(1-t*(1-a));switch(n*=255,i){case 0:return[n,u,s];case 1:return[o,n,s];case 2:return[s,n,u];case 3:return[s,o,n];case 4:return[u,s,n];case 5:return[n,s,o]}};d.hsv.hsl=function(e){var r=e[0],t=e[1]/100,n=e[2]/100,i=Math.max(n,.01),a,s,o;return o=(2-t)*n,a=(2-t)*i,s=t*i,s/=a<=1?a:2-a,s=s||0,o/=2,[r,s*100,o*100]};d.hwb.rgb=function(e){var r=e[0]/360,t=e[1]/100,n=e[2]/100,i=t+n,a,s,o,u;i>1&&(t/=i,n/=i),a=Math.floor(6*r),s=1-n,o=6*r-a,a&1&&(o=1-o),u=t+o*(s-t);var f,l,h;switch(a){default:case 6:case 0:f=s,l=u,h=t;break;case 1:f=u,l=s,h=t;break;case 2:f=t,l=s,h=u;break;case 3:f=t,l=u,h=s;break;case 4:f=u,l=t,h=s;break;case 5:f=s,l=t,h=u;break}return[f*255,l*255,h*255]};d.cmyk.rgb=function(e){var r=e[0]/100,t=e[1]/100,n=e[2]/100,i=e[3]/100,a,s,o;return a=1-Math.min(1,r*(1-i)+i),s=1-Math.min(1,t*(1-i)+i),o=1-Math.min(1,n*(1-i)+i),[a*255,s*255,o*255]};d.xyz.rgb=function(e){var r=e[0]/100,t=e[1]/100,n=e[2]/100,i,a,s;return i=r*3.2406+t*-1.5372+n*-.4986,a=r*-.9689+t*1.8758+n*.0415,s=r*.0557+t*-.204+n*1.057,i=i>.0031308?1.055*Math.pow(i,1/2.4)-.055:i*12.92,a=a>.0031308?1.055*Math.pow(a,1/2.4)-.055:a*12.92,s=s>.0031308?1.055*Math.pow(s,1/2.4)-.055:s*12.92,i=Math.min(Math.max(0,i),1),a=Math.min(Math.max(0,a),1),s=Math.min(Math.max(0,s),1),[i*255,a*255,s*255]};d.xyz.lab=function(e){var r=e[0],t=e[1],n=e[2],i,a,s;return r/=95.047,t/=100,n/=108.883,r=r>.008856?Math.pow(r,1/3):7.787*r+16/116,t=t>.008856?Math.pow(t,1/3):7.787*t+16/116,n=n>.008856?Math.pow(n,1/3):7.787*n+16/116,i=116*t-16,a=500*(r-t),s=200*(t-n),[i,a,s]};d.lab.xyz=function(e){var r=e[0],t=e[1],n=e[2],i,a,s;a=(r+16)/116,i=t/500+a,s=a-n/200;var o=Math.pow(a,3),u=Math.pow(i,3),f=Math.pow(s,3);return a=o>.008856?o:(a-16/116)/7.787,i=u>.008856?u:(i-16/116)/7.787,s=f>.008856?f:(s-16/116)/7.787,i*=95.047,a*=100,s*=108.883,[i,a,s]};d.lab.lch=function(e){var r=e[0],t=e[1],n=e[2],i,a,s;return i=Math.atan2(n,t),a=i*360/2/Math.PI,a<0&&(a+=360),s=Math.sqrt(t*t+n*n),[r,s,a]};d.lch.lab=function(e){var r=e[0],t=e[1],n=e[2],i,a,s;return s=n/360*2*Math.PI,i=t*Math.cos(s),a=t*Math.sin(s),[r,i,a]};d.rgb.ansi16=function(e){var r=e[0],t=e[1],n=e[2],i=1 in arguments?arguments[1]:d.rgb.hsv(e)[2];if(i=Math.round(i/50),i===0)return 30;var a=30+(Math.round(n/255)<<2|Math.round(t/255)<<1|Math.round(r/255));return i===2&&(a+=60),a};d.hsv.ansi16=function(e){return d.rgb.ansi16(d.hsv.rgb(e),e[2])};d.rgb.ansi256=function(e){var r=e[0],t=e[1],n=e[2];if(r===t&&t===n)return r<8?16:r>248?231:Math.round((r-8)/247*24)+232;var i=16+36*Math.round(r/255*5)+6*Math.round(t/255*5)+Math.round(n/255*5);return i};d.ansi16.rgb=function(e){var r=e%10;if(r===0||r===7)return e>50&&(r+=3.5),r=r/10.5*255,[r,r,r];var t=(~~(e>50)+1)*.5,n=(r&1)*t*255,i=(r>>1&1)*t*255,a=(r>>2&1)*t*255;return[n,i,a]};d.ansi256.rgb=function(e){if(e>=232){var r=(e-232)*10+8;return[r,r,r]}e-=16;var t,n=Math.floor(e/36)/5*255,i=Math.floor((t=e%36)/6)/5*255,a=t%6/5*255;return[n,i,a]};d.rgb.hex=function(e){var r=((Math.round(e[0])&255)<<16)+((Math.round(e[1])&255)<<8)+(Math.round(e[2])&255),t=r.toString(16).toUpperCase();return"000000".substring(t.length)+t};d.hex.rgb=function(e){var r=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!r)return[0,0,0];var t=r[0];r[0].length===3&&(t=t.split("").map(function(o){return o+o}).join(""));var n=parseInt(t,16),i=n>>16&255,a=n>>8&255,s=n&255;return[i,a,s]};d.rgb.hcg=function(e){var r=e[0]/255,t=e[1]/255,n=e[2]/255,i=Math.max(Math.max(r,t),n),a=Math.min(Math.min(r,t),n),s=i-a,o,u;return s<1?o=a/(1-s):o=0,s<=0?u=0:i===r?u=(t-n)/s%6:i===t?u=2+(n-r)/s:u=4+(r-t)/s+4,u/=6,u%=1,[u*360,s*100,o*100]};d.hsl.hcg=function(e){var r=e[1]/100,t=e[2]/100,n=1,i=0;return t<.5?n=2*r*t:n=2*r*(1-t),n<1&&(i=(t-.5*n)/(1-n)),[e[0],n*100,i*100]};d.hsv.hcg=function(e){var r=e[1]/100,t=e[2]/100,n=r*t,i=0;return n<1&&(i=(t-n)/(1-n)),[e[0],n*100,i*100]};d.hcg.rgb=function(e){var r=e[0]/360,t=e[1]/100,n=e[2]/100;if(t===0)return[n*255,n*255,n*255];var i=[0,0,0],a=r%1*6,s=a%1,o=1-s,u=0;switch(Math.floor(a)){case 0:i[0]=1,i[1]=s,i[2]=0;break;case 1:i[0]=o,i[1]=1,i[2]=0;break;case 2:i[0]=0,i[1]=1,i[2]=s;break;case 3:i[0]=0,i[1]=o,i[2]=1;break;case 4:i[0]=s,i[1]=0,i[2]=1;break;default:i[0]=1,i[1]=0,i[2]=o}return u=(1-t)*n,[(t*i[0]+u)*255,(t*i[1]+u)*255,(t*i[2]+u)*255]};d.hcg.hsv=function(e){var r=e[1]/100,t=e[2]/100,n=r+t*(1-r),i=0;return n>0&&(i=r/n),[e[0],i*100,n*100]};d.hcg.hsl=function(e){var r=e[1]/100,t=e[2]/100,n=t*(1-r)+.5*r,i=0;return n>0&&n<.5?i=r/(2*n):n>=.5&&n<1&&(i=r/(2*(1-n))),[e[0],i*100,n*100]};d.hcg.hwb=function(e){var r=e[1]/100,t=e[2]/100,n=r+t*(1-r);return[e[0],(n-r)*100,(1-n)*100]};d.hwb.hcg=function(e){var r=e[1]/100,t=e[2]/100,n=1-t,i=n-r,a=0;return i<1&&(a=(n-i)/(1-i)),[e[0],i*100,a*100]};d.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]};d.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]};d.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]};d.gray.hsl=d.gray.hsv=function(e){return[0,0,e[0]]};d.gray.hwb=function(e){return[0,100,e[0]]};d.gray.cmyk=function(e){return[0,0,0,e[0]]};d.gray.lab=function(e){return[e[0],0,0]};d.gray.hex=function(e){var r=Math.round(e[0]/100*255)&255,t=(r<<16)+(r<<8)+r,n=t.toString(16).toUpperCase();return"000000".substring(n.length)+n};d.rgb.gray=function(e){var r=(e[0]+e[1]+e[2])/3;return[r/255*100]}});var Wn=p((Yl,$n)=>{var dr=zr();function co(){for(var e={},r=Object.keys(dr),t=r.length,n=0;n<t;n++)e[r[n]]={distance:-1,parent:null};return e}function lo(e){var r=co(),t=[e];for(r[e].distance=0;t.length;)for(var n=t.pop(),i=Object.keys(dr[n]),a=i.length,s=0;s<a;s++){var o=i[s],u=r[o];u.distance===-1&&(u.distance=r[n].distance+1,u.parent=n,t.unshift(o))}return r}function fo(e,r){return function(t){return r(e(t))}}function po(e,r){for(var t=[r[e].parent,e],n=dr[r[e].parent][e],i=r[e].parent;r[i].parent;)t.unshift(r[i].parent),n=fo(dr[r[i].parent][i],n),i=r[i].parent;return n.conversion=t,n}$n.exports=function(e){for(var r=lo(e),t={},n=Object.keys(r),i=n.length,a=0;a<i;a++){var s=n[a],o=r[s];o.parent!==null&&(t[s]=po(s,r))}return t}});var Yn=p((zl,Hn)=>{var Vr=zr(),ho=Wn(),Pe={},mo=Object.keys(Vr);function vo(e){var r=function(t){return t==null?t:(arguments.length>1&&(t=Array.prototype.slice.call(arguments)),e(t))};return"conversion"in e&&(r.conversion=e.conversion),r}function go(e){var r=function(t){if(t==null)return t;arguments.length>1&&(t=Array.prototype.slice.call(arguments));var n=e(t);if(typeof n=="object")for(var i=n.length,a=0;a<i;a++)n[a]=Math.round(n[a]);return n};return"conversion"in e&&(r.conversion=e.conversion),r}mo.forEach(function(e){Pe[e]={},Object.defineProperty(Pe[e],"channels",{value:Vr[e].channels}),Object.defineProperty(Pe[e],"labels",{value:Vr[e].labels});var r=ho(e),t=Object.keys(r);t.forEach(function(n){var i=r[n];Pe[e][n]=go(i),Pe[e][n].raw=vo(i)})});Hn.exports=Pe});var Vn=p((Vl,zn)=>{"use strict";var Se=Yn(),hr=(e,r)=>function(){return`\x1B[${e.apply(Se,arguments)+r}m`},mr=(e,r)=>function(){let t=e.apply(Se,arguments);return`\x1B[${38+r};5;${t}m`},vr=(e,r)=>function(){let t=e.apply(Se,arguments);return`\x1B[${38+r};2;${t[0]};${t[1]};${t[2]}m`};function bo(){let e=new Map,r={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};r.color.grey=r.color.gray;for(let i of Object.keys(r)){let a=r[i];for(let s of Object.keys(a)){let o=a[s];r[s]={open:`\x1B[${o[0]}m`,close:`\x1B[${o[1]}m`},a[s]=r[s],e.set(o[0],o[1])}Object.defineProperty(r,i,{value:a,enumerable:!1}),Object.defineProperty(r,"codes",{value:e,enumerable:!1})}let t=i=>i,n=(i,a,s)=>[i,a,s];r.color.close="\x1B[39m",r.bgColor.close="\x1B[49m",r.color.ansi={ansi:hr(t,0)},r.color.ansi256={ansi256:mr(t,0)},r.color.ansi16m={rgb:vr(n,0)},r.bgColor.ansi={ansi:hr(t,10)},r.bgColor.ansi256={ansi256:mr(t,10)},r.bgColor.ansi16m={rgb:vr(n,10)};for(let i of Object.keys(Se)){if(typeof Se[i]!="object")continue;let a=Se[i];i==="ansi16"&&(i="ansi"),"ansi16"in a&&(r.color.ansi[i]=hr(a.ansi16,0),r.bgColor.ansi[i]=hr(a.ansi16,10)),"ansi256"in a&&(r.color.ansi256[i]=mr(a.ansi256,0),r.bgColor.ansi256[i]=mr(a.ansi256,10)),"rgb"in a&&(r.color.ansi16m[i]=vr(a.rgb,0),r.bgColor.ansi16m[i]=vr(a.rgb,10))}return r}Object.defineProperty(zn,"exports",{enumerable:!0,get:bo})});var Xn=p((Jl,Jn)=>{"use strict";Jn.exports=(e,r)=>{r=r||process.argv;let t=e.startsWith("-")?"":e.length===1?"-":"--",n=r.indexOf(t+e),i=r.indexOf("--");return n!==-1&&(i===-1?!0:n<i)}});var Zn=p((Xl,Kn)=>{"use strict";var yo=require("os"),X=Xn(),U=process.env,xe;X("no-color")||X("no-colors")||X("color=false")?xe=!1:(X("color")||X("colors")||X("color=true")||X("color=always"))&&(xe=!0);"FORCE_COLOR"in U&&(xe=U.FORCE_COLOR.length===0||parseInt(U.FORCE_COLOR,10)!==0);function Lo(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function Co(e){if(xe===!1)return 0;if(X("color=16m")||X("color=full")||X("color=truecolor"))return 3;if(X("color=256"))return 2;if(e&&!e.isTTY&&xe!==!0)return 0;let r=xe?1:0;if(process.platform==="win32"){let t=yo.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(t[0])>=10&&Number(t[2])>=10586?Number(t[2])>=14931?3:2:1}if("CI"in U)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(t=>t in U)||U.CI_NAME==="codeship"?1:r;if("TEAMCITY_VERSION"in U)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(U.TEAMCITY_VERSION)?1:0;if(U.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in U){let t=parseInt((U.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(U.TERM_PROGRAM){case"iTerm.app":return t>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(U.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(U.TERM)||"COLORTERM"in U?1:(U.TERM==="dumb",r)}function Jr(e){let r=Co(e);return Lo(r)}Kn.exports={supportsColor:Jr,stdout:Jr(process.stdout),stderr:Jr(process.stderr)}});var ni=p((Kl,ti)=>{"use strict";var wo=/(?:\\(u[a-f\d]{4}|x[a-f\d]{2}|.))|(?:\{(~)?(\w+(?:\([^)]*\))?(?:\.\w+(?:\([^)]*\))?)*)(?:[ \t]|(?=\r?\n)))|(\})|((?:.|[\r\n\f])+?)/gi,Qn=/(?:^|\.)(\w+)(?:\(([^)]*)\))?/g,Po=/^(['"])((?:\\.|(?!\1)[^\\])*)\1$/,So=/\\(u[a-f\d]{4}|x[a-f\d]{2}|.)|([^\\])/gi,xo=new Map([["n",`
`],["r","\r"],["t","	"],["b","\b"],["f","\f"],["v","\v"],["0","\0"],["\\","\\"],["e","\x1B"],["a","\x07"]]);function ri(e){return e[0]==="u"&&e.length===5||e[0]==="x"&&e.length===3?String.fromCharCode(parseInt(e.slice(1),16)):xo.get(e)||e}function Eo(e,r){let t=[],n=r.trim().split(/\s*,\s*/g),i;for(let a of n)if(!isNaN(a))t.push(Number(a));else if(i=a.match(Po))t.push(i[2].replace(So,(s,o,u)=>o?ri(o):u));else throw new Error(`Invalid Chalk template style argument: ${a} (in style '${e}')`);return t}function Oo(e){Qn.lastIndex=0;let r=[],t;for(;(t=Qn.exec(e))!==null;){let n=t[1];if(t[2]){let i=Eo(n,t[2]);r.push([n].concat(i))}else r.push([n])}return r}function ei(e,r){let t={};for(let i of r)for(let a of i.styles)t[a[0]]=i.inverse?null:a.slice(1);let n=e;for(let i of Object.keys(t))if(Array.isArray(t[i])){if(!(i in n))throw new Error(`Unknown Chalk style: ${i}`);t[i].length>0?n=n[i].apply(n,t[i]):n=n[i]}return n}ti.exports=(e,r)=>{let t=[],n=[],i=[];if(r.replace(wo,(a,s,o,u,f,l)=>{if(s)i.push(ri(s));else if(u){let h=i.join("");i=[],n.push(t.length===0?h:ei(e,t)(h)),t.push({inverse:o,styles:Oo(u)})}else if(f){if(t.length===0)throw new Error("Found extraneous } in Chalk template literal");n.push(ei(e,t)(i.join(""))),i=[],t.pop()}else i.push(l)}),n.push(i.join("")),t.length>0){let a=`Chalk template literal is missing ${t.length} closing bracket${t.length===1?"":"s"} (\`}\`)`;throw new Error(a)}return n.join("")}});var ui=p((Zl,Ue)=>{"use strict";var Kr=Fn(),B=Vn(),Xr=Zn().stdout,Do=ni(),ai=process.platform==="win32"&&!(process.env.TERM||"").toLowerCase().startsWith("xterm"),si=["ansi","ansi","ansi256","ansi16m"],oi=new Set(["gray"]),Ee=Object.create(null);function ii(e,r){r=r||{};let t=Xr?Xr.level:0;e.level=r.level===void 0?t:r.level,e.enabled="enabled"in r?r.enabled:e.level>0}function je(e){if(!this||!(this instanceof je)||this.template){let r={};return ii(r,e),r.template=function(){let t=[].slice.call(arguments);return Ao.apply(null,[r.template].concat(t))},Object.setPrototypeOf(r,je.prototype),Object.setPrototypeOf(r.template,r),r.template.constructor=je,r.template}ii(this,e)}ai&&(B.blue.open="\x1B[94m");for(let e of Object.keys(B))B[e].closeRe=new RegExp(Kr(B[e].close),"g"),Ee[e]={get(){let r=B[e];return gr.call(this,this._styles?this._styles.concat(r):[r],this._empty,e)}};Ee.visible={get(){return gr.call(this,this._styles||[],!0,"visible")}};B.color.closeRe=new RegExp(Kr(B.color.close),"g");for(let e of Object.keys(B.color.ansi))oi.has(e)||(Ee[e]={get(){let r=this.level;return function(){let n={open:B.color[si[r]][e].apply(null,arguments),close:B.color.close,closeRe:B.color.closeRe};return gr.call(this,this._styles?this._styles.concat(n):[n],this._empty,e)}}});B.bgColor.closeRe=new RegExp(Kr(B.bgColor.close),"g");for(let e of Object.keys(B.bgColor.ansi)){if(oi.has(e))continue;let r="bg"+e[0].toUpperCase()+e.slice(1);Ee[r]={get(){let t=this.level;return function(){let i={open:B.bgColor[si[t]][e].apply(null,arguments),close:B.bgColor.close,closeRe:B.bgColor.closeRe};return gr.call(this,this._styles?this._styles.concat(i):[i],this._empty,e)}}}}var No=Object.defineProperties(()=>{},Ee);function gr(e,r,t){let n=function(){return Io.apply(n,arguments)};n._styles=e,n._empty=r;let i=this;return Object.defineProperty(n,"level",{enumerable:!0,get(){return i.level},set(a){i.level=a}}),Object.defineProperty(n,"enabled",{enumerable:!0,get(){return i.enabled},set(a){i.enabled=a}}),n.hasGrey=this.hasGrey||t==="gray"||t==="grey",n.__proto__=No,n}function Io(){let e=arguments,r=e.length,t=String(arguments[0]);if(r===0)return"";if(r>1)for(let i=1;i<r;i++)t+=" "+e[i];if(!this.enabled||this.level<=0||!t)return this._empty?"":t;let n=B.dim.open;ai&&this.hasGrey&&(B.dim.open="");for(let i of this._styles.slice().reverse())t=i.open+t.replace(i.closeRe,i.open)+i.close,t=t.replace(/\r?\n/g,`${i.close}$&${i.open}`);return B.dim.open=n,t}function Ao(e,r){if(!Array.isArray(r))return[].slice.call(arguments,1).join(" ");let t=[].slice.call(arguments,2),n=[r.raw[0]];for(let i=1;i<r.length;i++)n.push(String(t[i-1]).replace(/[{}\\]/g,"\\$&")),n.push(String(r.raw[i]));return Do(e,n.join(""))}Object.defineProperties(je.prototype,Ee);Ue.exports=je();Ue.exports.supportsColor=Xr;Ue.exports.default=Ue.exports});var vi=p(qe=>{"use strict";Object.defineProperty(qe,"__esModule",{value:!0});qe.default=Mo;qe.shouldHighlight=mi;var ci=Cn(),li=An(),Qr=Ro(_n(),!0);function pi(e){if(typeof WeakMap!="function")return null;var r=new WeakMap,t=new WeakMap;return(pi=function(n){return n?t:r})(e)}function Ro(e,r){if(!r&&e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var t=pi(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if(a!=="default"&&{}.hasOwnProperty.call(e,a)){var s=i?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,t&&t.set(e,n),n}var di=typeof process=="object"&&(process.env.FORCE_COLOR==="0"||process.env.FORCE_COLOR==="false")?(0,Qr.createColors)(!1):Qr.default,fi=(e,r)=>t=>e(r(t)),ko=new Set(["as","async","from","get","of","set"]);function _o(e){return{keyword:e.cyan,capitalized:e.yellow,jsxIdentifier:e.yellow,punctuator:e.yellow,number:e.magenta,string:e.green,regex:e.magenta,comment:e.gray,invalid:fi(fi(e.white,e.bgRed),e.bold)}}var To=/\r\n|[\n\r\u2028\u2029]/,Fo=/^[()[\]{}]$/,hi;{let e=/^[a-z][\w-]*$/i,r=function(t,n,i){if(t.type==="name"){if((0,li.isKeyword)(t.value)||(0,li.isStrictReservedWord)(t.value,!0)||ko.has(t.value))return"keyword";if(e.test(t.value)&&(i[n-1]==="<"||i.slice(n-2,n)==="</"))return"jsxIdentifier";if(t.value[0]!==t.value[0].toLowerCase())return"capitalized"}return t.type==="punctuator"&&Fo.test(t.value)?"bracket":t.type==="invalid"&&(t.value==="@"||t.value==="#")?"punctuator":t.type};hi=function*(t){let n;for(;n=ci.default.exec(t);){let i=ci.matchToToken(n);yield{type:r(i,n.index,t),value:i.value}}}}function Bo(e,r){let t="";for(let{type:n,value:i}of hi(r)){let a=e[n];a?t+=i.split(To).map(s=>a(s)).join(`
`):t+=i}return t}function mi(e){return di.isColorSupported||e.forceColor}var Zr;function Go(e){if(e){var r;return(r=Zr)!=null||(Zr=(0,Qr.createColors)(!0)),Zr}return di}function Mo(e,r={}){if(e!==""&&mi(r)){let t=_o(Go(r.forceColor));return Bo(t,e)}else return e}{let e,r;qe.getChalk=({forceColor:t})=>{var n;if((n=e)!=null||(e=ui()),t){var i;return(i=r)!=null||(r=new e.constructor({enabled:!0,level:1})),r}return e}}});var Ci=p(br=>{"use strict";Object.defineProperty(br,"__esModule",{value:!0});br.codeFrameColumns=Li;br.default=$o;var et=jo(vi());function yi(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return yi=function(){return e},e}function jo(e){if(e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var r=yi();if(r&&r.has(e))return r.get(e);var t={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var a=n?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(t,i,a):t[i]=e[i]}return t.default=e,r&&r.set(e,t),t}var gi=!1;function Uo(e){return{gutter:e.grey,marker:e.red.bold,message:e.red.bold}}var bi=/\r\n|[\n\r\u2028\u2029]/;function qo(e,r,t){let n=Object.assign({column:0,line:-1},e.start),i=Object.assign({},n,e.end),{linesAbove:a=2,linesBelow:s=3}=t||{},o=n.line,u=n.column,f=i.line,l=i.column,h=Math.max(o-(a+1),0),b=Math.min(r.length,f+s);o===-1&&(h=0),f===-1&&(b=r.length);let x=f-o,m={};if(x)for(let g=0;g<=x;g++){let L=g+o;if(!u)m[L]=!0;else if(g===0){let I=r[L-1].length;m[L]=[u,I-u+1]}else if(g===x)m[L]=[0,l];else{let I=r[L-g].length;m[L]=[0,I]}}else u===l?u?m[o]=[u,0]:m[o]=!0:m[o]=[u,l-u];return{start:h,end:b,markerLines:m}}function Li(e,r,t={}){let n=(t.highlightCode||t.forceColor)&&(0,et.shouldHighlight)(t),i=(0,et.getChalk)(t),a=Uo(i),s=(g,L)=>n?g(L):L,o=e.split(bi),{start:u,end:f,markerLines:l}=qo(r,o,t),h=r.start&&typeof r.start.column=="number",b=String(f).length,m=(n?(0,et.default)(e,t):e).split(bi).slice(u,f).map((g,L)=>{let I=u+1+L,M=` ${` ${I}`.slice(-b)} | `,te=l[I],oe=!l[I+1];if(te){let z="";if(Array.isArray(te)){let E=g.slice(0,Math.max(te[0]-1,0)).replace(/[^\t]/g," "),R=te[1]||1;z=[`
 `,s(a.gutter,M.replace(/\d/g," ")),E,s(a.marker,"^").repeat(R)].join(""),oe&&t.message&&(z+=" "+s(a.message,t.message))}return[s(a.marker,">"),s(a.gutter,M),g,z].join("")}else return` ${s(a.gutter,M)}${g}`}).join(`
`);return t.message&&!h&&(m=`${" ".repeat(b+1)}${t.message}
${m}`),n?i.reset(m):m}function $o(e,r,t,n={}){if(!gi){gi=!0;let a="Passing lineNumber and colNumber is deprecated to @babel/code-frame. Please use `codeFrameColumns`.";if(process.emitWarning)process.emitWarning(a,"DeprecationWarning");else{let s=new Error(a);s.name="DeprecationWarning",console.warn(new Error(a))}}return t=Math.max(t,0),Li(e,{start:{column:t,line:r}},n)}});var xi=p((r1,Si)=>{"use strict";var rt=hn(),Wo=gn(),{default:Ho}=Ln(),{codeFrameColumns:Yo}=Ci(),wi=rt("JSONError",{fileName:rt.append("in %s"),codeFrame:rt.append(`

%s
`)}),Pi=(e,r,t)=>{typeof r=="string"&&(t=r,r=null);try{try{return JSON.parse(e,r)}catch(n){throw Wo(e,r),n}}catch(n){n.message=n.message.replace(/\n/g,"");let i=n.message.match(/in JSON at position (\d+) while parsing/),a=new wi(n);if(t&&(a.fileName=t),i&&i.length>0){let s=new Ho(e),o=Number(i[1]),u=s.locationForIndex(o),f=Yo(e,{start:{line:u.line+1,column:u.column+1}},{highlightCode:!0});a.codeFrame=f}throw a}};Pi.JSONError=wi;Si.exports=Pi});var qi=p((y,Ui)=>{y=Ui.exports=P;var O;typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?O=function(){var e=Array.prototype.slice.call(arguments,0);e.unshift("SEMVER"),console.log.apply(console,e)}:O=function(){};y.SEMVER_SPEC_VERSION="2.0.0";var $e=256,yr=Number.MAX_SAFE_INTEGER||9007199254740991,tt=16,zo=$e-6,We=y.re=[],N=y.safeRe=[],c=y.src=[],w=0,ot="[a-zA-Z0-9-]",nt=[["\\s",1],["\\d",$e],[ot,zo]];function xr(e){for(var r=0;r<nt.length;r++){var t=nt[r][0],n=nt[r][1];e=e.split(t+"*").join(t+"{0,"+n+"}").split(t+"+").join(t+"{1,"+n+"}")}return e}var Oe=w++;c[Oe]="0|[1-9]\\d*";var De=w++;c[De]="\\d+";var ut=w++;c[ut]="\\d*[a-zA-Z-]"+ot+"*";var Oi=w++;c[Oi]="("+c[Oe]+")\\.("+c[Oe]+")\\.("+c[Oe]+")";var Di=w++;c[Di]="("+c[De]+")\\.("+c[De]+")\\.("+c[De]+")";var it=w++;c[it]="(?:"+c[Oe]+"|"+c[ut]+")";var at=w++;c[at]="(?:"+c[De]+"|"+c[ut]+")";var ct=w++;c[ct]="(?:-("+c[it]+"(?:\\."+c[it]+")*))";var lt=w++;c[lt]="(?:-?("+c[at]+"(?:\\."+c[at]+")*))";var st=w++;c[st]=ot+"+";var Ye=w++;c[Ye]="(?:\\+("+c[st]+"(?:\\."+c[st]+")*))";var ft=w++,Ni="v?"+c[Oi]+c[ct]+"?"+c[Ye]+"?";c[ft]="^"+Ni+"$";var pt="[v=\\s]*"+c[Di]+c[lt]+"?"+c[Ye]+"?",dt=w++;c[dt]="^"+pt+"$";var ke=w++;c[ke]="((?:<|>)?=?)";var Lr=w++;c[Lr]=c[De]+"|x|X|\\*";var Cr=w++;c[Cr]=c[Oe]+"|x|X|\\*";var me=w++;c[me]="[v=\\s]*("+c[Cr]+")(?:\\.("+c[Cr]+")(?:\\.("+c[Cr]+")(?:"+c[ct]+")?"+c[Ye]+"?)?)?";var Ie=w++;c[Ie]="[v=\\s]*("+c[Lr]+")(?:\\.("+c[Lr]+")(?:\\.("+c[Lr]+")(?:"+c[lt]+")?"+c[Ye]+"?)?)?";var Ii=w++;c[Ii]="^"+c[ke]+"\\s*"+c[me]+"$";var Ai=w++;c[Ai]="^"+c[ke]+"\\s*"+c[Ie]+"$";var Ri=w++;c[Ri]="(?:^|[^\\d])(\\d{1,"+tt+"})(?:\\.(\\d{1,"+tt+"}))?(?:\\.(\\d{1,"+tt+"}))?(?:$|[^\\d])";var Er=w++;c[Er]="(?:~>?)";var Ae=w++;c[Ae]="(\\s*)"+c[Er]+"\\s+";We[Ae]=new RegExp(c[Ae],"g");N[Ae]=new RegExp(xr(c[Ae]),"g");var Vo="$1~",ki=w++;c[ki]="^"+c[Er]+c[me]+"$";var _i=w++;c[_i]="^"+c[Er]+c[Ie]+"$";var Or=w++;c[Or]="(?:\\^)";var Re=w++;c[Re]="(\\s*)"+c[Or]+"\\s+";We[Re]=new RegExp(c[Re],"g");N[Re]=new RegExp(xr(c[Re]),"g");var Jo="$1^",Ti=w++;c[Ti]="^"+c[Or]+c[me]+"$";var Fi=w++;c[Fi]="^"+c[Or]+c[Ie]+"$";var ht=w++;c[ht]="^"+c[ke]+"\\s*("+pt+")$|^$";var mt=w++;c[mt]="^"+c[ke]+"\\s*("+Ni+")$|^$";var ve=w++;c[ve]="(\\s*)"+c[ke]+"\\s*("+pt+"|"+c[me]+")";We[ve]=new RegExp(c[ve],"g");N[ve]=new RegExp(xr(c[ve]),"g");var Xo="$1$2$3",Bi=w++;c[Bi]="^\\s*("+c[me]+")\\s+-\\s+("+c[me]+")\\s*$";var Gi=w++;c[Gi]="^\\s*("+c[Ie]+")\\s+-\\s+("+c[Ie]+")\\s*$";var Mi=w++;c[Mi]="(<|>)?=?\\s*\\*";for(ee=0;ee<w;ee++)O(ee,c[ee]),We[ee]||(We[ee]=new RegExp(c[ee]),N[ee]=new RegExp(xr(c[ee])));var ee;y.parse=ge;function ge(e,r){if((!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1}),e instanceof P)return e;if(typeof e!="string"||e.length>$e)return null;var t=r.loose?N[dt]:N[ft];if(!t.test(e))return null;try{return new P(e,r)}catch{return null}}y.valid=Ko;function Ko(e,r){var t=ge(e,r);return t?t.version:null}y.clean=Zo;function Zo(e,r){var t=ge(e.trim().replace(/^[=v]+/,""),r);return t?t.version:null}y.SemVer=P;function P(e,r){if((!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1}),e instanceof P){if(e.loose===r.loose)return e;e=e.version}else if(typeof e!="string")throw new TypeError("Invalid Version: "+e);if(e.length>$e)throw new TypeError("version is longer than "+$e+" characters");if(!(this instanceof P))return new P(e,r);O("SemVer",e,r),this.options=r,this.loose=!!r.loose;var t=e.trim().match(r.loose?N[dt]:N[ft]);if(!t)throw new TypeError("Invalid Version: "+e);if(this.raw=e,this.major=+t[1],this.minor=+t[2],this.patch=+t[3],this.major>yr||this.major<0)throw new TypeError("Invalid major version");if(this.minor>yr||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>yr||this.patch<0)throw new TypeError("Invalid patch version");t[4]?this.prerelease=t[4].split(".").map(function(n){if(/^[0-9]+$/.test(n)){var i=+n;if(i>=0&&i<yr)return i}return n}):this.prerelease=[],this.build=t[5]?t[5].split("."):[],this.format()}P.prototype.format=function(){return this.version=this.major+"."+this.minor+"."+this.patch,this.prerelease.length&&(this.version+="-"+this.prerelease.join(".")),this.version};P.prototype.toString=function(){return this.version};P.prototype.compare=function(e){return O("SemVer.compare",this.version,this.options,e),e instanceof P||(e=new P(e,this.options)),this.compareMain(e)||this.comparePre(e)};P.prototype.compareMain=function(e){return e instanceof P||(e=new P(e,this.options)),Ne(this.major,e.major)||Ne(this.minor,e.minor)||Ne(this.patch,e.patch)};P.prototype.comparePre=function(e){if(e instanceof P||(e=new P(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;var r=0;do{var t=this.prerelease[r],n=e.prerelease[r];if(O("prerelease compare",r,t,n),t===void 0&&n===void 0)return 0;if(n===void 0)return 1;if(t===void 0)return-1;if(t===n)continue;return Ne(t,n)}while(++r)};P.prototype.inc=function(e,r){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r),this.inc("pre",r);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",r),this.inc("pre",r);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":if(this.prerelease.length===0)this.prerelease=[0];else{for(var t=this.prerelease.length;--t>=0;)typeof this.prerelease[t]=="number"&&(this.prerelease[t]++,t=-2);t===-1&&this.prerelease.push(0)}r&&(this.prerelease[0]===r?isNaN(this.prerelease[1])&&(this.prerelease=[r,0]):this.prerelease=[r,0]);break;default:throw new Error("invalid increment argument: "+e)}return this.format(),this.raw=this.version,this};y.inc=Qo;function Qo(e,r,t,n){typeof t=="string"&&(n=t,t=void 0);try{return new P(e,t).inc(r,n).version}catch{return null}}y.diff=eu;function eu(e,r){if(vt(e,r))return null;var t=ge(e),n=ge(r),i="";if(t.prerelease.length||n.prerelease.length){i="pre";var a="prerelease"}for(var s in t)if((s==="major"||s==="minor"||s==="patch")&&t[s]!==n[s])return i+s;return a}y.compareIdentifiers=Ne;var Ei=/^[0-9]+$/;function Ne(e,r){var t=Ei.test(e),n=Ei.test(r);return t&&n&&(e=+e,r=+r),e===r?0:t&&!n?-1:n&&!t?1:e<r?-1:1}y.rcompareIdentifiers=ru;function ru(e,r){return Ne(r,e)}y.major=tu;function tu(e,r){return new P(e,r).major}y.minor=nu;function nu(e,r){return new P(e,r).minor}y.patch=iu;function iu(e,r){return new P(e,r).patch}y.compare=ae;function ae(e,r,t){return new P(e,t).compare(new P(r,t))}y.compareLoose=au;function au(e,r){return ae(e,r,!0)}y.rcompare=su;function su(e,r,t){return ae(r,e,t)}y.sort=ou;function ou(e,r){return e.sort(function(t,n){return y.compare(t,n,r)})}y.rsort=uu;function uu(e,r){return e.sort(function(t,n){return y.rcompare(t,n,r)})}y.gt=He;function He(e,r,t){return ae(e,r,t)>0}y.lt=wr;function wr(e,r,t){return ae(e,r,t)<0}y.eq=vt;function vt(e,r,t){return ae(e,r,t)===0}y.neq=ji;function ji(e,r,t){return ae(e,r,t)!==0}y.gte=gt;function gt(e,r,t){return ae(e,r,t)>=0}y.lte=bt;function bt(e,r,t){return ae(e,r,t)<=0}y.cmp=Pr;function Pr(e,r,t,n){switch(r){case"===":return typeof e=="object"&&(e=e.version),typeof t=="object"&&(t=t.version),e===t;case"!==":return typeof e=="object"&&(e=e.version),typeof t=="object"&&(t=t.version),e!==t;case"":case"=":case"==":return vt(e,t,n);case"!=":return ji(e,t,n);case">":return He(e,t,n);case">=":return gt(e,t,n);case"<":return wr(e,t,n);case"<=":return bt(e,t,n);default:throw new TypeError("Invalid operator: "+r)}}y.Comparator=J;function J(e,r){if((!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1}),e instanceof J){if(e.loose===!!r.loose)return e;e=e.value}if(!(this instanceof J))return new J(e,r);e=e.trim().split(/\s+/).join(" "),O("comparator",e,r),this.options=r,this.loose=!!r.loose,this.parse(e),this.semver===ze?this.value="":this.value=this.operator+this.semver.version,O("comp",this)}var ze={};J.prototype.parse=function(e){var r=this.options.loose?N[ht]:N[mt],t=e.match(r);if(!t)throw new TypeError("Invalid comparator: "+e);this.operator=t[1],this.operator==="="&&(this.operator=""),t[2]?this.semver=new P(t[2],this.options.loose):this.semver=ze};J.prototype.toString=function(){return this.value};J.prototype.test=function(e){return O("Comparator.test",e,this.options.loose),this.semver===ze?!0:(typeof e=="string"&&(e=new P(e,this.options)),Pr(e,this.operator,this.semver,this.options))};J.prototype.intersects=function(e,r){if(!(e instanceof J))throw new TypeError("a Comparator is required");(!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1});var t;if(this.operator==="")return t=new A(e.value,r),Sr(this.value,t,r);if(e.operator==="")return t=new A(this.value,r),Sr(e.semver,t,r);var n=(this.operator===">="||this.operator===">")&&(e.operator===">="||e.operator===">"),i=(this.operator==="<="||this.operator==="<")&&(e.operator==="<="||e.operator==="<"),a=this.semver.version===e.semver.version,s=(this.operator===">="||this.operator==="<=")&&(e.operator===">="||e.operator==="<="),o=Pr(this.semver,"<",e.semver,r)&&(this.operator===">="||this.operator===">")&&(e.operator==="<="||e.operator==="<"),u=Pr(this.semver,">",e.semver,r)&&(this.operator==="<="||this.operator==="<")&&(e.operator===">="||e.operator===">");return n||i||a&&s||o||u};y.Range=A;function A(e,r){if((!r||typeof r!="object")&&(r={loose:!!r,includePrerelease:!1}),e instanceof A)return e.loose===!!r.loose&&e.includePrerelease===!!r.includePrerelease?e:new A(e.raw,r);if(e instanceof J)return new A(e.value,r);if(!(this instanceof A))return new A(e,r);if(this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease,this.raw=e.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(function(t){return this.parseRange(t.trim())},this).filter(function(t){return t.length}),!this.set.length)throw new TypeError("Invalid SemVer Range: "+this.raw);this.format()}A.prototype.format=function(){return this.range=this.set.map(function(e){return e.join(" ").trim()}).join("||").trim(),this.range};A.prototype.toString=function(){return this.range};A.prototype.parseRange=function(e){var r=this.options.loose,t=r?N[Gi]:N[Bi];e=e.replace(t,bu),O("hyphen replace",e),e=e.replace(N[ve],Xo),O("comparator trim",e,N[ve]),e=e.replace(N[Ae],Vo),e=e.replace(N[Re],Jo);var n=r?N[ht]:N[mt],i=e.split(" ").map(function(a){return lu(a,this.options)},this).join(" ").split(/\s+/);return this.options.loose&&(i=i.filter(function(a){return!!a.match(n)})),i=i.map(function(a){return new J(a,this.options)},this),i};A.prototype.intersects=function(e,r){if(!(e instanceof A))throw new TypeError("a Range is required");return this.set.some(function(t){return t.every(function(n){return e.set.some(function(i){return i.every(function(a){return n.intersects(a,r)})})})})};y.toComparators=cu;function cu(e,r){return new A(e,r).set.map(function(t){return t.map(function(n){return n.value}).join(" ").trim().split(" ")})}function lu(e,r){return O("comp",e,r),e=du(e,r),O("caret",e),e=fu(e,r),O("tildes",e),e=mu(e,r),O("xrange",e),e=gu(e,r),O("stars",e),e}function H(e){return!e||e.toLowerCase()==="x"||e==="*"}function fu(e,r){return e.trim().split(/\s+/).map(function(t){return pu(t,r)}).join(" ")}function pu(e,r){var t=r.loose?N[_i]:N[ki];return e.replace(t,function(n,i,a,s,o){O("tilde",e,n,i,a,s,o);var u;return H(i)?u="":H(a)?u=">="+i+".0.0 <"+(+i+1)+".0.0":H(s)?u=">="+i+"."+a+".0 <"+i+"."+(+a+1)+".0":o?(O("replaceTilde pr",o),u=">="+i+"."+a+"."+s+"-"+o+" <"+i+"."+(+a+1)+".0"):u=">="+i+"."+a+"."+s+" <"+i+"."+(+a+1)+".0",O("tilde return",u),u})}function du(e,r){return e.trim().split(/\s+/).map(function(t){return hu(t,r)}).join(" ")}function hu(e,r){O("caret",e,r);var t=r.loose?N[Fi]:N[Ti];return e.replace(t,function(n,i,a,s,o){O("caret",e,n,i,a,s,o);var u;return H(i)?u="":H(a)?u=">="+i+".0.0 <"+(+i+1)+".0.0":H(s)?i==="0"?u=">="+i+"."+a+".0 <"+i+"."+(+a+1)+".0":u=">="+i+"."+a+".0 <"+(+i+1)+".0.0":o?(O("replaceCaret pr",o),i==="0"?a==="0"?u=">="+i+"."+a+"."+s+"-"+o+" <"+i+"."+a+"."+(+s+1):u=">="+i+"."+a+"."+s+"-"+o+" <"+i+"."+(+a+1)+".0":u=">="+i+"."+a+"."+s+"-"+o+" <"+(+i+1)+".0.0"):(O("no pr"),i==="0"?a==="0"?u=">="+i+"."+a+"."+s+" <"+i+"."+a+"."+(+s+1):u=">="+i+"."+a+"."+s+" <"+i+"."+(+a+1)+".0":u=">="+i+"."+a+"."+s+" <"+(+i+1)+".0.0"),O("caret return",u),u})}function mu(e,r){return O("replaceXRanges",e,r),e.split(/\s+/).map(function(t){return vu(t,r)}).join(" ")}function vu(e,r){e=e.trim();var t=r.loose?N[Ai]:N[Ii];return e.replace(t,function(n,i,a,s,o,u){O("xRange",e,n,i,a,s,o,u);var f=H(a),l=f||H(s),h=l||H(o),b=h;return i==="="&&b&&(i=""),f?i===">"||i==="<"?n="<0.0.0":n="*":i&&b?(l&&(s=0),o=0,i===">"?(i=">=",l?(a=+a+1,s=0,o=0):(s=+s+1,o=0)):i==="<="&&(i="<",l?a=+a+1:s=+s+1),n=i+a+"."+s+"."+o):l?n=">="+a+".0.0 <"+(+a+1)+".0.0":h&&(n=">="+a+"."+s+".0 <"+a+"."+(+s+1)+".0"),O("xRange return",n),n})}function gu(e,r){return O("replaceStars",e,r),e.trim().replace(N[Mi],"")}function bu(e,r,t,n,i,a,s,o,u,f,l,h,b){return H(t)?r="":H(n)?r=">="+t+".0.0":H(i)?r=">="+t+"."+n+".0":r=">="+r,H(u)?o="":H(f)?o="<"+(+u+1)+".0.0":H(l)?o="<"+u+"."+(+f+1)+".0":h?o="<="+u+"."+f+"."+l+"-"+h:o="<="+o,(r+" "+o).trim()}A.prototype.test=function(e){if(!e)return!1;typeof e=="string"&&(e=new P(e,this.options));for(var r=0;r<this.set.length;r++)if(yu(this.set[r],e,this.options))return!0;return!1};function yu(e,r,t){for(var n=0;n<e.length;n++)if(!e[n].test(r))return!1;if(r.prerelease.length&&!t.includePrerelease){for(n=0;n<e.length;n++)if(O(e[n].semver),e[n].semver!==ze&&e[n].semver.prerelease.length>0){var i=e[n].semver;if(i.major===r.major&&i.minor===r.minor&&i.patch===r.patch)return!0}return!1}return!0}y.satisfies=Sr;function Sr(e,r,t){try{r=new A(r,t)}catch{return!1}return r.test(e)}y.maxSatisfying=Lu;function Lu(e,r,t){var n=null,i=null;try{var a=new A(r,t)}catch{return null}return e.forEach(function(s){a.test(s)&&(!n||i.compare(s)===-1)&&(n=s,i=new P(n,t))}),n}y.minSatisfying=Cu;function Cu(e,r,t){var n=null,i=null;try{var a=new A(r,t)}catch{return null}return e.forEach(function(s){a.test(s)&&(!n||i.compare(s)===1)&&(n=s,i=new P(n,t))}),n}y.minVersion=wu;function wu(e,r){e=new A(e,r);var t=new P("0.0.0");if(e.test(t)||(t=new P("0.0.0-0"),e.test(t)))return t;t=null;for(var n=0;n<e.set.length;++n){var i=e.set[n];i.forEach(function(a){var s=new P(a.semver.version);switch(a.operator){case">":s.prerelease.length===0?s.patch++:s.prerelease.push(0),s.raw=s.format();case"":case">=":(!t||He(t,s))&&(t=s);break;case"<":case"<=":break;default:throw new Error("Unexpected operation: "+a.operator)}})}return t&&e.test(t)?t:null}y.validRange=Pu;function Pu(e,r){try{return new A(e,r).range||"*"}catch{return null}}y.ltr=Su;function Su(e,r,t){return yt(e,r,"<",t)}y.gtr=xu;function xu(e,r,t){return yt(e,r,">",t)}y.outside=yt;function yt(e,r,t,n){e=new P(e,n),r=new A(r,n);var i,a,s,o,u;switch(t){case">":i=He,a=bt,s=wr,o=">",u=">=";break;case"<":i=wr,a=gt,s=He,o="<",u="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(Sr(e,r,n))return!1;for(var f=0;f<r.set.length;++f){var l=r.set[f],h=null,b=null;if(l.forEach(function(x){x.semver===ze&&(x=new J(">=0.0.0")),h=h||x,b=b||x,i(x.semver,h.semver,n)?h=x:s(x.semver,b.semver,n)&&(b=x)}),h.operator===o||h.operator===u||(!b.operator||b.operator===o)&&a(e,b.semver))return!1;if(b.operator===u&&s(e,b.semver))return!1}return!0}y.prerelease=Eu;function Eu(e,r){var t=ge(e,r);return t&&t.prerelease.length?t.prerelease:null}y.intersects=Ou;function Ou(e,r,t){return e=new A(e,t),r=new A(r,t),e.intersects(r)}y.coerce=Du;function Du(e){if(e instanceof P)return e;if(typeof e!="string")return null;var r=e.match(N[Ri]);return r==null?null:ge(r[1]+"."+(r[2]||"0")+"."+(r[3]||"0"))}});var Lt=p((t1,Nu)=>{Nu.exports=["0BSD","3D-Slicer-1.0","AAL","ADSL","AFL-1.1","AFL-1.2","AFL-2.0","AFL-2.1","AFL-3.0","AGPL-1.0-only","AGPL-1.0-or-later","AGPL-3.0-only","AGPL-3.0-or-later","AMD-newlib","AMDPLPA","AML","AML-glslang","AMPAS","ANTLR-PD","ANTLR-PD-fallback","APAFML","APL-1.0","APSL-1.0","APSL-1.1","APSL-1.2","APSL-2.0","ASWF-Digital-Assets-1.0","ASWF-Digital-Assets-1.1","Abstyles","AdaCore-doc","Adobe-2006","Adobe-Display-PostScript","Adobe-Glyph","Adobe-Utopia","Afmparse","Aladdin","Apache-1.0","Apache-1.1","Apache-2.0","App-s2p","Arphic-1999","Artistic-1.0","Artistic-1.0-Perl","Artistic-1.0-cl8","Artistic-2.0","BSD-1-Clause","BSD-2-Clause","BSD-2-Clause-Darwin","BSD-2-Clause-Patent","BSD-2-Clause-Views","BSD-2-Clause-first-lines","BSD-3-Clause","BSD-3-Clause-Attribution","BSD-3-Clause-Clear","BSD-3-Clause-HP","BSD-3-Clause-LBNL","BSD-3-Clause-Modification","BSD-3-Clause-No-Military-License","BSD-3-Clause-No-Nuclear-License","BSD-3-Clause-No-Nuclear-License-2014","BSD-3-Clause-No-Nuclear-Warranty","BSD-3-Clause-Open-MPI","BSD-3-Clause-Sun","BSD-3-Clause-acpica","BSD-3-Clause-flex","BSD-4-Clause","BSD-4-Clause-Shortened","BSD-4-Clause-UC","BSD-4.3RENO","BSD-4.3TAHOE","BSD-Advertising-Acknowledgement","BSD-Attribution-HPND-disclaimer","BSD-Inferno-Nettverk","BSD-Protection","BSD-Source-Code","BSD-Source-beginning-file","BSD-Systemics","BSD-Systemics-W3Works","BSL-1.0","BUSL-1.1","Baekmuk","Bahyph","Barr","Beerware","BitTorrent-1.0","BitTorrent-1.1","Bitstream-Charter","Bitstream-Vera","BlueOak-1.0.0","Boehm-GC","Borceux","Brian-Gladman-2-Clause","Brian-Gladman-3-Clause","C-UDA-1.0","CAL-1.0","CAL-1.0-Combined-Work-Exception","CATOSL-1.1","CC-BY-1.0","CC-BY-2.0","CC-BY-2.5","CC-BY-2.5-AU","CC-BY-3.0","CC-BY-3.0-AT","CC-BY-3.0-AU","CC-BY-3.0-DE","CC-BY-3.0-IGO","CC-BY-3.0-NL","CC-BY-3.0-US","CC-BY-4.0","CC-BY-NC-1.0","CC-BY-NC-2.0","CC-BY-NC-2.5","CC-BY-NC-3.0","CC-BY-NC-3.0-DE","CC-BY-NC-4.0","CC-BY-NC-ND-1.0","CC-BY-NC-ND-2.0","CC-BY-NC-ND-2.5","CC-BY-NC-ND-3.0","CC-BY-NC-ND-3.0-DE","CC-BY-NC-ND-3.0-IGO","CC-BY-NC-ND-4.0","CC-BY-NC-SA-1.0","CC-BY-NC-SA-2.0","CC-BY-NC-SA-2.0-DE","CC-BY-NC-SA-2.0-FR","CC-BY-NC-SA-2.0-UK","CC-BY-NC-SA-2.5","CC-BY-NC-SA-3.0","CC-BY-NC-SA-3.0-DE","CC-BY-NC-SA-3.0-IGO","CC-BY-NC-SA-4.0","CC-BY-ND-1.0","CC-BY-ND-2.0","CC-BY-ND-2.5","CC-BY-ND-3.0","CC-BY-ND-3.0-DE","CC-BY-ND-4.0","CC-BY-SA-1.0","CC-BY-SA-2.0","CC-BY-SA-2.0-UK","CC-BY-SA-2.1-JP","CC-BY-SA-2.5","CC-BY-SA-3.0","CC-BY-SA-3.0-AT","CC-BY-SA-3.0-DE","CC-BY-SA-3.0-IGO","CC-BY-SA-4.0","CC-PDDC","CC0-1.0","CDDL-1.0","CDDL-1.1","CDL-1.0","CDLA-Permissive-1.0","CDLA-Permissive-2.0","CDLA-Sharing-1.0","CECILL-1.0","CECILL-1.1","CECILL-2.0","CECILL-2.1","CECILL-B","CECILL-C","CERN-OHL-1.1","CERN-OHL-1.2","CERN-OHL-P-2.0","CERN-OHL-S-2.0","CERN-OHL-W-2.0","CFITSIO","CMU-Mach","CMU-Mach-nodoc","CNRI-Jython","CNRI-Python","CNRI-Python-GPL-Compatible","COIL-1.0","CPAL-1.0","CPL-1.0","CPOL-1.02","CUA-OPL-1.0","Caldera","Caldera-no-preamble","Catharon","ClArtistic","Clips","Community-Spec-1.0","Condor-1.1","Cornell-Lossless-JPEG","Cronyx","Crossword","CrystalStacker","Cube","D-FSL-1.0","DEC-3-Clause","DL-DE-BY-2.0","DL-DE-ZERO-2.0","DOC","DRL-1.0","DRL-1.1","DSDP","DocBook-Schema","DocBook-XML","Dotseqn","ECL-1.0","ECL-2.0","EFL-1.0","EFL-2.0","EPICS","EPL-1.0","EPL-2.0","EUDatagrid","EUPL-1.0","EUPL-1.1","EUPL-1.2","Elastic-2.0","Entessa","ErlPL-1.1","Eurosym","FBM","FDK-AAC","FSFAP","FSFAP-no-warranty-disclaimer","FSFUL","FSFULLR","FSFULLRWD","FTL","Fair","Ferguson-Twofish","Frameworx-1.0","FreeBSD-DOC","FreeImage","Furuseth","GCR-docs","GD","GFDL-1.1-invariants-only","GFDL-1.1-invariants-or-later","GFDL-1.1-no-invariants-only","GFDL-1.1-no-invariants-or-later","GFDL-1.1-only","GFDL-1.1-or-later","GFDL-1.2-invariants-only","GFDL-1.2-invariants-or-later","GFDL-1.2-no-invariants-only","GFDL-1.2-no-invariants-or-later","GFDL-1.2-only","GFDL-1.2-or-later","GFDL-1.3-invariants-only","GFDL-1.3-invariants-or-later","GFDL-1.3-no-invariants-only","GFDL-1.3-no-invariants-or-later","GFDL-1.3-only","GFDL-1.3-or-later","GL2PS","GLWTPL","GPL-1.0-only","GPL-1.0-or-later","GPL-2.0-only","GPL-2.0-or-later","GPL-3.0-only","GPL-3.0-or-later","Giftware","Glide","Glulxe","Graphics-Gems","Gutmann","HIDAPI","HP-1986","HP-1989","HPND","HPND-DEC","HPND-Fenneberg-Livingston","HPND-INRIA-IMAG","HPND-Intel","HPND-Kevlin-Henney","HPND-MIT-disclaimer","HPND-Markus-Kuhn","HPND-Netrek","HPND-Pbmplus","HPND-UC","HPND-UC-export-US","HPND-doc","HPND-doc-sell","HPND-export-US","HPND-export-US-acknowledgement","HPND-export-US-modify","HPND-export2-US","HPND-merchantability-variant","HPND-sell-MIT-disclaimer-xserver","HPND-sell-regexpr","HPND-sell-variant","HPND-sell-variant-MIT-disclaimer","HPND-sell-variant-MIT-disclaimer-rev","HTMLTIDY","HaskellReport","Hippocratic-2.1","IBM-pibs","ICU","IEC-Code-Components-EULA","IJG","IJG-short","IPA","IPL-1.0","ISC","ISC-Veillard","ImageMagick","Imlib2","Info-ZIP","Inner-Net-2.0","Intel","Intel-ACPI","Interbase-1.0","JPL-image","JPNIC","JSON","Jam","JasPer-2.0","Kastrup","Kazlib","Knuth-CTAN","LAL-1.2","LAL-1.3","LGPL-2.0-only","LGPL-2.0-or-later","LGPL-2.1-only","LGPL-2.1-or-later","LGPL-3.0-only","LGPL-3.0-or-later","LGPLLR","LOOP","LPD-document","LPL-1.0","LPL-1.02","LPPL-1.0","LPPL-1.1","LPPL-1.2","LPPL-1.3a","LPPL-1.3c","LZMA-SDK-9.11-to-9.20","LZMA-SDK-9.22","Latex2e","Latex2e-translated-notice","Leptonica","LiLiQ-P-1.1","LiLiQ-R-1.1","LiLiQ-Rplus-1.1","Libpng","Linux-OpenIB","Linux-man-pages-1-para","Linux-man-pages-copyleft","Linux-man-pages-copyleft-2-para","Linux-man-pages-copyleft-var","Lucida-Bitmap-Fonts","MIT","MIT-0","MIT-CMU","MIT-Festival","MIT-Khronos-old","MIT-Modern-Variant","MIT-Wu","MIT-advertising","MIT-enna","MIT-feh","MIT-open-group","MIT-testregex","MITNFA","MMIXware","MPEG-SSG","MPL-1.0","MPL-1.1","MPL-2.0","MPL-2.0-no-copyleft-exception","MS-LPL","MS-PL","MS-RL","MTLL","Mackerras-3-Clause","Mackerras-3-Clause-acknowledgment","MakeIndex","Martin-Birgmeier","McPhee-slideshow","Minpack","MirOS","Motosoto","MulanPSL-1.0","MulanPSL-2.0","Multics","Mup","NAIST-2003","NASA-1.3","NBPL-1.0","NCBI-PD","NCGL-UK-2.0","NCL","NCSA","NGPL","NICTA-1.0","NIST-PD","NIST-PD-fallback","NIST-Software","NLOD-1.0","NLOD-2.0","NLPL","NOSL","NPL-1.0","NPL-1.1","NPOSL-3.0","NRL","NTP","NTP-0","Naumen","NetCDF","Newsletr","Nokia","Noweb","O-UDA-1.0","OAR","OCCT-PL","OCLC-2.0","ODC-By-1.0","ODbL-1.0","OFFIS","OFL-1.0","OFL-1.0-RFN","OFL-1.0-no-RFN","OFL-1.1","OFL-1.1-RFN","OFL-1.1-no-RFN","OGC-1.0","OGDL-Taiwan-1.0","OGL-Canada-2.0","OGL-UK-1.0","OGL-UK-2.0","OGL-UK-3.0","OGTSL","OLDAP-1.1","OLDAP-1.2","OLDAP-1.3","OLDAP-1.4","OLDAP-2.0","OLDAP-2.0.1","OLDAP-2.1","OLDAP-2.2","OLDAP-2.2.1","OLDAP-2.2.2","OLDAP-2.3","OLDAP-2.4","OLDAP-2.5","OLDAP-2.6","OLDAP-2.7","OLDAP-2.8","OLFL-1.3","OML","OPL-1.0","OPL-UK-3.0","OPUBL-1.0","OSET-PL-2.1","OSL-1.0","OSL-1.1","OSL-2.0","OSL-2.1","OSL-3.0","OpenPBS-2.3","OpenSSL","OpenSSL-standalone","OpenVision","PADL","PDDL-1.0","PHP-3.0","PHP-3.01","PPL","PSF-2.0","Parity-6.0.0","Parity-7.0.0","Pixar","Plexus","PolyForm-Noncommercial-1.0.0","PolyForm-Small-Business-1.0.0","PostgreSQL","Python-2.0","Python-2.0.1","QPL-1.0","QPL-1.0-INRIA-2004","Qhull","RHeCos-1.1","RPL-1.1","RPL-1.5","RPSL-1.0","RSA-MD","RSCPL","Rdisc","Ruby","Ruby-pty","SAX-PD","SAX-PD-2.0","SCEA","SGI-B-1.0","SGI-B-1.1","SGI-B-2.0","SGI-OpenGL","SGP4","SHL-0.5","SHL-0.51","SISSL","SISSL-1.2","SL","SMLNJ","SMPPL","SNIA","SPL-1.0","SSH-OpenSSH","SSH-short","SSLeay-standalone","SSPL-1.0","SWL","Saxpath","SchemeReport","Sendmail","Sendmail-8.23","SimPL-2.0","Sleepycat","Soundex","Spencer-86","Spencer-94","Spencer-99","SugarCRM-1.1.3","Sun-PPP","Sun-PPP-2000","SunPro","Symlinks","TAPR-OHL-1.0","TCL","TCP-wrappers","TGPPL-1.0","TMate","TORQUE-1.1","TOSL","TPDL","TPL-1.0","TTWL","TTYP0","TU-Berlin-1.0","TU-Berlin-2.0","TermReadKey","UCAR","UCL-1.0","UMich-Merit","UPL-1.0","URT-RLE","Ubuntu-font-1.0","Unicode-3.0","Unicode-DFS-2015","Unicode-DFS-2016","Unicode-TOU","UnixCrypt","Unlicense","VOSTROM","VSL-1.0","Vim","W3C","W3C-19980720","W3C-20150513","WTFPL","Watcom-1.0","Widget-Workshop","Wsuipa","X11","X11-distribute-modifications-variant","X11-swapped","XFree86-1.1","XSkat","Xdebug-1.03","Xerox","Xfig","Xnet","YPL-1.0","YPL-1.1","ZPL-1.1","ZPL-2.0","ZPL-2.1","Zed","Zeeff","Zend-2.0","Zimbra-1.3","Zimbra-1.4","Zlib","any-OSI","bcrypt-Solar-Designer","blessing","bzip2-1.0.6","check-cvs","checkmk","copyleft-next-0.3.0","copyleft-next-0.3.1","curl","cve-tou","diffmark","dtoa","dvipdfm","eGenix","etalab-2.0","fwlw","gSOAP-1.3b","gnuplot","gtkbook","hdparm","iMatix","libpng-2.0","libselinux-1.0","libtiff","libutil-David-Nugent","lsof","magaz","mailprio","metamail","mpi-permissive","mpich2","mplus","pkgconf","pnmstitch","psfrag","psutils","python-ldap","radvd","snprintf","softSurfer","ssh-keyscan","swrule","threeparttable","ulem","w3m","xinetd","xkeyboard-config-Zinoviev","xlock","xpp","xzoom","zlib-acknowledgement"]});var $i=p((n1,Iu)=>{Iu.exports=["AGPL-1.0","AGPL-3.0","BSD-2-Clause-FreeBSD","BSD-2-Clause-NetBSD","GFDL-1.1","GFDL-1.2","GFDL-1.3","GPL-1.0","GPL-2.0","GPL-2.0-with-GCC-exception","GPL-2.0-with-autoconf-exception","GPL-2.0-with-bison-exception","GPL-2.0-with-classpath-exception","GPL-2.0-with-font-exception","GPL-3.0","GPL-3.0-with-GCC-exception","GPL-3.0-with-autoconf-exception","LGPL-2.0","LGPL-2.1","LGPL-3.0","Net-SNMP","Nunit","StandardML-NJ","bzip2-1.0.5","eCos-2.0","wxWindows"]});var Wi=p((i1,Au)=>{Au.exports=["389-exception","Asterisk-exception","Autoconf-exception-2.0","Autoconf-exception-3.0","Autoconf-exception-generic","Autoconf-exception-generic-3.0","Autoconf-exception-macro","Bison-exception-1.24","Bison-exception-2.2","Bootloader-exception","Classpath-exception-2.0","CLISP-exception-2.0","cryptsetup-OpenSSL-exception","DigiRule-FOSS-exception","eCos-exception-2.0","Fawkes-Runtime-exception","FLTK-exception","fmt-exception","Font-exception-2.0","freertos-exception-2.0","GCC-exception-2.0","GCC-exception-2.0-note","GCC-exception-3.1","Gmsh-exception","GNAT-exception","GNOME-examples-exception","GNU-compiler-exception","gnu-javamail-exception","GPL-3.0-interface-exception","GPL-3.0-linking-exception","GPL-3.0-linking-source-exception","GPL-CC-1.0","GStreamer-exception-2005","GStreamer-exception-2008","i2p-gpl-java-exception","KiCad-libraries-exception","LGPL-3.0-linking-exception","libpri-OpenH323-exception","Libtool-exception","Linux-syscall-note","LLGPL","LLVM-exception","LZMA-exception","mif-exception","OCaml-LGPL-linking-exception","OCCT-exception-1.0","OpenJDK-assembly-exception-1.0","openvpn-openssl-exception","PS-or-PDF-font-exception-20170817","QPL-1.0-INRIA-2004-exception","Qt-GPL-exception-1.0","Qt-LGPL-exception-1.1","Qwt-exception-1.0","SANE-exception","SHL-2.0","SHL-2.1","stunnel-exception","SWI-exception","Swift-exception","Texinfo-exception","u-boot-exception-2.0","UBDL-exception","Universal-FOSS-exception-1.0","vsftpd-openssl-exception","WxWindows-exception-3.1","x11vnc-openssl-exception"]});var Yi=p((a1,Hi)=>{"use strict";var Ru=[].concat(Lt()).concat($i()),ku=Wi();Hi.exports=function(e){var r=0;function t(){return r<e.length}function n(m){if(m instanceof RegExp){var g=e.slice(r),L=g.match(m);if(L)return r+=L[0].length,L[0]}else if(e.indexOf(m,r)===r)return r+=m.length,m}function i(){n(/[ ]*/)}function a(){for(var m,g=["WITH","AND","OR","(",")",":","+"],L=0;L<g.length&&(m=n(g[L]),!m);L++);if(m==="+"&&r>1&&e[r-2]===" ")throw new Error("Space before `+`");return m&&{type:"OPERATOR",string:m}}function s(){return n(/[A-Za-z0-9-.]+/)}function o(){var m=s();if(!m)throw new Error("Expected idstring at offset "+r);return m}function u(){if(n("DocumentRef-")){var m=o();return{type:"DOCUMENTREF",string:m}}}function f(){if(n("LicenseRef-")){var m=o();return{type:"LICENSEREF",string:m}}}function l(){var m=r,g=s();if(Ru.indexOf(g)!==-1)return{type:"LICENSE",string:g};if(ku.indexOf(g)!==-1)return{type:"EXCEPTION",string:g};r=m}function h(){return a()||u()||f()||l()}for(var b=[];t()&&(i(),!!t());){var x=h();if(!x)throw new Error("Unexpected `"+e[r]+"` at offset "+r);b.push(x)}return b}});var Vi=p((s1,zi)=>{"use strict";zi.exports=function(e){var r=0;function t(){return r<e.length}function n(){return t()?e[r]:null}function i(){if(!t())throw new Error;r++}function a(g){var L=n();if(L&&L.type==="OPERATOR"&&g===L.string)return i(),L.string}function s(){if(a("WITH")){var g=n();if(g&&g.type==="EXCEPTION")return i(),g.string;throw new Error("Expected exception after `WITH`")}}function o(){var g=r,L="",I=n();if(I.type==="DOCUMENTREF"&&(i(),L+="DocumentRef-"+I.string+":",!a(":")))throw new Error("Expected `:` after `DocumentRef-...`");if(I=n(),I.type==="LICENSEREF")return i(),L+="LicenseRef-"+I.string,{license:L};r=g}function u(){var g=n();if(g&&g.type==="LICENSE"){i();var L={license:g.string};a("+")&&(L.plus=!0);var I=s();return I&&(L.exception=I),L}}function f(){var g=a("(");if(g){var L=x();if(!a(")"))throw new Error("Expected `)`");return L}}function l(){return f()||o()||u()}function h(g,L){return function I(){var re=L();if(re){if(!a(g))return re;var M=I();if(!M)throw new Error("Expected expression");return{left:re,conjunction:g.toLowerCase(),right:M}}}}var b=h("AND",l),x=h("OR",b),m=x();if(!m||t())throw new Error("Syntax error");return m}});var Ct=p((o1,Ji)=>{"use strict";var _u=Yi(),Tu=Vi();Ji.exports=function(e){return Tu(_u(e))}});var ia=p((u1,na)=>{var Fu=Ct(),Bu=Lt();function Dr(e){try{return Fu(e),!0}catch{return!1}}function ta(e,r){var t=r[0].length-e[0].length;return t!==0?t:e[0].toUpperCase().localeCompare(r[0].toUpperCase())}var Xi=[["APGL","AGPL"],["Gpl","GPL"],["GLP","GPL"],["APL","Apache"],["ISD","ISC"],["GLP","GPL"],["IST","ISC"],["Claude","Clause"],[" or later","+"],[" International",""],["GNU","GPL"],["GUN","GPL"],["+",""],["GNU GPL","GPL"],["GNU LGPL","LGPL"],["GNU/GPL","GPL"],["GNU GLP","GPL"],["GNU LESSER GENERAL PUBLIC LICENSE","LGPL"],["GNU Lesser General Public License","LGPL"],["GNU LESSER GENERAL PUBLIC LICENSE","LGPL-2.1"],["GNU Lesser General Public License","LGPL-2.1"],["LESSER GENERAL PUBLIC LICENSE","LGPL"],["Lesser General Public License","LGPL"],["LESSER GENERAL PUBLIC LICENSE","LGPL-2.1"],["Lesser General Public License","LGPL-2.1"],["GNU General Public License","GPL"],["Gnu public license","GPL"],["GNU Public License","GPL"],["GNU GENERAL PUBLIC LICENSE","GPL"],["MTI","MIT"],["Mozilla Public License","MPL"],["Universal Permissive License","UPL"],["WTH","WTF"],["WTFGPL","WTFPL"],["-License",""]].sort(ta),Gu=0,Mu=1,Ki=[function(e){return e.toUpperCase()},function(e){return e.trim()},function(e){return e.replace(/\./g,"")},function(e){return e.replace(/\s+/g,"")},function(e){return e.replace(/\s+/g,"-")},function(e){return e.replace("v","-")},function(e){return e.replace(/,?\s*(\d)/,"-$1")},function(e){return e.replace(/,?\s*(\d)/,"-$1.0")},function(e){return e.replace(/,?\s*(V\.|v\.|V|v|Version|version)\s*(\d)/,"-$2")},function(e){return e.replace(/,?\s*(V\.|v\.|V|v|Version|version)\s*(\d)/,"-$2.0")},function(e){return e[0].toUpperCase()+e.slice(1)},function(e){return e.replace("/","-")},function(e){return e.replace(/\s*V\s*(\d)/,"-$1").replace(/(\d)$/,"$1.0")},function(e){return e.indexOf("3.0")!==-1?e+"-or-later":e+"-only"},function(e){return e+"only"},function(e){return e.replace(/(\d)$/,"-$1.0")},function(e){return e.replace(/(-| )?(\d)$/,"-$2-Clause")},function(e){return e.replace(/(-| )clause(-| )(\d)/,"-$3-Clause")},function(e){return e.replace(/\b(Modified|New|Revised)(-| )?BSD((-| )License)?/i,"BSD-3-Clause")},function(e){return e.replace(/\bSimplified(-| )?BSD((-| )License)?/i,"BSD-2-Clause")},function(e){return e.replace(/\b(Free|Net)(-| )?BSD((-| )License)?/i,"BSD-2-Clause-$1BSD")},function(e){return e.replace(/\bClear(-| )?BSD((-| )License)?/i,"BSD-3-Clause-Clear")},function(e){return e.replace(/\b(Old|Original)(-| )?BSD((-| )License)?/i,"BSD-4-Clause")},function(e){return"CC-"+e},function(e){return"CC-"+e+"-4.0"},function(e){return e.replace("Attribution","BY").replace("NonCommercial","NC").replace("NoDerivatives","ND").replace(/ (\d)/,"-$1").replace(/ ?International/,"")},function(e){return"CC-"+e.replace("Attribution","BY").replace("NonCommercial","NC").replace("NoDerivatives","ND").replace(/ (\d)/,"-$1").replace(/ ?International/,"")+"-4.0"}],wt=Bu.map(function(e){var r=/^(.*)-\d+\.\d+$/.exec(e);return r?[r[0],r[1]]:[e,null]}).reduce(function(e,r){var t=r[1];return e[t]=e[t]||[],e[t].push(r[0]),e},{}),ju=Object.keys(wt).map(function(r){return[r,wt[r]]}).filter(function(r){return r[1].length===1&&r[0]!==null&&r[0]!=="APL"}).map(function(r){return[r[0],r[1][0]]});wt=void 0;var Zi=[["UNLI","Unlicense"],["WTF","WTFPL"],["2 CLAUSE","BSD-2-Clause"],["2-CLAUSE","BSD-2-Clause"],["3 CLAUSE","BSD-3-Clause"],["3-CLAUSE","BSD-3-Clause"],["AFFERO","AGPL-3.0-or-later"],["AGPL","AGPL-3.0-or-later"],["APACHE","Apache-2.0"],["ARTISTIC","Artistic-2.0"],["Affero","AGPL-3.0-or-later"],["BEER","Beerware"],["BOOST","BSL-1.0"],["BSD","BSD-2-Clause"],["CDDL","CDDL-1.1"],["ECLIPSE","EPL-1.0"],["FUCK","WTFPL"],["GNU","GPL-3.0-or-later"],["LGPL","LGPL-3.0-or-later"],["GPLV1","GPL-1.0-only"],["GPL-1","GPL-1.0-only"],["GPLV2","GPL-2.0-only"],["GPL-2","GPL-2.0-only"],["GPL","GPL-3.0-or-later"],["MIT +NO-FALSE-ATTRIBS","MITNFA"],["MIT","MIT"],["MPL","MPL-2.0"],["X11","X11"],["ZLIB","Zlib"]].concat(ju).sort(ta),Uu=0,qu=1,Qi=function(e){for(var r=0;r<Ki.length;r++){var t=Ki[r](e).trim();if(t!==e&&Dr(t))return t}return null},ea=function(e){for(var r=e.toUpperCase(),t=0;t<Zi.length;t++){var n=Zi[t];if(r.indexOf(n[Uu])>-1)return n[qu]}return null},ra=function(e,r){for(var t=0;t<Xi.length;t++){var n=Xi[t],i=n[Gu];if(e.indexOf(i)>-1){var a=e.replace(i,n[Mu]),s=r(a);if(s!==null)return s}}return null};na.exports=function(e,r){r=r||{};var t=r.upgrade===void 0?!0:!!r.upgrade;function n(o){return t?$u(o):o}var i=typeof e=="string"&&e.trim().length!==0;if(!i)throw Error("Invalid argument. Expected non-empty string.");if(e=e.trim(),Dr(e))return n(e);var a=e.replace(/\+$/,"").trim();if(Dr(a))return n(a);var s=Qi(e);return s!==null||(s=ra(e,function(o){return Dr(o)?o:Qi(o)}),s!==null)||(s=ea(e),s!==null)||(s=ra(e,ea),s!==null)?n(s):null};function $u(e){return["GPL-1.0","LGPL-1.0","AGPL-1.0","GPL-2.0","LGPL-2.0","AGPL-2.0","LGPL-2.1"].indexOf(e)!==-1?e+"-only":["GPL-1.0+","GPL-2.0+","GPL-3.0+","LGPL-2.0+","LGPL-2.1+","LGPL-3.0+","AGPL-1.0+","AGPL-3.0+"].indexOf(e)!==-1?e.replace(/\+$/,"-or-later"):["GPL-3.0","LGPL-3.0","AGPL-3.0"].indexOf(e)!==-1?e+"-or-later":e}});var ua=p((c1,oa)=>{var Wu=Ct(),Hu=ia(),aa='license should be a valid SPDX license expression (without "LicenseRef"), "UNLICENSED", or "SEE LICENSE IN <filename>"',Yu=/^SEE LICEN[CS]E IN (.+)$/;function sa(e,r){return r.slice(0,e.length)===e}function Pt(e){if(e.hasOwnProperty("license")){var r=e.license;return sa("LicenseRef",r)||sa("DocumentRef",r)}else return Pt(e.left)||Pt(e.right)}oa.exports=function(e){var r;try{r=Wu(e)}catch{var t;if(e==="UNLICENSED"||e==="UNLICENCED")return{validForOldPackages:!0,validForNewPackages:!0,unlicensed:!0};if(t=Yu.exec(e))return{validForOldPackages:!0,validForNewPackages:!0,inFile:t[1]};var n={validForOldPackages:!1,validForNewPackages:!1,warnings:[aa]};if(e.trim().length!==0){var i=Hu(e);i&&n.warnings.push('license is similar to the valid expression "'+i+'"')}return n}return Pt(r)?{validForNewPackages:!1,validForOldPackages:!1,spdx:!0,warnings:[aa]}:{validForNewPackages:!0,validForOldPackages:!0,spdx:!0}}});var St=p((l1,fa)=>{"use strict";var Ve=fa.exports={github:{protocols:["git","http","git+ssh","git+https","ssh","https"],domain:"github.com",treepath:"tree",filetemplate:"https://{auth@}raw.githubusercontent.com/{user}/{project}/{committish}/{path}",bugstemplate:"https://{domain}/{user}/{project}/issues",gittemplate:"git://{auth@}{domain}/{user}/{project}.git{#committish}",tarballtemplate:"https://codeload.{domain}/{user}/{project}/tar.gz/{committish}"},bitbucket:{protocols:["git+ssh","git+https","ssh","https"],domain:"bitbucket.org",treepath:"src",tarballtemplate:"https://{domain}/{user}/{project}/get/{committish}.tar.gz"},gitlab:{protocols:["git+ssh","git+https","ssh","https"],domain:"gitlab.com",treepath:"tree",bugstemplate:"https://{domain}/{user}/{project}/issues",httpstemplate:"git+https://{auth@}{domain}/{user}/{projectPath}.git{#committish}",tarballtemplate:"https://{domain}/{user}/{project}/repository/archive.tar.gz?ref={committish}",pathmatch:/^[/]([^/]+)[/]((?!.*(\/-\/|\/repository\/archive\.tar\.gz\?=.*|\/repository\/[^/]+\/archive.tar.gz$)).*?)(?:[.]git|[/])?$/},gist:{protocols:["git","git+ssh","git+https","ssh","https"],domain:"gist.github.com",pathmatch:/^[/](?:([^/]+)[/])?([a-z0-9]{32,})(?:[.]git)?$/,filetemplate:"https://gist.githubusercontent.com/{user}/{project}/raw{/committish}/{path}",bugstemplate:"https://{domain}/{project}",gittemplate:"git://{domain}/{project}.git{#committish}",sshtemplate:"git@{domain}:/{project}.git{#committish}",sshurltemplate:"git+ssh://git@{domain}/{project}.git{#committish}",browsetemplate:"https://{domain}/{project}{/committish}",browsefiletemplate:"https://{domain}/{project}{/committish}{#path}",docstemplate:"https://{domain}/{project}{/committish}",httpstemplate:"git+https://{domain}/{project}.git{#committish}",shortcuttemplate:"{type}:{project}{#committish}",pathtemplate:"{project}{#committish}",tarballtemplate:"https://codeload.github.com/gist/{project}/tar.gz/{committish}",hashformat:function(e){return"file-"+la(e)}}},ca={sshtemplate:"git@{domain}:{user}/{project}.git{#committish}",sshurltemplate:"git+ssh://git@{domain}/{user}/{project}.git{#committish}",browsetemplate:"https://{domain}/{user}/{project}{/tree/committish}",browsefiletemplate:"https://{domain}/{user}/{project}/{treepath}/{committish}/{path}{#fragment}",docstemplate:"https://{domain}/{user}/{project}{/tree/committish}#readme",httpstemplate:"git+https://{auth@}{domain}/{user}/{project}.git{#committish}",filetemplate:"https://{domain}/{user}/{project}/raw/{committish}/{path}",shortcuttemplate:"{type}:{user}/{project}{#committish}",pathtemplate:"{user}/{project}{#committish}",pathmatch:/^[/]([^/]+)[/]([^/]+?)(?:[.]git|[/])?$/,hashformat:la};Object.keys(Ve).forEach(function(e){Object.keys(ca).forEach(function(r){Ve[e][r]||(Ve[e][r]=ca[r])}),Ve[e].protocols_re=RegExp("^("+Ve[e].protocols.map(function(r){return r.replace(/([\\+*{}()[\]$^|])/g,"\\$1")}).join("|")+"):$")});function la(e){return e.toLowerCase().replace(/^\W+|\/|\W+$/g,"").replace(/\W+/g,"-")}});var ha=p((f1,da)=>{"use strict";var pa=St(),_e=Object.assign||function(r,t){if(t===null||typeof t!="object")return r;for(var n=Object.keys(t),i=n.length;i--;)r[n[i]]=t[n[i]];return r};da.exports=q;function q(e,r,t,n,i,a,s){var o=this;o.type=e,Object.keys(pa[e]).forEach(function(u){o[u]=pa[e][u]}),o.user=r,o.auth=t,o.project=n,o.committish=i,o.default=a,o.opts=s||{}}q.prototype.hash=function(){return this.committish?"#"+this.committish:""};q.prototype._fill=function(e,r){if(e){var t=_e({},r);t.path=t.path?t.path.replace(/^[/]+/g,""):"",r=_e(_e({},this.opts),r);var n=this;Object.keys(this).forEach(function(l){n[l]!=null&&t[l]==null&&(t[l]=n[l])});var i=t.auth,a=t.committish,s=t.fragment,o=t.path,u=t.project;Object.keys(t).forEach(function(l){var h=t[l];(l==="path"||l==="project")&&typeof h=="string"?t[l]=h.split("/").map(function(b){return encodeURIComponent(b)}).join("/"):t[l]=encodeURIComponent(h)}),t["auth@"]=i?i+"@":"",t["#fragment"]=s?"#"+this.hashformat(s):"",t.fragment=t.fragment?t.fragment:"",t["#path"]=o?"#"+this.hashformat(o):"",t["/path"]=t.path?"/"+t.path:"",t.projectPath=u.split("/").map(encodeURIComponent).join("/"),r.noCommittish?(t["#committish"]="",t["/tree/committish"]="",t["/committish"]="",t.committish=""):(t["#committish"]=a?"#"+a:"",t["/tree/committish"]=t.committish?"/"+t.treepath+"/"+t.committish:"",t["/committish"]=t.committish?"/"+t.committish:"",t.committish=t.committish||"master");var f=e;return Object.keys(t).forEach(function(l){f=f.replace(new RegExp("[{]"+l+"[}]","g"),t[l])}),r.noGitPlus?f.replace(/^git[+]/,""):f}};q.prototype.ssh=function(e){return this._fill(this.sshtemplate,e)};q.prototype.sshurl=function(e){return this._fill(this.sshurltemplate,e)};q.prototype.browse=function(e,r,t){return typeof e=="string"?(typeof r!="string"&&(t=r,r=null),this._fill(this.browsefiletemplate,_e({fragment:r,path:e},t))):this._fill(this.browsetemplate,e)};q.prototype.docs=function(e){return this._fill(this.docstemplate,e)};q.prototype.bugs=function(e){return this._fill(this.bugstemplate,e)};q.prototype.https=function(e){return this._fill(this.httpstemplate,e)};q.prototype.git=function(e){return this._fill(this.gittemplate,e)};q.prototype.shortcut=function(e){return this._fill(this.shortcuttemplate,e)};q.prototype.path=function(e){return this._fill(this.pathtemplate,e)};q.prototype.tarball=function(e){var r=_e({},e,{noCommittish:!1});return this._fill(this.tarballtemplate,r)};q.prototype.file=function(e,r){return this._fill(this.filetemplate,_e({path:e},r))};q.prototype.getDefaultRepresentation=function(){return this.default};q.prototype.toString=function(e){return this.default&&typeof this[this.default]=="function"?this[this.default](e):this.sshurl(e)}});var va=p((p1,Et)=>{"use strict";var Nr=require("url"),ma=St(),zu=Et.exports=ha(),Vu={"git+ssh:":"sshurl","git+https:":"https","ssh:":"sshurl","git:":"git"};function Ju(e){return Vu[e]||e.slice(0,-1)}var Xu={"git:":!0,"https:":!0,"git+https:":!0,"http:":!0,"git+http:":!0},xt={};Et.exports.fromUrl=function(e,r){if(typeof e=="string"){var t=e+JSON.stringify(r||{});return t in xt||(xt[t]=Ku(e,r)),xt[t]}};function Ku(e,r){if(!(e==null||e==="")){var t=Qu(Zu(e)?"github:"+e:e),n=ec(t),i=t.match(/^([^:]+):(?:[^@]+@)?(?:([^/]*)\/)?([^#]+)/),a=Object.keys(ma).map(function(s){try{var o=ma[s],u=null;n.auth&&Xu[n.protocol]&&(u=n.auth);var f=n.hash?decodeURIComponent(n.hash.substr(1)):null,l=null,h=null,b=null;if(i&&i[1]===s)l=i[2]&&decodeURIComponent(i[2]),h=decodeURIComponent(i[3].replace(/\.git$/,"")),b="shortcut";else{if(n.host&&n.host!==o.domain&&n.host.replace(/^www[.]/,"")!==o.domain||!o.protocols_re.test(n.protocol)||!n.path)return;var x=o.pathmatch,m=n.path.match(x);if(!m)return;m[1]!==null&&m[1]!==void 0&&(l=decodeURIComponent(m[1].replace(/^:/,""))),h=decodeURIComponent(m[2]),b=Ju(n.protocol)}return new zu(s,l,u,h,f,b,r)}catch(g){if(!(g instanceof URIError))throw g}}).filter(function(s){return s});if(a.length===1)return a[0]}}function Zu(e){return/^[^:@%/\s.-][^:@%/\s]*[/][^:@\s/%]+(?:#.*)?$/.test(e)}function Qu(e){var r=Nr.parse(e);return r.protocol==="gist:"&&r.host&&!r.path?r.protocol+"/"+r.host:e}function ec(e){var r=e.match(/^([^@]+)@([^:/]+):[/]?((?:[^/]+[/])?[^/]+?)(?:[.]git)?(#.*)?$/);if(!r){var t=Nr.parse(e);if(t.auth&&typeof Nr.URL=="function"){var n=e.match(/[^@]+@[^:/]+/);if(n){var i=new Nr.URL(n[0]);t.auth=i.username||"",i.password&&(t.auth+=":"+i.password)}}return t}return{protocol:"git+ssh:",slashes:!0,auth:r[1],host:r[2],port:null,hostname:r[2],hash:r[4],search:null,query:null,pathname:"/"+r[3],path:"/"+r[3],href:"git+ssh://"+r[1]+"@"+r[2]+"/"+r[3]+(r[4]||"")}}});var Ot=p((d1,ga)=>{"use strict";var rc=require("os");ga.exports=rc.homedir||function(){var r=process.env.HOME,t=process.env.LOGNAME||process.env.USER||process.env.LNAME||process.env.USERNAME;return process.platform==="win32"?process.env.USERPROFILE||process.env.HOMEDRIVE+process.env.HOMEPATH||r||null:process.platform==="darwin"?r||(t?"/Users/"+t:null):process.platform==="linux"?r||(process.getuid()===0?"/root":t?"/home/"+t:null):r||null}});var Dt=p((h1,ba)=>{ba.exports=function(){var e=Error.prepareStackTrace;Error.prepareStackTrace=function(t,n){return n};var r=new Error().stack;return Error.prepareStackTrace=e,r[2].getFileName()}});var ya=p((m1,Je)=>{"use strict";var tc=process.platform==="win32",nc=/^(((?:[a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?[\\\/]?)(?:[^\\\/]*[\\\/])*)((\.{1,2}|[^\\\/]+?|)(\.[^.\/\\]*|))[\\\/]*$/,Nt={};function ic(e){return nc.exec(e).slice(1)}Nt.parse=function(e){if(typeof e!="string")throw new TypeError("Parameter 'pathString' must be a string, not "+typeof e);var r=ic(e);if(!r||r.length!==5)throw new TypeError("Invalid path '"+e+"'");return{root:r[1],dir:r[0]===r[1]?r[0]:r[0].slice(0,-1),base:r[2],ext:r[4],name:r[3]}};var ac=/^((\/?)(?:[^\/]*\/)*)((\.{1,2}|[^\/]+?|)(\.[^.\/]*|))[\/]*$/,It={};function sc(e){return ac.exec(e).slice(1)}It.parse=function(e){if(typeof e!="string")throw new TypeError("Parameter 'pathString' must be a string, not "+typeof e);var r=sc(e);if(!r||r.length!==5)throw new TypeError("Invalid path '"+e+"'");return{root:r[1],dir:r[0].slice(0,-1),base:r[2],ext:r[4],name:r[3]}};tc?Je.exports=Nt.parse:Je.exports=It.parse;Je.exports.posix=It.parse;Je.exports.win32=Nt.parse});var At=p((v1,Pa)=>{var wa=require("path"),La=wa.parse||ya(),Ca=function(r,t){var n="/";/^([A-Za-z]:)/.test(r)?n="":/^\\\\/.test(r)&&(n="\\\\");for(var i=[r],a=La(r);a.dir!==i[i.length-1];)i.push(a.dir),a=La(a.dir);return i.reduce(function(s,o){return s.concat(t.map(function(u){return wa.resolve(n,o,u)}))},[])};Pa.exports=function(r,t,n){var i=t&&t.moduleDirectory?[].concat(t.moduleDirectory):["node_modules"];if(t&&typeof t.paths=="function")return t.paths(n,r,function(){return Ca(r,i)},t);var a=Ca(r,i);return t&&t.paths?a.concat(t.paths):a}});var Rt=p((g1,Sa)=>{Sa.exports=function(e,r){return r||{}}});var Oa=p((b1,Ea)=>{"use strict";var oc="Function.prototype.bind called on incompatible ",uc=Object.prototype.toString,cc=Math.max,lc="[object Function]",xa=function(r,t){for(var n=[],i=0;i<r.length;i+=1)n[i]=r[i];for(var a=0;a<t.length;a+=1)n[a+r.length]=t[a];return n},fc=function(r,t){for(var n=[],i=t||0,a=0;i<r.length;i+=1,a+=1)n[a]=r[i];return n},pc=function(e,r){for(var t="",n=0;n<e.length;n+=1)t+=e[n],n+1<e.length&&(t+=r);return t};Ea.exports=function(r){var t=this;if(typeof t!="function"||uc.apply(t)!==lc)throw new TypeError(oc+t);for(var n=fc(arguments,1),i,a=function(){if(this instanceof i){var l=t.apply(this,xa(n,arguments));return Object(l)===l?l:this}return t.apply(r,xa(n,arguments))},s=cc(0,t.length-n.length),o=[],u=0;u<s;u++)o[u]="$"+u;if(i=Function("binder","return function ("+pc(o,",")+"){ return binder.apply(this,arguments); }")(a),t.prototype){var f=function(){};f.prototype=t.prototype,i.prototype=new f,f.prototype=null}return i}});var Na=p((y1,Da)=>{"use strict";var dc=Oa();Da.exports=Function.prototype.bind||dc});var Aa=p((L1,Ia)=>{"use strict";var hc=Function.prototype.call,mc=Object.prototype.hasOwnProperty,vc=Na();Ia.exports=vc.call(hc,mc)});var Ra=p((C1,gc)=>{gc.exports={assert:!0,"node:assert":[">= 14.18 && < 15",">= 16"],"assert/strict":">= 15","node:assert/strict":">= 16",async_hooks:">= 8","node:async_hooks":[">= 14.18 && < 15",">= 16"],buffer_ieee754:">= 0.5 && < 0.9.7",buffer:!0,"node:buffer":[">= 14.18 && < 15",">= 16"],child_process:!0,"node:child_process":[">= 14.18 && < 15",">= 16"],cluster:">= 0.5","node:cluster":[">= 14.18 && < 15",">= 16"],console:!0,"node:console":[">= 14.18 && < 15",">= 16"],constants:!0,"node:constants":[">= 14.18 && < 15",">= 16"],crypto:!0,"node:crypto":[">= 14.18 && < 15",">= 16"],_debug_agent:">= 1 && < 8",_debugger:"< 8",dgram:!0,"node:dgram":[">= 14.18 && < 15",">= 16"],diagnostics_channel:[">= 14.17 && < 15",">= 15.1"],"node:diagnostics_channel":[">= 14.18 && < 15",">= 16"],dns:!0,"node:dns":[">= 14.18 && < 15",">= 16"],"dns/promises":">= 15","node:dns/promises":">= 16",domain:">= 0.7.12","node:domain":[">= 14.18 && < 15",">= 16"],events:!0,"node:events":[">= 14.18 && < 15",">= 16"],freelist:"< 6",fs:!0,"node:fs":[">= 14.18 && < 15",">= 16"],"fs/promises":[">= 10 && < 10.1",">= 14"],"node:fs/promises":[">= 14.18 && < 15",">= 16"],_http_agent:">= 0.11.1","node:_http_agent":[">= 14.18 && < 15",">= 16"],_http_client:">= 0.11.1","node:_http_client":[">= 14.18 && < 15",">= 16"],_http_common:">= 0.11.1","node:_http_common":[">= 14.18 && < 15",">= 16"],_http_incoming:">= 0.11.1","node:_http_incoming":[">= 14.18 && < 15",">= 16"],_http_outgoing:">= 0.11.1","node:_http_outgoing":[">= 14.18 && < 15",">= 16"],_http_server:">= 0.11.1","node:_http_server":[">= 14.18 && < 15",">= 16"],http:!0,"node:http":[">= 14.18 && < 15",">= 16"],http2:">= 8.8","node:http2":[">= 14.18 && < 15",">= 16"],https:!0,"node:https":[">= 14.18 && < 15",">= 16"],inspector:">= 8","node:inspector":[">= 14.18 && < 15",">= 16"],"inspector/promises":[">= 19"],"node:inspector/promises":[">= 19"],_linklist:"< 8",module:!0,"node:module":[">= 14.18 && < 15",">= 16"],net:!0,"node:net":[">= 14.18 && < 15",">= 16"],"node-inspect/lib/_inspect":">= 7.6 && < 12","node-inspect/lib/internal/inspect_client":">= 7.6 && < 12","node-inspect/lib/internal/inspect_repl":">= 7.6 && < 12",os:!0,"node:os":[">= 14.18 && < 15",">= 16"],path:!0,"node:path":[">= 14.18 && < 15",">= 16"],"path/posix":">= 15.3","node:path/posix":">= 16","path/win32":">= 15.3","node:path/win32":">= 16",perf_hooks:">= 8.5","node:perf_hooks":[">= 14.18 && < 15",">= 16"],process:">= 1","node:process":[">= 14.18 && < 15",">= 16"],punycode:">= 0.5","node:punycode":[">= 14.18 && < 15",">= 16"],querystring:!0,"node:querystring":[">= 14.18 && < 15",">= 16"],readline:!0,"node:readline":[">= 14.18 && < 15",">= 16"],"readline/promises":">= 17","node:readline/promises":">= 17",repl:!0,"node:repl":[">= 14.18 && < 15",">= 16"],"node:sea":[">= 20.12 && < 21",">= 21.7"],smalloc:">= 0.11.5 && < 3",_stream_duplex:">= 0.9.4","node:_stream_duplex":[">= 14.18 && < 15",">= 16"],_stream_transform:">= 0.9.4","node:_stream_transform":[">= 14.18 && < 15",">= 16"],_stream_wrap:">= 1.4.1","node:_stream_wrap":[">= 14.18 && < 15",">= 16"],_stream_passthrough:">= 0.9.4","node:_stream_passthrough":[">= 14.18 && < 15",">= 16"],_stream_readable:">= 0.9.4","node:_stream_readable":[">= 14.18 && < 15",">= 16"],_stream_writable:">= 0.9.4","node:_stream_writable":[">= 14.18 && < 15",">= 16"],stream:!0,"node:stream":[">= 14.18 && < 15",">= 16"],"stream/consumers":">= 16.7","node:stream/consumers":">= 16.7","stream/promises":">= 15","node:stream/promises":">= 16","stream/web":">= 16.5","node:stream/web":">= 16.5",string_decoder:!0,"node:string_decoder":[">= 14.18 && < 15",">= 16"],sys:[">= 0.4 && < 0.7",">= 0.8"],"node:sys":[">= 14.18 && < 15",">= 16"],"test/reporters":">= 19.9 && < 20.2","node:test/reporters":[">= 18.17 && < 19",">= 19.9",">= 20"],"test/mock_loader":">= 22.3 && < 22.7","node:test/mock_loader":">= 22.3 && < 22.7","node:test":[">= 16.17 && < 17",">= 18"],timers:!0,"node:timers":[">= 14.18 && < 15",">= 16"],"timers/promises":">= 15","node:timers/promises":">= 16",_tls_common:">= 0.11.13","node:_tls_common":[">= 14.18 && < 15",">= 16"],_tls_legacy:">= 0.11.3 && < 10",_tls_wrap:">= 0.11.3","node:_tls_wrap":[">= 14.18 && < 15",">= 16"],tls:!0,"node:tls":[">= 14.18 && < 15",">= 16"],trace_events:">= 10","node:trace_events":[">= 14.18 && < 15",">= 16"],tty:!0,"node:tty":[">= 14.18 && < 15",">= 16"],url:!0,"node:url":[">= 14.18 && < 15",">= 16"],util:!0,"node:util":[">= 14.18 && < 15",">= 16"],"util/types":">= 15.3","node:util/types":">= 16","v8/tools/arguments":">= 10 && < 12","v8/tools/codemap":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/consarray":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/csvparser":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/logreader":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/profile_view":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/splaytree":[">= 4.4 && < 5",">= 5.2 && < 12"],v8:">= 1","node:v8":[">= 14.18 && < 15",">= 16"],vm:!0,"node:vm":[">= 14.18 && < 15",">= 16"],wasi:[">= 13.4 && < 13.5",">= 18.17 && < 19",">= 20"],"node:wasi":[">= 18.17 && < 19",">= 20"],worker_threads:">= 11.7","node:worker_threads":[">= 14.18 && < 15",">= 16"],zlib:">= 0.5","node:zlib":[">= 14.18 && < 15",">= 16"]}});var Xe=p((w1,Ta)=>{"use strict";var bc=Aa();function yc(e,r){for(var t=e.split("."),n=r.split(" "),i=n.length>1?n[0]:"=",a=(n.length>1?n[1]:n[0]).split("."),s=0;s<3;++s){var o=parseInt(t[s]||0,10),u=parseInt(a[s]||0,10);if(o!==u)return i==="<"?o<u:i===">="?o>=u:!1}return i===">="}function ka(e,r){var t=r.split(/ ?&& ?/);if(t.length===0)return!1;for(var n=0;n<t.length;++n)if(!yc(e,t[n]))return!1;return!0}function Lc(e,r){if(typeof r=="boolean")return r;var t=typeof e>"u"?process.versions&&process.versions.node:e;if(typeof t!="string")throw new TypeError(typeof e>"u"?"Unable to determine current node version":"If provided, a valid node version is required");if(r&&typeof r=="object"){for(var n=0;n<r.length;++n)if(ka(t,r[n]))return!0;return!1}return ka(t,r)}var _a=Ra();Ta.exports=function(r,t){return bc(_a,r)&&Lc(t,_a[r])}});var Ga=p((P1,Ba)=>{var be=require("fs"),Cc=Ot(),G=require("path"),wc=Dt(),Pc=At(),Sc=Rt(),xc=Xe(),Ec=process.platform!=="win32"&&be.realpath&&typeof be.realpath.native=="function"?be.realpath.native:be.realpath,Fa=Cc(),Oc=function(){return[G.join(Fa,".node_modules"),G.join(Fa,".node_libraries")]},Dc=function(r,t){be.stat(r,function(n,i){return n?n.code==="ENOENT"||n.code==="ENOTDIR"?t(null,!1):t(n):t(null,i.isFile()||i.isFIFO())})},Nc=function(r,t){be.stat(r,function(n,i){return n?n.code==="ENOENT"||n.code==="ENOTDIR"?t(null,!1):t(n):t(null,i.isDirectory())})},Ic=function(r,t){Ec(r,function(n,i){n&&n.code!=="ENOENT"?t(n):t(null,n?r:i)})},Ke=function(r,t,n,i){n&&n.preserveSymlinks===!1?r(t,i):i(null,t)},Ac=function(r,t,n){r(t,function(i,a){if(i)n(i);else try{var s=JSON.parse(a);n(null,s)}catch{n(null)}})},Rc=function(r,t,n){for(var i=Pc(t,n,r),a=0;a<i.length;a++)i[a]=G.join(i[a],r);return i};Ba.exports=function(r,t,n){var i=n,a=t;if(typeof t=="function"&&(i=a,a={}),typeof r!="string"){var s=new TypeError("Path must be a string.");return process.nextTick(function(){i(s)})}a=Sc(r,a);var o=a.isFile||Dc,u=a.isDirectory||Nc,f=a.readFile||be.readFile,l=a.realpath||Ic,h=a.readPackage||Ac;if(a.readFile&&a.readPackage){var b=new TypeError("`readFile` and `readPackage` are mutually exclusive.");return process.nextTick(function(){i(b)})}var x=a.packageIterator,m=a.extensions||[".js"],g=a.includeCoreModules!==!1,L=a.basedir||G.dirname(wc()),I=a.filename||L;a.paths=a.paths||Oc();var re=G.resolve(L);Ke(l,re,a,function(v,C){v?i(v):te(C)});var M;function te(v){if(/^(?:\.\.?(?:\/|$)|\/|([A-Za-z]:)?[/\\])/.test(r))M=G.resolve(v,r),(r==="."||r===".."||r.slice(-1)==="/")&&(M+="/"),/\/$/.test(r)&&M===v?R(M,a.package,oe):z(M,a.package,oe);else{if(g&&xc(r))return i(null,r);K(r,v,function(C,T,S){if(C)i(C);else{if(T)return Ke(l,T,a,function(V,F){V?i(V):i(null,F,S)});var k=new Error("Cannot find module '"+r+"' from '"+I+"'");k.code="MODULE_NOT_FOUND",i(k)}})}}function oe(v,C,T){v?i(v):C?i(null,C,T):R(M,function(S,k,V){if(S)i(S);else if(k)Ke(l,k,a,function(_,Y){_?i(_):i(null,Y,V)});else{var F=new Error("Cannot find module '"+r+"' from '"+I+"'");F.code="MODULE_NOT_FOUND",i(F)}})}function z(v,C,T){var S=C,k=T;typeof S=="function"&&(k=S,S=void 0);var V=[""].concat(m);F(V,v,S);function F(_,Y,ne){if(_.length===0)return k(null,void 0,ne);var pe=Y+_[0],Z=ne;Z?j(null,Z):E(G.dirname(pe),j);function j(ue,Ce,ie){if(Z=Ce,ue)return k(ue);if(ie&&Z&&a.pathFilter){var er=G.relative(ie,pe),rr=er.slice(0,er.length-_[0].length),Fe=a.pathFilter(Z,Y,rr);if(Fe)return F([""].concat(m.slice()),G.resolve(ie,Fe),Z)}o(pe,Qe)}function Qe(ue,Ce){if(ue)return k(ue);if(Ce)return k(null,pe,Z);F(_.slice(1),Y,Z)}}}function E(v,C){if(v===""||v==="/"||process.platform==="win32"&&/^\w:[/\\]*$/.test(v)||/[/\\]node_modules[/\\]*$/.test(v))return C(null);Ke(l,v,a,function(T,S){if(T)return E(G.dirname(v),C);var k=G.join(S,"package.json");o(k,function(V,F){if(!F)return E(G.dirname(v),C);h(f,k,function(_,Y){_&&C(_);var ne=Y;ne&&a.packageFilter&&(ne=a.packageFilter(ne,k)),C(null,ne,v)})})})}function R(v,C,T){var S=T,k=C;typeof k=="function"&&(S=k,k=a.package),Ke(l,v,a,function(V,F){if(V)return S(V);var _=G.join(F,"package.json");o(_,function(Y,ne){if(Y)return S(Y);if(!ne)return z(G.join(v,"index"),k,S);h(f,_,function(pe,Z){if(pe)return S(pe);var j=Z;if(j&&a.packageFilter&&(j=a.packageFilter(j,_)),j&&j.main){if(typeof j.main!="string"){var Qe=new TypeError("package \u201C"+j.name+"\u201D `main` must be a string");return Qe.code="INVALID_PACKAGE_MAIN",S(Qe)}(j.main==="."||j.main==="./")&&(j.main="index"),z(G.resolve(v,j.main),j,function(ue,Ce,ie){if(ue)return S(ue);if(Ce)return S(null,Ce,ie);if(!ie)return z(G.join(v,"index"),ie,S);var er=G.resolve(v,ie.main);R(er,ie,function(rr,Fe,Ht){if(rr)return S(rr);if(Fe)return S(null,Fe,Ht);z(G.join(v,"index"),Ht,S)})});return}z(G.join(v,"/index"),j,S)})})})}function D(v,C){if(C.length===0)return v(null,void 0);var T=C[0];u(G.dirname(T),S);function S(F,_){if(F)return v(F);if(!_)return D(v,C.slice(1));z(T,a.package,k)}function k(F,_,Y){if(F)return v(F);if(_)return v(null,_,Y);R(T,a.package,V)}function V(F,_,Y){if(F)return v(F);if(_)return v(null,_,Y);D(v,C.slice(1))}}function K(v,C,T){var S=function(){return Rc(v,C,a)};D(T,x?x(v,C,S,a):S())}}});var Ma=p((S1,kc)=>{kc.exports={assert:!0,"node:assert":[">= 14.18 && < 15",">= 16"],"assert/strict":">= 15","node:assert/strict":">= 16",async_hooks:">= 8","node:async_hooks":[">= 14.18 && < 15",">= 16"],buffer_ieee754:">= 0.5 && < 0.9.7",buffer:!0,"node:buffer":[">= 14.18 && < 15",">= 16"],child_process:!0,"node:child_process":[">= 14.18 && < 15",">= 16"],cluster:">= 0.5","node:cluster":[">= 14.18 && < 15",">= 16"],console:!0,"node:console":[">= 14.18 && < 15",">= 16"],constants:!0,"node:constants":[">= 14.18 && < 15",">= 16"],crypto:!0,"node:crypto":[">= 14.18 && < 15",">= 16"],_debug_agent:">= 1 && < 8",_debugger:"< 8",dgram:!0,"node:dgram":[">= 14.18 && < 15",">= 16"],diagnostics_channel:[">= 14.17 && < 15",">= 15.1"],"node:diagnostics_channel":[">= 14.18 && < 15",">= 16"],dns:!0,"node:dns":[">= 14.18 && < 15",">= 16"],"dns/promises":">= 15","node:dns/promises":">= 16",domain:">= 0.7.12","node:domain":[">= 14.18 && < 15",">= 16"],events:!0,"node:events":[">= 14.18 && < 15",">= 16"],freelist:"< 6",fs:!0,"node:fs":[">= 14.18 && < 15",">= 16"],"fs/promises":[">= 10 && < 10.1",">= 14"],"node:fs/promises":[">= 14.18 && < 15",">= 16"],_http_agent:">= 0.11.1","node:_http_agent":[">= 14.18 && < 15",">= 16"],_http_client:">= 0.11.1","node:_http_client":[">= 14.18 && < 15",">= 16"],_http_common:">= 0.11.1","node:_http_common":[">= 14.18 && < 15",">= 16"],_http_incoming:">= 0.11.1","node:_http_incoming":[">= 14.18 && < 15",">= 16"],_http_outgoing:">= 0.11.1","node:_http_outgoing":[">= 14.18 && < 15",">= 16"],_http_server:">= 0.11.1","node:_http_server":[">= 14.18 && < 15",">= 16"],http:!0,"node:http":[">= 14.18 && < 15",">= 16"],http2:">= 8.8","node:http2":[">= 14.18 && < 15",">= 16"],https:!0,"node:https":[">= 14.18 && < 15",">= 16"],inspector:">= 8","node:inspector":[">= 14.18 && < 15",">= 16"],"inspector/promises":[">= 19"],"node:inspector/promises":[">= 19"],_linklist:"< 8",module:!0,"node:module":[">= 14.18 && < 15",">= 16"],net:!0,"node:net":[">= 14.18 && < 15",">= 16"],"node-inspect/lib/_inspect":">= 7.6 && < 12","node-inspect/lib/internal/inspect_client":">= 7.6 && < 12","node-inspect/lib/internal/inspect_repl":">= 7.6 && < 12",os:!0,"node:os":[">= 14.18 && < 15",">= 16"],path:!0,"node:path":[">= 14.18 && < 15",">= 16"],"path/posix":">= 15.3","node:path/posix":">= 16","path/win32":">= 15.3","node:path/win32":">= 16",perf_hooks:">= 8.5","node:perf_hooks":[">= 14.18 && < 15",">= 16"],process:">= 1","node:process":[">= 14.18 && < 15",">= 16"],punycode:">= 0.5","node:punycode":[">= 14.18 && < 15",">= 16"],querystring:!0,"node:querystring":[">= 14.18 && < 15",">= 16"],readline:!0,"node:readline":[">= 14.18 && < 15",">= 16"],"readline/promises":">= 17","node:readline/promises":">= 17",repl:!0,"node:repl":[">= 14.18 && < 15",">= 16"],smalloc:">= 0.11.5 && < 3",_stream_duplex:">= 0.9.4","node:_stream_duplex":[">= 14.18 && < 15",">= 16"],_stream_transform:">= 0.9.4","node:_stream_transform":[">= 14.18 && < 15",">= 16"],_stream_wrap:">= 1.4.1","node:_stream_wrap":[">= 14.18 && < 15",">= 16"],_stream_passthrough:">= 0.9.4","node:_stream_passthrough":[">= 14.18 && < 15",">= 16"],_stream_readable:">= 0.9.4","node:_stream_readable":[">= 14.18 && < 15",">= 16"],_stream_writable:">= 0.9.4","node:_stream_writable":[">= 14.18 && < 15",">= 16"],stream:!0,"node:stream":[">= 14.18 && < 15",">= 16"],"stream/consumers":">= 16.7","node:stream/consumers":">= 16.7","stream/promises":">= 15","node:stream/promises":">= 16","stream/web":">= 16.5","node:stream/web":">= 16.5",string_decoder:!0,"node:string_decoder":[">= 14.18 && < 15",">= 16"],sys:[">= 0.4 && < 0.7",">= 0.8"],"node:sys":[">= 14.18 && < 15",">= 16"],"test/reporters":">= 19.9 && < 20.2","node:test/reporters":[">= 18.17 && < 19",">= 19.9",">= 20"],"node:test":[">= 16.17 && < 17",">= 18"],timers:!0,"node:timers":[">= 14.18 && < 15",">= 16"],"timers/promises":">= 15","node:timers/promises":">= 16",_tls_common:">= 0.11.13","node:_tls_common":[">= 14.18 && < 15",">= 16"],_tls_legacy:">= 0.11.3 && < 10",_tls_wrap:">= 0.11.3","node:_tls_wrap":[">= 14.18 && < 15",">= 16"],tls:!0,"node:tls":[">= 14.18 && < 15",">= 16"],trace_events:">= 10","node:trace_events":[">= 14.18 && < 15",">= 16"],tty:!0,"node:tty":[">= 14.18 && < 15",">= 16"],url:!0,"node:url":[">= 14.18 && < 15",">= 16"],util:!0,"node:util":[">= 14.18 && < 15",">= 16"],"util/types":">= 15.3","node:util/types":">= 16","v8/tools/arguments":">= 10 && < 12","v8/tools/codemap":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/consarray":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/csvparser":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/logreader":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/profile_view":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/splaytree":[">= 4.4 && < 5",">= 5.2 && < 12"],v8:">= 1","node:v8":[">= 14.18 && < 15",">= 16"],vm:!0,"node:vm":[">= 14.18 && < 15",">= 16"],wasi:[">= 13.4 && < 13.5",">= 18.17 && < 19",">= 20"],"node:wasi":[">= 18.17 && < 19",">= 20"],worker_threads:">= 11.7","node:worker_threads":[">= 14.18 && < 15",">= 16"],zlib:">= 0.5","node:zlib":[">= 14.18 && < 15",">= 16"]}});var $a=p((x1,qa)=>{"use strict";var _c=Xe(),ja=Ma(),Ua={};for(Ir in ja)Object.prototype.hasOwnProperty.call(ja,Ir)&&(Ua[Ir]=_c(Ir));var Ir;qa.exports=Ua});var Ha=p((E1,Wa)=>{var Tc=Xe();Wa.exports=function(r){return Tc(r)}});var Va=p((O1,za)=>{var Fc=Xe(),ye=require("fs"),$=require("path"),Bc=Ot(),Gc=Dt(),Mc=At(),jc=Rt(),Uc=process.platform!=="win32"&&ye.realpathSync&&typeof ye.realpathSync.native=="function"?ye.realpathSync.native:ye.realpathSync,Ya=Bc(),qc=function(){return[$.join(Ya,".node_modules"),$.join(Ya,".node_libraries")]},$c=function(r){try{var t=ye.statSync(r,{throwIfNoEntry:!1})}catch(n){if(n&&(n.code==="ENOENT"||n.code==="ENOTDIR"))return!1;throw n}return!!t&&(t.isFile()||t.isFIFO())},Wc=function(r){try{var t=ye.statSync(r,{throwIfNoEntry:!1})}catch(n){if(n&&(n.code==="ENOENT"||n.code==="ENOTDIR"))return!1;throw n}return!!t&&t.isDirectory()},Hc=function(r){try{return Uc(r)}catch(t){if(t.code!=="ENOENT")throw t}return r},Ze=function(r,t,n){return n&&n.preserveSymlinks===!1?r(t):t},Yc=function(r,t){var n=r(t);try{var i=JSON.parse(n);return i}catch{}},zc=function(r,t,n){for(var i=Mc(t,n,r),a=0;a<i.length;a++)i[a]=$.join(i[a],r);return i};za.exports=function(r,t){if(typeof r!="string")throw new TypeError("Path must be a string.");var n=jc(r,t),i=n.isFile||$c,a=n.readFileSync||ye.readFileSync,s=n.isDirectory||Wc,o=n.realpathSync||Hc,u=n.readPackageSync||Yc;if(n.readFileSync&&n.readPackageSync)throw new TypeError("`readFileSync` and `readPackageSync` are mutually exclusive.");var f=n.packageIterator,l=n.extensions||[".js"],h=n.includeCoreModules!==!1,b=n.basedir||$.dirname(Gc()),x=n.filename||b;n.paths=n.paths||qc();var m=Ze(o,$.resolve(b),n);if(/^(?:\.\.?(?:\/|$)|\/|([A-Za-z]:)?[/\\])/.test(r)){var g=$.resolve(m,r);(r==="."||r===".."||r.slice(-1)==="/")&&(g+="/");var L=M(g)||oe(g);if(L)return Ze(o,L,n)}else{if(h&&Fc(r))return r;var I=z(r,m);if(I)return Ze(o,I,n)}var re=new Error("Cannot find module '"+r+"' from '"+x+"'");throw re.code="MODULE_NOT_FOUND",re;function M(E){var R=te($.dirname(E));if(R&&R.dir&&R.pkg&&n.pathFilter){var D=$.relative(R.dir,E),K=n.pathFilter(R.pkg,E,D);K&&(E=$.resolve(R.dir,K))}if(i(E))return E;for(var v=0;v<l.length;v++){var C=E+l[v];if(i(C))return C}}function te(E){if(!(E===""||E==="/")&&!(process.platform==="win32"&&/^\w:[/\\]*$/.test(E))&&!/[/\\]node_modules[/\\]*$/.test(E)){var R=$.join(Ze(o,E,n),"package.json");if(!i(R))return te($.dirname(E));var D=u(a,R);return D&&n.packageFilter&&(D=n.packageFilter(D,E)),{pkg:D,dir:E}}}function oe(E){var R=$.join(Ze(o,E,n),"/package.json");if(i(R)){try{var D=u(a,R)}catch{}if(D&&n.packageFilter&&(D=n.packageFilter(D,E)),D&&D.main){if(typeof D.main!="string"){var K=new TypeError("package \u201C"+D.name+"\u201D `main` must be a string");throw K.code="INVALID_PACKAGE_MAIN",K}(D.main==="."||D.main==="./")&&(D.main="index");try{var v=M($.resolve(E,D.main));if(v)return v;var C=oe($.resolve(E,D.main));if(C)return C}catch{}}}return M($.join(E,"/index"))}function z(E,R){for(var D=function(){return zc(E,R,n)},K=f?f(E,R,D,n):D(),v=0;v<K.length;v++){var C=K[v];if(s($.dirname(C))){var T=M(C);if(T)return T;var S=oe(C);if(S)return S}}}}});var Xa=p((D1,Ja)=>{var Ar=Ga();Ar.core=$a();Ar.isCore=Ha();Ar.sync=Va();Ja.exports=Ar});var Za=p((N1,Ka)=>{Ka.exports=Vc;function Vc(e){if(e&&e!=="ERROR: No README data found!"){e=e.trim().split(`
`);for(var r=0;e[r]&&e[r].trim().match(/^(#|$)/);r++);for(var t=e.length,n=r+1;n<t&&e[n].trim();n++);return e.slice(r,n).join(" ").trim()}}});var Qa=p((I1,Jc)=>{Jc.exports={topLevel:{dependancies:"dependencies",dependecies:"dependencies",depdenencies:"dependencies",devEependencies:"devDependencies",depends:"dependencies","dev-dependencies":"devDependencies",devDependences:"devDependencies",devDepenencies:"devDependencies",devdependencies:"devDependencies",repostitory:"repository",repo:"repository",prefereGlobal:"preferGlobal",hompage:"homepage",hampage:"homepage",autohr:"author",autor:"author",contributers:"contributors",publicationConfig:"publishConfig",script:"scripts"},bugs:{web:"url",name:"url"},script:{server:"start",tests:"test"}}});var ns=p((R1,ts)=>{var es=qi(),Xc=ua(),Rr=va(),Kc=Xa().isCore,Zc=["dependencies","devDependencies","optionalDependencies"],Qc=Za(),kt=require("url"),le=Qa(),A1=ts.exports={warn:function(){},fixRepositoryField:function(e){if(e.repositories&&(this.warn("repositories"),e.repository=e.repositories[0]),!e.repository)return this.warn("missingRepository");typeof e.repository=="string"&&(e.repository={type:"git",url:e.repository});var r=e.repository.url||"";if(r){var t=Rr.fromUrl(r);t&&(r=e.repository.url=t.getDefaultRepresentation()=="shortcut"?t.https():t.toString())}r.match(/github.com\/[^\/]+\/[^\/]+\.git\.git$/)&&this.warn("brokenGitUrl",r)},fixTypos:function(e){Object.keys(le.topLevel).forEach(function(r){e.hasOwnProperty(r)&&this.warn("typo",r,le.topLevel[r])},this)},fixScriptsField:function(e){if(e.scripts){if(typeof e.scripts!="object"){this.warn("nonObjectScripts"),delete e.scripts;return}Object.keys(e.scripts).forEach(function(r){typeof e.scripts[r]!="string"?(this.warn("nonStringScript"),delete e.scripts[r]):le.script[r]&&!e.scripts[le.script[r]]&&this.warn("typo",r,le.script[r],"scripts")},this)}},fixFilesField:function(e){var r=e.files;r&&!Array.isArray(r)?(this.warn("nonArrayFiles"),delete e.files):e.files&&(e.files=e.files.filter(function(t){return!t||typeof t!="string"?(this.warn("invalidFilename",t),!1):!0},this))},fixBinField:function(e){if(e.bin&&typeof e.bin=="string"){var r={},t;(t=e.name.match(/^@[^/]+[/](.*)$/))?r[t[1]]=e.bin:r[e.name]=e.bin,e.bin=r}},fixManField:function(e){e.man&&typeof e.man=="string"&&(e.man=[e.man])},fixBundleDependenciesField:function(e){var r="bundledDependencies",t="bundleDependencies";e[r]&&!e[t]&&(e[t]=e[r],delete e[r]),e[t]&&!Array.isArray(e[t])?(this.warn("nonArrayBundleDependencies"),delete e[t]):e[t]&&(e[t]=e[t].filter(function(n){return!n||typeof n!="string"?(this.warn("nonStringBundleDependency",n),!1):(e.dependencies||(e.dependencies={}),e.dependencies.hasOwnProperty(n)||(this.warn("nonDependencyBundleDependency",n),e.dependencies[n]="*"),!0)},this))},fixDependencies:function(e,r){var t=!r;ol(e,this.warn),al(e,this.warn),this.fixBundleDependenciesField(e),["dependencies","devDependencies"].forEach(function(n){if(n in e){if(!e[n]||typeof e[n]!="object"){this.warn("nonObjectDependencies",n),delete e[n];return}Object.keys(e[n]).forEach(function(i){var a=e[n][i];typeof a!="string"&&(this.warn("nonStringDependency",i,JSON.stringify(a)),delete e[n][i]);var s=Rr.fromUrl(e[n][i]);s&&(e[n][i]=s.toString())},this)}},this)},fixModulesField:function(e){e.modules&&(this.warn("deprecatedModules"),delete e.modules)},fixKeywordsField:function(e){typeof e.keywords=="string"&&(e.keywords=e.keywords.split(/,\s+/)),e.keywords&&!Array.isArray(e.keywords)?(delete e.keywords,this.warn("nonArrayKeywords")):e.keywords&&(e.keywords=e.keywords.filter(function(r){return typeof r!="string"||!r?(this.warn("nonStringKeyword"),!1):!0},this))},fixVersionField:function(e,r){var t=!r;if(!e.version)return e.version="",!0;if(!es.valid(e.version,t))throw new Error('Invalid version: "'+e.version+'"');return e.version=es.clean(e.version,t),!0},fixPeople:function(e){rs(e,nl),rs(e,il)},fixNameField:function(e,r){typeof r=="boolean"?r={strict:r}:typeof r>"u"&&(r={});var t=r.strict;if(!e.name&&!t){e.name="";return}if(typeof e.name!="string")throw new Error("name field must be a string.");t||(e.name=e.name.trim()),tl(e.name,t,r.allowLegacyCase),Kc(e.name)&&this.warn("conflictingName",e.name)},fixDescriptionField:function(e){e.description&&typeof e.description!="string"&&(this.warn("nonStringDescription"),delete e.description),e.readme&&!e.description&&(e.description=Qc(e.readme)),e.description===void 0&&delete e.description,e.description||this.warn("missingDescription")},fixReadmeField:function(e){e.readme||(this.warn("missingReadme"),e.readme="ERROR: No README data found!")},fixBugsField:function(e){if(!e.bugs&&e.repository&&e.repository.url){var r=Rr.fromUrl(e.repository.url);r&&r.bugs()&&(e.bugs={url:r.bugs()})}else if(e.bugs){var t=/^.+@.*\..+$/;if(typeof e.bugs=="string")t.test(e.bugs)?e.bugs={email:e.bugs}:kt.parse(e.bugs).protocol?e.bugs={url:e.bugs}:this.warn("nonEmailUrlBugsString");else{ul(e.bugs,this.warn);var n=e.bugs;e.bugs={},n.url&&(typeof n.url=="string"&&kt.parse(n.url).protocol?e.bugs.url=n.url:this.warn("nonUrlBugsUrlField")),n.email&&(typeof n.email=="string"&&t.test(n.email)?e.bugs.email=n.email:this.warn("nonEmailBugsEmailField"))}!e.bugs.email&&!e.bugs.url&&(delete e.bugs,this.warn("emptyNormalizedBugs"))}},fixHomepageField:function(e){if(!e.homepage&&e.repository&&e.repository.url){var r=Rr.fromUrl(e.repository.url);r&&r.docs()&&(e.homepage=r.docs())}if(e.homepage){if(typeof e.homepage!="string")return this.warn("nonUrlHomepage"),delete e.homepage;kt.parse(e.homepage).protocol||(e.homepage="http://"+e.homepage)}},fixLicenseField:function(e){if(e.license)typeof e.license!="string"||e.license.length<1||e.license.trim()===""?this.warn("invalidLicense"):Xc(e.license).validForNewPackages||this.warn("invalidLicense");else return this.warn("missingLicense")}};function el(e){if(e.charAt(0)!=="@")return!1;var r=e.slice(1).split("/");return r.length!==2?!1:r[0]&&r[1]&&r[0]===encodeURIComponent(r[0])&&r[1]===encodeURIComponent(r[1])}function rl(e){return!e.match(/[\/@\s\+%:]/)&&e===encodeURIComponent(e)}function tl(e,r,t){if(e.charAt(0)==="."||!(el(e)||rl(e))||r&&!t&&e!==e.toLowerCase()||e.toLowerCase()==="node_modules"||e.toLowerCase()==="favicon.ico")throw new Error("Invalid name: "+JSON.stringify(e))}function rs(e,r){return e.author&&(e.author=r(e.author)),["maintainers","contributors"].forEach(function(t){Array.isArray(e[t])&&(e[t]=e[t].map(r))}),e}function nl(e){if(typeof e=="string")return e;var r=e.name||"",t=e.url||e.web,n=t?" ("+t+")":"",i=e.email||e.mail,a=i?" <"+i+">":"";return r+a+n}function il(e){if(typeof e!="string")return e;var r=e.match(/^([^\(<]+)/),t=e.match(/\(([^\)]+)\)/),n=e.match(/<([^>]+)>/),i={};return r&&r[0].trim()&&(i.name=r[0].trim()),n&&(i.email=n[1]),t&&(i.url=t[1]),i}function al(e,r){var t=e.optionalDependencies;if(t){var n=e.dependencies||{};Object.keys(t).forEach(function(i){n[i]=t[i]}),e.dependencies=n}}function sl(e,r,t){if(!e)return{};if(typeof e=="string"&&(e=e.trim().split(/[\n\r\s\t ,]+/)),!Array.isArray(e))return e;t("deprecatedArrayDependencies",r);var n={};return e.filter(function(i){return typeof i=="string"}).forEach(function(i){i=i.trim().split(/(:?[@\s><=])/);var a=i.shift(),s=i.join("");s=s.trim(),s=s.replace(/^@/,""),n[a]=s}),n}function ol(e,r){Zc.forEach(function(t){e[t]&&(e[t]=sl(e[t],t,r))})}function ul(e,r){e&&Object.keys(e).forEach(function(t){le.bugs[t]&&(r("typo",t,le.bugs[t],"bugs"),e[le.bugs[t]]=e[t],delete e[t])})}});var is=p((k1,cl)=>{cl.exports={repositories:"'repositories' (plural) Not supported. Please pick one as the 'repository' field",missingRepository:"No repository field.",brokenGitUrl:"Probably broken git url: %s",nonObjectScripts:"scripts must be an object",nonStringScript:"script values must be string commands",nonArrayFiles:"Invalid 'files' member",invalidFilename:"Invalid filename in 'files' list: %s",nonArrayBundleDependencies:"Invalid 'bundleDependencies' list. Must be array of package names",nonStringBundleDependency:"Invalid bundleDependencies member: %s",nonDependencyBundleDependency:"Non-dependency in bundleDependencies: %s",nonObjectDependencies:"%s field must be an object",nonStringDependency:"Invalid dependency: %s %s",deprecatedArrayDependencies:"specifying %s as array is deprecated",deprecatedModules:"modules field is deprecated",nonArrayKeywords:"keywords should be an array of strings",nonStringKeyword:"keywords should be an array of strings",conflictingName:"%s is also the name of a node core module.",nonStringDescription:"'description' field should be a string",missingDescription:"No description",missingReadme:"No README data",missingLicense:"No license field.",nonEmailUrlBugsString:"Bug string field must be url, email, or {email,url}",nonUrlBugsUrlField:"bugs.url field must be a string url. Deleted.",nonEmailBugsEmailField:"bugs.email field must be a string email. Deleted.",emptyNormalizedBugs:"Normalized value of bugs field is an empty object. Deleted.",nonUrlHomepage:"homepage field must be a string url. Deleted.",invalidLicense:"license should be a valid SPDX license expression",typo:"%s should probably be %s."}});var os=p((_1,ss)=>{var as=require("util"),_t=is();ss.exports=function(){var e=Array.prototype.slice.call(arguments,0),r=e.shift();if(r=="typo")return ll.apply(null,e);var t=_t[r]?_t[r]:r+": '%s'";return e.unshift(t),as.format.apply(null,e)};function ll(e,r,t){return t&&(e=t+"['"+e+"']",r=t+"['"+r+"']"),as.format(_t.typo,e,r)}});var Bt=p((T1,ls)=>{ls.exports=us;var Tt=ns();us.fixer=Tt;var fl=os(),pl=["name","version","description","repository","modules","scripts","files","bin","man","bugs","keywords","readme","homepage","license"],dl=["dependencies","people","typos"],Ft=pl.map(function(e){return cs(e)+"Field"});Ft=Ft.concat(dl);function us(e,r,t){r===!0&&(r=null,t=!0),t||(t=!1),(!r||e.private)&&(r=function(n){}),e.scripts&&e.scripts.install==="node-gyp rebuild"&&!e.scripts.preinstall&&(e.gypfile=!0),Tt.warn=function(){r(fl.apply(null,arguments))},Ft.forEach(function(n){Tt["fix"+cs(n)](e,t)}),e._id=e.name+"@"+e.version}function cs(e){return e.charAt(0).toUpperCase()+e.slice(1)}});var hs=p((F1,Gt)=>{"use strict";var{promisify:hl}=require("util"),fs=require("fs"),ps=require("path"),ds=xi(),ml=hl(fs.readFile);Gt.exports=async e=>{e={cwd:process.cwd(),normalize:!0,...e};let r=ps.resolve(e.cwd,"package.json"),t=ds(await ml(r,"utf8"));return e.normalize&&Bt()(t),t};Gt.exports.sync=e=>{e={cwd:process.cwd(),normalize:!0,...e};let r=ps.resolve(e.cwd,"package.json"),t=ds(fs.readFileSync(r,"utf8"));return e.normalize&&Bt()(t),t}});var bs=p((B1,Mt)=>{"use strict";var ms=require("path"),vs=ln(),gs=hs();Mt.exports=async e=>{let r=await vs("package.json",e);if(r)return{packageJson:await gs({...e,cwd:ms.dirname(r)}),path:r}};Mt.exports.sync=e=>{let r=vs.sync("package.json",e);if(r)return{packageJson:gs.sync({...e,cwd:ms.dirname(r)}),path:r}}});var ys=p(Te=>{"use strict";Object.defineProperty(Te,"__esModule",{value:!0});Te.crossLaunchCommand=Te.callbackLaunchCommand=void 0;var Le=require("@raycast/api"),vl=bs(),gl=async(e,r)=>(0,Le.launchCommand)({...e,context:{...e.context,...r}});Te.callbackLaunchCommand=gl;var bl=async(e,r)=>{if(r===!1)return(0,Le.launchCommand)(e);let t=(0,vl.sync)({cwd:__dirname,normalize:!1}),n=Le.environment.ownerOrAuthorName??t?.packageJson?.owner??t?.packageJson?.author;if("ownerOrAuthorName"in e){let i=`${e.ownerOrAuthorName}/${e.extensionName}`;if(!t?.packageJson?.crossExtensions?.includes(i)){let a=`Target extension '${i}' should be listed in 'crossExtensions' of package.json.`;console.error(a);return}}return(0,Le.launchCommand)({...e,context:{...e.context,callbackLaunchOptions:{name:Le.environment.commandName,extensionName:Le.environment.extensionName,ownerOrAuthorName:n,type:Le.LaunchType.UserInitiated,...r}}})};Te.crossLaunchCommand=bl});var El={};Is(El,{default:()=>xl});module.exports=Rs(El);var _r=require("@raycast/api");var fe=require("@raycast/api"),jt=As(ys()),{enableFocusWhileFocused:Ut}=(0,fe.getPreferenceValues)(),Ls={type:fe.LaunchType.Background,extensionName:"do-not-disturb",ownerOrAuthorName:"yakitrak"};async function qt(e,r){if(Ut)return(0,jt.crossLaunchCommand)({...Ls,name:e?"on":"off",context:{supressHUD:!e}},r||!1).catch(()=>{})}async function Cs(e){Ut&&await(0,jt.crossLaunchCommand)({...Ls,name:"status",context:{suppressHUD:!0}},e||!1).catch(async()=>{await(0,fe.confirmAlert)({title:"Need to Install Additional Extension",message:'The "Enable Do Not Disturb mode while focused" feature requires the "Do Not Distrub" extension, do you want to move to the install page now?'})&&await(0,fe.open)("raycast://extensions/yakitrak/do-not-disturb")})}var se=require("@raycast/api");var kr=new se.Cache,yl="pomodoro-interval/1.1",$t="pomodoro-interval/completed-pomodoro-count",ws="pomodoro-interval/history",Ll=()=>Math.round(new Date().valueOf()/1e3);async function Cl(){let e=await se.LocalStorage.getItem(ws);return typeof e!="string"||e===null?[]:JSON.parse(e)}async function wl(e){let r=await Cl(),t=r.findIndex(n=>n.id===e.id);t!==-1?r[t]=e:r.push(e),await se.LocalStorage.setItem(ws,JSON.stringify(r))}function Ps(e,r,t){let n=0;r||(n=parseInt(kr.get($t)??"0",10),n++),kr.set($t,n.toString());let i={type:e,id:n,length:t||Pl[e],parts:[{startedAt:Ll()}]};return kr.set(yl,JSON.stringify(i)),wl(i).then(),e==="focus"&&qt(!0),i}var Wt=(0,se.getPreferenceValues)(),Pl={focus:parseFloat(Wt.focusIntervalDuration)*60,"short-break":parseFloat(Wt.shortBreakIntervalDuration)*60,"long-break":parseFloat(Wt.longBreakIntervalDuration)*60};async function Ss(e,r){await Cs();let t=Ps(e,!1,r);return await Sl(),t}async function Sl(){try{await(0,_r.launchCommand)({name:"pomodoro-menu-bar",type:_r.LaunchType.UserInitiated})}catch(e){console.error(e)}}async function xl(e){return Ss(e.type,e.duration)}
