{"version": 3, "sources": ["../../node_modules/p-try/index.js", "../../node_modules/p-limit/index.js", "../../node_modules/p-locate/index.js", "../../node_modules/locate-path/index.js", "../../node_modules/path-exists/index.js", "../../node_modules/find-up/index.js", "../../node_modules/is-arrayish/index.js", "../../node_modules/error-ex/index.js", "../../node_modules/json-parse-even-better-errors/index.js", "../../node_modules/lines-and-columns/build/index.js", "../../node_modules/js-tokens/index.js", "../../node_modules/@babel/helper-validator-identifier/src/identifier.ts", "../../node_modules/@babel/helper-validator-identifier/src/keyword.ts", "../../node_modules/@babel/helper-validator-identifier/src/index.ts", "../../node_modules/picocolors/picocolors.js", "../../node_modules/@babel/highlight/node_modules/escape-string-regexp/index.js", "../../node_modules/@babel/highlight/node_modules/color-name/index.js", "../../node_modules/@babel/highlight/node_modules/color-convert/conversions.js", "../../node_modules/@babel/highlight/node_modules/color-convert/route.js", "../../node_modules/@babel/highlight/node_modules/color-convert/index.js", "../../node_modules/@babel/highlight/node_modules/ansi-styles/index.js", "../../node_modules/@babel/highlight/node_modules/has-flag/index.js", "../../node_modules/@babel/highlight/node_modules/supports-color/index.js", "../../node_modules/@babel/highlight/node_modules/chalk/templates.js", "../../node_modules/@babel/highlight/node_modules/chalk/index.js", "../../node_modules/@babel/highlight/src/index.ts", "../../node_modules/@babel/code-frame/lib/index.js", "../../node_modules/parse-json/index.js", "../../node_modules/normalize-package-data/node_modules/semver/semver.js", "../../node_modules/spdx-license-ids/index.json", "../../node_modules/spdx-license-ids/deprecated.json", "../../node_modules/spdx-exceptions/index.json", "../../node_modules/spdx-expression-parse/scan.js", "../../node_modules/spdx-expression-parse/parse.js", "../../node_modules/spdx-expression-parse/index.js", "../../node_modules/spdx-correct/index.js", "../../node_modules/validate-npm-package-license/index.js", "../../node_modules/hosted-git-info/git-host-info.js", "../../node_modules/hosted-git-info/git-host.js", "../../node_modules/hosted-git-info/index.js", "../../node_modules/resolve/lib/homedir.js", "../../node_modules/resolve/lib/caller.js", "../../node_modules/path-parse/index.js", "../../node_modules/resolve/lib/node-modules-paths.js", "../../node_modules/resolve/lib/normalize-options.js", "../../node_modules/function-bind/implementation.js", "../../node_modules/function-bind/index.js", "../../node_modules/hasown/index.js", "../../node_modules/is-core-module/core.json", "../../node_modules/is-core-module/index.js", "../../node_modules/resolve/lib/async.js", "../../node_modules/resolve/lib/core.json", "../../node_modules/resolve/lib/core.js", "../../node_modules/resolve/lib/is-core.js", "../../node_modules/resolve/lib/sync.js", "../../node_modules/resolve/index.js", "../../node_modules/normalize-package-data/lib/extract_description.js", "../../node_modules/normalize-package-data/lib/typos.json", "../../node_modules/normalize-package-data/lib/fixer.js", "../../node_modules/normalize-package-data/lib/warning_messages.json", "../../node_modules/normalize-package-data/lib/make_warning.js", "../../node_modules/normalize-package-data/lib/normalize.js", "../../node_modules/read-pkg/index.js", "../../node_modules/read-pkg-up/index.js", "../../node_modules/raycast-cross-extension/distribution/index.js", "../../src/tools/continue-timer.ts", "../../src/lib/intervals.tsx", "../../src/lib/doNotDisturb.tsx", "../../src/lib/timer.ts"], "sourcesContent": ["'use strict';\n\nconst pTry = (fn, ...arguments_) => new Promise(resolve => {\n\tresolve(fn(...arguments_));\n});\n\nmodule.exports = pTry;\n// TODO: remove this in the next major version\nmodule.exports.default = pTry;\n", "'use strict';\nconst pTry = require('p-try');\n\nconst pLimit = concurrency => {\n\tif (!((Number.isInteger(concurrency) || concurrency === Infinity) && concurrency > 0)) {\n\t\treturn Promise.reject(new TypeError('Expected `concurrency` to be a number from 1 and up'));\n\t}\n\n\tconst queue = [];\n\tlet activeCount = 0;\n\n\tconst next = () => {\n\t\tactiveCount--;\n\n\t\tif (queue.length > 0) {\n\t\t\tqueue.shift()();\n\t\t}\n\t};\n\n\tconst run = (fn, resolve, ...args) => {\n\t\tactiveCount++;\n\n\t\tconst result = pTry(fn, ...args);\n\n\t\tresolve(result);\n\n\t\tresult.then(next, next);\n\t};\n\n\tconst enqueue = (fn, resolve, ...args) => {\n\t\tif (activeCount < concurrency) {\n\t\t\trun(fn, resolve, ...args);\n\t\t} else {\n\t\t\tqueue.push(run.bind(null, fn, resolve, ...args));\n\t\t}\n\t};\n\n\tconst generator = (fn, ...args) => new Promise(resolve => enqueue(fn, resolve, ...args));\n\tObject.defineProperties(generator, {\n\t\tactiveCount: {\n\t\t\tget: () => activeCount\n\t\t},\n\t\tpendingCount: {\n\t\t\tget: () => queue.length\n\t\t},\n\t\tclearQueue: {\n\t\t\tvalue: () => {\n\t\t\t\tqueue.length = 0;\n\t\t\t}\n\t\t}\n\t});\n\n\treturn generator;\n};\n\nmodule.exports = pLimit;\nmodule.exports.default = pLimit;\n", "'use strict';\nconst pLimit = require('p-limit');\n\nclass EndError extends Error {\n\tconstructor(value) {\n\t\tsuper();\n\t\tthis.value = value;\n\t}\n}\n\n// The input can also be a promise, so we await it\nconst testElement = async (element, tester) => tester(await element);\n\n// The input can also be a promise, so we `Promise.all()` them both\nconst finder = async element => {\n\tconst values = await Promise.all(element);\n\tif (values[1] === true) {\n\t\tthrow new EndError(values[0]);\n\t}\n\n\treturn false;\n};\n\nconst pLocate = async (iterable, tester, options) => {\n\toptions = {\n\t\tconcurrency: Infinity,\n\t\tpreserveOrder: true,\n\t\t...options\n\t};\n\n\tconst limit = pLimit(options.concurrency);\n\n\t// Start all the promises concurrently with optional limit\n\tconst items = [...iterable].map(element => [element, limit(testElement, element, tester)]);\n\n\t// Check the promises either serially or concurrently\n\tconst checkLimit = pLimit(options.preserveOrder ? 1 : Infinity);\n\n\ttry {\n\t\tawait Promise.all(items.map(element => checkLimit(finder, element)));\n\t} catch (error) {\n\t\tif (error instanceof EndError) {\n\t\t\treturn error.value;\n\t\t}\n\n\t\tthrow error;\n\t}\n};\n\nmodule.exports = pLocate;\n// TODO: Remove this for the next major release\nmodule.exports.default = pLocate;\n", "'use strict';\nconst path = require('path');\nconst fs = require('fs');\nconst {promisify} = require('util');\nconst pLocate = require('p-locate');\n\nconst fsStat = promisify(fs.stat);\nconst fsLStat = promisify(fs.lstat);\n\nconst typeMappings = {\n\tdirectory: 'isDirectory',\n\tfile: 'isFile'\n};\n\nfunction checkType({type}) {\n\tif (type in typeMappings) {\n\t\treturn;\n\t}\n\n\tthrow new Error(`Invalid type specified: ${type}`);\n}\n\nconst matchType = (type, stat) => type === undefined || stat[typeMappings[type]]();\n\nmodule.exports = async (paths, options) => {\n\toptions = {\n\t\tcwd: process.cwd(),\n\t\ttype: 'file',\n\t\tallowSymlinks: true,\n\t\t...options\n\t};\n\tcheckType(options);\n\tconst statFn = options.allowSymlinks ? fsStat : fsLStat;\n\n\treturn pLocate(paths, async path_ => {\n\t\ttry {\n\t\t\tconst stat = await statFn(path.resolve(options.cwd, path_));\n\t\t\treturn matchType(options.type, stat);\n\t\t} catch (_) {\n\t\t\treturn false;\n\t\t}\n\t}, options);\n};\n\nmodule.exports.sync = (paths, options) => {\n\toptions = {\n\t\tcwd: process.cwd(),\n\t\tallowSymlinks: true,\n\t\ttype: 'file',\n\t\t...options\n\t};\n\tcheckType(options);\n\tconst statFn = options.allowSymlinks ? fs.statSync : fs.lstatSync;\n\n\tfor (const path_ of paths) {\n\t\ttry {\n\t\t\tconst stat = statFn(path.resolve(options.cwd, path_));\n\n\t\t\tif (matchType(options.type, stat)) {\n\t\t\t\treturn path_;\n\t\t\t}\n\t\t} catch (_) {\n\t\t}\n\t}\n};\n", "'use strict';\nconst fs = require('fs');\nconst {promisify} = require('util');\n\nconst pAccess = promisify(fs.access);\n\nmodule.exports = async path => {\n\ttry {\n\t\tawait pAccess(path);\n\t\treturn true;\n\t} catch (_) {\n\t\treturn false;\n\t}\n};\n\nmodule.exports.sync = path => {\n\ttry {\n\t\tfs.accessSync(path);\n\t\treturn true;\n\t} catch (_) {\n\t\treturn false;\n\t}\n};\n", "'use strict';\nconst path = require('path');\nconst locatePath = require('locate-path');\nconst pathExists = require('path-exists');\n\nconst stop = Symbol('findUp.stop');\n\nmodule.exports = async (name, options = {}) => {\n\tlet directory = path.resolve(options.cwd || '');\n\tconst {root} = path.parse(directory);\n\tconst paths = [].concat(name);\n\n\tconst runMatcher = async locateOptions => {\n\t\tif (typeof name !== 'function') {\n\t\t\treturn locatePath(paths, locateOptions);\n\t\t}\n\n\t\tconst foundPath = await name(locateOptions.cwd);\n\t\tif (typeof foundPath === 'string') {\n\t\t\treturn locatePath([foundPath], locateOptions);\n\t\t}\n\n\t\treturn foundPath;\n\t};\n\n\t// eslint-disable-next-line no-constant-condition\n\twhile (true) {\n\t\t// eslint-disable-next-line no-await-in-loop\n\t\tconst foundPath = await runMatcher({...options, cwd: directory});\n\n\t\tif (foundPath === stop) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (foundPath) {\n\t\t\treturn path.resolve(directory, foundPath);\n\t\t}\n\n\t\tif (directory === root) {\n\t\t\treturn;\n\t\t}\n\n\t\tdirectory = path.dirname(directory);\n\t}\n};\n\nmodule.exports.sync = (name, options = {}) => {\n\tlet directory = path.resolve(options.cwd || '');\n\tconst {root} = path.parse(directory);\n\tconst paths = [].concat(name);\n\n\tconst runMatcher = locateOptions => {\n\t\tif (typeof name !== 'function') {\n\t\t\treturn locatePath.sync(paths, locateOptions);\n\t\t}\n\n\t\tconst foundPath = name(locateOptions.cwd);\n\t\tif (typeof foundPath === 'string') {\n\t\t\treturn locatePath.sync([foundPath], locateOptions);\n\t\t}\n\n\t\treturn foundPath;\n\t};\n\n\t// eslint-disable-next-line no-constant-condition\n\twhile (true) {\n\t\tconst foundPath = runMatcher({...options, cwd: directory});\n\n\t\tif (foundPath === stop) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (foundPath) {\n\t\t\treturn path.resolve(directory, foundPath);\n\t\t}\n\n\t\tif (directory === root) {\n\t\t\treturn;\n\t\t}\n\n\t\tdirectory = path.dirname(directory);\n\t}\n};\n\nmodule.exports.exists = pathExists;\n\nmodule.exports.sync.exists = pathExists.sync;\n\nmodule.exports.stop = stop;\n", "'use strict';\n\nmodule.exports = function isArrayish(obj) {\n\tif (!obj) {\n\t\treturn false;\n\t}\n\n\treturn obj instanceof Array || Array.isArray(obj) ||\n\t\t(obj.length >= 0 && obj.splice instanceof Function);\n};\n", "'use strict';\n\nvar util = require('util');\nvar isArrayish = require('is-arrayish');\n\nvar errorEx = function errorEx(name, properties) {\n\tif (!name || name.constructor !== String) {\n\t\tproperties = name || {};\n\t\tname = Error.name;\n\t}\n\n\tvar errorExError = function ErrorEXError(message) {\n\t\tif (!this) {\n\t\t\treturn new ErrorEXError(message);\n\t\t}\n\n\t\tmessage = message instanceof Error\n\t\t\t? message.message\n\t\t\t: (message || this.message);\n\n\t\tError.call(this, message);\n\t\tError.captureStackTrace(this, errorExError);\n\n\t\tthis.name = name;\n\n\t\tObject.defineProperty(this, 'message', {\n\t\t\tconfigurable: true,\n\t\t\tenumerable: false,\n\t\t\tget: function () {\n\t\t\t\tvar newMessage = message.split(/\\r?\\n/g);\n\n\t\t\t\tfor (var key in properties) {\n\t\t\t\t\tif (!properties.hasOwnProperty(key)) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar modifier = properties[key];\n\n\t\t\t\t\tif ('message' in modifier) {\n\t\t\t\t\t\tnewMessage = modifier.message(this[key], newMessage) || newMessage;\n\t\t\t\t\t\tif (!isArrayish(newMessage)) {\n\t\t\t\t\t\t\tnewMessage = [newMessage];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn newMessage.join('\\n');\n\t\t\t},\n\t\t\tset: function (v) {\n\t\t\t\tmessage = v;\n\t\t\t}\n\t\t});\n\n\t\tvar overwrittenStack = null;\n\n\t\tvar stackDescriptor = Object.getOwnPropertyDescriptor(this, 'stack');\n\t\tvar stackGetter = stackDescriptor.get;\n\t\tvar stackValue = stackDescriptor.value;\n\t\tdelete stackDescriptor.value;\n\t\tdelete stackDescriptor.writable;\n\n\t\tstackDescriptor.set = function (newstack) {\n\t\t\toverwrittenStack = newstack;\n\t\t};\n\n\t\tstackDescriptor.get = function () {\n\t\t\tvar stack = (overwrittenStack || ((stackGetter)\n\t\t\t\t? stackGetter.call(this)\n\t\t\t\t: stackValue)).split(/\\r?\\n+/g);\n\n\t\t\t// starting in Node 7, the stack builder caches the message.\n\t\t\t// just replace it.\n\t\t\tif (!overwrittenStack) {\n\t\t\t\tstack[0] = this.name + ': ' + this.message;\n\t\t\t}\n\n\t\t\tvar lineCount = 1;\n\t\t\tfor (var key in properties) {\n\t\t\t\tif (!properties.hasOwnProperty(key)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tvar modifier = properties[key];\n\n\t\t\t\tif ('line' in modifier) {\n\t\t\t\t\tvar line = modifier.line(this[key]);\n\t\t\t\t\tif (line) {\n\t\t\t\t\t\tstack.splice(lineCount++, 0, '    ' + line);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif ('stack' in modifier) {\n\t\t\t\t\tmodifier.stack(this[key], stack);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn stack.join('\\n');\n\t\t};\n\n\t\tObject.defineProperty(this, 'stack', stackDescriptor);\n\t};\n\n\tif (Object.setPrototypeOf) {\n\t\tObject.setPrototypeOf(errorExError.prototype, Error.prototype);\n\t\tObject.setPrototypeOf(errorExError, Error);\n\t} else {\n\t\tutil.inherits(errorExError, Error);\n\t}\n\n\treturn errorExError;\n};\n\nerrorEx.append = function (str, def) {\n\treturn {\n\t\tmessage: function (v, message) {\n\t\t\tv = v || def;\n\n\t\t\tif (v) {\n\t\t\t\tmessage[0] += ' ' + str.replace('%s', v.toString());\n\t\t\t}\n\n\t\t\treturn message;\n\t\t}\n\t};\n};\n\nerrorEx.line = function (str, def) {\n\treturn {\n\t\tline: function (v) {\n\t\t\tv = v || def;\n\n\t\t\tif (v) {\n\t\t\t\treturn str.replace('%s', v.toString());\n\t\t\t}\n\n\t\t\treturn null;\n\t\t}\n\t};\n};\n\nmodule.exports = errorEx;\n", "'use strict'\n\nconst hexify = char => {\n  const h = char.charCodeAt(0).toString(16).toUpperCase()\n  return '0x' + (h.length % 2 ? '0' : '') + h\n}\n\nconst parseError = (e, txt, context) => {\n  if (!txt) {\n    return {\n      message: e.message + ' while parsing empty string',\n      position: 0,\n    }\n  }\n  const badToken = e.message.match(/^Unexpected token (.) .*position\\s+(\\d+)/i)\n  const errIdx = badToken ? +badToken[2]\n    : e.message.match(/^Unexpected end of JSON.*/i) ? txt.length - 1\n    : null\n\n  const msg = badToken ? e.message.replace(/^Unexpected token ./, `Unexpected token ${\n      JSON.stringify(badToken[1])\n    } (${hexify(badToken[1])})`)\n    : e.message\n\n  if (errIdx !== null && errIdx !== undefined) {\n    const start = errIdx <= context ? 0\n      : errIdx - context\n\n    const end = errIdx + context >= txt.length ? txt.length\n      : errIdx + context\n\n    const slice = (start === 0 ? '' : '...') +\n      txt.slice(start, end) +\n      (end === txt.length ? '' : '...')\n\n    const near = txt === slice ? '' : 'near '\n\n    return {\n      message: msg + ` while parsing ${near}${JSON.stringify(slice)}`,\n      position: errIdx,\n    }\n  } else {\n    return {\n      message: msg + ` while parsing '${txt.slice(0, context * 2)}'`,\n      position: 0,\n    }\n  }\n}\n\nclass JSONParseError extends SyntaxError {\n  constructor (er, txt, context, caller) {\n    context = context || 20\n    const metadata = parseError(er, txt, context)\n    super(metadata.message)\n    Object.assign(this, metadata)\n    this.code = 'EJSONPARSE'\n    this.systemError = er\n    Error.captureStackTrace(this, caller || this.constructor)\n  }\n  get name () { return this.constructor.name }\n  set name (n) {}\n  get [Symbol.toStringTag] () { return this.constructor.name }\n}\n\nconst kIndent = Symbol.for('indent')\nconst kNewline = Symbol.for('newline')\n// only respect indentation if we got a line break, otherwise squash it\n// things other than objects and arrays aren't indented, so ignore those\n// Important: in both of these regexps, the $1 capture group is the newline\n// or undefined, and the $2 capture group is the indent, or undefined.\nconst formatRE = /^\\s*[{\\[]((?:\\r?\\n)+)([\\s\\t]*)/\nconst emptyRE = /^(?:\\{\\}|\\[\\])((?:\\r?\\n)+)?$/\n\nconst parseJson = (txt, reviver, context) => {\n  const parseText = stripBOM(txt)\n  context = context || 20\n  try {\n    // get the indentation so that we can save it back nicely\n    // if the file starts with {\" then we have an indent of '', ie, none\n    // otherwise, pick the indentation of the next line after the first \\n\n    // If the pattern doesn't match, then it means no indentation.\n    // JSON.stringify ignores symbols, so this is reasonably safe.\n    // if the string is '{}' or '[]', then use the default 2-space indent.\n    const [, newline = '\\n', indent = '  '] = parseText.match(emptyRE) ||\n      parseText.match(formatRE) ||\n      [, '', '']\n\n    const result = JSON.parse(parseText, reviver)\n    if (result && typeof result === 'object') {\n      result[kNewline] = newline\n      result[kIndent] = indent\n    }\n    return result\n  } catch (e) {\n    if (typeof txt !== 'string' && !Buffer.isBuffer(txt)) {\n      const isEmptyArray = Array.isArray(txt) && txt.length === 0\n      throw Object.assign(new TypeError(\n        `Cannot parse ${isEmptyArray ? 'an empty array' : String(txt)}`\n      ), {\n        code: 'EJSONPARSE',\n        systemError: e,\n      })\n    }\n\n    throw new JSONParseError(e, parseText, context, parseJson)\n  }\n}\n\n// Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n// because the buffer-to-string conversion in `fs.readFileSync()`\n// translates it to FEFF, the UTF-16 BOM.\nconst stripBOM = txt => String(txt).replace(/^\\uFEFF/, '')\n\nmodule.exports = parseJson\nparseJson.JSONParseError = JSONParseError\n\nparseJson.noExceptions = (txt, reviver) => {\n  try {\n    return JSON.parse(stripBOM(txt), reviver)\n  } catch (e) {}\n}\n", "\"use strict\";\nexports.__esModule = true;\nexports.LinesAndColumns = void 0;\nvar LF = '\\n';\nvar CR = '\\r';\nvar LinesAndColumns = /** @class */ (function () {\n    function LinesAndColumns(string) {\n        this.string = string;\n        var offsets = [0];\n        for (var offset = 0; offset < string.length;) {\n            switch (string[offset]) {\n                case LF:\n                    offset += LF.length;\n                    offsets.push(offset);\n                    break;\n                case CR:\n                    offset += CR.length;\n                    if (string[offset] === LF) {\n                        offset += LF.length;\n                    }\n                    offsets.push(offset);\n                    break;\n                default:\n                    offset++;\n                    break;\n            }\n        }\n        this.offsets = offsets;\n    }\n    LinesAndColumns.prototype.locationForIndex = function (index) {\n        if (index < 0 || index > this.string.length) {\n            return null;\n        }\n        var line = 0;\n        var offsets = this.offsets;\n        while (offsets[line + 1] <= index) {\n            line++;\n        }\n        var column = index - offsets[line];\n        return { line: line, column: column };\n    };\n    LinesAndColumns.prototype.indexForLocation = function (location) {\n        var line = location.line, column = location.column;\n        if (line < 0 || line >= this.offsets.length) {\n            return null;\n        }\n        if (column < 0 || column > this.lengthOfLine(line)) {\n            return null;\n        }\n        return this.offsets[line] + column;\n    };\n    LinesAndColumns.prototype.lengthOfLine = function (line) {\n        var offset = this.offsets[line];\n        var nextOffset = line === this.offsets.length - 1\n            ? this.string.length\n            : this.offsets[line + 1];\n        return nextOffset - offset;\n    };\n    return LinesAndColumns;\n}());\nexports.LinesAndColumns = LinesAndColumns;\nexports[\"default\"] = LinesAndColumns;\n", "// Copyright 2014, 2015, 2016, 2017, 2018 <PERSON>\n// License: MIT. (See LICENSE.)\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n})\n\n// This regex comes from regex.coffee, and is inserted here by generate-index.js\n// (run `npm run build`).\nexports.default = /((['\"])(?:(?!\\2|\\\\).|\\\\(?:\\r\\n|[\\s\\S]))*(\\2)?|`(?:[^`\\\\$]|\\\\[\\s\\S]|\\$(?!\\{)|\\$\\{(?:[^{}]|\\{[^}]*\\}?)*\\}?)*(`)?)|(\\/\\/.*)|(\\/\\*(?:[^*]|\\*(?!\\/))*(\\*\\/)?)|(\\/(?!\\*)(?:\\[(?:(?![\\]\\\\]).|\\\\.)*\\]|(?![\\/\\]\\\\]).|\\\\.)+\\/(?:(?!\\s*(?:\\b|[\\u0080-\\uFFFF$\\\\'\"~({]|[+\\-!](?!=)|\\.?\\d))|[gmiyus]{1,6}\\b(?![\\u0080-\\uFFFF$\\\\]|\\s*(?:[+\\-*%&|^<>!=?({]|\\/(?![\\/*])))))|(0[xX][\\da-fA-F]+|0[oO][0-7]+|0[bB][01]+|(?:\\d*\\.\\d+|\\d+\\.?)(?:[eE][+-]?\\d+)?)|((?!\\d)(?:(?!\\s)[$\\w\\u0080-\\uFFFF]|\\\\u[\\da-fA-F]{4}|\\\\u\\{[\\da-fA-F]+\\})+)|(--|\\+\\+|&&|\\|\\||=>|\\.{3}|(?:[+\\-\\/%&|^]|\\*{1,2}|<{1,2}|>{1,3}|!=?|={1,2})=?|[?~.,:;[\\](){}])|(\\s+)|(^$|[\\s\\S])/g\n\nexports.matchToToken = function(match) {\n  var token = {type: \"invalid\", value: match[0], closed: undefined}\n       if (match[ 1]) token.type = \"string\" , token.closed = !!(match[3] || match[4])\n  else if (match[ 5]) token.type = \"comment\"\n  else if (match[ 6]) token.type = \"comment\", token.closed = !!match[7]\n  else if (match[ 8]) token.type = \"regex\"\n  else if (match[ 9]) token.type = \"number\"\n  else if (match[10]) token.type = \"name\"\n  else if (match[11]) token.type = \"punctuator\"\n  else if (match[12]) token.type = \"whitespace\"\n  return token\n}\n", "// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\n\n// ## Character categories\n\n// Big ugly regular expressions that match characters in the\n// whitespace, identifier, and identifier-start categories. These\n// are only applied when a character is found to actually have a\n// code point between 0x80 and 0xffff.\n// Generated by `scripts/generate-identifier-regex.cjs`.\n\n/* prettier-ignore */\nlet nonASCIIidentifierStartChars = \"\\xaa\\xb5\\xba\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0370-\\u0374\\u0376\\u0377\\u037a-\\u037d\\u037f\\u0386\\u0388-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u048a-\\u052f\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05d0-\\u05ea\\u05ef-\\u05f2\\u0620-\\u064a\\u066e\\u066f\\u0671-\\u06d3\\u06d5\\u06e5\\u06e6\\u06ee\\u06ef\\u06fa-\\u06fc\\u06ff\\u0710\\u0712-\\u072f\\u074d-\\u07a5\\u07b1\\u07ca-\\u07ea\\u07f4\\u07f5\\u07fa\\u0800-\\u0815\\u081a\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086a\\u0870-\\u0887\\u0889-\\u088e\\u08a0-\\u08c9\\u0904-\\u0939\\u093d\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098c\\u098f\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bd\\u09ce\\u09dc\\u09dd\\u09df-\\u09e1\\u09f0\\u09f1\\u09fc\\u0a05-\\u0a0a\\u0a0f\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32\\u0a33\\u0a35\\u0a36\\u0a38\\u0a39\\u0a59-\\u0a5c\\u0a5e\\u0a72-\\u0a74\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2\\u0ab3\\u0ab5-\\u0ab9\\u0abd\\u0ad0\\u0ae0\\u0ae1\\u0af9\\u0b05-\\u0b0c\\u0b0f\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32\\u0b33\\u0b35-\\u0b39\\u0b3d\\u0b5c\\u0b5d\\u0b5f-\\u0b61\\u0b71\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99\\u0b9a\\u0b9c\\u0b9e\\u0b9f\\u0ba3\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bd0\\u0c05-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c39\\u0c3d\\u0c58-\\u0c5a\\u0c5d\\u0c60\\u0c61\\u0c80\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbd\\u0cdd\\u0cde\\u0ce0\\u0ce1\\u0cf1\\u0cf2\\u0d04-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d3a\\u0d3d\\u0d4e\\u0d54-\\u0d56\\u0d5f-\\u0d61\\u0d7a-\\u0d7f\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0e01-\\u0e30\\u0e32\\u0e33\\u0e40-\\u0e46\\u0e81\\u0e82\\u0e84\\u0e86-\\u0e8a\\u0e8c-\\u0ea3\\u0ea5\\u0ea7-\\u0eb0\\u0eb2\\u0eb3\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0edc-\\u0edf\\u0f00\\u0f40-\\u0f47\\u0f49-\\u0f6c\\u0f88-\\u0f8c\\u1000-\\u102a\\u103f\\u1050-\\u1055\\u105a-\\u105d\\u1061\\u1065\\u1066\\u106e-\\u1070\\u1075-\\u1081\\u108e\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u1380-\\u138f\\u13a0-\\u13f5\\u13f8-\\u13fd\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f8\\u1700-\\u1711\\u171f-\\u1731\\u1740-\\u1751\\u1760-\\u176c\\u176e-\\u1770\\u1780-\\u17b3\\u17d7\\u17dc\\u1820-\\u1878\\u1880-\\u18a8\\u18aa\\u18b0-\\u18f5\\u1900-\\u191e\\u1950-\\u196d\\u1970-\\u1974\\u1980-\\u19ab\\u19b0-\\u19c9\\u1a00-\\u1a16\\u1a20-\\u1a54\\u1aa7\\u1b05-\\u1b33\\u1b45-\\u1b4c\\u1b83-\\u1ba0\\u1bae\\u1baf\\u1bba-\\u1be5\\u1c00-\\u1c23\\u1c4d-\\u1c4f\\u1c5a-\\u1c7d\\u1c80-\\u1c8a\\u1c90-\\u1cba\\u1cbd-\\u1cbf\\u1ce9-\\u1cec\\u1cee-\\u1cf3\\u1cf5\\u1cf6\\u1cfa\\u1d00-\\u1dbf\\u1e00-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u2071\\u207f\\u2090-\\u209c\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2118-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u2c00-\\u2ce4\\u2ceb-\\u2cee\\u2cf2\\u2cf3\\u2d00-\\u2d25\\u2d27\\u2d2d\\u2d30-\\u2d67\\u2d6f\\u2d80-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303c\\u3041-\\u3096\\u309b-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u31a0-\\u31bf\\u31f0-\\u31ff\\u3400-\\u4dbf\\u4e00-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua61f\\ua62a\\ua62b\\ua640-\\ua66e\\ua67f-\\ua69d\\ua6a0-\\ua6ef\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua7cd\\ua7d0\\ua7d1\\ua7d3\\ua7d5-\\ua7dc\\ua7f2-\\ua801\\ua803-\\ua805\\ua807-\\ua80a\\ua80c-\\ua822\\ua840-\\ua873\\ua882-\\ua8b3\\ua8f2-\\ua8f7\\ua8fb\\ua8fd\\ua8fe\\ua90a-\\ua925\\ua930-\\ua946\\ua960-\\ua97c\\ua984-\\ua9b2\\ua9cf\\ua9e0-\\ua9e4\\ua9e6-\\ua9ef\\ua9fa-\\ua9fe\\uaa00-\\uaa28\\uaa40-\\uaa42\\uaa44-\\uaa4b\\uaa60-\\uaa76\\uaa7a\\uaa7e-\\uaaaf\\uaab1\\uaab5\\uaab6\\uaab9-\\uaabd\\uaac0\\uaac2\\uaadb-\\uaadd\\uaae0-\\uaaea\\uaaf2-\\uaaf4\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uab30-\\uab5a\\uab5c-\\uab69\\uab70-\\uabe2\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d\\ufb1f-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40\\ufb41\\ufb43\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff21-\\uff3a\\uff41-\\uff5a\\uff66-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc\";\n/* prettier-ignore */\nlet nonASCIIidentifierChars = \"\\xb7\\u0300-\\u036f\\u0387\\u0483-\\u0487\\u0591-\\u05bd\\u05bf\\u05c1\\u05c2\\u05c4\\u05c5\\u05c7\\u0610-\\u061a\\u064b-\\u0669\\u0670\\u06d6-\\u06dc\\u06df-\\u06e4\\u06e7\\u06e8\\u06ea-\\u06ed\\u06f0-\\u06f9\\u0711\\u0730-\\u074a\\u07a6-\\u07b0\\u07c0-\\u07c9\\u07eb-\\u07f3\\u07fd\\u0816-\\u0819\\u081b-\\u0823\\u0825-\\u0827\\u0829-\\u082d\\u0859-\\u085b\\u0897-\\u089f\\u08ca-\\u08e1\\u08e3-\\u0903\\u093a-\\u093c\\u093e-\\u094f\\u0951-\\u0957\\u0962\\u0963\\u0966-\\u096f\\u0981-\\u0983\\u09bc\\u09be-\\u09c4\\u09c7\\u09c8\\u09cb-\\u09cd\\u09d7\\u09e2\\u09e3\\u09e6-\\u09ef\\u09fe\\u0a01-\\u0a03\\u0a3c\\u0a3e-\\u0a42\\u0a47\\u0a48\\u0a4b-\\u0a4d\\u0a51\\u0a66-\\u0a71\\u0a75\\u0a81-\\u0a83\\u0abc\\u0abe-\\u0ac5\\u0ac7-\\u0ac9\\u0acb-\\u0acd\\u0ae2\\u0ae3\\u0ae6-\\u0aef\\u0afa-\\u0aff\\u0b01-\\u0b03\\u0b3c\\u0b3e-\\u0b44\\u0b47\\u0b48\\u0b4b-\\u0b4d\\u0b55-\\u0b57\\u0b62\\u0b63\\u0b66-\\u0b6f\\u0b82\\u0bbe-\\u0bc2\\u0bc6-\\u0bc8\\u0bca-\\u0bcd\\u0bd7\\u0be6-\\u0bef\\u0c00-\\u0c04\\u0c3c\\u0c3e-\\u0c44\\u0c46-\\u0c48\\u0c4a-\\u0c4d\\u0c55\\u0c56\\u0c62\\u0c63\\u0c66-\\u0c6f\\u0c81-\\u0c83\\u0cbc\\u0cbe-\\u0cc4\\u0cc6-\\u0cc8\\u0cca-\\u0ccd\\u0cd5\\u0cd6\\u0ce2\\u0ce3\\u0ce6-\\u0cef\\u0cf3\\u0d00-\\u0d03\\u0d3b\\u0d3c\\u0d3e-\\u0d44\\u0d46-\\u0d48\\u0d4a-\\u0d4d\\u0d57\\u0d62\\u0d63\\u0d66-\\u0d6f\\u0d81-\\u0d83\\u0dca\\u0dcf-\\u0dd4\\u0dd6\\u0dd8-\\u0ddf\\u0de6-\\u0def\\u0df2\\u0df3\\u0e31\\u0e34-\\u0e3a\\u0e47-\\u0e4e\\u0e50-\\u0e59\\u0eb1\\u0eb4-\\u0ebc\\u0ec8-\\u0ece\\u0ed0-\\u0ed9\\u0f18\\u0f19\\u0f20-\\u0f29\\u0f35\\u0f37\\u0f39\\u0f3e\\u0f3f\\u0f71-\\u0f84\\u0f86\\u0f87\\u0f8d-\\u0f97\\u0f99-\\u0fbc\\u0fc6\\u102b-\\u103e\\u1040-\\u1049\\u1056-\\u1059\\u105e-\\u1060\\u1062-\\u1064\\u1067-\\u106d\\u1071-\\u1074\\u1082-\\u108d\\u108f-\\u109d\\u135d-\\u135f\\u1369-\\u1371\\u1712-\\u1715\\u1732-\\u1734\\u1752\\u1753\\u1772\\u1773\\u17b4-\\u17d3\\u17dd\\u17e0-\\u17e9\\u180b-\\u180d\\u180f-\\u1819\\u18a9\\u1920-\\u192b\\u1930-\\u193b\\u1946-\\u194f\\u19d0-\\u19da\\u1a17-\\u1a1b\\u1a55-\\u1a5e\\u1a60-\\u1a7c\\u1a7f-\\u1a89\\u1a90-\\u1a99\\u1ab0-\\u1abd\\u1abf-\\u1ace\\u1b00-\\u1b04\\u1b34-\\u1b44\\u1b50-\\u1b59\\u1b6b-\\u1b73\\u1b80-\\u1b82\\u1ba1-\\u1bad\\u1bb0-\\u1bb9\\u1be6-\\u1bf3\\u1c24-\\u1c37\\u1c40-\\u1c49\\u1c50-\\u1c59\\u1cd0-\\u1cd2\\u1cd4-\\u1ce8\\u1ced\\u1cf4\\u1cf7-\\u1cf9\\u1dc0-\\u1dff\\u200c\\u200d\\u203f\\u2040\\u2054\\u20d0-\\u20dc\\u20e1\\u20e5-\\u20f0\\u2cef-\\u2cf1\\u2d7f\\u2de0-\\u2dff\\u302a-\\u302f\\u3099\\u309a\\u30fb\\ua620-\\ua629\\ua66f\\ua674-\\ua67d\\ua69e\\ua69f\\ua6f0\\ua6f1\\ua802\\ua806\\ua80b\\ua823-\\ua827\\ua82c\\ua880\\ua881\\ua8b4-\\ua8c5\\ua8d0-\\ua8d9\\ua8e0-\\ua8f1\\ua8ff-\\ua909\\ua926-\\ua92d\\ua947-\\ua953\\ua980-\\ua983\\ua9b3-\\ua9c0\\ua9d0-\\ua9d9\\ua9e5\\ua9f0-\\ua9f9\\uaa29-\\uaa36\\uaa43\\uaa4c\\uaa4d\\uaa50-\\uaa59\\uaa7b-\\uaa7d\\uaab0\\uaab2-\\uaab4\\uaab7\\uaab8\\uaabe\\uaabf\\uaac1\\uaaeb-\\uaaef\\uaaf5\\uaaf6\\uabe3-\\uabea\\uabec\\uabed\\uabf0-\\uabf9\\ufb1e\\ufe00-\\ufe0f\\ufe20-\\ufe2f\\ufe33\\ufe34\\ufe4d-\\ufe4f\\uff10-\\uff19\\uff3f\\uff65\";\n\nconst nonASCIIidentifierStart = new RegExp(\n  \"[\" + nonASCIIidentifierStartChars + \"]\",\n);\nconst nonASCIIidentifier = new RegExp(\n  \"[\" + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"]\",\n);\n\nnonASCIIidentifierStartChars = nonASCIIidentifierChars = null;\n\n// These are a run-length and offset-encoded representation of the\n// >0xffff code points that are a valid part of identifiers. The\n// offset starts at 0x10000, and each pair of numbers represents an\n// offset to the next range, and then a size of the range. They were\n// generated by `scripts/generate-identifier-regex.cjs`.\n/* prettier-ignore */\nconst astralIdentifierStartCodes = [0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191];\n/* prettier-ignore */\nconst astralIdentifierCodes = [509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239];\n\n// This has a complexity linear to the value of the code. The\n// assumption is that looking up astral identifier characters is\n// rare.\nfunction isInAstralSet(code: number, set: readonly number[]): boolean {\n  let pos = 0x10000;\n  for (let i = 0, length = set.length; i < length; i += 2) {\n    pos += set[i];\n    if (pos > code) return false;\n\n    pos += set[i + 1];\n    if (pos >= code) return true;\n  }\n  return false;\n}\n\n// Test whether a given character code starts an identifier.\n\nexport function isIdentifierStart(code: number): boolean {\n  if (code < charCodes.uppercaseA) return code === charCodes.dollarSign;\n  if (code <= charCodes.uppercaseZ) return true;\n  if (code < charCodes.lowercaseA) return code === charCodes.underscore;\n  if (code <= charCodes.lowercaseZ) return true;\n  if (code <= 0xffff) {\n    return (\n      code >= 0xaa && nonASCIIidentifierStart.test(String.fromCharCode(code))\n    );\n  }\n  return isInAstralSet(code, astralIdentifierStartCodes);\n}\n\n// Test whether a given character is part of an identifier.\n\nexport function isIdentifierChar(code: number): boolean {\n  if (code < charCodes.digit0) return code === charCodes.dollarSign;\n  if (code < charCodes.colon) return true;\n  if (code < charCodes.uppercaseA) return false;\n  if (code <= charCodes.uppercaseZ) return true;\n  if (code < charCodes.lowercaseA) return code === charCodes.underscore;\n  if (code <= charCodes.lowercaseZ) return true;\n  if (code <= 0xffff) {\n    return code >= 0xaa && nonASCIIidentifier.test(String.fromCharCode(code));\n  }\n  return (\n    isInAstralSet(code, astralIdentifierStartCodes) ||\n    isInAstralSet(code, astralIdentifierCodes)\n  );\n}\n\n// Test whether a given string is a valid identifier name\n\nexport function isIdentifierName(name: string): boolean {\n  let isFirst = true;\n  for (let i = 0; i < name.length; i++) {\n    // The implementation is based on\n    // https://source.chromium.org/chromium/chromium/src/+/master:v8/src/builtins/builtins-string-gen.cc;l=1455;drc=221e331b49dfefadbc6fa40b0c68e6f97606d0b3;bpv=0;bpt=1\n    // We reimplement `codePointAt` because `codePointAt` is a V8 builtin which is not inlined by TurboFan (as of M91)\n    // since `name` is mostly ASCII, an inlined `charCodeAt` wins here\n    let cp = name.charCodeAt(i);\n    if ((cp & 0xfc00) === 0xd800 && i + 1 < name.length) {\n      const trail = name.charCodeAt(++i);\n      if ((trail & 0xfc00) === 0xdc00) {\n        cp = 0x10000 + ((cp & 0x3ff) << 10) + (trail & 0x3ff);\n      }\n    }\n    if (isFirst) {\n      isFirst = false;\n      if (!isIdentifierStart(cp)) {\n        return false;\n      }\n    } else if (!isIdentifierChar(cp)) {\n      return false;\n    }\n  }\n  return !isFirst;\n}\n", "const reservedWords = {\n  keyword: [\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"continue\",\n    \"debugger\",\n    \"default\",\n    \"do\",\n    \"else\",\n    \"finally\",\n    \"for\",\n    \"function\",\n    \"if\",\n    \"return\",\n    \"switch\",\n    \"throw\",\n    \"try\",\n    \"var\",\n    \"const\",\n    \"while\",\n    \"with\",\n    \"new\",\n    \"this\",\n    \"super\",\n    \"class\",\n    \"extends\",\n    \"export\",\n    \"import\",\n    \"null\",\n    \"true\",\n    \"false\",\n    \"in\",\n    \"instanceof\",\n    \"typeof\",\n    \"void\",\n    \"delete\",\n  ],\n  strict: [\n    \"implements\",\n    \"interface\",\n    \"let\",\n    \"package\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"static\",\n    \"yield\",\n  ],\n  strictBind: [\"eval\", \"arguments\"],\n};\nconst keywords = new Set(reservedWords.keyword);\nconst reservedWordsStrictSet = new Set(reservedWords.strict);\nconst reservedWordsStrictBindSet = new Set(reservedWords.strictBind);\n\n/**\n * Checks if word is a reserved word in non-strict mode\n */\nexport function isReservedWord(word: string, inModule: boolean): boolean {\n  return (inModule && word === \"await\") || word === \"enum\";\n}\n\n/**\n * Checks if word is a reserved word in non-binding strict mode\n *\n * Includes non-strict reserved words\n */\nexport function isStrictReservedWord(word: string, inModule: boolean): boolean {\n  return isReservedWord(word, inModule) || reservedWordsStrictSet.has(word);\n}\n\n/**\n * Checks if word is a reserved word in binding strict mode, but it is allowed as\n * a normal identifier.\n */\nexport function isStrictBindOnlyReservedWord(word: string): boolean {\n  return reservedWordsStrictBindSet.has(word);\n}\n\n/**\n * Checks if word is a reserved word in binding strict mode\n *\n * Includes non-strict reserved words and non-binding strict reserved words\n */\nexport function isStrictBindReservedWord(\n  word: string,\n  inModule: boolean,\n): boolean {\n  return (\n    isStrictReservedWord(word, inModule) || isStrictBindOnlyReservedWord(word)\n  );\n}\n\nexport function isKeyword(word: string): boolean {\n  return keywords.has(word);\n}\n", "export {\n  isIdentifierName,\n  isIdentifierChar,\n  isIdentifierStart,\n} from \"./identifier.ts\";\nexport {\n  isReservedWord,\n  isStrictBindOnlyReservedWord,\n  isStrictBindReservedWord,\n  isStrictReservedWord,\n  isKeyword,\n} from \"./keyword.ts\";\n", "let p = process || {}, argv = p.argv || [], env = p.env || {}\nlet isColorSupported =\n\t!(!!env.NO_COLOR || argv.includes(\"--no-color\")) &&\n\t(!!env.FORCE_COLOR || argv.includes(\"--color\") || p.platform === \"win32\" || ((p.stdout || {}).isTTY && env.TERM !== \"dumb\") || !!env.CI)\n\nlet formatter = (open, close, replace = open) =>\n\tinput => {\n\t\tlet string = \"\" + input, index = string.indexOf(close, open.length)\n\t\treturn ~index ? open + replaceClose(string, close, replace, index) + close : open + string + close\n\t}\n\nlet replaceClose = (string, close, replace, index) => {\n\tlet result = \"\", cursor = 0\n\tdo {\n\t\tresult += string.substring(cursor, index) + replace\n\t\tcursor = index + close.length\n\t\tindex = string.indexOf(close, cursor)\n\t} while (~index)\n\treturn result + string.substring(cursor)\n}\n\nlet createColors = (enabled = isColorSupported) => {\n\tlet f = enabled ? formatter : () => String\n\treturn {\n\t\tisColorSupported: enabled,\n\t\treset: f(\"\\x1b[0m\", \"\\x1b[0m\"),\n\t\tbold: f(\"\\x1b[1m\", \"\\x1b[22m\", \"\\x1b[22m\\x1b[1m\"),\n\t\tdim: f(\"\\x1b[2m\", \"\\x1b[22m\", \"\\x1b[22m\\x1b[2m\"),\n\t\titalic: f(\"\\x1b[3m\", \"\\x1b[23m\"),\n\t\tunderline: f(\"\\x1b[4m\", \"\\x1b[24m\"),\n\t\tinverse: f(\"\\x1b[7m\", \"\\x1b[27m\"),\n\t\thidden: f(\"\\x1b[8m\", \"\\x1b[28m\"),\n\t\tstrikethrough: f(\"\\x1b[9m\", \"\\x1b[29m\"),\n\n\t\tblack: f(\"\\x1b[30m\", \"\\x1b[39m\"),\n\t\tred: f(\"\\x1b[31m\", \"\\x1b[39m\"),\n\t\tgreen: f(\"\\x1b[32m\", \"\\x1b[39m\"),\n\t\tyellow: f(\"\\x1b[33m\", \"\\x1b[39m\"),\n\t\tblue: f(\"\\x1b[34m\", \"\\x1b[39m\"),\n\t\tmagenta: f(\"\\x1b[35m\", \"\\x1b[39m\"),\n\t\tcyan: f(\"\\x1b[36m\", \"\\x1b[39m\"),\n\t\twhite: f(\"\\x1b[37m\", \"\\x1b[39m\"),\n\t\tgray: f(\"\\x1b[90m\", \"\\x1b[39m\"),\n\n\t\tbgBlack: f(\"\\x1b[40m\", \"\\x1b[49m\"),\n\t\tbgRed: f(\"\\x1b[41m\", \"\\x1b[49m\"),\n\t\tbgGreen: f(\"\\x1b[42m\", \"\\x1b[49m\"),\n\t\tbgYellow: f(\"\\x1b[43m\", \"\\x1b[49m\"),\n\t\tbgBlue: f(\"\\x1b[44m\", \"\\x1b[49m\"),\n\t\tbgMagenta: f(\"\\x1b[45m\", \"\\x1b[49m\"),\n\t\tbgCyan: f(\"\\x1b[46m\", \"\\x1b[49m\"),\n\t\tbgWhite: f(\"\\x1b[47m\", \"\\x1b[49m\"),\n\n\t\tblackBright: f(\"\\x1b[90m\", \"\\x1b[39m\"),\n\t\tredBright: f(\"\\x1b[91m\", \"\\x1b[39m\"),\n\t\tgreenBright: f(\"\\x1b[92m\", \"\\x1b[39m\"),\n\t\tyellowBright: f(\"\\x1b[93m\", \"\\x1b[39m\"),\n\t\tblueBright: f(\"\\x1b[94m\", \"\\x1b[39m\"),\n\t\tmagentaBright: f(\"\\x1b[95m\", \"\\x1b[39m\"),\n\t\tcyanBright: f(\"\\x1b[96m\", \"\\x1b[39m\"),\n\t\twhiteBright: f(\"\\x1b[97m\", \"\\x1b[39m\"),\n\n\t\tbgBlackBright: f(\"\\x1b[100m\", \"\\x1b[49m\"),\n\t\tbgRedBright: f(\"\\x1b[101m\", \"\\x1b[49m\"),\n\t\tbgGreenBright: f(\"\\x1b[102m\", \"\\x1b[49m\"),\n\t\tbgYellowBright: f(\"\\x1b[103m\", \"\\x1b[49m\"),\n\t\tbgBlueBright: f(\"\\x1b[104m\", \"\\x1b[49m\"),\n\t\tbgMagentaBright: f(\"\\x1b[105m\", \"\\x1b[49m\"),\n\t\tbgCyanBright: f(\"\\x1b[106m\", \"\\x1b[49m\"),\n\t\tbgWhiteBright: f(\"\\x1b[107m\", \"\\x1b[49m\"),\n\t}\n}\n\nmodule.exports = createColors()\nmodule.exports.createColors = createColors\n", "'use strict';\n\nvar matchOperatorsRe = /[|\\\\{}()[\\]^$+*?.]/g;\n\nmodule.exports = function (str) {\n\tif (typeof str !== 'string') {\n\t\tthrow new TypeError('Expected a string');\n\t}\n\n\treturn str.replace(matchOperatorsRe, '\\\\$&');\n};\n", "'use strict'\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n", "/* MIT license */\nvar cssKeywords = require('color-name');\n\n// NOTE: conversions should only return primitive values (i.e. arrays, or\n//       values that give correct `typeof` results).\n//       do not use box values types (i.e. Number(), String(), etc.)\n\nvar reverseKeywords = {};\nfor (var key in cssKeywords) {\n\tif (cssKeywords.hasOwnProperty(key)) {\n\t\treverseKeywords[cssKeywords[key]] = key;\n\t}\n}\n\nvar convert = module.exports = {\n\trgb: {channels: 3, labels: 'rgb'},\n\thsl: {channels: 3, labels: 'hsl'},\n\thsv: {channels: 3, labels: 'hsv'},\n\thwb: {channels: 3, labels: 'hwb'},\n\tcmyk: {channels: 4, labels: 'cmyk'},\n\txyz: {channels: 3, labels: 'xyz'},\n\tlab: {channels: 3, labels: 'lab'},\n\tlch: {channels: 3, labels: 'lch'},\n\thex: {channels: 1, labels: ['hex']},\n\tkeyword: {channels: 1, labels: ['keyword']},\n\tansi16: {channels: 1, labels: ['ansi16']},\n\tansi256: {channels: 1, labels: ['ansi256']},\n\thcg: {channels: 3, labels: ['h', 'c', 'g']},\n\tapple: {channels: 3, labels: ['r16', 'g16', 'b16']},\n\tgray: {channels: 1, labels: ['gray']}\n};\n\n// hide .channels and .labels properties\nfor (var model in convert) {\n\tif (convert.hasOwnProperty(model)) {\n\t\tif (!('channels' in convert[model])) {\n\t\t\tthrow new Error('missing channels property: ' + model);\n\t\t}\n\n\t\tif (!('labels' in convert[model])) {\n\t\t\tthrow new Error('missing channel labels property: ' + model);\n\t\t}\n\n\t\tif (convert[model].labels.length !== convert[model].channels) {\n\t\t\tthrow new Error('channel and label counts mismatch: ' + model);\n\t\t}\n\n\t\tvar channels = convert[model].channels;\n\t\tvar labels = convert[model].labels;\n\t\tdelete convert[model].channels;\n\t\tdelete convert[model].labels;\n\t\tObject.defineProperty(convert[model], 'channels', {value: channels});\n\t\tObject.defineProperty(convert[model], 'labels', {value: labels});\n\t}\n}\n\nconvert.rgb.hsl = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar min = Math.min(r, g, b);\n\tvar max = Math.max(r, g, b);\n\tvar delta = max - min;\n\tvar h;\n\tvar s;\n\tvar l;\n\n\tif (max === min) {\n\t\th = 0;\n\t} else if (r === max) {\n\t\th = (g - b) / delta;\n\t} else if (g === max) {\n\t\th = 2 + (b - r) / delta;\n\t} else if (b === max) {\n\t\th = 4 + (r - g) / delta;\n\t}\n\n\th = Math.min(h * 60, 360);\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tl = (min + max) / 2;\n\n\tif (max === min) {\n\t\ts = 0;\n\t} else if (l <= 0.5) {\n\t\ts = delta / (max + min);\n\t} else {\n\t\ts = delta / (2 - max - min);\n\t}\n\n\treturn [h, s * 100, l * 100];\n};\n\nconvert.rgb.hsv = function (rgb) {\n\tvar rdif;\n\tvar gdif;\n\tvar bdif;\n\tvar h;\n\tvar s;\n\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar v = Math.max(r, g, b);\n\tvar diff = v - Math.min(r, g, b);\n\tvar diffc = function (c) {\n\t\treturn (v - c) / 6 / diff + 1 / 2;\n\t};\n\n\tif (diff === 0) {\n\t\th = s = 0;\n\t} else {\n\t\ts = diff / v;\n\t\trdif = diffc(r);\n\t\tgdif = diffc(g);\n\t\tbdif = diffc(b);\n\n\t\tif (r === v) {\n\t\t\th = bdif - gdif;\n\t\t} else if (g === v) {\n\t\t\th = (1 / 3) + rdif - bdif;\n\t\t} else if (b === v) {\n\t\t\th = (2 / 3) + gdif - rdif;\n\t\t}\n\t\tif (h < 0) {\n\t\t\th += 1;\n\t\t} else if (h > 1) {\n\t\t\th -= 1;\n\t\t}\n\t}\n\n\treturn [\n\t\th * 360,\n\t\ts * 100,\n\t\tv * 100\n\t];\n};\n\nconvert.rgb.hwb = function (rgb) {\n\tvar r = rgb[0];\n\tvar g = rgb[1];\n\tvar b = rgb[2];\n\tvar h = convert.rgb.hsl(rgb)[0];\n\tvar w = 1 / 255 * Math.min(r, Math.min(g, b));\n\n\tb = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n\n\treturn [h, w * 100, b * 100];\n};\n\nconvert.rgb.cmyk = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar c;\n\tvar m;\n\tvar y;\n\tvar k;\n\n\tk = Math.min(1 - r, 1 - g, 1 - b);\n\tc = (1 - r - k) / (1 - k) || 0;\n\tm = (1 - g - k) / (1 - k) || 0;\n\ty = (1 - b - k) / (1 - k) || 0;\n\n\treturn [c * 100, m * 100, y * 100, k * 100];\n};\n\n/**\n * See https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n * */\nfunction comparativeDistance(x, y) {\n\treturn (\n\t\tMath.pow(x[0] - y[0], 2) +\n\t\tMath.pow(x[1] - y[1], 2) +\n\t\tMath.pow(x[2] - y[2], 2)\n\t);\n}\n\nconvert.rgb.keyword = function (rgb) {\n\tvar reversed = reverseKeywords[rgb];\n\tif (reversed) {\n\t\treturn reversed;\n\t}\n\n\tvar currentClosestDistance = Infinity;\n\tvar currentClosestKeyword;\n\n\tfor (var keyword in cssKeywords) {\n\t\tif (cssKeywords.hasOwnProperty(keyword)) {\n\t\t\tvar value = cssKeywords[keyword];\n\n\t\t\t// Compute comparative distance\n\t\t\tvar distance = comparativeDistance(rgb, value);\n\n\t\t\t// Check if its less, if so set as closest\n\t\t\tif (distance < currentClosestDistance) {\n\t\t\t\tcurrentClosestDistance = distance;\n\t\t\t\tcurrentClosestKeyword = keyword;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn currentClosestKeyword;\n};\n\nconvert.keyword.rgb = function (keyword) {\n\treturn cssKeywords[keyword];\n};\n\nconvert.rgb.xyz = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\n\t// assume sRGB\n\tr = r > 0.04045 ? Math.pow(((r + 0.055) / 1.055), 2.4) : (r / 12.92);\n\tg = g > 0.04045 ? Math.pow(((g + 0.055) / 1.055), 2.4) : (g / 12.92);\n\tb = b > 0.04045 ? Math.pow(((b + 0.055) / 1.055), 2.4) : (b / 12.92);\n\n\tvar x = (r * 0.4124) + (g * 0.3576) + (b * 0.1805);\n\tvar y = (r * 0.2126) + (g * 0.7152) + (b * 0.0722);\n\tvar z = (r * 0.0193) + (g * 0.1192) + (b * 0.9505);\n\n\treturn [x * 100, y * 100, z * 100];\n};\n\nconvert.rgb.lab = function (rgb) {\n\tvar xyz = convert.rgb.xyz(rgb);\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.hsl.rgb = function (hsl) {\n\tvar h = hsl[0] / 360;\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar t1;\n\tvar t2;\n\tvar t3;\n\tvar rgb;\n\tvar val;\n\n\tif (s === 0) {\n\t\tval = l * 255;\n\t\treturn [val, val, val];\n\t}\n\n\tif (l < 0.5) {\n\t\tt2 = l * (1 + s);\n\t} else {\n\t\tt2 = l + s - l * s;\n\t}\n\n\tt1 = 2 * l - t2;\n\n\trgb = [0, 0, 0];\n\tfor (var i = 0; i < 3; i++) {\n\t\tt3 = h + 1 / 3 * -(i - 1);\n\t\tif (t3 < 0) {\n\t\t\tt3++;\n\t\t}\n\t\tif (t3 > 1) {\n\t\t\tt3--;\n\t\t}\n\n\t\tif (6 * t3 < 1) {\n\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t} else if (2 * t3 < 1) {\n\t\t\tval = t2;\n\t\t} else if (3 * t3 < 2) {\n\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t} else {\n\t\t\tval = t1;\n\t\t}\n\n\t\trgb[i] = val * 255;\n\t}\n\n\treturn rgb;\n};\n\nconvert.hsl.hsv = function (hsl) {\n\tvar h = hsl[0];\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar smin = s;\n\tvar lmin = Math.max(l, 0.01);\n\tvar sv;\n\tvar v;\n\n\tl *= 2;\n\ts *= (l <= 1) ? l : 2 - l;\n\tsmin *= lmin <= 1 ? lmin : 2 - lmin;\n\tv = (l + s) / 2;\n\tsv = l === 0 ? (2 * smin) / (lmin + smin) : (2 * s) / (l + s);\n\n\treturn [h, sv * 100, v * 100];\n};\n\nconvert.hsv.rgb = function (hsv) {\n\tvar h = hsv[0] / 60;\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar hi = Math.floor(h) % 6;\n\n\tvar f = h - Math.floor(h);\n\tvar p = 255 * v * (1 - s);\n\tvar q = 255 * v * (1 - (s * f));\n\tvar t = 255 * v * (1 - (s * (1 - f)));\n\tv *= 255;\n\n\tswitch (hi) {\n\t\tcase 0:\n\t\t\treturn [v, t, p];\n\t\tcase 1:\n\t\t\treturn [q, v, p];\n\t\tcase 2:\n\t\t\treturn [p, v, t];\n\t\tcase 3:\n\t\t\treturn [p, q, v];\n\t\tcase 4:\n\t\t\treturn [t, p, v];\n\t\tcase 5:\n\t\t\treturn [v, p, q];\n\t}\n};\n\nconvert.hsv.hsl = function (hsv) {\n\tvar h = hsv[0];\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar vmin = Math.max(v, 0.01);\n\tvar lmin;\n\tvar sl;\n\tvar l;\n\n\tl = (2 - s) * v;\n\tlmin = (2 - s) * vmin;\n\tsl = s * vmin;\n\tsl /= (lmin <= 1) ? lmin : 2 - lmin;\n\tsl = sl || 0;\n\tl /= 2;\n\n\treturn [h, sl * 100, l * 100];\n};\n\n// http://dev.w3.org/csswg/css-color/#hwb-to-rgb\nconvert.hwb.rgb = function (hwb) {\n\tvar h = hwb[0] / 360;\n\tvar wh = hwb[1] / 100;\n\tvar bl = hwb[2] / 100;\n\tvar ratio = wh + bl;\n\tvar i;\n\tvar v;\n\tvar f;\n\tvar n;\n\n\t// wh + bl cant be > 1\n\tif (ratio > 1) {\n\t\twh /= ratio;\n\t\tbl /= ratio;\n\t}\n\n\ti = Math.floor(6 * h);\n\tv = 1 - bl;\n\tf = 6 * h - i;\n\n\tif ((i & 0x01) !== 0) {\n\t\tf = 1 - f;\n\t}\n\n\tn = wh + f * (v - wh); // linear interpolation\n\n\tvar r;\n\tvar g;\n\tvar b;\n\tswitch (i) {\n\t\tdefault:\n\t\tcase 6:\n\t\tcase 0: r = v; g = n; b = wh; break;\n\t\tcase 1: r = n; g = v; b = wh; break;\n\t\tcase 2: r = wh; g = v; b = n; break;\n\t\tcase 3: r = wh; g = n; b = v; break;\n\t\tcase 4: r = n; g = wh; b = v; break;\n\t\tcase 5: r = v; g = wh; b = n; break;\n\t}\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.cmyk.rgb = function (cmyk) {\n\tvar c = cmyk[0] / 100;\n\tvar m = cmyk[1] / 100;\n\tvar y = cmyk[2] / 100;\n\tvar k = cmyk[3] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = 1 - Math.min(1, c * (1 - k) + k);\n\tg = 1 - Math.min(1, m * (1 - k) + k);\n\tb = 1 - Math.min(1, y * (1 - k) + k);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.rgb = function (xyz) {\n\tvar x = xyz[0] / 100;\n\tvar y = xyz[1] / 100;\n\tvar z = xyz[2] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = (x * 3.2406) + (y * -1.5372) + (z * -0.4986);\n\tg = (x * -0.9689) + (y * 1.8758) + (z * 0.0415);\n\tb = (x * 0.0557) + (y * -0.2040) + (z * 1.0570);\n\n\t// assume sRGB\n\tr = r > 0.0031308\n\t\t? ((1.055 * Math.pow(r, 1.0 / 2.4)) - 0.055)\n\t\t: r * 12.92;\n\n\tg = g > 0.0031308\n\t\t? ((1.055 * Math.pow(g, 1.0 / 2.4)) - 0.055)\n\t\t: g * 12.92;\n\n\tb = b > 0.0031308\n\t\t? ((1.055 * Math.pow(b, 1.0 / 2.4)) - 0.055)\n\t\t: b * 12.92;\n\n\tr = Math.min(Math.max(0, r), 1);\n\tg = Math.min(Math.max(0, g), 1);\n\tb = Math.min(Math.max(0, b), 1);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.lab = function (xyz) {\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.lab.xyz = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar x;\n\tvar y;\n\tvar z;\n\n\ty = (l + 16) / 116;\n\tx = a / 500 + y;\n\tz = y - b / 200;\n\n\tvar y2 = Math.pow(y, 3);\n\tvar x2 = Math.pow(x, 3);\n\tvar z2 = Math.pow(z, 3);\n\ty = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n\tx = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n\tz = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n\n\tx *= 95.047;\n\ty *= 100;\n\tz *= 108.883;\n\n\treturn [x, y, z];\n};\n\nconvert.lab.lch = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar hr;\n\tvar h;\n\tvar c;\n\n\thr = Math.atan2(b, a);\n\th = hr * 360 / 2 / Math.PI;\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tc = Math.sqrt(a * a + b * b);\n\n\treturn [l, c, h];\n};\n\nconvert.lch.lab = function (lch) {\n\tvar l = lch[0];\n\tvar c = lch[1];\n\tvar h = lch[2];\n\tvar a;\n\tvar b;\n\tvar hr;\n\n\thr = h / 360 * 2 * Math.PI;\n\ta = c * Math.cos(hr);\n\tb = c * Math.sin(hr);\n\n\treturn [l, a, b];\n};\n\nconvert.rgb.ansi16 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\tvar value = 1 in arguments ? arguments[1] : convert.rgb.hsv(args)[2]; // hsv -> ansi16 optimization\n\n\tvalue = Math.round(value / 50);\n\n\tif (value === 0) {\n\t\treturn 30;\n\t}\n\n\tvar ansi = 30\n\t\t+ ((Math.round(b / 255) << 2)\n\t\t| (Math.round(g / 255) << 1)\n\t\t| Math.round(r / 255));\n\n\tif (value === 2) {\n\t\tansi += 60;\n\t}\n\n\treturn ansi;\n};\n\nconvert.hsv.ansi16 = function (args) {\n\t// optimization here; we already know the value and don't need to get\n\t// it converted for us.\n\treturn convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\n\nconvert.rgb.ansi256 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\n\t// we use the extended greyscale palette here, with the exception of\n\t// black and white. normal palette only has 4 greyscale shades.\n\tif (r === g && g === b) {\n\t\tif (r < 8) {\n\t\t\treturn 16;\n\t\t}\n\n\t\tif (r > 248) {\n\t\t\treturn 231;\n\t\t}\n\n\t\treturn Math.round(((r - 8) / 247) * 24) + 232;\n\t}\n\n\tvar ansi = 16\n\t\t+ (36 * Math.round(r / 255 * 5))\n\t\t+ (6 * Math.round(g / 255 * 5))\n\t\t+ Math.round(b / 255 * 5);\n\n\treturn ansi;\n};\n\nconvert.ansi16.rgb = function (args) {\n\tvar color = args % 10;\n\n\t// handle greyscale\n\tif (color === 0 || color === 7) {\n\t\tif (args > 50) {\n\t\t\tcolor += 3.5;\n\t\t}\n\n\t\tcolor = color / 10.5 * 255;\n\n\t\treturn [color, color, color];\n\t}\n\n\tvar mult = (~~(args > 50) + 1) * 0.5;\n\tvar r = ((color & 1) * mult) * 255;\n\tvar g = (((color >> 1) & 1) * mult) * 255;\n\tvar b = (((color >> 2) & 1) * mult) * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.ansi256.rgb = function (args) {\n\t// handle greyscale\n\tif (args >= 232) {\n\t\tvar c = (args - 232) * 10 + 8;\n\t\treturn [c, c, c];\n\t}\n\n\targs -= 16;\n\n\tvar rem;\n\tvar r = Math.floor(args / 36) / 5 * 255;\n\tvar g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n\tvar b = (rem % 6) / 5 * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hex = function (args) {\n\tvar integer = ((Math.round(args[0]) & 0xFF) << 16)\n\t\t+ ((Math.round(args[1]) & 0xFF) << 8)\n\t\t+ (Math.round(args[2]) & 0xFF);\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.hex.rgb = function (args) {\n\tvar match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n\tif (!match) {\n\t\treturn [0, 0, 0];\n\t}\n\n\tvar colorString = match[0];\n\n\tif (match[0].length === 3) {\n\t\tcolorString = colorString.split('').map(function (char) {\n\t\t\treturn char + char;\n\t\t}).join('');\n\t}\n\n\tvar integer = parseInt(colorString, 16);\n\tvar r = (integer >> 16) & 0xFF;\n\tvar g = (integer >> 8) & 0xFF;\n\tvar b = integer & 0xFF;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hcg = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar max = Math.max(Math.max(r, g), b);\n\tvar min = Math.min(Math.min(r, g), b);\n\tvar chroma = (max - min);\n\tvar grayscale;\n\tvar hue;\n\n\tif (chroma < 1) {\n\t\tgrayscale = min / (1 - chroma);\n\t} else {\n\t\tgrayscale = 0;\n\t}\n\n\tif (chroma <= 0) {\n\t\thue = 0;\n\t} else\n\tif (max === r) {\n\t\thue = ((g - b) / chroma) % 6;\n\t} else\n\tif (max === g) {\n\t\thue = 2 + (b - r) / chroma;\n\t} else {\n\t\thue = 4 + (r - g) / chroma + 4;\n\t}\n\n\thue /= 6;\n\thue %= 1;\n\n\treturn [hue * 360, chroma * 100, grayscale * 100];\n};\n\nconvert.hsl.hcg = function (hsl) {\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar c = 1;\n\tvar f = 0;\n\n\tif (l < 0.5) {\n\t\tc = 2.0 * s * l;\n\t} else {\n\t\tc = 2.0 * s * (1.0 - l);\n\t}\n\n\tif (c < 1.0) {\n\t\tf = (l - 0.5 * c) / (1.0 - c);\n\t}\n\n\treturn [hsl[0], c * 100, f * 100];\n};\n\nconvert.hsv.hcg = function (hsv) {\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\n\tvar c = s * v;\n\tvar f = 0;\n\n\tif (c < 1.0) {\n\t\tf = (v - c) / (1 - c);\n\t}\n\n\treturn [hsv[0], c * 100, f * 100];\n};\n\nconvert.hcg.rgb = function (hcg) {\n\tvar h = hcg[0] / 360;\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tif (c === 0.0) {\n\t\treturn [g * 255, g * 255, g * 255];\n\t}\n\n\tvar pure = [0, 0, 0];\n\tvar hi = (h % 1) * 6;\n\tvar v = hi % 1;\n\tvar w = 1 - v;\n\tvar mg = 0;\n\n\tswitch (Math.floor(hi)) {\n\t\tcase 0:\n\t\t\tpure[0] = 1; pure[1] = v; pure[2] = 0; break;\n\t\tcase 1:\n\t\t\tpure[0] = w; pure[1] = 1; pure[2] = 0; break;\n\t\tcase 2:\n\t\t\tpure[0] = 0; pure[1] = 1; pure[2] = v; break;\n\t\tcase 3:\n\t\t\tpure[0] = 0; pure[1] = w; pure[2] = 1; break;\n\t\tcase 4:\n\t\t\tpure[0] = v; pure[1] = 0; pure[2] = 1; break;\n\t\tdefault:\n\t\t\tpure[0] = 1; pure[1] = 0; pure[2] = w;\n\t}\n\n\tmg = (1.0 - c) * g;\n\n\treturn [\n\t\t(c * pure[0] + mg) * 255,\n\t\t(c * pure[1] + mg) * 255,\n\t\t(c * pure[2] + mg) * 255\n\t];\n};\n\nconvert.hcg.hsv = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar v = c + g * (1.0 - c);\n\tvar f = 0;\n\n\tif (v > 0.0) {\n\t\tf = c / v;\n\t}\n\n\treturn [hcg[0], f * 100, v * 100];\n};\n\nconvert.hcg.hsl = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar l = g * (1.0 - c) + 0.5 * c;\n\tvar s = 0;\n\n\tif (l > 0.0 && l < 0.5) {\n\t\ts = c / (2 * l);\n\t} else\n\tif (l >= 0.5 && l < 1.0) {\n\t\ts = c / (2 * (1 - l));\n\t}\n\n\treturn [hcg[0], s * 100, l * 100];\n};\n\nconvert.hcg.hwb = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\tvar v = c + g * (1.0 - c);\n\treturn [hcg[0], (v - c) * 100, (1 - v) * 100];\n};\n\nconvert.hwb.hcg = function (hwb) {\n\tvar w = hwb[1] / 100;\n\tvar b = hwb[2] / 100;\n\tvar v = 1 - b;\n\tvar c = v - w;\n\tvar g = 0;\n\n\tif (c < 1) {\n\t\tg = (v - c) / (1 - c);\n\t}\n\n\treturn [hwb[0], c * 100, g * 100];\n};\n\nconvert.apple.rgb = function (apple) {\n\treturn [(apple[0] / 65535) * 255, (apple[1] / 65535) * 255, (apple[2] / 65535) * 255];\n};\n\nconvert.rgb.apple = function (rgb) {\n\treturn [(rgb[0] / 255) * 65535, (rgb[1] / 255) * 65535, (rgb[2] / 255) * 65535];\n};\n\nconvert.gray.rgb = function (args) {\n\treturn [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n};\n\nconvert.gray.hsl = convert.gray.hsv = function (args) {\n\treturn [0, 0, args[0]];\n};\n\nconvert.gray.hwb = function (gray) {\n\treturn [0, 100, gray[0]];\n};\n\nconvert.gray.cmyk = function (gray) {\n\treturn [0, 0, 0, gray[0]];\n};\n\nconvert.gray.lab = function (gray) {\n\treturn [gray[0], 0, 0];\n};\n\nconvert.gray.hex = function (gray) {\n\tvar val = Math.round(gray[0] / 100 * 255) & 0xFF;\n\tvar integer = (val << 16) + (val << 8) + val;\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.rgb.gray = function (rgb) {\n\tvar val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n\treturn [val / 255 * 100];\n};\n", "var conversions = require('./conversions');\n\n/*\n\tthis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/\n\nfunction buildGraph() {\n\tvar graph = {};\n\t// https://jsperf.com/object-keys-vs-for-in-with-closure/3\n\tvar models = Object.keys(conversions);\n\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tgraph[models[i]] = {\n\t\t\t// http://jsperf.com/1-vs-infinity\n\t\t\t// micro-opt, but this is simple.\n\t\t\tdistance: -1,\n\t\t\tparent: null\n\t\t};\n\t}\n\n\treturn graph;\n}\n\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n\tvar graph = buildGraph();\n\tvar queue = [fromModel]; // unshift -> queue -> pop\n\n\tgraph[fromModel].distance = 0;\n\n\twhile (queue.length) {\n\t\tvar current = queue.pop();\n\t\tvar adjacents = Object.keys(conversions[current]);\n\n\t\tfor (var len = adjacents.length, i = 0; i < len; i++) {\n\t\t\tvar adjacent = adjacents[i];\n\t\t\tvar node = graph[adjacent];\n\n\t\t\tif (node.distance === -1) {\n\t\t\t\tnode.distance = graph[current].distance + 1;\n\t\t\t\tnode.parent = current;\n\t\t\t\tqueue.unshift(adjacent);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn graph;\n}\n\nfunction link(from, to) {\n\treturn function (args) {\n\t\treturn to(from(args));\n\t};\n}\n\nfunction wrapConversion(toModel, graph) {\n\tvar path = [graph[toModel].parent, toModel];\n\tvar fn = conversions[graph[toModel].parent][toModel];\n\n\tvar cur = graph[toModel].parent;\n\twhile (graph[cur].parent) {\n\t\tpath.unshift(graph[cur].parent);\n\t\tfn = link(conversions[graph[cur].parent][cur], fn);\n\t\tcur = graph[cur].parent;\n\t}\n\n\tfn.conversion = path;\n\treturn fn;\n}\n\nmodule.exports = function (fromModel) {\n\tvar graph = deriveBFS(fromModel);\n\tvar conversion = {};\n\n\tvar models = Object.keys(graph);\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tvar toModel = models[i];\n\t\tvar node = graph[toModel];\n\n\t\tif (node.parent === null) {\n\t\t\t// no possible conversion, or this node is the source model.\n\t\t\tcontinue;\n\t\t}\n\n\t\tconversion[toModel] = wrapConversion(toModel, graph);\n\t}\n\n\treturn conversion;\n};\n\n", "var conversions = require('./conversions');\nvar route = require('./route');\n\nvar convert = {};\n\nvar models = Object.keys(conversions);\n\nfunction wrapRaw(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\treturn fn(args);\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nfunction wrapRounded(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\tvar result = fn(args);\n\n\t\t// we're assuming the result is an array here.\n\t\t// see notice in conversions.js; don't use box types\n\t\t// in conversion functions.\n\t\tif (typeof result === 'object') {\n\t\t\tfor (var len = result.length, i = 0; i < len; i++) {\n\t\t\t\tresult[i] = Math.round(result[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nmodels.forEach(function (fromModel) {\n\tconvert[fromModel] = {};\n\n\tObject.defineProperty(convert[fromModel], 'channels', {value: conversions[fromModel].channels});\n\tObject.defineProperty(convert[fromModel], 'labels', {value: conversions[fromModel].labels});\n\n\tvar routes = route(fromModel);\n\tvar routeModels = Object.keys(routes);\n\n\trouteModels.forEach(function (toModel) {\n\t\tvar fn = routes[toModel];\n\n\t\tconvert[fromModel][toModel] = wrapRounded(fn);\n\t\tconvert[fromModel][toModel].raw = wrapRaw(fn);\n\t});\n});\n\nmodule.exports = convert;\n", "'use strict';\nconst colorConvert = require('color-convert');\n\nconst wrapAnsi16 = (fn, offset) => function () {\n\tconst code = fn.apply(colorConvert, arguments);\n\treturn `\\u001B[${code + offset}m`;\n};\n\nconst wrapAnsi256 = (fn, offset) => function () {\n\tconst code = fn.apply(colorConvert, arguments);\n\treturn `\\u001B[${38 + offset};5;${code}m`;\n};\n\nconst wrapAnsi16m = (fn, offset) => function () {\n\tconst rgb = fn.apply(colorConvert, arguments);\n\treturn `\\u001B[${38 + offset};2;${rgb[0]};${rgb[1]};${rgb[2]}m`;\n};\n\nfunction assembleStyles() {\n\tconst codes = new Map();\n\tconst styles = {\n\t\tmodifier: {\n\t\t\treset: [0, 0],\n\t\t\t// 21 isn't widely supported and 22 does the same thing\n\t\t\tbold: [1, 22],\n\t\t\tdim: [2, 22],\n\t\t\titalic: [3, 23],\n\t\t\tunderline: [4, 24],\n\t\t\tinverse: [7, 27],\n\t\t\thidden: [8, 28],\n\t\t\tstrikethrough: [9, 29]\n\t\t},\n\t\tcolor: {\n\t\t\tblack: [30, 39],\n\t\t\tred: [31, 39],\n\t\t\tgreen: [32, 39],\n\t\t\tyellow: [33, 39],\n\t\t\tblue: [34, 39],\n\t\t\tmagenta: [35, 39],\n\t\t\tcyan: [36, 39],\n\t\t\twhite: [37, 39],\n\t\t\tgray: [90, 39],\n\n\t\t\t// Bright color\n\t\t\tredBright: [91, 39],\n\t\t\tgreenBright: [92, 39],\n\t\t\tyellowBright: [93, 39],\n\t\t\tblueBright: [94, 39],\n\t\t\tmagentaBright: [95, 39],\n\t\t\tcyanBright: [96, 39],\n\t\t\twhiteBright: [97, 39]\n\t\t},\n\t\tbgColor: {\n\t\t\tbgBlack: [40, 49],\n\t\t\tbgRed: [41, 49],\n\t\t\tbgGreen: [42, 49],\n\t\t\tbgYellow: [43, 49],\n\t\t\tbgBlue: [44, 49],\n\t\t\tbgMagenta: [45, 49],\n\t\t\tbgCyan: [46, 49],\n\t\t\tbgWhite: [47, 49],\n\n\t\t\t// Bright color\n\t\t\tbgBlackBright: [100, 49],\n\t\t\tbgRedBright: [101, 49],\n\t\t\tbgGreenBright: [102, 49],\n\t\t\tbgYellowBright: [103, 49],\n\t\t\tbgBlueBright: [104, 49],\n\t\t\tbgMagentaBright: [105, 49],\n\t\t\tbgCyanBright: [106, 49],\n\t\t\tbgWhiteBright: [107, 49]\n\t\t}\n\t};\n\n\t// Fix humans\n\tstyles.color.grey = styles.color.gray;\n\n\tfor (const groupName of Object.keys(styles)) {\n\t\tconst group = styles[groupName];\n\n\t\tfor (const styleName of Object.keys(group)) {\n\t\t\tconst style = group[styleName];\n\n\t\t\tstyles[styleName] = {\n\t\t\t\topen: `\\u001B[${style[0]}m`,\n\t\t\t\tclose: `\\u001B[${style[1]}m`\n\t\t\t};\n\n\t\t\tgroup[styleName] = styles[styleName];\n\n\t\t\tcodes.set(style[0], style[1]);\n\t\t}\n\n\t\tObject.defineProperty(styles, groupName, {\n\t\t\tvalue: group,\n\t\t\tenumerable: false\n\t\t});\n\n\t\tObject.defineProperty(styles, 'codes', {\n\t\t\tvalue: codes,\n\t\t\tenumerable: false\n\t\t});\n\t}\n\n\tconst ansi2ansi = n => n;\n\tconst rgb2rgb = (r, g, b) => [r, g, b];\n\n\tstyles.color.close = '\\u001B[39m';\n\tstyles.bgColor.close = '\\u001B[49m';\n\n\tstyles.color.ansi = {\n\t\tansi: wrapAnsi16(ansi2ansi, 0)\n\t};\n\tstyles.color.ansi256 = {\n\t\tansi256: wrapAnsi256(ansi2ansi, 0)\n\t};\n\tstyles.color.ansi16m = {\n\t\trgb: wrapAnsi16m(rgb2rgb, 0)\n\t};\n\n\tstyles.bgColor.ansi = {\n\t\tansi: wrapAnsi16(ansi2ansi, 10)\n\t};\n\tstyles.bgColor.ansi256 = {\n\t\tansi256: wrapAnsi256(ansi2ansi, 10)\n\t};\n\tstyles.bgColor.ansi16m = {\n\t\trgb: wrapAnsi16m(rgb2rgb, 10)\n\t};\n\n\tfor (let key of Object.keys(colorConvert)) {\n\t\tif (typeof colorConvert[key] !== 'object') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst suite = colorConvert[key];\n\n\t\tif (key === 'ansi16') {\n\t\t\tkey = 'ansi';\n\t\t}\n\n\t\tif ('ansi16' in suite) {\n\t\t\tstyles.color.ansi[key] = wrapAnsi16(suite.ansi16, 0);\n\t\t\tstyles.bgColor.ansi[key] = wrapAnsi16(suite.ansi16, 10);\n\t\t}\n\n\t\tif ('ansi256' in suite) {\n\t\t\tstyles.color.ansi256[key] = wrapAnsi256(suite.ansi256, 0);\n\t\t\tstyles.bgColor.ansi256[key] = wrapAnsi256(suite.ansi256, 10);\n\t\t}\n\n\t\tif ('rgb' in suite) {\n\t\t\tstyles.color.ansi16m[key] = wrapAnsi16m(suite.rgb, 0);\n\t\t\tstyles.bgColor.ansi16m[key] = wrapAnsi16m(suite.rgb, 10);\n\t\t}\n\t}\n\n\treturn styles;\n}\n\n// Make the export immutable\nObject.defineProperty(module, 'exports', {\n\tenumerable: true,\n\tget: assembleStyles\n});\n", "'use strict';\nmodule.exports = (flag, argv) => {\n\targv = argv || process.argv;\n\tconst prefix = flag.startsWith('-') ? '' : (flag.length === 1 ? '-' : '--');\n\tconst pos = argv.indexOf(prefix + flag);\n\tconst terminatorPos = argv.indexOf('--');\n\treturn pos !== -1 && (terminatorPos === -1 ? true : pos < terminatorPos);\n};\n", "'use strict';\nconst os = require('os');\nconst hasFlag = require('has-flag');\n\nconst env = process.env;\n\nlet forceColor;\nif (hasFlag('no-color') ||\n\thasFlag('no-colors') ||\n\thasFlag('color=false')) {\n\tforceColor = false;\n} else if (hasFlag('color') ||\n\thasFlag('colors') ||\n\thasFlag('color=true') ||\n\thasFlag('color=always')) {\n\tforceColor = true;\n}\nif ('FORCE_COLOR' in env) {\n\tforceColor = env.FORCE_COLOR.length === 0 || parseInt(env.FORCE_COLOR, 10) !== 0;\n}\n\nfunction translateLevel(level) {\n\tif (level === 0) {\n\t\treturn false;\n\t}\n\n\treturn {\n\t\tlevel,\n\t\thasBasic: true,\n\t\thas256: level >= 2,\n\t\thas16m: level >= 3\n\t};\n}\n\nfunction supportsColor(stream) {\n\tif (forceColor === false) {\n\t\treturn 0;\n\t}\n\n\tif (hasFlag('color=16m') ||\n\t\thasFlag('color=full') ||\n\t\thasFlag('color=truecolor')) {\n\t\treturn 3;\n\t}\n\n\tif (hasFlag('color=256')) {\n\t\treturn 2;\n\t}\n\n\tif (stream && !stream.isTTY && forceColor !== true) {\n\t\treturn 0;\n\t}\n\n\tconst min = forceColor ? 1 : 0;\n\n\tif (process.platform === 'win32') {\n\t\t// Node.js 7.5.0 is the first version of Node.js to include a patch to\n\t\t// libuv that enables 256 color output on Windows. Anything earlier and it\n\t\t// won't work. However, here we target Node.js 8 at minimum as it is an LTS\n\t\t// release, and Node.js 7 is not. Windows 10 build 10586 is the first Windows\n\t\t// release that supports 256 colors. Windows 10 build 14931 is the first release\n\t\t// that supports 16m/TrueColor.\n\t\tconst osRelease = os.release().split('.');\n\t\tif (\n\t\t\tNumber(process.versions.node.split('.')[0]) >= 8 &&\n\t\t\tNumber(osRelease[0]) >= 10 &&\n\t\t\tNumber(osRelease[2]) >= 10586\n\t\t) {\n\t\t\treturn Number(osRelease[2]) >= 14931 ? 3 : 2;\n\t\t}\n\n\t\treturn 1;\n\t}\n\n\tif ('CI' in env) {\n\t\tif (['TRAVIS', 'CIRCLECI', 'APPVEYOR', 'GITLAB_CI'].some(sign => sign in env) || env.CI_NAME === 'codeship') {\n\t\t\treturn 1;\n\t\t}\n\n\t\treturn min;\n\t}\n\n\tif ('TEAMCITY_VERSION' in env) {\n\t\treturn /^(9\\.(0*[1-9]\\d*)\\.|\\d{2,}\\.)/.test(env.TEAMCITY_VERSION) ? 1 : 0;\n\t}\n\n\tif (env.COLORTERM === 'truecolor') {\n\t\treturn 3;\n\t}\n\n\tif ('TERM_PROGRAM' in env) {\n\t\tconst version = parseInt((env.TERM_PROGRAM_VERSION || '').split('.')[0], 10);\n\n\t\tswitch (env.TERM_PROGRAM) {\n\t\t\tcase 'iTerm.app':\n\t\t\t\treturn version >= 3 ? 3 : 2;\n\t\t\tcase 'Apple_Terminal':\n\t\t\t\treturn 2;\n\t\t\t// No default\n\t\t}\n\t}\n\n\tif (/-256(color)?$/i.test(env.TERM)) {\n\t\treturn 2;\n\t}\n\n\tif (/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(env.TERM)) {\n\t\treturn 1;\n\t}\n\n\tif ('COLORTERM' in env) {\n\t\treturn 1;\n\t}\n\n\tif (env.TERM === 'dumb') {\n\t\treturn min;\n\t}\n\n\treturn min;\n}\n\nfunction getSupportLevel(stream) {\n\tconst level = supportsColor(stream);\n\treturn translateLevel(level);\n}\n\nmodule.exports = {\n\tsupportsColor: getSupportLevel,\n\tstdout: getSupportLevel(process.stdout),\n\tstderr: getSupportLevel(process.stderr)\n};\n", "'use strict';\nconst TEMPLATE_REGEX = /(?:\\\\(u[a-f\\d]{4}|x[a-f\\d]{2}|.))|(?:\\{(~)?(\\w+(?:\\([^)]*\\))?(?:\\.\\w+(?:\\([^)]*\\))?)*)(?:[ \\t]|(?=\\r?\\n)))|(\\})|((?:.|[\\r\\n\\f])+?)/gi;\nconst STYLE_REGEX = /(?:^|\\.)(\\w+)(?:\\(([^)]*)\\))?/g;\nconst STRING_REGEX = /^(['\"])((?:\\\\.|(?!\\1)[^\\\\])*)\\1$/;\nconst ESCAPE_REGEX = /\\\\(u[a-f\\d]{4}|x[a-f\\d]{2}|.)|([^\\\\])/gi;\n\nconst ESCAPES = new Map([\n\t['n', '\\n'],\n\t['r', '\\r'],\n\t['t', '\\t'],\n\t['b', '\\b'],\n\t['f', '\\f'],\n\t['v', '\\v'],\n\t['0', '\\0'],\n\t['\\\\', '\\\\'],\n\t['e', '\\u001B'],\n\t['a', '\\u0007']\n]);\n\nfunction unescape(c) {\n\tif ((c[0] === 'u' && c.length === 5) || (c[0] === 'x' && c.length === 3)) {\n\t\treturn String.fromCharCode(parseInt(c.slice(1), 16));\n\t}\n\n\treturn ESCAPES.get(c) || c;\n}\n\nfunction parseArguments(name, args) {\n\tconst results = [];\n\tconst chunks = args.trim().split(/\\s*,\\s*/g);\n\tlet matches;\n\n\tfor (const chunk of chunks) {\n\t\tif (!isNaN(chunk)) {\n\t\t\tresults.push(Number(chunk));\n\t\t} else if ((matches = chunk.match(STRING_REGEX))) {\n\t\t\tresults.push(matches[2].replace(ESCAPE_REGEX, (m, escape, chr) => escape ? unescape(escape) : chr));\n\t\t} else {\n\t\t\tthrow new Error(`Invalid Chalk template style argument: ${chunk} (in style '${name}')`);\n\t\t}\n\t}\n\n\treturn results;\n}\n\nfunction parseStyle(style) {\n\tSTYLE_REGEX.lastIndex = 0;\n\n\tconst results = [];\n\tlet matches;\n\n\twhile ((matches = STYLE_REGEX.exec(style)) !== null) {\n\t\tconst name = matches[1];\n\n\t\tif (matches[2]) {\n\t\t\tconst args = parseArguments(name, matches[2]);\n\t\t\tresults.push([name].concat(args));\n\t\t} else {\n\t\t\tresults.push([name]);\n\t\t}\n\t}\n\n\treturn results;\n}\n\nfunction buildStyle(chalk, styles) {\n\tconst enabled = {};\n\n\tfor (const layer of styles) {\n\t\tfor (const style of layer.styles) {\n\t\t\tenabled[style[0]] = layer.inverse ? null : style.slice(1);\n\t\t}\n\t}\n\n\tlet current = chalk;\n\tfor (const styleName of Object.keys(enabled)) {\n\t\tif (Array.isArray(enabled[styleName])) {\n\t\t\tif (!(styleName in current)) {\n\t\t\t\tthrow new Error(`Unknown Chalk style: ${styleName}`);\n\t\t\t}\n\n\t\t\tif (enabled[styleName].length > 0) {\n\t\t\t\tcurrent = current[styleName].apply(current, enabled[styleName]);\n\t\t\t} else {\n\t\t\t\tcurrent = current[styleName];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn current;\n}\n\nmodule.exports = (chalk, tmp) => {\n\tconst styles = [];\n\tconst chunks = [];\n\tlet chunk = [];\n\n\t// eslint-disable-next-line max-params\n\ttmp.replace(TEMPLATE_REGEX, (m, escapeChar, inverse, style, close, chr) => {\n\t\tif (escapeChar) {\n\t\t\tchunk.push(unescape(escapeChar));\n\t\t} else if (style) {\n\t\t\tconst str = chunk.join('');\n\t\t\tchunk = [];\n\t\t\tchunks.push(styles.length === 0 ? str : buildStyle(chalk, styles)(str));\n\t\t\tstyles.push({inverse, styles: parseStyle(style)});\n\t\t} else if (close) {\n\t\t\tif (styles.length === 0) {\n\t\t\t\tthrow new Error('Found extraneous } in Chalk template literal');\n\t\t\t}\n\n\t\t\tchunks.push(buildStyle(chalk, styles)(chunk.join('')));\n\t\t\tchunk = [];\n\t\t\tstyles.pop();\n\t\t} else {\n\t\t\tchunk.push(chr);\n\t\t}\n\t});\n\n\tchunks.push(chunk.join(''));\n\n\tif (styles.length > 0) {\n\t\tconst errMsg = `Chalk template literal is missing ${styles.length} closing bracket${styles.length === 1 ? '' : 's'} (\\`}\\`)`;\n\t\tthrow new Error(errMsg);\n\t}\n\n\treturn chunks.join('');\n};\n", "'use strict';\nconst escapeStringRegexp = require('escape-string-regexp');\nconst ansiStyles = require('ansi-styles');\nconst stdoutColor = require('supports-color').stdout;\n\nconst template = require('./templates.js');\n\nconst isSimpleWindowsTerm = process.platform === 'win32' && !(process.env.TERM || '').toLowerCase().startsWith('xterm');\n\n// `supportsColor.level` → `ansiStyles.color[name]` mapping\nconst levelMapping = ['ansi', 'ansi', 'ansi256', 'ansi16m'];\n\n// `color-convert` models to exclude from the Chalk API due to conflicts and such\nconst skipModels = new Set(['gray']);\n\nconst styles = Object.create(null);\n\nfunction applyOptions(obj, options) {\n\toptions = options || {};\n\n\t// Detect level if not set manually\n\tconst scLevel = stdoutColor ? stdoutColor.level : 0;\n\tobj.level = options.level === undefined ? scLevel : options.level;\n\tobj.enabled = 'enabled' in options ? options.enabled : obj.level > 0;\n}\n\nfunction Chalk(options) {\n\t// We check for this.template here since calling `chalk.constructor()`\n\t// by itself will have a `this` of a previously constructed chalk object\n\tif (!this || !(this instanceof Chalk) || this.template) {\n\t\tconst chalk = {};\n\t\tapplyOptions(chalk, options);\n\n\t\tchalk.template = function () {\n\t\t\tconst args = [].slice.call(arguments);\n\t\t\treturn chalkTag.apply(null, [chalk.template].concat(args));\n\t\t};\n\n\t\tObject.setPrototypeOf(chalk, Chalk.prototype);\n\t\tObject.setPrototypeOf(chalk.template, chalk);\n\n\t\tchalk.template.constructor = Chalk;\n\n\t\treturn chalk.template;\n\t}\n\n\tapplyOptions(this, options);\n}\n\n// Use bright blue on Windows as the normal blue color is illegible\nif (isSimpleWindowsTerm) {\n\tansiStyles.blue.open = '\\u001B[94m';\n}\n\nfor (const key of Object.keys(ansiStyles)) {\n\tansiStyles[key].closeRe = new RegExp(escapeStringRegexp(ansiStyles[key].close), 'g');\n\n\tstyles[key] = {\n\t\tget() {\n\t\t\tconst codes = ansiStyles[key];\n\t\t\treturn build.call(this, this._styles ? this._styles.concat(codes) : [codes], this._empty, key);\n\t\t}\n\t};\n}\n\nstyles.visible = {\n\tget() {\n\t\treturn build.call(this, this._styles || [], true, 'visible');\n\t}\n};\n\nansiStyles.color.closeRe = new RegExp(escapeStringRegexp(ansiStyles.color.close), 'g');\nfor (const model of Object.keys(ansiStyles.color.ansi)) {\n\tif (skipModels.has(model)) {\n\t\tcontinue;\n\t}\n\n\tstyles[model] = {\n\t\tget() {\n\t\t\tconst level = this.level;\n\t\t\treturn function () {\n\t\t\t\tconst open = ansiStyles.color[levelMapping[level]][model].apply(null, arguments);\n\t\t\t\tconst codes = {\n\t\t\t\t\topen,\n\t\t\t\t\tclose: ansiStyles.color.close,\n\t\t\t\t\tcloseRe: ansiStyles.color.closeRe\n\t\t\t\t};\n\t\t\t\treturn build.call(this, this._styles ? this._styles.concat(codes) : [codes], this._empty, model);\n\t\t\t};\n\t\t}\n\t};\n}\n\nansiStyles.bgColor.closeRe = new RegExp(escapeStringRegexp(ansiStyles.bgColor.close), 'g');\nfor (const model of Object.keys(ansiStyles.bgColor.ansi)) {\n\tif (skipModels.has(model)) {\n\t\tcontinue;\n\t}\n\n\tconst bgModel = 'bg' + model[0].toUpperCase() + model.slice(1);\n\tstyles[bgModel] = {\n\t\tget() {\n\t\t\tconst level = this.level;\n\t\t\treturn function () {\n\t\t\t\tconst open = ansiStyles.bgColor[levelMapping[level]][model].apply(null, arguments);\n\t\t\t\tconst codes = {\n\t\t\t\t\topen,\n\t\t\t\t\tclose: ansiStyles.bgColor.close,\n\t\t\t\t\tcloseRe: ansiStyles.bgColor.closeRe\n\t\t\t\t};\n\t\t\t\treturn build.call(this, this._styles ? this._styles.concat(codes) : [codes], this._empty, model);\n\t\t\t};\n\t\t}\n\t};\n}\n\nconst proto = Object.defineProperties(() => {}, styles);\n\nfunction build(_styles, _empty, key) {\n\tconst builder = function () {\n\t\treturn applyStyle.apply(builder, arguments);\n\t};\n\n\tbuilder._styles = _styles;\n\tbuilder._empty = _empty;\n\n\tconst self = this;\n\n\tObject.defineProperty(builder, 'level', {\n\t\tenumerable: true,\n\t\tget() {\n\t\t\treturn self.level;\n\t\t},\n\t\tset(level) {\n\t\t\tself.level = level;\n\t\t}\n\t});\n\n\tObject.defineProperty(builder, 'enabled', {\n\t\tenumerable: true,\n\t\tget() {\n\t\t\treturn self.enabled;\n\t\t},\n\t\tset(enabled) {\n\t\t\tself.enabled = enabled;\n\t\t}\n\t});\n\n\t// See below for fix regarding invisible grey/dim combination on Windows\n\tbuilder.hasGrey = this.hasGrey || key === 'gray' || key === 'grey';\n\n\t// `__proto__` is used because we must return a function, but there is\n\t// no way to create a function with a different prototype\n\tbuilder.__proto__ = proto; // eslint-disable-line no-proto\n\n\treturn builder;\n}\n\nfunction applyStyle() {\n\t// Support varags, but simply cast to string in case there's only one arg\n\tconst args = arguments;\n\tconst argsLen = args.length;\n\tlet str = String(arguments[0]);\n\n\tif (argsLen === 0) {\n\t\treturn '';\n\t}\n\n\tif (argsLen > 1) {\n\t\t// Don't slice `arguments`, it prevents V8 optimizations\n\t\tfor (let a = 1; a < argsLen; a++) {\n\t\t\tstr += ' ' + args[a];\n\t\t}\n\t}\n\n\tif (!this.enabled || this.level <= 0 || !str) {\n\t\treturn this._empty ? '' : str;\n\t}\n\n\t// Turns out that on Windows dimmed gray text becomes invisible in cmd.exe,\n\t// see https://github.com/chalk/chalk/issues/58\n\t// If we're on Windows and we're dealing with a gray color, temporarily make 'dim' a noop.\n\tconst originalDim = ansiStyles.dim.open;\n\tif (isSimpleWindowsTerm && this.hasGrey) {\n\t\tansiStyles.dim.open = '';\n\t}\n\n\tfor (const code of this._styles.slice().reverse()) {\n\t\t// Replace any instances already present with a re-opening code\n\t\t// otherwise only the part of the string until said closing code\n\t\t// will be colored, and the rest will simply be 'plain'.\n\t\tstr = code.open + str.replace(code.closeRe, code.open) + code.close;\n\n\t\t// Close the styling before a linebreak and reopen\n\t\t// after next line to fix a bleed issue on macOS\n\t\t// https://github.com/chalk/chalk/pull/92\n\t\tstr = str.replace(/\\r?\\n/g, `${code.close}$&${code.open}`);\n\t}\n\n\t// Reset the original `dim` if we changed it to work around the Windows dimmed gray issue\n\tansiStyles.dim.open = originalDim;\n\n\treturn str;\n}\n\nfunction chalkTag(chalk, strings) {\n\tif (!Array.isArray(strings)) {\n\t\t// If chalk() was called by itself or with a string,\n\t\t// return the string itself as a string.\n\t\treturn [].slice.call(arguments, 1).join(' ');\n\t}\n\n\tconst args = [].slice.call(arguments, 2);\n\tconst parts = [strings.raw[0]];\n\n\tfor (let i = 1; i < strings.length; i++) {\n\t\tparts.push(String(args[i - 1]).replace(/[{}\\\\]/g, '\\\\$&'));\n\t\tparts.push(String(strings.raw[i]));\n\t}\n\n\treturn template(chalk, parts.join(''));\n}\n\nObject.defineProperties(Chalk.prototype, styles);\n\nmodule.exports = Chalk(); // eslint-disable-line new-cap\nmodule.exports.supportsColor = stdoutColor;\nmodule.exports.default = module.exports; // For TypeScript\n", "import type { Token as J<PERSON><PERSON>, J<PERSON><PERSON>oken } from \"js-tokens\";\nimport jsTokens from \"js-tokens\";\n\nimport {\n  isStrictReservedWord,\n  isKeyword,\n} from \"@babel/helper-validator-identifier\";\n\nimport _colors, { createColors } from \"picocolors\";\nimport type { Colors, Formatter } from \"picocolors/types\";\n// See https://github.com/alexeyraspopov/picocolors/issues/62\nconst colors =\n  typeof process === \"object\" &&\n  (process.env.FORCE_COLOR === \"0\" || process.env.FORCE_COLOR === \"false\")\n    ? createColors(false)\n    : _colors;\n\nconst compose: <T, U, V>(f: (gv: U) => V, g: (v: T) => U) => (v: T) => V =\n  (f, g) => v =>\n    f(g(v));\n\n/**\n * Names that are always allowed as identifiers, but also appear as keywords\n * within certain syntactic productions.\n *\n * https://tc39.es/ecma262/#sec-keywords-and-reserved-words\n *\n * `target` has been omitted since it is very likely going to be a false\n * positive.\n */\nconst sometimesKeywords = new Set([\"as\", \"async\", \"from\", \"get\", \"of\", \"set\"]);\n\ntype InternalTokenType =\n  | \"keyword\"\n  | \"capitalized\"\n  | \"jsxIdentifier\"\n  | \"punctuator\"\n  | \"number\"\n  | \"string\"\n  | \"regex\"\n  | \"comment\"\n  | \"invalid\";\n\ntype Token = {\n  type: InternalTokenType | \"uncolored\";\n  value: string;\n};\n/**\n * Styles for token types.\n */\nfunction getDefs(colors: Colors): Record<InternalTokenType, Formatter> {\n  return {\n    keyword: colors.cyan,\n    capitalized: colors.yellow,\n    jsxIdentifier: colors.yellow,\n    punctuator: colors.yellow,\n    number: colors.magenta,\n    string: colors.green,\n    regex: colors.magenta,\n    comment: colors.gray,\n    invalid: compose(compose(colors.white, colors.bgRed), colors.bold),\n  };\n}\n\n/**\n * RegExp to test for newlines in terminal.\n */\nconst NEWLINE = /\\r\\n|[\\n\\r\\u2028\\u2029]/;\n\n/**\n * RegExp to test for the three types of brackets.\n */\nconst BRACKET = /^[()[\\]{}]$/;\n\nlet tokenize: (\n  text: string,\n) => Generator<{ type: InternalTokenType | \"uncolored\"; value: string }>;\n\nif (process.env.BABEL_8_BREAKING) {\n  /**\n   * Get the type of token, specifying punctuator type.\n   */\n  const getTokenType = function (\n    token: JSToken | JSXToken,\n  ): InternalTokenType | \"uncolored\" {\n    if (token.type === \"IdentifierName\") {\n      if (\n        isKeyword(token.value) ||\n        isStrictReservedWord(token.value, true) ||\n        sometimesKeywords.has(token.value)\n      ) {\n        return \"keyword\";\n      }\n\n      if (token.value[0] !== token.value[0].toLowerCase()) {\n        return \"capitalized\";\n      }\n    }\n\n    if (token.type === \"Punctuator\" && BRACKET.test(token.value)) {\n      return \"uncolored\";\n    }\n\n    if (token.type === \"Invalid\" && token.value === \"@\") {\n      return \"punctuator\";\n    }\n\n    switch (token.type) {\n      case \"NumericLiteral\":\n        return \"number\";\n\n      case \"StringLiteral\":\n      case \"JSXString\":\n      case \"NoSubstitutionTemplate\":\n        return \"string\";\n\n      case \"RegularExpressionLiteral\":\n        return \"regex\";\n\n      case \"Punctuator\":\n      case \"JSXPunctuator\":\n        return \"punctuator\";\n\n      case \"MultiLineComment\":\n      case \"SingleLineComment\":\n        return \"comment\";\n\n      case \"Invalid\":\n      case \"JSXInvalid\":\n        return \"invalid\";\n\n      case \"JSXIdentifier\":\n        return \"jsxIdentifier\";\n\n      default:\n        return \"uncolored\";\n    }\n  };\n\n  /**\n   * Turn a string of JS into an array of objects.\n   */\n  tokenize = function* (text: string): Generator<Token> {\n    for (const token of jsTokens(text, { jsx: true })) {\n      switch (token.type) {\n        case \"TemplateHead\":\n          yield { type: \"string\", value: token.value.slice(0, -2) };\n          yield { type: \"punctuator\", value: \"${\" };\n          break;\n\n        case \"TemplateMiddle\":\n          yield { type: \"punctuator\", value: \"}\" };\n          yield { type: \"string\", value: token.value.slice(1, -2) };\n          yield { type: \"punctuator\", value: \"${\" };\n          break;\n\n        case \"TemplateTail\":\n          yield { type: \"punctuator\", value: \"}\" };\n          yield { type: \"string\", value: token.value.slice(1) };\n          break;\n\n        default:\n          yield {\n            type: getTokenType(token),\n            value: token.value,\n          };\n      }\n    }\n  };\n} else {\n  /**\n   * RegExp to test for what seems to be a JSX tag name.\n   */\n  const JSX_TAG = /^[a-z][\\w-]*$/i;\n\n  // The token here is defined in js-tokens@4. However we don't bother\n  // typing it since the whole block will be removed in Babel 8\n  const getTokenType = function (token: any, offset: number, text: string) {\n    if (token.type === \"name\") {\n      if (\n        isKeyword(token.value) ||\n        isStrictReservedWord(token.value, true) ||\n        sometimesKeywords.has(token.value)\n      ) {\n        return \"keyword\";\n      }\n\n      if (\n        JSX_TAG.test(token.value) &&\n        (text[offset - 1] === \"<\" || text.slice(offset - 2, offset) === \"</\")\n      ) {\n        return \"jsxIdentifier\";\n      }\n\n      if (token.value[0] !== token.value[0].toLowerCase()) {\n        return \"capitalized\";\n      }\n    }\n\n    if (token.type === \"punctuator\" && BRACKET.test(token.value)) {\n      return \"bracket\";\n    }\n\n    if (\n      token.type === \"invalid\" &&\n      (token.value === \"@\" || token.value === \"#\")\n    ) {\n      return \"punctuator\";\n    }\n\n    return token.type;\n  };\n\n  tokenize = function* (text: string) {\n    let match;\n    while ((match = (jsTokens as any).default.exec(text))) {\n      const token = (jsTokens as any).matchToToken(match);\n\n      yield {\n        type: getTokenType(token, match.index, text),\n        value: token.value,\n      };\n    }\n  };\n}\n\n/**\n * Highlight `text` using the token definitions in `defs`.\n */\nfunction highlightTokens(defs: Record<string, Formatter>, text: string) {\n  let highlighted = \"\";\n\n  for (const { type, value } of tokenize(text)) {\n    const colorize = defs[type];\n    if (colorize) {\n      highlighted += value\n        .split(NEWLINE)\n        .map(str => colorize(str))\n        .join(\"\\n\");\n    } else {\n      highlighted += value;\n    }\n  }\n\n  return highlighted;\n}\n\n/**\n * Highlight `text` using the token definitions in `defs`.\n */\n\ntype Options = {\n  forceColor?: boolean;\n};\n\n/**\n * Whether the code should be highlighted given the passed options.\n */\nexport function shouldHighlight(options: Options): boolean {\n  return colors.isColorSupported || options.forceColor;\n}\n\nlet pcWithForcedColor: Colors = undefined;\nfunction getColors(forceColor: boolean) {\n  if (forceColor) {\n    pcWithForcedColor ??= createColors(true);\n    return pcWithForcedColor;\n  }\n  return colors;\n}\n\n/**\n * Highlight `code`.\n */\nexport default function highlight(code: string, options: Options = {}): string {\n  if (code !== \"\" && shouldHighlight(options)) {\n    const defs = getDefs(getColors(options.forceColor));\n    return highlightTokens(defs, code);\n  } else {\n    return code;\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n  let chalk: any, chalkWithForcedColor: any;\n  // eslint-disable-next-line no-restricted-globals\n  exports.getChalk = ({ forceColor }: Options) => {\n    // eslint-disable-next-line no-restricted-globals\n    chalk ??= require(\"chalk\");\n    if (forceColor) {\n      chalkWithForcedColor ??= new chalk.constructor({\n        enabled: true,\n        level: 1,\n      });\n      return chalkWithForcedColor;\n    }\n    return chalk;\n  };\n}\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.codeFrameColumns = codeFrameColumns;\nexports.default = _default;\n\nvar _highlight = _interopRequireWildcard(require(\"@babel/highlight\"));\n\nfunction _getRequireWildcardCache() { if (typeof WeakMap !== \"function\") return null; var cache = new WeakMap(); _getRequireWildcardCache = function () { return cache; }; return cache; }\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nlet deprecationWarningShown = false;\n\nfunction getDefs(chalk) {\n  return {\n    gutter: chalk.grey,\n    marker: chalk.red.bold,\n    message: chalk.red.bold\n  };\n}\n\nconst NEWLINE = /\\r\\n|[\\n\\r\\u2028\\u2029]/;\n\nfunction getMarkerLines(loc, source, opts) {\n  const startLoc = Object.assign({\n    column: 0,\n    line: -1\n  }, loc.start);\n  const endLoc = Object.assign({}, startLoc, loc.end);\n  const {\n    linesAbove = 2,\n    linesBelow = 3\n  } = opts || {};\n  const startLine = startLoc.line;\n  const startColumn = startLoc.column;\n  const endLine = endLoc.line;\n  const endColumn = endLoc.column;\n  let start = Math.max(startLine - (linesAbove + 1), 0);\n  let end = Math.min(source.length, endLine + linesBelow);\n\n  if (startLine === -1) {\n    start = 0;\n  }\n\n  if (endLine === -1) {\n    end = source.length;\n  }\n\n  const lineDiff = endLine - startLine;\n  const markerLines = {};\n\n  if (lineDiff) {\n    for (let i = 0; i <= lineDiff; i++) {\n      const lineNumber = i + startLine;\n\n      if (!startColumn) {\n        markerLines[lineNumber] = true;\n      } else if (i === 0) {\n        const sourceLength = source[lineNumber - 1].length;\n        markerLines[lineNumber] = [startColumn, sourceLength - startColumn + 1];\n      } else if (i === lineDiff) {\n        markerLines[lineNumber] = [0, endColumn];\n      } else {\n        const sourceLength = source[lineNumber - i].length;\n        markerLines[lineNumber] = [0, sourceLength];\n      }\n    }\n  } else {\n    if (startColumn === endColumn) {\n      if (startColumn) {\n        markerLines[startLine] = [startColumn, 0];\n      } else {\n        markerLines[startLine] = true;\n      }\n    } else {\n      markerLines[startLine] = [startColumn, endColumn - startColumn];\n    }\n  }\n\n  return {\n    start,\n    end,\n    markerLines\n  };\n}\n\nfunction codeFrameColumns(rawLines, loc, opts = {}) {\n  const highlighted = (opts.highlightCode || opts.forceColor) && (0, _highlight.shouldHighlight)(opts);\n  const chalk = (0, _highlight.getChalk)(opts);\n  const defs = getDefs(chalk);\n\n  const maybeHighlight = (chalkFn, string) => {\n    return highlighted ? chalkFn(string) : string;\n  };\n\n  const lines = rawLines.split(NEWLINE);\n  const {\n    start,\n    end,\n    markerLines\n  } = getMarkerLines(loc, lines, opts);\n  const hasColumns = loc.start && typeof loc.start.column === \"number\";\n  const numberMaxWidth = String(end).length;\n  const highlightedLines = highlighted ? (0, _highlight.default)(rawLines, opts) : rawLines;\n  let frame = highlightedLines.split(NEWLINE).slice(start, end).map((line, index) => {\n    const number = start + 1 + index;\n    const paddedNumber = ` ${number}`.slice(-numberMaxWidth);\n    const gutter = ` ${paddedNumber} | `;\n    const hasMarker = markerLines[number];\n    const lastMarkerLine = !markerLines[number + 1];\n\n    if (hasMarker) {\n      let markerLine = \"\";\n\n      if (Array.isArray(hasMarker)) {\n        const markerSpacing = line.slice(0, Math.max(hasMarker[0] - 1, 0)).replace(/[^\\t]/g, \" \");\n        const numberOfMarkers = hasMarker[1] || 1;\n        markerLine = [\"\\n \", maybeHighlight(defs.gutter, gutter.replace(/\\d/g, \" \")), markerSpacing, maybeHighlight(defs.marker, \"^\").repeat(numberOfMarkers)].join(\"\");\n\n        if (lastMarkerLine && opts.message) {\n          markerLine += \" \" + maybeHighlight(defs.message, opts.message);\n        }\n      }\n\n      return [maybeHighlight(defs.marker, \">\"), maybeHighlight(defs.gutter, gutter), line, markerLine].join(\"\");\n    } else {\n      return ` ${maybeHighlight(defs.gutter, gutter)}${line}`;\n    }\n  }).join(\"\\n\");\n\n  if (opts.message && !hasColumns) {\n    frame = `${\" \".repeat(numberMaxWidth + 1)}${opts.message}\\n${frame}`;\n  }\n\n  if (highlighted) {\n    return chalk.reset(frame);\n  } else {\n    return frame;\n  }\n}\n\nfunction _default(rawLines, lineNumber, colNumber, opts = {}) {\n  if (!deprecationWarningShown) {\n    deprecationWarningShown = true;\n    const message = \"Passing lineNumber and colNumber is deprecated to @babel/code-frame. Please use `codeFrameColumns`.\";\n\n    if (process.emitWarning) {\n      process.emitWarning(message, \"DeprecationWarning\");\n    } else {\n      const deprecationError = new Error(message);\n      deprecationError.name = \"DeprecationWarning\";\n      console.warn(new Error(message));\n    }\n  }\n\n  colNumber = Math.max(colNumber, 0);\n  const location = {\n    start: {\n      column: colNumber,\n      line: lineNumber\n    }\n  };\n  return codeFrameColumns(rawLines, location, opts);\n}", "'use strict';\nconst errorEx = require('error-ex');\nconst fallback = require('json-parse-even-better-errors');\nconst {default: LinesAndColumns} = require('lines-and-columns');\nconst {codeFrameColumns} = require('@babel/code-frame');\n\nconst JSONError = errorEx('JSONError', {\n\tfileName: errorEx.append('in %s'),\n\tcodeFrame: errorEx.append('\\n\\n%s\\n')\n});\n\nconst parseJson = (string, reviver, filename) => {\n\tif (typeof reviver === 'string') {\n\t\tfilename = reviver;\n\t\treviver = null;\n\t}\n\n\ttry {\n\t\ttry {\n\t\t\treturn JSON.parse(string, reviver);\n\t\t} catch (error) {\n\t\t\tfallback(string, reviver);\n\t\t\tthrow error;\n\t\t}\n\t} catch (error) {\n\t\terror.message = error.message.replace(/\\n/g, '');\n\t\tconst indexMatch = error.message.match(/in JSON at position (\\d+) while parsing/);\n\n\t\tconst jsonError = new JSONError(error);\n\t\tif (filename) {\n\t\t\tjsonError.fileName = filename;\n\t\t}\n\n\t\tif (indexMatch && indexMatch.length > 0) {\n\t\t\tconst lines = new LinesAndColumns(string);\n\t\t\tconst index = Number(indexMatch[1]);\n\t\t\tconst location = lines.locationForIndex(index);\n\n\t\t\tconst codeFrame = codeFrameColumns(\n\t\t\t\tstring,\n\t\t\t\t{start: {line: location.line + 1, column: location.column + 1}},\n\t\t\t\t{highlightCode: true}\n\t\t\t);\n\n\t\t\tjsonError.codeFrame = codeFrame;\n\t\t}\n\n\t\tthrow jsonError;\n\t}\n};\n\nparseJson.JSONError = JSONError;\n\nmodule.exports = parseJson;\n", "exports = module.exports = SemVer\n\nvar debug\n/* istanbul ignore next */\nif (typeof process === 'object' &&\n    process.env &&\n    process.env.NODE_DEBUG &&\n    /\\bsemver\\b/i.test(process.env.NODE_DEBUG)) {\n  debug = function () {\n    var args = Array.prototype.slice.call(arguments, 0)\n    args.unshift('SEMVER')\n    console.log.apply(console, args)\n  }\n} else {\n  debug = function () {}\n}\n\n// Note: this is the semver.org version of the spec that it implements\n// Not necessarily the package version of this code.\nexports.SEMVER_SPEC_VERSION = '2.0.0'\n\nvar MAX_LENGTH = 256\nvar MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER ||\n  /* istanbul ignore next */ 9007199254740991\n\n// Max safe segment length for coercion.\nvar MAX_SAFE_COMPONENT_LENGTH = 16\n\nvar MAX_SAFE_BUILD_LENGTH = MAX_LENGTH - 6\n\n// The actual regexps go on exports.re\nvar re = exports.re = []\nvar safeRe = exports.safeRe = []\nvar src = exports.src = []\nvar R = 0\n\nvar LETTERDASHNUMBER = '[a-zA-Z0-9-]'\n\n// Replace some greedy regex tokens to prevent regex dos issues. These regex are\n// used internally via the safeRe object since all inputs in this library get\n// normalized first to trim and collapse all extra whitespace. The original\n// regexes are exported for userland consumption and lower level usage. A\n// future breaking change could export the safer regex only with a note that\n// all input should have extra whitespace removed.\nvar safeRegexReplacements = [\n  ['\\\\s', 1],\n  ['\\\\d', MAX_LENGTH],\n  [LETTERDASHNUMBER, MAX_SAFE_BUILD_LENGTH],\n]\n\nfunction makeSafeRe (value) {\n  for (var i = 0; i < safeRegexReplacements.length; i++) {\n    var token = safeRegexReplacements[i][0]\n    var max = safeRegexReplacements[i][1]\n    value = value\n      .split(token + '*').join(token + '{0,' + max + '}')\n      .split(token + '+').join(token + '{1,' + max + '}')\n  }\n  return value\n}\n\n// The following Regular Expressions can be used for tokenizing,\n// validating, and parsing SemVer version strings.\n\n// ## Numeric Identifier\n// A single `0`, or a non-zero digit followed by zero or more digits.\n\nvar NUMERICIDENTIFIER = R++\nsrc[NUMERICIDENTIFIER] = '0|[1-9]\\\\d*'\nvar NUMERICIDENTIFIERLOOSE = R++\nsrc[NUMERICIDENTIFIERLOOSE] = '\\\\d+'\n\n// ## Non-numeric Identifier\n// Zero or more digits, followed by a letter or hyphen, and then zero or\n// more letters, digits, or hyphens.\n\nvar NONNUMERICIDENTIFIER = R++\nsrc[NONNUMERICIDENTIFIER] = '\\\\d*[a-zA-Z-]' + LETTERDASHNUMBER + '*'\n\n// ## Main Version\n// Three dot-separated numeric identifiers.\n\nvar MAINVERSION = R++\nsrc[MAINVERSION] = '(' + src[NUMERICIDENTIFIER] + ')\\\\.' +\n                   '(' + src[NUMERICIDENTIFIER] + ')\\\\.' +\n                   '(' + src[NUMERICIDENTIFIER] + ')'\n\nvar MAINVERSIONLOOSE = R++\nsrc[MAINVERSIONLOOSE] = '(' + src[NUMERICIDENTIFIERLOOSE] + ')\\\\.' +\n                        '(' + src[NUMERICIDENTIFIERLOOSE] + ')\\\\.' +\n                        '(' + src[NUMERICIDENTIFIERLOOSE] + ')'\n\n// ## Pre-release Version Identifier\n// A numeric identifier, or a non-numeric identifier.\n\nvar PRERELEASEIDENTIFIER = R++\nsrc[PRERELEASEIDENTIFIER] = '(?:' + src[NUMERICIDENTIFIER] +\n                            '|' + src[NONNUMERICIDENTIFIER] + ')'\n\nvar PRERELEASEIDENTIFIERLOOSE = R++\nsrc[PRERELEASEIDENTIFIERLOOSE] = '(?:' + src[NUMERICIDENTIFIERLOOSE] +\n                                 '|' + src[NONNUMERICIDENTIFIER] + ')'\n\n// ## Pre-release Version\n// Hyphen, followed by one or more dot-separated pre-release version\n// identifiers.\n\nvar PRERELEASE = R++\nsrc[PRERELEASE] = '(?:-(' + src[PRERELEASEIDENTIFIER] +\n                  '(?:\\\\.' + src[PRERELEASEIDENTIFIER] + ')*))'\n\nvar PRERELEASELOOSE = R++\nsrc[PRERELEASELOOSE] = '(?:-?(' + src[PRERELEASEIDENTIFIERLOOSE] +\n                       '(?:\\\\.' + src[PRERELEASEIDENTIFIERLOOSE] + ')*))'\n\n// ## Build Metadata Identifier\n// Any combination of digits, letters, or hyphens.\n\nvar BUILDIDENTIFIER = R++\nsrc[BUILDIDENTIFIER] = LETTERDASHNUMBER + '+'\n\n// ## Build Metadata\n// Plus sign, followed by one or more period-separated build metadata\n// identifiers.\n\nvar BUILD = R++\nsrc[BUILD] = '(?:\\\\+(' + src[BUILDIDENTIFIER] +\n             '(?:\\\\.' + src[BUILDIDENTIFIER] + ')*))'\n\n// ## Full Version String\n// A main version, followed optionally by a pre-release version and\n// build metadata.\n\n// Note that the only major, minor, patch, and pre-release sections of\n// the version string are capturing groups.  The build metadata is not a\n// capturing group, because it should not ever be used in version\n// comparison.\n\nvar FULL = R++\nvar FULLPLAIN = 'v?' + src[MAINVERSION] +\n                src[PRERELEASE] + '?' +\n                src[BUILD] + '?'\n\nsrc[FULL] = '^' + FULLPLAIN + '$'\n\n// like full, but allows v1.2.3 and =1.2.3, which people do sometimes.\n// also, 1.0.0alpha1 (prerelease without the hyphen) which is pretty\n// common in the npm registry.\nvar LOOSEPLAIN = '[v=\\\\s]*' + src[MAINVERSIONLOOSE] +\n                 src[PRERELEASELOOSE] + '?' +\n                 src[BUILD] + '?'\n\nvar LOOSE = R++\nsrc[LOOSE] = '^' + LOOSEPLAIN + '$'\n\nvar GTLT = R++\nsrc[GTLT] = '((?:<|>)?=?)'\n\n// Something like \"2.*\" or \"1.2.x\".\n// Note that \"x.x\" is a valid xRange identifer, meaning \"any version\"\n// Only the first item is strictly required.\nvar XRANGEIDENTIFIERLOOSE = R++\nsrc[XRANGEIDENTIFIERLOOSE] = src[NUMERICIDENTIFIERLOOSE] + '|x|X|\\\\*'\nvar XRANGEIDENTIFIER = R++\nsrc[XRANGEIDENTIFIER] = src[NUMERICIDENTIFIER] + '|x|X|\\\\*'\n\nvar XRANGEPLAIN = R++\nsrc[XRANGEPLAIN] = '[v=\\\\s]*(' + src[XRANGEIDENTIFIER] + ')' +\n                   '(?:\\\\.(' + src[XRANGEIDENTIFIER] + ')' +\n                   '(?:\\\\.(' + src[XRANGEIDENTIFIER] + ')' +\n                   '(?:' + src[PRERELEASE] + ')?' +\n                   src[BUILD] + '?' +\n                   ')?)?'\n\nvar XRANGEPLAINLOOSE = R++\nsrc[XRANGEPLAINLOOSE] = '[v=\\\\s]*(' + src[XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:\\\\.(' + src[XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:\\\\.(' + src[XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:' + src[PRERELEASELOOSE] + ')?' +\n                        src[BUILD] + '?' +\n                        ')?)?'\n\nvar XRANGE = R++\nsrc[XRANGE] = '^' + src[GTLT] + '\\\\s*' + src[XRANGEPLAIN] + '$'\nvar XRANGELOOSE = R++\nsrc[XRANGELOOSE] = '^' + src[GTLT] + '\\\\s*' + src[XRANGEPLAINLOOSE] + '$'\n\n// Coercion.\n// Extract anything that could conceivably be a part of a valid semver\nvar COERCE = R++\nsrc[COERCE] = '(?:^|[^\\\\d])' +\n              '(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '})' +\n              '(?:\\\\.(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '}))?' +\n              '(?:\\\\.(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '}))?' +\n              '(?:$|[^\\\\d])'\n\n// Tilde ranges.\n// Meaning is \"reasonably at or greater than\"\nvar LONETILDE = R++\nsrc[LONETILDE] = '(?:~>?)'\n\nvar TILDETRIM = R++\nsrc[TILDETRIM] = '(\\\\s*)' + src[LONETILDE] + '\\\\s+'\nre[TILDETRIM] = new RegExp(src[TILDETRIM], 'g')\nsafeRe[TILDETRIM] = new RegExp(makeSafeRe(src[TILDETRIM]), 'g')\nvar tildeTrimReplace = '$1~'\n\nvar TILDE = R++\nsrc[TILDE] = '^' + src[LONETILDE] + src[XRANGEPLAIN] + '$'\nvar TILDELOOSE = R++\nsrc[TILDELOOSE] = '^' + src[LONETILDE] + src[XRANGEPLAINLOOSE] + '$'\n\n// Caret ranges.\n// Meaning is \"at least and backwards compatible with\"\nvar LONECARET = R++\nsrc[LONECARET] = '(?:\\\\^)'\n\nvar CARETTRIM = R++\nsrc[CARETTRIM] = '(\\\\s*)' + src[LONECARET] + '\\\\s+'\nre[CARETTRIM] = new RegExp(src[CARETTRIM], 'g')\nsafeRe[CARETTRIM] = new RegExp(makeSafeRe(src[CARETTRIM]), 'g')\nvar caretTrimReplace = '$1^'\n\nvar CARET = R++\nsrc[CARET] = '^' + src[LONECARET] + src[XRANGEPLAIN] + '$'\nvar CARETLOOSE = R++\nsrc[CARETLOOSE] = '^' + src[LONECARET] + src[XRANGEPLAINLOOSE] + '$'\n\n// A simple gt/lt/eq thing, or just \"\" to indicate \"any version\"\nvar COMPARATORLOOSE = R++\nsrc[COMPARATORLOOSE] = '^' + src[GTLT] + '\\\\s*(' + LOOSEPLAIN + ')$|^$'\nvar COMPARATOR = R++\nsrc[COMPARATOR] = '^' + src[GTLT] + '\\\\s*(' + FULLPLAIN + ')$|^$'\n\n// An expression to strip any whitespace between the gtlt and the thing\n// it modifies, so that `> 1.2.3` ==> `>1.2.3`\nvar COMPARATORTRIM = R++\nsrc[COMPARATORTRIM] = '(\\\\s*)' + src[GTLT] +\n                      '\\\\s*(' + LOOSEPLAIN + '|' + src[XRANGEPLAIN] + ')'\n\n// this one has to use the /g flag\nre[COMPARATORTRIM] = new RegExp(src[COMPARATORTRIM], 'g')\nsafeRe[COMPARATORTRIM] = new RegExp(makeSafeRe(src[COMPARATORTRIM]), 'g')\nvar comparatorTrimReplace = '$1$2$3'\n\n// Something like `1.2.3 - 1.2.4`\n// Note that these all use the loose form, because they'll be\n// checked against either the strict or loose comparator form\n// later.\nvar HYPHENRANGE = R++\nsrc[HYPHENRANGE] = '^\\\\s*(' + src[XRANGEPLAIN] + ')' +\n                   '\\\\s+-\\\\s+' +\n                   '(' + src[XRANGEPLAIN] + ')' +\n                   '\\\\s*$'\n\nvar HYPHENRANGELOOSE = R++\nsrc[HYPHENRANGELOOSE] = '^\\\\s*(' + src[XRANGEPLAINLOOSE] + ')' +\n                        '\\\\s+-\\\\s+' +\n                        '(' + src[XRANGEPLAINLOOSE] + ')' +\n                        '\\\\s*$'\n\n// Star ranges basically just allow anything at all.\nvar STAR = R++\nsrc[STAR] = '(<|>)?=?\\\\s*\\\\*'\n\n// Compile to actual regexp objects.\n// All are flag-free, unless they were created above with a flag.\nfor (var i = 0; i < R; i++) {\n  debug(i, src[i])\n  if (!re[i]) {\n    re[i] = new RegExp(src[i])\n\n    // Replace all greedy whitespace to prevent regex dos issues. These regex are\n    // used internally via the safeRe object since all inputs in this library get\n    // normalized first to trim and collapse all extra whitespace. The original\n    // regexes are exported for userland consumption and lower level usage. A\n    // future breaking change could export the safer regex only with a note that\n    // all input should have extra whitespace removed.\n    safeRe[i] = new RegExp(makeSafeRe(src[i]))\n  }\n}\n\nexports.parse = parse\nfunction parse (version, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  if (version.length > MAX_LENGTH) {\n    return null\n  }\n\n  var r = options.loose ? safeRe[LOOSE] : safeRe[FULL]\n  if (!r.test(version)) {\n    return null\n  }\n\n  try {\n    return new SemVer(version, options)\n  } catch (er) {\n    return null\n  }\n}\n\nexports.valid = valid\nfunction valid (version, options) {\n  var v = parse(version, options)\n  return v ? v.version : null\n}\n\nexports.clean = clean\nfunction clean (version, options) {\n  var s = parse(version.trim().replace(/^[=v]+/, ''), options)\n  return s ? s.version : null\n}\n\nexports.SemVer = SemVer\n\nfunction SemVer (version, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n  if (version instanceof SemVer) {\n    if (version.loose === options.loose) {\n      return version\n    } else {\n      version = version.version\n    }\n  } else if (typeof version !== 'string') {\n    throw new TypeError('Invalid Version: ' + version)\n  }\n\n  if (version.length > MAX_LENGTH) {\n    throw new TypeError('version is longer than ' + MAX_LENGTH + ' characters')\n  }\n\n  if (!(this instanceof SemVer)) {\n    return new SemVer(version, options)\n  }\n\n  debug('SemVer', version, options)\n  this.options = options\n  this.loose = !!options.loose\n\n  var m = version.trim().match(options.loose ? safeRe[LOOSE] : safeRe[FULL])\n\n  if (!m) {\n    throw new TypeError('Invalid Version: ' + version)\n  }\n\n  this.raw = version\n\n  // these are actually numbers\n  this.major = +m[1]\n  this.minor = +m[2]\n  this.patch = +m[3]\n\n  if (this.major > MAX_SAFE_INTEGER || this.major < 0) {\n    throw new TypeError('Invalid major version')\n  }\n\n  if (this.minor > MAX_SAFE_INTEGER || this.minor < 0) {\n    throw new TypeError('Invalid minor version')\n  }\n\n  if (this.patch > MAX_SAFE_INTEGER || this.patch < 0) {\n    throw new TypeError('Invalid patch version')\n  }\n\n  // numberify any prerelease numeric ids\n  if (!m[4]) {\n    this.prerelease = []\n  } else {\n    this.prerelease = m[4].split('.').map(function (id) {\n      if (/^[0-9]+$/.test(id)) {\n        var num = +id\n        if (num >= 0 && num < MAX_SAFE_INTEGER) {\n          return num\n        }\n      }\n      return id\n    })\n  }\n\n  this.build = m[5] ? m[5].split('.') : []\n  this.format()\n}\n\nSemVer.prototype.format = function () {\n  this.version = this.major + '.' + this.minor + '.' + this.patch\n  if (this.prerelease.length) {\n    this.version += '-' + this.prerelease.join('.')\n  }\n  return this.version\n}\n\nSemVer.prototype.toString = function () {\n  return this.version\n}\n\nSemVer.prototype.compare = function (other) {\n  debug('SemVer.compare', this.version, this.options, other)\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  return this.compareMain(other) || this.comparePre(other)\n}\n\nSemVer.prototype.compareMain = function (other) {\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  return compareIdentifiers(this.major, other.major) ||\n         compareIdentifiers(this.minor, other.minor) ||\n         compareIdentifiers(this.patch, other.patch)\n}\n\nSemVer.prototype.comparePre = function (other) {\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  // NOT having a prerelease is > having one\n  if (this.prerelease.length && !other.prerelease.length) {\n    return -1\n  } else if (!this.prerelease.length && other.prerelease.length) {\n    return 1\n  } else if (!this.prerelease.length && !other.prerelease.length) {\n    return 0\n  }\n\n  var i = 0\n  do {\n    var a = this.prerelease[i]\n    var b = other.prerelease[i]\n    debug('prerelease compare', i, a, b)\n    if (a === undefined && b === undefined) {\n      return 0\n    } else if (b === undefined) {\n      return 1\n    } else if (a === undefined) {\n      return -1\n    } else if (a === b) {\n      continue\n    } else {\n      return compareIdentifiers(a, b)\n    }\n  } while (++i)\n}\n\n// preminor will bump the version up to the next minor release, and immediately\n// down to pre-release. premajor and prepatch work the same way.\nSemVer.prototype.inc = function (release, identifier) {\n  switch (release) {\n    case 'premajor':\n      this.prerelease.length = 0\n      this.patch = 0\n      this.minor = 0\n      this.major++\n      this.inc('pre', identifier)\n      break\n    case 'preminor':\n      this.prerelease.length = 0\n      this.patch = 0\n      this.minor++\n      this.inc('pre', identifier)\n      break\n    case 'prepatch':\n      // If this is already a prerelease, it will bump to the next version\n      // drop any prereleases that might already exist, since they are not\n      // relevant at this point.\n      this.prerelease.length = 0\n      this.inc('patch', identifier)\n      this.inc('pre', identifier)\n      break\n    // If the input is a non-prerelease version, this acts the same as\n    // prepatch.\n    case 'prerelease':\n      if (this.prerelease.length === 0) {\n        this.inc('patch', identifier)\n      }\n      this.inc('pre', identifier)\n      break\n\n    case 'major':\n      // If this is a pre-major version, bump up to the same major version.\n      // Otherwise increment major.\n      // 1.0.0-5 bumps to 1.0.0\n      // 1.1.0 bumps to 2.0.0\n      if (this.minor !== 0 ||\n          this.patch !== 0 ||\n          this.prerelease.length === 0) {\n        this.major++\n      }\n      this.minor = 0\n      this.patch = 0\n      this.prerelease = []\n      break\n    case 'minor':\n      // If this is a pre-minor version, bump up to the same minor version.\n      // Otherwise increment minor.\n      // 1.2.0-5 bumps to 1.2.0\n      // 1.2.1 bumps to 1.3.0\n      if (this.patch !== 0 || this.prerelease.length === 0) {\n        this.minor++\n      }\n      this.patch = 0\n      this.prerelease = []\n      break\n    case 'patch':\n      // If this is not a pre-release version, it will increment the patch.\n      // If it is a pre-release it will bump up to the same patch version.\n      // 1.2.0-5 patches to 1.2.0\n      // 1.2.0 patches to 1.2.1\n      if (this.prerelease.length === 0) {\n        this.patch++\n      }\n      this.prerelease = []\n      break\n    // This probably shouldn't be used publicly.\n    // 1.0.0 \"pre\" would become 1.0.0-0 which is the wrong direction.\n    case 'pre':\n      if (this.prerelease.length === 0) {\n        this.prerelease = [0]\n      } else {\n        var i = this.prerelease.length\n        while (--i >= 0) {\n          if (typeof this.prerelease[i] === 'number') {\n            this.prerelease[i]++\n            i = -2\n          }\n        }\n        if (i === -1) {\n          // didn't increment anything\n          this.prerelease.push(0)\n        }\n      }\n      if (identifier) {\n        // 1.2.0-beta.1 bumps to 1.2.0-beta.2,\n        // 1.2.0-beta.fooblz or 1.2.0-beta bumps to 1.2.0-beta.0\n        if (this.prerelease[0] === identifier) {\n          if (isNaN(this.prerelease[1])) {\n            this.prerelease = [identifier, 0]\n          }\n        } else {\n          this.prerelease = [identifier, 0]\n        }\n      }\n      break\n\n    default:\n      throw new Error('invalid increment argument: ' + release)\n  }\n  this.format()\n  this.raw = this.version\n  return this\n}\n\nexports.inc = inc\nfunction inc (version, release, loose, identifier) {\n  if (typeof (loose) === 'string') {\n    identifier = loose\n    loose = undefined\n  }\n\n  try {\n    return new SemVer(version, loose).inc(release, identifier).version\n  } catch (er) {\n    return null\n  }\n}\n\nexports.diff = diff\nfunction diff (version1, version2) {\n  if (eq(version1, version2)) {\n    return null\n  } else {\n    var v1 = parse(version1)\n    var v2 = parse(version2)\n    var prefix = ''\n    if (v1.prerelease.length || v2.prerelease.length) {\n      prefix = 'pre'\n      var defaultResult = 'prerelease'\n    }\n    for (var key in v1) {\n      if (key === 'major' || key === 'minor' || key === 'patch') {\n        if (v1[key] !== v2[key]) {\n          return prefix + key\n        }\n      }\n    }\n    return defaultResult // may be undefined\n  }\n}\n\nexports.compareIdentifiers = compareIdentifiers\n\nvar numeric = /^[0-9]+$/\nfunction compareIdentifiers (a, b) {\n  var anum = numeric.test(a)\n  var bnum = numeric.test(b)\n\n  if (anum && bnum) {\n    a = +a\n    b = +b\n  }\n\n  return a === b ? 0\n    : (anum && !bnum) ? -1\n    : (bnum && !anum) ? 1\n    : a < b ? -1\n    : 1\n}\n\nexports.rcompareIdentifiers = rcompareIdentifiers\nfunction rcompareIdentifiers (a, b) {\n  return compareIdentifiers(b, a)\n}\n\nexports.major = major\nfunction major (a, loose) {\n  return new SemVer(a, loose).major\n}\n\nexports.minor = minor\nfunction minor (a, loose) {\n  return new SemVer(a, loose).minor\n}\n\nexports.patch = patch\nfunction patch (a, loose) {\n  return new SemVer(a, loose).patch\n}\n\nexports.compare = compare\nfunction compare (a, b, loose) {\n  return new SemVer(a, loose).compare(new SemVer(b, loose))\n}\n\nexports.compareLoose = compareLoose\nfunction compareLoose (a, b) {\n  return compare(a, b, true)\n}\n\nexports.rcompare = rcompare\nfunction rcompare (a, b, loose) {\n  return compare(b, a, loose)\n}\n\nexports.sort = sort\nfunction sort (list, loose) {\n  return list.sort(function (a, b) {\n    return exports.compare(a, b, loose)\n  })\n}\n\nexports.rsort = rsort\nfunction rsort (list, loose) {\n  return list.sort(function (a, b) {\n    return exports.rcompare(a, b, loose)\n  })\n}\n\nexports.gt = gt\nfunction gt (a, b, loose) {\n  return compare(a, b, loose) > 0\n}\n\nexports.lt = lt\nfunction lt (a, b, loose) {\n  return compare(a, b, loose) < 0\n}\n\nexports.eq = eq\nfunction eq (a, b, loose) {\n  return compare(a, b, loose) === 0\n}\n\nexports.neq = neq\nfunction neq (a, b, loose) {\n  return compare(a, b, loose) !== 0\n}\n\nexports.gte = gte\nfunction gte (a, b, loose) {\n  return compare(a, b, loose) >= 0\n}\n\nexports.lte = lte\nfunction lte (a, b, loose) {\n  return compare(a, b, loose) <= 0\n}\n\nexports.cmp = cmp\nfunction cmp (a, op, b, loose) {\n  switch (op) {\n    case '===':\n      if (typeof a === 'object')\n        a = a.version\n      if (typeof b === 'object')\n        b = b.version\n      return a === b\n\n    case '!==':\n      if (typeof a === 'object')\n        a = a.version\n      if (typeof b === 'object')\n        b = b.version\n      return a !== b\n\n    case '':\n    case '=':\n    case '==':\n      return eq(a, b, loose)\n\n    case '!=':\n      return neq(a, b, loose)\n\n    case '>':\n      return gt(a, b, loose)\n\n    case '>=':\n      return gte(a, b, loose)\n\n    case '<':\n      return lt(a, b, loose)\n\n    case '<=':\n      return lte(a, b, loose)\n\n    default:\n      throw new TypeError('Invalid operator: ' + op)\n  }\n}\n\nexports.Comparator = Comparator\nfunction Comparator (comp, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (comp instanceof Comparator) {\n    if (comp.loose === !!options.loose) {\n      return comp\n    } else {\n      comp = comp.value\n    }\n  }\n\n  if (!(this instanceof Comparator)) {\n    return new Comparator(comp, options)\n  }\n\n  comp = comp.trim().split(/\\s+/).join(' ')\n  debug('comparator', comp, options)\n  this.options = options\n  this.loose = !!options.loose\n  this.parse(comp)\n\n  if (this.semver === ANY) {\n    this.value = ''\n  } else {\n    this.value = this.operator + this.semver.version\n  }\n\n  debug('comp', this)\n}\n\nvar ANY = {}\nComparator.prototype.parse = function (comp) {\n  var r = this.options.loose ? safeRe[COMPARATORLOOSE] : safeRe[COMPARATOR]\n  var m = comp.match(r)\n\n  if (!m) {\n    throw new TypeError('Invalid comparator: ' + comp)\n  }\n\n  this.operator = m[1]\n  if (this.operator === '=') {\n    this.operator = ''\n  }\n\n  // if it literally is just '>' or '' then allow anything.\n  if (!m[2]) {\n    this.semver = ANY\n  } else {\n    this.semver = new SemVer(m[2], this.options.loose)\n  }\n}\n\nComparator.prototype.toString = function () {\n  return this.value\n}\n\nComparator.prototype.test = function (version) {\n  debug('Comparator.test', version, this.options.loose)\n\n  if (this.semver === ANY) {\n    return true\n  }\n\n  if (typeof version === 'string') {\n    version = new SemVer(version, this.options)\n  }\n\n  return cmp(version, this.operator, this.semver, this.options)\n}\n\nComparator.prototype.intersects = function (comp, options) {\n  if (!(comp instanceof Comparator)) {\n    throw new TypeError('a Comparator is required')\n  }\n\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  var rangeTmp\n\n  if (this.operator === '') {\n    rangeTmp = new Range(comp.value, options)\n    return satisfies(this.value, rangeTmp, options)\n  } else if (comp.operator === '') {\n    rangeTmp = new Range(this.value, options)\n    return satisfies(comp.semver, rangeTmp, options)\n  }\n\n  var sameDirectionIncreasing =\n    (this.operator === '>=' || this.operator === '>') &&\n    (comp.operator === '>=' || comp.operator === '>')\n  var sameDirectionDecreasing =\n    (this.operator === '<=' || this.operator === '<') &&\n    (comp.operator === '<=' || comp.operator === '<')\n  var sameSemVer = this.semver.version === comp.semver.version\n  var differentDirectionsInclusive =\n    (this.operator === '>=' || this.operator === '<=') &&\n    (comp.operator === '>=' || comp.operator === '<=')\n  var oppositeDirectionsLessThan =\n    cmp(this.semver, '<', comp.semver, options) &&\n    ((this.operator === '>=' || this.operator === '>') &&\n    (comp.operator === '<=' || comp.operator === '<'))\n  var oppositeDirectionsGreaterThan =\n    cmp(this.semver, '>', comp.semver, options) &&\n    ((this.operator === '<=' || this.operator === '<') &&\n    (comp.operator === '>=' || comp.operator === '>'))\n\n  return sameDirectionIncreasing || sameDirectionDecreasing ||\n    (sameSemVer && differentDirectionsInclusive) ||\n    oppositeDirectionsLessThan || oppositeDirectionsGreaterThan\n}\n\nexports.Range = Range\nfunction Range (range, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (range instanceof Range) {\n    if (range.loose === !!options.loose &&\n        range.includePrerelease === !!options.includePrerelease) {\n      return range\n    } else {\n      return new Range(range.raw, options)\n    }\n  }\n\n  if (range instanceof Comparator) {\n    return new Range(range.value, options)\n  }\n\n  if (!(this instanceof Range)) {\n    return new Range(range, options)\n  }\n\n  this.options = options\n  this.loose = !!options.loose\n  this.includePrerelease = !!options.includePrerelease\n\n  // First reduce all whitespace as much as possible so we do not have to rely\n  // on potentially slow regexes like \\s*. This is then stored and used for\n  // future error messages as well.\n  this.raw = range\n    .trim()\n    .split(/\\s+/)\n    .join(' ')\n\n  // First, split based on boolean or ||\n  this.set = this.raw.split('||').map(function (range) {\n    return this.parseRange(range.trim())\n  }, this).filter(function (c) {\n    // throw out any that are not relevant for whatever reason\n    return c.length\n  })\n\n  if (!this.set.length) {\n    throw new TypeError('Invalid SemVer Range: ' + this.raw)\n  }\n\n  this.format()\n}\n\nRange.prototype.format = function () {\n  this.range = this.set.map(function (comps) {\n    return comps.join(' ').trim()\n  }).join('||').trim()\n  return this.range\n}\n\nRange.prototype.toString = function () {\n  return this.range\n}\n\nRange.prototype.parseRange = function (range) {\n  var loose = this.options.loose\n  // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n  var hr = loose ? safeRe[HYPHENRANGELOOSE] : safeRe[HYPHENRANGE]\n  range = range.replace(hr, hyphenReplace)\n  debug('hyphen replace', range)\n  // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n  range = range.replace(safeRe[COMPARATORTRIM], comparatorTrimReplace)\n  debug('comparator trim', range, safeRe[COMPARATORTRIM])\n\n  // `~ 1.2.3` => `~1.2.3`\n  range = range.replace(safeRe[TILDETRIM], tildeTrimReplace)\n\n  // `^ 1.2.3` => `^1.2.3`\n  range = range.replace(safeRe[CARETTRIM], caretTrimReplace)\n\n  // At this point, the range is completely trimmed and\n  // ready to be split into comparators.\n  var compRe = loose ? safeRe[COMPARATORLOOSE] : safeRe[COMPARATOR]\n  var set = range.split(' ').map(function (comp) {\n    return parseComparator(comp, this.options)\n  }, this).join(' ').split(/\\s+/)\n  if (this.options.loose) {\n    // in loose mode, throw out any that are not valid comparators\n    set = set.filter(function (comp) {\n      return !!comp.match(compRe)\n    })\n  }\n  set = set.map(function (comp) {\n    return new Comparator(comp, this.options)\n  }, this)\n\n  return set\n}\n\nRange.prototype.intersects = function (range, options) {\n  if (!(range instanceof Range)) {\n    throw new TypeError('a Range is required')\n  }\n\n  return this.set.some(function (thisComparators) {\n    return thisComparators.every(function (thisComparator) {\n      return range.set.some(function (rangeComparators) {\n        return rangeComparators.every(function (rangeComparator) {\n          return thisComparator.intersects(rangeComparator, options)\n        })\n      })\n    })\n  })\n}\n\n// Mostly just for testing and legacy API reasons\nexports.toComparators = toComparators\nfunction toComparators (range, options) {\n  return new Range(range, options).set.map(function (comp) {\n    return comp.map(function (c) {\n      return c.value\n    }).join(' ').trim().split(' ')\n  })\n}\n\n// comprised of xranges, tildes, stars, and gtlt's at this point.\n// already replaced the hyphen ranges\n// turn into a set of JUST comparators.\nfunction parseComparator (comp, options) {\n  debug('comp', comp, options)\n  comp = replaceCarets(comp, options)\n  debug('caret', comp)\n  comp = replaceTildes(comp, options)\n  debug('tildes', comp)\n  comp = replaceXRanges(comp, options)\n  debug('xrange', comp)\n  comp = replaceStars(comp, options)\n  debug('stars', comp)\n  return comp\n}\n\nfunction isX (id) {\n  return !id || id.toLowerCase() === 'x' || id === '*'\n}\n\n// ~, ~> --> * (any, kinda silly)\n// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0\n// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0\n// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0\n// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0\n// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0\nfunction replaceTildes (comp, options) {\n  return comp.trim().split(/\\s+/).map(function (comp) {\n    return replaceTilde(comp, options)\n  }).join(' ')\n}\n\nfunction replaceTilde (comp, options) {\n  var r = options.loose ? safeRe[TILDELOOSE] : safeRe[TILDE]\n  return comp.replace(r, function (_, M, m, p, pr) {\n    debug('tilde', comp, _, M, m, p, pr)\n    var ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (isX(p)) {\n      // ~1.2 == >=1.2.0 <1.3.0\n      ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n    } else if (pr) {\n      debug('replaceTilde pr', pr)\n      ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n            ' <' + M + '.' + (+m + 1) + '.0'\n    } else {\n      // ~1.2.3 == >=1.2.3 <1.3.0\n      ret = '>=' + M + '.' + m + '.' + p +\n            ' <' + M + '.' + (+m + 1) + '.0'\n    }\n\n    debug('tilde return', ret)\n    return ret\n  })\n}\n\n// ^ --> * (any, kinda silly)\n// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0\n// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0\n// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0\n// ^1.2.3 --> >=1.2.3 <2.0.0\n// ^1.2.0 --> >=1.2.0 <2.0.0\nfunction replaceCarets (comp, options) {\n  return comp.trim().split(/\\s+/).map(function (comp) {\n    return replaceCaret(comp, options)\n  }).join(' ')\n}\n\nfunction replaceCaret (comp, options) {\n  debug('caret', comp, options)\n  var r = options.loose ? safeRe[CARETLOOSE] : safeRe[CARET]\n  return comp.replace(r, function (_, M, m, p, pr) {\n    debug('caret', comp, _, M, m, p, pr)\n    var ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (isX(p)) {\n      if (M === '0') {\n        ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n      } else {\n        ret = '>=' + M + '.' + m + '.0 <' + (+M + 1) + '.0.0'\n      }\n    } else if (pr) {\n      debug('replaceCaret pr', pr)\n      if (M === '0') {\n        if (m === '0') {\n          ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n                ' <' + M + '.' + m + '.' + (+p + 1)\n        } else {\n          ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n                ' <' + M + '.' + (+m + 1) + '.0'\n        }\n      } else {\n        ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n              ' <' + (+M + 1) + '.0.0'\n      }\n    } else {\n      debug('no pr')\n      if (M === '0') {\n        if (m === '0') {\n          ret = '>=' + M + '.' + m + '.' + p +\n                ' <' + M + '.' + m + '.' + (+p + 1)\n        } else {\n          ret = '>=' + M + '.' + m + '.' + p +\n                ' <' + M + '.' + (+m + 1) + '.0'\n        }\n      } else {\n        ret = '>=' + M + '.' + m + '.' + p +\n              ' <' + (+M + 1) + '.0.0'\n      }\n    }\n\n    debug('caret return', ret)\n    return ret\n  })\n}\n\nfunction replaceXRanges (comp, options) {\n  debug('replaceXRanges', comp, options)\n  return comp.split(/\\s+/).map(function (comp) {\n    return replaceXRange(comp, options)\n  }).join(' ')\n}\n\nfunction replaceXRange (comp, options) {\n  comp = comp.trim()\n  var r = options.loose ? safeRe[XRANGELOOSE] : safeRe[XRANGE]\n  return comp.replace(r, function (ret, gtlt, M, m, p, pr) {\n    debug('xRange', comp, ret, gtlt, M, m, p, pr)\n    var xM = isX(M)\n    var xm = xM || isX(m)\n    var xp = xm || isX(p)\n    var anyX = xp\n\n    if (gtlt === '=' && anyX) {\n      gtlt = ''\n    }\n\n    if (xM) {\n      if (gtlt === '>' || gtlt === '<') {\n        // nothing is allowed\n        ret = '<0.0.0'\n      } else {\n        // nothing is forbidden\n        ret = '*'\n      }\n    } else if (gtlt && anyX) {\n      // we know patch is an x, because we have any x at all.\n      // replace X with 0\n      if (xm) {\n        m = 0\n      }\n      p = 0\n\n      if (gtlt === '>') {\n        // >1 => >=2.0.0\n        // >1.2 => >=1.3.0\n        // >1.2.3 => >= 1.2.4\n        gtlt = '>='\n        if (xm) {\n          M = +M + 1\n          m = 0\n          p = 0\n        } else {\n          m = +m + 1\n          p = 0\n        }\n      } else if (gtlt === '<=') {\n        // <=0.7.x is actually <0.8.0, since any 0.7.x should\n        // pass.  Similarly, <=7.x is actually <8.0.0, etc.\n        gtlt = '<'\n        if (xm) {\n          M = +M + 1\n        } else {\n          m = +m + 1\n        }\n      }\n\n      ret = gtlt + M + '.' + m + '.' + p\n    } else if (xm) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (xp) {\n      ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n    }\n\n    debug('xRange return', ret)\n\n    return ret\n  })\n}\n\n// Because * is AND-ed with everything else in the comparator,\n// and '' means \"any version\", just remove the *s entirely.\nfunction replaceStars (comp, options) {\n  debug('replaceStars', comp, options)\n  // Looseness is ignored here.  star is always as loose as it gets!\n  return comp.trim().replace(safeRe[STAR], '')\n}\n\n// This function is passed to string.replace(safeRe[HYPHENRANGE])\n// M, m, patch, prerelease, build\n// 1.2 - 3.4.5 => >=1.2.0 <=3.4.5\n// 1.2.3 - 3.4 => >=1.2.0 <3.5.0 Any 3.4.x will do\n// 1.2 - 3.4 => >=1.2.0 <3.5.0\nfunction hyphenReplace ($0,\n  from, fM, fm, fp, fpr, fb,\n  to, tM, tm, tp, tpr, tb) {\n  if (isX(fM)) {\n    from = ''\n  } else if (isX(fm)) {\n    from = '>=' + fM + '.0.0'\n  } else if (isX(fp)) {\n    from = '>=' + fM + '.' + fm + '.0'\n  } else {\n    from = '>=' + from\n  }\n\n  if (isX(tM)) {\n    to = ''\n  } else if (isX(tm)) {\n    to = '<' + (+tM + 1) + '.0.0'\n  } else if (isX(tp)) {\n    to = '<' + tM + '.' + (+tm + 1) + '.0'\n  } else if (tpr) {\n    to = '<=' + tM + '.' + tm + '.' + tp + '-' + tpr\n  } else {\n    to = '<=' + to\n  }\n\n  return (from + ' ' + to).trim()\n}\n\n// if ANY of the sets match ALL of its comparators, then pass\nRange.prototype.test = function (version) {\n  if (!version) {\n    return false\n  }\n\n  if (typeof version === 'string') {\n    version = new SemVer(version, this.options)\n  }\n\n  for (var i = 0; i < this.set.length; i++) {\n    if (testSet(this.set[i], version, this.options)) {\n      return true\n    }\n  }\n  return false\n}\n\nfunction testSet (set, version, options) {\n  for (var i = 0; i < set.length; i++) {\n    if (!set[i].test(version)) {\n      return false\n    }\n  }\n\n  if (version.prerelease.length && !options.includePrerelease) {\n    // Find the set of versions that are allowed to have prereleases\n    // For example, ^1.2.3-pr.1 desugars to >=1.2.3-pr.1 <2.0.0\n    // That should allow `1.2.3-pr.2` to pass.\n    // However, `1.2.4-alpha.notready` should NOT be allowed,\n    // even though it's within the range set by the comparators.\n    for (i = 0; i < set.length; i++) {\n      debug(set[i].semver)\n      if (set[i].semver === ANY) {\n        continue\n      }\n\n      if (set[i].semver.prerelease.length > 0) {\n        var allowed = set[i].semver\n        if (allowed.major === version.major &&\n            allowed.minor === version.minor &&\n            allowed.patch === version.patch) {\n          return true\n        }\n      }\n    }\n\n    // Version has a -pre, but it's not one of the ones we like.\n    return false\n  }\n\n  return true\n}\n\nexports.satisfies = satisfies\nfunction satisfies (version, range, options) {\n  try {\n    range = new Range(range, options)\n  } catch (er) {\n    return false\n  }\n  return range.test(version)\n}\n\nexports.maxSatisfying = maxSatisfying\nfunction maxSatisfying (versions, range, options) {\n  var max = null\n  var maxSV = null\n  try {\n    var rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach(function (v) {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!max || maxSV.compare(v) === -1) {\n        // compare(max, v, true)\n        max = v\n        maxSV = new SemVer(max, options)\n      }\n    }\n  })\n  return max\n}\n\nexports.minSatisfying = minSatisfying\nfunction minSatisfying (versions, range, options) {\n  var min = null\n  var minSV = null\n  try {\n    var rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach(function (v) {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!min || minSV.compare(v) === 1) {\n        // compare(min, v, true)\n        min = v\n        minSV = new SemVer(min, options)\n      }\n    }\n  })\n  return min\n}\n\nexports.minVersion = minVersion\nfunction minVersion (range, loose) {\n  range = new Range(range, loose)\n\n  var minver = new SemVer('0.0.0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = new SemVer('0.0.0-0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = null\n  for (var i = 0; i < range.set.length; ++i) {\n    var comparators = range.set[i]\n\n    comparators.forEach(function (comparator) {\n      // Clone to avoid manipulating the comparator's semver object.\n      var compver = new SemVer(comparator.semver.version)\n      switch (comparator.operator) {\n        case '>':\n          if (compver.prerelease.length === 0) {\n            compver.patch++\n          } else {\n            compver.prerelease.push(0)\n          }\n          compver.raw = compver.format()\n          /* fallthrough */\n        case '':\n        case '>=':\n          if (!minver || gt(minver, compver)) {\n            minver = compver\n          }\n          break\n        case '<':\n        case '<=':\n          /* Ignore maximum versions */\n          break\n        /* istanbul ignore next */\n        default:\n          throw new Error('Unexpected operation: ' + comparator.operator)\n      }\n    })\n  }\n\n  if (minver && range.test(minver)) {\n    return minver\n  }\n\n  return null\n}\n\nexports.validRange = validRange\nfunction validRange (range, options) {\n  try {\n    // Return '*' instead of '' so that truthiness works.\n    // This will throw if it's invalid anyway\n    return new Range(range, options).range || '*'\n  } catch (er) {\n    return null\n  }\n}\n\n// Determine if version is less than all the versions possible in the range\nexports.ltr = ltr\nfunction ltr (version, range, options) {\n  return outside(version, range, '<', options)\n}\n\n// Determine if version is greater than all the versions possible in the range.\nexports.gtr = gtr\nfunction gtr (version, range, options) {\n  return outside(version, range, '>', options)\n}\n\nexports.outside = outside\nfunction outside (version, range, hilo, options) {\n  version = new SemVer(version, options)\n  range = new Range(range, options)\n\n  var gtfn, ltefn, ltfn, comp, ecomp\n  switch (hilo) {\n    case '>':\n      gtfn = gt\n      ltefn = lte\n      ltfn = lt\n      comp = '>'\n      ecomp = '>='\n      break\n    case '<':\n      gtfn = lt\n      ltefn = gte\n      ltfn = gt\n      comp = '<'\n      ecomp = '<='\n      break\n    default:\n      throw new TypeError('Must provide a hilo val of \"<\" or \">\"')\n  }\n\n  // If it satisifes the range it is not outside\n  if (satisfies(version, range, options)) {\n    return false\n  }\n\n  // From now on, variable terms are as if we're in \"gtr\" mode.\n  // but note that everything is flipped for the \"ltr\" function.\n\n  for (var i = 0; i < range.set.length; ++i) {\n    var comparators = range.set[i]\n\n    var high = null\n    var low = null\n\n    comparators.forEach(function (comparator) {\n      if (comparator.semver === ANY) {\n        comparator = new Comparator('>=0.0.0')\n      }\n      high = high || comparator\n      low = low || comparator\n      if (gtfn(comparator.semver, high.semver, options)) {\n        high = comparator\n      } else if (ltfn(comparator.semver, low.semver, options)) {\n        low = comparator\n      }\n    })\n\n    // If the edge version comparator has a operator then our version\n    // isn't outside it\n    if (high.operator === comp || high.operator === ecomp) {\n      return false\n    }\n\n    // If the lowest version comparator has an operator and our version\n    // is less than it then it isn't higher than the range\n    if ((!low.operator || low.operator === comp) &&\n        ltefn(version, low.semver)) {\n      return false\n    } else if (low.operator === ecomp && ltfn(version, low.semver)) {\n      return false\n    }\n  }\n  return true\n}\n\nexports.prerelease = prerelease\nfunction prerelease (version, options) {\n  var parsed = parse(version, options)\n  return (parsed && parsed.prerelease.length) ? parsed.prerelease : null\n}\n\nexports.intersects = intersects\nfunction intersects (r1, r2, options) {\n  r1 = new Range(r1, options)\n  r2 = new Range(r2, options)\n  return r1.intersects(r2)\n}\n\nexports.coerce = coerce\nfunction coerce (version) {\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  var match = version.match(safeRe[COERCE])\n\n  if (match == null) {\n    return null\n  }\n\n  return parse(match[1] +\n    '.' + (match[2] || '0') +\n    '.' + (match[3] || '0'))\n}\n", "[\n\t\"0BSD\",\n\t\"3D-Slicer-1.0\",\n\t\"AAL\",\n\t\"ADSL\",\n\t\"AFL-1.1\",\n\t\"AFL-1.2\",\n\t\"AFL-2.0\",\n\t\"AFL-2.1\",\n\t\"AFL-3.0\",\n\t\"AGPL-1.0-only\",\n\t\"AGPL-1.0-or-later\",\n\t\"AGPL-3.0-only\",\n\t\"AGPL-3.0-or-later\",\n\t\"AMD-newlib\",\n\t\"AMDPLPA\",\n\t\"AML\",\n\t\"AML-glslang\",\n\t\"AMPAS\",\n\t\"ANTLR-PD\",\n\t\"ANTLR-PD-fallback\",\n\t\"APAFML\",\n\t\"APL-1.0\",\n\t\"APSL-1.0\",\n\t\"APSL-1.1\",\n\t\"APSL-1.2\",\n\t\"APSL-2.0\",\n\t\"ASWF-Digital-Assets-1.0\",\n\t\"ASWF-Digital-Assets-1.1\",\n\t\"Abstyles\",\n\t\"AdaCore-doc\",\n\t\"Adobe-2006\",\n\t\"Adobe-Display-PostScript\",\n\t\"Adobe-Glyph\",\n\t\"Adobe-Utopia\",\n\t\"Afmparse\",\n\t\"Aladdin\",\n\t\"Apache-1.0\",\n\t\"Apache-1.1\",\n\t\"Apache-2.0\",\n\t\"App-s2p\",\n\t\"Arphic-1999\",\n\t\"Artistic-1.0\",\n\t\"Artistic-1.0-Perl\",\n\t\"Artistic-1.0-cl8\",\n\t\"Artistic-2.0\",\n\t\"BSD-1-Clause\",\n\t\"BSD-2-Clause\",\n\t\"BSD-2-Clause-Darwin\",\n\t\"BSD-2-Clause-Patent\",\n\t\"BSD-2-Clause-Views\",\n\t\"BSD-2-Clause-first-lines\",\n\t\"BSD-3-Clause\",\n\t\"BSD-3-Clause-Attribution\",\n\t\"BSD-3-Clause-Clear\",\n\t\"BSD-3-Clause-HP\",\n\t\"BSD-3-Clause-LBNL\",\n\t\"BSD-3-Clause-Modification\",\n\t\"BSD-3-Clause-No-Military-License\",\n\t\"BSD-3-Clause-No-Nuclear-License\",\n\t\"BSD-3-Clause-No-Nuclear-License-2014\",\n\t\"BSD-3-Clause-No-Nuclear-Warranty\",\n\t\"BSD-3-Clause-Open-MPI\",\n\t\"BSD-3-Clause-Sun\",\n\t\"BSD-3-Clause-acpica\",\n\t\"BSD-3-Clause-flex\",\n\t\"BSD-4-Clause\",\n\t\"BSD-4-Clause-Shortened\",\n\t\"BSD-4-Clause-UC\",\n\t\"BSD-4.3RENO\",\n\t\"BSD-4.3TAHOE\",\n\t\"BSD-Advertising-Acknowledgement\",\n\t\"BSD-Attribution-HPND-disclaimer\",\n\t\"BSD-Inferno-Nettverk\",\n\t\"BSD-Protection\",\n\t\"BSD-Source-Code\",\n\t\"BSD-Source-beginning-file\",\n\t\"BSD-Systemics\",\n\t\"BSD-Systemics-W3Works\",\n\t\"BSL-1.0\",\n\t\"BUSL-1.1\",\n\t\"Baekmuk\",\n\t\"Bahyph\",\n\t\"Barr\",\n\t\"Beerware\",\n\t\"BitTorrent-1.0\",\n\t\"BitTorrent-1.1\",\n\t\"Bitstream-Charter\",\n\t\"Bitstream-Vera\",\n\t\"BlueOak-1.0.0\",\n\t\"Boehm-GC\",\n\t\"Borceux\",\n\t\"Brian-Gladman-2-Clause\",\n\t\"Brian-Gladman-3-Clause\",\n\t\"C-UDA-1.0\",\n\t\"CAL-1.0\",\n\t\"CAL-1.0-Combined-Work-Exception\",\n\t\"CATOSL-1.1\",\n\t\"CC-BY-1.0\",\n\t\"CC-BY-2.0\",\n\t\"CC-BY-2.5\",\n\t\"CC-BY-2.5-AU\",\n\t\"CC-BY-3.0\",\n\t\"CC-BY-3.0-AT\",\n\t\"CC-BY-3.0-AU\",\n\t\"CC-BY-3.0-DE\",\n\t\"CC-BY-3.0-IGO\",\n\t\"CC-BY-3.0-NL\",\n\t\"CC-BY-3.0-US\",\n\t\"CC-BY-4.0\",\n\t\"CC-BY-NC-1.0\",\n\t\"CC-BY-NC-2.0\",\n\t\"CC-BY-NC-2.5\",\n\t\"CC-BY-NC-3.0\",\n\t\"CC-BY-NC-3.0-DE\",\n\t\"CC-BY-NC-4.0\",\n\t\"CC-BY-NC-ND-1.0\",\n\t\"CC-BY-NC-ND-2.0\",\n\t\"CC-BY-NC-ND-2.5\",\n\t\"CC-BY-NC-ND-3.0\",\n\t\"CC-BY-NC-ND-3.0-DE\",\n\t\"CC-BY-NC-ND-3.0-IGO\",\n\t\"CC-BY-NC-ND-4.0\",\n\t\"CC-BY-NC-SA-1.0\",\n\t\"CC-BY-NC-SA-2.0\",\n\t\"CC-BY-NC-SA-2.0-DE\",\n\t\"CC-BY-NC-SA-2.0-FR\",\n\t\"CC-BY-NC-SA-2.0-UK\",\n\t\"CC-BY-NC-SA-2.5\",\n\t\"CC-BY-NC-SA-3.0\",\n\t\"CC-BY-NC-SA-3.0-DE\",\n\t\"CC-BY-NC-SA-3.0-IGO\",\n\t\"CC-BY-NC-SA-4.0\",\n\t\"CC-BY-ND-1.0\",\n\t\"CC-BY-ND-2.0\",\n\t\"CC-BY-ND-2.5\",\n\t\"CC-BY-ND-3.0\",\n\t\"CC-BY-ND-3.0-DE\",\n\t\"CC-BY-ND-4.0\",\n\t\"CC-BY-SA-1.0\",\n\t\"CC-BY-SA-2.0\",\n\t\"CC-BY-SA-2.0-UK\",\n\t\"CC-BY-SA-2.1-JP\",\n\t\"CC-BY-SA-2.5\",\n\t\"CC-BY-SA-3.0\",\n\t\"CC-BY-SA-3.0-AT\",\n\t\"CC-BY-SA-3.0-DE\",\n\t\"CC-BY-SA-3.0-IGO\",\n\t\"CC-BY-SA-4.0\",\n\t\"CC-PDDC\",\n\t\"CC0-1.0\",\n\t\"CDDL-1.0\",\n\t\"CDDL-1.1\",\n\t\"CDL-1.0\",\n\t\"CDLA-Permissive-1.0\",\n\t\"CDLA-Permissive-2.0\",\n\t\"CDLA-Sharing-1.0\",\n\t\"CECILL-1.0\",\n\t\"CECILL-1.1\",\n\t\"CECILL-2.0\",\n\t\"CECILL-2.1\",\n\t\"CECILL-B\",\n\t\"CECILL-C\",\n\t\"CERN-OHL-1.1\",\n\t\"CERN-OHL-1.2\",\n\t\"CERN-OHL-P-2.0\",\n\t\"CERN-OHL-S-2.0\",\n\t\"CERN-OHL-W-2.0\",\n\t\"CFITSIO\",\n\t\"CMU-Mach\",\n\t\"CMU-Mach-nodoc\",\n\t\"CNRI-Jython\",\n\t\"CNRI-Python\",\n\t\"CNRI-Python-GPL-Compatible\",\n\t\"COIL-1.0\",\n\t\"CPAL-1.0\",\n\t\"CPL-1.0\",\n\t\"CPOL-1.02\",\n\t\"CUA-OPL-1.0\",\n\t\"Caldera\",\n\t\"Caldera-no-preamble\",\n\t\"Catharon\",\n\t\"ClArtistic\",\n\t\"Clips\",\n\t\"Community-Spec-1.0\",\n\t\"Condor-1.1\",\n\t\"Cornell-Lossless-JPEG\",\n\t\"Cronyx\",\n\t\"Crossword\",\n\t\"CrystalStacker\",\n\t\"Cube\",\n\t\"D-FSL-1.0\",\n\t\"DEC-3-Clause\",\n\t\"DL-DE-BY-2.0\",\n\t\"DL-DE-ZERO-2.0\",\n\t\"DOC\",\n\t\"DRL-1.0\",\n\t\"DRL-1.1\",\n\t\"DSDP\",\n\t\"DocBook-Schema\",\n\t\"DocBook-XML\",\n\t\"Dotseqn\",\n\t\"ECL-1.0\",\n\t\"ECL-2.0\",\n\t\"EFL-1.0\",\n\t\"EFL-2.0\",\n\t\"EPICS\",\n\t\"EPL-1.0\",\n\t\"EPL-2.0\",\n\t\"EUDatagrid\",\n\t\"EUPL-1.0\",\n\t\"EUPL-1.1\",\n\t\"EUPL-1.2\",\n\t\"Elastic-2.0\",\n\t\"Entessa\",\n\t\"ErlPL-1.1\",\n\t\"Eurosym\",\n\t\"FBM\",\n\t\"FDK-AAC\",\n\t\"FSFAP\",\n\t\"FSFAP-no-warranty-disclaimer\",\n\t\"FSFUL\",\n\t\"FSFULLR\",\n\t\"FSFULLRWD\",\n\t\"FTL\",\n\t\"Fair\",\n\t\"Ferguson-Twofish\",\n\t\"Frameworx-1.0\",\n\t\"FreeBSD-DOC\",\n\t\"FreeImage\",\n\t\"Furuseth\",\n\t\"GCR-docs\",\n\t\"GD\",\n\t\"GFDL-1.1-invariants-only\",\n\t\"GFDL-1.1-invariants-or-later\",\n\t\"GFDL-1.1-no-invariants-only\",\n\t\"GFDL-1.1-no-invariants-or-later\",\n\t\"GFDL-1.1-only\",\n\t\"GFDL-1.1-or-later\",\n\t\"GFDL-1.2-invariants-only\",\n\t\"GFDL-1.2-invariants-or-later\",\n\t\"GFDL-1.2-no-invariants-only\",\n\t\"GFDL-1.2-no-invariants-or-later\",\n\t\"GFDL-1.2-only\",\n\t\"GFDL-1.2-or-later\",\n\t\"GFDL-1.3-invariants-only\",\n\t\"GFDL-1.3-invariants-or-later\",\n\t\"GFDL-1.3-no-invariants-only\",\n\t\"GFDL-1.3-no-invariants-or-later\",\n\t\"GFDL-1.3-only\",\n\t\"GFDL-1.3-or-later\",\n\t\"GL2PS\",\n\t\"GLWTPL\",\n\t\"GPL-1.0-only\",\n\t\"GPL-1.0-or-later\",\n\t\"GPL-2.0-only\",\n\t\"GPL-2.0-or-later\",\n\t\"GPL-3.0-only\",\n\t\"GPL-3.0-or-later\",\n\t\"Giftware\",\n\t\"Glide\",\n\t\"Glulxe\",\n\t\"Graphics-Gems\",\n\t\"Gutmann\",\n\t\"HIDAPI\",\n\t\"HP-1986\",\n\t\"HP-1989\",\n\t\"HPND\",\n\t\"HPND-DEC\",\n\t\"HPND-Fenneberg-Livingston\",\n\t\"HPND-INRIA-IMAG\",\n\t\"HPND-Intel\",\n\t\"HPND-Kevlin-Henney\",\n\t\"HPND-MIT-disclaimer\",\n\t\"HPND-Markus-Kuhn\",\n\t\"HPND-Netrek\",\n\t\"HPND-Pbmplus\",\n\t\"HPND-UC\",\n\t\"HPND-UC-export-US\",\n\t\"HPND-doc\",\n\t\"HPND-doc-sell\",\n\t\"HPND-export-US\",\n\t\"HPND-export-US-acknowledgement\",\n\t\"HPND-export-US-modify\",\n\t\"HPND-export2-US\",\n\t\"HPND-merchantability-variant\",\n\t\"HPND-sell-MIT-disclaimer-xserver\",\n\t\"HPND-sell-regexpr\",\n\t\"HPND-sell-variant\",\n\t\"HPND-sell-variant-MIT-disclaimer\",\n\t\"HPND-sell-variant-MIT-disclaimer-rev\",\n\t\"HTMLTIDY\",\n\t\"HaskellReport\",\n\t\"Hippocratic-2.1\",\n\t\"IBM-pibs\",\n\t\"ICU\",\n\t\"IEC-Code-Components-EULA\",\n\t\"IJG\",\n\t\"IJG-short\",\n\t\"IPA\",\n\t\"IPL-1.0\",\n\t\"ISC\",\n\t\"ISC-Veillard\",\n\t\"ImageMagick\",\n\t\"Imlib2\",\n\t\"Info-ZIP\",\n\t\"Inner-Net-2.0\",\n\t\"Intel\",\n\t\"Intel-ACPI\",\n\t\"Interbase-1.0\",\n\t\"JPL-image\",\n\t\"JPNIC\",\n\t\"JSON\",\n\t\"Jam\",\n\t\"JasPer-2.0\",\n\t\"Kastrup\",\n\t\"Kazlib\",\n\t\"Knuth-CTAN\",\n\t\"LAL-1.2\",\n\t\"LAL-1.3\",\n\t\"LGPL-2.0-only\",\n\t\"LGPL-2.0-or-later\",\n\t\"LGPL-2.1-only\",\n\t\"LGPL-2.1-or-later\",\n\t\"LGPL-3.0-only\",\n\t\"LGPL-3.0-or-later\",\n\t\"LGPLLR\",\n\t\"LOOP\",\n\t\"LPD-document\",\n\t\"LPL-1.0\",\n\t\"LPL-1.02\",\n\t\"LPPL-1.0\",\n\t\"LPPL-1.1\",\n\t\"LPPL-1.2\",\n\t\"LPPL-1.3a\",\n\t\"LPPL-1.3c\",\n\t\"LZMA-SDK-9.11-to-9.20\",\n\t\"LZMA-SDK-9.22\",\n\t\"Latex2e\",\n\t\"Latex2e-translated-notice\",\n\t\"Leptonica\",\n\t\"LiLiQ-P-1.1\",\n\t\"LiLiQ-R-1.1\",\n\t\"LiLiQ-Rplus-1.1\",\n\t\"Libpng\",\n\t\"Linux-OpenIB\",\n\t\"Linux-man-pages-1-para\",\n\t\"Linux-man-pages-copyleft\",\n\t\"Linux-man-pages-copyleft-2-para\",\n\t\"Linux-man-pages-copyleft-var\",\n\t\"Lucida-Bitmap-Fonts\",\n\t\"MIT\",\n\t\"MIT-0\",\n\t\"MIT-CMU\",\n\t\"MIT-Festival\",\n\t\"MIT-Khronos-old\",\n\t\"MIT-Modern-Variant\",\n\t\"MIT-Wu\",\n\t\"MIT-advertising\",\n\t\"MIT-enna\",\n\t\"MIT-feh\",\n\t\"MIT-open-group\",\n\t\"MIT-testregex\",\n\t\"MITNFA\",\n\t\"MMIXware\",\n\t\"MPEG-SSG\",\n\t\"MPL-1.0\",\n\t\"MPL-1.1\",\n\t\"MPL-2.0\",\n\t\"MPL-2.0-no-copyleft-exception\",\n\t\"MS-LPL\",\n\t\"MS-PL\",\n\t\"MS-RL\",\n\t\"MTLL\",\n\t\"Mackerras-3-Clause\",\n\t\"Mackerras-3-Clause-acknowledgment\",\n\t\"MakeIndex\",\n\t\"Martin-Birgmeier\",\n\t\"McPhee-slideshow\",\n\t\"Minpack\",\n\t\"MirOS\",\n\t\"Motosoto\",\n\t\"MulanPSL-1.0\",\n\t\"MulanPSL-2.0\",\n\t\"Multics\",\n\t\"Mup\",\n\t\"NAIST-2003\",\n\t\"NASA-1.3\",\n\t\"NBPL-1.0\",\n\t\"NCBI-PD\",\n\t\"NCGL-UK-2.0\",\n\t\"NCL\",\n\t\"NCSA\",\n\t\"NGPL\",\n\t\"NICTA-1.0\",\n\t\"NIST-PD\",\n\t\"NIST-PD-fallback\",\n\t\"NIST-Software\",\n\t\"NLOD-1.0\",\n\t\"NLOD-2.0\",\n\t\"NLPL\",\n\t\"NOSL\",\n\t\"NPL-1.0\",\n\t\"NPL-1.1\",\n\t\"NPOSL-3.0\",\n\t\"NRL\",\n\t\"NTP\",\n\t\"NTP-0\",\n\t\"Naumen\",\n\t\"NetCDF\",\n\t\"Newsletr\",\n\t\"Nokia\",\n\t\"Noweb\",\n\t\"O-UDA-1.0\",\n\t\"OAR\",\n\t\"OCCT-PL\",\n\t\"OCLC-2.0\",\n\t\"ODC-By-1.0\",\n\t\"ODbL-1.0\",\n\t\"OFFIS\",\n\t\"OFL-1.0\",\n\t\"OFL-1.0-RFN\",\n\t\"OFL-1.0-no-RFN\",\n\t\"OFL-1.1\",\n\t\"OFL-1.1-RFN\",\n\t\"OFL-1.1-no-RFN\",\n\t\"OGC-1.0\",\n\t\"OGDL-Taiwan-1.0\",\n\t\"OGL-Canada-2.0\",\n\t\"OGL-UK-1.0\",\n\t\"OGL-UK-2.0\",\n\t\"OGL-UK-3.0\",\n\t\"OGTSL\",\n\t\"OLDAP-1.1\",\n\t\"OLDAP-1.2\",\n\t\"OLDAP-1.3\",\n\t\"OLDAP-1.4\",\n\t\"OLDAP-2.0\",\n\t\"OLDAP-2.0.1\",\n\t\"OLDAP-2.1\",\n\t\"OLDAP-2.2\",\n\t\"OLDAP-2.2.1\",\n\t\"OLDAP-2.2.2\",\n\t\"OLDAP-2.3\",\n\t\"OLDAP-2.4\",\n\t\"OLDAP-2.5\",\n\t\"OLDAP-2.6\",\n\t\"OLDAP-2.7\",\n\t\"OLDAP-2.8\",\n\t\"OLFL-1.3\",\n\t\"OML\",\n\t\"OPL-1.0\",\n\t\"OPL-UK-3.0\",\n\t\"OPUBL-1.0\",\n\t\"OSET-PL-2.1\",\n\t\"OSL-1.0\",\n\t\"OSL-1.1\",\n\t\"OSL-2.0\",\n\t\"OSL-2.1\",\n\t\"OSL-3.0\",\n\t\"OpenPBS-2.3\",\n\t\"OpenSSL\",\n\t\"OpenSSL-standalone\",\n\t\"OpenVision\",\n\t\"PADL\",\n\t\"PDDL-1.0\",\n\t\"PHP-3.0\",\n\t\"PHP-3.01\",\n\t\"PPL\",\n\t\"PSF-2.0\",\n\t\"Parity-6.0.0\",\n\t\"Parity-7.0.0\",\n\t\"Pixar\",\n\t\"Plexus\",\n\t\"PolyForm-Noncommercial-1.0.0\",\n\t\"PolyForm-Small-Business-1.0.0\",\n\t\"PostgreSQL\",\n\t\"Python-2.0\",\n\t\"Python-2.0.1\",\n\t\"QPL-1.0\",\n\t\"QPL-1.0-INRIA-2004\",\n\t\"Qhull\",\n\t\"RHeCos-1.1\",\n\t\"RPL-1.1\",\n\t\"RPL-1.5\",\n\t\"RPSL-1.0\",\n\t\"RSA-MD\",\n\t\"RSCPL\",\n\t\"Rdisc\",\n\t\"Ruby\",\n\t\"Ruby-pty\",\n\t\"SAX-PD\",\n\t\"SAX-PD-2.0\",\n\t\"SCEA\",\n\t\"SGI-B-1.0\",\n\t\"SGI-B-1.1\",\n\t\"SGI-B-2.0\",\n\t\"SGI-OpenGL\",\n\t\"SGP4\",\n\t\"SHL-0.5\",\n\t\"SHL-0.51\",\n\t\"SISSL\",\n\t\"SISSL-1.2\",\n\t\"SL\",\n\t\"SMLNJ\",\n\t\"SMPPL\",\n\t\"SNIA\",\n\t\"SPL-1.0\",\n\t\"SSH-OpenSSH\",\n\t\"SSH-short\",\n\t\"SSLeay-standalone\",\n\t\"SSPL-1.0\",\n\t\"SWL\",\n\t\"Saxpath\",\n\t\"SchemeReport\",\n\t\"Sendmail\",\n\t\"Sendmail-8.23\",\n\t\"SimPL-2.0\",\n\t\"Sleepycat\",\n\t\"Soundex\",\n\t\"Spencer-86\",\n\t\"Spencer-94\",\n\t\"Spencer-99\",\n\t\"SugarCRM-1.1.3\",\n\t\"Sun-PPP\",\n\t\"Sun-PPP-2000\",\n\t\"SunPro\",\n\t\"Symlinks\",\n\t\"TAPR-OHL-1.0\",\n\t\"TCL\",\n\t\"TCP-wrappers\",\n\t\"TGPPL-1.0\",\n\t\"TMate\",\n\t\"TORQUE-1.1\",\n\t\"TOSL\",\n\t\"TPDL\",\n\t\"TPL-1.0\",\n\t\"TTWL\",\n\t\"TTYP0\",\n\t\"TU-Berlin-1.0\",\n\t\"TU-Berlin-2.0\",\n\t\"TermReadKey\",\n\t\"UCAR\",\n\t\"UCL-1.0\",\n\t\"UMich-Merit\",\n\t\"UPL-1.0\",\n\t\"URT-RLE\",\n\t\"Ubuntu-font-1.0\",\n\t\"Unicode-3.0\",\n\t\"Unicode-DFS-2015\",\n\t\"Unicode-DFS-2016\",\n\t\"Unicode-TOU\",\n\t\"UnixCrypt\",\n\t\"Unlicense\",\n\t\"VOSTROM\",\n\t\"VSL-1.0\",\n\t\"Vim\",\n\t\"W3C\",\n\t\"W3C-19980720\",\n\t\"W3C-20150513\",\n\t\"WTFPL\",\n\t\"Watcom-1.0\",\n\t\"Widget-Workshop\",\n\t\"Wsuipa\",\n\t\"X11\",\n\t\"X11-distribute-modifications-variant\",\n\t\"X11-swapped\",\n\t\"XFree86-1.1\",\n\t\"XSkat\",\n\t\"Xdebug-1.03\",\n\t\"Xerox\",\n\t\"Xfig\",\n\t\"Xnet\",\n\t\"YPL-1.0\",\n\t\"YPL-1.1\",\n\t\"ZPL-1.1\",\n\t\"ZPL-2.0\",\n\t\"ZPL-2.1\",\n\t\"Zed\",\n\t\"Zeeff\",\n\t\"Zend-2.0\",\n\t\"Zimbra-1.3\",\n\t\"Zimbra-1.4\",\n\t\"Zlib\",\n\t\"any-OSI\",\n\t\"bcrypt-Solar-Designer\",\n\t\"blessing\",\n\t\"bzip2-1.0.6\",\n\t\"check-cvs\",\n\t\"checkmk\",\n\t\"copyleft-next-0.3.0\",\n\t\"copyleft-next-0.3.1\",\n\t\"curl\",\n\t\"cve-tou\",\n\t\"diffmark\",\n\t\"dtoa\",\n\t\"dvipdfm\",\n\t\"eGenix\",\n\t\"etalab-2.0\",\n\t\"fwlw\",\n\t\"gSOAP-1.3b\",\n\t\"gnuplot\",\n\t\"gtkbook\",\n\t\"hdparm\",\n\t\"iMatix\",\n\t\"libpng-2.0\",\n\t\"libselinux-1.0\",\n\t\"libtiff\",\n\t\"libutil-David-Nugent\",\n\t\"lsof\",\n\t\"magaz\",\n\t\"mailprio\",\n\t\"metamail\",\n\t\"mpi-permissive\",\n\t\"mpich2\",\n\t\"mplus\",\n\t\"pkgconf\",\n\t\"pnmstitch\",\n\t\"psfrag\",\n\t\"psutils\",\n\t\"python-ldap\",\n\t\"radvd\",\n\t\"snprintf\",\n\t\"softSurfer\",\n\t\"ssh-keyscan\",\n\t\"swrule\",\n\t\"threeparttable\",\n\t\"ulem\",\n\t\"w3m\",\n\t\"xinetd\",\n\t\"xkeyboard-config-Zinoviev\",\n\t\"xlock\",\n\t\"xpp\",\n\t\"xzoom\",\n\t\"zlib-acknowledgement\"\n]\n", "[\n\t\"AGPL-1.0\",\n\t\"AGPL-3.0\",\n\t\"BSD-2-Clause-FreeBSD\",\n\t\"BSD-2-Clause-NetBSD\",\n\t\"GFDL-1.1\",\n\t\"GFDL-1.2\",\n\t\"GFDL-1.3\",\n\t\"GPL-1.0\",\n\t\"GPL-2.0\",\n\t\"GPL-2.0-with-GCC-exception\",\n\t\"GPL-2.0-with-autoconf-exception\",\n\t\"GPL-2.0-with-bison-exception\",\n\t\"GPL-2.0-with-classpath-exception\",\n\t\"GPL-2.0-with-font-exception\",\n\t\"GPL-3.0\",\n\t\"GPL-3.0-with-GCC-exception\",\n\t\"GPL-3.0-with-autoconf-exception\",\n\t\"LGPL-2.0\",\n\t\"LGPL-2.1\",\n\t\"LGPL-3.0\",\n\t\"Net-SNMP\",\n\t\"Nunit\",\n\t\"StandardML-NJ\",\n\t\"bzip2-1.0.5\",\n\t\"eCos-2.0\",\n\t\"wxWindows\"\n]\n", "[\n  \"389-exception\",\n  \"Asterisk-exception\",\n  \"Autoconf-exception-2.0\",\n  \"Autoconf-exception-3.0\",\n  \"Autoconf-exception-generic\",\n  \"Autoconf-exception-generic-3.0\",\n  \"Autoconf-exception-macro\",\n  \"Bison-exception-1.24\",\n  \"Bison-exception-2.2\",\n  \"Bootloader-exception\",\n  \"Classpath-exception-2.0\",\n  \"CLISP-exception-2.0\",\n  \"cryptsetup-OpenSSL-exception\",\n  \"DigiRule-FOSS-exception\",\n  \"eCos-exception-2.0\",\n  \"Fawkes-Runtime-exception\",\n  \"FLTK-exception\",\n  \"fmt-exception\",\n  \"Font-exception-2.0\",\n  \"freertos-exception-2.0\",\n  \"GCC-exception-2.0\",\n  \"GCC-exception-2.0-note\",\n  \"GCC-exception-3.1\",\n  \"Gmsh-exception\",\n  \"GNAT-exception\",\n  \"GNOME-examples-exception\",\n  \"GNU-compiler-exception\",\n  \"gnu-javamail-exception\",\n  \"GPL-3.0-interface-exception\",\n  \"GPL-3.0-linking-exception\",\n  \"GPL-3.0-linking-source-exception\",\n  \"GPL-CC-1.0\",\n  \"GStreamer-exception-2005\",\n  \"GStreamer-exception-2008\",\n  \"i2p-gpl-java-exception\",\n  \"KiCad-libraries-exception\",\n  \"LGPL-3.0-linking-exception\",\n  \"libpri-OpenH323-exception\",\n  \"Libtool-exception\",\n  \"Linux-syscall-note\",\n  \"LLGPL\",\n  \"LLVM-exception\",\n  \"LZMA-exception\",\n  \"mif-exception\",\n  \"OCaml-LGPL-linking-exception\",\n  \"OCCT-exception-1.0\",\n  \"OpenJDK-assembly-exception-1.0\",\n  \"openvpn-openssl-exception\",\n  \"PS-or-PDF-font-exception-20170817\",\n  \"QPL-1.0-INRIA-2004-exception\",\n  \"Qt-GPL-exception-1.0\",\n  \"Qt-LGPL-exception-1.1\",\n  \"Qwt-exception-1.0\",\n  \"SANE-exception\",\n  \"SHL-2.0\",\n  \"SHL-2.1\",\n  \"stunnel-exception\",\n  \"SWI-exception\",\n  \"Swift-exception\",\n  \"Texinfo-exception\",\n  \"u-boot-exception-2.0\",\n  \"UBDL-exception\",\n  \"Universal-FOSS-exception-1.0\",\n  \"vsftpd-openssl-exception\",\n  \"WxWindows-exception-3.1\",\n  \"x11vnc-openssl-exception\"\n]\n", "'use strict'\n\nvar licenses = []\n  .concat(require('spdx-license-ids'))\n  .concat(require('spdx-license-ids/deprecated'))\nvar exceptions = require('spdx-exceptions')\n\nmodule.exports = function (source) {\n  var index = 0\n\n  function hasMore () {\n    return index < source.length\n  }\n\n  // `value` can be a regexp or a string.\n  // If it is recognized, the matching source string is returned and\n  // the index is incremented. Otherwise `undefined` is returned.\n  function read (value) {\n    if (value instanceof RegExp) {\n      var chars = source.slice(index)\n      var match = chars.match(value)\n      if (match) {\n        index += match[0].length\n        return match[0]\n      }\n    } else {\n      if (source.indexOf(value, index) === index) {\n        index += value.length\n        return value\n      }\n    }\n  }\n\n  function skipWhitespace () {\n    read(/[ ]*/)\n  }\n\n  function operator () {\n    var string\n    var possibilities = ['WITH', 'AND', 'OR', '(', ')', ':', '+']\n    for (var i = 0; i < possibilities.length; i++) {\n      string = read(possibilities[i])\n      if (string) {\n        break\n      }\n    }\n\n    if (string === '+' && index > 1 && source[index - 2] === ' ') {\n      throw new Error('Space before `+`')\n    }\n\n    return string && {\n      type: 'OPERATOR',\n      string: string\n    }\n  }\n\n  function idstring () {\n    return read(/[A-Za-z0-9-.]+/)\n  }\n\n  function expectIdstring () {\n    var string = idstring()\n    if (!string) {\n      throw new Error('Expected idstring at offset ' + index)\n    }\n    return string\n  }\n\n  function documentRef () {\n    if (read('DocumentRef-')) {\n      var string = expectIdstring()\n      return { type: 'DOCUMENTREF', string: string }\n    }\n  }\n\n  function licenseRef () {\n    if (read('LicenseRef-')) {\n      var string = expectIdstring()\n      return { type: 'LICENSEREF', string: string }\n    }\n  }\n\n  function identifier () {\n    var begin = index\n    var string = idstring()\n\n    if (licenses.indexOf(string) !== -1) {\n      return {\n        type: 'LICENSE',\n        string: string\n      }\n    } else if (exceptions.indexOf(string) !== -1) {\n      return {\n        type: 'EXCEPTION',\n        string: string\n      }\n    }\n\n    index = begin\n  }\n\n  // Tries to read the next token. Returns `undefined` if no token is\n  // recognized.\n  function parseToken () {\n    // Ordering matters\n    return (\n      operator() ||\n      documentRef() ||\n      licenseRef() ||\n      identifier()\n    )\n  }\n\n  var tokens = []\n  while (hasMore()) {\n    skipWhitespace()\n    if (!hasMore()) {\n      break\n    }\n\n    var token = parseToken()\n    if (!token) {\n      throw new Error('Unexpected `' + source[index] +\n                      '` at offset ' + index)\n    }\n\n    tokens.push(token)\n  }\n  return tokens\n}\n", "'use strict'\n\n// The ABNF grammar in the spec is totally ambiguous.\n//\n// This parser follows the operator precedence defined in the\n// `Order of Precedence and Parentheses` section.\n\nmodule.exports = function (tokens) {\n  var index = 0\n\n  function hasMore () {\n    return index < tokens.length\n  }\n\n  function token () {\n    return hasMore() ? tokens[index] : null\n  }\n\n  function next () {\n    if (!hasMore()) {\n      throw new Error()\n    }\n    index++\n  }\n\n  function parseOperator (operator) {\n    var t = token()\n    if (t && t.type === 'OPERATOR' && operator === t.string) {\n      next()\n      return t.string\n    }\n  }\n\n  function parseWith () {\n    if (parseOperator('WITH')) {\n      var t = token()\n      if (t && t.type === 'EXCEPTION') {\n        next()\n        return t.string\n      }\n      throw new Error('Expected exception after `WITH`')\n    }\n  }\n\n  function parseLicenseRef () {\n    // TODO: Actually, everything is concatenated into one string\n    // for backward-compatibility but it could be better to return\n    // a nice structure.\n    var begin = index\n    var string = ''\n    var t = token()\n    if (t.type === 'DOCUMENTREF') {\n      next()\n      string += 'DocumentRef-' + t.string + ':'\n      if (!parseOperator(':')) {\n        throw new Error('Expected `:` after `DocumentRef-...`')\n      }\n    }\n    t = token()\n    if (t.type === 'LICENSEREF') {\n      next()\n      string += 'LicenseRef-' + t.string\n      return { license: string }\n    }\n    index = begin\n  }\n\n  function parseLicense () {\n    var t = token()\n    if (t && t.type === 'LICENSE') {\n      next()\n      var node = { license: t.string }\n      if (parseOperator('+')) {\n        node.plus = true\n      }\n      var exception = parseWith()\n      if (exception) {\n        node.exception = exception\n      }\n      return node\n    }\n  }\n\n  function parseParenthesizedExpression () {\n    var left = parseOperator('(')\n    if (!left) {\n      return\n    }\n\n    var expr = parseExpression()\n\n    if (!parseOperator(')')) {\n      throw new Error('Expected `)`')\n    }\n\n    return expr\n  }\n\n  function parseAtom () {\n    return (\n      parseParenthesizedExpression() ||\n      parseLicenseRef() ||\n      parseLicense()\n    )\n  }\n\n  function makeBinaryOpParser (operator, nextParser) {\n    return function parseBinaryOp () {\n      var left = nextParser()\n      if (!left) {\n        return\n      }\n\n      if (!parseOperator(operator)) {\n        return left\n      }\n\n      var right = parseBinaryOp()\n      if (!right) {\n        throw new Error('Expected expression')\n      }\n      return {\n        left: left,\n        conjunction: operator.toLowerCase(),\n        right: right\n      }\n    }\n  }\n\n  var parseAnd = makeBinaryOpParser('AND', parseAtom)\n  var parseExpression = makeBinaryOpParser('OR', parseAnd)\n\n  var node = parseExpression()\n  if (!node || hasMore()) {\n    throw new Error('Syntax error')\n  }\n  return node\n}\n", "'use strict'\n\nvar scan = require('./scan')\nvar parse = require('./parse')\n\nmodule.exports = function (source) {\n  return parse(scan(source))\n}\n", "/*\nCopyright spdx-correct.js contributors\n\nLicensed under the Apache License, Version 2.0 (the \"License\");\nyou may not use this file except in compliance with the License.\nYou may obtain a copy of the License at\n\n   http://www.apache.org/licenses/LICENSE-2.0\n\nUnless required by applicable law or agreed to in writing, software\ndistributed under the License is distributed on an \"AS IS\" BASIS,\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\nSee the License for the specific language governing permissions and\nlimitations under the License.\n*/\nvar parse = require('spdx-expression-parse')\nvar spdxLicenseIds = require('spdx-license-ids')\n\nfunction valid (string) {\n  try {\n    parse(string)\n    return true\n  } catch (error) {\n    return false\n  }\n}\n\n// Sorting function that orders the given array of transpositions such\n// that a transposition with the longer pattern comes before a transposition\n// with a shorter pattern. This is to prevent e.g. the transposition\n// [\"General Public License\", \"GPL\"] from matching to \"Lesser General Public License\"\n// before a longer and more accurate transposition [\"Lesser General Public License\", \"LGPL\"]\n// has a chance to be recognized.\nfunction sortTranspositions(a, b) {\n  var length = b[0].length - a[0].length\n  if (length !== 0) return length\n  return a[0].toUpperCase().localeCompare(b[0].toUpperCase())\n}\n\n// Common transpositions of license identifier acronyms\nvar transpositions = [\n  ['APGL', 'AGPL'],\n  ['Gpl', 'GPL'],\n  ['GLP', 'GPL'],\n  ['APL', 'Apache'],\n  ['ISD', 'ISC'],\n  ['GLP', 'GPL'],\n  ['IST', 'ISC'],\n  ['Claude', 'Clause'],\n  [' or later', '+'],\n  [' International', ''],\n  ['GNU', 'GPL'],\n  ['GUN', 'GPL'],\n  ['+', ''],\n  ['GNU GPL', 'GPL'],\n  ['GNU LGPL', 'LGPL'],\n  ['GNU/GPL', 'GPL'],\n  ['GNU GLP', 'GPL'],\n  ['GNU LESSER GENERAL PUBLIC LICENSE', 'LGPL'],\n  ['GNU Lesser General Public License', 'LGPL'],\n  ['GNU LESSER GENERAL PUBLIC LICENSE', 'LGPL-2.1'],\n  ['GNU Lesser General Public License', 'LGPL-2.1'],\n  ['LESSER GENERAL PUBLIC LICENSE', 'LGPL'],\n  ['Lesser General Public License', 'LGPL'],\n  ['LESSER GENERAL PUBLIC LICENSE', 'LGPL-2.1'],\n  ['Lesser General Public License', 'LGPL-2.1'],\n  ['GNU General Public License', 'GPL'],\n  ['Gnu public license', 'GPL'],\n  ['GNU Public License', 'GPL'],\n  ['GNU GENERAL PUBLIC LICENSE', 'GPL'],\n  ['MTI', 'MIT'],\n  ['Mozilla Public License', 'MPL'],\n  ['Universal Permissive License', 'UPL'],\n  ['WTH', 'WTF'],\n  ['WTFGPL', 'WTFPL'],\n  ['-License', '']\n].sort(sortTranspositions)\n\nvar TRANSPOSED = 0\nvar CORRECT = 1\n\n// Simple corrections to nearly valid identifiers.\nvar transforms = [\n  // e.g. 'mit'\n  function (argument) {\n    return argument.toUpperCase()\n  },\n  // e.g. 'MIT '\n  function (argument) {\n    return argument.trim()\n  },\n  // e.g. 'M.I.T.'\n  function (argument) {\n    return argument.replace(/\\./g, '')\n  },\n  // e.g. 'Apache- 2.0'\n  function (argument) {\n    return argument.replace(/\\s+/g, '')\n  },\n  // e.g. 'CC BY 4.0''\n  function (argument) {\n    return argument.replace(/\\s+/g, '-')\n  },\n  // e.g. 'LGPLv2.1'\n  function (argument) {\n    return argument.replace('v', '-')\n  },\n  // e.g. 'Apache 2.0'\n  function (argument) {\n    return argument.replace(/,?\\s*(\\d)/, '-$1')\n  },\n  // e.g. 'GPL 2'\n  function (argument) {\n    return argument.replace(/,?\\s*(\\d)/, '-$1.0')\n  },\n  // e.g. 'Apache Version 2.0'\n  function (argument) {\n    return argument\n      .replace(/,?\\s*(V\\.|v\\.|V|v|Version|version)\\s*(\\d)/, '-$2')\n  },\n  // e.g. 'Apache Version 2'\n  function (argument) {\n    return argument\n      .replace(/,?\\s*(V\\.|v\\.|V|v|Version|version)\\s*(\\d)/, '-$2.0')\n  },\n  // e.g. 'ZLIB'\n  function (argument) {\n    return argument[0].toUpperCase() + argument.slice(1)\n  },\n  // e.g. 'MPL/2.0'\n  function (argument) {\n    return argument.replace('/', '-')\n  },\n  // e.g. 'Apache 2'\n  function (argument) {\n    return argument\n      .replace(/\\s*V\\s*(\\d)/, '-$1')\n      .replace(/(\\d)$/, '$1.0')\n  },\n  // e.g. 'GPL-2.0', 'GPL-3.0'\n  function (argument) {\n    if (argument.indexOf('3.0') !== -1) {\n      return argument + '-or-later'\n    } else {\n      return argument + '-only'\n    }\n  },\n  // e.g. 'GPL-2.0-'\n  function (argument) {\n    return argument + 'only'\n  },\n  // e.g. 'GPL2'\n  function (argument) {\n    return argument.replace(/(\\d)$/, '-$1.0')\n  },\n  // e.g. 'BSD 3'\n  function (argument) {\n    return argument.replace(/(-| )?(\\d)$/, '-$2-Clause')\n  },\n  // e.g. 'BSD clause 3'\n  function (argument) {\n    return argument.replace(/(-| )clause(-| )(\\d)/, '-$3-Clause')\n  },\n  // e.g. 'New BSD license'\n  function (argument) {\n    return argument.replace(/\\b(Modified|New|Revised)(-| )?BSD((-| )License)?/i, 'BSD-3-Clause')\n  },\n  // e.g. 'Simplified BSD license'\n  function (argument) {\n    return argument.replace(/\\bSimplified(-| )?BSD((-| )License)?/i, 'BSD-2-Clause')\n  },\n  // e.g. 'Free BSD license'\n  function (argument) {\n    return argument.replace(/\\b(Free|Net)(-| )?BSD((-| )License)?/i, 'BSD-2-Clause-$1BSD')\n  },\n  // e.g. 'Clear BSD license'\n  function (argument) {\n    return argument.replace(/\\bClear(-| )?BSD((-| )License)?/i, 'BSD-3-Clause-Clear')\n  },\n  // e.g. 'Old BSD License'\n  function (argument) {\n    return argument.replace(/\\b(Old|Original)(-| )?BSD((-| )License)?/i, 'BSD-4-Clause')\n  },\n  // e.g. 'BY-NC-4.0'\n  function (argument) {\n    return 'CC-' + argument\n  },\n  // e.g. 'BY-NC'\n  function (argument) {\n    return 'CC-' + argument + '-4.0'\n  },\n  // e.g. 'Attribution-NonCommercial'\n  function (argument) {\n    return argument\n      .replace('Attribution', 'BY')\n      .replace('NonCommercial', 'NC')\n      .replace('NoDerivatives', 'ND')\n      .replace(/ (\\d)/, '-$1')\n      .replace(/ ?International/, '')\n  },\n  // e.g. 'Attribution-NonCommercial'\n  function (argument) {\n    return 'CC-' +\n      argument\n        .replace('Attribution', 'BY')\n        .replace('NonCommercial', 'NC')\n        .replace('NoDerivatives', 'ND')\n        .replace(/ (\\d)/, '-$1')\n        .replace(/ ?International/, '') +\n      '-4.0'\n  }\n]\n\nvar licensesWithVersions = spdxLicenseIds\n  .map(function (id) {\n    var match = /^(.*)-\\d+\\.\\d+$/.exec(id)\n    return match\n      ? [match[0], match[1]]\n      : [id, null]\n  })\n  .reduce(function (objectMap, item) {\n    var key = item[1]\n    objectMap[key] = objectMap[key] || []\n    objectMap[key].push(item[0])\n    return objectMap\n  }, {})\n\nvar licensesWithOneVersion = Object.keys(licensesWithVersions)\n  .map(function makeEntries (key) {\n    return [key, licensesWithVersions[key]]\n  })\n  .filter(function identifySoleVersions (item) {\n    return (\n      // Licenses has just one valid version suffix.\n      item[1].length === 1 &&\n      item[0] !== null &&\n      // APL will be considered Apache, rather than APL-1.0\n      item[0] !== 'APL'\n    )\n  })\n  .map(function createLastResorts (item) {\n    return [item[0], item[1][0]]\n  })\n\nlicensesWithVersions = undefined\n\n// If all else fails, guess that strings containing certain substrings\n// meant to identify certain licenses.\nvar lastResorts = [\n  ['UNLI', 'Unlicense'],\n  ['WTF', 'WTFPL'],\n  ['2 CLAUSE', 'BSD-2-Clause'],\n  ['2-CLAUSE', 'BSD-2-Clause'],\n  ['3 CLAUSE', 'BSD-3-Clause'],\n  ['3-CLAUSE', 'BSD-3-Clause'],\n  ['AFFERO', 'AGPL-3.0-or-later'],\n  ['AGPL', 'AGPL-3.0-or-later'],\n  ['APACHE', 'Apache-2.0'],\n  ['ARTISTIC', 'Artistic-2.0'],\n  ['Affero', 'AGPL-3.0-or-later'],\n  ['BEER', 'Beerware'],\n  ['BOOST', 'BSL-1.0'],\n  ['BSD', 'BSD-2-Clause'],\n  ['CDDL', 'CDDL-1.1'],\n  ['ECLIPSE', 'EPL-1.0'],\n  ['FUCK', 'WTFPL'],\n  ['GNU', 'GPL-3.0-or-later'],\n  ['LGPL', 'LGPL-3.0-or-later'],\n  ['GPLV1', 'GPL-1.0-only'],\n  ['GPL-1', 'GPL-1.0-only'],\n  ['GPLV2', 'GPL-2.0-only'],\n  ['GPL-2', 'GPL-2.0-only'],\n  ['GPL', 'GPL-3.0-or-later'],\n  ['MIT +NO-FALSE-ATTRIBS', 'MITNFA'],\n  ['MIT', 'MIT'],\n  ['MPL', 'MPL-2.0'],\n  ['X11', 'X11'],\n  ['ZLIB', 'Zlib']\n].concat(licensesWithOneVersion).sort(sortTranspositions)\n\nvar SUBSTRING = 0\nvar IDENTIFIER = 1\n\nvar validTransformation = function (identifier) {\n  for (var i = 0; i < transforms.length; i++) {\n    var transformed = transforms[i](identifier).trim()\n    if (transformed !== identifier && valid(transformed)) {\n      return transformed\n    }\n  }\n  return null\n}\n\nvar validLastResort = function (identifier) {\n  var upperCased = identifier.toUpperCase()\n  for (var i = 0; i < lastResorts.length; i++) {\n    var lastResort = lastResorts[i]\n    if (upperCased.indexOf(lastResort[SUBSTRING]) > -1) {\n      return lastResort[IDENTIFIER]\n    }\n  }\n  return null\n}\n\nvar anyCorrection = function (identifier, check) {\n  for (var i = 0; i < transpositions.length; i++) {\n    var transposition = transpositions[i]\n    var transposed = transposition[TRANSPOSED]\n    if (identifier.indexOf(transposed) > -1) {\n      var corrected = identifier.replace(\n        transposed,\n        transposition[CORRECT]\n      )\n      var checked = check(corrected)\n      if (checked !== null) {\n        return checked\n      }\n    }\n  }\n  return null\n}\n\nmodule.exports = function (identifier, options) {\n  options = options || {}\n  var upgrade = options.upgrade === undefined ? true : !!options.upgrade\n  function postprocess (value) {\n    return upgrade ? upgradeGPLs(value) : value\n  }\n  var validArugment = (\n    typeof identifier === 'string' &&\n    identifier.trim().length !== 0\n  )\n  if (!validArugment) {\n    throw Error('Invalid argument. Expected non-empty string.')\n  }\n  identifier = identifier.trim()\n  if (valid(identifier)) {\n    return postprocess(identifier)\n  }\n  var noPlus = identifier.replace(/\\+$/, '').trim()\n  if (valid(noPlus)) {\n    return postprocess(noPlus)\n  }\n  var transformed = validTransformation(identifier)\n  if (transformed !== null) {\n    return postprocess(transformed)\n  }\n  transformed = anyCorrection(identifier, function (argument) {\n    if (valid(argument)) {\n      return argument\n    }\n    return validTransformation(argument)\n  })\n  if (transformed !== null) {\n    return postprocess(transformed)\n  }\n  transformed = validLastResort(identifier)\n  if (transformed !== null) {\n    return postprocess(transformed)\n  }\n  transformed = anyCorrection(identifier, validLastResort)\n  if (transformed !== null) {\n    return postprocess(transformed)\n  }\n  return null\n}\n\nfunction upgradeGPLs (value) {\n  if ([\n    'GPL-1.0', 'LGPL-1.0', 'AGPL-1.0',\n    'GPL-2.0', 'LGPL-2.0', 'AGPL-2.0',\n    'LGPL-2.1'\n  ].indexOf(value) !== -1) {\n    return value + '-only'\n  } else if ([\n    'GPL-1.0+', 'GPL-2.0+', 'GPL-3.0+',\n    'LGPL-2.0+', 'LGPL-2.1+', 'LGPL-3.0+',\n    'AGPL-1.0+', 'AGPL-3.0+'\n  ].indexOf(value) !== -1) {\n    return value.replace(/\\+$/, '-or-later')\n  } else if (['GPL-3.0', 'LGPL-3.0', 'AGPL-3.0'].indexOf(value) !== -1) {\n    return value + '-or-later'\n  } else {\n    return value\n  }\n}\n", "var parse = require('spdx-expression-parse');\nvar correct = require('spdx-correct');\n\nvar genericWarning = (\n  'license should be ' +\n  'a valid SPDX license expression (without \"LicenseRef\"), ' +\n  '\"UNLICENSED\", or ' +\n  '\"SEE LICENSE IN <filename>\"'\n);\n\nvar fileReferenceRE = /^SEE LICEN[CS]E IN (.+)$/;\n\nfunction startsWith(prefix, string) {\n  return string.slice(0, prefix.length) === prefix;\n}\n\nfunction usesLicenseRef(ast) {\n  if (ast.hasOwnProperty('license')) {\n    var license = ast.license;\n    return (\n      startsWith('LicenseRef', license) ||\n      startsWith('DocumentRef', license)\n    );\n  } else {\n    return (\n      usesLicenseRef(ast.left) ||\n      usesLicenseRef(ast.right)\n    );\n  }\n}\n\nmodule.exports = function(argument) {\n  var ast;\n\n  try {\n    ast = parse(argument);\n  } catch (e) {\n    var match\n    if (\n      argument === 'UNLICENSED' ||\n      argument === 'UNLICENCED'\n    ) {\n      return {\n        validForOldPackages: true,\n        validForNewPackages: true,\n        unlicensed: true\n      };\n    } else if (match = fileReferenceRE.exec(argument)) {\n      return {\n        validForOldPackages: true,\n        validForNewPackages: true,\n        inFile: match[1]\n      };\n    } else {\n      var result = {\n        validForOldPackages: false,\n        validForNewPackages: false,\n        warnings: [genericWarning]\n      };\n      if (argument.trim().length !== 0) {\n        var corrected = correct(argument);\n        if (corrected) {\n          result.warnings.push(\n            'license is similar to the valid expression \"' + corrected + '\"'\n          );\n        }\n      }\n      return result;\n    }\n  }\n\n  if (usesLicenseRef(ast)) {\n    return {\n      validForNewPackages: false,\n      validForOldPackages: false,\n      spdx: true,\n      warnings: [genericWarning]\n    };\n  } else {\n    return {\n      validForNewPackages: true,\n      validForOldPackages: true,\n      spdx: true\n    };\n  }\n};\n", "'use strict'\n\nvar gitHosts = module.exports = {\n  github: {\n    // First two are insecure and generally shouldn't be used any more, but\n    // they are still supported.\n    'protocols': [ 'git', 'http', 'git+ssh', 'git+https', 'ssh', 'https' ],\n    'domain': 'github.com',\n    'treepath': 'tree',\n    'filetemplate': 'https://{auth@}raw.githubusercontent.com/{user}/{project}/{committish}/{path}',\n    'bugstemplate': 'https://{domain}/{user}/{project}/issues',\n    'gittemplate': 'git://{auth@}{domain}/{user}/{project}.git{#committish}',\n    'tarballtemplate': 'https://codeload.{domain}/{user}/{project}/tar.gz/{committish}'\n  },\n  bitbucket: {\n    'protocols': [ 'git+ssh', 'git+https', 'ssh', 'https' ],\n    'domain': 'bitbucket.org',\n    'treepath': 'src',\n    'tarballtemplate': 'https://{domain}/{user}/{project}/get/{committish}.tar.gz'\n  },\n  gitlab: {\n    'protocols': [ 'git+ssh', 'git+https', 'ssh', 'https' ],\n    'domain': 'gitlab.com',\n    'treepath': 'tree',\n    'bugstemplate': 'https://{domain}/{user}/{project}/issues',\n    'httpstemplate': 'git+https://{auth@}{domain}/{user}/{projectPath}.git{#committish}',\n    'tarballtemplate': 'https://{domain}/{user}/{project}/repository/archive.tar.gz?ref={committish}',\n    'pathmatch': /^[/]([^/]+)[/]((?!.*(\\/-\\/|\\/repository\\/archive\\.tar\\.gz\\?=.*|\\/repository\\/[^/]+\\/archive.tar.gz$)).*?)(?:[.]git|[/])?$/\n  },\n  gist: {\n    'protocols': [ 'git', 'git+ssh', 'git+https', 'ssh', 'https' ],\n    'domain': 'gist.github.com',\n    'pathmatch': /^[/](?:([^/]+)[/])?([a-z0-9]{32,})(?:[.]git)?$/,\n    'filetemplate': 'https://gist.githubusercontent.com/{user}/{project}/raw{/committish}/{path}',\n    'bugstemplate': 'https://{domain}/{project}',\n    'gittemplate': 'git://{domain}/{project}.git{#committish}',\n    'sshtemplate': 'git@{domain}:/{project}.git{#committish}',\n    'sshurltemplate': 'git+ssh://git@{domain}/{project}.git{#committish}',\n    'browsetemplate': 'https://{domain}/{project}{/committish}',\n    'browsefiletemplate': 'https://{domain}/{project}{/committish}{#path}',\n    'docstemplate': 'https://{domain}/{project}{/committish}',\n    'httpstemplate': 'git+https://{domain}/{project}.git{#committish}',\n    'shortcuttemplate': '{type}:{project}{#committish}',\n    'pathtemplate': '{project}{#committish}',\n    'tarballtemplate': 'https://codeload.github.com/gist/{project}/tar.gz/{committish}',\n    'hashformat': function (fragment) {\n      return 'file-' + formatHashFragment(fragment)\n    }\n  }\n}\n\nvar gitHostDefaults = {\n  'sshtemplate': 'git@{domain}:{user}/{project}.git{#committish}',\n  'sshurltemplate': 'git+ssh://git@{domain}/{user}/{project}.git{#committish}',\n  'browsetemplate': 'https://{domain}/{user}/{project}{/tree/committish}',\n  'browsefiletemplate': 'https://{domain}/{user}/{project}/{treepath}/{committish}/{path}{#fragment}',\n  'docstemplate': 'https://{domain}/{user}/{project}{/tree/committish}#readme',\n  'httpstemplate': 'git+https://{auth@}{domain}/{user}/{project}.git{#committish}',\n  'filetemplate': 'https://{domain}/{user}/{project}/raw/{committish}/{path}',\n  'shortcuttemplate': '{type}:{user}/{project}{#committish}',\n  'pathtemplate': '{user}/{project}{#committish}',\n  'pathmatch': /^[/]([^/]+)[/]([^/]+?)(?:[.]git|[/])?$/,\n  'hashformat': formatHashFragment\n}\n\nObject.keys(gitHosts).forEach(function (name) {\n  Object.keys(gitHostDefaults).forEach(function (key) {\n    if (gitHosts[name][key]) return\n    gitHosts[name][key] = gitHostDefaults[key]\n  })\n  gitHosts[name].protocols_re = RegExp('^(' +\n    gitHosts[name].protocols.map(function (protocol) {\n      return protocol.replace(/([\\\\+*{}()[\\]$^|])/g, '\\\\$1')\n    }).join('|') + '):$')\n})\n\nfunction formatHashFragment (fragment) {\n  return fragment.toLowerCase().replace(/^\\W+|\\/|\\W+$/g, '').replace(/\\W+/g, '-')\n}\n", "'use strict'\nvar gitHosts = require('./git-host-info.js')\n/* eslint-disable node/no-deprecated-api */\n\n// copy-pasta util._extend from node's source, to avoid pulling\n// the whole util module into peoples' webpack bundles.\n/* istanbul ignore next */\nvar extend = Object.assign || function _extend (target, source) {\n  // Don't do anything if source isn't an object\n  if (source === null || typeof source !== 'object') return target\n\n  var keys = Object.keys(source)\n  var i = keys.length\n  while (i--) {\n    target[keys[i]] = source[keys[i]]\n  }\n  return target\n}\n\nmodule.exports = GitHost\nfunction GitHost (type, user, auth, project, committish, defaultRepresentation, opts) {\n  var gitHostInfo = this\n  gitHostInfo.type = type\n  Object.keys(gitHosts[type]).forEach(function (key) {\n    gitHostInfo[key] = gitHosts[type][key]\n  })\n  gitHostInfo.user = user\n  gitHostInfo.auth = auth\n  gitHostInfo.project = project\n  gitHostInfo.committish = committish\n  gitHostInfo.default = defaultRepresentation\n  gitHostInfo.opts = opts || {}\n}\n\nGitHost.prototype.hash = function () {\n  return this.committish ? '#' + this.committish : ''\n}\n\nGitHost.prototype._fill = function (template, opts) {\n  if (!template) return\n  var vars = extend({}, opts)\n  vars.path = vars.path ? vars.path.replace(/^[/]+/g, '') : ''\n  opts = extend(extend({}, this.opts), opts)\n  var self = this\n  Object.keys(this).forEach(function (key) {\n    if (self[key] != null && vars[key] == null) vars[key] = self[key]\n  })\n  var rawAuth = vars.auth\n  var rawcommittish = vars.committish\n  var rawFragment = vars.fragment\n  var rawPath = vars.path\n  var rawProject = vars.project\n  Object.keys(vars).forEach(function (key) {\n    var value = vars[key]\n    if ((key === 'path' || key === 'project') && typeof value === 'string') {\n      vars[key] = value.split('/').map(function (pathComponent) {\n        return encodeURIComponent(pathComponent)\n      }).join('/')\n    } else {\n      vars[key] = encodeURIComponent(value)\n    }\n  })\n  vars['auth@'] = rawAuth ? rawAuth + '@' : ''\n  vars['#fragment'] = rawFragment ? '#' + this.hashformat(rawFragment) : ''\n  vars.fragment = vars.fragment ? vars.fragment : ''\n  vars['#path'] = rawPath ? '#' + this.hashformat(rawPath) : ''\n  vars['/path'] = vars.path ? '/' + vars.path : ''\n  vars.projectPath = rawProject.split('/').map(encodeURIComponent).join('/')\n  if (opts.noCommittish) {\n    vars['#committish'] = ''\n    vars['/tree/committish'] = ''\n    vars['/committish'] = ''\n    vars.committish = ''\n  } else {\n    vars['#committish'] = rawcommittish ? '#' + rawcommittish : ''\n    vars['/tree/committish'] = vars.committish\n      ? '/' + vars.treepath + '/' + vars.committish\n      : ''\n    vars['/committish'] = vars.committish ? '/' + vars.committish : ''\n    vars.committish = vars.committish || 'master'\n  }\n  var res = template\n  Object.keys(vars).forEach(function (key) {\n    res = res.replace(new RegExp('[{]' + key + '[}]', 'g'), vars[key])\n  })\n  if (opts.noGitPlus) {\n    return res.replace(/^git[+]/, '')\n  } else {\n    return res\n  }\n}\n\nGitHost.prototype.ssh = function (opts) {\n  return this._fill(this.sshtemplate, opts)\n}\n\nGitHost.prototype.sshurl = function (opts) {\n  return this._fill(this.sshurltemplate, opts)\n}\n\nGitHost.prototype.browse = function (P, F, opts) {\n  if (typeof P === 'string') {\n    if (typeof F !== 'string') {\n      opts = F\n      F = null\n    }\n    return this._fill(this.browsefiletemplate, extend({\n      fragment: F,\n      path: P\n    }, opts))\n  } else {\n    return this._fill(this.browsetemplate, P)\n  }\n}\n\nGitHost.prototype.docs = function (opts) {\n  return this._fill(this.docstemplate, opts)\n}\n\nGitHost.prototype.bugs = function (opts) {\n  return this._fill(this.bugstemplate, opts)\n}\n\nGitHost.prototype.https = function (opts) {\n  return this._fill(this.httpstemplate, opts)\n}\n\nGitHost.prototype.git = function (opts) {\n  return this._fill(this.gittemplate, opts)\n}\n\nGitHost.prototype.shortcut = function (opts) {\n  return this._fill(this.shortcuttemplate, opts)\n}\n\nGitHost.prototype.path = function (opts) {\n  return this._fill(this.pathtemplate, opts)\n}\n\nGitHost.prototype.tarball = function (opts_) {\n  var opts = extend({}, opts_, { noCommittish: false })\n  return this._fill(this.tarballtemplate, opts)\n}\n\nGitHost.prototype.file = function (P, opts) {\n  return this._fill(this.filetemplate, extend({ path: P }, opts))\n}\n\nGitHost.prototype.getDefaultRepresentation = function () {\n  return this.default\n}\n\nGitHost.prototype.toString = function (opts) {\n  if (this.default && typeof this[this.default] === 'function') return this[this.default](opts)\n  return this.sshurl(opts)\n}\n", "'use strict'\nvar url = require('url')\nvar gitHosts = require('./git-host-info.js')\nvar GitHost = module.exports = require('./git-host.js')\n\nvar protocolToRepresentationMap = {\n  'git+ssh:': 'sshurl',\n  'git+https:': 'https',\n  'ssh:': 'sshurl',\n  'git:': 'git'\n}\n\nfunction protocolToRepresentation (protocol) {\n  return protocolToRepresentationMap[protocol] || protocol.slice(0, -1)\n}\n\nvar authProtocols = {\n  'git:': true,\n  'https:': true,\n  'git+https:': true,\n  'http:': true,\n  'git+http:': true\n}\n\nvar cache = {}\n\nmodule.exports.fromUrl = function (giturl, opts) {\n  if (typeof giturl !== 'string') return\n  var key = giturl + JSON.stringify(opts || {})\n\n  if (!(key in cache)) {\n    cache[key] = fromUrl(giturl, opts)\n  }\n\n  return cache[key]\n}\n\nfunction fromUrl (giturl, opts) {\n  if (giturl == null || giturl === '') return\n  var url = fixupUnqualifiedGist(\n    isGitHubShorthand(giturl) ? 'github:' + giturl : giturl\n  )\n  var parsed = parseGitUrl(url)\n  var shortcutMatch = url.match(/^([^:]+):(?:[^@]+@)?(?:([^/]*)\\/)?([^#]+)/)\n  var matches = Object.keys(gitHosts).map(function (gitHostName) {\n    try {\n      var gitHostInfo = gitHosts[gitHostName]\n      var auth = null\n      if (parsed.auth && authProtocols[parsed.protocol]) {\n        auth = parsed.auth\n      }\n      var committish = parsed.hash ? decodeURIComponent(parsed.hash.substr(1)) : null\n      var user = null\n      var project = null\n      var defaultRepresentation = null\n      if (shortcutMatch && shortcutMatch[1] === gitHostName) {\n        user = shortcutMatch[2] && decodeURIComponent(shortcutMatch[2])\n        project = decodeURIComponent(shortcutMatch[3].replace(/\\.git$/, ''))\n        defaultRepresentation = 'shortcut'\n      } else {\n        if (parsed.host && parsed.host !== gitHostInfo.domain && parsed.host.replace(/^www[.]/, '') !== gitHostInfo.domain) return\n        if (!gitHostInfo.protocols_re.test(parsed.protocol)) return\n        if (!parsed.path) return\n        var pathmatch = gitHostInfo.pathmatch\n        var matched = parsed.path.match(pathmatch)\n        if (!matched) return\n        /* istanbul ignore else */\n        if (matched[1] !== null && matched[1] !== undefined) {\n          user = decodeURIComponent(matched[1].replace(/^:/, ''))\n        }\n        project = decodeURIComponent(matched[2])\n        defaultRepresentation = protocolToRepresentation(parsed.protocol)\n      }\n      return new GitHost(gitHostName, user, auth, project, committish, defaultRepresentation, opts)\n    } catch (ex) {\n      /* istanbul ignore else */\n      if (ex instanceof URIError) {\n      } else throw ex\n    }\n  }).filter(function (gitHostInfo) { return gitHostInfo })\n  if (matches.length !== 1) return\n  return matches[0]\n}\n\nfunction isGitHubShorthand (arg) {\n  // Note: This does not fully test the git ref format.\n  // See https://www.kernel.org/pub/software/scm/git/docs/git-check-ref-format.html\n  //\n  // The only way to do this properly would be to shell out to\n  // git-check-ref-format, and as this is a fast sync function,\n  // we don't want to do that.  Just let git fail if it turns\n  // out that the commit-ish is invalid.\n  // GH usernames cannot start with . or -\n  return /^[^:@%/\\s.-][^:@%/\\s]*[/][^:@\\s/%]+(?:#.*)?$/.test(arg)\n}\n\nfunction fixupUnqualifiedGist (giturl) {\n  // necessary for round-tripping gists\n  var parsed = url.parse(giturl)\n  if (parsed.protocol === 'gist:' && parsed.host && !parsed.path) {\n    return parsed.protocol + '/' + parsed.host\n  } else {\n    return giturl\n  }\n}\n\nfunction parseGitUrl (giturl) {\n  var matched = giturl.match(/^([^@]+)@([^:/]+):[/]?((?:[^/]+[/])?[^/]+?)(?:[.]git)?(#.*)?$/)\n  if (!matched) {\n    var legacy = url.parse(giturl)\n    // If we don't have url.URL, then sorry, this is just not fixable.\n    // This affects Node <= 6.12.\n    if (legacy.auth && typeof url.URL === 'function') {\n      // git urls can be in the form of scp-style/ssh-connect strings, like\n      // git+ssh://<EMAIL>:some/path, which the legacy url parser\n      // supports, but WhatWG url.URL class does not.  However, the legacy\n      // parser de-urlencodes the username and password, so something like\n      // https://user%3An%40me:<EMAIL>/ becomes\n      // https://user:n@me:p@ss:<EMAIL>/ which is all kinds of wrong.\n      // Pull off just the auth and host, so we dont' get the confusing\n      // scp-style URL, then pass that to the WhatWG parser to get the\n      // auth properly escaped.\n      var authmatch = giturl.match(/[^@]+@[^:/]+/)\n      /* istanbul ignore else - this should be impossible */\n      if (authmatch) {\n        var whatwg = new url.URL(authmatch[0])\n        legacy.auth = whatwg.username || ''\n        if (whatwg.password) legacy.auth += ':' + whatwg.password\n      }\n    }\n    return legacy\n  }\n  return {\n    protocol: 'git+ssh:',\n    slashes: true,\n    auth: matched[1],\n    host: matched[2],\n    port: null,\n    hostname: matched[2],\n    hash: matched[4],\n    search: null,\n    query: null,\n    pathname: '/' + matched[3],\n    path: '/' + matched[3],\n    href: 'git+ssh://' + matched[1] + '@' + matched[2] +\n          '/' + matched[3] + (matched[4] || '')\n  }\n}\n", "'use strict';\n\nvar os = require('os');\n\n// adapted from https://github.com/sindresorhus/os-homedir/blob/11e089f4754db38bb535e5a8416320c4446e8cfd/index.js\n\nmodule.exports = os.homedir || function homedir() {\n    var home = process.env.HOME;\n    var user = process.env.LOGNAME || process.env.USER || process.env.LNAME || process.env.USERNAME;\n\n    if (process.platform === 'win32') {\n        return process.env.USERPROFILE || process.env.HOMEDRIVE + process.env.HOMEPATH || home || null;\n    }\n\n    if (process.platform === 'darwin') {\n        return home || (user ? '/Users/' + user : null);\n    }\n\n    if (process.platform === 'linux') {\n        return home || (process.getuid() === 0 ? '/root' : (user ? '/home/' + user : null)); // eslint-disable-line no-extra-parens\n    }\n\n    return home || null;\n};\n", "module.exports = function () {\n    // see https://code.google.com/p/v8/wiki/JavaScriptStackTraceApi\n    var origPrepareStackTrace = Error.prepareStackTrace;\n    Error.prepareStackTrace = function (_, stack) { return stack; };\n    var stack = (new Error()).stack;\n    Error.prepareStackTrace = origPrepareStackTrace;\n    return stack[2].getFileName();\n};\n", "'use strict';\n\nvar isWindows = process.platform === 'win32';\n\n// Regex to split a windows path into into [dir, root, basename, name, ext]\nvar splitWindowsRe =\n    /^(((?:[a-zA-Z]:|[\\\\\\/]{2}[^\\\\\\/]+[\\\\\\/]+[^\\\\\\/]+)?[\\\\\\/]?)(?:[^\\\\\\/]*[\\\\\\/])*)((\\.{1,2}|[^\\\\\\/]+?|)(\\.[^.\\/\\\\]*|))[\\\\\\/]*$/;\n\nvar win32 = {};\n\nfunction win32SplitPath(filename) {\n  return splitWindowsRe.exec(filename).slice(1);\n}\n\nwin32.parse = function(pathString) {\n  if (typeof pathString !== 'string') {\n    throw new TypeError(\n        \"Parameter 'pathString' must be a string, not \" + typeof pathString\n    );\n  }\n  var allParts = win32SplitPath(pathString);\n  if (!allParts || allParts.length !== 5) {\n    throw new TypeError(\"Invalid path '\" + pathString + \"'\");\n  }\n  return {\n    root: allParts[1],\n    dir: allParts[0] === allParts[1] ? allParts[0] : allParts[0].slice(0, -1),\n    base: allParts[2],\n    ext: allParts[4],\n    name: allParts[3]\n  };\n};\n\n\n\n// Split a filename into [dir, root, basename, name, ext], unix version\n// 'root' is just a slash, or nothing.\nvar splitPathRe =\n    /^((\\/?)(?:[^\\/]*\\/)*)((\\.{1,2}|[^\\/]+?|)(\\.[^.\\/]*|))[\\/]*$/;\nvar posix = {};\n\n\nfunction posixSplitPath(filename) {\n  return splitPathRe.exec(filename).slice(1);\n}\n\n\nposix.parse = function(pathString) {\n  if (typeof pathString !== 'string') {\n    throw new TypeError(\n        \"Parameter 'pathString' must be a string, not \" + typeof pathString\n    );\n  }\n  var allParts = posixSplitPath(pathString);\n  if (!allParts || allParts.length !== 5) {\n    throw new TypeError(\"Invalid path '\" + pathString + \"'\");\n  }\n  \n  return {\n    root: allParts[1],\n    dir: allParts[0].slice(0, -1),\n    base: allParts[2],\n    ext: allParts[4],\n    name: allParts[3],\n  };\n};\n\n\nif (isWindows)\n  module.exports = win32.parse;\nelse /* posix */\n  module.exports = posix.parse;\n\nmodule.exports.posix = posix.parse;\nmodule.exports.win32 = win32.parse;\n", "var path = require('path');\nvar parse = path.parse || require('path-parse'); // eslint-disable-line global-require\n\nvar getNodeModulesDirs = function getNodeModulesDirs(absoluteStart, modules) {\n    var prefix = '/';\n    if ((/^([A-Za-z]:)/).test(absoluteStart)) {\n        prefix = '';\n    } else if ((/^\\\\\\\\/).test(absoluteStart)) {\n        prefix = '\\\\\\\\';\n    }\n\n    var paths = [absoluteStart];\n    var parsed = parse(absoluteStart);\n    while (parsed.dir !== paths[paths.length - 1]) {\n        paths.push(parsed.dir);\n        parsed = parse(parsed.dir);\n    }\n\n    return paths.reduce(function (dirs, aPath) {\n        return dirs.concat(modules.map(function (moduleDir) {\n            return path.resolve(prefix, aPath, moduleDir);\n        }));\n    }, []);\n};\n\nmodule.exports = function nodeModulesPaths(start, opts, request) {\n    var modules = opts && opts.moduleDirectory\n        ? [].concat(opts.moduleDirectory)\n        : ['node_modules'];\n\n    if (opts && typeof opts.paths === 'function') {\n        return opts.paths(\n            request,\n            start,\n            function () { return getNodeModulesDirs(start, modules); },\n            opts\n        );\n    }\n\n    var dirs = getNodeModulesDirs(start, modules);\n    return opts && opts.paths ? dirs.concat(opts.paths) : dirs;\n};\n", "module.exports = function (x, opts) {\n    /**\n     * This file is purposefully a passthrough. It's expected that third-party\n     * environments will override it at runtime in order to inject special logic\n     * into `resolve` (by manipulating the options). One such example is the PnP\n     * code path in Yarn.\n     */\n\n    return opts || {};\n};\n", "'use strict';\n\n/* eslint no-invalid-this: 1 */\n\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar toStr = Object.prototype.toString;\nvar max = Math.max;\nvar funcType = '[object Function]';\n\nvar concatty = function concatty(a, b) {\n    var arr = [];\n\n    for (var i = 0; i < a.length; i += 1) {\n        arr[i] = a[i];\n    }\n    for (var j = 0; j < b.length; j += 1) {\n        arr[j + a.length] = b[j];\n    }\n\n    return arr;\n};\n\nvar slicy = function slicy(arrLike, offset) {\n    var arr = [];\n    for (var i = offset || 0, j = 0; i < arrLike.length; i += 1, j += 1) {\n        arr[j] = arrLike[i];\n    }\n    return arr;\n};\n\nvar joiny = function (arr, joiner) {\n    var str = '';\n    for (var i = 0; i < arr.length; i += 1) {\n        str += arr[i];\n        if (i + 1 < arr.length) {\n            str += joiner;\n        }\n    }\n    return str;\n};\n\nmodule.exports = function bind(that) {\n    var target = this;\n    if (typeof target !== 'function' || toStr.apply(target) !== funcType) {\n        throw new TypeError(ERROR_MESSAGE + target);\n    }\n    var args = slicy(arguments, 1);\n\n    var bound;\n    var binder = function () {\n        if (this instanceof bound) {\n            var result = target.apply(\n                this,\n                concatty(args, arguments)\n            );\n            if (Object(result) === result) {\n                return result;\n            }\n            return this;\n        }\n        return target.apply(\n            that,\n            concatty(args, arguments)\n        );\n\n    };\n\n    var boundLength = max(0, target.length - args.length);\n    var boundArgs = [];\n    for (var i = 0; i < boundLength; i++) {\n        boundArgs[i] = '$' + i;\n    }\n\n    bound = Function('binder', 'return function (' + joiny(boundArgs, ',') + '){ return binder.apply(this,arguments); }')(binder);\n\n    if (target.prototype) {\n        var Empty = function Empty() {};\n        Empty.prototype = target.prototype;\n        bound.prototype = new Empty();\n        Empty.prototype = null;\n    }\n\n    return bound;\n};\n", "'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = Function.prototype.bind || implementation;\n", "'use strict';\n\nvar call = Function.prototype.call;\nvar $hasOwn = Object.prototype.hasOwnProperty;\nvar bind = require('function-bind');\n\n/** @type {import('.')} */\nmodule.exports = bind.call(call, $hasOwn);\n", "{\n\t\"assert\": true,\n\t\"node:assert\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"assert/strict\": \">= 15\",\n\t\"node:assert/strict\": \">= 16\",\n\t\"async_hooks\": \">= 8\",\n\t\"node:async_hooks\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"buffer_ieee754\": \">= 0.5 && < 0.9.7\",\n\t\"buffer\": true,\n\t\"node:buffer\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"child_process\": true,\n\t\"node:child_process\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"cluster\": \">= 0.5\",\n\t\"node:cluster\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"console\": true,\n\t\"node:console\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"constants\": true,\n\t\"node:constants\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"crypto\": true,\n\t\"node:crypto\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_debug_agent\": \">= 1 && < 8\",\n\t\"_debugger\": \"< 8\",\n\t\"dgram\": true,\n\t\"node:dgram\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"diagnostics_channel\": [\">= 14.17 && < 15\", \">= 15.1\"],\n\t\"node:diagnostics_channel\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"dns\": true,\n\t\"node:dns\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"dns/promises\": \">= 15\",\n\t\"node:dns/promises\": \">= 16\",\n\t\"domain\": \">= 0.7.12\",\n\t\"node:domain\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"events\": true,\n\t\"node:events\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"freelist\": \"< 6\",\n\t\"fs\": true,\n\t\"node:fs\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"fs/promises\": [\">= 10 && < 10.1\", \">= 14\"],\n\t\"node:fs/promises\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_http_agent\": \">= 0.11.1\",\n\t\"node:_http_agent\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_http_client\": \">= 0.11.1\",\n\t\"node:_http_client\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_http_common\": \">= 0.11.1\",\n\t\"node:_http_common\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_http_incoming\": \">= 0.11.1\",\n\t\"node:_http_incoming\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_http_outgoing\": \">= 0.11.1\",\n\t\"node:_http_outgoing\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_http_server\": \">= 0.11.1\",\n\t\"node:_http_server\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"http\": true,\n\t\"node:http\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"http2\": \">= 8.8\",\n\t\"node:http2\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"https\": true,\n\t\"node:https\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"inspector\": \">= 8\",\n\t\"node:inspector\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"inspector/promises\": [\">= 19\"],\n\t\"node:inspector/promises\": [\">= 19\"],\n\t\"_linklist\": \"< 8\",\n\t\"module\": true,\n\t\"node:module\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"net\": true,\n\t\"node:net\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"node-inspect/lib/_inspect\": \">= 7.6 && < 12\",\n\t\"node-inspect/lib/internal/inspect_client\": \">= 7.6 && < 12\",\n\t\"node-inspect/lib/internal/inspect_repl\": \">= 7.6 && < 12\",\n\t\"os\": true,\n\t\"node:os\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"path\": true,\n\t\"node:path\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"path/posix\": \">= 15.3\",\n\t\"node:path/posix\": \">= 16\",\n\t\"path/win32\": \">= 15.3\",\n\t\"node:path/win32\": \">= 16\",\n\t\"perf_hooks\": \">= 8.5\",\n\t\"node:perf_hooks\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"process\": \">= 1\",\n\t\"node:process\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"punycode\": \">= 0.5\",\n\t\"node:punycode\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"querystring\": true,\n\t\"node:querystring\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"readline\": true,\n\t\"node:readline\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"readline/promises\": \">= 17\",\n\t\"node:readline/promises\": \">= 17\",\n\t\"repl\": true,\n\t\"node:repl\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"node:sea\": [\">= 20.12 && < 21\", \">= 21.7\"],\n\t\"smalloc\": \">= 0.11.5 && < 3\",\n\t\"_stream_duplex\": \">= 0.9.4\",\n\t\"node:_stream_duplex\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_stream_transform\": \">= 0.9.4\",\n\t\"node:_stream_transform\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_stream_wrap\": \">= 1.4.1\",\n\t\"node:_stream_wrap\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_stream_passthrough\": \">= 0.9.4\",\n\t\"node:_stream_passthrough\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_stream_readable\": \">= 0.9.4\",\n\t\"node:_stream_readable\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_stream_writable\": \">= 0.9.4\",\n\t\"node:_stream_writable\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"stream\": true,\n\t\"node:stream\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"stream/consumers\": \">= 16.7\",\n\t\"node:stream/consumers\": \">= 16.7\",\n\t\"stream/promises\": \">= 15\",\n\t\"node:stream/promises\": \">= 16\",\n\t\"stream/web\": \">= 16.5\",\n\t\"node:stream/web\": \">= 16.5\",\n\t\"string_decoder\": true,\n\t\"node:string_decoder\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"sys\": [\">= 0.4 && < 0.7\", \">= 0.8\"],\n\t\"node:sys\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"test/reporters\": \">= 19.9 && < 20.2\",\n\t\"node:test/reporters\": [\">= 18.17 && < 19\", \">= 19.9\", \">= 20\"],\n\t\"test/mock_loader\": \">= 22.3 && < 22.7\",\n\t\"node:test/mock_loader\": \">= 22.3 && < 22.7\",\n\t\"node:test\": [\">= 16.17 && < 17\", \">= 18\"],\n\t\"timers\": true,\n\t\"node:timers\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"timers/promises\": \">= 15\",\n\t\"node:timers/promises\": \">= 16\",\n\t\"_tls_common\": \">= 0.11.13\",\n\t\"node:_tls_common\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_tls_legacy\": \">= 0.11.3 && < 10\",\n\t\"_tls_wrap\": \">= 0.11.3\",\n\t\"node:_tls_wrap\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"tls\": true,\n\t\"node:tls\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"trace_events\": \">= 10\",\n\t\"node:trace_events\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"tty\": true,\n\t\"node:tty\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"url\": true,\n\t\"node:url\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"util\": true,\n\t\"node:util\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"util/types\": \">= 15.3\",\n\t\"node:util/types\": \">= 16\",\n\t\"v8/tools/arguments\": \">= 10 && < 12\",\n\t\"v8/tools/codemap\": [\">= 4.4 && < 5\", \">= 5.2 && < 12\"],\n\t\"v8/tools/consarray\": [\">= 4.4 && < 5\", \">= 5.2 && < 12\"],\n\t\"v8/tools/csvparser\": [\">= 4.4 && < 5\", \">= 5.2 && < 12\"],\n\t\"v8/tools/logreader\": [\">= 4.4 && < 5\", \">= 5.2 && < 12\"],\n\t\"v8/tools/profile_view\": [\">= 4.4 && < 5\", \">= 5.2 && < 12\"],\n\t\"v8/tools/splaytree\": [\">= 4.4 && < 5\", \">= 5.2 && < 12\"],\n\t\"v8\": \">= 1\",\n\t\"node:v8\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"vm\": true,\n\t\"node:vm\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"wasi\": [\">= 13.4 && < 13.5\", \">= 18.17 && < 19\", \">= 20\"],\n\t\"node:wasi\": [\">= 18.17 && < 19\", \">= 20\"],\n\t\"worker_threads\": \">= 11.7\",\n\t\"node:worker_threads\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"zlib\": \">= 0.5\",\n\t\"node:zlib\": [\">= 14.18 && < 15\", \">= 16\"]\n}\n", "'use strict';\n\nvar hasOwn = require('hasown');\n\nfunction specifierIncluded(current, specifier) {\n\tvar nodeParts = current.split('.');\n\tvar parts = specifier.split(' ');\n\tvar op = parts.length > 1 ? parts[0] : '=';\n\tvar versionParts = (parts.length > 1 ? parts[1] : parts[0]).split('.');\n\n\tfor (var i = 0; i < 3; ++i) {\n\t\tvar cur = parseInt(nodeParts[i] || 0, 10);\n\t\tvar ver = parseInt(versionParts[i] || 0, 10);\n\t\tif (cur === ver) {\n\t\t\tcontinue; // eslint-disable-line no-restricted-syntax, no-continue\n\t\t}\n\t\tif (op === '<') {\n\t\t\treturn cur < ver;\n\t\t}\n\t\tif (op === '>=') {\n\t\t\treturn cur >= ver;\n\t\t}\n\t\treturn false;\n\t}\n\treturn op === '>=';\n}\n\nfunction matchesRange(current, range) {\n\tvar specifiers = range.split(/ ?&& ?/);\n\tif (specifiers.length === 0) {\n\t\treturn false;\n\t}\n\tfor (var i = 0; i < specifiers.length; ++i) {\n\t\tif (!specifierIncluded(current, specifiers[i])) {\n\t\t\treturn false;\n\t\t}\n\t}\n\treturn true;\n}\n\nfunction versionIncluded(nodeVersion, specifierValue) {\n\tif (typeof specifierValue === 'boolean') {\n\t\treturn specifierValue;\n\t}\n\n\tvar current = typeof nodeVersion === 'undefined'\n\t\t? process.versions && process.versions.node\n\t\t: nodeVersion;\n\n\tif (typeof current !== 'string') {\n\t\tthrow new TypeError(typeof nodeVersion === 'undefined' ? 'Unable to determine current node version' : 'If provided, a valid node version is required');\n\t}\n\n\tif (specifierValue && typeof specifierValue === 'object') {\n\t\tfor (var i = 0; i < specifierValue.length; ++i) {\n\t\t\tif (matchesRange(current, specifierValue[i])) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t}\n\treturn matchesRange(current, specifierValue);\n}\n\nvar data = require('./core.json');\n\nmodule.exports = function isCore(x, nodeVersion) {\n\treturn hasOwn(data, x) && versionIncluded(nodeVersion, data[x]);\n};\n", "var fs = require('fs');\nvar getHomedir = require('./homedir');\nvar path = require('path');\nvar caller = require('./caller');\nvar nodeModulesPaths = require('./node-modules-paths');\nvar normalizeOptions = require('./normalize-options');\nvar isCore = require('is-core-module');\n\nvar realpathFS = process.platform !== 'win32' && fs.realpath && typeof fs.realpath.native === 'function' ? fs.realpath.native : fs.realpath;\n\nvar homedir = getHomedir();\nvar defaultPaths = function () {\n    return [\n        path.join(homedir, '.node_modules'),\n        path.join(homedir, '.node_libraries')\n    ];\n};\n\nvar defaultIsFile = function isFile(file, cb) {\n    fs.stat(file, function (err, stat) {\n        if (!err) {\n            return cb(null, stat.isFile() || stat.isFIFO());\n        }\n        if (err.code === 'ENOENT' || err.code === 'ENOTDIR') return cb(null, false);\n        return cb(err);\n    });\n};\n\nvar defaultIsDir = function isDirectory(dir, cb) {\n    fs.stat(dir, function (err, stat) {\n        if (!err) {\n            return cb(null, stat.isDirectory());\n        }\n        if (err.code === 'ENOENT' || err.code === 'ENOTDIR') return cb(null, false);\n        return cb(err);\n    });\n};\n\nvar defaultRealpath = function realpath(x, cb) {\n    realpathFS(x, function (realpathErr, realPath) {\n        if (realpathErr && realpathErr.code !== 'ENOENT') cb(realpathErr);\n        else cb(null, realpathErr ? x : realPath);\n    });\n};\n\nvar maybeRealpath = function maybeRealpath(realpath, x, opts, cb) {\n    if (opts && opts.preserveSymlinks === false) {\n        realpath(x, cb);\n    } else {\n        cb(null, x);\n    }\n};\n\nvar defaultReadPackage = function defaultReadPackage(readFile, pkgfile, cb) {\n    readFile(pkgfile, function (readFileErr, body) {\n        if (readFileErr) cb(readFileErr);\n        else {\n            try {\n                var pkg = JSON.parse(body);\n                cb(null, pkg);\n            } catch (jsonErr) {\n                cb(null);\n            }\n        }\n    });\n};\n\nvar getPackageCandidates = function getPackageCandidates(x, start, opts) {\n    var dirs = nodeModulesPaths(start, opts, x);\n    for (var i = 0; i < dirs.length; i++) {\n        dirs[i] = path.join(dirs[i], x);\n    }\n    return dirs;\n};\n\nmodule.exports = function resolve(x, options, callback) {\n    var cb = callback;\n    var opts = options;\n    if (typeof options === 'function') {\n        cb = opts;\n        opts = {};\n    }\n    if (typeof x !== 'string') {\n        var err = new TypeError('Path must be a string.');\n        return process.nextTick(function () {\n            cb(err);\n        });\n    }\n\n    opts = normalizeOptions(x, opts);\n\n    var isFile = opts.isFile || defaultIsFile;\n    var isDirectory = opts.isDirectory || defaultIsDir;\n    var readFile = opts.readFile || fs.readFile;\n    var realpath = opts.realpath || defaultRealpath;\n    var readPackage = opts.readPackage || defaultReadPackage;\n    if (opts.readFile && opts.readPackage) {\n        var conflictErr = new TypeError('`readFile` and `readPackage` are mutually exclusive.');\n        return process.nextTick(function () {\n            cb(conflictErr);\n        });\n    }\n    var packageIterator = opts.packageIterator;\n\n    var extensions = opts.extensions || ['.js'];\n    var includeCoreModules = opts.includeCoreModules !== false;\n    var basedir = opts.basedir || path.dirname(caller());\n    var parent = opts.filename || basedir;\n\n    opts.paths = opts.paths || defaultPaths();\n\n    // ensure that `basedir` is an absolute path at this point, resolving against the process' current working directory\n    var absoluteStart = path.resolve(basedir);\n\n    maybeRealpath(\n        realpath,\n        absoluteStart,\n        opts,\n        function (err, realStart) {\n            if (err) cb(err);\n            else init(realStart);\n        }\n    );\n\n    var res;\n    function init(basedir) {\n        if ((/^(?:\\.\\.?(?:\\/|$)|\\/|([A-Za-z]:)?[/\\\\])/).test(x)) {\n            res = path.resolve(basedir, x);\n            if (x === '.' || x === '..' || x.slice(-1) === '/') res += '/';\n            if ((/\\/$/).test(x) && res === basedir) {\n                loadAsDirectory(res, opts.package, onfile);\n            } else loadAsFile(res, opts.package, onfile);\n        } else if (includeCoreModules && isCore(x)) {\n            return cb(null, x);\n        } else loadNodeModules(x, basedir, function (err, n, pkg) {\n            if (err) cb(err);\n            else if (n) {\n                return maybeRealpath(realpath, n, opts, function (err, realN) {\n                    if (err) {\n                        cb(err);\n                    } else {\n                        cb(null, realN, pkg);\n                    }\n                });\n            } else {\n                var moduleError = new Error(\"Cannot find module '\" + x + \"' from '\" + parent + \"'\");\n                moduleError.code = 'MODULE_NOT_FOUND';\n                cb(moduleError);\n            }\n        });\n    }\n\n    function onfile(err, m, pkg) {\n        if (err) cb(err);\n        else if (m) cb(null, m, pkg);\n        else loadAsDirectory(res, function (err, d, pkg) {\n            if (err) cb(err);\n            else if (d) {\n                maybeRealpath(realpath, d, opts, function (err, realD) {\n                    if (err) {\n                        cb(err);\n                    } else {\n                        cb(null, realD, pkg);\n                    }\n                });\n            } else {\n                var moduleError = new Error(\"Cannot find module '\" + x + \"' from '\" + parent + \"'\");\n                moduleError.code = 'MODULE_NOT_FOUND';\n                cb(moduleError);\n            }\n        });\n    }\n\n    function loadAsFile(x, thePackage, callback) {\n        var loadAsFilePackage = thePackage;\n        var cb = callback;\n        if (typeof loadAsFilePackage === 'function') {\n            cb = loadAsFilePackage;\n            loadAsFilePackage = undefined;\n        }\n\n        var exts = [''].concat(extensions);\n        load(exts, x, loadAsFilePackage);\n\n        function load(exts, x, loadPackage) {\n            if (exts.length === 0) return cb(null, undefined, loadPackage);\n            var file = x + exts[0];\n\n            var pkg = loadPackage;\n            if (pkg) onpkg(null, pkg);\n            else loadpkg(path.dirname(file), onpkg);\n\n            function onpkg(err, pkg_, dir) {\n                pkg = pkg_;\n                if (err) return cb(err);\n                if (dir && pkg && opts.pathFilter) {\n                    var rfile = path.relative(dir, file);\n                    var rel = rfile.slice(0, rfile.length - exts[0].length);\n                    var r = opts.pathFilter(pkg, x, rel);\n                    if (r) return load(\n                        [''].concat(extensions.slice()),\n                        path.resolve(dir, r),\n                        pkg\n                    );\n                }\n                isFile(file, onex);\n            }\n            function onex(err, ex) {\n                if (err) return cb(err);\n                if (ex) return cb(null, file, pkg);\n                load(exts.slice(1), x, pkg);\n            }\n        }\n    }\n\n    function loadpkg(dir, cb) {\n        if (dir === '' || dir === '/') return cb(null);\n        if (process.platform === 'win32' && (/^\\w:[/\\\\]*$/).test(dir)) {\n            return cb(null);\n        }\n        if ((/[/\\\\]node_modules[/\\\\]*$/).test(dir)) return cb(null);\n\n        maybeRealpath(realpath, dir, opts, function (unwrapErr, pkgdir) {\n            if (unwrapErr) return loadpkg(path.dirname(dir), cb);\n            var pkgfile = path.join(pkgdir, 'package.json');\n            isFile(pkgfile, function (err, ex) {\n                // on err, ex is false\n                if (!ex) return loadpkg(path.dirname(dir), cb);\n\n                readPackage(readFile, pkgfile, function (err, pkgParam) {\n                    if (err) cb(err);\n\n                    var pkg = pkgParam;\n\n                    if (pkg && opts.packageFilter) {\n                        pkg = opts.packageFilter(pkg, pkgfile);\n                    }\n                    cb(null, pkg, dir);\n                });\n            });\n        });\n    }\n\n    function loadAsDirectory(x, loadAsDirectoryPackage, callback) {\n        var cb = callback;\n        var fpkg = loadAsDirectoryPackage;\n        if (typeof fpkg === 'function') {\n            cb = fpkg;\n            fpkg = opts.package;\n        }\n\n        maybeRealpath(realpath, x, opts, function (unwrapErr, pkgdir) {\n            if (unwrapErr) return cb(unwrapErr);\n            var pkgfile = path.join(pkgdir, 'package.json');\n            isFile(pkgfile, function (err, ex) {\n                if (err) return cb(err);\n                if (!ex) return loadAsFile(path.join(x, 'index'), fpkg, cb);\n\n                readPackage(readFile, pkgfile, function (err, pkgParam) {\n                    if (err) return cb(err);\n\n                    var pkg = pkgParam;\n\n                    if (pkg && opts.packageFilter) {\n                        pkg = opts.packageFilter(pkg, pkgfile);\n                    }\n\n                    if (pkg && pkg.main) {\n                        if (typeof pkg.main !== 'string') {\n                            var mainError = new TypeError('package “' + pkg.name + '” `main` must be a string');\n                            mainError.code = 'INVALID_PACKAGE_MAIN';\n                            return cb(mainError);\n                        }\n                        if (pkg.main === '.' || pkg.main === './') {\n                            pkg.main = 'index';\n                        }\n                        loadAsFile(path.resolve(x, pkg.main), pkg, function (err, m, pkg) {\n                            if (err) return cb(err);\n                            if (m) return cb(null, m, pkg);\n                            if (!pkg) return loadAsFile(path.join(x, 'index'), pkg, cb);\n\n                            var dir = path.resolve(x, pkg.main);\n                            loadAsDirectory(dir, pkg, function (err, n, pkg) {\n                                if (err) return cb(err);\n                                if (n) return cb(null, n, pkg);\n                                loadAsFile(path.join(x, 'index'), pkg, cb);\n                            });\n                        });\n                        return;\n                    }\n\n                    loadAsFile(path.join(x, '/index'), pkg, cb);\n                });\n            });\n        });\n    }\n\n    function processDirs(cb, dirs) {\n        if (dirs.length === 0) return cb(null, undefined);\n        var dir = dirs[0];\n\n        isDirectory(path.dirname(dir), isdir);\n\n        function isdir(err, isdir) {\n            if (err) return cb(err);\n            if (!isdir) return processDirs(cb, dirs.slice(1));\n            loadAsFile(dir, opts.package, onfile);\n        }\n\n        function onfile(err, m, pkg) {\n            if (err) return cb(err);\n            if (m) return cb(null, m, pkg);\n            loadAsDirectory(dir, opts.package, ondir);\n        }\n\n        function ondir(err, n, pkg) {\n            if (err) return cb(err);\n            if (n) return cb(null, n, pkg);\n            processDirs(cb, dirs.slice(1));\n        }\n    }\n    function loadNodeModules(x, start, cb) {\n        var thunk = function () { return getPackageCandidates(x, start, opts); };\n        processDirs(\n            cb,\n            packageIterator ? packageIterator(x, start, thunk, opts) : thunk()\n        );\n    }\n};\n", "{\n\t\"assert\": true,\n\t\"node:assert\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"assert/strict\": \">= 15\",\n\t\"node:assert/strict\": \">= 16\",\n\t\"async_hooks\": \">= 8\",\n\t\"node:async_hooks\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"buffer_ieee754\": \">= 0.5 && < 0.9.7\",\n\t\"buffer\": true,\n\t\"node:buffer\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"child_process\": true,\n\t\"node:child_process\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"cluster\": \">= 0.5\",\n\t\"node:cluster\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"console\": true,\n\t\"node:console\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"constants\": true,\n\t\"node:constants\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"crypto\": true,\n\t\"node:crypto\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_debug_agent\": \">= 1 && < 8\",\n\t\"_debugger\": \"< 8\",\n\t\"dgram\": true,\n\t\"node:dgram\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"diagnostics_channel\": [\">= 14.17 && < 15\", \">= 15.1\"],\n\t\"node:diagnostics_channel\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"dns\": true,\n\t\"node:dns\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"dns/promises\": \">= 15\",\n\t\"node:dns/promises\": \">= 16\",\n\t\"domain\": \">= 0.7.12\",\n\t\"node:domain\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"events\": true,\n\t\"node:events\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"freelist\": \"< 6\",\n\t\"fs\": true,\n\t\"node:fs\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"fs/promises\": [\">= 10 && < 10.1\", \">= 14\"],\n\t\"node:fs/promises\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_http_agent\": \">= 0.11.1\",\n\t\"node:_http_agent\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_http_client\": \">= 0.11.1\",\n\t\"node:_http_client\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_http_common\": \">= 0.11.1\",\n\t\"node:_http_common\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_http_incoming\": \">= 0.11.1\",\n\t\"node:_http_incoming\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_http_outgoing\": \">= 0.11.1\",\n\t\"node:_http_outgoing\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_http_server\": \">= 0.11.1\",\n\t\"node:_http_server\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"http\": true,\n\t\"node:http\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"http2\": \">= 8.8\",\n\t\"node:http2\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"https\": true,\n\t\"node:https\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"inspector\": \">= 8\",\n\t\"node:inspector\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"inspector/promises\": [\">= 19\"],\n\t\"node:inspector/promises\": [\">= 19\"],\n\t\"_linklist\": \"< 8\",\n\t\"module\": true,\n\t\"node:module\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"net\": true,\n\t\"node:net\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"node-inspect/lib/_inspect\": \">= 7.6 && < 12\",\n\t\"node-inspect/lib/internal/inspect_client\": \">= 7.6 && < 12\",\n\t\"node-inspect/lib/internal/inspect_repl\": \">= 7.6 && < 12\",\n\t\"os\": true,\n\t\"node:os\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"path\": true,\n\t\"node:path\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"path/posix\": \">= 15.3\",\n\t\"node:path/posix\": \">= 16\",\n\t\"path/win32\": \">= 15.3\",\n\t\"node:path/win32\": \">= 16\",\n\t\"perf_hooks\": \">= 8.5\",\n\t\"node:perf_hooks\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"process\": \">= 1\",\n\t\"node:process\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"punycode\": \">= 0.5\",\n\t\"node:punycode\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"querystring\": true,\n\t\"node:querystring\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"readline\": true,\n\t\"node:readline\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"readline/promises\": \">= 17\",\n\t\"node:readline/promises\": \">= 17\",\n\t\"repl\": true,\n\t\"node:repl\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"smalloc\": \">= 0.11.5 && < 3\",\n\t\"_stream_duplex\": \">= 0.9.4\",\n\t\"node:_stream_duplex\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_stream_transform\": \">= 0.9.4\",\n\t\"node:_stream_transform\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_stream_wrap\": \">= 1.4.1\",\n\t\"node:_stream_wrap\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_stream_passthrough\": \">= 0.9.4\",\n\t\"node:_stream_passthrough\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_stream_readable\": \">= 0.9.4\",\n\t\"node:_stream_readable\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_stream_writable\": \">= 0.9.4\",\n\t\"node:_stream_writable\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"stream\": true,\n\t\"node:stream\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"stream/consumers\": \">= 16.7\",\n\t\"node:stream/consumers\": \">= 16.7\",\n\t\"stream/promises\": \">= 15\",\n\t\"node:stream/promises\": \">= 16\",\n\t\"stream/web\": \">= 16.5\",\n\t\"node:stream/web\": \">= 16.5\",\n\t\"string_decoder\": true,\n\t\"node:string_decoder\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"sys\": [\">= 0.4 && < 0.7\", \">= 0.8\"],\n\t\"node:sys\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"test/reporters\": \">= 19.9 && < 20.2\",\n\t\"node:test/reporters\": [\">= 18.17 && < 19\", \">= 19.9\", \">= 20\"],\n\t\"node:test\": [\">= 16.17 && < 17\", \">= 18\"],\n\t\"timers\": true,\n\t\"node:timers\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"timers/promises\": \">= 15\",\n\t\"node:timers/promises\": \">= 16\",\n\t\"_tls_common\": \">= 0.11.13\",\n\t\"node:_tls_common\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"_tls_legacy\": \">= 0.11.3 && < 10\",\n\t\"_tls_wrap\": \">= 0.11.3\",\n\t\"node:_tls_wrap\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"tls\": true,\n\t\"node:tls\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"trace_events\": \">= 10\",\n\t\"node:trace_events\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"tty\": true,\n\t\"node:tty\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"url\": true,\n\t\"node:url\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"util\": true,\n\t\"node:util\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"util/types\": \">= 15.3\",\n\t\"node:util/types\": \">= 16\",\n\t\"v8/tools/arguments\": \">= 10 && < 12\",\n\t\"v8/tools/codemap\": [\">= 4.4 && < 5\", \">= 5.2 && < 12\"],\n\t\"v8/tools/consarray\": [\">= 4.4 && < 5\", \">= 5.2 && < 12\"],\n\t\"v8/tools/csvparser\": [\">= 4.4 && < 5\", \">= 5.2 && < 12\"],\n\t\"v8/tools/logreader\": [\">= 4.4 && < 5\", \">= 5.2 && < 12\"],\n\t\"v8/tools/profile_view\": [\">= 4.4 && < 5\", \">= 5.2 && < 12\"],\n\t\"v8/tools/splaytree\": [\">= 4.4 && < 5\", \">= 5.2 && < 12\"],\n\t\"v8\": \">= 1\",\n\t\"node:v8\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"vm\": true,\n\t\"node:vm\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"wasi\": [\">= 13.4 && < 13.5\", \">= 18.17 && < 19\", \">= 20\"],\n\t\"node:wasi\": [\">= 18.17 && < 19\", \">= 20\"],\n\t\"worker_threads\": \">= 11.7\",\n\t\"node:worker_threads\": [\">= 14.18 && < 15\", \">= 16\"],\n\t\"zlib\": \">= 0.5\",\n\t\"node:zlib\": [\">= 14.18 && < 15\", \">= 16\"]\n}\n", "'use strict';\n\nvar isCoreModule = require('is-core-module');\nvar data = require('./core.json');\n\nvar core = {};\nfor (var mod in data) { // eslint-disable-line no-restricted-syntax\n    if (Object.prototype.hasOwnProperty.call(data, mod)) {\n        core[mod] = isCoreModule(mod);\n    }\n}\nmodule.exports = core;\n", "var isCoreModule = require('is-core-module');\n\nmodule.exports = function isCore(x) {\n    return isCoreModule(x);\n};\n", "var isCore = require('is-core-module');\nvar fs = require('fs');\nvar path = require('path');\nvar getHomedir = require('./homedir');\nvar caller = require('./caller');\nvar nodeModulesPaths = require('./node-modules-paths');\nvar normalizeOptions = require('./normalize-options');\n\nvar realpathFS = process.platform !== 'win32' && fs.realpathSync && typeof fs.realpathSync.native === 'function' ? fs.realpathSync.native : fs.realpathSync;\n\nvar homedir = getHomedir();\nvar defaultPaths = function () {\n    return [\n        path.join(homedir, '.node_modules'),\n        path.join(homedir, '.node_libraries')\n    ];\n};\n\nvar defaultIsFile = function isFile(file) {\n    try {\n        var stat = fs.statSync(file, { throwIfNoEntry: false });\n    } catch (e) {\n        if (e && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) return false;\n        throw e;\n    }\n    return !!stat && (stat.isFile() || stat.isFIFO());\n};\n\nvar defaultIsDir = function isDirectory(dir) {\n    try {\n        var stat = fs.statSync(dir, { throwIfNoEntry: false });\n    } catch (e) {\n        if (e && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) return false;\n        throw e;\n    }\n    return !!stat && stat.isDirectory();\n};\n\nvar defaultRealpathSync = function realpathSync(x) {\n    try {\n        return realpathFS(x);\n    } catch (realpathErr) {\n        if (realpathErr.code !== 'ENOENT') {\n            throw realpathErr;\n        }\n    }\n    return x;\n};\n\nvar maybeRealpathSync = function maybeRealpathSync(realpathSync, x, opts) {\n    if (opts && opts.preserveSymlinks === false) {\n        return realpathSync(x);\n    }\n    return x;\n};\n\nvar defaultReadPackageSync = function defaultReadPackageSync(readFileSync, pkgfile) {\n    var body = readFileSync(pkgfile);\n    try {\n        var pkg = JSON.parse(body);\n        return pkg;\n    } catch (jsonErr) {}\n};\n\nvar getPackageCandidates = function getPackageCandidates(x, start, opts) {\n    var dirs = nodeModulesPaths(start, opts, x);\n    for (var i = 0; i < dirs.length; i++) {\n        dirs[i] = path.join(dirs[i], x);\n    }\n    return dirs;\n};\n\nmodule.exports = function resolveSync(x, options) {\n    if (typeof x !== 'string') {\n        throw new TypeError('Path must be a string.');\n    }\n    var opts = normalizeOptions(x, options);\n\n    var isFile = opts.isFile || defaultIsFile;\n    var readFileSync = opts.readFileSync || fs.readFileSync;\n    var isDirectory = opts.isDirectory || defaultIsDir;\n    var realpathSync = opts.realpathSync || defaultRealpathSync;\n    var readPackageSync = opts.readPackageSync || defaultReadPackageSync;\n    if (opts.readFileSync && opts.readPackageSync) {\n        throw new TypeError('`readFileSync` and `readPackageSync` are mutually exclusive.');\n    }\n    var packageIterator = opts.packageIterator;\n\n    var extensions = opts.extensions || ['.js'];\n    var includeCoreModules = opts.includeCoreModules !== false;\n    var basedir = opts.basedir || path.dirname(caller());\n    var parent = opts.filename || basedir;\n\n    opts.paths = opts.paths || defaultPaths();\n\n    // ensure that `basedir` is an absolute path at this point, resolving against the process' current working directory\n    var absoluteStart = maybeRealpathSync(realpathSync, path.resolve(basedir), opts);\n\n    if ((/^(?:\\.\\.?(?:\\/|$)|\\/|([A-Za-z]:)?[/\\\\])/).test(x)) {\n        var res = path.resolve(absoluteStart, x);\n        if (x === '.' || x === '..' || x.slice(-1) === '/') res += '/';\n        var m = loadAsFileSync(res) || loadAsDirectorySync(res);\n        if (m) return maybeRealpathSync(realpathSync, m, opts);\n    } else if (includeCoreModules && isCore(x)) {\n        return x;\n    } else {\n        var n = loadNodeModulesSync(x, absoluteStart);\n        if (n) return maybeRealpathSync(realpathSync, n, opts);\n    }\n\n    var err = new Error(\"Cannot find module '\" + x + \"' from '\" + parent + \"'\");\n    err.code = 'MODULE_NOT_FOUND';\n    throw err;\n\n    function loadAsFileSync(x) {\n        var pkg = loadpkg(path.dirname(x));\n\n        if (pkg && pkg.dir && pkg.pkg && opts.pathFilter) {\n            var rfile = path.relative(pkg.dir, x);\n            var r = opts.pathFilter(pkg.pkg, x, rfile);\n            if (r) {\n                x = path.resolve(pkg.dir, r); // eslint-disable-line no-param-reassign\n            }\n        }\n\n        if (isFile(x)) {\n            return x;\n        }\n\n        for (var i = 0; i < extensions.length; i++) {\n            var file = x + extensions[i];\n            if (isFile(file)) {\n                return file;\n            }\n        }\n    }\n\n    function loadpkg(dir) {\n        if (dir === '' || dir === '/') return;\n        if (process.platform === 'win32' && (/^\\w:[/\\\\]*$/).test(dir)) {\n            return;\n        }\n        if ((/[/\\\\]node_modules[/\\\\]*$/).test(dir)) return;\n\n        var pkgfile = path.join(maybeRealpathSync(realpathSync, dir, opts), 'package.json');\n\n        if (!isFile(pkgfile)) {\n            return loadpkg(path.dirname(dir));\n        }\n\n        var pkg = readPackageSync(readFileSync, pkgfile);\n\n        if (pkg && opts.packageFilter) {\n            // v2 will pass pkgfile\n            pkg = opts.packageFilter(pkg, /*pkgfile,*/ dir); // eslint-disable-line spaced-comment\n        }\n\n        return { pkg: pkg, dir: dir };\n    }\n\n    function loadAsDirectorySync(x) {\n        var pkgfile = path.join(maybeRealpathSync(realpathSync, x, opts), '/package.json');\n        if (isFile(pkgfile)) {\n            try {\n                var pkg = readPackageSync(readFileSync, pkgfile);\n            } catch (e) {}\n\n            if (pkg && opts.packageFilter) {\n                // v2 will pass pkgfile\n                pkg = opts.packageFilter(pkg, /*pkgfile,*/ x); // eslint-disable-line spaced-comment\n            }\n\n            if (pkg && pkg.main) {\n                if (typeof pkg.main !== 'string') {\n                    var mainError = new TypeError('package “' + pkg.name + '” `main` must be a string');\n                    mainError.code = 'INVALID_PACKAGE_MAIN';\n                    throw mainError;\n                }\n                if (pkg.main === '.' || pkg.main === './') {\n                    pkg.main = 'index';\n                }\n                try {\n                    var m = loadAsFileSync(path.resolve(x, pkg.main));\n                    if (m) return m;\n                    var n = loadAsDirectorySync(path.resolve(x, pkg.main));\n                    if (n) return n;\n                } catch (e) {}\n            }\n        }\n\n        return loadAsFileSync(path.join(x, '/index'));\n    }\n\n    function loadNodeModulesSync(x, start) {\n        var thunk = function () { return getPackageCandidates(x, start, opts); };\n        var dirs = packageIterator ? packageIterator(x, start, thunk, opts) : thunk();\n\n        for (var i = 0; i < dirs.length; i++) {\n            var dir = dirs[i];\n            if (isDirectory(path.dirname(dir))) {\n                var m = loadAsFileSync(dir);\n                if (m) return m;\n                var n = loadAsDirectorySync(dir);\n                if (n) return n;\n            }\n        }\n    }\n};\n", "var async = require('./lib/async');\nasync.core = require('./lib/core');\nasync.isCore = require('./lib/is-core');\nasync.sync = require('./lib/sync');\n\nmodule.exports = async;\n", "module.exports = extractDescription\n\n// Extracts description from contents of a readme file in markdown format\nfunction extractDescription (d) {\n  if (!d) return;\n  if (d === \"ERROR: No README data found!\") return;\n  // the first block of text before the first heading\n  // that isn't the first line heading\n  d = d.trim().split('\\n')\n  for (var s = 0; d[s] && d[s].trim().match(/^(#|$)/); s ++);\n  var l = d.length\n  for (var e = s + 1; e < l && d[e].trim(); e ++);\n  return d.slice(s, e).join(' ').trim()\n}\n", "{\n  \"topLevel\": {\n    \"dependancies\": \"dependencies\"\n   ,\"dependecies\": \"dependencies\"\n   ,\"depdenencies\": \"dependencies\"\n   ,\"devEependencies\": \"devDependencies\"\n   ,\"depends\": \"dependencies\"\n   ,\"dev-dependencies\": \"devDependencies\"\n   ,\"devDependences\": \"devDependencies\"\n   ,\"devDepenencies\": \"devDependencies\"\n   ,\"devdependencies\": \"devDependencies\"\n   ,\"repostitory\": \"repository\"\n   ,\"repo\": \"repository\"\n   ,\"prefereGlobal\": \"preferGlobal\"\n   ,\"hompage\": \"homepage\"\n   ,\"hampage\": \"homepage\"\n   ,\"autohr\": \"author\"\n   ,\"autor\": \"author\"\n   ,\"contributers\": \"contributors\"\n   ,\"publicationConfig\": \"publishConfig\"\n   ,\"script\": \"scripts\"\n  },\n  \"bugs\": { \"web\": \"url\", \"name\": \"url\" },\n  \"script\": { \"server\": \"start\", \"tests\": \"test\" }\n}\n", "var semver = require(\"semver\")\nvar validateLicense = require('validate-npm-package-license');\nvar hostedGitInfo = require(\"hosted-git-info\")\nvar isBuiltinModule = require(\"resolve\").isCore\nvar depTypes = [\"dependencies\",\"devDependencies\",\"optionalDependencies\"]\nvar extractDescription = require(\"./extract_description\")\nvar url = require(\"url\")\nvar typos = require(\"./typos.json\")\n\nvar fixer = module.exports = {\n  // default warning function\n  warn: function() {},\n\n  fixRepositoryField: function(data) {\n    if (data.repositories) {\n      this.warn(\"repositories\");\n      data.repository = data.repositories[0]\n    }\n    if (!data.repository) return this.warn(\"missingRepository\")\n    if (typeof data.repository === \"string\") {\n      data.repository = {\n        type: \"git\",\n        url: data.repository\n      }\n    }\n    var r = data.repository.url || \"\"\n    if (r) {\n      var hosted = hostedGitInfo.fromUrl(r)\n      if (hosted) {\n        r = data.repository.url\n          = hosted.getDefaultRepresentation() == \"shortcut\" ? hosted.https() : hosted.toString()\n      }\n    }\n\n    if (r.match(/github.com\\/[^\\/]+\\/[^\\/]+\\.git\\.git$/)) {\n      this.warn(\"brokenGitUrl\", r)\n    }\n  }\n\n, fixTypos: function(data) {\n    Object.keys(typos.topLevel).forEach(function (d) {\n      if (data.hasOwnProperty(d)) {\n        this.warn(\"typo\", d, typos.topLevel[d])\n      }\n    }, this)\n  }\n\n, fixScriptsField: function(data) {\n    if (!data.scripts) return\n    if (typeof data.scripts !== \"object\") {\n      this.warn(\"nonObjectScripts\")\n      delete data.scripts\n      return\n    }\n    Object.keys(data.scripts).forEach(function (k) {\n      if (typeof data.scripts[k] !== \"string\") {\n        this.warn(\"nonStringScript\")\n        delete data.scripts[k]\n      } else if (typos.script[k] && !data.scripts[typos.script[k]]) {\n        this.warn(\"typo\", k, typos.script[k], \"scripts\")\n      }\n    }, this)\n  }\n\n, fixFilesField: function(data) {\n    var files = data.files\n    if (files && !Array.isArray(files)) {\n      this.warn(\"nonArrayFiles\")\n      delete data.files\n    } else if (data.files) {\n      data.files = data.files.filter(function(file) {\n        if (!file || typeof file !== \"string\") {\n          this.warn(\"invalidFilename\", file)\n          return false\n        } else {\n          return true\n        }\n      }, this)\n    }\n  }\n\n, fixBinField: function(data) {\n    if (!data.bin) return;\n    if (typeof data.bin === \"string\") {\n      var b = {}\n      var match\n      if (match = data.name.match(/^@[^/]+[/](.*)$/)) {\n        b[match[1]] = data.bin\n      } else {\n        b[data.name] = data.bin\n      }\n      data.bin = b\n    }\n  }\n\n, fixManField: function(data) {\n    if (!data.man) return;\n    if (typeof data.man === \"string\") {\n      data.man = [ data.man ]\n    }\n  }\n, fixBundleDependenciesField: function(data) {\n    var bdd = \"bundledDependencies\"\n    var bd = \"bundleDependencies\"\n    if (data[bdd] && !data[bd]) {\n      data[bd] = data[bdd]\n      delete data[bdd]\n    }\n    if (data[bd] && !Array.isArray(data[bd])) {\n      this.warn(\"nonArrayBundleDependencies\")\n      delete data[bd]\n    } else if (data[bd]) {\n      data[bd] = data[bd].filter(function(bd) {\n        if (!bd || typeof bd !== 'string') {\n          this.warn(\"nonStringBundleDependency\", bd)\n          return false\n        } else {\n          if (!data.dependencies) {\n            data.dependencies = {}\n          }\n          if (!data.dependencies.hasOwnProperty(bd)) {\n            this.warn(\"nonDependencyBundleDependency\", bd)\n            data.dependencies[bd] = \"*\"\n          }\n          return true\n        }\n      }, this)\n    }\n  }\n\n, fixDependencies: function(data, strict) {\n    var loose = !strict\n    objectifyDeps(data, this.warn)\n    addOptionalDepsToDeps(data, this.warn)\n    this.fixBundleDependenciesField(data)\n\n    ;['dependencies','devDependencies'].forEach(function(deps) {\n      if (!(deps in data)) return\n      if (!data[deps] || typeof data[deps] !== \"object\") {\n        this.warn(\"nonObjectDependencies\", deps)\n        delete data[deps]\n        return\n      }\n      Object.keys(data[deps]).forEach(function (d) {\n        var r = data[deps][d]\n        if (typeof r !== 'string') {\n          this.warn(\"nonStringDependency\", d, JSON.stringify(r))\n          delete data[deps][d]\n        }\n        var hosted = hostedGitInfo.fromUrl(data[deps][d])\n        if (hosted) data[deps][d] = hosted.toString()\n      }, this)\n    }, this)\n  }\n\n, fixModulesField: function (data) {\n    if (data.modules) {\n      this.warn(\"deprecatedModules\")\n      delete data.modules\n    }\n  }\n\n, fixKeywordsField: function (data) {\n    if (typeof data.keywords === \"string\") {\n      data.keywords = data.keywords.split(/,\\s+/)\n    }\n    if (data.keywords && !Array.isArray(data.keywords)) {\n      delete data.keywords\n      this.warn(\"nonArrayKeywords\")\n    } else if (data.keywords) {\n      data.keywords = data.keywords.filter(function(kw) {\n        if (typeof kw !== \"string\" || !kw) {\n          this.warn(\"nonStringKeyword\");\n          return false\n        } else {\n          return true\n        }\n      }, this)\n    }\n  }\n\n, fixVersionField: function(data, strict) {\n    // allow \"loose\" semver 1.0 versions in non-strict mode\n    // enforce strict semver 2.0 compliance in strict mode\n    var loose = !strict\n    if (!data.version) {\n      data.version = \"\"\n      return true\n    }\n    if (!semver.valid(data.version, loose)) {\n      throw new Error('Invalid version: \"'+ data.version + '\"')\n    }\n    data.version = semver.clean(data.version, loose)\n    return true\n  }\n\n, fixPeople: function(data) {\n    modifyPeople(data, unParsePerson)\n    modifyPeople(data, parsePerson)\n  }\n\n, fixNameField: function(data, options) {\n    if (typeof options === \"boolean\") options = {strict: options}\n    else if (typeof options === \"undefined\") options = {}\n    var strict = options.strict\n    if (!data.name && !strict) {\n      data.name = \"\"\n      return\n    }\n    if (typeof data.name !== \"string\") {\n      throw new Error(\"name field must be a string.\")\n    }\n    if (!strict)\n      data.name = data.name.trim()\n    ensureValidName(data.name, strict, options.allowLegacyCase)\n    if (isBuiltinModule(data.name))\n      this.warn(\"conflictingName\", data.name)\n  }\n\n\n, fixDescriptionField: function (data) {\n    if (data.description && typeof data.description !== 'string') {\n      this.warn(\"nonStringDescription\")\n      delete data.description\n    }\n    if (data.readme && !data.description)\n      data.description = extractDescription(data.readme)\n      if(data.description === undefined) delete data.description;\n    if (!data.description) this.warn(\"missingDescription\")\n  }\n\n, fixReadmeField: function (data) {\n    if (!data.readme) {\n      this.warn(\"missingReadme\")\n      data.readme = \"ERROR: No README data found!\"\n    }\n  }\n\n, fixBugsField: function(data) {\n    if (!data.bugs && data.repository && data.repository.url) {\n      var hosted = hostedGitInfo.fromUrl(data.repository.url)\n      if(hosted && hosted.bugs()) {\n        data.bugs = {url: hosted.bugs()}\n      }\n    }\n    else if(data.bugs) {\n      var emailRe = /^.+@.*\\..+$/\n      if(typeof data.bugs == \"string\") {\n        if(emailRe.test(data.bugs))\n          data.bugs = {email:data.bugs}\n        else if(url.parse(data.bugs).protocol)\n          data.bugs = {url: data.bugs}\n        else\n          this.warn(\"nonEmailUrlBugsString\")\n      }\n      else {\n        bugsTypos(data.bugs, this.warn)\n        var oldBugs = data.bugs\n        data.bugs = {}\n        if(oldBugs.url) {\n          if(typeof(oldBugs.url) == \"string\" && url.parse(oldBugs.url).protocol)\n            data.bugs.url = oldBugs.url\n          else\n            this.warn(\"nonUrlBugsUrlField\")\n        }\n        if(oldBugs.email) {\n          if(typeof(oldBugs.email) == \"string\" && emailRe.test(oldBugs.email))\n            data.bugs.email = oldBugs.email\n          else\n            this.warn(\"nonEmailBugsEmailField\")\n        }\n      }\n      if(!data.bugs.email && !data.bugs.url) {\n        delete data.bugs\n        this.warn(\"emptyNormalizedBugs\")\n      }\n    }\n  }\n\n, fixHomepageField: function(data) {\n    if (!data.homepage && data.repository && data.repository.url) {\n      var hosted = hostedGitInfo.fromUrl(data.repository.url)\n      if (hosted && hosted.docs()) data.homepage = hosted.docs()\n    }\n    if (!data.homepage) return\n\n    if(typeof data.homepage !== \"string\") {\n      this.warn(\"nonUrlHomepage\")\n      return delete data.homepage\n    }\n    if(!url.parse(data.homepage).protocol) {\n      data.homepage = \"http://\" + data.homepage\n    }\n  }\n\n, fixLicenseField: function(data) {\n    if (!data.license) {\n      return this.warn(\"missingLicense\")\n    } else{\n      if (\n        typeof(data.license) !== 'string' ||\n        data.license.length < 1 ||\n        data.license.trim() === ''\n      ) {\n        this.warn(\"invalidLicense\")\n      } else {\n        if (!validateLicense(data.license).validForNewPackages)\n          this.warn(\"invalidLicense\")\n      }\n    }\n  }\n}\n\nfunction isValidScopedPackageName(spec) {\n  if (spec.charAt(0) !== '@') return false\n\n  var rest = spec.slice(1).split('/')\n  if (rest.length !== 2) return false\n\n  return rest[0] && rest[1] &&\n    rest[0] === encodeURIComponent(rest[0]) &&\n    rest[1] === encodeURIComponent(rest[1])\n}\n\nfunction isCorrectlyEncodedName(spec) {\n  return !spec.match(/[\\/@\\s\\+%:]/) &&\n    spec === encodeURIComponent(spec)\n}\n\nfunction ensureValidName (name, strict, allowLegacyCase) {\n  if (name.charAt(0) === \".\" ||\n      !(isValidScopedPackageName(name) || isCorrectlyEncodedName(name)) ||\n      (strict && (!allowLegacyCase) && name !== name.toLowerCase()) ||\n      name.toLowerCase() === \"node_modules\" ||\n      name.toLowerCase() === \"favicon.ico\") {\n        throw new Error(\"Invalid name: \" + JSON.stringify(name))\n  }\n}\n\nfunction modifyPeople (data, fn) {\n  if (data.author) data.author = fn(data.author)\n  ;[\"maintainers\", \"contributors\"].forEach(function (set) {\n    if (!Array.isArray(data[set])) return;\n    data[set] = data[set].map(fn)\n  })\n  return data\n}\n\nfunction unParsePerson (person) {\n  if (typeof person === \"string\") return person\n  var name = person.name || \"\"\n  var u = person.url || person.web\n  var url = u ? (\" (\"+u+\")\") : \"\"\n  var e = person.email || person.mail\n  var email = e ? (\" <\"+e+\">\") : \"\"\n  return name+email+url\n}\n\nfunction parsePerson (person) {\n  if (typeof person !== \"string\") return person\n  var name = person.match(/^([^\\(<]+)/)\n  var url = person.match(/\\(([^\\)]+)\\)/)\n  var email = person.match(/<([^>]+)>/)\n  var obj = {}\n  if (name && name[0].trim()) obj.name = name[0].trim()\n  if (email) obj.email = email[1];\n  if (url) obj.url = url[1];\n  return obj\n}\n\nfunction addOptionalDepsToDeps (data, warn) {\n  var o = data.optionalDependencies\n  if (!o) return;\n  var d = data.dependencies || {}\n  Object.keys(o).forEach(function (k) {\n    d[k] = o[k]\n  })\n  data.dependencies = d\n}\n\nfunction depObjectify (deps, type, warn) {\n  if (!deps) return {}\n  if (typeof deps === \"string\") {\n    deps = deps.trim().split(/[\\n\\r\\s\\t ,]+/)\n  }\n  if (!Array.isArray(deps)) return deps\n  warn(\"deprecatedArrayDependencies\", type)\n  var o = {}\n  deps.filter(function (d) {\n    return typeof d === \"string\"\n  }).forEach(function(d) {\n    d = d.trim().split(/(:?[@\\s><=])/)\n    var dn = d.shift()\n    var dv = d.join(\"\")\n    dv = dv.trim()\n    dv = dv.replace(/^@/, \"\")\n    o[dn] = dv\n  })\n  return o\n}\n\nfunction objectifyDeps (data, warn) {\n  depTypes.forEach(function (type) {\n    if (!data[type]) return;\n    data[type] = depObjectify(data[type], type, warn)\n  })\n}\n\nfunction bugsTypos(bugs, warn) {\n  if (!bugs) return\n  Object.keys(bugs).forEach(function (k) {\n    if (typos.bugs[k]) {\n      warn(\"typo\", k, typos.bugs[k], \"bugs\")\n      bugs[typos.bugs[k]] = bugs[k]\n      delete bugs[k]\n    }\n  })\n}\n", "{\n  \"repositories\": \"'repositories' (plural) Not supported. Please pick one as the 'repository' field\"\n  ,\"missingRepository\": \"No repository field.\"\n  ,\"brokenGitUrl\": \"Probably broken git url: %s\"\n  ,\"nonObjectScripts\": \"scripts must be an object\"\n  ,\"nonStringScript\": \"script values must be string commands\"\n  ,\"nonArrayFiles\": \"Invalid 'files' member\"\n  ,\"invalidFilename\": \"Invalid filename in 'files' list: %s\"\n  ,\"nonArrayBundleDependencies\": \"Invalid 'bundleDependencies' list. Must be array of package names\"\n  ,\"nonStringBundleDependency\": \"Invalid bundleDependencies member: %s\"\n  ,\"nonDependencyBundleDependency\": \"Non-dependency in bundleDependencies: %s\"\n  ,\"nonObjectDependencies\": \"%s field must be an object\"\n  ,\"nonStringDependency\": \"Invalid dependency: %s %s\"\n  ,\"deprecatedArrayDependencies\": \"specifying %s as array is deprecated\"\n  ,\"deprecatedModules\": \"modules field is deprecated\"\n  ,\"nonArrayKeywords\": \"keywords should be an array of strings\"\n  ,\"nonStringKeyword\": \"keywords should be an array of strings\"\n  ,\"conflictingName\": \"%s is also the name of a node core module.\"\n  ,\"nonStringDescription\": \"'description' field should be a string\"\n  ,\"missingDescription\": \"No description\"\n  ,\"missingReadme\": \"No README data\"\n  ,\"missingLicense\": \"No license field.\"\n  ,\"nonEmailUrlBugsString\": \"Bug string field must be url, email, or {email,url}\"\n  ,\"nonUrlBugsUrlField\": \"bugs.url field must be a string url. Deleted.\"\n  ,\"nonEmailBugsEmailField\": \"bugs.email field must be a string email. Deleted.\"\n  ,\"emptyNormalizedBugs\": \"Normalized value of bugs field is an empty object. Deleted.\"\n  ,\"nonUrlHomepage\": \"homepage field must be a string url. Deleted.\"\n  ,\"invalidLicense\": \"license should be a valid SPDX license expression\"\n  ,\"typo\": \"%s should probably be %s.\"\n}\n", "var util = require(\"util\")\nvar messages = require(\"./warning_messages.json\")\n\nmodule.exports = function() {\n  var args = Array.prototype.slice.call(arguments, 0)\n  var warningName = args.shift()\n  if (warningName == \"typo\") {\n    return makeTypoWarning.apply(null,args)\n  }\n  else {\n    var msgTemplate = messages[warningName] ? messages[warningName] : warningName + \": '%s'\"\n    args.unshift(msgTemplate)\n    return util.format.apply(null, args)\n  }\n}\n\nfunction makeTypoWarning (providedName, probableName, field) {\n  if (field) {\n    providedName = field + \"['\" + providedName + \"']\"\n    probableName = field + \"['\" + probableName + \"']\"\n  }\n  return util.format(messages.typo, providedName, probableName)\n}\n", "module.exports = normalize\n\nvar fixer = require(\"./fixer\")\nnormalize.fixer = fixer\n\nvar makeWarning = require(\"./make_warning\")\n\nvar fieldsToFix = ['name','version','description','repository','modules','scripts'\n                  ,'files','bin','man','bugs','keywords','readme','homepage','license']\nvar otherThingsToFix = ['dependencies','people', 'typos']\n\nvar thingsToFix = fieldsToFix.map(function(fieldName) {\n  return ucFirst(fieldName) + \"Field\"\n})\n// two ways to do this in CoffeeScript on only one line, sub-70 chars:\n// thingsToFix = fieldsToFix.map (name) -> ucFirst(name) + \"Field\"\n// thingsToFix = (ucFirst(name) + \"Field\" for name in fieldsToFix)\nthingsToFix = thingsToFix.concat(otherThingsToFix)\n\nfunction normalize (data, warn, strict) {\n  if(warn === true) warn = null, strict = true\n  if(!strict) strict = false\n  if(!warn || data.private) warn = function(msg) { /* noop */ }\n\n  if (data.scripts &&\n      data.scripts.install === \"node-gyp rebuild\" &&\n      !data.scripts.preinstall) {\n    data.gypfile = true\n  }\n  fixer.warn = function() { warn(makeWarning.apply(null, arguments)) }\n  thingsToFix.forEach(function(thingName) {\n    fixer[\"fix\" + ucFirst(thingName)](data, strict)\n  })\n  data._id = data.name + \"@\" + data.version\n}\n\nfunction ucFirst (string) {\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}\n", "'use strict';\nconst {promisify} = require('util');\nconst fs = require('fs');\nconst path = require('path');\nconst parseJson = require('parse-json');\n\nconst readFileAsync = promisify(fs.readFile);\n\nmodule.exports = async options => {\n\toptions = {\n\t\tcwd: process.cwd(),\n\t\tnormalize: true,\n\t\t...options\n\t};\n\n\tconst filePath = path.resolve(options.cwd, 'package.json');\n\tconst json = parseJson(await readFileAsync(filePath, 'utf8'));\n\n\tif (options.normalize) {\n\t\trequire('normalize-package-data')(json);\n\t}\n\n\treturn json;\n};\n\nmodule.exports.sync = options => {\n\toptions = {\n\t\tcwd: process.cwd(),\n\t\tnormalize: true,\n\t\t...options\n\t};\n\n\tconst filePath = path.resolve(options.cwd, 'package.json');\n\tconst json = parseJson(fs.readFileSync(filePath, 'utf8'));\n\n\tif (options.normalize) {\n\t\trequire('normalize-package-data')(json);\n\t}\n\n\treturn json;\n};\n", "'use strict';\nconst path = require('path');\nconst findUp = require('find-up');\nconst readPkg = require('read-pkg');\n\nmodule.exports = async options => {\n\tconst filePath = await findUp('package.json', options);\n\n\tif (!filePath) {\n\t\treturn;\n\t}\n\n\treturn {\n\t\tpackageJson: await readPkg({...options, cwd: path.dirname(filePath)}),\n\t\tpath: filePath\n\t};\n};\n\nmodule.exports.sync = options => {\n\tconst filePath = findUp.sync('package.json', options);\n\n\tif (!filePath) {\n\t\treturn;\n\t}\n\n\treturn {\n\t\tpackageJson: readPkg.sync({...options, cwd: path.dirname(filePath)}),\n\t\tpath: filePath\n\t};\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.crossLaunchCommand = exports.callbackLaunchCommand = void 0;\nconst api_1 = require(\"@raycast/api\");\nconst read_pkg_up_1 = require(\"read-pkg-up\");\nconst callbackLaunchCommand = async (options, result) => (0, api_1.launchCommand)({\n    ...options,\n    context: {\n        ...options.context,\n        ...result,\n    },\n});\nexports.callbackLaunchCommand = callbackLaunchCommand;\nconst crossLaunchCommand = async (options, callbackLaunchOptions) => {\n    if (callbackLaunchOptions === false) {\n        return (0, api_1.launchCommand)(options);\n    }\n    // eslint-disable-next-line unicorn/prefer-module, @typescript-eslint/no-unsafe-assignment\n    const pack = (0, read_pkg_up_1.sync)({ cwd: __dirname, normalize: false });\n    const ownerOrAuthorName = \n    // The `ownerOrAuthorName` was introduced in @raycast/api@1.78.0. We use a fallback to support older versions.\n    api_1.environment.ownerOrAuthorName ??\n        (pack?.packageJson?.owner ?? pack?.packageJson?.author);\n    if ('ownerOrAuthorName' in options) {\n        const targetHandle = `${options.ownerOrAuthorName}/${options.extensionName}`;\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n        if (!pack?.packageJson?.crossExtensions?.includes(targetHandle)) {\n            const message = `Target extension '${targetHandle}' should be listed in 'crossExtensions' of package.json.`;\n            console.error(message);\n            return;\n        }\n    }\n    return (0, api_1.launchCommand)({\n        ...options,\n        context: {\n            ...options.context,\n            callbackLaunchOptions: {\n                name: api_1.environment.commandName,\n                extensionName: api_1.environment.extensionName,\n                ownerOrAuthorName,\n                type: api_1.LaunchType.UserInitiated,\n                ...callbackLaunchOptions,\n            },\n        },\n    });\n};\nexports.crossLaunchCommand = crossLaunchCommand;\n", "import { getCurrentInterval, isPaused } from \"../lib/intervals\";\nimport { continueTimer } from \"../lib/timer\";\n\nexport default async function () {\n  const currentInterval = getCurrentInterval();\n  if (!currentInterval) {\n    return \"No active timer\";\n  }\n  if (!isPaused(currentInterval)) {\n    return \"Timer is not paused\";\n  }\n  await continueTimer();\n  return \"Timer continued\";\n}\n", "import { Cache, LaunchType, LocalStorage, getPreferenceValues, launchCommand } from \"@raycast/api\";\nimport { FocusText, LongBreakText, ShortBreakText } from \"./constants\";\nimport { enableFocusWhileFocused, setDND } from \"./doNotDisturb\";\nimport { Interval, IntervalExecutor, IntervalType } from \"./types\";\n\nconst cache = new Cache();\n\nconst CURRENT_INTERVAL_CACHE_KEY = \"pomodoro-interval/1.1\";\nconst COMPLETED_POMODORO_COUNT_CACHE_KEY = \"pomodoro-interval/completed-pomodoro-count\";\nconst POMODORO_INTERVAL_HISTORY = \"pomodoro-interval/history\";\n\nconst currentTimestamp = () => Math.round(new Date().valueOf() / 1000);\n\nexport async function getIntervalHistory(): Promise<Interval[]> {\n  const history = await LocalStorage.getItem(POMODORO_INTERVAL_HISTORY);\n\n  if (typeof history !== \"string\" || history === null) {\n    return [];\n  }\n  const intervales = JSON.parse(history);\n  return intervales;\n}\n\nexport async function saveIntervalHistory(interval: Interval) {\n  const history = await getIntervalHistory();\n  const index = history.findIndex((i) => i.id === interval.id);\n\n  if (index !== -1) {\n    history[index] = interval;\n  } else {\n    history.push(interval);\n  }\n\n  await LocalStorage.setItem(POMODORO_INTERVAL_HISTORY, JSON.stringify(history));\n}\n\nexport function duration({ parts }: Interval): number {\n  return parts.reduce((acc, part) => {\n    return (\n      (typeof part.pausedAt !== \"undefined\" ? part.pausedAt - part.startedAt : currentTimestamp() - part.startedAt) +\n      acc\n    );\n  }, 0);\n}\n\nexport function progress(interval: Interval): number {\n  return (duration(interval) / interval.length) * 100;\n}\n\nexport function isPaused({ parts }: Interval): boolean {\n  return !!parts[parts.length - 1].pausedAt;\n}\n\nexport function createInterval(type: IntervalType, isFreshStart?: boolean, customDuration?: number): Interval {\n  let completedCount = 0;\n  if (isFreshStart) {\n    cache.set(COMPLETED_POMODORO_COUNT_CACHE_KEY, completedCount.toString());\n  } else {\n    completedCount = parseInt(cache.get(COMPLETED_POMODORO_COUNT_CACHE_KEY) ?? \"0\", 10);\n    completedCount++;\n    cache.set(COMPLETED_POMODORO_COUNT_CACHE_KEY, completedCount.toString());\n  }\n\n  const interval: Interval = {\n    type,\n    id: completedCount,\n    length: customDuration || intervalDurations[type],\n    parts: [\n      {\n        startedAt: currentTimestamp(),\n      },\n    ],\n  };\n\n  cache.set(CURRENT_INTERVAL_CACHE_KEY, JSON.stringify(interval));\n  saveIntervalHistory(interval).then();\n  if (type === \"focus\") setDND(true);\n  return interval;\n}\n\nexport function pauseInterval(): Interval | undefined {\n  let interval = getCurrentInterval();\n  if (interval?.type === \"focus\") setDND(false);\n  if (interval) {\n    const parts = [...interval.parts];\n    parts[parts.length - 1].pausedAt = currentTimestamp();\n    interval = {\n      ...interval,\n      parts,\n    };\n    cache.set(CURRENT_INTERVAL_CACHE_KEY, JSON.stringify(interval));\n  }\n  return interval;\n}\n\nexport function continueInterval(): Interval | undefined {\n  let interval = getCurrentInterval();\n  if (interval) {\n    const parts = [...interval.parts, { startedAt: currentTimestamp() }];\n    interval = {\n      ...interval,\n      parts,\n    };\n    cache.set(CURRENT_INTERVAL_CACHE_KEY, JSON.stringify(interval));\n    if (interval.type === \"focus\") setDND(true);\n  }\n  return interval;\n}\n\nexport function resetInterval() {\n  cache.remove(CURRENT_INTERVAL_CACHE_KEY);\n}\n\nexport function restartInterval() {\n  const currentInterval = getCurrentInterval();\n  if (currentInterval) {\n    const { type } = currentInterval;\n    if (type === \"focus\") setDND(true);\n    createInterval(type, false); // Uses existing caching mechanism to reset interval\n  }\n}\n\nexport function getCurrentInterval(): Interval | undefined {\n  const result = cache.get(CURRENT_INTERVAL_CACHE_KEY);\n  if (result) {\n    return JSON.parse(result);\n  }\n}\n\nexport function endOfInterval(currentInterval: Interval) {\n  try {\n    currentInterval.parts[currentInterval.parts.length - 1].endAt = currentTimestamp();\n    saveIntervalHistory(currentInterval).then();\n    if (currentInterval.type === \"focus\" && enableFocusWhileFocused) {\n      setDND(false, {\n        name: \"pomodoro-control-timer\",\n        context: { currentInterval },\n      });\n    } else {\n      launchCommand({\n        name: \"pomodoro-control-timer\",\n        type: LaunchType.UserInitiated,\n        context: { currentInterval },\n      });\n    }\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nexport function getCompletedPomodoroCount(): number {\n  const result = cache.get(COMPLETED_POMODORO_COUNT_CACHE_KEY);\n  if (result) {\n    return parseInt(result, 10);\n  }\n\n  return 0;\n}\n\nexport function getNextIntervalExecutor(): IntervalExecutor {\n  const currentInterval = getCurrentInterval();\n  resetInterval();\n\n  const completedCount = getCompletedPomodoroCount();\n  const longBreakThreshold = parseInt(preferences.longBreakStartThreshold, 10);\n  let executor: IntervalExecutor | undefined;\n  switch (currentInterval?.type) {\n    case \"short-break\":\n      executor = {\n        title: FocusText,\n        onStart: () => createInterval(\"focus\", false),\n      };\n      break;\n    case \"long-break\":\n      executor = { title: FocusText, onStart: () => createInterval(\"focus\") };\n      break;\n    default:\n      if (completedCount === longBreakThreshold) {\n        executor = {\n          title: LongBreakText,\n          onStart: () => createInterval(\"long-break\"),\n        };\n      } else {\n        executor = {\n          title: ShortBreakText,\n          onStart: () => createInterval(\"short-break\", false),\n        };\n      }\n      break;\n  }\n\n  return executor;\n}\n\nexport const preferences = getPreferenceValues<Preferences>();\nexport const intervalDurations: Record<IntervalType, number> = {\n  focus: parseFloat(preferences.focusIntervalDuration) * 60,\n  \"short-break\": parseFloat(preferences.shortBreakIntervalDuration) * 60,\n  \"long-break\": parseFloat(preferences.longBreakIntervalDuration) * 60,\n};\n", "import { confirmAlert, LaunchType, open, getPreferenceValues } from \"@raycast/api\";\nimport { crossLaunchCommand, LaunchOptions } from \"raycast-cross-extension\";\n\nexport const { enableFocusWhileFocused } = getPreferenceValues<ExtensionPreferences>();\n\nexport const dndLaunchOptions = {\n  type: LaunchType.Background,\n  extensionName: \"do-not-disturb\",\n  ownerOrAuthorName: \"yakitrak\",\n};\n\nexport async function setDND(enabled: boolean, callbackOptions?: Partial<LaunchOptions>) {\n  if (!enableFocusWhileFocused) return;\n  return crossLaunchCommand(\n    {\n      ...dndLaunchOptions,\n      name: enabled ? \"on\" : \"off\",\n      context: { supressHUD: !enabled },\n    },\n    callbackOptions ? callbackOptions : false,\n  ).catch(() => {\n    // Do nothing here because we're going to check when mounting the extension\n  });\n}\n\nexport async function checkDNDExtensionInstall(callbackOptions?: Partial<LaunchOptions>) {\n  if (!enableFocusWhileFocused) return;\n  await crossLaunchCommand(\n    {\n      ...dndLaunchOptions,\n      name: \"status\",\n      context: { suppressHUD: true },\n    },\n    callbackOptions ? callbackOptions : false,\n  ).catch(async () => {\n    const installDND = await confirmAlert({\n      title: \"Need to Install Additional Extension\",\n      message:\n        'The \"Enable Do Not Disturb mode while focused\" feature requires the \"Do Not Distrub\" extension, do you want to move to the install page now?',\n    });\n    if (installDND) {\n      // Open the store view\n      await open(\"raycast://extensions/yakitrak/do-not-disturb\");\n    }\n  });\n}\n", "import { launchCommand, LaunchType } from \"@raycast/api\";\nimport { checkDNDExtensionInstall, setDND } from \"./doNotDisturb\";\nimport { continueInterval, createInterval, pauseInterval, resetInterval } from \"./intervals\";\nimport { IntervalType } from \"./types\";\n\nexport async function startTimer(type: IntervalType, duration?: number) {\n  await checkDNDExtensionInstall();\n  const interval = createInterval(type, false, duration);\n  await refreshMenuBar();\n  return interval;\n}\n\nexport async function pauseTimer() {\n  const interval = pauseInterval();\n  await refreshMenuBar();\n  return interval;\n}\n\nexport async function continueTimer() {\n  const interval = continueInterval();\n  await refreshMenuBar();\n  return interval;\n}\n\nexport async function stopTimer() {\n  resetInterval();\n  setDND(false);\n  await refreshMenuBar();\n  return \"Timer stopped\";\n}\n\nasync function refreshMenuBar() {\n  try {\n    await launchCommand({\n      name: \"pomodoro-menu-bar\",\n      type: LaunchType.UserInitiated,\n    });\n  } catch (error) {\n    console.error(error);\n  }\n}\n"], "mappings": "yoBAAA,IAAAA,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAO,CAACC,KAAOC,IAAe,IAAI,QAAQC,GAAW,CAC1DA,EAAQF,EAAG,GAAGC,CAAU,CAAC,CAC1B,CAAC,EAEDH,GAAO,QAAUC,GAEjBD,GAAO,QAAQ,QAAUC,KCRzB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAMC,GAAO,KAEPC,GAASC,GAAe,CAC7B,GAAI,GAAG,OAAO,UAAUA,CAAW,GAAKA,IAAgB,MAAaA,EAAc,GAClF,OAAO,QAAQ,OAAO,IAAI,UAAU,qDAAqD,CAAC,EAG3F,IAAMC,EAAQ,CAAC,EACXC,EAAc,EAEZC,EAAO,IAAM,CAClBD,IAEID,EAAM,OAAS,GAClBA,EAAM,MAAM,EAAE,CAEhB,EAEMG,EAAM,CAACC,EAAIC,KAAYC,IAAS,CACrCL,IAEA,IAAMM,EAASV,GAAKO,EAAI,GAAGE,CAAI,EAE/BD,EAAQE,CAAM,EAEdA,EAAO,KAAKL,EAAMA,CAAI,CACvB,EAEMM,EAAU,CAACJ,EAAIC,KAAYC,IAAS,CACrCL,EAAcF,EACjBI,EAAIC,EAAIC,EAAS,GAAGC,CAAI,EAExBN,EAAM,KAAKG,EAAI,KAAK,KAAMC,EAAIC,EAAS,GAAGC,CAAI,CAAC,CAEjD,EAEMG,EAAY,CAACL,KAAOE,IAAS,IAAI,QAAQD,GAAWG,EAAQJ,EAAIC,EAAS,GAAGC,CAAI,CAAC,EACvF,cAAO,iBAAiBG,EAAW,CAClC,YAAa,CACZ,IAAK,IAAMR,CACZ,EACA,aAAc,CACb,IAAK,IAAMD,EAAM,MAClB,EACA,WAAY,CACX,MAAO,IAAM,CACZA,EAAM,OAAS,CAChB,CACD,CACD,CAAC,EAEMS,CACR,EAEAb,GAAO,QAAUE,GACjBF,GAAO,QAAQ,QAAUE,KCxDzB,IAAAY,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAMC,GAAS,KAETC,GAAN,cAAuB,KAAM,CAC5B,YAAYC,EAAO,CAClB,MAAM,EACN,KAAK,MAAQA,CACd,CACD,EAGMC,GAAc,MAAOC,EAASC,IAAWA,EAAO,MAAMD,CAAO,EAG7DE,GAAS,MAAMF,GAAW,CAC/B,IAAMG,EAAS,MAAM,QAAQ,IAAIH,CAAO,EACxC,GAAIG,EAAO,CAAC,IAAM,GACjB,MAAM,IAAIN,GAASM,EAAO,CAAC,CAAC,EAG7B,MAAO,EACR,EAEMC,GAAU,MAAOC,EAAUJ,EAAQK,IAAY,CACpDA,EAAU,CACT,YAAa,IACb,cAAe,GACf,GAAGA,CACJ,EAEA,IAAMC,EAAQX,GAAOU,EAAQ,WAAW,EAGlCE,EAAQ,CAAC,GAAGH,CAAQ,EAAE,IAAIL,GAAW,CAACA,EAASO,EAAMR,GAAaC,EAASC,CAAM,CAAC,CAAC,EAGnFQ,EAAab,GAAOU,EAAQ,cAAgB,EAAI,GAAQ,EAE9D,GAAI,CACH,MAAM,QAAQ,IAAIE,EAAM,IAAIR,GAAWS,EAAWP,GAAQF,CAAO,CAAC,CAAC,CACpE,OAASU,EAAO,CACf,GAAIA,aAAiBb,GACpB,OAAOa,EAAM,MAGd,MAAMA,CACP,CACD,EAEAf,GAAO,QAAUS,GAEjBT,GAAO,QAAQ,QAAUS,KCnDzB,IAAAO,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAMC,GAAO,QAAQ,MAAM,EACrBC,GAAK,QAAQ,IAAI,EACjB,CAAC,UAAAC,EAAS,EAAI,QAAQ,MAAM,EAC5BC,GAAU,KAEVC,GAASF,GAAUD,GAAG,IAAI,EAC1BI,GAAUH,GAAUD,GAAG,KAAK,EAE5BK,GAAe,CACpB,UAAW,cACX,KAAM,QACP,EAEA,SAASC,GAAU,CAAC,KAAAC,CAAI,EAAG,CAC1B,GAAI,EAAAA,KAAQF,IAIZ,MAAM,IAAI,MAAM,2BAA2BE,CAAI,EAAE,CAClD,CAEA,IAAMC,GAAY,CAACD,EAAME,IAASF,IAAS,QAAaE,EAAKJ,GAAaE,CAAI,CAAC,EAAE,EAEjFT,GAAO,QAAU,MAAOY,EAAOC,IAAY,CAC1CA,EAAU,CACT,IAAK,QAAQ,IAAI,EACjB,KAAM,OACN,cAAe,GACf,GAAGA,CACJ,EACAL,GAAUK,CAAO,EACjB,IAAMC,EAASD,EAAQ,cAAgBR,GAASC,GAEhD,OAAOF,GAAQQ,EAAO,MAAMG,GAAS,CACpC,GAAI,CACH,IAAMJ,EAAO,MAAMG,EAAOb,GAAK,QAAQY,EAAQ,IAAKE,CAAK,CAAC,EAC1D,OAAOL,GAAUG,EAAQ,KAAMF,CAAI,CACpC,MAAY,CACX,MAAO,EACR,CACD,EAAGE,CAAO,CACX,EAEAb,GAAO,QAAQ,KAAO,CAACY,EAAOC,IAAY,CACzCA,EAAU,CACT,IAAK,QAAQ,IAAI,EACjB,cAAe,GACf,KAAM,OACN,GAAGA,CACJ,EACAL,GAAUK,CAAO,EACjB,IAAMC,EAASD,EAAQ,cAAgBX,GAAG,SAAWA,GAAG,UAExD,QAAWa,KAASH,EACnB,GAAI,CACH,IAAMD,EAAOG,EAAOb,GAAK,QAAQY,EAAQ,IAAKE,CAAK,CAAC,EAEpD,GAAIL,GAAUG,EAAQ,KAAMF,CAAI,EAC/B,OAAOI,CAET,MAAY,CACZ,CAEF,IChEA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAMC,GAAK,QAAQ,IAAI,EACjB,CAAC,UAAAC,EAAS,EAAI,QAAQ,MAAM,EAE5BC,GAAUD,GAAUD,GAAG,MAAM,EAEnCD,GAAO,QAAU,MAAMI,GAAQ,CAC9B,GAAI,CACH,aAAMD,GAAQC,CAAI,EACX,EACR,MAAY,CACX,MAAO,EACR,CACD,EAEAJ,GAAO,QAAQ,KAAOI,GAAQ,CAC7B,GAAI,CACH,OAAAH,GAAG,WAAWG,CAAI,EACX,EACR,MAAY,CACX,MAAO,EACR,CACD,ICtBA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAMC,GAAO,QAAQ,MAAM,EACrBC,GAAa,KACbC,GAAa,KAEbC,GAAO,OAAO,aAAa,EAEjCJ,GAAO,QAAU,MAAOK,EAAMC,EAAU,CAAC,IAAM,CAC9C,IAAIC,EAAYN,GAAK,QAAQK,EAAQ,KAAO,EAAE,EACxC,CAAC,KAAAE,CAAI,EAAIP,GAAK,MAAMM,CAAS,EAC7BE,EAAQ,CAAC,EAAE,OAAOJ,CAAI,EAEtBK,EAAa,MAAMC,GAAiB,CACzC,GAAI,OAAON,GAAS,WACnB,OAAOH,GAAWO,EAAOE,CAAa,EAGvC,IAAMC,EAAY,MAAMP,EAAKM,EAAc,GAAG,EAC9C,OAAI,OAAOC,GAAc,SACjBV,GAAW,CAACU,CAAS,EAAGD,CAAa,EAGtCC,CACR,EAGA,OAAa,CAEZ,IAAMA,EAAY,MAAMF,EAAW,CAAC,GAAGJ,EAAS,IAAKC,CAAS,CAAC,EAE/D,GAAIK,IAAcR,GACjB,OAGD,GAAIQ,EACH,OAAOX,GAAK,QAAQM,EAAWK,CAAS,EAGzC,GAAIL,IAAcC,EACjB,OAGDD,EAAYN,GAAK,QAAQM,CAAS,CACnC,CACD,EAEAP,GAAO,QAAQ,KAAO,CAACK,EAAMC,EAAU,CAAC,IAAM,CAC7C,IAAIC,EAAYN,GAAK,QAAQK,EAAQ,KAAO,EAAE,EACxC,CAAC,KAAAE,CAAI,EAAIP,GAAK,MAAMM,CAAS,EAC7BE,EAAQ,CAAC,EAAE,OAAOJ,CAAI,EAEtBK,EAAaC,GAAiB,CACnC,GAAI,OAAON,GAAS,WACnB,OAAOH,GAAW,KAAKO,EAAOE,CAAa,EAG5C,IAAMC,EAAYP,EAAKM,EAAc,GAAG,EACxC,OAAI,OAAOC,GAAc,SACjBV,GAAW,KAAK,CAACU,CAAS,EAAGD,CAAa,EAG3CC,CACR,EAGA,OAAa,CACZ,IAAMA,EAAYF,EAAW,CAAC,GAAGJ,EAAS,IAAKC,CAAS,CAAC,EAEzD,GAAIK,IAAcR,GACjB,OAGD,GAAIQ,EACH,OAAOX,GAAK,QAAQM,EAAWK,CAAS,EAGzC,GAAIL,IAAcC,EACjB,OAGDD,EAAYN,GAAK,QAAQM,CAAS,CACnC,CACD,EAEAP,GAAO,QAAQ,OAASG,GAExBH,GAAO,QAAQ,KAAK,OAASG,GAAW,KAExCH,GAAO,QAAQ,KAAOI,KCxFtB,IAAAS,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEAA,GAAO,QAAU,SAAoBC,EAAK,CACzC,OAAKA,EAIEA,aAAe,OAAS,MAAM,QAAQA,CAAG,GAC9CA,EAAI,QAAU,GAAKA,EAAI,kBAAkB,SAJnC,EAKT,ICTA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAIC,GAAO,QAAQ,MAAM,EACrBC,GAAa,KAEbC,GAAU,SAAiBC,EAAMC,EAAY,EAC5C,CAACD,GAAQA,EAAK,cAAgB,UACjCC,EAAaD,GAAQ,CAAC,EACtBA,EAAO,MAAM,MAGd,IAAIE,EAAe,SAASC,EAAaC,EAAS,CACjD,GAAI,CAAC,KACJ,OAAO,IAAID,EAAaC,CAAO,EAGhCA,EAAUA,aAAmB,MAC1BA,EAAQ,QACPA,GAAW,KAAK,QAEpB,MAAM,KAAK,KAAMA,CAAO,EACxB,MAAM,kBAAkB,KAAMF,CAAY,EAE1C,KAAK,KAAOF,EAEZ,OAAO,eAAe,KAAM,UAAW,CACtC,aAAc,GACd,WAAY,GACZ,IAAK,UAAY,CAChB,IAAIK,EAAaD,EAAQ,MAAM,QAAQ,EAEvC,QAASE,KAAOL,EACf,GAAKA,EAAW,eAAeK,CAAG,EAIlC,KAAIC,EAAWN,EAAWK,CAAG,EAEzB,YAAaC,IAChBF,EAAaE,EAAS,QAAQ,KAAKD,CAAG,EAAGD,CAAU,GAAKA,EACnDP,GAAWO,CAAU,IACzBA,EAAa,CAACA,CAAU,IAK3B,OAAOA,EAAW,KAAK;AAAA,CAAI,CAC5B,EACA,IAAK,SAAUG,EAAG,CACjBJ,EAAUI,CACX,CACD,CAAC,EAED,IAAIC,EAAmB,KAEnBC,EAAkB,OAAO,yBAAyB,KAAM,OAAO,EAC/DC,EAAcD,EAAgB,IAC9BE,EAAaF,EAAgB,MACjC,OAAOA,EAAgB,MACvB,OAAOA,EAAgB,SAEvBA,EAAgB,IAAM,SAAUG,EAAU,CACzCJ,EAAmBI,CACpB,EAEAH,EAAgB,IAAM,UAAY,CACjC,IAAII,GAASL,IAAsBE,EAChCA,EAAY,KAAK,IAAI,EACrBC,IAAa,MAAM,SAAS,EAI1BH,IACJK,EAAM,CAAC,EAAI,KAAK,KAAO,KAAO,KAAK,SAGpC,IAAIC,EAAY,EAChB,QAAST,KAAOL,EACf,GAAKA,EAAW,eAAeK,CAAG,EAIlC,KAAIC,EAAWN,EAAWK,CAAG,EAE7B,GAAI,SAAUC,EAAU,CACvB,IAAIS,EAAOT,EAAS,KAAK,KAAKD,CAAG,CAAC,EAC9BU,GACHF,EAAM,OAAOC,IAAa,EAAG,OAASC,CAAI,CAE5C,CAEI,UAAWT,GACdA,EAAS,MAAM,KAAKD,CAAG,EAAGQ,CAAK,EAIjC,OAAOA,EAAM,KAAK;AAAA,CAAI,CACvB,EAEA,OAAO,eAAe,KAAM,QAASJ,CAAe,CACrD,EAEA,OAAI,OAAO,gBACV,OAAO,eAAeR,EAAa,UAAW,MAAM,SAAS,EAC7D,OAAO,eAAeA,EAAc,KAAK,GAEzCL,GAAK,SAASK,EAAc,KAAK,EAG3BA,CACR,EAEAH,GAAQ,OAAS,SAAUkB,EAAKC,EAAK,CACpC,MAAO,CACN,QAAS,SAAUV,EAAGJ,EAAS,CAC9B,OAAAI,EAAIA,GAAKU,EAELV,IACHJ,EAAQ,CAAC,GAAK,IAAMa,EAAI,QAAQ,KAAMT,EAAE,SAAS,CAAC,GAG5CJ,CACR,CACD,CACD,EAEAL,GAAQ,KAAO,SAAUkB,EAAKC,EAAK,CAClC,MAAO,CACN,KAAM,SAAUV,EAAG,CAGlB,OAFAA,EAAIA,GAAKU,EAELV,EACIS,EAAI,QAAQ,KAAMT,EAAE,SAAS,CAAC,EAG/B,IACR,CACD,CACD,EAEAZ,GAAO,QAAUG,KC5IjB,IAAAoB,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAASC,GAAQ,CACrB,IAAMC,EAAID,EAAK,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY,EACtD,MAAO,MAAQC,EAAE,OAAS,EAAI,IAAM,IAAMA,CAC5C,EAEMC,GAAa,CAAC,EAAGC,EAAKC,IAAY,CACtC,GAAI,CAACD,EACH,MAAO,CACL,QAAS,EAAE,QAAU,8BACrB,SAAU,CACZ,EAEF,IAAME,EAAW,EAAE,QAAQ,MAAM,2CAA2C,EACtEC,EAASD,EAAW,CAACA,EAAS,CAAC,EACjC,EAAE,QAAQ,MAAM,4BAA4B,EAAIF,EAAI,OAAS,EAC7D,KAEEI,EAAMF,EAAW,EAAE,QAAQ,QAAQ,sBAAuB,oBAC5D,KAAK,UAAUA,EAAS,CAAC,CAAC,CAC5B,KAAKN,GAAOM,EAAS,CAAC,CAAC,CAAC,GAAG,EACzB,EAAE,QAEN,GAAIC,GAAW,KAA8B,CAC3C,IAAME,EAAQF,GAAUF,EAAU,EAC9BE,EAASF,EAEPK,EAAMH,EAASF,GAAWD,EAAI,OAASA,EAAI,OAC7CG,EAASF,EAEPM,GAASF,IAAU,EAAI,GAAK,OAChCL,EAAI,MAAMK,EAAOC,CAAG,GACnBA,IAAQN,EAAI,OAAS,GAAK,OAI7B,MAAO,CACL,QAASI,EAAM,kBAHJJ,IAAQO,EAAQ,GAAK,OAGK,GAAG,KAAK,UAAUA,CAAK,CAAC,GAC7D,SAAUJ,CACZ,CACF,KACE,OAAO,CACL,QAASC,EAAM,mBAAmBJ,EAAI,MAAM,EAAGC,EAAU,CAAC,CAAC,IAC3D,SAAU,CACZ,CAEJ,EAEMO,GAAN,cAA6B,WAAY,CACvC,YAAaC,EAAIT,EAAKC,EAASS,EAAQ,CACrCT,EAAUA,GAAW,GACrB,IAAMU,EAAWZ,GAAWU,EAAIT,EAAKC,CAAO,EAC5C,MAAMU,EAAS,OAAO,EACtB,OAAO,OAAO,KAAMA,CAAQ,EAC5B,KAAK,KAAO,aACZ,KAAK,YAAcF,EACnB,MAAM,kBAAkB,KAAMC,GAAU,KAAK,WAAW,CAC1D,CACA,IAAI,MAAQ,CAAE,OAAO,KAAK,YAAY,IAAK,CAC3C,IAAI,KAAME,EAAG,CAAC,CACd,IAAK,OAAO,WAAW,GAAK,CAAE,OAAO,KAAK,YAAY,IAAK,CAC7D,EAEMC,GAAU,OAAO,IAAI,QAAQ,EAC7BC,GAAW,OAAO,IAAI,SAAS,EAK/BC,GAAW,iCACXC,GAAU,+BAEVC,GAAY,CAACjB,EAAKkB,EAASjB,IAAY,CAC3C,IAAMkB,EAAYC,GAASpB,CAAG,EAC9BC,EAAUA,GAAW,GACrB,GAAI,CAOF,GAAM,CAAC,CAAEoB,EAAU;AAAA,EAAMC,EAAS,IAAI,EAAIH,EAAU,MAAMH,EAAO,GAC/DG,EAAU,MAAMJ,EAAQ,GACxB,CAAC,CAAE,GAAI,EAAE,EAELQ,EAAS,KAAK,MAAMJ,EAAWD,CAAO,EAC5C,OAAIK,GAAU,OAAOA,GAAW,WAC9BA,EAAOT,EAAQ,EAAIO,EACnBE,EAAOV,EAAO,EAAIS,GAEbC,CACT,OAASC,EAAG,CACV,GAAI,OAAOxB,GAAQ,UAAY,CAAC,OAAO,SAASA,CAAG,EAAG,CACpD,IAAMyB,EAAe,MAAM,QAAQzB,CAAG,GAAKA,EAAI,SAAW,EAC1D,MAAM,OAAO,OAAO,IAAI,UACtB,gBAAgByB,EAAe,iBAAmB,OAAOzB,CAAG,CAAC,EAC/D,EAAG,CACD,KAAM,aACN,YAAawB,CACf,CAAC,CACH,CAEA,MAAM,IAAIhB,GAAegB,EAAGL,EAAWlB,EAASgB,EAAS,CAC3D,CACF,EAKMG,GAAWpB,GAAO,OAAOA,CAAG,EAAE,QAAQ,UAAW,EAAE,EAEzDL,GAAO,QAAUsB,GACjBA,GAAU,eAAiBT,GAE3BS,GAAU,aAAe,CAACjB,EAAKkB,IAAY,CACzC,GAAI,CACF,OAAO,KAAK,MAAME,GAASpB,CAAG,EAAGkB,CAAO,CAC1C,MAAY,CAAC,CACf,ICxHA,IAAAQ,GAAAC,EAAAC,IAAA,cACAA,GAAQ,WAAa,GACrBA,GAAQ,gBAAkB,OAC1B,IAAIC,GAAK;AAAA,EACLC,GAAK,KACLC,GAAiC,UAAY,CAC7C,SAASA,EAAgBC,EAAQ,CAC7B,KAAK,OAASA,EAEd,QADIC,EAAU,CAAC,CAAC,EACPC,EAAS,EAAGA,EAASF,EAAO,QACjC,OAAQA,EAAOE,CAAM,EAAG,CACpB,KAAKL,GACDK,GAAUL,GAAG,OACbI,EAAQ,KAAKC,CAAM,EACnB,MACJ,KAAKJ,GACDI,GAAUJ,GAAG,OACTE,EAAOE,CAAM,IAAML,KACnBK,GAAUL,GAAG,QAEjBI,EAAQ,KAAKC,CAAM,EACnB,MACJ,QACIA,IACA,KACR,CAEJ,KAAK,QAAUD,CACnB,CACA,OAAAF,EAAgB,UAAU,iBAAmB,SAAUI,EAAO,CAC1D,GAAIA,EAAQ,GAAKA,EAAQ,KAAK,OAAO,OACjC,OAAO,KAIX,QAFIC,EAAO,EACPH,EAAU,KAAK,QACZA,EAAQG,EAAO,CAAC,GAAKD,GACxBC,IAEJ,IAAIC,EAASF,EAAQF,EAAQG,CAAI,EACjC,MAAO,CAAE,KAAMA,EAAM,OAAQC,CAAO,CACxC,EACAN,EAAgB,UAAU,iBAAmB,SAAUO,EAAU,CAC7D,IAAIF,EAAOE,EAAS,KAAMD,EAASC,EAAS,OAI5C,OAHIF,EAAO,GAAKA,GAAQ,KAAK,QAAQ,QAGjCC,EAAS,GAAKA,EAAS,KAAK,aAAaD,CAAI,EACtC,KAEJ,KAAK,QAAQA,CAAI,EAAIC,CAChC,EACAN,EAAgB,UAAU,aAAe,SAAUK,EAAM,CACrD,IAAIF,EAAS,KAAK,QAAQE,CAAI,EAC1BG,EAAaH,IAAS,KAAK,QAAQ,OAAS,EAC1C,KAAK,OAAO,OACZ,KAAK,QAAQA,EAAO,CAAC,EAC3B,OAAOG,EAAaL,CACxB,EACOH,CACX,EAAE,EACFH,GAAQ,gBAAkBG,GAC1BH,GAAQ,QAAaG,KC7DrB,IAAAS,GAAAC,EAAAC,IAAA,CAGA,OAAO,eAAeA,GAAS,aAAc,CAC3C,MAAO,EACT,CAAC,EAIDA,GAAQ,QAAU,umBAElBA,GAAQ,aAAe,SAASC,EAAO,CACrC,IAAIC,EAAQ,CAAC,KAAM,UAAW,MAAOD,EAAM,CAAC,EAAG,OAAQ,MAAS,EAC3D,OAAIA,EAAO,CAAC,GAAGC,EAAM,KAAO,SAAWA,EAAM,OAAS,CAAC,EAAED,EAAM,CAAC,GAAKA,EAAM,CAAC,IACxEA,EAAO,CAAC,EAAGC,EAAM,KAAO,UACxBD,EAAO,CAAC,GAAGC,EAAM,KAAO,UAAWA,EAAM,OAAS,CAAC,CAACD,EAAM,CAAC,GAC3DA,EAAO,CAAC,EAAGC,EAAM,KAAO,QACxBD,EAAO,CAAC,EAAGC,EAAM,KAAO,SACxBD,EAAM,EAAE,EAAGC,EAAM,KAAO,OACxBD,EAAM,EAAE,EAAGC,EAAM,KAAO,aACxBD,EAAM,EAAE,IAAGC,EAAM,KAAO,cAC1BA,CACT,uJCTA,IAAIC,GAA+B,+qIAE/BC,GAA0B,glFAExBC,GAA0B,IAAIC,OAClC,IAAMH,GAA+B,GACvC,EACMI,GAAqB,IAAID,OAC7B,IAAMH,GAA+BC,GAA0B,GACjE,EAEAD,GAA+BC,GAA0B,KAQzD,IAAMI,GAA6B,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,IAAI,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,GAAG,IAAI,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,IAAI,GAAG,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,KAAK,GAAG,KAAK,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,KAAK,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,KAAK,GAAG,EAAE,EAAE,IAAI,GAAG,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,GAAG,GAAG,EAAE,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,MAAM,GAAG,KAAK,EAAE,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,KAAK,EAAE,IAAI,EAE1jDC,GAAwB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,MAAM,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,GAAG,EAAE,GAAG,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,GAAG,EAK90B,SAASC,GAAcC,EAAcC,EAAiC,CACpE,IAAIC,EAAM,MACV,QAASC,EAAI,EAAGC,EAASH,EAAIG,OAAQD,EAAIC,EAAQD,GAAK,EAAG,CAEvD,GADAD,GAAOD,EAAIE,CAAC,EACRD,EAAMF,EAAM,MAAO,GAGvB,GADAE,GAAOD,EAAIE,EAAI,CAAC,EACZD,GAAOF,EAAM,MAAO,EAC1B,CACA,MAAO,EACT,CAIO,SAASK,GAAkBL,EAAuB,CACvD,OAAIA,EAAI,GAAgCA,IAAI,GACxCA,GAAI,GAAiC,GACrCA,EAAI,GAAgCA,IAAI,GACxCA,GAAI,IAAiC,GACrCA,GAAQ,MAERA,GAAQ,KAAQN,GAAwBY,KAAKC,OAAOC,aAAaR,CAAI,CAAC,EAGnED,GAAcC,EAAMH,EAA0B,CACvD,CAIO,SAASY,GAAiBT,EAAuB,CACtD,OAAIA,EAAI,GAA4BA,IAAI,GACpCA,EAAI,GAA2B,GAC/BA,EAAI,GAAgC,GACpCA,GAAI,GAAiC,GACrCA,EAAI,GAAgCA,IAAI,GACxCA,GAAI,IAAiC,GACrCA,GAAQ,MACHA,GAAQ,KAAQJ,GAAmBU,KAAKC,OAAOC,aAAaR,CAAI,CAAC,EAGxED,GAAcC,EAAMH,EAA0B,GAC9CE,GAAcC,EAAMF,EAAqB,CAE7C,CAIO,SAASY,GAAiBC,EAAuB,CACtD,IAAIC,EAAU,GACd,QAAST,EAAI,EAAGA,EAAIQ,EAAKP,OAAQD,IAAK,CAKpC,IAAIU,EAAKF,EAAKG,WAAWX,CAAC,EAC1B,IAAKU,EAAK,SAAY,OAAUV,EAAI,EAAIQ,EAAKP,OAAQ,CACnD,IAAMW,EAAQJ,EAAKG,WAAW,EAAEX,CAAC,GAC5BY,EAAQ,SAAY,QACvBF,EAAK,QAAYA,EAAK,OAAU,KAAOE,EAAQ,MAEnD,CACA,GAAIH,GAEF,GADAA,EAAU,GACN,CAACP,GAAkBQ,CAAE,EACvB,MAAO,WAEA,CAACJ,GAAiBI,CAAE,EAC7B,MAAO,EAEX,CACA,MAAO,CAACD,CACV,mNC9GA,IAAMI,GAAgB,CACpBC,QAAS,CACP,QACA,OACA,QACA,WACA,WACA,UACA,KACA,OACA,UACA,MACA,WACA,KACA,SACA,SACA,QACA,MACA,MACA,QACA,QACA,OACA,MACA,OACA,QACA,QACA,UACA,SACA,SACA,OACA,OACA,QACA,KACA,aACA,SACA,OACA,QAAQ,EAEVC,OAAQ,CACN,aACA,YACA,MACA,UACA,UACA,YACA,SACA,SACA,OAAO,EAETC,WAAY,CAAC,OAAQ,WAAW,CAClC,EACMC,GAAW,IAAIC,IAAIL,GAAcC,OAAO,EACxCK,GAAyB,IAAID,IAAIL,GAAcE,MAAM,EACrDK,GAA6B,IAAIF,IAAIL,GAAcG,UAAU,EAK5D,SAASK,GAAeC,EAAcC,EAA4B,CACvE,OAAQA,GAAYD,IAAS,SAAYA,IAAS,MACpD,CAOO,SAASE,GAAqBF,EAAcC,EAA4B,CAC7E,OAAOF,GAAeC,EAAMC,CAAQ,GAAKJ,GAAuBM,IAAIH,CAAI,CAC1E,CAMO,SAASI,GAA6BJ,EAAuB,CAClE,OAAOF,GAA2BK,IAAIH,CAAI,CAC5C,CAOO,SAASK,GACdL,EACAC,EACS,CACT,OACEC,GAAqBF,EAAMC,CAAQ,GAAKG,GAA6BJ,CAAI,CAE7E,CAEO,SAASM,GAAUN,EAAuB,CAC/C,OAAOL,GAASQ,IAAIH,CAAI,CAC1B,u6BC/FA,IAAAO,GAAAC,KAKAC,GAAAD,OCLA,IAAAE,GAAAC,EAAA,CAAAC,GAAAC,KAAA,KAAIC,GAAI,SAAW,CAAC,EAAGC,GAAOD,GAAE,MAAQ,CAAC,EAAGE,GAAMF,GAAE,KAAO,CAAC,EACxDG,GACH,EAAID,GAAI,UAAYD,GAAK,SAAS,YAAY,KAC7C,CAAC,CAACC,GAAI,aAAeD,GAAK,SAAS,SAAS,GAAKD,GAAE,WAAa,UAAaA,GAAE,QAAU,CAAC,GAAG,OAASE,GAAI,OAAS,QAAW,CAAC,CAACA,GAAI,IAElIE,GAAY,CAACC,EAAMC,EAAOC,EAAUF,IACvCG,GAAS,CACR,IAAIC,EAAS,GAAKD,EAAOE,EAAQD,EAAO,QAAQH,EAAOD,EAAK,MAAM,EAClE,MAAO,CAACK,EAAQL,EAAOM,GAAaF,EAAQH,EAAOC,EAASG,CAAK,EAAIJ,EAAQD,EAAOI,EAASH,CAC9F,EAEGK,GAAe,CAACF,EAAQH,EAAOC,EAASG,IAAU,CACrD,IAAIE,EAAS,GAAIC,EAAS,EAC1B,GACCD,GAAUH,EAAO,UAAUI,EAAQH,CAAK,EAAIH,EAC5CM,EAASH,EAAQJ,EAAM,OACvBI,EAAQD,EAAO,QAAQH,EAAOO,CAAM,QAC5B,CAACH,GACV,OAAOE,EAASH,EAAO,UAAUI,CAAM,CACxC,EAEIC,GAAe,CAACC,EAAUZ,KAAqB,CAClD,IAAIa,EAAID,EAAUX,GAAY,IAAM,OACpC,MAAO,CACN,iBAAkBW,EAClB,MAAOC,EAAE,UAAW,SAAS,EAC7B,KAAMA,EAAE,UAAW,WAAY,iBAAiB,EAChD,IAAKA,EAAE,UAAW,WAAY,iBAAiB,EAC/C,OAAQA,EAAE,UAAW,UAAU,EAC/B,UAAWA,EAAE,UAAW,UAAU,EAClC,QAASA,EAAE,UAAW,UAAU,EAChC,OAAQA,EAAE,UAAW,UAAU,EAC/B,cAAeA,EAAE,UAAW,UAAU,EAEtC,MAAOA,EAAE,WAAY,UAAU,EAC/B,IAAKA,EAAE,WAAY,UAAU,EAC7B,MAAOA,EAAE,WAAY,UAAU,EAC/B,OAAQA,EAAE,WAAY,UAAU,EAChC,KAAMA,EAAE,WAAY,UAAU,EAC9B,QAASA,EAAE,WAAY,UAAU,EACjC,KAAMA,EAAE,WAAY,UAAU,EAC9B,MAAOA,EAAE,WAAY,UAAU,EAC/B,KAAMA,EAAE,WAAY,UAAU,EAE9B,QAASA,EAAE,WAAY,UAAU,EACjC,MAAOA,EAAE,WAAY,UAAU,EAC/B,QAASA,EAAE,WAAY,UAAU,EACjC,SAAUA,EAAE,WAAY,UAAU,EAClC,OAAQA,EAAE,WAAY,UAAU,EAChC,UAAWA,EAAE,WAAY,UAAU,EACnC,OAAQA,EAAE,WAAY,UAAU,EAChC,QAASA,EAAE,WAAY,UAAU,EAEjC,YAAaA,EAAE,WAAY,UAAU,EACrC,UAAWA,EAAE,WAAY,UAAU,EACnC,YAAaA,EAAE,WAAY,UAAU,EACrC,aAAcA,EAAE,WAAY,UAAU,EACtC,WAAYA,EAAE,WAAY,UAAU,EACpC,cAAeA,EAAE,WAAY,UAAU,EACvC,WAAYA,EAAE,WAAY,UAAU,EACpC,YAAaA,EAAE,WAAY,UAAU,EAErC,cAAeA,EAAE,YAAa,UAAU,EACxC,YAAaA,EAAE,YAAa,UAAU,EACtC,cAAeA,EAAE,YAAa,UAAU,EACxC,eAAgBA,EAAE,YAAa,UAAU,EACzC,aAAcA,EAAE,YAAa,UAAU,EACvC,gBAAiBA,EAAE,YAAa,UAAU,EAC1C,aAAcA,EAAE,YAAa,UAAU,EACvC,cAAeA,EAAE,YAAa,UAAU,CACzC,CACD,EAEAjB,GAAO,QAAUe,GAAa,EAC9Bf,GAAO,QAAQ,aAAee,KC1E9B,IAAAG,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAIC,GAAmB,sBAEvBD,GAAO,QAAU,SAAUE,EAAK,CAC/B,GAAI,OAAOA,GAAQ,SAClB,MAAM,IAAI,UAAU,mBAAmB,EAGxC,OAAOA,EAAI,QAAQD,GAAkB,MAAM,CAC5C,ICVA,IAAAE,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEAA,GAAO,QAAU,CAChB,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,aAAgB,CAAC,IAAK,IAAK,GAAG,EAC9B,KAAQ,CAAC,EAAG,IAAK,GAAG,EACpB,WAAc,CAAC,IAAK,IAAK,GAAG,EAC5B,MAAS,CAAC,IAAK,IAAK,GAAG,EACvB,MAAS,CAAC,IAAK,IAAK,GAAG,EACvB,OAAU,CAAC,IAAK,IAAK,GAAG,EACxB,MAAS,CAAC,EAAG,EAAG,CAAC,EACjB,eAAkB,CAAC,IAAK,IAAK,GAAG,EAChC,KAAQ,CAAC,EAAG,EAAG,GAAG,EAClB,WAAc,CAAC,IAAK,GAAI,GAAG,EAC3B,MAAS,CAAC,IAAK,GAAI,EAAE,EACrB,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,UAAa,CAAC,GAAI,IAAK,GAAG,EAC1B,WAAc,CAAC,IAAK,IAAK,CAAC,EAC1B,UAAa,CAAC,IAAK,IAAK,EAAE,EAC1B,MAAS,CAAC,IAAK,IAAK,EAAE,EACtB,eAAkB,CAAC,IAAK,IAAK,GAAG,EAChC,SAAY,CAAC,IAAK,IAAK,GAAG,EAC1B,QAAW,CAAC,IAAK,GAAI,EAAE,EACvB,KAAQ,CAAC,EAAG,IAAK,GAAG,EACpB,SAAY,CAAC,EAAG,EAAG,GAAG,EACtB,SAAY,CAAC,EAAG,IAAK,GAAG,EACxB,cAAiB,CAAC,IAAK,IAAK,EAAE,EAC9B,SAAY,CAAC,IAAK,IAAK,GAAG,EAC1B,UAAa,CAAC,EAAG,IAAK,CAAC,EACvB,SAAY,CAAC,IAAK,IAAK,GAAG,EAC1B,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,YAAe,CAAC,IAAK,EAAG,GAAG,EAC3B,eAAkB,CAAC,GAAI,IAAK,EAAE,EAC9B,WAAc,CAAC,IAAK,IAAK,CAAC,EAC1B,WAAc,CAAC,IAAK,GAAI,GAAG,EAC3B,QAAW,CAAC,IAAK,EAAG,CAAC,EACrB,WAAc,CAAC,IAAK,IAAK,GAAG,EAC5B,aAAgB,CAAC,IAAK,IAAK,GAAG,EAC9B,cAAiB,CAAC,GAAI,GAAI,GAAG,EAC7B,cAAiB,CAAC,GAAI,GAAI,EAAE,EAC5B,cAAiB,CAAC,GAAI,GAAI,EAAE,EAC5B,cAAiB,CAAC,EAAG,IAAK,GAAG,EAC7B,WAAc,CAAC,IAAK,EAAG,GAAG,EAC1B,SAAY,CAAC,IAAK,GAAI,GAAG,EACzB,YAAe,CAAC,EAAG,IAAK,GAAG,EAC3B,QAAW,CAAC,IAAK,IAAK,GAAG,EACzB,QAAW,CAAC,IAAK,IAAK,GAAG,EACzB,WAAc,CAAC,GAAI,IAAK,GAAG,EAC3B,UAAa,CAAC,IAAK,GAAI,EAAE,EACzB,YAAe,CAAC,IAAK,IAAK,GAAG,EAC7B,YAAe,CAAC,GAAI,IAAK,EAAE,EAC3B,QAAW,CAAC,IAAK,EAAG,GAAG,EACvB,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,WAAc,CAAC,IAAK,IAAK,GAAG,EAC5B,KAAQ,CAAC,IAAK,IAAK,CAAC,EACpB,UAAa,CAAC,IAAK,IAAK,EAAE,EAC1B,KAAQ,CAAC,IAAK,IAAK,GAAG,EACtB,MAAS,CAAC,EAAG,IAAK,CAAC,EACnB,YAAe,CAAC,IAAK,IAAK,EAAE,EAC5B,KAAQ,CAAC,IAAK,IAAK,GAAG,EACtB,SAAY,CAAC,IAAK,IAAK,GAAG,EAC1B,QAAW,CAAC,IAAK,IAAK,GAAG,EACzB,UAAa,CAAC,IAAK,GAAI,EAAE,EACzB,OAAU,CAAC,GAAI,EAAG,GAAG,EACrB,MAAS,CAAC,IAAK,IAAK,GAAG,EACvB,MAAS,CAAC,IAAK,IAAK,GAAG,EACvB,SAAY,CAAC,IAAK,IAAK,GAAG,EAC1B,cAAiB,CAAC,IAAK,IAAK,GAAG,EAC/B,UAAa,CAAC,IAAK,IAAK,CAAC,EACzB,aAAgB,CAAC,IAAK,IAAK,GAAG,EAC9B,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,WAAc,CAAC,IAAK,IAAK,GAAG,EAC5B,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,qBAAwB,CAAC,IAAK,IAAK,GAAG,EACtC,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,WAAc,CAAC,IAAK,IAAK,GAAG,EAC5B,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,YAAe,CAAC,IAAK,IAAK,GAAG,EAC7B,cAAiB,CAAC,GAAI,IAAK,GAAG,EAC9B,aAAgB,CAAC,IAAK,IAAK,GAAG,EAC9B,eAAkB,CAAC,IAAK,IAAK,GAAG,EAChC,eAAkB,CAAC,IAAK,IAAK,GAAG,EAChC,eAAkB,CAAC,IAAK,IAAK,GAAG,EAChC,YAAe,CAAC,IAAK,IAAK,GAAG,EAC7B,KAAQ,CAAC,EAAG,IAAK,CAAC,EAClB,UAAa,CAAC,GAAI,IAAK,EAAE,EACzB,MAAS,CAAC,IAAK,IAAK,GAAG,EACvB,QAAW,CAAC,IAAK,EAAG,GAAG,EACvB,OAAU,CAAC,IAAK,EAAG,CAAC,EACpB,iBAAoB,CAAC,IAAK,IAAK,GAAG,EAClC,WAAc,CAAC,EAAG,EAAG,GAAG,EACxB,aAAgB,CAAC,IAAK,GAAI,GAAG,EAC7B,aAAgB,CAAC,IAAK,IAAK,GAAG,EAC9B,eAAkB,CAAC,GAAI,IAAK,GAAG,EAC/B,gBAAmB,CAAC,IAAK,IAAK,GAAG,EACjC,kBAAqB,CAAC,EAAG,IAAK,GAAG,EACjC,gBAAmB,CAAC,GAAI,IAAK,GAAG,EAChC,gBAAmB,CAAC,IAAK,GAAI,GAAG,EAChC,aAAgB,CAAC,GAAI,GAAI,GAAG,EAC5B,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,SAAY,CAAC,IAAK,IAAK,GAAG,EAC1B,YAAe,CAAC,IAAK,IAAK,GAAG,EAC7B,KAAQ,CAAC,EAAG,EAAG,GAAG,EAClB,QAAW,CAAC,IAAK,IAAK,GAAG,EACzB,MAAS,CAAC,IAAK,IAAK,CAAC,EACrB,UAAa,CAAC,IAAK,IAAK,EAAE,EAC1B,OAAU,CAAC,IAAK,IAAK,CAAC,EACtB,UAAa,CAAC,IAAK,GAAI,CAAC,EACxB,OAAU,CAAC,IAAK,IAAK,GAAG,EACxB,cAAiB,CAAC,IAAK,IAAK,GAAG,EAC/B,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,cAAiB,CAAC,IAAK,IAAK,GAAG,EAC/B,cAAiB,CAAC,IAAK,IAAK,GAAG,EAC/B,WAAc,CAAC,IAAK,IAAK,GAAG,EAC5B,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,KAAQ,CAAC,IAAK,IAAK,EAAE,EACrB,KAAQ,CAAC,IAAK,IAAK,GAAG,EACtB,KAAQ,CAAC,IAAK,IAAK,GAAG,EACtB,WAAc,CAAC,IAAK,IAAK,GAAG,EAC5B,OAAU,CAAC,IAAK,EAAG,GAAG,EACtB,cAAiB,CAAC,IAAK,GAAI,GAAG,EAC9B,IAAO,CAAC,IAAK,EAAG,CAAC,EACjB,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,UAAa,CAAC,GAAI,IAAK,GAAG,EAC1B,YAAe,CAAC,IAAK,GAAI,EAAE,EAC3B,OAAU,CAAC,IAAK,IAAK,GAAG,EACxB,WAAc,CAAC,IAAK,IAAK,EAAE,EAC3B,SAAY,CAAC,GAAI,IAAK,EAAE,EACxB,SAAY,CAAC,IAAK,IAAK,GAAG,EAC1B,OAAU,CAAC,IAAK,GAAI,EAAE,EACtB,OAAU,CAAC,IAAK,IAAK,GAAG,EACxB,QAAW,CAAC,IAAK,IAAK,GAAG,EACzB,UAAa,CAAC,IAAK,GAAI,GAAG,EAC1B,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,UAAa,CAAC,IAAK,IAAK,GAAG,EAC3B,KAAQ,CAAC,IAAK,IAAK,GAAG,EACtB,YAAe,CAAC,EAAG,IAAK,GAAG,EAC3B,UAAa,CAAC,GAAI,IAAK,GAAG,EAC1B,IAAO,CAAC,IAAK,IAAK,GAAG,EACrB,KAAQ,CAAC,EAAG,IAAK,GAAG,EACpB,QAAW,CAAC,IAAK,IAAK,GAAG,EACzB,OAAU,CAAC,IAAK,GAAI,EAAE,EACtB,UAAa,CAAC,GAAI,IAAK,GAAG,EAC1B,OAAU,CAAC,IAAK,IAAK,GAAG,EACxB,MAAS,CAAC,IAAK,IAAK,GAAG,EACvB,MAAS,CAAC,IAAK,IAAK,GAAG,EACvB,WAAc,CAAC,IAAK,IAAK,GAAG,EAC5B,OAAU,CAAC,IAAK,IAAK,CAAC,EACtB,YAAe,CAAC,IAAK,IAAK,EAAE,CAC7B,ICvJA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CACA,IAAIC,GAAc,KAMdC,GAAkB,CAAC,EACvB,IAASC,MAAOF,GACXA,GAAY,eAAeE,EAAG,IACjCD,GAAgBD,GAAYE,EAAG,CAAC,EAAIA,IAF7B,IAAAA,GAMLC,EAAUJ,GAAO,QAAU,CAC9B,IAAK,CAAC,SAAU,EAAG,OAAQ,KAAK,EAChC,IAAK,CAAC,SAAU,EAAG,OAAQ,KAAK,EAChC,IAAK,CAAC,SAAU,EAAG,OAAQ,KAAK,EAChC,IAAK,CAAC,SAAU,EAAG,OAAQ,KAAK,EAChC,KAAM,CAAC,SAAU,EAAG,OAAQ,MAAM,EAClC,IAAK,CAAC,SAAU,EAAG,OAAQ,KAAK,EAChC,IAAK,CAAC,SAAU,EAAG,OAAQ,KAAK,EAChC,IAAK,CAAC,SAAU,EAAG,OAAQ,KAAK,EAChC,IAAK,CAAC,SAAU,EAAG,OAAQ,CAAC,KAAK,CAAC,EAClC,QAAS,CAAC,SAAU,EAAG,OAAQ,CAAC,SAAS,CAAC,EAC1C,OAAQ,CAAC,SAAU,EAAG,OAAQ,CAAC,QAAQ,CAAC,EACxC,QAAS,CAAC,SAAU,EAAG,OAAQ,CAAC,SAAS,CAAC,EAC1C,IAAK,CAAC,SAAU,EAAG,OAAQ,CAAC,IAAK,IAAK,GAAG,CAAC,EAC1C,MAAO,CAAC,SAAU,EAAG,OAAQ,CAAC,MAAO,MAAO,KAAK,CAAC,EAClD,KAAM,CAAC,SAAU,EAAG,OAAQ,CAAC,MAAM,CAAC,CACrC,EAGA,IAASK,KAASD,EACjB,GAAIA,EAAQ,eAAeC,CAAK,EAAG,CAClC,GAAI,EAAE,aAAcD,EAAQC,CAAK,GAChC,MAAM,IAAI,MAAM,8BAAgCA,CAAK,EAGtD,GAAI,EAAE,WAAYD,EAAQC,CAAK,GAC9B,MAAM,IAAI,MAAM,oCAAsCA,CAAK,EAG5D,GAAID,EAAQC,CAAK,EAAE,OAAO,SAAWD,EAAQC,CAAK,EAAE,SACnD,MAAM,IAAI,MAAM,sCAAwCA,CAAK,EAG1DC,GAAWF,EAAQC,CAAK,EAAE,SAC1BE,GAASH,EAAQC,CAAK,EAAE,OAC5B,OAAOD,EAAQC,CAAK,EAAE,SACtB,OAAOD,EAAQC,CAAK,EAAE,OACtB,OAAO,eAAeD,EAAQC,CAAK,EAAG,WAAY,CAAC,MAAOC,EAAQ,CAAC,EACnE,OAAO,eAAeF,EAAQC,CAAK,EAAG,SAAU,CAAC,MAAOE,EAAM,CAAC,CAChE,CANK,IAAAD,GACAC,GAfGF,EAuBTD,EAAQ,IAAI,IAAM,SAAUI,EAAK,CAChC,IAAI,EAAIA,EAAI,CAAC,EAAI,IACbC,EAAID,EAAI,CAAC,EAAI,IACbE,EAAIF,EAAI,CAAC,EAAI,IACbG,EAAM,KAAK,IAAI,EAAGF,EAAGC,CAAC,EACtBE,EAAM,KAAK,IAAI,EAAGH,EAAGC,CAAC,EACtBG,EAAQD,EAAMD,EACdG,EACAC,EACAC,EAEJ,OAAIJ,IAAQD,EACXG,EAAI,EACM,IAAMF,EAChBE,GAAKL,EAAIC,GAAKG,EACJJ,IAAMG,EAChBE,EAAI,GAAKJ,EAAI,GAAKG,EACRH,IAAME,IAChBE,EAAI,GAAK,EAAIL,GAAKI,GAGnBC,EAAI,KAAK,IAAIA,EAAI,GAAI,GAAG,EAEpBA,EAAI,IACPA,GAAK,KAGNE,GAAKL,EAAMC,GAAO,EAEdA,IAAQD,EACXI,EAAI,EACMC,GAAK,GACfD,EAAIF,GAASD,EAAMD,GAEnBI,EAAIF,GAAS,EAAID,EAAMD,GAGjB,CAACG,EAAGC,EAAI,IAAKC,EAAI,GAAG,CAC5B,EAEAZ,EAAQ,IAAI,IAAM,SAAUI,EAAK,CAChC,IAAIS,EACAC,EACAC,EACAL,EACAC,EAEAK,EAAIZ,EAAI,CAAC,EAAI,IACbC,EAAID,EAAI,CAAC,EAAI,IACbE,EAAIF,EAAI,CAAC,EAAI,IACba,EAAI,KAAK,IAAID,EAAGX,EAAGC,CAAC,EACpBY,EAAOD,EAAI,KAAK,IAAID,EAAGX,EAAGC,CAAC,EAC3Ba,EAAQ,SAAUC,EAAG,CACxB,OAAQH,EAAIG,GAAK,EAAIF,EAAO,EAAI,CACjC,EAEA,OAAIA,IAAS,EACZR,EAAIC,EAAI,GAERA,EAAIO,EAAOD,EACXJ,EAAOM,EAAMH,CAAC,EACdF,EAAOK,EAAMd,CAAC,EACdU,EAAOI,EAAMb,CAAC,EAEVU,IAAMC,EACTP,EAAIK,EAAOD,EACDT,IAAMY,EAChBP,EAAK,EAAI,EAAKG,EAAOE,EACXT,IAAMW,IAChBP,EAAK,EAAI,EAAKI,EAAOD,GAElBH,EAAI,EACPA,GAAK,EACKA,EAAI,IACdA,GAAK,IAIA,CACNA,EAAI,IACJC,EAAI,IACJM,EAAI,GACL,CACD,EAEAjB,EAAQ,IAAI,IAAM,SAAUI,EAAK,CAChC,IAAI,EAAIA,EAAI,CAAC,EACTC,EAAID,EAAI,CAAC,EACTE,EAAIF,EAAI,CAAC,EACTM,EAAIV,EAAQ,IAAI,IAAII,CAAG,EAAE,CAAC,EAC1BiB,EAAI,EAAI,IAAM,KAAK,IAAI,EAAG,KAAK,IAAIhB,EAAGC,CAAC,CAAC,EAE5C,OAAAA,EAAI,EAAI,EAAI,IAAM,KAAK,IAAI,EAAG,KAAK,IAAID,EAAGC,CAAC,CAAC,EAErC,CAACI,EAAGW,EAAI,IAAKf,EAAI,GAAG,CAC5B,EAEAN,EAAQ,IAAI,KAAO,SAAUI,EAAK,CACjC,IAAI,EAAIA,EAAI,CAAC,EAAI,IACbC,EAAID,EAAI,CAAC,EAAI,IACbE,EAAIF,EAAI,CAAC,EAAI,IACbgB,EACAE,EACAC,EACAC,EAEJ,OAAAA,EAAI,KAAK,IAAI,EAAI,EAAG,EAAInB,EAAG,EAAIC,CAAC,EAChCc,GAAK,EAAI,EAAII,IAAM,EAAIA,IAAM,EAC7BF,GAAK,EAAIjB,EAAImB,IAAM,EAAIA,IAAM,EAC7BD,GAAK,EAAIjB,EAAIkB,IAAM,EAAIA,IAAM,EAEtB,CAACJ,EAAI,IAAKE,EAAI,IAAKC,EAAI,IAAKC,EAAI,GAAG,CAC3C,EAKA,SAASC,GAAoBC,EAAGH,EAAG,CAClC,OACC,KAAK,IAAIG,EAAE,CAAC,EAAIH,EAAE,CAAC,EAAG,CAAC,EACvB,KAAK,IAAIG,EAAE,CAAC,EAAIH,EAAE,CAAC,EAAG,CAAC,EACvB,KAAK,IAAIG,EAAE,CAAC,EAAIH,EAAE,CAAC,EAAG,CAAC,CAEzB,CAEAvB,EAAQ,IAAI,QAAU,SAAUI,EAAK,CACpC,IAAIuB,EAAW7B,GAAgBM,CAAG,EAClC,GAAIuB,EACH,OAAOA,EAGR,IAAIC,EAAyB,IACzBC,EAEJ,QAASC,KAAWjC,GACnB,GAAIA,GAAY,eAAeiC,CAAO,EAAG,CACxC,IAAIC,EAAQlC,GAAYiC,CAAO,EAG3BE,EAAWP,GAAoBrB,EAAK2B,CAAK,EAGzCC,EAAWJ,IACdA,EAAyBI,EACzBH,EAAwBC,EAE1B,CAGD,OAAOD,CACR,EAEA7B,EAAQ,QAAQ,IAAM,SAAU8B,EAAS,CACxC,OAAOjC,GAAYiC,CAAO,CAC3B,EAEA9B,EAAQ,IAAI,IAAM,SAAUI,EAAK,CAChC,IAAI,EAAIA,EAAI,CAAC,EAAI,IACbC,EAAID,EAAI,CAAC,EAAI,IACbE,EAAIF,EAAI,CAAC,EAAI,IAGjB,EAAI,EAAI,OAAU,KAAK,KAAM,EAAI,MAAS,MAAQ,GAAG,EAAK,EAAI,MAC9DC,EAAIA,EAAI,OAAU,KAAK,KAAMA,EAAI,MAAS,MAAQ,GAAG,EAAKA,EAAI,MAC9DC,EAAIA,EAAI,OAAU,KAAK,KAAMA,EAAI,MAAS,MAAQ,GAAG,EAAKA,EAAI,MAE9D,IAAIoB,EAAK,EAAI,MAAWrB,EAAI,MAAWC,EAAI,MACvCiB,EAAK,EAAI,MAAWlB,EAAI,MAAWC,EAAI,MACvC2B,EAAK,EAAI,MAAW5B,EAAI,MAAWC,EAAI,MAE3C,MAAO,CAACoB,EAAI,IAAKH,EAAI,IAAKU,EAAI,GAAG,CAClC,EAEAjC,EAAQ,IAAI,IAAM,SAAUI,EAAK,CAChC,IAAI8B,EAAMlC,EAAQ,IAAI,IAAII,CAAG,EACzBsB,EAAIQ,EAAI,CAAC,EACTX,EAAIW,EAAI,CAAC,EACTD,EAAIC,EAAI,CAAC,EACTtB,EACAuB,EACA7B,EAEJ,OAAAoB,GAAK,OACLH,GAAK,IACLU,GAAK,QAELP,EAAIA,EAAI,QAAW,KAAK,IAAIA,EAAG,EAAI,CAAC,EAAK,MAAQA,EAAM,GAAK,IAC5DH,EAAIA,EAAI,QAAW,KAAK,IAAIA,EAAG,EAAI,CAAC,EAAK,MAAQA,EAAM,GAAK,IAC5DU,EAAIA,EAAI,QAAW,KAAK,IAAIA,EAAG,EAAI,CAAC,EAAK,MAAQA,EAAM,GAAK,IAE5DrB,EAAK,IAAMW,EAAK,GAChBY,EAAI,KAAOT,EAAIH,GACfjB,EAAI,KAAOiB,EAAIU,GAER,CAACrB,EAAGuB,EAAG7B,CAAC,CAChB,EAEAN,EAAQ,IAAI,IAAM,SAAUoC,EAAK,CAChC,IAAI1B,EAAI0B,EAAI,CAAC,EAAI,IACbzB,EAAIyB,EAAI,CAAC,EAAI,IACbxB,EAAIwB,EAAI,CAAC,EAAI,IACbC,EACAC,EACAC,EACAnC,EACAoC,EAEJ,GAAI7B,IAAM,EACT,OAAA6B,EAAM5B,EAAI,IACH,CAAC4B,EAAKA,EAAKA,CAAG,EAGlB5B,EAAI,GACP0B,EAAK1B,GAAK,EAAID,GAEd2B,EAAK1B,EAAID,EAAIC,EAAID,EAGlB0B,EAAK,EAAIzB,EAAI0B,EAEblC,EAAM,CAAC,EAAG,EAAG,CAAC,EACd,QAASqC,EAAI,EAAGA,EAAI,EAAGA,IACtBF,EAAK7B,EAAI,EAAI,EAAI,EAAE+B,EAAI,GACnBF,EAAK,GACRA,IAEGA,EAAK,GACRA,IAGG,EAAIA,EAAK,EACZC,EAAMH,GAAMC,EAAKD,GAAM,EAAIE,EACjB,EAAIA,EAAK,EACnBC,EAAMF,EACI,EAAIC,EAAK,EACnBC,EAAMH,GAAMC,EAAKD,IAAO,EAAI,EAAIE,GAAM,EAEtCC,EAAMH,EAGPjC,EAAIqC,CAAC,EAAID,EAAM,IAGhB,OAAOpC,CACR,EAEAJ,EAAQ,IAAI,IAAM,SAAUoC,EAAK,CAChC,IAAI1B,EAAI0B,EAAI,CAAC,EACTzB,EAAIyB,EAAI,CAAC,EAAI,IACbxB,EAAIwB,EAAI,CAAC,EAAI,IACbM,EAAO/B,EACPgC,EAAO,KAAK,IAAI/B,EAAG,GAAI,EACvBgC,EACA3B,EAEJ,OAAAL,GAAK,EACLD,GAAMC,GAAK,EAAKA,EAAI,EAAIA,EACxB8B,GAAQC,GAAQ,EAAIA,EAAO,EAAIA,EAC/B1B,GAAKL,EAAID,GAAK,EACdiC,EAAKhC,IAAM,EAAK,EAAI8B,GAASC,EAAOD,GAAS,EAAI/B,GAAMC,EAAID,GAEpD,CAACD,EAAGkC,EAAK,IAAK3B,EAAI,GAAG,CAC7B,EAEAjB,EAAQ,IAAI,IAAM,SAAU6C,EAAK,CAChC,IAAInC,EAAImC,EAAI,CAAC,EAAI,GACblC,EAAIkC,EAAI,CAAC,EAAI,IACb5B,EAAI4B,EAAI,CAAC,EAAI,IACbC,EAAK,KAAK,MAAMpC,CAAC,EAAI,EAErBqC,EAAIrC,EAAI,KAAK,MAAMA,CAAC,EACpBsC,EAAI,IAAM/B,GAAK,EAAIN,GACnBsC,EAAI,IAAMhC,GAAK,EAAKN,EAAIoC,GACxBG,EAAI,IAAMjC,GAAK,EAAKN,GAAK,EAAIoC,IAGjC,OAFA9B,GAAK,IAEG6B,EAAI,CACX,IAAK,GACJ,MAAO,CAAC7B,EAAGiC,EAAGF,CAAC,EAChB,IAAK,GACJ,MAAO,CAACC,EAAGhC,EAAG+B,CAAC,EAChB,IAAK,GACJ,MAAO,CAACA,EAAG/B,EAAGiC,CAAC,EAChB,IAAK,GACJ,MAAO,CAACF,EAAGC,EAAGhC,CAAC,EAChB,IAAK,GACJ,MAAO,CAACiC,EAAGF,EAAG/B,CAAC,EAChB,IAAK,GACJ,MAAO,CAACA,EAAG+B,EAAGC,CAAC,CACjB,CACD,EAEAjD,EAAQ,IAAI,IAAM,SAAU6C,EAAK,CAChC,IAAInC,EAAImC,EAAI,CAAC,EACTlC,EAAIkC,EAAI,CAAC,EAAI,IACb5B,EAAI4B,EAAI,CAAC,EAAI,IACbM,EAAO,KAAK,IAAIlC,EAAG,GAAI,EACvB0B,EACAS,EACAxC,EAEJ,OAAAA,GAAK,EAAID,GAAKM,EACd0B,GAAQ,EAAIhC,GAAKwC,EACjBC,EAAKzC,EAAIwC,EACTC,GAAOT,GAAQ,EAAKA,EAAO,EAAIA,EAC/BS,EAAKA,GAAM,EACXxC,GAAK,EAEE,CAACF,EAAG0C,EAAK,IAAKxC,EAAI,GAAG,CAC7B,EAGAZ,EAAQ,IAAI,IAAM,SAAUqD,EAAK,CAChC,IAAI3C,EAAI2C,EAAI,CAAC,EAAI,IACbC,EAAKD,EAAI,CAAC,EAAI,IACdE,EAAKF,EAAI,CAAC,EAAI,IACdG,EAAQF,EAAKC,EACbd,EACAxB,EACA8B,EACAU,EAGAD,EAAQ,IACXF,GAAME,EACND,GAAMC,GAGPf,EAAI,KAAK,MAAM,EAAI/B,CAAC,EACpBO,EAAI,EAAIsC,EACRR,EAAI,EAAIrC,EAAI+B,EAEPA,EAAI,IACRM,EAAI,EAAIA,GAGTU,EAAIH,EAAKP,GAAK9B,EAAIqC,GAElB,IAAItC,EACAX,EACAC,EACJ,OAAQmC,EAAG,CACV,QACA,IAAK,GACL,IAAK,GAAGzB,EAAIC,EAAGZ,EAAIoD,EAAGnD,EAAIgD,EAAI,MAC9B,IAAK,GAAGtC,EAAIyC,EAAGpD,EAAIY,EAAGX,EAAIgD,EAAI,MAC9B,IAAK,GAAGtC,EAAIsC,EAAIjD,EAAIY,EAAGX,EAAImD,EAAG,MAC9B,IAAK,GAAGzC,EAAIsC,EAAIjD,EAAIoD,EAAGnD,EAAIW,EAAG,MAC9B,IAAK,GAAGD,EAAIyC,EAAGpD,EAAIiD,EAAIhD,EAAIW,EAAG,MAC9B,IAAK,GAAGD,EAAIC,EAAGZ,EAAIiD,EAAIhD,EAAImD,EAAG,KAC/B,CAEA,MAAO,CAACzC,EAAI,IAAKX,EAAI,IAAKC,EAAI,GAAG,CAClC,EAEAN,EAAQ,KAAK,IAAM,SAAU0D,EAAM,CAClC,IAAItC,EAAIsC,EAAK,CAAC,EAAI,IACdpC,EAAIoC,EAAK,CAAC,EAAI,IACdnC,EAAImC,EAAK,CAAC,EAAI,IACdlC,EAAIkC,EAAK,CAAC,EAAI,IACd1C,EACAX,EACAC,EAEJ,OAAAU,EAAI,EAAI,KAAK,IAAI,EAAGI,GAAK,EAAII,GAAKA,CAAC,EACnCnB,EAAI,EAAI,KAAK,IAAI,EAAGiB,GAAK,EAAIE,GAAKA,CAAC,EACnClB,EAAI,EAAI,KAAK,IAAI,EAAGiB,GAAK,EAAIC,GAAKA,CAAC,EAE5B,CAACR,EAAI,IAAKX,EAAI,IAAKC,EAAI,GAAG,CAClC,EAEAN,EAAQ,IAAI,IAAM,SAAUkC,EAAK,CAChC,IAAIR,EAAIQ,EAAI,CAAC,EAAI,IACbX,EAAIW,EAAI,CAAC,EAAI,IACbD,EAAIC,EAAI,CAAC,EAAI,IACblB,EACAX,EACAC,EAEJ,OAAAU,EAAKU,EAAI,OAAWH,EAAI,QAAYU,EAAI,OACxC5B,EAAKqB,EAAI,OAAYH,EAAI,OAAWU,EAAI,MACxC3B,EAAKoB,EAAI,MAAWH,EAAI,MAAYU,EAAI,MAGxCjB,EAAIA,EAAI,SACH,MAAQ,KAAK,IAAIA,EAAG,EAAM,GAAG,EAAK,KACpCA,EAAI,MAEPX,EAAIA,EAAI,SACH,MAAQ,KAAK,IAAIA,EAAG,EAAM,GAAG,EAAK,KACpCA,EAAI,MAEPC,EAAIA,EAAI,SACH,MAAQ,KAAK,IAAIA,EAAG,EAAM,GAAG,EAAK,KACpCA,EAAI,MAEPU,EAAI,KAAK,IAAI,KAAK,IAAI,EAAGA,CAAC,EAAG,CAAC,EAC9BX,EAAI,KAAK,IAAI,KAAK,IAAI,EAAGA,CAAC,EAAG,CAAC,EAC9BC,EAAI,KAAK,IAAI,KAAK,IAAI,EAAGA,CAAC,EAAG,CAAC,EAEvB,CAACU,EAAI,IAAKX,EAAI,IAAKC,EAAI,GAAG,CAClC,EAEAN,EAAQ,IAAI,IAAM,SAAUkC,EAAK,CAChC,IAAIR,EAAIQ,EAAI,CAAC,EACTX,EAAIW,EAAI,CAAC,EACTD,EAAIC,EAAI,CAAC,EACTtB,EACA,EACAN,EAEJ,OAAAoB,GAAK,OACLH,GAAK,IACLU,GAAK,QAELP,EAAIA,EAAI,QAAW,KAAK,IAAIA,EAAG,EAAI,CAAC,EAAK,MAAQA,EAAM,GAAK,IAC5DH,EAAIA,EAAI,QAAW,KAAK,IAAIA,EAAG,EAAI,CAAC,EAAK,MAAQA,EAAM,GAAK,IAC5DU,EAAIA,EAAI,QAAW,KAAK,IAAIA,EAAG,EAAI,CAAC,EAAK,MAAQA,EAAM,GAAK,IAE5DrB,EAAK,IAAMW,EAAK,GAChB,EAAI,KAAOG,EAAIH,GACfjB,EAAI,KAAOiB,EAAIU,GAER,CAACrB,EAAG,EAAGN,CAAC,CAChB,EAEAN,EAAQ,IAAI,IAAM,SAAU2D,EAAK,CAChC,IAAI/C,EAAI+C,EAAI,CAAC,EACTxB,EAAIwB,EAAI,CAAC,EACTrD,EAAIqD,EAAI,CAAC,EACTjC,EACAH,EACAU,EAEJV,GAAKX,EAAI,IAAM,IACfc,EAAIS,EAAI,IAAMZ,EACdU,EAAIV,EAAIjB,EAAI,IAEZ,IAAIsD,EAAK,KAAK,IAAIrC,EAAG,CAAC,EAClBsC,EAAK,KAAK,IAAInC,EAAG,CAAC,EAClBoC,EAAK,KAAK,IAAI7B,EAAG,CAAC,EACtB,OAAAV,EAAIqC,EAAK,QAAWA,GAAMrC,EAAI,GAAK,KAAO,MAC1CG,EAAImC,EAAK,QAAWA,GAAMnC,EAAI,GAAK,KAAO,MAC1CO,EAAI6B,EAAK,QAAWA,GAAM7B,EAAI,GAAK,KAAO,MAE1CP,GAAK,OACLH,GAAK,IACLU,GAAK,QAEE,CAACP,EAAGH,EAAGU,CAAC,CAChB,EAEAjC,EAAQ,IAAI,IAAM,SAAU2D,EAAK,CAChC,IAAI/C,EAAI+C,EAAI,CAAC,EACTxB,EAAIwB,EAAI,CAAC,EACTrD,EAAIqD,EAAI,CAAC,EACTI,EACArD,EACAU,EAEJ,OAAA2C,EAAK,KAAK,MAAMzD,EAAG6B,CAAC,EACpBzB,EAAIqD,EAAK,IAAM,EAAI,KAAK,GAEpBrD,EAAI,IACPA,GAAK,KAGNU,EAAI,KAAK,KAAKe,EAAIA,EAAI7B,EAAIA,CAAC,EAEpB,CAACM,EAAGQ,EAAGV,CAAC,CAChB,EAEAV,EAAQ,IAAI,IAAM,SAAUgE,EAAK,CAChC,IAAIpD,EAAIoD,EAAI,CAAC,EACT5C,EAAI4C,EAAI,CAAC,EACTtD,EAAIsD,EAAI,CAAC,EACT7B,EACA7B,EACAyD,EAEJ,OAAAA,EAAKrD,EAAI,IAAM,EAAI,KAAK,GACxByB,EAAIf,EAAI,KAAK,IAAI2C,CAAE,EACnBzD,EAAIc,EAAI,KAAK,IAAI2C,CAAE,EAEZ,CAACnD,EAAGuB,EAAG7B,CAAC,CAChB,EAEAN,EAAQ,IAAI,OAAS,SAAUiE,EAAM,CACpC,IAAI,EAAIA,EAAK,CAAC,EACV5D,EAAI4D,EAAK,CAAC,EACV3D,EAAI2D,EAAK,CAAC,EACVlC,EAAQ,KAAK,UAAY,UAAU,CAAC,EAAI/B,EAAQ,IAAI,IAAIiE,CAAI,EAAE,CAAC,EAInE,GAFAlC,EAAQ,KAAK,MAAMA,EAAQ,EAAE,EAEzBA,IAAU,EACb,MAAO,IAGR,IAAImC,EAAO,IACN,KAAK,MAAM5D,EAAI,GAAG,GAAK,EACxB,KAAK,MAAMD,EAAI,GAAG,GAAK,EACxB,KAAK,MAAM,EAAI,GAAG,GAErB,OAAI0B,IAAU,IACbmC,GAAQ,IAGFA,CACR,EAEAlE,EAAQ,IAAI,OAAS,SAAUiE,EAAM,CAGpC,OAAOjE,EAAQ,IAAI,OAAOA,EAAQ,IAAI,IAAIiE,CAAI,EAAGA,EAAK,CAAC,CAAC,CACzD,EAEAjE,EAAQ,IAAI,QAAU,SAAUiE,EAAM,CACrC,IAAI,EAAIA,EAAK,CAAC,EACV5D,EAAI4D,EAAK,CAAC,EACV3D,EAAI2D,EAAK,CAAC,EAId,GAAI,IAAM5D,GAAKA,IAAMC,EACpB,OAAI,EAAI,EACA,GAGJ,EAAI,IACA,IAGD,KAAK,OAAQ,EAAI,GAAK,IAAO,EAAE,EAAI,IAG3C,IAAI4D,EAAO,GACP,GAAK,KAAK,MAAM,EAAI,IAAM,CAAC,EAC3B,EAAI,KAAK,MAAM7D,EAAI,IAAM,CAAC,EAC3B,KAAK,MAAMC,EAAI,IAAM,CAAC,EAEzB,OAAO4D,CACR,EAEAlE,EAAQ,OAAO,IAAM,SAAUiE,EAAM,CACpC,IAAIE,EAAQF,EAAO,GAGnB,GAAIE,IAAU,GAAKA,IAAU,EAC5B,OAAIF,EAAO,KACVE,GAAS,KAGVA,EAAQA,EAAQ,KAAO,IAEhB,CAACA,EAAOA,EAAOA,CAAK,EAG5B,IAAIC,GAAQ,CAAC,EAAEH,EAAO,IAAM,GAAK,GAC7BjD,GAAMmD,EAAQ,GAAKC,EAAQ,IAC3B/D,GAAO8D,GAAS,EAAK,GAAKC,EAAQ,IAClC9D,GAAO6D,GAAS,EAAK,GAAKC,EAAQ,IAEtC,MAAO,CAACpD,EAAGX,EAAGC,CAAC,CAChB,EAEAN,EAAQ,QAAQ,IAAM,SAAUiE,EAAM,CAErC,GAAIA,GAAQ,IAAK,CAChB,IAAI7C,GAAK6C,EAAO,KAAO,GAAK,EAC5B,MAAO,CAAC7C,EAAGA,EAAGA,CAAC,CAChB,CAEA6C,GAAQ,GAER,IAAII,EACArD,EAAI,KAAK,MAAMiD,EAAO,EAAE,EAAI,EAAI,IAChC5D,EAAI,KAAK,OAAOgE,EAAMJ,EAAO,IAAM,CAAC,EAAI,EAAI,IAC5C3D,EAAK+D,EAAM,EAAK,EAAI,IAExB,MAAO,CAACrD,EAAGX,EAAGC,CAAC,CAChB,EAEAN,EAAQ,IAAI,IAAM,SAAUiE,EAAM,CACjC,IAAIK,IAAY,KAAK,MAAML,EAAK,CAAC,CAAC,EAAI,MAAS,MAC1C,KAAK,MAAMA,EAAK,CAAC,CAAC,EAAI,MAAS,IAChC,KAAK,MAAMA,EAAK,CAAC,CAAC,EAAI,KAEtBM,EAASD,EAAQ,SAAS,EAAE,EAAE,YAAY,EAC9C,MAAO,SAAS,UAAUC,EAAO,MAAM,EAAIA,CAC5C,EAEAvE,EAAQ,IAAI,IAAM,SAAUiE,EAAM,CACjC,IAAIO,EAAQP,EAAK,SAAS,EAAE,EAAE,MAAM,0BAA0B,EAC9D,GAAI,CAACO,EACJ,MAAO,CAAC,EAAG,EAAG,CAAC,EAGhB,IAAIC,EAAcD,EAAM,CAAC,EAErBA,EAAM,CAAC,EAAE,SAAW,IACvBC,EAAcA,EAAY,MAAM,EAAE,EAAE,IAAI,SAAUC,EAAM,CACvD,OAAOA,EAAOA,CACf,CAAC,EAAE,KAAK,EAAE,GAGX,IAAIJ,EAAU,SAASG,EAAa,EAAE,EAClCzD,EAAKsD,GAAW,GAAM,IACtBjE,EAAKiE,GAAW,EAAK,IACrBhE,EAAIgE,EAAU,IAElB,MAAO,CAACtD,EAAGX,EAAGC,CAAC,CAChB,EAEAN,EAAQ,IAAI,IAAM,SAAUI,EAAK,CAChC,IAAI,EAAIA,EAAI,CAAC,EAAI,IACbC,EAAID,EAAI,CAAC,EAAI,IACbE,EAAIF,EAAI,CAAC,EAAI,IACbI,EAAM,KAAK,IAAI,KAAK,IAAI,EAAGH,CAAC,EAAGC,CAAC,EAChCC,EAAM,KAAK,IAAI,KAAK,IAAI,EAAGF,CAAC,EAAGC,CAAC,EAChCqE,EAAUnE,EAAMD,EAChBqE,EACAC,EAEJ,OAAIF,EAAS,EACZC,EAAYrE,GAAO,EAAIoE,GAEvBC,EAAY,EAGTD,GAAU,EACbE,EAAM,EAEHrE,IAAQ,EACXqE,GAAQxE,EAAIC,GAAKqE,EAAU,EAExBnE,IAAQH,EACXwE,EAAM,GAAKvE,EAAI,GAAKqE,EAEpBE,EAAM,GAAK,EAAIxE,GAAKsE,EAAS,EAG9BE,GAAO,EACPA,GAAO,EAEA,CAACA,EAAM,IAAKF,EAAS,IAAKC,EAAY,GAAG,CACjD,EAEA5E,EAAQ,IAAI,IAAM,SAAUoC,EAAK,CAChC,IAAIzB,EAAIyB,EAAI,CAAC,EAAI,IACbxB,EAAIwB,EAAI,CAAC,EAAI,IACbhB,EAAI,EACJ2B,EAAI,EAER,OAAInC,EAAI,GACPQ,EAAI,EAAMT,EAAIC,EAEdQ,EAAI,EAAMT,GAAK,EAAMC,GAGlBQ,EAAI,IACP2B,GAAKnC,EAAI,GAAMQ,IAAM,EAAMA,IAGrB,CAACgB,EAAI,CAAC,EAAGhB,EAAI,IAAK2B,EAAI,GAAG,CACjC,EAEA/C,EAAQ,IAAI,IAAM,SAAU6C,EAAK,CAChC,IAAIlC,EAAIkC,EAAI,CAAC,EAAI,IACb5B,EAAI4B,EAAI,CAAC,EAAI,IAEbzB,EAAIT,EAAIM,EACR8B,EAAI,EAER,OAAI3B,EAAI,IACP2B,GAAK9B,EAAIG,IAAM,EAAIA,IAGb,CAACyB,EAAI,CAAC,EAAGzB,EAAI,IAAK2B,EAAI,GAAG,CACjC,EAEA/C,EAAQ,IAAI,IAAM,SAAU8E,EAAK,CAChC,IAAIpE,EAAIoE,EAAI,CAAC,EAAI,IACb1D,EAAI0D,EAAI,CAAC,EAAI,IACbzE,EAAIyE,EAAI,CAAC,EAAI,IAEjB,GAAI1D,IAAM,EACT,MAAO,CAACf,EAAI,IAAKA,EAAI,IAAKA,EAAI,GAAG,EAGlC,IAAI0E,EAAO,CAAC,EAAG,EAAG,CAAC,EACfjC,EAAMpC,EAAI,EAAK,EACfO,EAAI6B,EAAK,EACTzB,EAAI,EAAIJ,EACR+D,EAAK,EAET,OAAQ,KAAK,MAAMlC,CAAE,EAAG,CACvB,IAAK,GACJiC,EAAK,CAAC,EAAI,EAAGA,EAAK,CAAC,EAAI9D,EAAG8D,EAAK,CAAC,EAAI,EAAG,MACxC,IAAK,GACJA,EAAK,CAAC,EAAI1D,EAAG0D,EAAK,CAAC,EAAI,EAAGA,EAAK,CAAC,EAAI,EAAG,MACxC,IAAK,GACJA,EAAK,CAAC,EAAI,EAAGA,EAAK,CAAC,EAAI,EAAGA,EAAK,CAAC,EAAI9D,EAAG,MACxC,IAAK,GACJ8D,EAAK,CAAC,EAAI,EAAGA,EAAK,CAAC,EAAI1D,EAAG0D,EAAK,CAAC,EAAI,EAAG,MACxC,IAAK,GACJA,EAAK,CAAC,EAAI9D,EAAG8D,EAAK,CAAC,EAAI,EAAGA,EAAK,CAAC,EAAI,EAAG,MACxC,QACCA,EAAK,CAAC,EAAI,EAAGA,EAAK,CAAC,EAAI,EAAGA,EAAK,CAAC,EAAI1D,CACtC,CAEA,OAAA2D,GAAM,EAAM5D,GAAKf,EAEV,EACLe,EAAI2D,EAAK,CAAC,EAAIC,GAAM,KACpB5D,EAAI2D,EAAK,CAAC,EAAIC,GAAM,KACpB5D,EAAI2D,EAAK,CAAC,EAAIC,GAAM,GACtB,CACD,EAEAhF,EAAQ,IAAI,IAAM,SAAU8E,EAAK,CAChC,IAAI1D,EAAI0D,EAAI,CAAC,EAAI,IACbzE,EAAIyE,EAAI,CAAC,EAAI,IAEb7D,EAAIG,EAAIf,GAAK,EAAMe,GACnB2B,EAAI,EAER,OAAI9B,EAAI,IACP8B,EAAI3B,EAAIH,GAGF,CAAC6D,EAAI,CAAC,EAAG/B,EAAI,IAAK9B,EAAI,GAAG,CACjC,EAEAjB,EAAQ,IAAI,IAAM,SAAU8E,EAAK,CAChC,IAAI1D,EAAI0D,EAAI,CAAC,EAAI,IACbzE,EAAIyE,EAAI,CAAC,EAAI,IAEblE,EAAIP,GAAK,EAAMe,GAAK,GAAMA,EAC1BT,EAAI,EAER,OAAIC,EAAI,GAAOA,EAAI,GAClBD,EAAIS,GAAK,EAAIR,GAEVA,GAAK,IAAOA,EAAI,IACnBD,EAAIS,GAAK,GAAK,EAAIR,KAGZ,CAACkE,EAAI,CAAC,EAAGnE,EAAI,IAAKC,EAAI,GAAG,CACjC,EAEAZ,EAAQ,IAAI,IAAM,SAAU8E,EAAK,CAChC,IAAI1D,EAAI0D,EAAI,CAAC,EAAI,IACbzE,EAAIyE,EAAI,CAAC,EAAI,IACb7D,EAAIG,EAAIf,GAAK,EAAMe,GACvB,MAAO,CAAC0D,EAAI,CAAC,GAAI7D,EAAIG,GAAK,KAAM,EAAIH,GAAK,GAAG,CAC7C,EAEAjB,EAAQ,IAAI,IAAM,SAAUqD,EAAK,CAChC,IAAIhC,EAAIgC,EAAI,CAAC,EAAI,IACb/C,EAAI+C,EAAI,CAAC,EAAI,IACbpC,EAAI,EAAIX,EACRc,EAAIH,EAAII,EACRhB,EAAI,EAER,OAAIe,EAAI,IACPf,GAAKY,EAAIG,IAAM,EAAIA,IAGb,CAACiC,EAAI,CAAC,EAAGjC,EAAI,IAAKf,EAAI,GAAG,CACjC,EAEAL,EAAQ,MAAM,IAAM,SAAUiF,EAAO,CACpC,MAAO,CAAEA,EAAM,CAAC,EAAI,MAAS,IAAMA,EAAM,CAAC,EAAI,MAAS,IAAMA,EAAM,CAAC,EAAI,MAAS,GAAG,CACrF,EAEAjF,EAAQ,IAAI,MAAQ,SAAUI,EAAK,CAClC,MAAO,CAAEA,EAAI,CAAC,EAAI,IAAO,MAAQA,EAAI,CAAC,EAAI,IAAO,MAAQA,EAAI,CAAC,EAAI,IAAO,KAAK,CAC/E,EAEAJ,EAAQ,KAAK,IAAM,SAAUiE,EAAM,CAClC,MAAO,CAACA,EAAK,CAAC,EAAI,IAAM,IAAKA,EAAK,CAAC,EAAI,IAAM,IAAKA,EAAK,CAAC,EAAI,IAAM,GAAG,CACtE,EAEAjE,EAAQ,KAAK,IAAMA,EAAQ,KAAK,IAAM,SAAUiE,EAAM,CACrD,MAAO,CAAC,EAAG,EAAGA,EAAK,CAAC,CAAC,CACtB,EAEAjE,EAAQ,KAAK,IAAM,SAAUkF,EAAM,CAClC,MAAO,CAAC,EAAG,IAAKA,EAAK,CAAC,CAAC,CACxB,EAEAlF,EAAQ,KAAK,KAAO,SAAUkF,EAAM,CACnC,MAAO,CAAC,EAAG,EAAG,EAAGA,EAAK,CAAC,CAAC,CACzB,EAEAlF,EAAQ,KAAK,IAAM,SAAUkF,EAAM,CAClC,MAAO,CAACA,EAAK,CAAC,EAAG,EAAG,CAAC,CACtB,EAEAlF,EAAQ,KAAK,IAAM,SAAUkF,EAAM,CAClC,IAAI1C,EAAM,KAAK,MAAM0C,EAAK,CAAC,EAAI,IAAM,GAAG,EAAI,IACxCZ,GAAW9B,GAAO,KAAOA,GAAO,GAAKA,EAErC+B,EAASD,EAAQ,SAAS,EAAE,EAAE,YAAY,EAC9C,MAAO,SAAS,UAAUC,EAAO,MAAM,EAAIA,CAC5C,EAEAvE,EAAQ,IAAI,KAAO,SAAUI,EAAK,CACjC,IAAIoC,GAAOpC,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAIA,EAAI,CAAC,GAAK,EACvC,MAAO,CAACoC,EAAM,IAAM,GAAG,CACxB,ICn2BA,IAAA2C,GAAAC,EAAA,CAAAC,GAAAC,KAAA,KAAIC,GAAc,KAalB,SAASC,IAAa,CAKrB,QAJIC,EAAQ,CAAC,EAETC,EAAS,OAAO,KAAKH,EAAW,EAE3BI,EAAMD,EAAO,OAAQE,EAAI,EAAGA,EAAID,EAAKC,IAC7CH,EAAMC,EAAOE,CAAC,CAAC,EAAI,CAGlB,SAAU,GACV,OAAQ,IACT,EAGD,OAAOH,CACR,CAGA,SAASI,GAAUC,EAAW,CAC7B,IAAIL,EAAQD,GAAW,EACnBO,EAAQ,CAACD,CAAS,EAItB,IAFAL,EAAMK,CAAS,EAAE,SAAW,EAErBC,EAAM,QAIZ,QAHIC,EAAUD,EAAM,IAAI,EACpBE,EAAY,OAAO,KAAKV,GAAYS,CAAO,CAAC,EAEvCL,EAAMM,EAAU,OAAQL,EAAI,EAAGA,EAAID,EAAKC,IAAK,CACrD,IAAIM,EAAWD,EAAUL,CAAC,EACtBO,EAAOV,EAAMS,CAAQ,EAErBC,EAAK,WAAa,KACrBA,EAAK,SAAWV,EAAMO,CAAO,EAAE,SAAW,EAC1CG,EAAK,OAASH,EACdD,EAAM,QAAQG,CAAQ,EAExB,CAGD,OAAOT,CACR,CAEA,SAASW,GAAKC,EAAMC,EAAI,CACvB,OAAO,SAAUC,EAAM,CACtB,OAAOD,EAAGD,EAAKE,CAAI,CAAC,CACrB,CACD,CAEA,SAASC,GAAeC,EAAShB,EAAO,CAKvC,QAJIiB,EAAO,CAACjB,EAAMgB,CAAO,EAAE,OAAQA,CAAO,EACtCE,EAAKpB,GAAYE,EAAMgB,CAAO,EAAE,MAAM,EAAEA,CAAO,EAE/CG,EAAMnB,EAAMgB,CAAO,EAAE,OAClBhB,EAAMmB,CAAG,EAAE,QACjBF,EAAK,QAAQjB,EAAMmB,CAAG,EAAE,MAAM,EAC9BD,EAAKP,GAAKb,GAAYE,EAAMmB,CAAG,EAAE,MAAM,EAAEA,CAAG,EAAGD,CAAE,EACjDC,EAAMnB,EAAMmB,CAAG,EAAE,OAGlB,OAAAD,EAAG,WAAaD,EACTC,CACR,CAEArB,GAAO,QAAU,SAAUQ,EAAW,CAKrC,QAJIL,EAAQI,GAAUC,CAAS,EAC3Be,EAAa,CAAC,EAEdnB,EAAS,OAAO,KAAKD,CAAK,EACrBE,EAAMD,EAAO,OAAQE,EAAI,EAAGA,EAAID,EAAKC,IAAK,CAClD,IAAIa,EAAUf,EAAOE,CAAC,EAClBO,EAAOV,EAAMgB,CAAO,EAEpBN,EAAK,SAAW,OAKpBU,EAAWJ,CAAO,EAAID,GAAeC,EAAShB,CAAK,EACpD,CAEA,OAAOoB,CACR,IC/FA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,KAAIC,GAAc,KACdC,GAAQ,KAERC,GAAU,CAAC,EAEXC,GAAS,OAAO,KAAKH,EAAW,EAEpC,SAASI,GAAQC,EAAI,CACpB,IAAIC,EAAY,SAAUC,EAAM,CAC/B,OAA0BA,GAAS,KAC3BA,GAGJ,UAAU,OAAS,IACtBA,EAAO,MAAM,UAAU,MAAM,KAAK,SAAS,GAGrCF,EAAGE,CAAI,EACf,EAGA,MAAI,eAAgBF,IACnBC,EAAU,WAAaD,EAAG,YAGpBC,CACR,CAEA,SAASE,GAAYH,EAAI,CACxB,IAAIC,EAAY,SAAUC,EAAM,CAC/B,GAA0BA,GAAS,KAClC,OAAOA,EAGJ,UAAU,OAAS,IACtBA,EAAO,MAAM,UAAU,MAAM,KAAK,SAAS,GAG5C,IAAIE,EAASJ,EAAGE,CAAI,EAKpB,GAAI,OAAOE,GAAW,SACrB,QAASC,EAAMD,EAAO,OAAQE,EAAI,EAAGA,EAAID,EAAKC,IAC7CF,EAAOE,CAAC,EAAI,KAAK,MAAMF,EAAOE,CAAC,CAAC,EAIlC,OAAOF,CACR,EAGA,MAAI,eAAgBJ,IACnBC,EAAU,WAAaD,EAAG,YAGpBC,CACR,CAEAH,GAAO,QAAQ,SAAUS,EAAW,CACnCV,GAAQU,CAAS,EAAI,CAAC,EAEtB,OAAO,eAAeV,GAAQU,CAAS,EAAG,WAAY,CAAC,MAAOZ,GAAYY,CAAS,EAAE,QAAQ,CAAC,EAC9F,OAAO,eAAeV,GAAQU,CAAS,EAAG,SAAU,CAAC,MAAOZ,GAAYY,CAAS,EAAE,MAAM,CAAC,EAE1F,IAAIC,EAASZ,GAAMW,CAAS,EACxBE,EAAc,OAAO,KAAKD,CAAM,EAEpCC,EAAY,QAAQ,SAAUC,EAAS,CACtC,IAAIV,EAAKQ,EAAOE,CAAO,EAEvBb,GAAQU,CAAS,EAAEG,CAAO,EAAIP,GAAYH,CAAE,EAC5CH,GAAQU,CAAS,EAAEG,CAAO,EAAE,IAAMX,GAAQC,CAAE,CAC7C,CAAC,CACF,CAAC,EAEDN,GAAO,QAAUG,KC7EjB,IAAAc,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAMC,GAAe,KAEfC,GAAa,CAACC,EAAIC,IAAW,UAAY,CAE9C,MAAO,QADMD,EAAG,MAAMF,GAAc,SAAS,EACrBG,CAAM,GAC/B,EAEMC,GAAc,CAACF,EAAIC,IAAW,UAAY,CAC/C,IAAME,EAAOH,EAAG,MAAMF,GAAc,SAAS,EAC7C,MAAO,QAAU,GAAKG,CAAM,MAAME,CAAI,GACvC,EAEMC,GAAc,CAACJ,EAAIC,IAAW,UAAY,CAC/C,IAAMI,EAAML,EAAG,MAAMF,GAAc,SAAS,EAC5C,MAAO,QAAU,GAAKG,CAAM,MAAMI,EAAI,CAAC,CAAC,IAAIA,EAAI,CAAC,CAAC,IAAIA,EAAI,CAAC,CAAC,GAC7D,EAEA,SAASC,IAAiB,CACzB,IAAMC,EAAQ,IAAI,IACZC,EAAS,CACd,SAAU,CACT,MAAO,CAAC,EAAG,CAAC,EAEZ,KAAM,CAAC,EAAG,EAAE,EACZ,IAAK,CAAC,EAAG,EAAE,EACX,OAAQ,CAAC,EAAG,EAAE,EACd,UAAW,CAAC,EAAG,EAAE,EACjB,QAAS,CAAC,EAAG,EAAE,EACf,OAAQ,CAAC,EAAG,EAAE,EACd,cAAe,CAAC,EAAG,EAAE,CACtB,EACA,MAAO,CACN,MAAO,CAAC,GAAI,EAAE,EACd,IAAK,CAAC,GAAI,EAAE,EACZ,MAAO,CAAC,GAAI,EAAE,EACd,OAAQ,CAAC,GAAI,EAAE,EACf,KAAM,CAAC,GAAI,EAAE,EACb,QAAS,CAAC,GAAI,EAAE,EAChB,KAAM,CAAC,GAAI,EAAE,EACb,MAAO,CAAC,GAAI,EAAE,EACd,KAAM,CAAC,GAAI,EAAE,EAGb,UAAW,CAAC,GAAI,EAAE,EAClB,YAAa,CAAC,GAAI,EAAE,EACpB,aAAc,CAAC,GAAI,EAAE,EACrB,WAAY,CAAC,GAAI,EAAE,EACnB,cAAe,CAAC,GAAI,EAAE,EACtB,WAAY,CAAC,GAAI,EAAE,EACnB,YAAa,CAAC,GAAI,EAAE,CACrB,EACA,QAAS,CACR,QAAS,CAAC,GAAI,EAAE,EAChB,MAAO,CAAC,GAAI,EAAE,EACd,QAAS,CAAC,GAAI,EAAE,EAChB,SAAU,CAAC,GAAI,EAAE,EACjB,OAAQ,CAAC,GAAI,EAAE,EACf,UAAW,CAAC,GAAI,EAAE,EAClB,OAAQ,CAAC,GAAI,EAAE,EACf,QAAS,CAAC,GAAI,EAAE,EAGhB,cAAe,CAAC,IAAK,EAAE,EACvB,YAAa,CAAC,IAAK,EAAE,EACrB,cAAe,CAAC,IAAK,EAAE,EACvB,eAAgB,CAAC,IAAK,EAAE,EACxB,aAAc,CAAC,IAAK,EAAE,EACtB,gBAAiB,CAAC,IAAK,EAAE,EACzB,aAAc,CAAC,IAAK,EAAE,EACtB,cAAe,CAAC,IAAK,EAAE,CACxB,CACD,EAGAA,EAAO,MAAM,KAAOA,EAAO,MAAM,KAEjC,QAAWC,KAAa,OAAO,KAAKD,CAAM,EAAG,CAC5C,IAAME,EAAQF,EAAOC,CAAS,EAE9B,QAAWE,KAAa,OAAO,KAAKD,CAAK,EAAG,CAC3C,IAAME,EAAQF,EAAMC,CAAS,EAE7BH,EAAOG,CAAS,EAAI,CACnB,KAAM,QAAUC,EAAM,CAAC,CAAC,IACxB,MAAO,QAAUA,EAAM,CAAC,CAAC,GAC1B,EAEAF,EAAMC,CAAS,EAAIH,EAAOG,CAAS,EAEnCJ,EAAM,IAAIK,EAAM,CAAC,EAAGA,EAAM,CAAC,CAAC,CAC7B,CAEA,OAAO,eAAeJ,EAAQC,EAAW,CACxC,MAAOC,EACP,WAAY,EACb,CAAC,EAED,OAAO,eAAeF,EAAQ,QAAS,CACtC,MAAOD,EACP,WAAY,EACb,CAAC,CACF,CAEA,IAAMM,EAAYC,GAAKA,EACjBC,EAAU,CAACC,EAAGC,EAAGC,IAAM,CAACF,EAAGC,EAAGC,CAAC,EAErCV,EAAO,MAAM,MAAQ,WACrBA,EAAO,QAAQ,MAAQ,WAEvBA,EAAO,MAAM,KAAO,CACnB,KAAMT,GAAWc,EAAW,CAAC,CAC9B,EACAL,EAAO,MAAM,QAAU,CACtB,QAASN,GAAYW,EAAW,CAAC,CAClC,EACAL,EAAO,MAAM,QAAU,CACtB,IAAKJ,GAAYW,EAAS,CAAC,CAC5B,EAEAP,EAAO,QAAQ,KAAO,CACrB,KAAMT,GAAWc,EAAW,EAAE,CAC/B,EACAL,EAAO,QAAQ,QAAU,CACxB,QAASN,GAAYW,EAAW,EAAE,CACnC,EACAL,EAAO,QAAQ,QAAU,CACxB,IAAKJ,GAAYW,EAAS,EAAE,CAC7B,EAEA,QAASI,KAAO,OAAO,KAAKrB,EAAY,EAAG,CAC1C,GAAI,OAAOA,GAAaqB,CAAG,GAAM,SAChC,SAGD,IAAMC,EAAQtB,GAAaqB,CAAG,EAE1BA,IAAQ,WACXA,EAAM,QAGH,WAAYC,IACfZ,EAAO,MAAM,KAAKW,CAAG,EAAIpB,GAAWqB,EAAM,OAAQ,CAAC,EACnDZ,EAAO,QAAQ,KAAKW,CAAG,EAAIpB,GAAWqB,EAAM,OAAQ,EAAE,GAGnD,YAAaA,IAChBZ,EAAO,MAAM,QAAQW,CAAG,EAAIjB,GAAYkB,EAAM,QAAS,CAAC,EACxDZ,EAAO,QAAQ,QAAQW,CAAG,EAAIjB,GAAYkB,EAAM,QAAS,EAAE,GAGxD,QAASA,IACZZ,EAAO,MAAM,QAAQW,CAAG,EAAIf,GAAYgB,EAAM,IAAK,CAAC,EACpDZ,EAAO,QAAQ,QAAQW,CAAG,EAAIf,GAAYgB,EAAM,IAAK,EAAE,EAEzD,CAEA,OAAOZ,CACR,CAGA,OAAO,eAAeX,GAAQ,UAAW,CACxC,WAAY,GACZ,IAAKS,EACN,CAAC,ICpKD,IAAAe,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACAA,GAAO,QAAU,CAACC,EAAMC,IAAS,CAChCA,EAAOA,GAAQ,QAAQ,KACvB,IAAMC,EAASF,EAAK,WAAW,GAAG,EAAI,GAAMA,EAAK,SAAW,EAAI,IAAM,KAChEG,EAAMF,EAAK,QAAQC,EAASF,CAAI,EAChCI,EAAgBH,EAAK,QAAQ,IAAI,EACvC,OAAOE,IAAQ,KAAOC,IAAkB,GAAK,GAAOD,EAAMC,EAC3D,ICPA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAMC,GAAK,QAAQ,IAAI,EACjBC,EAAU,KAEVC,EAAM,QAAQ,IAEhBC,GACAF,EAAQ,UAAU,GACrBA,EAAQ,WAAW,GACnBA,EAAQ,aAAa,EACrBE,GAAa,IACHF,EAAQ,OAAO,GACzBA,EAAQ,QAAQ,GAChBA,EAAQ,YAAY,GACpBA,EAAQ,cAAc,KACtBE,GAAa,IAEV,gBAAiBD,IACpBC,GAAaD,EAAI,YAAY,SAAW,GAAK,SAASA,EAAI,YAAa,EAAE,IAAM,GAGhF,SAASE,GAAeC,EAAO,CAC9B,OAAIA,IAAU,EACN,GAGD,CACN,MAAAA,EACA,SAAU,GACV,OAAQA,GAAS,EACjB,OAAQA,GAAS,CAClB,CACD,CAEA,SAASC,GAAcC,EAAQ,CAC9B,GAAIJ,KAAe,GAClB,MAAO,GAGR,GAAIF,EAAQ,WAAW,GACtBA,EAAQ,YAAY,GACpBA,EAAQ,iBAAiB,EACzB,MAAO,GAGR,GAAIA,EAAQ,WAAW,EACtB,MAAO,GAGR,GAAIM,GAAU,CAACA,EAAO,OAASJ,KAAe,GAC7C,MAAO,GAGR,IAAMK,EAAML,GAAa,EAAI,EAE7B,GAAI,QAAQ,WAAa,QAAS,CAOjC,IAAMM,EAAYT,GAAG,QAAQ,EAAE,MAAM,GAAG,EACxC,OACC,OAAO,QAAQ,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,GAAK,GAC/C,OAAOS,EAAU,CAAC,CAAC,GAAK,IACxB,OAAOA,EAAU,CAAC,CAAC,GAAK,MAEjB,OAAOA,EAAU,CAAC,CAAC,GAAK,MAAQ,EAAI,EAGrC,CACR,CAEA,GAAI,OAAQP,EACX,MAAI,CAAC,SAAU,WAAY,WAAY,WAAW,EAAE,KAAKQ,GAAQA,KAAQR,CAAG,GAAKA,EAAI,UAAY,WACzF,EAGDM,EAGR,GAAI,qBAAsBN,EACzB,MAAO,gCAAgC,KAAKA,EAAI,gBAAgB,EAAI,EAAI,EAGzE,GAAIA,EAAI,YAAc,YACrB,MAAO,GAGR,GAAI,iBAAkBA,EAAK,CAC1B,IAAMS,EAAU,UAAUT,EAAI,sBAAwB,IAAI,MAAM,GAAG,EAAE,CAAC,EAAG,EAAE,EAE3E,OAAQA,EAAI,aAAc,CACzB,IAAK,YACJ,OAAOS,GAAW,EAAI,EAAI,EAC3B,IAAK,iBACJ,MAAO,EAET,CACD,CAEA,MAAI,iBAAiB,KAAKT,EAAI,IAAI,EAC1B,EAGJ,8DAA8D,KAAKA,EAAI,IAAI,GAI3E,cAAeA,EACX,GAGJA,EAAI,OAAS,OACTM,EAIT,CAEA,SAASI,GAAgBL,EAAQ,CAChC,IAAMF,EAAQC,GAAcC,CAAM,EAClC,OAAOH,GAAeC,CAAK,CAC5B,CAEAN,GAAO,QAAU,CAChB,cAAea,GACf,OAAQA,GAAgB,QAAQ,MAAM,EACtC,OAAQA,GAAgB,QAAQ,MAAM,CACvC,IClIA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAMC,GAAiB,uIACjBC,GAAc,iCACdC,GAAe,mCACfC,GAAe,0CAEfC,GAAU,IAAI,IAAI,CACvB,CAAC,IAAK;AAAA,CAAI,EACV,CAAC,IAAK,IAAI,EACV,CAAC,IAAK,GAAI,EACV,CAAC,IAAK,IAAI,EACV,CAAC,IAAK,IAAI,EACV,CAAC,IAAK,IAAI,EACV,CAAC,IAAK,IAAI,EACV,CAAC,KAAM,IAAI,EACX,CAAC,IAAK,MAAQ,EACd,CAAC,IAAK,MAAQ,CACf,CAAC,EAED,SAASC,GAASC,EAAG,CACpB,OAAKA,EAAE,CAAC,IAAM,KAAOA,EAAE,SAAW,GAAOA,EAAE,CAAC,IAAM,KAAOA,EAAE,SAAW,EAC9D,OAAO,aAAa,SAASA,EAAE,MAAM,CAAC,EAAG,EAAE,CAAC,EAG7CF,GAAQ,IAAIE,CAAC,GAAKA,CAC1B,CAEA,SAASC,GAAeC,EAAMC,EAAM,CACnC,IAAMC,EAAU,CAAC,EACXC,EAASF,EAAK,KAAK,EAAE,MAAM,UAAU,EACvCG,EAEJ,QAAWC,KAASF,EACnB,GAAI,CAAC,MAAME,CAAK,EACfH,EAAQ,KAAK,OAAOG,CAAK,CAAC,UACfD,EAAUC,EAAM,MAAMX,EAAY,EAC7CQ,EAAQ,KAAKE,EAAQ,CAAC,EAAE,QAAQT,GAAc,CAACW,EAAGC,EAAQC,IAAQD,EAASV,GAASU,CAAM,EAAIC,CAAG,CAAC,MAElG,OAAM,IAAI,MAAM,0CAA0CH,CAAK,eAAeL,CAAI,IAAI,EAIxF,OAAOE,CACR,CAEA,SAASO,GAAWC,EAAO,CAC1BjB,GAAY,UAAY,EAExB,IAAMS,EAAU,CAAC,EACbE,EAEJ,MAAQA,EAAUX,GAAY,KAAKiB,CAAK,KAAO,MAAM,CACpD,IAAMV,EAAOI,EAAQ,CAAC,EAEtB,GAAIA,EAAQ,CAAC,EAAG,CACf,IAAMH,EAAOF,GAAeC,EAAMI,EAAQ,CAAC,CAAC,EAC5CF,EAAQ,KAAK,CAACF,CAAI,EAAE,OAAOC,CAAI,CAAC,CACjC,MACCC,EAAQ,KAAK,CAACF,CAAI,CAAC,CAErB,CAEA,OAAOE,CACR,CAEA,SAASS,GAAWC,EAAOC,EAAQ,CAClC,IAAMC,EAAU,CAAC,EAEjB,QAAWC,KAASF,EACnB,QAAWH,KAASK,EAAM,OACzBD,EAAQJ,EAAM,CAAC,CAAC,EAAIK,EAAM,QAAU,KAAOL,EAAM,MAAM,CAAC,EAI1D,IAAIM,EAAUJ,EACd,QAAWK,KAAa,OAAO,KAAKH,CAAO,EAC1C,GAAI,MAAM,QAAQA,EAAQG,CAAS,CAAC,EAAG,CACtC,GAAI,EAAEA,KAAaD,GAClB,MAAM,IAAI,MAAM,wBAAwBC,CAAS,EAAE,EAGhDH,EAAQG,CAAS,EAAE,OAAS,EAC/BD,EAAUA,EAAQC,CAAS,EAAE,MAAMD,EAASF,EAAQG,CAAS,CAAC,EAE9DD,EAAUA,EAAQC,CAAS,CAE7B,CAGD,OAAOD,CACR,CAEAzB,GAAO,QAAU,CAACqB,EAAOM,IAAQ,CAChC,IAAML,EAAS,CAAC,EACVV,EAAS,CAAC,EACZE,EAAQ,CAAC,EA0Bb,GAvBAa,EAAI,QAAQ1B,GAAgB,CAACc,EAAGa,EAAYC,EAASV,EAAOW,EAAOb,IAAQ,CAC1E,GAAIW,EACHd,EAAM,KAAKR,GAASsB,CAAU,CAAC,UACrBT,EAAO,CACjB,IAAMY,EAAMjB,EAAM,KAAK,EAAE,EACzBA,EAAQ,CAAC,EACTF,EAAO,KAAKU,EAAO,SAAW,EAAIS,EAAMX,GAAWC,EAAOC,CAAM,EAAES,CAAG,CAAC,EACtET,EAAO,KAAK,CAAC,QAAAO,EAAS,OAAQX,GAAWC,CAAK,CAAC,CAAC,CACjD,SAAWW,EAAO,CACjB,GAAIR,EAAO,SAAW,EACrB,MAAM,IAAI,MAAM,8CAA8C,EAG/DV,EAAO,KAAKQ,GAAWC,EAAOC,CAAM,EAAER,EAAM,KAAK,EAAE,CAAC,CAAC,EACrDA,EAAQ,CAAC,EACTQ,EAAO,IAAI,CACZ,MACCR,EAAM,KAAKG,CAAG,CAEhB,CAAC,EAEDL,EAAO,KAAKE,EAAM,KAAK,EAAE,CAAC,EAEtBQ,EAAO,OAAS,EAAG,CACtB,IAAMU,EAAS,qCAAqCV,EAAO,MAAM,mBAAmBA,EAAO,SAAW,EAAI,GAAK,GAAG,WAClH,MAAM,IAAI,MAAMU,CAAM,CACvB,CAEA,OAAOpB,EAAO,KAAK,EAAE,CACtB,IC/HA,IAAAqB,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAMC,GAAqB,KACrBC,EAAa,KACbC,GAAc,KAA0B,OAExCC,GAAW,KAEXC,GAAsB,QAAQ,WAAa,SAAW,EAAE,QAAQ,IAAI,MAAQ,IAAI,YAAY,EAAE,WAAW,OAAO,EAGhHC,GAAe,CAAC,OAAQ,OAAQ,UAAW,SAAS,EAGpDC,GAAa,IAAI,IAAI,CAAC,MAAM,CAAC,EAE7BC,GAAS,OAAO,OAAO,IAAI,EAEjC,SAASC,GAAaC,EAAKC,EAAS,CACnCA,EAAUA,GAAW,CAAC,EAGtB,IAAMC,EAAUT,GAAcA,GAAY,MAAQ,EAClDO,EAAI,MAAQC,EAAQ,QAAU,OAAYC,EAAUD,EAAQ,MAC5DD,EAAI,QAAU,YAAaC,EAAUA,EAAQ,QAAUD,EAAI,MAAQ,CACpE,CAEA,SAASG,GAAMF,EAAS,CAGvB,GAAI,CAAC,MAAQ,EAAE,gBAAgBE,KAAU,KAAK,SAAU,CACvD,IAAMC,EAAQ,CAAC,EACf,OAAAL,GAAaK,EAAOH,CAAO,EAE3BG,EAAM,SAAW,UAAY,CAC5B,IAAMC,EAAO,CAAC,EAAE,MAAM,KAAK,SAAS,EACpC,OAAOC,GAAS,MAAM,KAAM,CAACF,EAAM,QAAQ,EAAE,OAAOC,CAAI,CAAC,CAC1D,EAEA,OAAO,eAAeD,EAAOD,GAAM,SAAS,EAC5C,OAAO,eAAeC,EAAM,SAAUA,CAAK,EAE3CA,EAAM,SAAS,YAAcD,GAEtBC,EAAM,QACd,CAEAL,GAAa,KAAME,CAAO,CAC3B,CAGIN,KACHH,EAAW,KAAK,KAAO,YAGxB,QAAWe,KAAO,OAAO,KAAKf,CAAU,EACvCA,EAAWe,CAAG,EAAE,QAAU,IAAI,OAAOhB,GAAmBC,EAAWe,CAAG,EAAE,KAAK,EAAG,GAAG,EAEnFT,GAAOS,CAAG,EAAI,CACb,KAAM,CACL,IAAMC,EAAQhB,EAAWe,CAAG,EAC5B,OAAOE,GAAM,KAAK,KAAM,KAAK,QAAU,KAAK,QAAQ,OAAOD,CAAK,EAAI,CAACA,CAAK,EAAG,KAAK,OAAQD,CAAG,CAC9F,CACD,EAGDT,GAAO,QAAU,CAChB,KAAM,CACL,OAAOW,GAAM,KAAK,KAAM,KAAK,SAAW,CAAC,EAAG,GAAM,SAAS,CAC5D,CACD,EAEAjB,EAAW,MAAM,QAAU,IAAI,OAAOD,GAAmBC,EAAW,MAAM,KAAK,EAAG,GAAG,EACrF,QAAWkB,KAAS,OAAO,KAAKlB,EAAW,MAAM,IAAI,EAChDK,GAAW,IAAIa,CAAK,IAIxBZ,GAAOY,CAAK,EAAI,CACf,KAAM,CACL,IAAMC,EAAQ,KAAK,MACnB,OAAO,UAAY,CAElB,IAAMH,EAAQ,CACb,KAFYhB,EAAW,MAAMI,GAAae,CAAK,CAAC,EAAED,CAAK,EAAE,MAAM,KAAM,SAAS,EAG9E,MAAOlB,EAAW,MAAM,MACxB,QAASA,EAAW,MAAM,OAC3B,EACA,OAAOiB,GAAM,KAAK,KAAM,KAAK,QAAU,KAAK,QAAQ,OAAOD,CAAK,EAAI,CAACA,CAAK,EAAG,KAAK,OAAQE,CAAK,CAChG,CACD,CACD,GAGDlB,EAAW,QAAQ,QAAU,IAAI,OAAOD,GAAmBC,EAAW,QAAQ,KAAK,EAAG,GAAG,EACzF,QAAWkB,KAAS,OAAO,KAAKlB,EAAW,QAAQ,IAAI,EAAG,CACzD,GAAIK,GAAW,IAAIa,CAAK,EACvB,SAGD,IAAME,EAAU,KAAOF,EAAM,CAAC,EAAE,YAAY,EAAIA,EAAM,MAAM,CAAC,EAC7DZ,GAAOc,CAAO,EAAI,CACjB,KAAM,CACL,IAAMD,EAAQ,KAAK,MACnB,OAAO,UAAY,CAElB,IAAMH,EAAQ,CACb,KAFYhB,EAAW,QAAQI,GAAae,CAAK,CAAC,EAAED,CAAK,EAAE,MAAM,KAAM,SAAS,EAGhF,MAAOlB,EAAW,QAAQ,MAC1B,QAASA,EAAW,QAAQ,OAC7B,EACA,OAAOiB,GAAM,KAAK,KAAM,KAAK,QAAU,KAAK,QAAQ,OAAOD,CAAK,EAAI,CAACA,CAAK,EAAG,KAAK,OAAQE,CAAK,CAChG,CACD,CACD,CACD,CAEA,IAAMG,GAAQ,OAAO,iBAAiB,IAAM,CAAC,EAAGf,EAAM,EAEtD,SAASW,GAAMK,EAASC,EAAQR,EAAK,CACpC,IAAMS,EAAU,UAAY,CAC3B,OAAOC,GAAW,MAAMD,EAAS,SAAS,CAC3C,EAEAA,EAAQ,QAAUF,EAClBE,EAAQ,OAASD,EAEjB,IAAMG,EAAO,KAEb,cAAO,eAAeF,EAAS,QAAS,CACvC,WAAY,GACZ,KAAM,CACL,OAAOE,EAAK,KACb,EACA,IAAIP,EAAO,CACVO,EAAK,MAAQP,CACd,CACD,CAAC,EAED,OAAO,eAAeK,EAAS,UAAW,CACzC,WAAY,GACZ,KAAM,CACL,OAAOE,EAAK,OACb,EACA,IAAIC,EAAS,CACZD,EAAK,QAAUC,CAChB,CACD,CAAC,EAGDH,EAAQ,QAAU,KAAK,SAAWT,IAAQ,QAAUA,IAAQ,OAI5DS,EAAQ,UAAYH,GAEbG,CACR,CAEA,SAASC,IAAa,CAErB,IAAMZ,EAAO,UACPe,EAAUf,EAAK,OACjBgB,EAAM,OAAO,UAAU,CAAC,CAAC,EAE7B,GAAID,IAAY,EACf,MAAO,GAGR,GAAIA,EAAU,EAEb,QAASE,EAAI,EAAGA,EAAIF,EAASE,IAC5BD,GAAO,IAAMhB,EAAKiB,CAAC,EAIrB,GAAI,CAAC,KAAK,SAAW,KAAK,OAAS,GAAK,CAACD,EACxC,OAAO,KAAK,OAAS,GAAKA,EAM3B,IAAME,EAAc/B,EAAW,IAAI,KAC/BG,IAAuB,KAAK,UAC/BH,EAAW,IAAI,KAAO,IAGvB,QAAWgC,KAAQ,KAAK,QAAQ,MAAM,EAAE,QAAQ,EAI/CH,EAAMG,EAAK,KAAOH,EAAI,QAAQG,EAAK,QAASA,EAAK,IAAI,EAAIA,EAAK,MAK9DH,EAAMA,EAAI,QAAQ,SAAU,GAAGG,EAAK,KAAK,KAAKA,EAAK,IAAI,EAAE,EAI1D,OAAAhC,EAAW,IAAI,KAAO+B,EAEfF,CACR,CAEA,SAASf,GAASF,EAAOqB,EAAS,CACjC,GAAI,CAAC,MAAM,QAAQA,CAAO,EAGzB,MAAO,CAAC,EAAE,MAAM,KAAK,UAAW,CAAC,EAAE,KAAK,GAAG,EAG5C,IAAMpB,EAAO,CAAC,EAAE,MAAM,KAAK,UAAW,CAAC,EACjCqB,EAAQ,CAACD,EAAQ,IAAI,CAAC,CAAC,EAE7B,QAAS,EAAI,EAAG,EAAIA,EAAQ,OAAQ,IACnCC,EAAM,KAAK,OAAOrB,EAAK,EAAI,CAAC,CAAC,EAAE,QAAQ,UAAW,MAAM,CAAC,EACzDqB,EAAM,KAAK,OAAOD,EAAQ,IAAI,CAAC,CAAC,CAAC,EAGlC,OAAO/B,GAASU,EAAOsB,EAAM,KAAK,EAAE,CAAC,CACtC,CAEA,OAAO,iBAAiBvB,GAAM,UAAWL,EAAM,EAE/CR,GAAO,QAAUa,GAAM,EACvBb,GAAO,QAAQ,cAAgBG,GAC/BH,GAAO,QAAQ,QAAUA,GAAO,2HClOhC,IAAAqC,GAAAC,KAEAC,GAAAD,KAKAE,GAAAC,GAAAH,KAAA,EAAA,EAAmD,SAAAI,GAAAC,EAAA,CAAA,GAAA,OAAAC,SAAA,WAAA,OAAA,KAAA,IAAAC,EAAA,IAAAD,QAAAE,EAAA,IAAAF,QAAA,OAAAF,GAAA,SAAAC,EAAA,CAAA,OAAAA,EAAAG,EAAAD,CAAA,GAAAF,CAAA,CAAA,CAAA,SAAAF,GAAAE,EAAAE,EAAA,CAAA,GAAA,CAAAA,GAAAF,GAAAA,EAAAI,WAAA,OAAAJ,EAAA,GAAAA,IAAA,MAAA,OAAAA,GAAA,UAAA,OAAAA,GAAA,WAAA,MAAA,CAAAK,QAAAL,CAAA,EAAA,IAAAG,EAAAJ,GAAAG,CAAA,EAAA,GAAAC,GAAAA,EAAAG,IAAAN,CAAA,EAAA,OAAAG,EAAAI,IAAAP,CAAA,EAAA,IAAAQ,EAAA,CAAAC,UAAA,IAAA,EAAAC,EAAAC,OAAAC,gBAAAD,OAAAE,yBAAA,QAAAC,KAAAd,EAAA,GAAAc,IAAA,WAAA,CAAA,EAAAC,eAAAC,KAAAhB,EAAAc,CAAA,EAAA,CAAA,IAAAG,EAAAP,EAAAC,OAAAE,yBAAAb,EAAAc,CAAA,EAAA,KAAAG,IAAAA,EAAAV,KAAAU,EAAAC,KAAAP,OAAAC,eAAAJ,EAAAM,EAAAG,CAAA,EAAAT,EAAAM,CAAA,EAAAd,EAAAc,CAAA,CAAA,CAAA,OAAAN,EAAAH,QAAAL,EAAAG,GAAAA,EAAAe,IAAAlB,EAAAQ,CAAA,EAAAA,CAAA,CAGnD,IAAMW,GACJ,OAAOC,SAAY,WAClBA,QAAQC,IAAIC,cAAgB,KAAOF,QAAQC,IAAIC,cAAgB,YAC5DC,GAAAA,cAAa,EAAK,EAClBC,GAAAA,QAEAC,GACJA,CAACC,EAAGC,IAAMC,GACRF,EAAEC,EAAEC,CAAC,CAAC,EAWJC,GAAoB,IAAIC,IAAI,CAAC,KAAM,QAAS,OAAQ,MAAO,KAAM,KAAK,CAAC,EAoB7E,SAASC,GAAQZ,EAAsD,CACrE,MAAO,CACLa,QAASb,EAAOc,KAChBC,YAAaf,EAAOgB,OACpBC,cAAejB,EAAOgB,OACtBE,WAAYlB,EAAOgB,OACnBG,OAAQnB,EAAOoB,QACfC,OAAQrB,EAAOsB,MACfC,MAAOvB,EAAOoB,QACdI,QAASxB,EAAOyB,KAChBC,QAASpB,GAAQA,GAAQN,EAAO2B,MAAO3B,EAAO4B,KAAK,EAAG5B,EAAO6B,IAAI,CACnE,CACF,CAKA,IAAMC,GAAU,0BAKVC,GAAU,cAEZC,GA+FG,CAIL,IAAMC,EAAU,iBAIVC,EAAe,SAAUC,EAAYC,EAAgBC,EAAc,CACvE,GAAIF,EAAMG,OAAS,OAAQ,CACzB,MACEC,GAAAA,WAAUJ,EAAMK,KAAK,MACrBC,GAAAA,sBAAqBN,EAAMK,MAAO,EAAI,GACtC9B,GAAkBvB,IAAIgD,EAAMK,KAAK,EAEjC,MAAO,UAGT,GACEP,EAAQS,KAAKP,EAAMK,KAAK,IACvBH,EAAKD,EAAS,CAAC,IAAM,KAAOC,EAAKM,MAAMP,EAAS,EAAGA,CAAM,IAAM,MAEhE,MAAO,gBAGT,GAAID,EAAMK,MAAM,CAAC,IAAML,EAAMK,MAAM,CAAC,EAAEI,YAAY,EAChD,MAAO,aAEX,CAEA,OAAIT,EAAMG,OAAS,cAAgBP,GAAQW,KAAKP,EAAMK,KAAK,EAClD,UAIPL,EAAMG,OAAS,YACdH,EAAMK,QAAU,KAAOL,EAAMK,QAAU,KAEjC,aAGFL,EAAMG,IACf,EAEAN,GAAW,UAAWK,EAAc,CAClC,IAAIQ,EACJ,KAAQA,EAASC,GAAiB5D,QAAQ6D,KAAKV,CAAI,GAAI,CACrD,IAAMF,EAASW,GAAiBE,aAAaH,CAAK,EAElD,KAAM,CACJP,KAAMJ,EAAaC,EAAOU,EAAMI,MAAOZ,CAAI,EAC3CG,MAAOL,EAAMK,KACf,CACF,CACF,CACF,CAKA,SAASU,GAAgBC,EAAiCd,EAAc,CACtE,IAAIe,EAAc,GAElB,OAAW,CAAEd,KAAAA,EAAME,MAAAA,CAAM,IAAKR,GAASK,CAAI,EAAG,CAC5C,IAAMgB,EAAWF,EAAKb,CAAI,EACtBe,EACFD,GAAeZ,EACZc,MAAMxB,EAAO,EACbyB,IAAIC,GAAOH,EAASG,CAAG,CAAC,EACxBC,KAAK;CAAI,EAEZL,GAAeZ,CAEnB,CAEA,OAAOY,CACT,CAaO,SAASM,GAAgBC,EAA2B,CACzD,OAAO3D,GAAO4D,kBAAoBD,EAAQE,UAC5C,CAEA,IAAIC,GACJ,SAASC,GAAUF,EAAqB,CACtC,GAAIA,EAAY,CAAA,IAAAG,EACd,OAAAA,EAAAF,KAAiB,OAAjBA,MAAsB1D,GAAAA,cAAa,EAAI,GAChC0D,EACT,CACA,OAAO9D,EACT,CAKe,SAASiE,GAAUC,EAAcP,EAAmB,CAAC,EAAW,CAC7E,GAAIO,IAAS,IAAMR,GAAgBC,CAAO,EAAG,CAC3C,IAAMR,EAAOvC,GAAQmD,GAAUJ,EAAQE,UAAU,CAAC,EAClD,OAAOX,GAAgBC,EAAMe,CAAI,CACnC,KACE,QAAOA,CAEX,CAEiE,CAC/D,IAAIC,EAAYC,EAEhBC,GAAQC,SAAW,CAAC,CAAET,WAAAA,CAAoB,IAAM,CAAA,IAAAU,EAG9C,IADAA,EAAAJ,IAAK,OAALA,EAAU3F,MACNqF,EAAY,CAAA,IAAAW,EACd,OAAAA,EAAAJ,IAAoB,OAApBA,EAAyB,IAAID,EAAMM,YAAY,CAC7CC,QAAS,GACTC,MAAO,CACT,CAAC,GACMP,CACT,CACA,OAAOD,CACT,CACF,IC1SA,IAAAS,GAAAC,EAAAC,IAAA,cAEA,OAAO,eAAeA,GAAS,aAAc,CAC3C,MAAO,EACT,CAAC,EACDA,GAAQ,iBAAmBC,GAC3BD,GAAQ,QAAUE,GAElB,IAAIC,GAAaC,GAAwB,IAA2B,EAEpE,SAASC,IAA2B,CAAE,GAAI,OAAO,SAAY,WAAY,OAAO,KAAM,IAAIC,EAAQ,IAAI,QAAW,OAAAD,GAA2B,UAAY,CAAE,OAAOC,CAAO,EAAUA,CAAO,CAEzL,SAASF,GAAwBG,EAAK,CAAE,GAAIA,GAAOA,EAAI,WAAc,OAAOA,EAAO,GAAIA,IAAQ,MAAQ,OAAOA,GAAQ,UAAY,OAAOA,GAAQ,WAAc,MAAO,CAAE,QAASA,CAAI,EAAK,IAAID,EAAQD,GAAyB,EAAG,GAAIC,GAASA,EAAM,IAAIC,CAAG,EAAK,OAAOD,EAAM,IAAIC,CAAG,EAAK,IAAIC,EAAS,CAAC,EAAOC,EAAwB,OAAO,gBAAkB,OAAO,yBAA0B,QAASC,KAAOH,EAAO,GAAI,OAAO,UAAU,eAAe,KAAKA,EAAKG,CAAG,EAAG,CAAE,IAAIC,EAAOF,EAAwB,OAAO,yBAAyBF,EAAKG,CAAG,EAAI,KAAUC,IAASA,EAAK,KAAOA,EAAK,KAAQ,OAAO,eAAeH,EAAQE,EAAKC,CAAI,EAAYH,EAAOE,CAAG,EAAIH,EAAIG,CAAG,CAAK,CAAI,OAAAF,EAAO,QAAUD,EAASD,GAASA,EAAM,IAAIC,EAAKC,CAAM,EAAYA,CAAQ,CAEtuB,IAAII,GAA0B,GAE9B,SAASC,GAAQC,EAAO,CACtB,MAAO,CACL,OAAQA,EAAM,KACd,OAAQA,EAAM,IAAI,KAClB,QAASA,EAAM,IAAI,IACrB,CACF,CAEA,IAAMC,GAAU,0BAEhB,SAASC,GAAeC,EAAKC,EAAQC,EAAM,CACzC,IAAMC,EAAW,OAAO,OAAO,CAC7B,OAAQ,EACR,KAAM,EACR,EAAGH,EAAI,KAAK,EACNI,EAAS,OAAO,OAAO,CAAC,EAAGD,EAAUH,EAAI,GAAG,EAC5C,CACJ,WAAAK,EAAa,EACb,WAAAC,EAAa,CACf,EAAIJ,GAAQ,CAAC,EACPK,EAAYJ,EAAS,KACrBK,EAAcL,EAAS,OACvBM,EAAUL,EAAO,KACjBM,EAAYN,EAAO,OACrBO,EAAQ,KAAK,IAAIJ,GAAaF,EAAa,GAAI,CAAC,EAChDO,EAAM,KAAK,IAAIX,EAAO,OAAQQ,EAAUH,CAAU,EAElDC,IAAc,KAChBI,EAAQ,GAGNF,IAAY,KACdG,EAAMX,EAAO,QAGf,IAAMY,EAAWJ,EAAUF,EACrBO,EAAc,CAAC,EAErB,GAAID,EACF,QAASE,EAAI,EAAGA,GAAKF,EAAUE,IAAK,CAClC,IAAMC,EAAaD,EAAIR,EAEvB,GAAI,CAACC,EACHM,EAAYE,CAAU,EAAI,WACjBD,IAAM,EAAG,CAClB,IAAME,EAAehB,EAAOe,EAAa,CAAC,EAAE,OAC5CF,EAAYE,CAAU,EAAI,CAACR,EAAaS,EAAeT,EAAc,CAAC,CACxE,SAAWO,IAAMF,EACfC,EAAYE,CAAU,EAAI,CAAC,EAAGN,CAAS,MAClC,CACL,IAAMO,EAAehB,EAAOe,EAAaD,CAAC,EAAE,OAC5CD,EAAYE,CAAU,EAAI,CAAC,EAAGC,CAAY,CAC5C,CACF,MAEIT,IAAgBE,EACdF,EACFM,EAAYP,CAAS,EAAI,CAACC,EAAa,CAAC,EAExCM,EAAYP,CAAS,EAAI,GAG3BO,EAAYP,CAAS,EAAI,CAACC,EAAaE,EAAYF,CAAW,EAIlE,MAAO,CACL,MAAAG,EACA,IAAAC,EACA,YAAAE,CACF,CACF,CAEA,SAAS9B,GAAiBkC,EAAUlB,EAAKE,EAAO,CAAC,EAAG,CAClD,IAAMiB,GAAejB,EAAK,eAAiBA,EAAK,gBAAmBhB,GAAW,iBAAiBgB,CAAI,EAC7FL,KAAYX,GAAW,UAAUgB,CAAI,EACrCkB,EAAOxB,GAAQC,CAAK,EAEpBwB,EAAiB,CAACC,EAASC,IACxBJ,EAAcG,EAAQC,CAAM,EAAIA,EAGnCC,EAAQN,EAAS,MAAMpB,EAAO,EAC9B,CACJ,MAAAa,EACA,IAAAC,EACA,YAAAE,CACF,EAAIf,GAAeC,EAAKwB,EAAOtB,CAAI,EAC7BuB,EAAazB,EAAI,OAAS,OAAOA,EAAI,MAAM,QAAW,SACtD0B,EAAiB,OAAOd,CAAG,EAAE,OAE/Be,GADqBR,KAAkBjC,GAAW,SAASgC,EAAUhB,CAAI,EAAIgB,GACpD,MAAMpB,EAAO,EAAE,MAAMa,EAAOC,CAAG,EAAE,IAAI,CAACgB,EAAMC,IAAU,CACjF,IAAMC,EAASnB,EAAQ,EAAIkB,EAErBE,EAAS,IADM,IAAID,CAAM,GAAG,MAAM,CAACJ,CAAc,CACxB,MACzBM,GAAYlB,EAAYgB,CAAM,EAC9BG,GAAiB,CAACnB,EAAYgB,EAAS,CAAC,EAE9C,GAAIE,GAAW,CACb,IAAIE,EAAa,GAEjB,GAAI,MAAM,QAAQF,EAAS,EAAG,CAC5B,IAAMG,EAAgBP,EAAK,MAAM,EAAG,KAAK,IAAII,GAAU,CAAC,EAAI,EAAG,CAAC,CAAC,EAAE,QAAQ,SAAU,GAAG,EAClFI,EAAkBJ,GAAU,CAAC,GAAK,EACxCE,EAAa,CAAC;AAAA,GAAOb,EAAeD,EAAK,OAAQW,EAAO,QAAQ,MAAO,GAAG,CAAC,EAAGI,EAAed,EAAeD,EAAK,OAAQ,GAAG,EAAE,OAAOgB,CAAe,CAAC,EAAE,KAAK,EAAE,EAE1JH,IAAkB/B,EAAK,UACzBgC,GAAc,IAAMb,EAAeD,EAAK,QAASlB,EAAK,OAAO,EAEjE,CAEA,MAAO,CAACmB,EAAeD,EAAK,OAAQ,GAAG,EAAGC,EAAeD,EAAK,OAAQW,CAAM,EAAGH,EAAMM,CAAU,EAAE,KAAK,EAAE,CAC1G,KACE,OAAO,IAAIb,EAAeD,EAAK,OAAQW,CAAM,CAAC,GAAGH,CAAI,EAEzD,CAAC,EAAE,KAAK;AAAA,CAAI,EAMZ,OAJI1B,EAAK,SAAW,CAACuB,IACnBE,EAAQ,GAAG,IAAI,OAAOD,EAAiB,CAAC,CAAC,GAAGxB,EAAK,OAAO;AAAA,EAAKyB,CAAK,IAGhER,EACKtB,EAAM,MAAM8B,CAAK,EAEjBA,CAEX,CAEA,SAAS1C,GAASiC,EAAUF,EAAYqB,EAAWnC,EAAO,CAAC,EAAG,CAC5D,GAAI,CAACP,GAAyB,CAC5BA,GAA0B,GAC1B,IAAM2C,EAAU,sGAEhB,GAAI,QAAQ,YACV,QAAQ,YAAYA,EAAS,oBAAoB,MAC5C,CACL,IAAMC,EAAmB,IAAI,MAAMD,CAAO,EAC1CC,EAAiB,KAAO,qBACxB,QAAQ,KAAK,IAAI,MAAMD,CAAO,CAAC,CACjC,CACF,CAEA,OAAAD,EAAY,KAAK,IAAIA,EAAW,CAAC,EAO1BrD,GAAiBkC,EANP,CACf,MAAO,CACL,OAAQmB,EACR,KAAMrB,CACR,CACF,EAC4Cd,CAAI,CAClD,ICtKA,IAAAsC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAMC,GAAU,KACVC,GAAW,KACX,CAAC,QAASC,EAAe,EAAI,KAC7B,CAAC,iBAAAC,EAAgB,EAAI,KAErBC,GAAYJ,GAAQ,YAAa,CACtC,SAAUA,GAAQ,OAAO,OAAO,EAChC,UAAWA,GAAQ,OAAO;AAAA;AAAA;AAAA,CAAU,CACrC,CAAC,EAEKK,GAAY,CAACC,EAAQC,EAASC,IAAa,CAC5C,OAAOD,GAAY,WACtBC,EAAWD,EACXA,EAAU,MAGX,GAAI,CACH,GAAI,CACH,OAAO,KAAK,MAAMD,EAAQC,CAAO,CAClC,OAASE,EAAO,CACf,MAAAR,GAASK,EAAQC,CAAO,EAClBE,CACP,CACD,OAASA,EAAO,CACfA,EAAM,QAAUA,EAAM,QAAQ,QAAQ,MAAO,EAAE,EAC/C,IAAMC,EAAaD,EAAM,QAAQ,MAAM,yCAAyC,EAE1EE,EAAY,IAAIP,GAAUK,CAAK,EAKrC,GAJID,IACHG,EAAU,SAAWH,GAGlBE,GAAcA,EAAW,OAAS,EAAG,CACxC,IAAME,EAAQ,IAAIV,GAAgBI,CAAM,EAClCO,EAAQ,OAAOH,EAAW,CAAC,CAAC,EAC5BI,EAAWF,EAAM,iBAAiBC,CAAK,EAEvCE,EAAYZ,GACjBG,EACA,CAAC,MAAO,CAAC,KAAMQ,EAAS,KAAO,EAAG,OAAQA,EAAS,OAAS,CAAC,CAAC,EAC9D,CAAC,cAAe,EAAI,CACrB,EAEAH,EAAU,UAAYI,CACvB,CAEA,MAAMJ,CACP,CACD,EAEAN,GAAU,UAAYD,GAEtBL,GAAO,QAAUM,KCrDjB,IAAAW,GAAAC,EAAA,CAAAC,EAAAC,KAAA,CAAAD,EAAUC,GAAO,QAAUC,EAE3B,IAAIC,EAEA,OAAO,SAAY,UACnB,QAAQ,KACR,QAAQ,IAAI,YACZ,cAAc,KAAK,QAAQ,IAAI,UAAU,EAC3CA,EAAQ,UAAY,CAClB,IAAIC,EAAO,MAAM,UAAU,MAAM,KAAK,UAAW,CAAC,EAClDA,EAAK,QAAQ,QAAQ,EACrB,QAAQ,IAAI,MAAM,QAASA,CAAI,CACjC,EAEAD,EAAQ,UAAY,CAAC,EAKvBH,EAAQ,oBAAsB,QAE9B,IAAIK,GAAa,IACbC,GAAmB,OAAO,kBACD,iBAGzBC,GAA4B,GAE5BC,GAAwBH,GAAa,EAGrCI,GAAKT,EAAQ,GAAK,CAAC,EACnBU,EAASV,EAAQ,OAAS,CAAC,EAC3BW,EAAMX,EAAQ,IAAM,CAAC,EACrBY,EAAI,EAEJC,GAAmB,eAQnBC,GAAwB,CAC1B,CAAC,MAAO,CAAC,EACT,CAAC,MAAOT,EAAU,EAClB,CAACQ,GAAkBL,EAAqB,CAC1C,EAEA,SAASO,GAAYC,EAAO,CAC1B,QAASC,EAAI,EAAGA,EAAIH,GAAsB,OAAQG,IAAK,CACrD,IAAIC,EAAQJ,GAAsBG,CAAC,EAAE,CAAC,EAClCE,EAAML,GAAsBG,CAAC,EAAE,CAAC,EACpCD,EAAQA,EACL,MAAME,EAAQ,GAAG,EAAE,KAAKA,EAAQ,MAAQC,EAAM,GAAG,EACjD,MAAMD,EAAQ,GAAG,EAAE,KAAKA,EAAQ,MAAQC,EAAM,GAAG,CACtD,CACA,OAAOH,CACT,CAQA,IAAII,GAAoBR,IACxBD,EAAIS,EAAiB,EAAI,cACzB,IAAIC,GAAyBT,IAC7BD,EAAIU,EAAsB,EAAI,OAM9B,IAAIC,GAAuBV,IAC3BD,EAAIW,EAAoB,EAAI,gBAAkBT,GAAmB,IAKjE,IAAIU,GAAcX,IAClBD,EAAIY,EAAW,EAAI,IAAMZ,EAAIS,EAAiB,EAAI,QACzBT,EAAIS,EAAiB,EAAI,QACzBT,EAAIS,EAAiB,EAAI,IAElD,IAAII,GAAmBZ,IACvBD,EAAIa,EAAgB,EAAI,IAAMb,EAAIU,EAAsB,EAAI,QAC9BV,EAAIU,EAAsB,EAAI,QAC9BV,EAAIU,EAAsB,EAAI,IAK5D,IAAII,GAAuBb,IAC3BD,EAAIc,EAAoB,EAAI,MAAQd,EAAIS,EAAiB,EAC7B,IAAMT,EAAIW,EAAoB,EAAI,IAE9D,IAAII,GAA4Bd,IAChCD,EAAIe,EAAyB,EAAI,MAAQf,EAAIU,EAAsB,EAClC,IAAMV,EAAIW,EAAoB,EAAI,IAMnE,IAAIK,GAAaf,IACjBD,EAAIgB,EAAU,EAAI,QAAUhB,EAAIc,EAAoB,EAClC,SAAWd,EAAIc,EAAoB,EAAI,OAEzD,IAAIG,GAAkBhB,IACtBD,EAAIiB,EAAe,EAAI,SAAWjB,EAAIe,EAAyB,EACxC,SAAWf,EAAIe,EAAyB,EAAI,OAKnE,IAAIG,GAAkBjB,IACtBD,EAAIkB,EAAe,EAAIhB,GAAmB,IAM1C,IAAIiB,GAAQlB,IACZD,EAAImB,EAAK,EAAI,UAAYnB,EAAIkB,EAAe,EAC/B,SAAWlB,EAAIkB,EAAe,EAAI,OAW/C,IAAIE,GAAOnB,IACPoB,GAAY,KAAOrB,EAAIY,EAAW,EACtBZ,EAAIgB,EAAU,EAAI,IAClBhB,EAAImB,EAAK,EAAI,IAE7BnB,EAAIoB,EAAI,EAAI,IAAMC,GAAY,IAK9B,IAAIC,GAAa,WAAatB,EAAIa,EAAgB,EACjCb,EAAIiB,EAAe,EAAI,IACvBjB,EAAImB,EAAK,EAAI,IAE1BI,GAAQtB,IACZD,EAAIuB,EAAK,EAAI,IAAMD,GAAa,IAEhC,IAAIE,GAAOvB,IACXD,EAAIwB,EAAI,EAAI,eAKZ,IAAIC,GAAwBxB,IAC5BD,EAAIyB,EAAqB,EAAIzB,EAAIU,EAAsB,EAAI,WAC3D,IAAIgB,GAAmBzB,IACvBD,EAAI0B,EAAgB,EAAI1B,EAAIS,EAAiB,EAAI,WAEjD,IAAIkB,GAAc1B,IAClBD,EAAI2B,EAAW,EAAI,YAAc3B,EAAI0B,EAAgB,EAAI,WAC1B1B,EAAI0B,EAAgB,EAAI,WACxB1B,EAAI0B,EAAgB,EAAI,OAC5B1B,EAAIgB,EAAU,EAAI,KAC1BhB,EAAImB,EAAK,EAAI,QAGhC,IAAIS,GAAmB3B,IACvBD,EAAI4B,EAAgB,EAAI,YAAc5B,EAAIyB,EAAqB,EAAI,WAC/BzB,EAAIyB,EAAqB,EAAI,WAC7BzB,EAAIyB,EAAqB,EAAI,OACjCzB,EAAIiB,EAAe,EAAI,KAC/BjB,EAAImB,EAAK,EAAI,QAGrC,IAAIU,GAAS5B,IACbD,EAAI6B,EAAM,EAAI,IAAM7B,EAAIwB,EAAI,EAAI,OAASxB,EAAI2B,EAAW,EAAI,IAC5D,IAAIG,GAAc7B,IAClBD,EAAI8B,EAAW,EAAI,IAAM9B,EAAIwB,EAAI,EAAI,OAASxB,EAAI4B,EAAgB,EAAI,IAItE,IAAIG,GAAS9B,IACbD,EAAI+B,EAAM,EAAI,sBACYnC,GAA4B,kBACtBA,GAA4B,oBAC5BA,GAA4B,mBAK5D,IAAIoC,GAAY/B,IAChBD,EAAIgC,EAAS,EAAI,UAEjB,IAAIC,GAAYhC,IAChBD,EAAIiC,EAAS,EAAI,SAAWjC,EAAIgC,EAAS,EAAI,OAC7ClC,GAAGmC,EAAS,EAAI,IAAI,OAAOjC,EAAIiC,EAAS,EAAG,GAAG,EAC9ClC,EAAOkC,EAAS,EAAI,IAAI,OAAO7B,GAAWJ,EAAIiC,EAAS,CAAC,EAAG,GAAG,EAC9D,IAAIC,GAAmB,MAEnBC,GAAQlC,IACZD,EAAImC,EAAK,EAAI,IAAMnC,EAAIgC,EAAS,EAAIhC,EAAI2B,EAAW,EAAI,IACvD,IAAIS,GAAanC,IACjBD,EAAIoC,EAAU,EAAI,IAAMpC,EAAIgC,EAAS,EAAIhC,EAAI4B,EAAgB,EAAI,IAIjE,IAAIS,GAAYpC,IAChBD,EAAIqC,EAAS,EAAI,UAEjB,IAAIC,GAAYrC,IAChBD,EAAIsC,EAAS,EAAI,SAAWtC,EAAIqC,EAAS,EAAI,OAC7CvC,GAAGwC,EAAS,EAAI,IAAI,OAAOtC,EAAIsC,EAAS,EAAG,GAAG,EAC9CvC,EAAOuC,EAAS,EAAI,IAAI,OAAOlC,GAAWJ,EAAIsC,EAAS,CAAC,EAAG,GAAG,EAC9D,IAAIC,GAAmB,MAEnBC,GAAQvC,IACZD,EAAIwC,EAAK,EAAI,IAAMxC,EAAIqC,EAAS,EAAIrC,EAAI2B,EAAW,EAAI,IACvD,IAAIc,GAAaxC,IACjBD,EAAIyC,EAAU,EAAI,IAAMzC,EAAIqC,EAAS,EAAIrC,EAAI4B,EAAgB,EAAI,IAGjE,IAAIc,GAAkBzC,IACtBD,EAAI0C,EAAe,EAAI,IAAM1C,EAAIwB,EAAI,EAAI,QAAUF,GAAa,QAChE,IAAIqB,GAAa1C,IACjBD,EAAI2C,EAAU,EAAI,IAAM3C,EAAIwB,EAAI,EAAI,QAAUH,GAAY,QAI1D,IAAIuB,GAAiB3C,IACrBD,EAAI4C,EAAc,EAAI,SAAW5C,EAAIwB,EAAI,EACnB,QAAUF,GAAa,IAAMtB,EAAI2B,EAAW,EAAI,IAGtE7B,GAAG8C,EAAc,EAAI,IAAI,OAAO5C,EAAI4C,EAAc,EAAG,GAAG,EACxD7C,EAAO6C,EAAc,EAAI,IAAI,OAAOxC,GAAWJ,EAAI4C,EAAc,CAAC,EAAG,GAAG,EACxE,IAAIC,GAAwB,SAMxBC,GAAc7C,IAClBD,EAAI8C,EAAW,EAAI,SAAW9C,EAAI2B,EAAW,EAAI,cAExB3B,EAAI2B,EAAW,EAAI,SAG5C,IAAIoB,GAAmB9C,IACvBD,EAAI+C,EAAgB,EAAI,SAAW/C,EAAI4B,EAAgB,EAAI,cAE7B5B,EAAI4B,EAAgB,EAAI,SAItD,IAAIoB,GAAO/C,IACXD,EAAIgD,EAAI,EAAI,kBAIZ,IAAS1C,GAAI,EAAGA,GAAIL,EAAGK,KACrBd,EAAMc,GAAGN,EAAIM,EAAC,CAAC,EACVR,GAAGQ,EAAC,IACPR,GAAGQ,EAAC,EAAI,IAAI,OAAON,EAAIM,EAAC,CAAC,EAQzBP,EAAOO,EAAC,EAAI,IAAI,OAAOF,GAAWJ,EAAIM,EAAC,CAAC,CAAC,GAXpC,IAAAA,GAeTjB,EAAQ,MAAQ4D,GAChB,SAASA,GAAOC,EAASC,EAAS,CAQhC,IAPI,CAACA,GAAW,OAAOA,GAAY,YACjCA,EAAU,CACR,MAAO,CAAC,CAACA,EACT,kBAAmB,EACrB,GAGED,aAAmB3D,EACrB,OAAO2D,EAOT,GAJI,OAAOA,GAAY,UAInBA,EAAQ,OAASxD,GACnB,OAAO,KAGT,IAAI0D,EAAID,EAAQ,MAAQpD,EAAOwB,EAAK,EAAIxB,EAAOqB,EAAI,EACnD,GAAI,CAACgC,EAAE,KAAKF,CAAO,EACjB,OAAO,KAGT,GAAI,CACF,OAAO,IAAI3D,EAAO2D,EAASC,CAAO,CACpC,MAAa,CACX,OAAO,IACT,CACF,CAEA9D,EAAQ,MAAQgE,GAChB,SAASA,GAAOH,EAASC,EAAS,CAChC,IAAIG,EAAIL,GAAMC,EAASC,CAAO,EAC9B,OAAOG,EAAIA,EAAE,QAAU,IACzB,CAEAjE,EAAQ,MAAQkE,GAChB,SAASA,GAAOL,EAASC,EAAS,CAChC,IAAIK,EAAIP,GAAMC,EAAQ,KAAK,EAAE,QAAQ,SAAU,EAAE,EAAGC,CAAO,EAC3D,OAAOK,EAAIA,EAAE,QAAU,IACzB,CAEAnE,EAAQ,OAASE,EAEjB,SAASA,EAAQ2D,EAASC,EAAS,CAOjC,IANI,CAACA,GAAW,OAAOA,GAAY,YACjCA,EAAU,CACR,MAAO,CAAC,CAACA,EACT,kBAAmB,EACrB,GAEED,aAAmB3D,EAAQ,CAC7B,GAAI2D,EAAQ,QAAUC,EAAQ,MAC5B,OAAOD,EAEPA,EAAUA,EAAQ,OAEtB,SAAW,OAAOA,GAAY,SAC5B,MAAM,IAAI,UAAU,oBAAsBA,CAAO,EAGnD,GAAIA,EAAQ,OAASxD,GACnB,MAAM,IAAI,UAAU,0BAA4BA,GAAa,aAAa,EAG5E,GAAI,EAAE,gBAAgBH,GACpB,OAAO,IAAIA,EAAO2D,EAASC,CAAO,EAGpC3D,EAAM,SAAU0D,EAASC,CAAO,EAChC,KAAK,QAAUA,EACf,KAAK,MAAQ,CAAC,CAACA,EAAQ,MAEvB,IAAIM,EAAIP,EAAQ,KAAK,EAAE,MAAMC,EAAQ,MAAQpD,EAAOwB,EAAK,EAAIxB,EAAOqB,EAAI,CAAC,EAEzE,GAAI,CAACqC,EACH,MAAM,IAAI,UAAU,oBAAsBP,CAAO,EAUnD,GAPA,KAAK,IAAMA,EAGX,KAAK,MAAQ,CAACO,EAAE,CAAC,EACjB,KAAK,MAAQ,CAACA,EAAE,CAAC,EACjB,KAAK,MAAQ,CAACA,EAAE,CAAC,EAEb,KAAK,MAAQ9D,IAAoB,KAAK,MAAQ,EAChD,MAAM,IAAI,UAAU,uBAAuB,EAG7C,GAAI,KAAK,MAAQA,IAAoB,KAAK,MAAQ,EAChD,MAAM,IAAI,UAAU,uBAAuB,EAG7C,GAAI,KAAK,MAAQA,IAAoB,KAAK,MAAQ,EAChD,MAAM,IAAI,UAAU,uBAAuB,EAIxC8D,EAAE,CAAC,EAGN,KAAK,WAAaA,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,SAAUC,EAAI,CAClD,GAAI,WAAW,KAAKA,CAAE,EAAG,CACvB,IAAIC,EAAM,CAACD,EACX,GAAIC,GAAO,GAAKA,EAAMhE,GACpB,OAAOgE,CAEX,CACA,OAAOD,CACT,CAAC,EAVD,KAAK,WAAa,CAAC,EAarB,KAAK,MAAQD,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAE,MAAM,GAAG,EAAI,CAAC,EACvC,KAAK,OAAO,CACd,CAEAlE,EAAO,UAAU,OAAS,UAAY,CACpC,YAAK,QAAU,KAAK,MAAQ,IAAM,KAAK,MAAQ,IAAM,KAAK,MACtD,KAAK,WAAW,SAClB,KAAK,SAAW,IAAM,KAAK,WAAW,KAAK,GAAG,GAEzC,KAAK,OACd,EAEAA,EAAO,UAAU,SAAW,UAAY,CACtC,OAAO,KAAK,OACd,EAEAA,EAAO,UAAU,QAAU,SAAUqE,EAAO,CAC1C,OAAApE,EAAM,iBAAkB,KAAK,QAAS,KAAK,QAASoE,CAAK,EACnDA,aAAiBrE,IACrBqE,EAAQ,IAAIrE,EAAOqE,EAAO,KAAK,OAAO,GAGjC,KAAK,YAAYA,CAAK,GAAK,KAAK,WAAWA,CAAK,CACzD,EAEArE,EAAO,UAAU,YAAc,SAAUqE,EAAO,CAC9C,OAAMA,aAAiBrE,IACrBqE,EAAQ,IAAIrE,EAAOqE,EAAO,KAAK,OAAO,GAGjCC,GAAmB,KAAK,MAAOD,EAAM,KAAK,GAC1CC,GAAmB,KAAK,MAAOD,EAAM,KAAK,GAC1CC,GAAmB,KAAK,MAAOD,EAAM,KAAK,CACnD,EAEArE,EAAO,UAAU,WAAa,SAAUqE,EAAO,CAM7C,GALMA,aAAiBrE,IACrBqE,EAAQ,IAAIrE,EAAOqE,EAAO,KAAK,OAAO,GAIpC,KAAK,WAAW,QAAU,CAACA,EAAM,WAAW,OAC9C,MAAO,GACF,GAAI,CAAC,KAAK,WAAW,QAAUA,EAAM,WAAW,OACrD,MAAO,GACF,GAAI,CAAC,KAAK,WAAW,QAAU,CAACA,EAAM,WAAW,OACtD,MAAO,GAGT,IAAItD,EAAI,EACR,EAAG,CACD,IAAIwD,EAAI,KAAK,WAAWxD,CAAC,EACrByD,EAAIH,EAAM,WAAWtD,CAAC,EAE1B,GADAd,EAAM,qBAAsBc,EAAGwD,EAAGC,CAAC,EAC/BD,IAAM,QAAaC,IAAM,OAC3B,MAAO,GACF,GAAIA,IAAM,OACf,MAAO,GACF,GAAID,IAAM,OACf,MAAO,GACF,GAAIA,IAAMC,EACf,SAEA,OAAOF,GAAmBC,EAAGC,CAAC,CAElC,OAAS,EAAEzD,EACb,EAIAf,EAAO,UAAU,IAAM,SAAUyE,EAASC,EAAY,CACpD,OAAQD,EAAS,CACf,IAAK,WACH,KAAK,WAAW,OAAS,EACzB,KAAK,MAAQ,EACb,KAAK,MAAQ,EACb,KAAK,QACL,KAAK,IAAI,MAAOC,CAAU,EAC1B,MACF,IAAK,WACH,KAAK,WAAW,OAAS,EACzB,KAAK,MAAQ,EACb,KAAK,QACL,KAAK,IAAI,MAAOA,CAAU,EAC1B,MACF,IAAK,WAIH,KAAK,WAAW,OAAS,EACzB,KAAK,IAAI,QAASA,CAAU,EAC5B,KAAK,IAAI,MAAOA,CAAU,EAC1B,MAGF,IAAK,aACC,KAAK,WAAW,SAAW,GAC7B,KAAK,IAAI,QAASA,CAAU,EAE9B,KAAK,IAAI,MAAOA,CAAU,EAC1B,MAEF,IAAK,SAKC,KAAK,QAAU,GACf,KAAK,QAAU,GACf,KAAK,WAAW,SAAW,IAC7B,KAAK,QAEP,KAAK,MAAQ,EACb,KAAK,MAAQ,EACb,KAAK,WAAa,CAAC,EACnB,MACF,IAAK,SAKC,KAAK,QAAU,GAAK,KAAK,WAAW,SAAW,IACjD,KAAK,QAEP,KAAK,MAAQ,EACb,KAAK,WAAa,CAAC,EACnB,MACF,IAAK,QAKC,KAAK,WAAW,SAAW,GAC7B,KAAK,QAEP,KAAK,WAAa,CAAC,EACnB,MAGF,IAAK,MACH,GAAI,KAAK,WAAW,SAAW,EAC7B,KAAK,WAAa,CAAC,CAAC,MACf,CAEL,QADI3D,EAAI,KAAK,WAAW,OACjB,EAAEA,GAAK,GACR,OAAO,KAAK,WAAWA,CAAC,GAAM,WAChC,KAAK,WAAWA,CAAC,IACjBA,EAAI,IAGJA,IAAM,IAER,KAAK,WAAW,KAAK,CAAC,CAE1B,CACI2D,IAGE,KAAK,WAAW,CAAC,IAAMA,EACrB,MAAM,KAAK,WAAW,CAAC,CAAC,IAC1B,KAAK,WAAa,CAACA,EAAY,CAAC,GAGlC,KAAK,WAAa,CAACA,EAAY,CAAC,GAGpC,MAEF,QACE,MAAM,IAAI,MAAM,+BAAiCD,CAAO,CAC5D,CACA,YAAK,OAAO,EACZ,KAAK,IAAM,KAAK,QACT,IACT,EAEA3E,EAAQ,IAAM6E,GACd,SAASA,GAAKhB,EAASc,EAASG,EAAOF,EAAY,CAC7C,OAAQE,GAAW,WACrBF,EAAaE,EACbA,EAAQ,QAGV,GAAI,CACF,OAAO,IAAI5E,EAAO2D,EAASiB,CAAK,EAAE,IAAIH,EAASC,CAAU,EAAE,OAC7D,MAAa,CACX,OAAO,IACT,CACF,CAEA5E,EAAQ,KAAO+E,GACf,SAASA,GAAMC,EAAUC,EAAU,CACjC,GAAIC,GAAGF,EAAUC,CAAQ,EACvB,OAAO,KAEP,IAAIE,EAAKvB,GAAMoB,CAAQ,EACnBI,EAAKxB,GAAMqB,CAAQ,EACnBI,EAAS,GACb,GAAIF,EAAG,WAAW,QAAUC,EAAG,WAAW,OAAQ,CAChDC,EAAS,MACT,IAAIC,EAAgB,YACtB,CACA,QAASC,KAAOJ,EACd,IAAII,IAAQ,SAAWA,IAAQ,SAAWA,IAAQ,UAC5CJ,EAAGI,CAAG,IAAMH,EAAGG,CAAG,EACpB,OAAOF,EAASE,EAItB,OAAOD,CAEX,CAEAtF,EAAQ,mBAAqBwE,GAE7B,IAAIgB,GAAU,WACd,SAAShB,GAAoBC,EAAGC,EAAG,CACjC,IAAIe,EAAOD,GAAQ,KAAKf,CAAC,EACrBiB,EAAOF,GAAQ,KAAKd,CAAC,EAEzB,OAAIe,GAAQC,IACVjB,EAAI,CAACA,EACLC,EAAI,CAACA,GAGAD,IAAMC,EAAI,EACZe,GAAQ,CAACC,EAAQ,GACjBA,GAAQ,CAACD,EAAQ,EAClBhB,EAAIC,EAAI,GACR,CACN,CAEA1E,EAAQ,oBAAsB2F,GAC9B,SAASA,GAAqBlB,EAAGC,EAAG,CAClC,OAAOF,GAAmBE,EAAGD,CAAC,CAChC,CAEAzE,EAAQ,MAAQ4F,GAChB,SAASA,GAAOnB,EAAGK,EAAO,CACxB,OAAO,IAAI5E,EAAOuE,EAAGK,CAAK,EAAE,KAC9B,CAEA9E,EAAQ,MAAQ6F,GAChB,SAASA,GAAOpB,EAAGK,EAAO,CACxB,OAAO,IAAI5E,EAAOuE,EAAGK,CAAK,EAAE,KAC9B,CAEA9E,EAAQ,MAAQ8F,GAChB,SAASA,GAAOrB,EAAGK,EAAO,CACxB,OAAO,IAAI5E,EAAOuE,EAAGK,CAAK,EAAE,KAC9B,CAEA9E,EAAQ,QAAU+F,GAClB,SAASA,GAAStB,EAAGC,EAAGI,EAAO,CAC7B,OAAO,IAAI5E,EAAOuE,EAAGK,CAAK,EAAE,QAAQ,IAAI5E,EAAOwE,EAAGI,CAAK,CAAC,CAC1D,CAEA9E,EAAQ,aAAegG,GACvB,SAASA,GAAcvB,EAAGC,EAAG,CAC3B,OAAOqB,GAAQtB,EAAGC,EAAG,EAAI,CAC3B,CAEA1E,EAAQ,SAAWiG,GACnB,SAASA,GAAUxB,EAAGC,EAAGI,EAAO,CAC9B,OAAOiB,GAAQrB,EAAGD,EAAGK,CAAK,CAC5B,CAEA9E,EAAQ,KAAOkG,GACf,SAASA,GAAMC,EAAMrB,EAAO,CAC1B,OAAOqB,EAAK,KAAK,SAAU1B,EAAGC,EAAG,CAC/B,OAAO1E,EAAQ,QAAQyE,EAAGC,EAAGI,CAAK,CACpC,CAAC,CACH,CAEA9E,EAAQ,MAAQoG,GAChB,SAASA,GAAOD,EAAMrB,EAAO,CAC3B,OAAOqB,EAAK,KAAK,SAAU1B,EAAGC,EAAG,CAC/B,OAAO1E,EAAQ,SAASyE,EAAGC,EAAGI,CAAK,CACrC,CAAC,CACH,CAEA9E,EAAQ,GAAKqG,GACb,SAASA,GAAI5B,EAAGC,EAAGI,EAAO,CACxB,OAAOiB,GAAQtB,EAAGC,EAAGI,CAAK,EAAI,CAChC,CAEA9E,EAAQ,GAAKsG,GACb,SAASA,GAAI7B,EAAGC,EAAGI,EAAO,CACxB,OAAOiB,GAAQtB,EAAGC,EAAGI,CAAK,EAAI,CAChC,CAEA9E,EAAQ,GAAKkF,GACb,SAASA,GAAIT,EAAGC,EAAGI,EAAO,CACxB,OAAOiB,GAAQtB,EAAGC,EAAGI,CAAK,IAAM,CAClC,CAEA9E,EAAQ,IAAMuG,GACd,SAASA,GAAK9B,EAAGC,EAAGI,EAAO,CACzB,OAAOiB,GAAQtB,EAAGC,EAAGI,CAAK,IAAM,CAClC,CAEA9E,EAAQ,IAAMwG,GACd,SAASA,GAAK/B,EAAGC,EAAGI,EAAO,CACzB,OAAOiB,GAAQtB,EAAGC,EAAGI,CAAK,GAAK,CACjC,CAEA9E,EAAQ,IAAMyG,GACd,SAASA,GAAKhC,EAAGC,EAAGI,EAAO,CACzB,OAAOiB,GAAQtB,EAAGC,EAAGI,CAAK,GAAK,CACjC,CAEA9E,EAAQ,IAAM0G,GACd,SAASA,GAAKjC,EAAGkC,EAAIjC,EAAGI,EAAO,CAC7B,OAAQ6B,EAAI,CACV,IAAK,MACH,OAAI,OAAOlC,GAAM,WACfA,EAAIA,EAAE,SACJ,OAAOC,GAAM,WACfA,EAAIA,EAAE,SACDD,IAAMC,EAEf,IAAK,MACH,OAAI,OAAOD,GAAM,WACfA,EAAIA,EAAE,SACJ,OAAOC,GAAM,WACfA,EAAIA,EAAE,SACDD,IAAMC,EAEf,IAAK,GACL,IAAK,IACL,IAAK,KACH,OAAOQ,GAAGT,EAAGC,EAAGI,CAAK,EAEvB,IAAK,KACH,OAAOyB,GAAI9B,EAAGC,EAAGI,CAAK,EAExB,IAAK,IACH,OAAOuB,GAAG5B,EAAGC,EAAGI,CAAK,EAEvB,IAAK,KACH,OAAO0B,GAAI/B,EAAGC,EAAGI,CAAK,EAExB,IAAK,IACH,OAAOwB,GAAG7B,EAAGC,EAAGI,CAAK,EAEvB,IAAK,KACH,OAAO2B,GAAIhC,EAAGC,EAAGI,CAAK,EAExB,QACE,MAAM,IAAI,UAAU,qBAAuB6B,CAAE,CACjD,CACF,CAEA3G,EAAQ,WAAa4G,EACrB,SAASA,EAAYC,EAAM/C,EAAS,CAQlC,IAPI,CAACA,GAAW,OAAOA,GAAY,YACjCA,EAAU,CACR,MAAO,CAAC,CAACA,EACT,kBAAmB,EACrB,GAGE+C,aAAgBD,EAAY,CAC9B,GAAIC,EAAK,QAAU,CAAC,CAAC/C,EAAQ,MAC3B,OAAO+C,EAEPA,EAAOA,EAAK,KAEhB,CAEA,GAAI,EAAE,gBAAgBD,GACpB,OAAO,IAAIA,EAAWC,EAAM/C,CAAO,EAGrC+C,EAAOA,EAAK,KAAK,EAAE,MAAM,KAAK,EAAE,KAAK,GAAG,EACxC1G,EAAM,aAAc0G,EAAM/C,CAAO,EACjC,KAAK,QAAUA,EACf,KAAK,MAAQ,CAAC,CAACA,EAAQ,MACvB,KAAK,MAAM+C,CAAI,EAEX,KAAK,SAAWC,GAClB,KAAK,MAAQ,GAEb,KAAK,MAAQ,KAAK,SAAW,KAAK,OAAO,QAG3C3G,EAAM,OAAQ,IAAI,CACpB,CAEA,IAAI2G,GAAM,CAAC,EACXF,EAAW,UAAU,MAAQ,SAAUC,EAAM,CAC3C,IAAI,EAAI,KAAK,QAAQ,MAAQnG,EAAO2C,EAAe,EAAI3C,EAAO4C,EAAU,EACpEc,EAAIyC,EAAK,MAAM,CAAC,EAEpB,GAAI,CAACzC,EACH,MAAM,IAAI,UAAU,uBAAyByC,CAAI,EAGnD,KAAK,SAAWzC,EAAE,CAAC,EACf,KAAK,WAAa,MACpB,KAAK,SAAW,IAIbA,EAAE,CAAC,EAGN,KAAK,OAAS,IAAIlE,EAAOkE,EAAE,CAAC,EAAG,KAAK,QAAQ,KAAK,EAFjD,KAAK,OAAS0C,EAIlB,EAEAF,EAAW,UAAU,SAAW,UAAY,CAC1C,OAAO,KAAK,KACd,EAEAA,EAAW,UAAU,KAAO,SAAU/C,EAAS,CAG7C,OAFA1D,EAAM,kBAAmB0D,EAAS,KAAK,QAAQ,KAAK,EAEhD,KAAK,SAAWiD,GACX,IAGL,OAAOjD,GAAY,WACrBA,EAAU,IAAI3D,EAAO2D,EAAS,KAAK,OAAO,GAGrC6C,GAAI7C,EAAS,KAAK,SAAU,KAAK,OAAQ,KAAK,OAAO,EAC9D,EAEA+C,EAAW,UAAU,WAAa,SAAUC,EAAM/C,EAAS,CACzD,GAAI,EAAE+C,aAAgBD,GACpB,MAAM,IAAI,UAAU,0BAA0B,GAG5C,CAAC9C,GAAW,OAAOA,GAAY,YACjCA,EAAU,CACR,MAAO,CAAC,CAACA,EACT,kBAAmB,EACrB,GAGF,IAAIiD,EAEJ,GAAI,KAAK,WAAa,GACpB,OAAAA,EAAW,IAAIC,EAAMH,EAAK,MAAO/C,CAAO,EACjCmD,GAAU,KAAK,MAAOF,EAAUjD,CAAO,EACzC,GAAI+C,EAAK,WAAa,GAC3B,OAAAE,EAAW,IAAIC,EAAM,KAAK,MAAOlD,CAAO,EACjCmD,GAAUJ,EAAK,OAAQE,EAAUjD,CAAO,EAGjD,IAAIoD,GACD,KAAK,WAAa,MAAQ,KAAK,WAAa,OAC5CL,EAAK,WAAa,MAAQA,EAAK,WAAa,KAC3CM,GACD,KAAK,WAAa,MAAQ,KAAK,WAAa,OAC5CN,EAAK,WAAa,MAAQA,EAAK,WAAa,KAC3CO,EAAa,KAAK,OAAO,UAAYP,EAAK,OAAO,QACjDQ,GACD,KAAK,WAAa,MAAQ,KAAK,WAAa,QAC5CR,EAAK,WAAa,MAAQA,EAAK,WAAa,MAC3CS,EACFZ,GAAI,KAAK,OAAQ,IAAKG,EAAK,OAAQ/C,CAAO,IACxC,KAAK,WAAa,MAAQ,KAAK,WAAa,OAC7C+C,EAAK,WAAa,MAAQA,EAAK,WAAa,KAC3CU,EACFb,GAAI,KAAK,OAAQ,IAAKG,EAAK,OAAQ/C,CAAO,IACxC,KAAK,WAAa,MAAQ,KAAK,WAAa,OAC7C+C,EAAK,WAAa,MAAQA,EAAK,WAAa,KAE/C,OAAOK,GAA2BC,GAC/BC,GAAcC,GACfC,GAA8BC,CAClC,EAEAvH,EAAQ,MAAQgH,EAChB,SAASA,EAAOQ,EAAO1D,EAAS,CAQ9B,IAPI,CAACA,GAAW,OAAOA,GAAY,YACjCA,EAAU,CACR,MAAO,CAAC,CAACA,EACT,kBAAmB,EACrB,GAGE0D,aAAiBR,EACnB,OAAIQ,EAAM,QAAU,CAAC,CAAC1D,EAAQ,OAC1B0D,EAAM,oBAAsB,CAAC,CAAC1D,EAAQ,kBACjC0D,EAEA,IAAIR,EAAMQ,EAAM,IAAK1D,CAAO,EAIvC,GAAI0D,aAAiBZ,EACnB,OAAO,IAAII,EAAMQ,EAAM,MAAO1D,CAAO,EAGvC,GAAI,EAAE,gBAAgBkD,GACpB,OAAO,IAAIA,EAAMQ,EAAO1D,CAAO,EAuBjC,GApBA,KAAK,QAAUA,EACf,KAAK,MAAQ,CAAC,CAACA,EAAQ,MACvB,KAAK,kBAAoB,CAAC,CAACA,EAAQ,kBAKnC,KAAK,IAAM0D,EACR,KAAK,EACL,MAAM,KAAK,EACX,KAAK,GAAG,EAGX,KAAK,IAAM,KAAK,IAAI,MAAM,IAAI,EAAE,IAAI,SAAUA,EAAO,CACnD,OAAO,KAAK,WAAWA,EAAM,KAAK,CAAC,CACrC,EAAG,IAAI,EAAE,OAAO,SAAUC,EAAG,CAE3B,OAAOA,EAAE,MACX,CAAC,EAEG,CAAC,KAAK,IAAI,OACZ,MAAM,IAAI,UAAU,yBAA2B,KAAK,GAAG,EAGzD,KAAK,OAAO,CACd,CAEAT,EAAM,UAAU,OAAS,UAAY,CACnC,YAAK,MAAQ,KAAK,IAAI,IAAI,SAAUU,EAAO,CACzC,OAAOA,EAAM,KAAK,GAAG,EAAE,KAAK,CAC9B,CAAC,EAAE,KAAK,IAAI,EAAE,KAAK,EACZ,KAAK,KACd,EAEAV,EAAM,UAAU,SAAW,UAAY,CACrC,OAAO,KAAK,KACd,EAEAA,EAAM,UAAU,WAAa,SAAUQ,EAAO,CAC5C,IAAI1C,EAAQ,KAAK,QAAQ,MAErB6C,EAAK7C,EAAQpE,EAAOgD,EAAgB,EAAIhD,EAAO+C,EAAW,EAC9D+D,EAAQA,EAAM,QAAQG,EAAIC,EAAa,EACvCzH,EAAM,iBAAkBqH,CAAK,EAE7BA,EAAQA,EAAM,QAAQ9G,EAAO6C,EAAc,EAAGC,EAAqB,EACnErD,EAAM,kBAAmBqH,EAAO9G,EAAO6C,EAAc,CAAC,EAGtDiE,EAAQA,EAAM,QAAQ9G,EAAOkC,EAAS,EAAGC,EAAgB,EAGzD2E,EAAQA,EAAM,QAAQ9G,EAAOuC,EAAS,EAAGC,EAAgB,EAIzD,IAAI2E,EAAS/C,EAAQpE,EAAO2C,EAAe,EAAI3C,EAAO4C,EAAU,EAC5DwE,EAAMN,EAAM,MAAM,GAAG,EAAE,IAAI,SAAUX,EAAM,CAC7C,OAAOkB,GAAgBlB,EAAM,KAAK,OAAO,CAC3C,EAAG,IAAI,EAAE,KAAK,GAAG,EAAE,MAAM,KAAK,EAC9B,OAAI,KAAK,QAAQ,QAEfiB,EAAMA,EAAI,OAAO,SAAUjB,EAAM,CAC/B,MAAO,CAAC,CAACA,EAAK,MAAMgB,CAAM,CAC5B,CAAC,GAEHC,EAAMA,EAAI,IAAI,SAAUjB,EAAM,CAC5B,OAAO,IAAID,EAAWC,EAAM,KAAK,OAAO,CAC1C,EAAG,IAAI,EAEAiB,CACT,EAEAd,EAAM,UAAU,WAAa,SAAUQ,EAAO1D,EAAS,CACrD,GAAI,EAAE0D,aAAiBR,GACrB,MAAM,IAAI,UAAU,qBAAqB,EAG3C,OAAO,KAAK,IAAI,KAAK,SAAUgB,EAAiB,CAC9C,OAAOA,EAAgB,MAAM,SAAUC,EAAgB,CACrD,OAAOT,EAAM,IAAI,KAAK,SAAUU,EAAkB,CAChD,OAAOA,EAAiB,MAAM,SAAUC,EAAiB,CACvD,OAAOF,EAAe,WAAWE,EAAiBrE,CAAO,CAC3D,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,EAGA9D,EAAQ,cAAgBoI,GACxB,SAASA,GAAeZ,EAAO1D,EAAS,CACtC,OAAO,IAAIkD,EAAMQ,EAAO1D,CAAO,EAAE,IAAI,IAAI,SAAU+C,EAAM,CACvD,OAAOA,EAAK,IAAI,SAAUY,EAAG,CAC3B,OAAOA,EAAE,KACX,CAAC,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG,CAC/B,CAAC,CACH,CAKA,SAASM,GAAiBlB,EAAM/C,EAAS,CACvC,OAAA3D,EAAM,OAAQ0G,EAAM/C,CAAO,EAC3B+C,EAAOwB,GAAcxB,EAAM/C,CAAO,EAClC3D,EAAM,QAAS0G,CAAI,EACnBA,EAAOyB,GAAczB,EAAM/C,CAAO,EAClC3D,EAAM,SAAU0G,CAAI,EACpBA,EAAO0B,GAAe1B,EAAM/C,CAAO,EACnC3D,EAAM,SAAU0G,CAAI,EACpBA,EAAO2B,GAAa3B,EAAM/C,CAAO,EACjC3D,EAAM,QAAS0G,CAAI,EACZA,CACT,CAEA,SAAS4B,EAAKpE,EAAI,CAChB,MAAO,CAACA,GAAMA,EAAG,YAAY,IAAM,KAAOA,IAAO,GACnD,CAQA,SAASiE,GAAezB,EAAM/C,EAAS,CACrC,OAAO+C,EAAK,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,SAAUA,EAAM,CAClD,OAAO6B,GAAa7B,EAAM/C,CAAO,CACnC,CAAC,EAAE,KAAK,GAAG,CACb,CAEA,SAAS4E,GAAc7B,EAAM/C,EAAS,CACpC,IAAIC,EAAID,EAAQ,MAAQpD,EAAOqC,EAAU,EAAIrC,EAAOoC,EAAK,EACzD,OAAO+D,EAAK,QAAQ9C,EAAG,SAAU4E,EAAGC,EAAGxE,EAAGyE,EAAGC,EAAI,CAC/C3I,EAAM,QAAS0G,EAAM8B,EAAGC,EAAGxE,EAAGyE,EAAGC,CAAE,EACnC,IAAIC,EAEJ,OAAIN,EAAIG,CAAC,EACPG,EAAM,GACGN,EAAIrE,CAAC,EACd2E,EAAM,KAAOH,EAAI,UAAY,CAACA,EAAI,GAAK,OAC9BH,EAAII,CAAC,EAEdE,EAAM,KAAOH,EAAI,IAAMxE,EAAI,OAASwE,EAAI,KAAO,CAACxE,EAAI,GAAK,KAChD0E,GACT3I,EAAM,kBAAmB2I,CAAE,EAC3BC,EAAM,KAAOH,EAAI,IAAMxE,EAAI,IAAMyE,EAAI,IAAMC,EACrC,KAAOF,EAAI,KAAO,CAACxE,EAAI,GAAK,MAGlC2E,EAAM,KAAOH,EAAI,IAAMxE,EAAI,IAAMyE,EAC3B,KAAOD,EAAI,KAAO,CAACxE,EAAI,GAAK,KAGpCjE,EAAM,eAAgB4I,CAAG,EAClBA,CACT,CAAC,CACH,CAQA,SAASV,GAAexB,EAAM/C,EAAS,CACrC,OAAO+C,EAAK,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,SAAUA,EAAM,CAClD,OAAOmC,GAAanC,EAAM/C,CAAO,CACnC,CAAC,EAAE,KAAK,GAAG,CACb,CAEA,SAASkF,GAAcnC,EAAM/C,EAAS,CACpC3D,EAAM,QAAS0G,EAAM/C,CAAO,EAC5B,IAAIC,EAAID,EAAQ,MAAQpD,EAAO0C,EAAU,EAAI1C,EAAOyC,EAAK,EACzD,OAAO0D,EAAK,QAAQ9C,EAAG,SAAU4E,EAAGC,EAAGxE,EAAGyE,EAAGC,EAAI,CAC/C3I,EAAM,QAAS0G,EAAM8B,EAAGC,EAAGxE,EAAGyE,EAAGC,CAAE,EACnC,IAAIC,EAEJ,OAAIN,EAAIG,CAAC,EACPG,EAAM,GACGN,EAAIrE,CAAC,EACd2E,EAAM,KAAOH,EAAI,UAAY,CAACA,EAAI,GAAK,OAC9BH,EAAII,CAAC,EACVD,IAAM,IACRG,EAAM,KAAOH,EAAI,IAAMxE,EAAI,OAASwE,EAAI,KAAO,CAACxE,EAAI,GAAK,KAEzD2E,EAAM,KAAOH,EAAI,IAAMxE,EAAI,QAAU,CAACwE,EAAI,GAAK,OAExCE,GACT3I,EAAM,kBAAmB2I,CAAE,EACvBF,IAAM,IACJxE,IAAM,IACR2E,EAAM,KAAOH,EAAI,IAAMxE,EAAI,IAAMyE,EAAI,IAAMC,EACrC,KAAOF,EAAI,IAAMxE,EAAI,KAAO,CAACyE,EAAI,GAEvCE,EAAM,KAAOH,EAAI,IAAMxE,EAAI,IAAMyE,EAAI,IAAMC,EACrC,KAAOF,EAAI,KAAO,CAACxE,EAAI,GAAK,KAGpC2E,EAAM,KAAOH,EAAI,IAAMxE,EAAI,IAAMyE,EAAI,IAAMC,EACrC,MAAQ,CAACF,EAAI,GAAK,SAG1BzI,EAAM,OAAO,EACTyI,IAAM,IACJxE,IAAM,IACR2E,EAAM,KAAOH,EAAI,IAAMxE,EAAI,IAAMyE,EAC3B,KAAOD,EAAI,IAAMxE,EAAI,KAAO,CAACyE,EAAI,GAEvCE,EAAM,KAAOH,EAAI,IAAMxE,EAAI,IAAMyE,EAC3B,KAAOD,EAAI,KAAO,CAACxE,EAAI,GAAK,KAGpC2E,EAAM,KAAOH,EAAI,IAAMxE,EAAI,IAAMyE,EAC3B,MAAQ,CAACD,EAAI,GAAK,QAI5BzI,EAAM,eAAgB4I,CAAG,EAClBA,CACT,CAAC,CACH,CAEA,SAASR,GAAgB1B,EAAM/C,EAAS,CACtC,OAAA3D,EAAM,iBAAkB0G,EAAM/C,CAAO,EAC9B+C,EAAK,MAAM,KAAK,EAAE,IAAI,SAAUA,EAAM,CAC3C,OAAOoC,GAAcpC,EAAM/C,CAAO,CACpC,CAAC,EAAE,KAAK,GAAG,CACb,CAEA,SAASmF,GAAepC,EAAM/C,EAAS,CACrC+C,EAAOA,EAAK,KAAK,EACjB,IAAI9C,EAAID,EAAQ,MAAQpD,EAAO+B,EAAW,EAAI/B,EAAO8B,EAAM,EAC3D,OAAOqE,EAAK,QAAQ9C,EAAG,SAAUgF,EAAKG,EAAMN,EAAGxE,EAAGyE,EAAGC,EAAI,CACvD3I,EAAM,SAAU0G,EAAMkC,EAAKG,EAAMN,EAAGxE,EAAGyE,EAAGC,CAAE,EAC5C,IAAIK,EAAKV,EAAIG,CAAC,EACVQ,EAAKD,GAAMV,EAAIrE,CAAC,EAChBiF,EAAKD,GAAMX,EAAII,CAAC,EAChBS,EAAOD,EAEX,OAAIH,IAAS,KAAOI,IAClBJ,EAAO,IAGLC,EACED,IAAS,KAAOA,IAAS,IAE3BH,EAAM,SAGNA,EAAM,IAECG,GAAQI,GAGbF,IACFhF,EAAI,GAENyE,EAAI,EAEAK,IAAS,KAIXA,EAAO,KACHE,GACFR,EAAI,CAACA,EAAI,EACTxE,EAAI,EACJyE,EAAI,IAEJzE,EAAI,CAACA,EAAI,EACTyE,EAAI,IAEGK,IAAS,OAGlBA,EAAO,IACHE,EACFR,EAAI,CAACA,EAAI,EAETxE,EAAI,CAACA,EAAI,GAIb2E,EAAMG,EAAON,EAAI,IAAMxE,EAAI,IAAMyE,GACxBO,EACTL,EAAM,KAAOH,EAAI,UAAY,CAACA,EAAI,GAAK,OAC9BS,IACTN,EAAM,KAAOH,EAAI,IAAMxE,EAAI,OAASwE,EAAI,KAAO,CAACxE,EAAI,GAAK,MAG3DjE,EAAM,gBAAiB4I,CAAG,EAEnBA,CACT,CAAC,CACH,CAIA,SAASP,GAAc3B,EAAM/C,EAAS,CACpC,OAAA3D,EAAM,eAAgB0G,EAAM/C,CAAO,EAE5B+C,EAAK,KAAK,EAAE,QAAQnG,EAAOiD,EAAI,EAAG,EAAE,CAC7C,CAOA,SAASiE,GAAe2B,EACtBC,EAAMC,EAAIC,EAAIC,EAAIC,EAAKC,EACvBC,EAAIC,EAAIC,EAAIC,EAAIC,EAAKC,EAAI,CACzB,OAAI1B,EAAIgB,CAAE,EACRD,EAAO,GACEf,EAAIiB,CAAE,EACfF,EAAO,KAAOC,EAAK,OACVhB,EAAIkB,CAAE,EACfH,EAAO,KAAOC,EAAK,IAAMC,EAAK,KAE9BF,EAAO,KAAOA,EAGZf,EAAIsB,CAAE,EACRD,EAAK,GACIrB,EAAIuB,CAAE,EACfF,EAAK,KAAO,CAACC,EAAK,GAAK,OACdtB,EAAIwB,CAAE,EACfH,EAAK,IAAMC,EAAK,KAAO,CAACC,EAAK,GAAK,KACzBE,EACTJ,EAAK,KAAOC,EAAK,IAAMC,EAAK,IAAMC,EAAK,IAAMC,EAE7CJ,EAAK,KAAOA,GAGNN,EAAO,IAAMM,GAAI,KAAK,CAChC,CAGA9C,EAAM,UAAU,KAAO,SAAUnD,EAAS,CACxC,GAAI,CAACA,EACH,MAAO,GAGL,OAAOA,GAAY,WACrBA,EAAU,IAAI3D,EAAO2D,EAAS,KAAK,OAAO,GAG5C,QAAS5C,EAAI,EAAGA,EAAI,KAAK,IAAI,OAAQA,IACnC,GAAImJ,GAAQ,KAAK,IAAInJ,CAAC,EAAG4C,EAAS,KAAK,OAAO,EAC5C,MAAO,GAGX,MAAO,EACT,EAEA,SAASuG,GAAStC,EAAKjE,EAASC,EAAS,CACvC,QAAS7C,EAAI,EAAGA,EAAI6G,EAAI,OAAQ7G,IAC9B,GAAI,CAAC6G,EAAI7G,CAAC,EAAE,KAAK4C,CAAO,EACtB,MAAO,GAIX,GAAIA,EAAQ,WAAW,QAAU,CAACC,EAAQ,kBAAmB,CAM3D,IAAK7C,EAAI,EAAGA,EAAI6G,EAAI,OAAQ7G,IAE1B,GADAd,EAAM2H,EAAI7G,CAAC,EAAE,MAAM,EACf6G,EAAI7G,CAAC,EAAE,SAAW6F,IAIlBgB,EAAI7G,CAAC,EAAE,OAAO,WAAW,OAAS,EAAG,CACvC,IAAIoJ,EAAUvC,EAAI7G,CAAC,EAAE,OACrB,GAAIoJ,EAAQ,QAAUxG,EAAQ,OAC1BwG,EAAQ,QAAUxG,EAAQ,OAC1BwG,EAAQ,QAAUxG,EAAQ,MAC5B,MAAO,EAEX,CAIF,MAAO,EACT,CAEA,MAAO,EACT,CAEA7D,EAAQ,UAAYiH,GACpB,SAASA,GAAWpD,EAAS2D,EAAO1D,EAAS,CAC3C,GAAI,CACF0D,EAAQ,IAAIR,EAAMQ,EAAO1D,CAAO,CAClC,MAAa,CACX,MAAO,EACT,CACA,OAAO0D,EAAM,KAAK3D,CAAO,CAC3B,CAEA7D,EAAQ,cAAgBsK,GACxB,SAASA,GAAeC,EAAU/C,EAAO1D,EAAS,CAChD,IAAI3C,EAAM,KACNqJ,EAAQ,KACZ,GAAI,CACF,IAAIC,EAAW,IAAIzD,EAAMQ,EAAO1D,CAAO,CACzC,MAAa,CACX,OAAO,IACT,CACA,OAAAyG,EAAS,QAAQ,SAAUtG,EAAG,CACxBwG,EAAS,KAAKxG,CAAC,IAEb,CAAC9C,GAAOqJ,EAAM,QAAQvG,CAAC,IAAM,MAE/B9C,EAAM8C,EACNuG,EAAQ,IAAItK,EAAOiB,EAAK2C,CAAO,EAGrC,CAAC,EACM3C,CACT,CAEAnB,EAAQ,cAAgB0K,GACxB,SAASA,GAAeH,EAAU/C,EAAO1D,EAAS,CAChD,IAAI6G,EAAM,KACNC,EAAQ,KACZ,GAAI,CACF,IAAIH,EAAW,IAAIzD,EAAMQ,EAAO1D,CAAO,CACzC,MAAa,CACX,OAAO,IACT,CACA,OAAAyG,EAAS,QAAQ,SAAUtG,EAAG,CACxBwG,EAAS,KAAKxG,CAAC,IAEb,CAAC0G,GAAOC,EAAM,QAAQ3G,CAAC,IAAM,KAE/B0G,EAAM1G,EACN2G,EAAQ,IAAI1K,EAAOyK,EAAK7G,CAAO,EAGrC,CAAC,EACM6G,CACT,CAEA3K,EAAQ,WAAa6K,GACrB,SAASA,GAAYrD,EAAO1C,EAAO,CACjC0C,EAAQ,IAAIR,EAAMQ,EAAO1C,CAAK,EAE9B,IAAIgG,EAAS,IAAI5K,EAAO,OAAO,EAM/B,GALIsH,EAAM,KAAKsD,CAAM,IAIrBA,EAAS,IAAI5K,EAAO,SAAS,EACzBsH,EAAM,KAAKsD,CAAM,GACnB,OAAOA,EAGTA,EAAS,KACT,QAAS7J,EAAI,EAAGA,EAAIuG,EAAM,IAAI,OAAQ,EAAEvG,EAAG,CACzC,IAAI8J,EAAcvD,EAAM,IAAIvG,CAAC,EAE7B8J,EAAY,QAAQ,SAAUC,EAAY,CAExC,IAAIC,EAAU,IAAI/K,EAAO8K,EAAW,OAAO,OAAO,EAClD,OAAQA,EAAW,SAAU,CAC3B,IAAK,IACCC,EAAQ,WAAW,SAAW,EAChCA,EAAQ,QAERA,EAAQ,WAAW,KAAK,CAAC,EAE3BA,EAAQ,IAAMA,EAAQ,OAAO,EAE/B,IAAK,GACL,IAAK,MACC,CAACH,GAAUzE,GAAGyE,EAAQG,CAAO,KAC/BH,EAASG,GAEX,MACF,IAAK,IACL,IAAK,KAEH,MAEF,QACE,MAAM,IAAI,MAAM,yBAA2BD,EAAW,QAAQ,CAClE,CACF,CAAC,CACH,CAEA,OAAIF,GAAUtD,EAAM,KAAKsD,CAAM,EACtBA,EAGF,IACT,CAEA9K,EAAQ,WAAakL,GACrB,SAASA,GAAY1D,EAAO1D,EAAS,CACnC,GAAI,CAGF,OAAO,IAAIkD,EAAMQ,EAAO1D,CAAO,EAAE,OAAS,GAC5C,MAAa,CACX,OAAO,IACT,CACF,CAGA9D,EAAQ,IAAMmL,GACd,SAASA,GAAKtH,EAAS2D,EAAO1D,EAAS,CACrC,OAAOsH,GAAQvH,EAAS2D,EAAO,IAAK1D,CAAO,CAC7C,CAGA9D,EAAQ,IAAMqL,GACd,SAASA,GAAKxH,EAAS2D,EAAO1D,EAAS,CACrC,OAAOsH,GAAQvH,EAAS2D,EAAO,IAAK1D,CAAO,CAC7C,CAEA9D,EAAQ,QAAUoL,GAClB,SAASA,GAASvH,EAAS2D,EAAO8D,EAAMxH,EAAS,CAC/CD,EAAU,IAAI3D,EAAO2D,EAASC,CAAO,EACrC0D,EAAQ,IAAIR,EAAMQ,EAAO1D,CAAO,EAEhC,IAAIyH,EAAMC,EAAOC,EAAM5E,EAAM6E,EAC7B,OAAQJ,EAAM,CACZ,IAAK,IACHC,EAAOlF,GACPmF,EAAQ/E,GACRgF,EAAOnF,GACPO,EAAO,IACP6E,EAAQ,KACR,MACF,IAAK,IACHH,EAAOjF,GACPkF,EAAQhF,GACRiF,EAAOpF,GACPQ,EAAO,IACP6E,EAAQ,KACR,MACF,QACE,MAAM,IAAI,UAAU,uCAAuC,CAC/D,CAGA,GAAIzE,GAAUpD,EAAS2D,EAAO1D,CAAO,EACnC,MAAO,GAMT,QAAS7C,EAAI,EAAGA,EAAIuG,EAAM,IAAI,OAAQ,EAAEvG,EAAG,CACzC,IAAI8J,EAAcvD,EAAM,IAAIvG,CAAC,EAEzB0K,EAAO,KACPC,EAAM,KAuBV,GArBAb,EAAY,QAAQ,SAAUC,EAAY,CACpCA,EAAW,SAAWlE,KACxBkE,EAAa,IAAIpE,EAAW,SAAS,GAEvC+E,EAAOA,GAAQX,EACfY,EAAMA,GAAOZ,EACTO,EAAKP,EAAW,OAAQW,EAAK,OAAQ7H,CAAO,EAC9C6H,EAAOX,EACES,EAAKT,EAAW,OAAQY,EAAI,OAAQ9H,CAAO,IACpD8H,EAAMZ,EAEV,CAAC,EAIGW,EAAK,WAAa9E,GAAQ8E,EAAK,WAAaD,IAM3C,CAACE,EAAI,UAAYA,EAAI,WAAa/E,IACnC2E,EAAM3H,EAAS+H,EAAI,MAAM,EAC3B,MAAO,GACF,GAAIA,EAAI,WAAaF,GAASD,EAAK5H,EAAS+H,EAAI,MAAM,EAC3D,MAAO,EAEX,CACA,MAAO,EACT,CAEA5L,EAAQ,WAAa6L,GACrB,SAASA,GAAYhI,EAASC,EAAS,CACrC,IAAIgI,EAASlI,GAAMC,EAASC,CAAO,EACnC,OAAQgI,GAAUA,EAAO,WAAW,OAAUA,EAAO,WAAa,IACpE,CAEA9L,EAAQ,WAAa+L,GACrB,SAASA,GAAYC,EAAIC,EAAInI,EAAS,CACpC,OAAAkI,EAAK,IAAIhF,EAAMgF,EAAIlI,CAAO,EAC1BmI,EAAK,IAAIjF,EAAMiF,EAAInI,CAAO,EACnBkI,EAAG,WAAWC,CAAE,CACzB,CAEAjM,EAAQ,OAASkM,GACjB,SAASA,GAAQrI,EAAS,CACxB,GAAIA,aAAmB3D,EACrB,OAAO2D,EAGT,GAAI,OAAOA,GAAY,SACrB,OAAO,KAGT,IAAIsI,EAAQtI,EAAQ,MAAMnD,EAAOgC,EAAM,CAAC,EAExC,OAAIyJ,GAAS,KACJ,KAGFvI,GAAMuI,EAAM,CAAC,EAClB,KAAOA,EAAM,CAAC,GAAK,KACnB,KAAOA,EAAM,CAAC,GAAK,IAAI,CAC3B,ICp/CA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAA,SACC,OACA,gBACA,MACA,OACA,UACA,UACA,UACA,UACA,UACA,gBACA,oBACA,gBACA,oBACA,aACA,UACA,MACA,cACA,QACA,WACA,oBACA,SACA,UACA,WACA,WACA,WACA,WACA,0BACA,0BACA,WACA,cACA,aACA,2BACA,cACA,eACA,WACA,UACA,aACA,aACA,aACA,UACA,cACA,eACA,oBACA,mBACA,eACA,eACA,eACA,sBACA,sBACA,qBACA,2BACA,eACA,2BACA,qBACA,kBACA,oBACA,4BACA,mCACA,kCACA,uCACA,mCACA,wBACA,mBACA,sBACA,oBACA,eACA,yBACA,kBACA,cACA,eACA,kCACA,kCACA,uBACA,iBACA,kBACA,4BACA,gBACA,wBACA,UACA,WACA,UACA,SACA,OACA,WACA,iBACA,iBACA,oBACA,iBACA,gBACA,WACA,UACA,yBACA,yBACA,YACA,UACA,kCACA,aACA,YACA,YACA,YACA,eACA,YACA,eACA,eACA,eACA,gBACA,eACA,eACA,YACA,eACA,eACA,eACA,eACA,kBACA,eACA,kBACA,kBACA,kBACA,kBACA,qBACA,sBACA,kBACA,kBACA,kBACA,qBACA,qBACA,qBACA,kBACA,kBACA,qBACA,sBACA,kBACA,eACA,eACA,eACA,eACA,kBACA,eACA,eACA,eACA,kBACA,kBACA,eACA,eACA,kBACA,kBACA,mBACA,eACA,UACA,UACA,WACA,WACA,UACA,sBACA,sBACA,mBACA,aACA,aACA,aACA,aACA,WACA,WACA,eACA,eACA,iBACA,iBACA,iBACA,UACA,WACA,iBACA,cACA,cACA,6BACA,WACA,WACA,UACA,YACA,cACA,UACA,sBACA,WACA,aACA,QACA,qBACA,aACA,wBACA,SACA,YACA,iBACA,OACA,YACA,eACA,eACA,iBACA,MACA,UACA,UACA,OACA,iBACA,cACA,UACA,UACA,UACA,UACA,UACA,QACA,UACA,UACA,aACA,WACA,WACA,WACA,cACA,UACA,YACA,UACA,MACA,UACA,QACA,+BACA,QACA,UACA,YACA,MACA,OACA,mBACA,gBACA,cACA,YACA,WACA,WACA,KACA,2BACA,+BACA,8BACA,kCACA,gBACA,oBACA,2BACA,+BACA,8BACA,kCACA,gBACA,oBACA,2BACA,+BACA,8BACA,kCACA,gBACA,oBACA,QACA,SACA,eACA,mBACA,eACA,mBACA,eACA,mBACA,WACA,QACA,SACA,gBACA,UACA,SACA,UACA,UACA,OACA,WACA,4BACA,kBACA,aACA,qBACA,sBACA,mBACA,cACA,eACA,UACA,oBACA,WACA,gBACA,iBACA,iCACA,wBACA,kBACA,+BACA,mCACA,oBACA,oBACA,mCACA,uCACA,WACA,gBACA,kBACA,WACA,MACA,2BACA,MACA,YACA,MACA,UACA,MACA,eACA,cACA,SACA,WACA,gBACA,QACA,aACA,gBACA,YACA,QACA,OACA,MACA,aACA,UACA,SACA,aACA,UACA,UACA,gBACA,oBACA,gBACA,oBACA,gBACA,oBACA,SACA,OACA,eACA,UACA,WACA,WACA,WACA,WACA,YACA,YACA,wBACA,gBACA,UACA,4BACA,YACA,cACA,cACA,kBACA,SACA,eACA,yBACA,2BACA,kCACA,+BACA,sBACA,MACA,QACA,UACA,eACA,kBACA,qBACA,SACA,kBACA,WACA,UACA,iBACA,gBACA,SACA,WACA,WACA,UACA,UACA,UACA,gCACA,SACA,QACA,QACA,OACA,qBACA,oCACA,YACA,mBACA,mBACA,UACA,QACA,WACA,eACA,eACA,UACA,MACA,aACA,WACA,WACA,UACA,cACA,MACA,OACA,OACA,YACA,UACA,mBACA,gBACA,WACA,WACA,OACA,OACA,UACA,UACA,YACA,MACA,MACA,QACA,SACA,SACA,WACA,QACA,QACA,YACA,MACA,UACA,WACA,aACA,WACA,QACA,UACA,cACA,iBACA,UACA,cACA,iBACA,UACA,kBACA,iBACA,aACA,aACA,aACA,QACA,YACA,YACA,YACA,YACA,YACA,cACA,YACA,YACA,cACA,cACA,YACA,YACA,YACA,YACA,YACA,YACA,WACA,MACA,UACA,aACA,YACA,cACA,UACA,UACA,UACA,UACA,UACA,cACA,UACA,qBACA,aACA,OACA,WACA,UACA,WACA,MACA,UACA,eACA,eACA,QACA,SACA,+BACA,gCACA,aACA,aACA,eACA,UACA,qBACA,QACA,aACA,UACA,UACA,WACA,SACA,QACA,QACA,OACA,WACA,SACA,aACA,OACA,YACA,YACA,YACA,aACA,OACA,UACA,WACA,QACA,YACA,KACA,QACA,QACA,OACA,UACA,cACA,YACA,oBACA,WACA,MACA,UACA,eACA,WACA,gBACA,YACA,YACA,UACA,aACA,aACA,aACA,iBACA,UACA,eACA,SACA,WACA,eACA,MACA,eACA,YACA,QACA,aACA,OACA,OACA,UACA,OACA,QACA,gBACA,gBACA,cACA,OACA,UACA,cACA,UACA,UACA,kBACA,cACA,mBACA,mBACA,cACA,YACA,YACA,UACA,UACA,MACA,MACA,eACA,eACA,QACA,aACA,kBACA,SACA,MACA,uCACA,cACA,cACA,QACA,cACA,QACA,OACA,OACA,UACA,UACA,UACA,UACA,UACA,MACA,QACA,WACA,aACA,aACA,OACA,UACA,wBACA,WACA,cACA,YACA,UACA,sBACA,sBACA,OACA,UACA,WACA,OACA,UACA,SACA,aACA,OACA,aACA,UACA,UACA,SACA,SACA,aACA,iBACA,UACA,uBACA,OACA,QACA,WACA,WACA,iBACA,SACA,QACA,UACA,YACA,SACA,UACA,cACA,QACA,WACA,aACA,cACA,SACA,iBACA,OACA,MACA,SACA,4BACA,QACA,MACA,QACA,sBACD,IC3nBA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAA,SACC,WACA,WACA,uBACA,sBACA,WACA,WACA,WACA,UACA,UACA,6BACA,kCACA,+BACA,mCACA,8BACA,UACA,6BACA,kCACA,WACA,WACA,WACA,WACA,QACA,gBACA,cACA,WACA,WACD,IC3BA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAA,SACE,gBACA,qBACA,yBACA,yBACA,6BACA,iCACA,2BACA,uBACA,sBACA,uBACA,0BACA,sBACA,+BACA,0BACA,qBACA,2BACA,iBACA,gBACA,qBACA,yBACA,oBACA,yBACA,oBACA,iBACA,iBACA,2BACA,yBACA,yBACA,8BACA,4BACA,mCACA,aACA,2BACA,2BACA,yBACA,4BACA,6BACA,4BACA,oBACA,qBACA,QACA,iBACA,iBACA,gBACA,+BACA,qBACA,iCACA,4BACA,oCACA,+BACA,uBACA,wBACA,oBACA,iBACA,UACA,UACA,oBACA,gBACA,kBACA,oBACA,uBACA,iBACA,+BACA,2BACA,0BACA,0BACF,ICnEA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAIC,GAAW,CAAC,EACb,OAAO,IAA2B,EAClC,OAAO,IAAsC,EAC5CC,GAAa,KAEjBF,GAAO,QAAU,SAAUG,EAAQ,CACjC,IAAIC,EAAQ,EAEZ,SAASC,GAAW,CAClB,OAAOD,EAAQD,EAAO,MACxB,CAKA,SAASG,EAAMC,EAAO,CACpB,GAAIA,aAAiB,OAAQ,CAC3B,IAAIC,EAAQL,EAAO,MAAMC,CAAK,EAC1BK,EAAQD,EAAM,MAAMD,CAAK,EAC7B,GAAIE,EACF,OAAAL,GAASK,EAAM,CAAC,EAAE,OACXA,EAAM,CAAC,CAElB,SACMN,EAAO,QAAQI,EAAOH,CAAK,IAAMA,EACnC,OAAAA,GAASG,EAAM,OACRA,CAGb,CAEA,SAASG,GAAkB,CACzBJ,EAAK,MAAM,CACb,CAEA,SAASK,GAAY,CAGnB,QAFIC,EACAC,EAAgB,CAAC,OAAQ,MAAO,KAAM,IAAK,IAAK,IAAK,GAAG,EACnDC,EAAI,EAAGA,EAAID,EAAc,SAChCD,EAASN,EAAKO,EAAcC,CAAC,CAAC,EAC1B,CAAAF,GAFoCE,IAExC,CAKF,GAAIF,IAAW,KAAOR,EAAQ,GAAKD,EAAOC,EAAQ,CAAC,IAAM,IACvD,MAAM,IAAI,MAAM,kBAAkB,EAGpC,OAAOQ,GAAU,CACf,KAAM,WACN,OAAQA,CACV,CACF,CAEA,SAASG,GAAY,CACnB,OAAOT,EAAK,gBAAgB,CAC9B,CAEA,SAASU,GAAkB,CACzB,IAAIJ,EAASG,EAAS,EACtB,GAAI,CAACH,EACH,MAAM,IAAI,MAAM,+BAAiCR,CAAK,EAExD,OAAOQ,CACT,CAEA,SAASK,GAAe,CACtB,GAAIX,EAAK,cAAc,EAAG,CACxB,IAAIM,EAASI,EAAe,EAC5B,MAAO,CAAE,KAAM,cAAe,OAAQJ,CAAO,CAC/C,CACF,CAEA,SAASM,GAAc,CACrB,GAAIZ,EAAK,aAAa,EAAG,CACvB,IAAIM,EAASI,EAAe,EAC5B,MAAO,CAAE,KAAM,aAAc,OAAQJ,CAAO,CAC9C,CACF,CAEA,SAASO,GAAc,CACrB,IAAIC,EAAQhB,EACRQ,EAASG,EAAS,EAEtB,GAAId,GAAS,QAAQW,CAAM,IAAM,GAC/B,MAAO,CACL,KAAM,UACN,OAAQA,CACV,EACK,GAAIV,GAAW,QAAQU,CAAM,IAAM,GACxC,MAAO,CACL,KAAM,YACN,OAAQA,CACV,EAGFR,EAAQgB,CACV,CAIA,SAASC,GAAc,CAErB,OACEV,EAAS,GACTM,EAAY,GACZC,EAAW,GACXC,EAAW,CAEf,CAGA,QADIG,EAAS,CAAC,EACPjB,EAAQ,IACbK,EAAe,EACX,EAACL,EAAQ,IAFG,CAMhB,IAAIkB,EAAQF,EAAW,EACvB,GAAI,CAACE,EACH,MAAM,IAAI,MAAM,eAAiBpB,EAAOC,CAAK,EAC7B,eAAiBA,CAAK,EAGxCkB,EAAO,KAAKC,CAAK,CACnB,CACA,OAAOD,CACT,IClIA,IAAAE,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAOAA,GAAO,QAAU,SAAUC,EAAQ,CACjC,IAAIC,EAAQ,EAEZ,SAASC,GAAW,CAClB,OAAOD,EAAQD,EAAO,MACxB,CAEA,SAASG,GAAS,CAChB,OAAOD,EAAQ,EAAIF,EAAOC,CAAK,EAAI,IACrC,CAEA,SAASG,GAAQ,CACf,GAAI,CAACF,EAAQ,EACX,MAAM,IAAI,MAEZD,GACF,CAEA,SAASI,EAAeC,EAAU,CAChC,IAAIC,EAAIJ,EAAM,EACd,GAAII,GAAKA,EAAE,OAAS,YAAcD,IAAaC,EAAE,OAC/C,OAAAH,EAAK,EACEG,EAAE,MAEb,CAEA,SAASC,GAAa,CACpB,GAAIH,EAAc,MAAM,EAAG,CACzB,IAAIE,EAAIJ,EAAM,EACd,GAAII,GAAKA,EAAE,OAAS,YAClB,OAAAH,EAAK,EACEG,EAAE,OAEX,MAAM,IAAI,MAAM,iCAAiC,CACnD,CACF,CAEA,SAASE,GAAmB,CAI1B,IAAIC,EAAQT,EACRU,EAAS,GACTJ,EAAIJ,EAAM,EACd,GAAII,EAAE,OAAS,gBACbH,EAAK,EACLO,GAAU,eAAiBJ,EAAE,OAAS,IAClC,CAACF,EAAc,GAAG,GACpB,MAAM,IAAI,MAAM,sCAAsC,EAI1D,GADAE,EAAIJ,EAAM,EACNI,EAAE,OAAS,aACb,OAAAH,EAAK,EACLO,GAAU,cAAgBJ,EAAE,OACrB,CAAE,QAASI,CAAO,EAE3BV,EAAQS,CACV,CAEA,SAASE,GAAgB,CACvB,IAAIL,EAAIJ,EAAM,EACd,GAAII,GAAKA,EAAE,OAAS,UAAW,CAC7BH,EAAK,EACL,IAAIS,EAAO,CAAE,QAASN,EAAE,MAAO,EAC3BF,EAAc,GAAG,IACnBQ,EAAK,KAAO,IAEd,IAAIC,EAAYN,EAAU,EAC1B,OAAIM,IACFD,EAAK,UAAYC,GAEZD,CACT,CACF,CAEA,SAASE,GAAgC,CACvC,IAAIC,EAAOX,EAAc,GAAG,EAC5B,GAAKW,EAIL,KAAIC,EAAOC,EAAgB,EAE3B,GAAI,CAACb,EAAc,GAAG,EACpB,MAAM,IAAI,MAAM,cAAc,EAGhC,OAAOY,EACT,CAEA,SAASE,GAAa,CACpB,OACEJ,EAA6B,GAC7BN,EAAgB,GAChBG,EAAa,CAEjB,CAEA,SAASQ,EAAoBd,EAAUe,EAAY,CACjD,OAAO,SAASC,GAAiB,CAC/B,IAAIN,GAAOK,EAAW,EACtB,GAAKL,GAIL,IAAI,CAACX,EAAcC,CAAQ,EACzB,OAAOU,GAGT,IAAIO,EAAQD,EAAc,EAC1B,GAAI,CAACC,EACH,MAAM,IAAI,MAAM,qBAAqB,EAEvC,MAAO,CACL,KAAMP,GACN,YAAaV,EAAS,YAAY,EAClC,MAAOiB,CACT,EACF,CACF,CAEA,IAAIC,EAAWJ,EAAmB,MAAOD,CAAS,EAC9CD,EAAkBE,EAAmB,KAAMI,CAAQ,EAEnDX,EAAOK,EAAgB,EAC3B,GAAI,CAACL,GAAQX,EAAQ,EACnB,MAAM,IAAI,MAAM,cAAc,EAEhC,OAAOW,CACT,ICzIA,IAAAY,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAIC,GAAO,KACPC,GAAQ,KAEZF,GAAO,QAAU,SAAUG,EAAQ,CACjC,OAAOD,GAAMD,GAAKE,CAAM,CAAC,CAC3B,ICPA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAeA,IAAIC,GAAQ,KACRC,GAAiB,KAErB,SAASC,GAAOC,EAAQ,CACtB,GAAI,CACF,OAAAH,GAAMG,CAAM,EACL,EACT,MAAgB,CACd,MAAO,EACT,CACF,CAQA,SAASC,GAAmBC,EAAGC,EAAG,CAChC,IAAIC,EAASD,EAAE,CAAC,EAAE,OAASD,EAAE,CAAC,EAAE,OAChC,OAAIE,IAAW,EAAUA,EAClBF,EAAE,CAAC,EAAE,YAAY,EAAE,cAAcC,EAAE,CAAC,EAAE,YAAY,CAAC,CAC5D,CAGA,IAAIE,GAAiB,CACnB,CAAC,OAAQ,MAAM,EACf,CAAC,MAAO,KAAK,EACb,CAAC,MAAO,KAAK,EACb,CAAC,MAAO,QAAQ,EAChB,CAAC,MAAO,KAAK,EACb,CAAC,MAAO,KAAK,EACb,CAAC,MAAO,KAAK,EACb,CAAC,SAAU,QAAQ,EACnB,CAAC,YAAa,GAAG,EACjB,CAAC,iBAAkB,EAAE,EACrB,CAAC,MAAO,KAAK,EACb,CAAC,MAAO,KAAK,EACb,CAAC,IAAK,EAAE,EACR,CAAC,UAAW,KAAK,EACjB,CAAC,WAAY,MAAM,EACnB,CAAC,UAAW,KAAK,EACjB,CAAC,UAAW,KAAK,EACjB,CAAC,oCAAqC,MAAM,EAC5C,CAAC,oCAAqC,MAAM,EAC5C,CAAC,oCAAqC,UAAU,EAChD,CAAC,oCAAqC,UAAU,EAChD,CAAC,gCAAiC,MAAM,EACxC,CAAC,gCAAiC,MAAM,EACxC,CAAC,gCAAiC,UAAU,EAC5C,CAAC,gCAAiC,UAAU,EAC5C,CAAC,6BAA8B,KAAK,EACpC,CAAC,qBAAsB,KAAK,EAC5B,CAAC,qBAAsB,KAAK,EAC5B,CAAC,6BAA8B,KAAK,EACpC,CAAC,MAAO,KAAK,EACb,CAAC,yBAA0B,KAAK,EAChC,CAAC,+BAAgC,KAAK,EACtC,CAAC,MAAO,KAAK,EACb,CAAC,SAAU,OAAO,EAClB,CAAC,WAAY,EAAE,CACjB,EAAE,KAAKJ,EAAkB,EAErBK,GAAa,EACbC,GAAU,EAGVC,GAAa,CAEf,SAAUC,EAAU,CAClB,OAAOA,EAAS,YAAY,CAC9B,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,KAAK,CACvB,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,MAAO,EAAE,CACnC,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,OAAQ,EAAE,CACpC,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,OAAQ,GAAG,CACrC,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,IAAK,GAAG,CAClC,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,YAAa,KAAK,CAC5C,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,YAAa,OAAO,CAC9C,EAEA,SAAUA,EAAU,CAClB,OAAOA,EACJ,QAAQ,4CAA6C,KAAK,CAC/D,EAEA,SAAUA,EAAU,CAClB,OAAOA,EACJ,QAAQ,4CAA6C,OAAO,CACjE,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,CAAC,EAAE,YAAY,EAAIA,EAAS,MAAM,CAAC,CACrD,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,IAAK,GAAG,CAClC,EAEA,SAAUA,EAAU,CAClB,OAAOA,EACJ,QAAQ,cAAe,KAAK,EAC5B,QAAQ,QAAS,MAAM,CAC5B,EAEA,SAAUA,EAAU,CAClB,OAAIA,EAAS,QAAQ,KAAK,IAAM,GACvBA,EAAW,YAEXA,EAAW,OAEtB,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAW,MACpB,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,QAAS,OAAO,CAC1C,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,cAAe,YAAY,CACrD,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,uBAAwB,YAAY,CAC9D,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,oDAAqD,cAAc,CAC7F,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,wCAAyC,cAAc,CACjF,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,wCAAyC,oBAAoB,CACvF,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,mCAAoC,oBAAoB,CAClF,EAEA,SAAUA,EAAU,CAClB,OAAOA,EAAS,QAAQ,4CAA6C,cAAc,CACrF,EAEA,SAAUA,EAAU,CAClB,MAAO,MAAQA,CACjB,EAEA,SAAUA,EAAU,CAClB,MAAO,MAAQA,EAAW,MAC5B,EAEA,SAAUA,EAAU,CAClB,OAAOA,EACJ,QAAQ,cAAe,IAAI,EAC3B,QAAQ,gBAAiB,IAAI,EAC7B,QAAQ,gBAAiB,IAAI,EAC7B,QAAQ,QAAS,KAAK,EACtB,QAAQ,kBAAmB,EAAE,CAClC,EAEA,SAAUA,EAAU,CAClB,MAAO,MACLA,EACG,QAAQ,cAAe,IAAI,EAC3B,QAAQ,gBAAiB,IAAI,EAC7B,QAAQ,gBAAiB,IAAI,EAC7B,QAAQ,QAAS,KAAK,EACtB,QAAQ,kBAAmB,EAAE,EAChC,MACJ,CACF,EAEIC,GAAuBZ,GACxB,IAAI,SAAUa,EAAI,CACjB,IAAIC,EAAQ,kBAAkB,KAAKD,CAAE,EACrC,OAAOC,EACH,CAACA,EAAM,CAAC,EAAGA,EAAM,CAAC,CAAC,EACnB,CAACD,EAAI,IAAI,CACf,CAAC,EACA,OAAO,SAAUE,EAAWC,EAAM,CACjC,IAAIC,EAAMD,EAAK,CAAC,EAChB,OAAAD,EAAUE,CAAG,EAAIF,EAAUE,CAAG,GAAK,CAAC,EACpCF,EAAUE,CAAG,EAAE,KAAKD,EAAK,CAAC,CAAC,EACpBD,CACT,EAAG,CAAC,CAAC,EAEHG,GAAyB,OAAO,KAAKN,EAAoB,EAC1D,IAAI,SAAsBK,EAAK,CAC9B,MAAO,CAACA,EAAKL,GAAqBK,CAAG,CAAC,CACxC,CAAC,EACA,OAAO,SAA+BD,EAAM,CAC3C,OAEEA,EAAK,CAAC,EAAE,SAAW,GACnBA,EAAK,CAAC,IAAM,MAEZA,EAAK,CAAC,IAAM,KAEhB,CAAC,EACA,IAAI,SAA4BA,EAAM,CACrC,MAAO,CAACA,EAAK,CAAC,EAAGA,EAAK,CAAC,EAAE,CAAC,CAAC,CAC7B,CAAC,EAEHJ,GAAuB,OAIvB,IAAIO,GAAc,CAChB,CAAC,OAAQ,WAAW,EACpB,CAAC,MAAO,OAAO,EACf,CAAC,WAAY,cAAc,EAC3B,CAAC,WAAY,cAAc,EAC3B,CAAC,WAAY,cAAc,EAC3B,CAAC,WAAY,cAAc,EAC3B,CAAC,SAAU,mBAAmB,EAC9B,CAAC,OAAQ,mBAAmB,EAC5B,CAAC,SAAU,YAAY,EACvB,CAAC,WAAY,cAAc,EAC3B,CAAC,SAAU,mBAAmB,EAC9B,CAAC,OAAQ,UAAU,EACnB,CAAC,QAAS,SAAS,EACnB,CAAC,MAAO,cAAc,EACtB,CAAC,OAAQ,UAAU,EACnB,CAAC,UAAW,SAAS,EACrB,CAAC,OAAQ,OAAO,EAChB,CAAC,MAAO,kBAAkB,EAC1B,CAAC,OAAQ,mBAAmB,EAC5B,CAAC,QAAS,cAAc,EACxB,CAAC,QAAS,cAAc,EACxB,CAAC,QAAS,cAAc,EACxB,CAAC,QAAS,cAAc,EACxB,CAAC,MAAO,kBAAkB,EAC1B,CAAC,wBAAyB,QAAQ,EAClC,CAAC,MAAO,KAAK,EACb,CAAC,MAAO,SAAS,EACjB,CAAC,MAAO,KAAK,EACb,CAAC,OAAQ,MAAM,CACjB,EAAE,OAAOD,EAAsB,EAAE,KAAKf,EAAkB,EAEpDiB,GAAY,EACZC,GAAa,EAEbC,GAAsB,SAAUC,EAAY,CAC9C,QAASC,EAAI,EAAGA,EAAId,GAAW,OAAQc,IAAK,CAC1C,IAAIC,EAAcf,GAAWc,CAAC,EAAED,CAAU,EAAE,KAAK,EACjD,GAAIE,IAAgBF,GAActB,GAAMwB,CAAW,EACjD,OAAOA,CAEX,CACA,OAAO,IACT,EAEIC,GAAkB,SAAUH,EAAY,CAE1C,QADII,EAAaJ,EAAW,YAAY,EAC/BC,EAAI,EAAGA,EAAIL,GAAY,OAAQK,IAAK,CAC3C,IAAII,EAAaT,GAAYK,CAAC,EAC9B,GAAIG,EAAW,QAAQC,EAAWR,EAAS,CAAC,EAAI,GAC9C,OAAOQ,EAAWP,EAAU,CAEhC,CACA,OAAO,IACT,EAEIQ,GAAgB,SAAUN,EAAYO,EAAO,CAC/C,QAASN,EAAI,EAAGA,EAAIjB,GAAe,OAAQiB,IAAK,CAC9C,IAAIO,EAAgBxB,GAAeiB,CAAC,EAChCQ,EAAaD,EAAcvB,EAAU,EACzC,GAAIe,EAAW,QAAQS,CAAU,EAAI,GAAI,CACvC,IAAIC,EAAYV,EAAW,QACzBS,EACAD,EAActB,EAAO,CACvB,EACIyB,EAAUJ,EAAMG,CAAS,EAC7B,GAAIC,IAAY,KACd,OAAOA,CAEX,CACF,CACA,OAAO,IACT,EAEApC,GAAO,QAAU,SAAUyB,EAAYY,EAAS,CAC9CA,EAAUA,GAAW,CAAC,EACtB,IAAIC,EAAUD,EAAQ,UAAY,OAAY,GAAO,CAAC,CAACA,EAAQ,QAC/D,SAASE,EAAaC,EAAO,CAC3B,OAAOF,EAAUG,GAAYD,CAAK,EAAIA,CACxC,CACA,IAAIE,EACF,OAAOjB,GAAe,UACtBA,EAAW,KAAK,EAAE,SAAW,EAE/B,GAAI,CAACiB,EACH,MAAM,MAAM,8CAA8C,EAG5D,GADAjB,EAAaA,EAAW,KAAK,EACzBtB,GAAMsB,CAAU,EAClB,OAAOc,EAAYd,CAAU,EAE/B,IAAIkB,EAASlB,EAAW,QAAQ,MAAO,EAAE,EAAE,KAAK,EAChD,GAAItB,GAAMwC,CAAM,EACd,OAAOJ,EAAYI,CAAM,EAE3B,IAAIhB,EAAcH,GAAoBC,CAAU,EAkBhD,OAjBIE,IAAgB,OAGpBA,EAAcI,GAAcN,EAAY,SAAUZ,EAAU,CAC1D,OAAIV,GAAMU,CAAQ,EACTA,EAEFW,GAAoBX,CAAQ,CACrC,CAAC,EACGc,IAAgB,QAGpBA,EAAcC,GAAgBH,CAAU,EACpCE,IAAgB,QAGpBA,EAAcI,GAAcN,EAAYG,EAAe,EACnDD,IAAgB,MACXY,EAAYZ,CAAW,EAEzB,IACT,EAEA,SAASc,GAAaD,EAAO,CAC3B,MAAI,CACF,UAAW,WAAY,WACvB,UAAW,WAAY,WACvB,UACF,EAAE,QAAQA,CAAK,IAAM,GACZA,EAAQ,QACN,CACT,WAAY,WAAY,WACxB,YAAa,YAAa,YAC1B,YAAa,WACf,EAAE,QAAQA,CAAK,IAAM,GACZA,EAAM,QAAQ,MAAO,WAAW,EAC9B,CAAC,UAAW,WAAY,UAAU,EAAE,QAAQA,CAAK,IAAM,GACzDA,EAAQ,YAERA,CAEX,ICjYA,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,KAAIC,GAAQ,KACRC,GAAU,KAEVC,GACF,yHAMEC,GAAkB,2BAEtB,SAASC,GAAWC,EAAQC,EAAQ,CAClC,OAAOA,EAAO,MAAM,EAAGD,EAAO,MAAM,IAAMA,CAC5C,CAEA,SAASE,GAAeC,EAAK,CAC3B,GAAIA,EAAI,eAAe,SAAS,EAAG,CACjC,IAAIC,EAAUD,EAAI,QAClB,OACEJ,GAAW,aAAcK,CAAO,GAChCL,GAAW,cAAeK,CAAO,CAErC,KACE,QACEF,GAAeC,EAAI,IAAI,GACvBD,GAAeC,EAAI,KAAK,CAG9B,CAEAT,GAAO,QAAU,SAASW,EAAU,CAClC,IAAIF,EAEJ,GAAI,CACFA,EAAMR,GAAMU,CAAQ,CACtB,MAAY,CACV,IAAIC,EACJ,GACED,IAAa,cACbA,IAAa,aAEb,MAAO,CACL,oBAAqB,GACrB,oBAAqB,GACrB,WAAY,EACd,EACK,GAAIC,EAAQR,GAAgB,KAAKO,CAAQ,EAC9C,MAAO,CACL,oBAAqB,GACrB,oBAAqB,GACrB,OAAQC,EAAM,CAAC,CACjB,EAEA,IAAIC,EAAS,CACX,oBAAqB,GACrB,oBAAqB,GACrB,SAAU,CAACV,EAAc,CAC3B,EACA,GAAIQ,EAAS,KAAK,EAAE,SAAW,EAAG,CAChC,IAAIG,EAAYZ,GAAQS,CAAQ,EAC5BG,GACFD,EAAO,SAAS,KACd,+CAAiDC,EAAY,GAC/D,CAEJ,CACA,OAAOD,CAEX,CAEA,OAAIL,GAAeC,CAAG,EACb,CACL,oBAAqB,GACrB,oBAAqB,GACrB,KAAM,GACN,SAAU,CAACN,EAAc,CAC3B,EAEO,CACL,oBAAqB,GACrB,oBAAqB,GACrB,KAAM,EACR,CAEJ,ICrFA,IAAAY,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAIC,GAAWD,GAAO,QAAU,CAC9B,OAAQ,CAGN,UAAa,CAAE,MAAO,OAAQ,UAAW,YAAa,MAAO,OAAQ,EACrE,OAAU,aACV,SAAY,OACZ,aAAgB,gFAChB,aAAgB,2CAChB,YAAe,0DACf,gBAAmB,gEACrB,EACA,UAAW,CACT,UAAa,CAAE,UAAW,YAAa,MAAO,OAAQ,EACtD,OAAU,gBACV,SAAY,MACZ,gBAAmB,2DACrB,EACA,OAAQ,CACN,UAAa,CAAE,UAAW,YAAa,MAAO,OAAQ,EACtD,OAAU,aACV,SAAY,OACZ,aAAgB,2CAChB,cAAiB,oEACjB,gBAAmB,+EACnB,UAAa,2HACf,EACA,KAAM,CACJ,UAAa,CAAE,MAAO,UAAW,YAAa,MAAO,OAAQ,EAC7D,OAAU,kBACV,UAAa,iDACb,aAAgB,8EAChB,aAAgB,6BAChB,YAAe,4CACf,YAAe,2CACf,eAAkB,oDAClB,eAAkB,0CAClB,mBAAsB,iDACtB,aAAgB,0CAChB,cAAiB,kDACjB,iBAAoB,gCACpB,aAAgB,yBAChB,gBAAmB,iEACnB,WAAc,SAAUE,EAAU,CAChC,MAAO,QAAUC,GAAmBD,CAAQ,CAC9C,CACF,CACF,EAEIE,GAAkB,CACpB,YAAe,iDACf,eAAkB,2DAClB,eAAkB,sDAClB,mBAAsB,8EACtB,aAAgB,6DAChB,cAAiB,gEACjB,aAAgB,4DAChB,iBAAoB,uCACpB,aAAgB,gCAChB,UAAa,yCACb,WAAcD,EAChB,EAEA,OAAO,KAAKF,EAAQ,EAAE,QAAQ,SAAUI,EAAM,CAC5C,OAAO,KAAKD,EAAe,EAAE,QAAQ,SAAUE,EAAK,CAC9CL,GAASI,CAAI,EAAEC,CAAG,IACtBL,GAASI,CAAI,EAAEC,CAAG,EAAIF,GAAgBE,CAAG,EAC3C,CAAC,EACDL,GAASI,CAAI,EAAE,aAAe,OAAO,KACnCJ,GAASI,CAAI,EAAE,UAAU,IAAI,SAAUE,EAAU,CAC/C,OAAOA,EAAS,QAAQ,sBAAuB,MAAM,CACvD,CAAC,EAAE,KAAK,GAAG,EAAI,KAAK,CACxB,CAAC,EAED,SAASJ,GAAoBD,EAAU,CACrC,OAAOA,EAAS,YAAY,EAAE,QAAQ,gBAAiB,EAAE,EAAE,QAAQ,OAAQ,GAAG,CAChF,IC9EA,IAAAM,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAIC,GAAW,KAMXC,GAAS,OAAO,QAAU,SAAkBC,EAAQC,EAAQ,CAE9D,GAAIA,IAAW,MAAQ,OAAOA,GAAW,SAAU,OAAOD,EAI1D,QAFIE,EAAO,OAAO,KAAKD,CAAM,EACzB,EAAIC,EAAK,OACN,KACLF,EAAOE,EAAK,CAAC,CAAC,EAAID,EAAOC,EAAK,CAAC,CAAC,EAElC,OAAOF,CACT,EAEAH,GAAO,QAAUM,EACjB,SAASA,EAASC,EAAMC,EAAMC,EAAMC,EAASC,EAAYC,EAAuBC,EAAM,CACpF,IAAIC,EAAc,KAClBA,EAAY,KAAOP,EACnB,OAAO,KAAKN,GAASM,CAAI,CAAC,EAAE,QAAQ,SAAUQ,EAAK,CACjDD,EAAYC,CAAG,EAAId,GAASM,CAAI,EAAEQ,CAAG,CACvC,CAAC,EACDD,EAAY,KAAON,EACnBM,EAAY,KAAOL,EACnBK,EAAY,QAAUJ,EACtBI,EAAY,WAAaH,EACzBG,EAAY,QAAUF,EACtBE,EAAY,KAAOD,GAAQ,CAAC,CAC9B,CAEAP,EAAQ,UAAU,KAAO,UAAY,CACnC,OAAO,KAAK,WAAa,IAAM,KAAK,WAAa,EACnD,EAEAA,EAAQ,UAAU,MAAQ,SAAUU,EAAUH,EAAM,CAClD,GAAKG,EACL,KAAIC,EAAOf,GAAO,CAAC,EAAGW,CAAI,EAC1BI,EAAK,KAAOA,EAAK,KAAOA,EAAK,KAAK,QAAQ,SAAU,EAAE,EAAI,GAC1DJ,EAAOX,GAAOA,GAAO,CAAC,EAAG,KAAK,IAAI,EAAGW,CAAI,EACzC,IAAIK,EAAO,KACX,OAAO,KAAK,IAAI,EAAE,QAAQ,SAAUH,EAAK,CACnCG,EAAKH,CAAG,GAAK,MAAQE,EAAKF,CAAG,GAAK,OAAME,EAAKF,CAAG,EAAIG,EAAKH,CAAG,EAClE,CAAC,EACD,IAAII,EAAUF,EAAK,KACfG,EAAgBH,EAAK,WACrBI,EAAcJ,EAAK,SACnBK,EAAUL,EAAK,KACfM,EAAaN,EAAK,QACtB,OAAO,KAAKA,CAAI,EAAE,QAAQ,SAAUF,EAAK,CACvC,IAAIS,EAAQP,EAAKF,CAAG,GACfA,IAAQ,QAAUA,IAAQ,YAAc,OAAOS,GAAU,SAC5DP,EAAKF,CAAG,EAAIS,EAAM,MAAM,GAAG,EAAE,IAAI,SAAUC,EAAe,CACxD,OAAO,mBAAmBA,CAAa,CACzC,CAAC,EAAE,KAAK,GAAG,EAEXR,EAAKF,CAAG,EAAI,mBAAmBS,CAAK,CAExC,CAAC,EACDP,EAAK,OAAO,EAAIE,EAAUA,EAAU,IAAM,GAC1CF,EAAK,WAAW,EAAII,EAAc,IAAM,KAAK,WAAWA,CAAW,EAAI,GACvEJ,EAAK,SAAWA,EAAK,SAAWA,EAAK,SAAW,GAChDA,EAAK,OAAO,EAAIK,EAAU,IAAM,KAAK,WAAWA,CAAO,EAAI,GAC3DL,EAAK,OAAO,EAAIA,EAAK,KAAO,IAAMA,EAAK,KAAO,GAC9CA,EAAK,YAAcM,EAAW,MAAM,GAAG,EAAE,IAAI,kBAAkB,EAAE,KAAK,GAAG,EACrEV,EAAK,cACPI,EAAK,aAAa,EAAI,GACtBA,EAAK,kBAAkB,EAAI,GAC3BA,EAAK,aAAa,EAAI,GACtBA,EAAK,WAAa,KAElBA,EAAK,aAAa,EAAIG,EAAgB,IAAMA,EAAgB,GAC5DH,EAAK,kBAAkB,EAAIA,EAAK,WAC5B,IAAMA,EAAK,SAAW,IAAMA,EAAK,WACjC,GACJA,EAAK,aAAa,EAAIA,EAAK,WAAa,IAAMA,EAAK,WAAa,GAChEA,EAAK,WAAaA,EAAK,YAAc,UAEvC,IAAIS,EAAMV,EAIV,OAHA,OAAO,KAAKC,CAAI,EAAE,QAAQ,SAAUF,EAAK,CACvCW,EAAMA,EAAI,QAAQ,IAAI,OAAO,MAAQX,EAAM,MAAO,GAAG,EAAGE,EAAKF,CAAG,CAAC,CACnE,CAAC,EACGF,EAAK,UACAa,EAAI,QAAQ,UAAW,EAAE,EAEzBA,EAEX,EAEApB,EAAQ,UAAU,IAAM,SAAUO,EAAM,CACtC,OAAO,KAAK,MAAM,KAAK,YAAaA,CAAI,CAC1C,EAEAP,EAAQ,UAAU,OAAS,SAAUO,EAAM,CACzC,OAAO,KAAK,MAAM,KAAK,eAAgBA,CAAI,CAC7C,EAEAP,EAAQ,UAAU,OAAS,SAAUqB,EAAGC,EAAGf,EAAM,CAC/C,OAAI,OAAOc,GAAM,UACX,OAAOC,GAAM,WACff,EAAOe,EACPA,EAAI,MAEC,KAAK,MAAM,KAAK,mBAAoB1B,GAAO,CAChD,SAAU0B,EACV,KAAMD,CACR,EAAGd,CAAI,CAAC,GAED,KAAK,MAAM,KAAK,eAAgBc,CAAC,CAE5C,EAEArB,EAAQ,UAAU,KAAO,SAAUO,EAAM,CACvC,OAAO,KAAK,MAAM,KAAK,aAAcA,CAAI,CAC3C,EAEAP,EAAQ,UAAU,KAAO,SAAUO,EAAM,CACvC,OAAO,KAAK,MAAM,KAAK,aAAcA,CAAI,CAC3C,EAEAP,EAAQ,UAAU,MAAQ,SAAUO,EAAM,CACxC,OAAO,KAAK,MAAM,KAAK,cAAeA,CAAI,CAC5C,EAEAP,EAAQ,UAAU,IAAM,SAAUO,EAAM,CACtC,OAAO,KAAK,MAAM,KAAK,YAAaA,CAAI,CAC1C,EAEAP,EAAQ,UAAU,SAAW,SAAUO,EAAM,CAC3C,OAAO,KAAK,MAAM,KAAK,iBAAkBA,CAAI,CAC/C,EAEAP,EAAQ,UAAU,KAAO,SAAUO,EAAM,CACvC,OAAO,KAAK,MAAM,KAAK,aAAcA,CAAI,CAC3C,EAEAP,EAAQ,UAAU,QAAU,SAAUuB,EAAO,CAC3C,IAAIhB,EAAOX,GAAO,CAAC,EAAG2B,EAAO,CAAE,aAAc,EAAM,CAAC,EACpD,OAAO,KAAK,MAAM,KAAK,gBAAiBhB,CAAI,CAC9C,EAEAP,EAAQ,UAAU,KAAO,SAAUqB,EAAGd,EAAM,CAC1C,OAAO,KAAK,MAAM,KAAK,aAAcX,GAAO,CAAE,KAAMyB,CAAE,EAAGd,CAAI,CAAC,CAChE,EAEAP,EAAQ,UAAU,yBAA2B,UAAY,CACvD,OAAO,KAAK,OACd,EAEAA,EAAQ,UAAU,SAAW,SAAUO,EAAM,CAC3C,OAAI,KAAK,SAAW,OAAO,KAAK,KAAK,OAAO,GAAM,WAAmB,KAAK,KAAK,OAAO,EAAEA,CAAI,EACrF,KAAK,OAAOA,CAAI,CACzB,IC3JA,IAAAiB,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAIC,GAAM,QAAQ,KAAK,EACnBC,GAAW,KACXC,GAAUH,GAAO,QAAU,KAE3BI,GAA8B,CAChC,WAAY,SACZ,aAAc,QACd,OAAQ,SACR,OAAQ,KACV,EAEA,SAASC,GAA0BC,EAAU,CAC3C,OAAOF,GAA4BE,CAAQ,GAAKA,EAAS,MAAM,EAAG,EAAE,CACtE,CAEA,IAAIC,GAAgB,CAClB,OAAQ,GACR,SAAU,GACV,aAAc,GACd,QAAS,GACT,YAAa,EACf,EAEIC,GAAQ,CAAC,EAEbR,GAAO,QAAQ,QAAU,SAAUS,EAAQC,EAAM,CAC/C,GAAI,OAAOD,GAAW,SACtB,KAAIE,EAAMF,EAAS,KAAK,UAAUC,GAAQ,CAAC,CAAC,EAE5C,OAAMC,KAAOH,KACXA,GAAMG,CAAG,EAAIC,GAAQH,EAAQC,CAAI,GAG5BF,GAAMG,CAAG,EAClB,EAEA,SAASC,GAASH,EAAQC,EAAM,CAC9B,GAAI,EAAAD,GAAU,MAAQA,IAAW,IACjC,KAAIR,EAAMY,GACRC,GAAkBL,CAAM,EAAI,UAAYA,EAASA,CACnD,EACIM,EAASC,GAAYf,CAAG,EACxBgB,EAAgBhB,EAAI,MAAM,2CAA2C,EACrEiB,EAAU,OAAO,KAAKhB,EAAQ,EAAE,IAAI,SAAUiB,EAAa,CAC7D,GAAI,CACF,IAAIC,EAAclB,GAASiB,CAAW,EAClCE,EAAO,KACPN,EAAO,MAAQR,GAAcQ,EAAO,QAAQ,IAC9CM,EAAON,EAAO,MAEhB,IAAIO,EAAaP,EAAO,KAAO,mBAAmBA,EAAO,KAAK,OAAO,CAAC,CAAC,EAAI,KACvEQ,EAAO,KACPC,EAAU,KACVC,EAAwB,KAC5B,GAAIR,GAAiBA,EAAc,CAAC,IAAME,EACxCI,EAAON,EAAc,CAAC,GAAK,mBAAmBA,EAAc,CAAC,CAAC,EAC9DO,EAAU,mBAAmBP,EAAc,CAAC,EAAE,QAAQ,SAAU,EAAE,CAAC,EACnEQ,EAAwB,eACnB,CAGL,GAFIV,EAAO,MAAQA,EAAO,OAASK,EAAY,QAAUL,EAAO,KAAK,QAAQ,UAAW,EAAE,IAAMK,EAAY,QACxG,CAACA,EAAY,aAAa,KAAKL,EAAO,QAAQ,GAC9C,CAACA,EAAO,KAAM,OAClB,IAAIW,EAAYN,EAAY,UACxBO,EAAUZ,EAAO,KAAK,MAAMW,CAAS,EACzC,GAAI,CAACC,EAAS,OAEVA,EAAQ,CAAC,IAAM,MAAQA,EAAQ,CAAC,IAAM,SACxCJ,EAAO,mBAAmBI,EAAQ,CAAC,EAAE,QAAQ,KAAM,EAAE,CAAC,GAExDH,EAAU,mBAAmBG,EAAQ,CAAC,CAAC,EACvCF,EAAwBpB,GAAyBU,EAAO,QAAQ,CAClE,CACA,OAAO,IAAIZ,GAAQgB,EAAaI,EAAMF,EAAMG,EAASF,EAAYG,EAAuBf,CAAI,CAC9F,OAASkB,EAAI,CAEX,GAAI,EAAAA,aAAc,UACX,MAAMA,CACf,CACF,CAAC,EAAE,OAAO,SAAUR,EAAa,CAAE,OAAOA,CAAY,CAAC,EACvD,GAAIF,EAAQ,SAAW,EACvB,OAAOA,EAAQ,CAAC,EAClB,CAEA,SAASJ,GAAmBe,EAAK,CAS/B,MAAO,+CAA+C,KAAKA,CAAG,CAChE,CAEA,SAAShB,GAAsBJ,EAAQ,CAErC,IAAIM,EAASd,GAAI,MAAMQ,CAAM,EAC7B,OAAIM,EAAO,WAAa,SAAWA,EAAO,MAAQ,CAACA,EAAO,KACjDA,EAAO,SAAW,IAAMA,EAAO,KAE/BN,CAEX,CAEA,SAASO,GAAaP,EAAQ,CAC5B,IAAIkB,EAAUlB,EAAO,MAAM,+DAA+D,EAC1F,GAAI,CAACkB,EAAS,CACZ,IAAIG,EAAS7B,GAAI,MAAMQ,CAAM,EAG7B,GAAIqB,EAAO,MAAQ,OAAO7B,GAAI,KAAQ,WAAY,CAUhD,IAAI8B,EAAYtB,EAAO,MAAM,cAAc,EAE3C,GAAIsB,EAAW,CACb,IAAIC,EAAS,IAAI/B,GAAI,IAAI8B,EAAU,CAAC,CAAC,EACrCD,EAAO,KAAOE,EAAO,UAAY,GAC7BA,EAAO,WAAUF,EAAO,MAAQ,IAAME,EAAO,SACnD,CACF,CACA,OAAOF,CACT,CACA,MAAO,CACL,SAAU,WACV,QAAS,GACT,KAAMH,EAAQ,CAAC,EACf,KAAMA,EAAQ,CAAC,EACf,KAAM,KACN,SAAUA,EAAQ,CAAC,EACnB,KAAMA,EAAQ,CAAC,EACf,OAAQ,KACR,MAAO,KACP,SAAU,IAAMA,EAAQ,CAAC,EACzB,KAAM,IAAMA,EAAQ,CAAC,EACrB,KAAM,aAAeA,EAAQ,CAAC,EAAI,IAAMA,EAAQ,CAAC,EAC3C,IAAMA,EAAQ,CAAC,GAAKA,EAAQ,CAAC,GAAK,GAC1C,CACF,ICnJA,IAAAM,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAIC,GAAK,QAAQ,IAAI,EAIrBD,GAAO,QAAUC,GAAG,SAAW,UAAmB,CAC9C,IAAIC,EAAO,QAAQ,IAAI,KACnBC,EAAO,QAAQ,IAAI,SAAW,QAAQ,IAAI,MAAQ,QAAQ,IAAI,OAAS,QAAQ,IAAI,SAEvF,OAAI,QAAQ,WAAa,QACd,QAAQ,IAAI,aAAe,QAAQ,IAAI,UAAY,QAAQ,IAAI,UAAYD,GAAQ,KAG1F,QAAQ,WAAa,SACdA,IAASC,EAAO,UAAYA,EAAO,MAG1C,QAAQ,WAAa,QACdD,IAAS,QAAQ,OAAO,IAAM,EAAI,QAAWC,EAAO,SAAWA,EAAO,MAG1ED,GAAQ,IACnB,ICvBA,IAAAE,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAO,QAAU,UAAY,CAEzB,IAAIC,EAAwB,MAAM,kBAClC,MAAM,kBAAoB,SAAUC,EAAGC,EAAO,CAAE,OAAOA,CAAO,EAC9D,IAAIA,EAAS,IAAI,MAAM,EAAG,MAC1B,aAAM,kBAAoBF,EACnBE,EAAM,CAAC,EAAE,YAAY,CAChC,ICPA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAIC,GAAY,QAAQ,WAAa,QAGjCC,GACA,6HAEAC,GAAQ,CAAC,EAEb,SAASC,GAAeC,EAAU,CAChC,OAAOH,GAAe,KAAKG,CAAQ,EAAE,MAAM,CAAC,CAC9C,CAEAF,GAAM,MAAQ,SAASG,EAAY,CACjC,GAAI,OAAOA,GAAe,SACxB,MAAM,IAAI,UACN,gDAAkD,OAAOA,CAC7D,EAEF,IAAIC,EAAWH,GAAeE,CAAU,EACxC,GAAI,CAACC,GAAYA,EAAS,SAAW,EACnC,MAAM,IAAI,UAAU,iBAAmBD,EAAa,GAAG,EAEzD,MAAO,CACL,KAAMC,EAAS,CAAC,EAChB,IAAKA,EAAS,CAAC,IAAMA,EAAS,CAAC,EAAIA,EAAS,CAAC,EAAIA,EAAS,CAAC,EAAE,MAAM,EAAG,EAAE,EACxE,KAAMA,EAAS,CAAC,EAChB,IAAKA,EAAS,CAAC,EACf,KAAMA,EAAS,CAAC,CAClB,CACF,EAMA,IAAIC,GACA,8DACAC,GAAQ,CAAC,EAGb,SAASC,GAAeL,EAAU,CAChC,OAAOG,GAAY,KAAKH,CAAQ,EAAE,MAAM,CAAC,CAC3C,CAGAI,GAAM,MAAQ,SAASH,EAAY,CACjC,GAAI,OAAOA,GAAe,SACxB,MAAM,IAAI,UACN,gDAAkD,OAAOA,CAC7D,EAEF,IAAIC,EAAWG,GAAeJ,CAAU,EACxC,GAAI,CAACC,GAAYA,EAAS,SAAW,EACnC,MAAM,IAAI,UAAU,iBAAmBD,EAAa,GAAG,EAGzD,MAAO,CACL,KAAMC,EAAS,CAAC,EAChB,IAAKA,EAAS,CAAC,EAAE,MAAM,EAAG,EAAE,EAC5B,KAAMA,EAAS,CAAC,EAChB,IAAKA,EAAS,CAAC,EACf,KAAMA,EAAS,CAAC,CAClB,CACF,EAGIN,GACFD,GAAO,QAAUG,GAAM,MAEvBH,GAAO,QAAUS,GAAM,MAEzBT,GAAO,QAAQ,MAAQS,GAAM,MAC7BT,GAAO,QAAQ,MAAQG,GAAM,QC1E7B,IAAAQ,GAAAC,EAAA,CAAAC,GAAAC,KAAA,KAAIC,GAAO,QAAQ,MAAM,EACrBC,GAAQD,GAAK,OAAS,KAEtBE,GAAqB,SAA4BC,EAAeC,EAAS,CACzE,IAAIC,EAAS,IACR,eAAgB,KAAKF,CAAa,EACnCE,EAAS,GACD,QAAS,KAAKF,CAAa,IACnCE,EAAS,QAKb,QAFIC,EAAQ,CAACH,CAAa,EACtBI,EAASN,GAAME,CAAa,EACzBI,EAAO,MAAQD,EAAMA,EAAM,OAAS,CAAC,GACxCA,EAAM,KAAKC,EAAO,GAAG,EACrBA,EAASN,GAAMM,EAAO,GAAG,EAG7B,OAAOD,EAAM,OAAO,SAAUE,EAAMC,EAAO,CACvC,OAAOD,EAAK,OAAOJ,EAAQ,IAAI,SAAUM,EAAW,CAChD,OAAOV,GAAK,QAAQK,EAAQI,EAAOC,CAAS,CAChD,CAAC,CAAC,CACN,EAAG,CAAC,CAAC,CACT,EAEAX,GAAO,QAAU,SAA0BY,EAAOC,EAAMC,EAAS,CAC7D,IAAIT,EAAUQ,GAAQA,EAAK,gBACrB,CAAC,EAAE,OAAOA,EAAK,eAAe,EAC9B,CAAC,cAAc,EAErB,GAAIA,GAAQ,OAAOA,EAAK,OAAU,WAC9B,OAAOA,EAAK,MACRC,EACAF,EACA,UAAY,CAAE,OAAOT,GAAmBS,EAAOP,CAAO,CAAG,EACzDQ,CACJ,EAGJ,IAAIJ,EAAON,GAAmBS,EAAOP,CAAO,EAC5C,OAAOQ,GAAQA,EAAK,MAAQJ,EAAK,OAAOI,EAAK,KAAK,EAAIJ,CAC1D,ICzCA,IAAAM,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAO,QAAU,SAAUC,EAAGC,EAAM,CAQhC,OAAOA,GAAQ,CAAC,CACpB,ICTA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAIA,IAAIC,GAAgB,kDAChBC,GAAQ,OAAO,UAAU,SACzBC,GAAM,KAAK,IACXC,GAAW,oBAEXC,GAAW,SAAkBC,EAAGC,EAAG,CAGnC,QAFIC,EAAM,CAAC,EAEF,EAAI,EAAG,EAAIF,EAAE,OAAQ,GAAK,EAC/BE,EAAI,CAAC,EAAIF,EAAE,CAAC,EAEhB,QAASG,EAAI,EAAGA,EAAIF,EAAE,OAAQE,GAAK,EAC/BD,EAAIC,EAAIH,EAAE,MAAM,EAAIC,EAAEE,CAAC,EAG3B,OAAOD,CACX,EAEIE,GAAQ,SAAeC,EAASC,EAAQ,CAExC,QADIJ,EAAM,CAAC,EACF,EAAII,GAAU,EAAGH,EAAI,EAAG,EAAIE,EAAQ,OAAQ,GAAK,EAAGF,GAAK,EAC9DD,EAAIC,CAAC,EAAIE,EAAQ,CAAC,EAEtB,OAAOH,CACX,EAEIK,GAAQ,SAAUL,EAAKM,EAAQ,CAE/B,QADIC,EAAM,GACDC,EAAI,EAAGA,EAAIR,EAAI,OAAQQ,GAAK,EACjCD,GAAOP,EAAIQ,CAAC,EACRA,EAAI,EAAIR,EAAI,SACZO,GAAOD,GAGf,OAAOC,CACX,EAEAf,GAAO,QAAU,SAAciB,EAAM,CACjC,IAAIC,EAAS,KACb,GAAI,OAAOA,GAAW,YAAchB,GAAM,MAAMgB,CAAM,IAAMd,GACxD,MAAM,IAAI,UAAUH,GAAgBiB,CAAM,EAyB9C,QAvBIC,EAAOT,GAAM,UAAW,CAAC,EAEzBU,EACAC,EAAS,UAAY,CACrB,GAAI,gBAAgBD,EAAO,CACvB,IAAIE,EAASJ,EAAO,MAChB,KACAb,GAASc,EAAM,SAAS,CAC5B,EACA,OAAI,OAAOG,CAAM,IAAMA,EACZA,EAEJ,IACX,CACA,OAAOJ,EAAO,MACVD,EACAZ,GAASc,EAAM,SAAS,CAC5B,CAEJ,EAEII,EAAcpB,GAAI,EAAGe,EAAO,OAASC,EAAK,MAAM,EAChDK,EAAY,CAAC,EACRR,EAAI,EAAGA,EAAIO,EAAaP,IAC7BQ,EAAUR,CAAC,EAAI,IAAMA,EAKzB,GAFAI,EAAQ,SAAS,SAAU,oBAAsBP,GAAMW,EAAW,GAAG,EAAI,2CAA2C,EAAEH,CAAM,EAExHH,EAAO,UAAW,CAClB,IAAIO,EAAQ,UAAiB,CAAC,EAC9BA,EAAM,UAAYP,EAAO,UACzBE,EAAM,UAAY,IAAIK,EACtBA,EAAM,UAAY,IACtB,CAEA,OAAOL,CACX,ICnFA,IAAAM,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAIC,GAAiB,KAErBD,GAAO,QAAU,SAAS,UAAU,MAAQC,KCJ5C,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAIC,GAAO,SAAS,UAAU,KAC1BC,GAAU,OAAO,UAAU,eAC3BC,GAAO,KAGXH,GAAO,QAAUG,GAAK,KAAKF,GAAMC,EAAO,ICPxC,IAAAE,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAA,SACC,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,gBAAiB,QACjB,qBAAsB,QACtB,YAAe,OACf,mBAAoB,CAAC,mBAAoB,OAAO,EAChD,eAAkB,oBAClB,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,cAAiB,GACjB,qBAAsB,CAAC,mBAAoB,OAAO,EAClD,QAAW,SACX,eAAgB,CAAC,mBAAoB,OAAO,EAC5C,QAAW,GACX,eAAgB,CAAC,mBAAoB,OAAO,EAC5C,UAAa,GACb,iBAAkB,CAAC,mBAAoB,OAAO,EAC9C,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,aAAgB,cAChB,UAAa,MACb,MAAS,GACT,aAAc,CAAC,mBAAoB,OAAO,EAC1C,oBAAuB,CAAC,mBAAoB,SAAS,EACrD,2BAA4B,CAAC,mBAAoB,OAAO,EACxD,IAAO,GACP,WAAY,CAAC,mBAAoB,OAAO,EACxC,eAAgB,QAChB,oBAAqB,QACrB,OAAU,YACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,SAAY,MACZ,GAAM,GACN,UAAW,CAAC,mBAAoB,OAAO,EACvC,cAAe,CAAC,kBAAmB,OAAO,EAC1C,mBAAoB,CAAC,mBAAoB,OAAO,EAChD,YAAe,YACf,mBAAoB,CAAC,mBAAoB,OAAO,EAChD,aAAgB,YAChB,oBAAqB,CAAC,mBAAoB,OAAO,EACjD,aAAgB,YAChB,oBAAqB,CAAC,mBAAoB,OAAO,EACjD,eAAkB,YAClB,sBAAuB,CAAC,mBAAoB,OAAO,EACnD,eAAkB,YAClB,sBAAuB,CAAC,mBAAoB,OAAO,EACnD,aAAgB,YAChB,oBAAqB,CAAC,mBAAoB,OAAO,EACjD,KAAQ,GACR,YAAa,CAAC,mBAAoB,OAAO,EACzC,MAAS,SACT,aAAc,CAAC,mBAAoB,OAAO,EAC1C,MAAS,GACT,aAAc,CAAC,mBAAoB,OAAO,EAC1C,UAAa,OACb,iBAAkB,CAAC,mBAAoB,OAAO,EAC9C,qBAAsB,CAAC,OAAO,EAC9B,0BAA2B,CAAC,OAAO,EACnC,UAAa,MACb,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,IAAO,GACP,WAAY,CAAC,mBAAoB,OAAO,EACxC,4BAA6B,iBAC7B,2CAA4C,iBAC5C,yCAA0C,iBAC1C,GAAM,GACN,UAAW,CAAC,mBAAoB,OAAO,EACvC,KAAQ,GACR,YAAa,CAAC,mBAAoB,OAAO,EACzC,aAAc,UACd,kBAAmB,QACnB,aAAc,UACd,kBAAmB,QACnB,WAAc,SACd,kBAAmB,CAAC,mBAAoB,OAAO,EAC/C,QAAW,OACX,eAAgB,CAAC,mBAAoB,OAAO,EAC5C,SAAY,SACZ,gBAAiB,CAAC,mBAAoB,OAAO,EAC7C,YAAe,GACf,mBAAoB,CAAC,mBAAoB,OAAO,EAChD,SAAY,GACZ,gBAAiB,CAAC,mBAAoB,OAAO,EAC7C,oBAAqB,QACrB,yBAA0B,QAC1B,KAAQ,GACR,YAAa,CAAC,mBAAoB,OAAO,EACzC,WAAY,CAAC,mBAAoB,SAAS,EAC1C,QAAW,mBACX,eAAkB,WAClB,sBAAuB,CAAC,mBAAoB,OAAO,EACnD,kBAAqB,WACrB,yBAA0B,CAAC,mBAAoB,OAAO,EACtD,aAAgB,WAChB,oBAAqB,CAAC,mBAAoB,OAAO,EACjD,oBAAuB,WACvB,2BAA4B,CAAC,mBAAoB,OAAO,EACxD,iBAAoB,WACpB,wBAAyB,CAAC,mBAAoB,OAAO,EACrD,iBAAoB,WACpB,wBAAyB,CAAC,mBAAoB,OAAO,EACrD,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,mBAAoB,UACpB,wBAAyB,UACzB,kBAAmB,QACnB,uBAAwB,QACxB,aAAc,UACd,kBAAmB,UACnB,eAAkB,GAClB,sBAAuB,CAAC,mBAAoB,OAAO,EACnD,IAAO,CAAC,kBAAmB,QAAQ,EACnC,WAAY,CAAC,mBAAoB,OAAO,EACxC,iBAAkB,oBAClB,sBAAuB,CAAC,mBAAoB,UAAW,OAAO,EAC9D,mBAAoB,oBACpB,wBAAyB,oBACzB,YAAa,CAAC,mBAAoB,OAAO,EACzC,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,kBAAmB,QACnB,uBAAwB,QACxB,YAAe,aACf,mBAAoB,CAAC,mBAAoB,OAAO,EAChD,YAAe,oBACf,UAAa,YACb,iBAAkB,CAAC,mBAAoB,OAAO,EAC9C,IAAO,GACP,WAAY,CAAC,mBAAoB,OAAO,EACxC,aAAgB,QAChB,oBAAqB,CAAC,mBAAoB,OAAO,EACjD,IAAO,GACP,WAAY,CAAC,mBAAoB,OAAO,EACxC,IAAO,GACP,WAAY,CAAC,mBAAoB,OAAO,EACxC,KAAQ,GACR,YAAa,CAAC,mBAAoB,OAAO,EACzC,aAAc,UACd,kBAAmB,QACnB,qBAAsB,gBACtB,mBAAoB,CAAC,gBAAiB,gBAAgB,EACtD,qBAAsB,CAAC,gBAAiB,gBAAgB,EACxD,qBAAsB,CAAC,gBAAiB,gBAAgB,EACxD,qBAAsB,CAAC,gBAAiB,gBAAgB,EACxD,wBAAyB,CAAC,gBAAiB,gBAAgB,EAC3D,qBAAsB,CAAC,gBAAiB,gBAAgB,EACxD,GAAM,OACN,UAAW,CAAC,mBAAoB,OAAO,EACvC,GAAM,GACN,UAAW,CAAC,mBAAoB,OAAO,EACvC,KAAQ,CAAC,oBAAqB,mBAAoB,OAAO,EACzD,YAAa,CAAC,mBAAoB,OAAO,EACzC,eAAkB,UAClB,sBAAuB,CAAC,mBAAoB,OAAO,EACnD,KAAQ,SACR,YAAa,CAAC,mBAAoB,OAAO,CAC1C,IChKA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAIC,GAAS,KAEb,SAASC,GAAkBC,EAASC,EAAW,CAM9C,QALIC,EAAYF,EAAQ,MAAM,GAAG,EAC7BG,EAAQF,EAAU,MAAM,GAAG,EAC3BG,EAAKD,EAAM,OAAS,EAAIA,EAAM,CAAC,EAAI,IACnCE,GAAgBF,EAAM,OAAS,EAAIA,EAAM,CAAC,EAAIA,EAAM,CAAC,GAAG,MAAM,GAAG,EAE5DG,EAAI,EAAGA,EAAI,EAAG,EAAEA,EAAG,CAC3B,IAAIC,EAAM,SAASL,EAAUI,CAAC,GAAK,EAAG,EAAE,EACpCE,EAAM,SAASH,EAAaC,CAAC,GAAK,EAAG,EAAE,EAC3C,GAAIC,IAAQC,EAGZ,OAAIJ,IAAO,IACHG,EAAMC,EAEVJ,IAAO,KACHG,GAAOC,EAER,EACR,CACA,OAAOJ,IAAO,IACf,CAEA,SAASK,GAAaT,EAASU,EAAO,CACrC,IAAIC,EAAaD,EAAM,MAAM,QAAQ,EACrC,GAAIC,EAAW,SAAW,EACzB,MAAO,GAER,QAASL,EAAI,EAAGA,EAAIK,EAAW,OAAQ,EAAEL,EACxC,GAAI,CAACP,GAAkBC,EAASW,EAAWL,CAAC,CAAC,EAC5C,MAAO,GAGT,MAAO,EACR,CAEA,SAASM,GAAgBC,EAAaC,EAAgB,CACrD,GAAI,OAAOA,GAAmB,UAC7B,OAAOA,EAGR,IAAId,EAAU,OAAOa,EAAgB,IAClC,QAAQ,UAAY,QAAQ,SAAS,KACrCA,EAEH,GAAI,OAAOb,GAAY,SACtB,MAAM,IAAI,UAAU,OAAOa,EAAgB,IAAc,2CAA6C,+CAA+C,EAGtJ,GAAIC,GAAkB,OAAOA,GAAmB,SAAU,CACzD,QAASR,EAAI,EAAGA,EAAIQ,EAAe,OAAQ,EAAER,EAC5C,GAAIG,GAAaT,EAASc,EAAeR,CAAC,CAAC,EAC1C,MAAO,GAGT,MAAO,EACR,CACA,OAAOG,GAAaT,EAASc,CAAc,CAC5C,CAEA,IAAIC,GAAO,KAEXlB,GAAO,QAAU,SAAgBmB,EAAGH,EAAa,CAChD,OAAOf,GAAOiB,GAAMC,CAAC,GAAKJ,GAAgBC,EAAaE,GAAKC,CAAC,CAAC,CAC/D,ICpEA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,KAAIC,GAAK,QAAQ,IAAI,EACjBC,GAAa,KACbC,EAAO,QAAQ,MAAM,EACrBC,GAAS,KACTC,GAAmB,KACnBC,GAAmB,KACnBC,GAAS,KAETC,GAAa,QAAQ,WAAa,SAAWP,GAAG,UAAY,OAAOA,GAAG,SAAS,QAAW,WAAaA,GAAG,SAAS,OAASA,GAAG,SAE/HQ,GAAUP,GAAW,EACrBQ,GAAe,UAAY,CAC3B,MAAO,CACHP,EAAK,KAAKM,GAAS,eAAe,EAClCN,EAAK,KAAKM,GAAS,iBAAiB,CACxC,CACJ,EAEIE,GAAgB,SAAgBC,EAAMC,EAAI,CAC1CZ,GAAG,KAAKW,EAAM,SAAUE,EAAKC,EAAM,CAC/B,OAAKD,EAGDA,EAAI,OAAS,UAAYA,EAAI,OAAS,UAAkBD,EAAG,KAAM,EAAK,EACnEA,EAAGC,CAAG,EAHFD,EAAG,KAAME,EAAK,OAAO,GAAKA,EAAK,OAAO,CAAC,CAItD,CAAC,CACL,EAEIC,GAAe,SAAqBC,EAAKJ,EAAI,CAC7CZ,GAAG,KAAKgB,EAAK,SAAUH,EAAKC,EAAM,CAC9B,OAAKD,EAGDA,EAAI,OAAS,UAAYA,EAAI,OAAS,UAAkBD,EAAG,KAAM,EAAK,EACnEA,EAAGC,CAAG,EAHFD,EAAG,KAAME,EAAK,YAAY,CAAC,CAI1C,CAAC,CACL,EAEIG,GAAkB,SAAkBC,EAAGN,EAAI,CAC3CL,GAAWW,EAAG,SAAUC,EAAaC,EAAU,CACvCD,GAAeA,EAAY,OAAS,SAAUP,EAAGO,CAAW,EAC3DP,EAAG,KAAMO,EAAcD,EAAIE,CAAQ,CAC5C,CAAC,CACL,EAEIC,GAAgB,SAAuBC,EAAUJ,EAAGK,EAAMX,EAAI,CAC1DW,GAAQA,EAAK,mBAAqB,GAClCD,EAASJ,EAAGN,CAAE,EAEdA,EAAG,KAAMM,CAAC,CAElB,EAEIM,GAAqB,SAA4BC,EAAUC,EAASd,EAAI,CACxEa,EAASC,EAAS,SAAUC,EAAaC,EAAM,CAC3C,GAAID,EAAaf,EAAGe,CAAW,MAE3B,IAAI,CACA,IAAIE,EAAM,KAAK,MAAMD,CAAI,EACzBhB,EAAG,KAAMiB,CAAG,CAChB,MAAkB,CACdjB,EAAG,IAAI,CACX,CAER,CAAC,CACL,EAEIkB,GAAuB,SAA8BZ,EAAGa,EAAOR,EAAM,CAErE,QADIS,EAAO5B,GAAiB2B,EAAOR,EAAML,CAAC,EACjCe,EAAI,EAAGA,EAAID,EAAK,OAAQC,IAC7BD,EAAKC,CAAC,EAAI/B,EAAK,KAAK8B,EAAKC,CAAC,EAAGf,CAAC,EAElC,OAAOc,CACX,EAEAjC,GAAO,QAAU,SAAiBmB,EAAGgB,EAASC,EAAU,CACpD,IAAIvB,EAAKuB,EACLZ,EAAOW,EAKX,GAJI,OAAOA,GAAY,aACnBtB,EAAKW,EACLA,EAAO,CAAC,GAER,OAAOL,GAAM,SAAU,CACvB,IAAIL,EAAM,IAAI,UAAU,wBAAwB,EAChD,OAAO,QAAQ,SAAS,UAAY,CAChCD,EAAGC,CAAG,CACV,CAAC,CACL,CAEAU,EAAOlB,GAAiBa,EAAGK,CAAI,EAE/B,IAAIa,EAASb,EAAK,QAAUb,GACxB2B,EAAcd,EAAK,aAAeR,GAClCU,EAAWF,EAAK,UAAYvB,GAAG,SAC/BsB,EAAWC,EAAK,UAAYN,GAC5BqB,EAAcf,EAAK,aAAeC,GACtC,GAAID,EAAK,UAAYA,EAAK,YAAa,CACnC,IAAIgB,EAAc,IAAI,UAAU,sDAAsD,EACtF,OAAO,QAAQ,SAAS,UAAY,CAChC3B,EAAG2B,CAAW,CAClB,CAAC,CACL,CACA,IAAIC,EAAkBjB,EAAK,gBAEvBkB,EAAalB,EAAK,YAAc,CAAC,KAAK,EACtCmB,EAAqBnB,EAAK,qBAAuB,GACjDoB,EAAUpB,EAAK,SAAWrB,EAAK,QAAQC,GAAO,CAAC,EAC/CyC,EAASrB,EAAK,UAAYoB,EAE9BpB,EAAK,MAAQA,EAAK,OAASd,GAAa,EAGxC,IAAIoC,GAAgB3C,EAAK,QAAQyC,CAAO,EAExCtB,GACIC,EACAuB,GACAtB,EACA,SAAUV,EAAKiC,EAAW,CAClBjC,EAAKD,EAAGC,CAAG,EACVkC,GAAKD,CAAS,CACvB,CACJ,EAEA,IAAIE,EACJ,SAASD,GAAKJ,EAAS,CACnB,GAAK,0CAA2C,KAAKzB,CAAC,EAClD8B,EAAM9C,EAAK,QAAQyC,EAASzB,CAAC,GACzBA,IAAM,KAAOA,IAAM,MAAQA,EAAE,MAAM,EAAE,IAAM,OAAK8B,GAAO,KACtD,MAAO,KAAK9B,CAAC,GAAK8B,IAAQL,EAC3BM,EAAgBD,EAAKzB,EAAK,QAAS2B,EAAM,EACtCC,EAAWH,EAAKzB,EAAK,QAAS2B,EAAM,MACxC,IAAIR,GAAsBpC,GAAOY,CAAC,EACrC,OAAON,EAAG,KAAMM,CAAC,EACdkC,EAAgBlC,EAAGyB,EAAS,SAAU9B,EAAKwC,EAAGxB,EAAK,CACtD,GAAIhB,EAAKD,EAAGC,CAAG,MACV,IAAIwC,EACL,OAAOhC,GAAcC,EAAU+B,EAAG9B,EAAM,SAAUV,EAAKyC,EAAO,CACtDzC,EACAD,EAAGC,CAAG,EAEND,EAAG,KAAM0C,EAAOzB,CAAG,CAE3B,CAAC,EAED,IAAI0B,EAAc,IAAI,MAAM,uBAAyBrC,EAAI,WAAa0B,EAAS,GAAG,EAClFW,EAAY,KAAO,mBACnB3C,EAAG2C,CAAW,EAEtB,CAAC,EACL,CAEA,SAASL,GAAOrC,EAAK2C,EAAG3B,EAAK,CACrBhB,EAAKD,EAAGC,CAAG,EACN2C,EAAG5C,EAAG,KAAM4C,EAAG3B,CAAG,EACtBoB,EAAgBD,EAAK,SAAUnC,EAAK4C,EAAG5B,EAAK,CAC7C,GAAIhB,EAAKD,EAAGC,CAAG,UACN4C,EACLpC,GAAcC,EAAUmC,EAAGlC,EAAM,SAAUV,EAAK6C,EAAO,CAC/C7C,EACAD,EAAGC,CAAG,EAEND,EAAG,KAAM8C,EAAO7B,CAAG,CAE3B,CAAC,MACE,CACH,IAAI0B,EAAc,IAAI,MAAM,uBAAyBrC,EAAI,WAAa0B,EAAS,GAAG,EAClFW,EAAY,KAAO,mBACnB3C,EAAG2C,CAAW,CAClB,CACJ,CAAC,CACL,CAEA,SAASJ,EAAWjC,EAAGyC,EAAYxB,EAAU,CACzC,IAAIyB,EAAoBD,EACpB/C,EAAKuB,EACL,OAAOyB,GAAsB,aAC7BhD,EAAKgD,EACLA,EAAoB,QAGxB,IAAIC,EAAO,CAAC,EAAE,EAAE,OAAOpB,CAAU,EACjCqB,EAAKD,EAAM3C,EAAG0C,CAAiB,EAE/B,SAASE,EAAKD,EAAM3C,EAAG6C,GAAa,CAChC,GAAIF,EAAK,SAAW,EAAG,OAAOjD,EAAG,KAAM,OAAWmD,EAAW,EAC7D,IAAIpD,GAAOO,EAAI2C,EAAK,CAAC,EAEjBhC,EAAMkC,GACNlC,EAAKmC,EAAM,KAAMnC,CAAG,EACnBoC,EAAQ/D,EAAK,QAAQS,EAAI,EAAGqD,CAAK,EAEtC,SAASA,EAAMnD,GAAKqD,GAAMlD,GAAK,CAE3B,GADAa,EAAMqC,GACFrD,GAAK,OAAOD,EAAGC,EAAG,EACtB,GAAIG,IAAOa,GAAON,EAAK,WAAY,CAC/B,IAAI4C,GAAQjE,EAAK,SAASc,GAAKL,EAAI,EAC/ByD,GAAMD,GAAM,MAAM,EAAGA,GAAM,OAASN,EAAK,CAAC,EAAE,MAAM,EAClDQ,GAAI9C,EAAK,WAAWM,EAAKX,EAAGkD,EAAG,EACnC,GAAIC,GAAG,OAAOP,EACV,CAAC,EAAE,EAAE,OAAOrB,EAAW,MAAM,CAAC,EAC9BvC,EAAK,QAAQc,GAAKqD,EAAC,EACnBxC,CACJ,CACJ,CACAO,EAAOzB,GAAM2D,EAAI,CACrB,CACA,SAASA,GAAKzD,GAAK0D,GAAI,CACnB,GAAI1D,GAAK,OAAOD,EAAGC,EAAG,EACtB,GAAI0D,GAAI,OAAO3D,EAAG,KAAMD,GAAMkB,CAAG,EACjCiC,EAAKD,EAAK,MAAM,CAAC,EAAG3C,EAAGW,CAAG,CAC9B,CACJ,CACJ,CAEA,SAASoC,EAAQjD,EAAKJ,EAAI,CAKtB,GAJII,IAAQ,IAAMA,IAAQ,KACtB,QAAQ,WAAa,SAAY,cAAe,KAAKA,CAAG,GAGvD,2BAA4B,KAAKA,CAAG,EAAG,OAAOJ,EAAG,IAAI,EAE1DS,GAAcC,EAAUN,EAAKO,EAAM,SAAUiD,EAAWC,EAAQ,CAC5D,GAAID,EAAW,OAAOP,EAAQ/D,EAAK,QAAQc,CAAG,EAAGJ,CAAE,EACnD,IAAIc,EAAUxB,EAAK,KAAKuE,EAAQ,cAAc,EAC9CrC,EAAOV,EAAS,SAAUb,EAAK0D,EAAI,CAE/B,GAAI,CAACA,EAAI,OAAON,EAAQ/D,EAAK,QAAQc,CAAG,EAAGJ,CAAE,EAE7C0B,EAAYb,EAAUC,EAAS,SAAUb,EAAK6D,EAAU,CAChD7D,GAAKD,EAAGC,CAAG,EAEf,IAAIgB,GAAM6C,EAEN7C,IAAON,EAAK,gBACZM,GAAMN,EAAK,cAAcM,GAAKH,CAAO,GAEzCd,EAAG,KAAMiB,GAAKb,CAAG,CACrB,CAAC,CACL,CAAC,CACL,CAAC,CACL,CAEA,SAASiC,EAAgB/B,EAAGyD,EAAwBxC,EAAU,CAC1D,IAAIvB,EAAKuB,EACLyC,EAAOD,EACP,OAAOC,GAAS,aAChBhE,EAAKgE,EACLA,EAAOrD,EAAK,SAGhBF,GAAcC,EAAUJ,EAAGK,EAAM,SAAUiD,EAAWC,EAAQ,CAC1D,GAAID,EAAW,OAAO5D,EAAG4D,CAAS,EAClC,IAAI9C,EAAUxB,EAAK,KAAKuE,EAAQ,cAAc,EAC9CrC,EAAOV,EAAS,SAAUb,EAAK0D,GAAI,CAC/B,GAAI1D,EAAK,OAAOD,EAAGC,CAAG,EACtB,GAAI,CAAC0D,GAAI,OAAOpB,EAAWjD,EAAK,KAAKgB,EAAG,OAAO,EAAG0D,EAAMhE,CAAE,EAE1D0B,EAAYb,EAAUC,EAAS,SAAUb,GAAK6D,EAAU,CACpD,GAAI7D,GAAK,OAAOD,EAAGC,EAAG,EAEtB,IAAIgB,EAAM6C,EAMV,GAJI7C,GAAON,EAAK,gBACZM,EAAMN,EAAK,cAAcM,EAAKH,CAAO,GAGrCG,GAAOA,EAAI,KAAM,CACjB,GAAI,OAAOA,EAAI,MAAS,SAAU,CAC9B,IAAIgD,GAAY,IAAI,UAAU,iBAAchD,EAAI,KAAO,gCAA2B,EAClF,OAAAgD,GAAU,KAAO,uBACVjE,EAAGiE,EAAS,CACvB,EACIhD,EAAI,OAAS,KAAOA,EAAI,OAAS,QACjCA,EAAI,KAAO,SAEfsB,EAAWjD,EAAK,QAAQgB,EAAGW,EAAI,IAAI,EAAGA,EAAK,SAAUhB,GAAK2C,GAAG3B,GAAK,CAC9D,GAAIhB,GAAK,OAAOD,EAAGC,EAAG,EACtB,GAAI2C,GAAG,OAAO5C,EAAG,KAAM4C,GAAG3B,EAAG,EAC7B,GAAI,CAACA,GAAK,OAAOsB,EAAWjD,EAAK,KAAKgB,EAAG,OAAO,EAAGW,GAAKjB,CAAE,EAE1D,IAAII,GAAMd,EAAK,QAAQgB,EAAGW,GAAI,IAAI,EAClCoB,EAAgBjC,GAAKa,GAAK,SAAUhB,GAAKwC,GAAGxB,GAAK,CAC7C,GAAIhB,GAAK,OAAOD,EAAGC,EAAG,EACtB,GAAIwC,GAAG,OAAOzC,EAAG,KAAMyC,GAAGxB,EAAG,EAC7BsB,EAAWjD,EAAK,KAAKgB,EAAG,OAAO,EAAGW,GAAKjB,CAAE,CAC7C,CAAC,CACL,CAAC,EACD,MACJ,CAEAuC,EAAWjD,EAAK,KAAKgB,EAAG,QAAQ,EAAGW,EAAKjB,CAAE,CAC9C,CAAC,CACL,CAAC,CACL,CAAC,CACL,CAEA,SAASkE,EAAYlE,EAAIoB,EAAM,CAC3B,GAAIA,EAAK,SAAW,EAAG,OAAOpB,EAAG,KAAM,MAAS,EAChD,IAAII,EAAMgB,EAAK,CAAC,EAEhBK,EAAYnC,EAAK,QAAQc,CAAG,EAAG+D,CAAK,EAEpC,SAASA,EAAMlE,EAAKkE,EAAO,CACvB,GAAIlE,EAAK,OAAOD,EAAGC,CAAG,EACtB,GAAI,CAACkE,EAAO,OAAOD,EAAYlE,EAAIoB,EAAK,MAAM,CAAC,CAAC,EAChDmB,EAAWnC,EAAKO,EAAK,QAAS2B,CAAM,CACxC,CAEA,SAASA,EAAOrC,EAAK2C,EAAG3B,EAAK,CACzB,GAAIhB,EAAK,OAAOD,EAAGC,CAAG,EACtB,GAAI2C,EAAG,OAAO5C,EAAG,KAAM4C,EAAG3B,CAAG,EAC7BoB,EAAgBjC,EAAKO,EAAK,QAASyD,CAAK,CAC5C,CAEA,SAASA,EAAMnE,EAAKwC,EAAGxB,EAAK,CACxB,GAAIhB,EAAK,OAAOD,EAAGC,CAAG,EACtB,GAAIwC,EAAG,OAAOzC,EAAG,KAAMyC,EAAGxB,CAAG,EAC7BiD,EAAYlE,EAAIoB,EAAK,MAAM,CAAC,CAAC,CACjC,CACJ,CACA,SAASoB,EAAgBlC,EAAGa,EAAOnB,EAAI,CACnC,IAAIqE,EAAQ,UAAY,CAAE,OAAOnD,GAAqBZ,EAAGa,EAAOR,CAAI,CAAG,EACvEuD,EACIlE,EACA4B,EAAkBA,EAAgBtB,EAAGa,EAAOkD,EAAO1D,CAAI,EAAI0D,EAAM,CACrE,CACJ,CACJ,ICxUA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAA,SACC,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,gBAAiB,QACjB,qBAAsB,QACtB,YAAe,OACf,mBAAoB,CAAC,mBAAoB,OAAO,EAChD,eAAkB,oBAClB,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,cAAiB,GACjB,qBAAsB,CAAC,mBAAoB,OAAO,EAClD,QAAW,SACX,eAAgB,CAAC,mBAAoB,OAAO,EAC5C,QAAW,GACX,eAAgB,CAAC,mBAAoB,OAAO,EAC5C,UAAa,GACb,iBAAkB,CAAC,mBAAoB,OAAO,EAC9C,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,aAAgB,cAChB,UAAa,MACb,MAAS,GACT,aAAc,CAAC,mBAAoB,OAAO,EAC1C,oBAAuB,CAAC,mBAAoB,SAAS,EACrD,2BAA4B,CAAC,mBAAoB,OAAO,EACxD,IAAO,GACP,WAAY,CAAC,mBAAoB,OAAO,EACxC,eAAgB,QAChB,oBAAqB,QACrB,OAAU,YACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,SAAY,MACZ,GAAM,GACN,UAAW,CAAC,mBAAoB,OAAO,EACvC,cAAe,CAAC,kBAAmB,OAAO,EAC1C,mBAAoB,CAAC,mBAAoB,OAAO,EAChD,YAAe,YACf,mBAAoB,CAAC,mBAAoB,OAAO,EAChD,aAAgB,YAChB,oBAAqB,CAAC,mBAAoB,OAAO,EACjD,aAAgB,YAChB,oBAAqB,CAAC,mBAAoB,OAAO,EACjD,eAAkB,YAClB,sBAAuB,CAAC,mBAAoB,OAAO,EACnD,eAAkB,YAClB,sBAAuB,CAAC,mBAAoB,OAAO,EACnD,aAAgB,YAChB,oBAAqB,CAAC,mBAAoB,OAAO,EACjD,KAAQ,GACR,YAAa,CAAC,mBAAoB,OAAO,EACzC,MAAS,SACT,aAAc,CAAC,mBAAoB,OAAO,EAC1C,MAAS,GACT,aAAc,CAAC,mBAAoB,OAAO,EAC1C,UAAa,OACb,iBAAkB,CAAC,mBAAoB,OAAO,EAC9C,qBAAsB,CAAC,OAAO,EAC9B,0BAA2B,CAAC,OAAO,EACnC,UAAa,MACb,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,IAAO,GACP,WAAY,CAAC,mBAAoB,OAAO,EACxC,4BAA6B,iBAC7B,2CAA4C,iBAC5C,yCAA0C,iBAC1C,GAAM,GACN,UAAW,CAAC,mBAAoB,OAAO,EACvC,KAAQ,GACR,YAAa,CAAC,mBAAoB,OAAO,EACzC,aAAc,UACd,kBAAmB,QACnB,aAAc,UACd,kBAAmB,QACnB,WAAc,SACd,kBAAmB,CAAC,mBAAoB,OAAO,EAC/C,QAAW,OACX,eAAgB,CAAC,mBAAoB,OAAO,EAC5C,SAAY,SACZ,gBAAiB,CAAC,mBAAoB,OAAO,EAC7C,YAAe,GACf,mBAAoB,CAAC,mBAAoB,OAAO,EAChD,SAAY,GACZ,gBAAiB,CAAC,mBAAoB,OAAO,EAC7C,oBAAqB,QACrB,yBAA0B,QAC1B,KAAQ,GACR,YAAa,CAAC,mBAAoB,OAAO,EACzC,QAAW,mBACX,eAAkB,WAClB,sBAAuB,CAAC,mBAAoB,OAAO,EACnD,kBAAqB,WACrB,yBAA0B,CAAC,mBAAoB,OAAO,EACtD,aAAgB,WAChB,oBAAqB,CAAC,mBAAoB,OAAO,EACjD,oBAAuB,WACvB,2BAA4B,CAAC,mBAAoB,OAAO,EACxD,iBAAoB,WACpB,wBAAyB,CAAC,mBAAoB,OAAO,EACrD,iBAAoB,WACpB,wBAAyB,CAAC,mBAAoB,OAAO,EACrD,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,mBAAoB,UACpB,wBAAyB,UACzB,kBAAmB,QACnB,uBAAwB,QACxB,aAAc,UACd,kBAAmB,UACnB,eAAkB,GAClB,sBAAuB,CAAC,mBAAoB,OAAO,EACnD,IAAO,CAAC,kBAAmB,QAAQ,EACnC,WAAY,CAAC,mBAAoB,OAAO,EACxC,iBAAkB,oBAClB,sBAAuB,CAAC,mBAAoB,UAAW,OAAO,EAC9D,YAAa,CAAC,mBAAoB,OAAO,EACzC,OAAU,GACV,cAAe,CAAC,mBAAoB,OAAO,EAC3C,kBAAmB,QACnB,uBAAwB,QACxB,YAAe,aACf,mBAAoB,CAAC,mBAAoB,OAAO,EAChD,YAAe,oBACf,UAAa,YACb,iBAAkB,CAAC,mBAAoB,OAAO,EAC9C,IAAO,GACP,WAAY,CAAC,mBAAoB,OAAO,EACxC,aAAgB,QAChB,oBAAqB,CAAC,mBAAoB,OAAO,EACjD,IAAO,GACP,WAAY,CAAC,mBAAoB,OAAO,EACxC,IAAO,GACP,WAAY,CAAC,mBAAoB,OAAO,EACxC,KAAQ,GACR,YAAa,CAAC,mBAAoB,OAAO,EACzC,aAAc,UACd,kBAAmB,QACnB,qBAAsB,gBACtB,mBAAoB,CAAC,gBAAiB,gBAAgB,EACtD,qBAAsB,CAAC,gBAAiB,gBAAgB,EACxD,qBAAsB,CAAC,gBAAiB,gBAAgB,EACxD,qBAAsB,CAAC,gBAAiB,gBAAgB,EACxD,wBAAyB,CAAC,gBAAiB,gBAAgB,EAC3D,qBAAsB,CAAC,gBAAiB,gBAAgB,EACxD,GAAM,OACN,UAAW,CAAC,mBAAoB,OAAO,EACvC,GAAM,GACN,UAAW,CAAC,mBAAoB,OAAO,EACvC,KAAQ,CAAC,oBAAqB,mBAAoB,OAAO,EACzD,YAAa,CAAC,mBAAoB,OAAO,EACzC,eAAkB,UAClB,sBAAuB,CAAC,mBAAoB,OAAO,EACnD,KAAQ,SACR,YAAa,CAAC,mBAAoB,OAAO,CAC1C,IC7JA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAIC,GAAe,KACfC,GAAO,KAEPC,GAAO,CAAC,EACZ,IAASC,MAAOF,GACR,OAAO,UAAU,eAAe,KAAKA,GAAME,EAAG,IAC9CD,GAAKC,EAAG,EAAIH,GAAaG,EAAG,GAF3B,IAAAA,GAKTJ,GAAO,QAAUG,KCXjB,IAAAE,GAAAC,EAAA,CAAAC,GAAAC,KAAA,KAAIC,GAAe,KAEnBD,GAAO,QAAU,SAAgBE,EAAG,CAChC,OAAOD,GAAaC,CAAC,CACzB,ICJA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,KAAIC,GAAS,KACTC,GAAK,QAAQ,IAAI,EACjBC,EAAO,QAAQ,MAAM,EACrBC,GAAa,KACbC,GAAS,KACTC,GAAmB,KACnBC,GAAmB,KAEnBC,GAAa,QAAQ,WAAa,SAAWN,GAAG,cAAgB,OAAOA,GAAG,aAAa,QAAW,WAAaA,GAAG,aAAa,OAASA,GAAG,aAE3IO,GAAUL,GAAW,EACrBM,GAAe,UAAY,CAC3B,MAAO,CACHP,EAAK,KAAKM,GAAS,eAAe,EAClCN,EAAK,KAAKM,GAAS,iBAAiB,CACxC,CACJ,EAEIE,GAAgB,SAAgBC,EAAM,CACtC,GAAI,CACA,IAAIC,EAAOX,GAAG,SAASU,EAAM,CAAE,eAAgB,EAAM,CAAC,CAC1D,OAASE,EAAG,CACR,GAAIA,IAAMA,EAAE,OAAS,UAAYA,EAAE,OAAS,WAAY,MAAO,GAC/D,MAAMA,CACV,CACA,MAAO,CAAC,CAACD,IAASA,EAAK,OAAO,GAAKA,EAAK,OAAO,EACnD,EAEIE,GAAe,SAAqBC,EAAK,CACzC,GAAI,CACA,IAAIH,EAAOX,GAAG,SAASc,EAAK,CAAE,eAAgB,EAAM,CAAC,CACzD,OAASF,EAAG,CACR,GAAIA,IAAMA,EAAE,OAAS,UAAYA,EAAE,OAAS,WAAY,MAAO,GAC/D,MAAMA,CACV,CACA,MAAO,CAAC,CAACD,GAAQA,EAAK,YAAY,CACtC,EAEII,GAAsB,SAAsBC,EAAG,CAC/C,GAAI,CACA,OAAOV,GAAWU,CAAC,CACvB,OAASC,EAAa,CAClB,GAAIA,EAAY,OAAS,SACrB,MAAMA,CAEd,CACA,OAAOD,CACX,EAEIE,GAAoB,SAA2BC,EAAcH,EAAGI,EAAM,CACtE,OAAIA,GAAQA,EAAK,mBAAqB,GAC3BD,EAAaH,CAAC,EAElBA,CACX,EAEIK,GAAyB,SAAgCC,EAAcC,EAAS,CAChF,IAAIC,EAAOF,EAAaC,CAAO,EAC/B,GAAI,CACA,IAAIE,EAAM,KAAK,MAAMD,CAAI,EACzB,OAAOC,CACX,MAAkB,CAAC,CACvB,EAEIC,GAAuB,SAA8BV,EAAGW,EAAOP,EAAM,CAErE,QADIQ,EAAOxB,GAAiBuB,EAAOP,EAAMJ,CAAC,EACjCa,EAAI,EAAGA,EAAID,EAAK,OAAQC,IAC7BD,EAAKC,CAAC,EAAI5B,EAAK,KAAK2B,EAAKC,CAAC,EAAGb,CAAC,EAElC,OAAOY,CACX,EAEA9B,GAAO,QAAU,SAAqBkB,EAAGc,EAAS,CAC9C,GAAI,OAAOd,GAAM,SACb,MAAM,IAAI,UAAU,wBAAwB,EAEhD,IAAII,EAAOf,GAAiBW,EAAGc,CAAO,EAElCC,EAASX,EAAK,QAAUX,GACxBa,EAAeF,EAAK,cAAgBpB,GAAG,aACvCgC,EAAcZ,EAAK,aAAeP,GAClCM,EAAeC,EAAK,cAAgBL,GACpCkB,EAAkBb,EAAK,iBAAmBC,GAC9C,GAAID,EAAK,cAAgBA,EAAK,gBAC1B,MAAM,IAAI,UAAU,8DAA8D,EAEtF,IAAIc,EAAkBd,EAAK,gBAEvBe,EAAaf,EAAK,YAAc,CAAC,KAAK,EACtCgB,EAAqBhB,EAAK,qBAAuB,GACjDiB,EAAUjB,EAAK,SAAWnB,EAAK,QAAQE,GAAO,CAAC,EAC/CmC,EAASlB,EAAK,UAAYiB,EAE9BjB,EAAK,MAAQA,EAAK,OAASZ,GAAa,EAGxC,IAAI+B,EAAgBrB,GAAkBC,EAAclB,EAAK,QAAQoC,CAAO,EAAGjB,CAAI,EAE/E,GAAK,0CAA2C,KAAKJ,CAAC,EAAG,CACrD,IAAIwB,EAAMvC,EAAK,QAAQsC,EAAevB,CAAC,GACnCA,IAAM,KAAOA,IAAM,MAAQA,EAAE,MAAM,EAAE,IAAM,OAAKwB,GAAO,KAC3D,IAAIC,EAAIC,EAAeF,CAAG,GAAKG,GAAoBH,CAAG,EACtD,GAAIC,EAAG,OAAOvB,GAAkBC,EAAcsB,EAAGrB,CAAI,CACzD,KAAO,IAAIgB,GAAsBrC,GAAOiB,CAAC,EACrC,OAAOA,EAEP,IAAI4B,EAAIC,EAAoB7B,EAAGuB,CAAa,EAC5C,GAAIK,EAAG,OAAO1B,GAAkBC,EAAcyB,EAAGxB,CAAI,EAGzD,IAAI0B,GAAM,IAAI,MAAM,uBAAyB9B,EAAI,WAAasB,EAAS,GAAG,EAC1E,MAAAQ,GAAI,KAAO,mBACLA,GAEN,SAASJ,EAAe1B,EAAG,CACvB,IAAIS,EAAMsB,GAAQ9C,EAAK,QAAQe,CAAC,CAAC,EAEjC,GAAIS,GAAOA,EAAI,KAAOA,EAAI,KAAOL,EAAK,WAAY,CAC9C,IAAI4B,EAAQ/C,EAAK,SAASwB,EAAI,IAAKT,CAAC,EAChCiC,EAAI7B,EAAK,WAAWK,EAAI,IAAKT,EAAGgC,CAAK,EACrCC,IACAjC,EAAIf,EAAK,QAAQwB,EAAI,IAAKwB,CAAC,EAEnC,CAEA,GAAIlB,EAAOf,CAAC,EACR,OAAOA,EAGX,QAASa,EAAI,EAAGA,EAAIM,EAAW,OAAQN,IAAK,CACxC,IAAInB,EAAOM,EAAImB,EAAWN,CAAC,EAC3B,GAAIE,EAAOrB,CAAI,EACX,OAAOA,CAEf,CACJ,CAEA,SAASqC,GAAQjC,EAAK,CAClB,GAAI,EAAAA,IAAQ,IAAMA,IAAQ,MACtB,UAAQ,WAAa,SAAY,cAAe,KAAKA,CAAG,IAGvD,4BAA4B,KAAKA,CAAG,EAEzC,KAAIS,EAAUtB,EAAK,KAAKiB,GAAkBC,EAAcL,EAAKM,CAAI,EAAG,cAAc,EAElF,GAAI,CAACW,EAAOR,CAAO,EACf,OAAOwB,GAAQ9C,EAAK,QAAQa,CAAG,CAAC,EAGpC,IAAIW,EAAMQ,EAAgBX,EAAcC,CAAO,EAE/C,OAAIE,GAAOL,EAAK,gBAEZK,EAAML,EAAK,cAAcK,EAAkBX,CAAG,GAG3C,CAAE,IAAKW,EAAK,IAAKX,CAAI,EAChC,CAEA,SAAS6B,GAAoB3B,EAAG,CAC5B,IAAIO,EAAUtB,EAAK,KAAKiB,GAAkBC,EAAcH,EAAGI,CAAI,EAAG,eAAe,EACjF,GAAIW,EAAOR,CAAO,EAAG,CACjB,GAAI,CACA,IAAIE,EAAMQ,EAAgBX,EAAcC,CAAO,CACnD,MAAY,CAAC,CAOb,GALIE,GAAOL,EAAK,gBAEZK,EAAML,EAAK,cAAcK,EAAkBT,CAAC,GAG5CS,GAAOA,EAAI,KAAM,CACjB,GAAI,OAAOA,EAAI,MAAS,SAAU,CAC9B,IAAIyB,EAAY,IAAI,UAAU,iBAAczB,EAAI,KAAO,gCAA2B,EAClF,MAAAyB,EAAU,KAAO,uBACXA,CACV,EACIzB,EAAI,OAAS,KAAOA,EAAI,OAAS,QACjCA,EAAI,KAAO,SAEf,GAAI,CACA,IAAIgB,EAAIC,EAAezC,EAAK,QAAQe,EAAGS,EAAI,IAAI,CAAC,EAChD,GAAIgB,EAAG,OAAOA,EACd,IAAIG,EAAID,GAAoB1C,EAAK,QAAQe,EAAGS,EAAI,IAAI,CAAC,EACrD,GAAImB,EAAG,OAAOA,CAClB,MAAY,CAAC,CACjB,CACJ,CAEA,OAAOF,EAAezC,EAAK,KAAKe,EAAG,QAAQ,CAAC,CAChD,CAEA,SAAS6B,EAAoB7B,EAAGW,EAAO,CAInC,QAHIwB,EAAQ,UAAY,CAAE,OAAOzB,GAAqBV,EAAGW,EAAOP,CAAI,CAAG,EACnEQ,EAAOM,EAAkBA,EAAgBlB,EAAGW,EAAOwB,EAAO/B,CAAI,EAAI+B,EAAM,EAEnEtB,EAAI,EAAGA,EAAID,EAAK,OAAQC,IAAK,CAClC,IAAIf,EAAMc,EAAKC,CAAC,EAChB,GAAIG,EAAY/B,EAAK,QAAQa,CAAG,CAAC,EAAG,CAChC,IAAI2B,EAAIC,EAAe5B,CAAG,EAC1B,GAAI2B,EAAG,OAAOA,EACd,IAAIG,EAAID,GAAoB7B,CAAG,EAC/B,GAAI8B,EAAG,OAAOA,CAClB,CACJ,CACJ,CACJ,IC/MA,IAAAQ,GAAAC,EAAA,CAAAC,GAAAC,KAAA,KAAIC,GAAQ,KACZA,GAAM,KAAO,KACbA,GAAM,OAAS,KACfA,GAAM,KAAO,KAEbD,GAAO,QAAUC,KCLjB,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAO,QAAUC,GAGjB,SAASA,GAAoBC,EAAG,CAC9B,GAAKA,GACDA,IAAM,+BAGV,CAAAA,EAAIA,EAAE,KAAK,EAAE,MAAM;AAAA,CAAI,EACvB,QAASC,EAAI,EAAGD,EAAEC,CAAC,GAAKD,EAAEC,CAAC,EAAE,KAAK,EAAE,MAAM,QAAQ,EAAGA,IAAK,CAE1D,QADIC,EAAIF,EAAE,OACDG,EAAIF,EAAI,EAAGE,EAAID,GAAKF,EAAEG,CAAC,EAAE,KAAK,EAAGA,IAAK,CAC/C,OAAOH,EAAE,MAAMC,EAAGE,CAAC,EAAE,KAAK,GAAG,EAAE,KAAK,EACtC,ICbA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAA,SACE,SAAY,CACV,aAAgB,eAChB,YAAe,eACf,aAAgB,eAChB,gBAAmB,kBACnB,QAAW,eACX,mBAAoB,kBACpB,eAAkB,kBAClB,eAAkB,kBAClB,gBAAmB,kBACnB,YAAe,aACf,KAAQ,aACR,cAAiB,eACjB,QAAW,WACX,QAAW,WACX,OAAU,SACV,MAAS,SACT,aAAgB,eAChB,kBAAqB,gBACrB,OAAU,SACZ,EACA,KAAQ,CAAE,IAAO,MAAO,KAAQ,KAAM,EACtC,OAAU,CAAE,OAAU,QAAS,MAAS,MAAO,CACjD,ICxBA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,KAAIC,GAAS,KACTC,GAAkB,KAClBC,GAAgB,KAChBC,GAAkB,KAAmB,OACrCC,GAAW,CAAC,eAAe,kBAAkB,sBAAsB,EACnEC,GAAqB,KACrBC,GAAM,QAAQ,KAAK,EACnBC,GAAQ,KAERC,GAAQT,GAAO,QAAU,CAE3B,KAAM,UAAW,CAAC,EAElB,mBAAoB,SAASU,EAAM,CAKjC,GAJIA,EAAK,eACP,KAAK,KAAK,cAAc,EACxBA,EAAK,WAAaA,EAAK,aAAa,CAAC,GAEnC,CAACA,EAAK,WAAY,OAAO,KAAK,KAAK,mBAAmB,EACtD,OAAOA,EAAK,YAAe,WAC7BA,EAAK,WAAa,CAChB,KAAM,MACN,IAAKA,EAAK,UACZ,GAEF,IAAI,EAAIA,EAAK,WAAW,KAAO,GAC/B,GAAI,EAAG,CACL,IAAIC,EAASR,GAAc,QAAQ,CAAC,EAChCQ,IACF,EAAID,EAAK,WAAW,IAChBC,EAAO,yBAAyB,GAAK,WAAaA,EAAO,MAAM,EAAIA,EAAO,SAAS,EAE3F,CAEI,EAAE,MAAM,uCAAuC,GACjD,KAAK,KAAK,eAAgB,CAAC,CAE/B,EAEA,SAAU,SAASD,EAAM,CACvB,OAAO,KAAKF,GAAM,QAAQ,EAAE,QAAQ,SAAUI,EAAG,CAC3CF,EAAK,eAAeE,CAAC,GACvB,KAAK,KAAK,OAAQA,EAAGJ,GAAM,SAASI,CAAC,CAAC,CAE1C,EAAG,IAAI,CACT,EAEA,gBAAiB,SAASF,EAAM,CAC9B,GAAKA,EAAK,QACV,IAAI,OAAOA,EAAK,SAAY,SAAU,CACpC,KAAK,KAAK,kBAAkB,EAC5B,OAAOA,EAAK,QACZ,MACF,CACA,OAAO,KAAKA,EAAK,OAAO,EAAE,QAAQ,SAAUG,EAAG,CACzC,OAAOH,EAAK,QAAQG,CAAC,GAAM,UAC7B,KAAK,KAAK,iBAAiB,EAC3B,OAAOH,EAAK,QAAQG,CAAC,GACZL,GAAM,OAAOK,CAAC,GAAK,CAACH,EAAK,QAAQF,GAAM,OAAOK,CAAC,CAAC,GACzD,KAAK,KAAK,OAAQA,EAAGL,GAAM,OAAOK,CAAC,EAAG,SAAS,CAEnD,EAAG,IAAI,EACT,EAEA,cAAe,SAASH,EAAM,CAC5B,IAAII,EAAQJ,EAAK,MACbI,GAAS,CAAC,MAAM,QAAQA,CAAK,GAC/B,KAAK,KAAK,eAAe,EACzB,OAAOJ,EAAK,OACHA,EAAK,QACdA,EAAK,MAAQA,EAAK,MAAM,OAAO,SAASK,EAAM,CAC5C,MAAI,CAACA,GAAQ,OAAOA,GAAS,UAC3B,KAAK,KAAK,kBAAmBA,CAAI,EAC1B,IAEA,EAEX,EAAG,IAAI,EAEX,EAEA,YAAa,SAASL,EAAM,CAC1B,GAAKA,EAAK,KACN,OAAOA,EAAK,KAAQ,SAAU,CAChC,IAAIM,EAAI,CAAC,EACLC,GACAA,EAAQP,EAAK,KAAK,MAAM,iBAAiB,GAC3CM,EAAEC,EAAM,CAAC,CAAC,EAAIP,EAAK,IAEnBM,EAAEN,EAAK,IAAI,EAAIA,EAAK,IAEtBA,EAAK,IAAMM,CACb,CACF,EAEA,YAAa,SAASN,EAAM,CACrBA,EAAK,KACN,OAAOA,EAAK,KAAQ,WACtBA,EAAK,IAAM,CAAEA,EAAK,GAAI,EAE1B,EACA,2BAA4B,SAASA,EAAM,CACzC,IAAIQ,EAAM,sBACNC,EAAK,qBACLT,EAAKQ,CAAG,GAAK,CAACR,EAAKS,CAAE,IACvBT,EAAKS,CAAE,EAAIT,EAAKQ,CAAG,EACnB,OAAOR,EAAKQ,CAAG,GAEbR,EAAKS,CAAE,GAAK,CAAC,MAAM,QAAQT,EAAKS,CAAE,CAAC,GACrC,KAAK,KAAK,4BAA4B,EACtC,OAAOT,EAAKS,CAAE,GACLT,EAAKS,CAAE,IAChBT,EAAKS,CAAE,EAAIT,EAAKS,CAAE,EAAE,OAAO,SAASA,EAAI,CACtC,MAAI,CAACA,GAAM,OAAOA,GAAO,UACvB,KAAK,KAAK,4BAA6BA,CAAE,EAClC,KAEFT,EAAK,eACRA,EAAK,aAAe,CAAC,GAElBA,EAAK,aAAa,eAAeS,CAAE,IACtC,KAAK,KAAK,gCAAiCA,CAAE,EAC7CT,EAAK,aAAaS,CAAE,EAAI,KAEnB,GAEX,EAAG,IAAI,EAEX,EAEA,gBAAiB,SAAST,EAAMU,EAAQ,CACtC,IAAIC,EAAQ,CAACD,EACbE,GAAcZ,EAAM,KAAK,IAAI,EAC7Ba,GAAsBb,EAAM,KAAK,IAAI,EACrC,KAAK,2BAA2BA,CAAI,EAEnC,CAAC,eAAe,iBAAiB,EAAE,QAAQ,SAASc,EAAM,CACzD,GAAMA,KAAQd,EACd,IAAI,CAACA,EAAKc,CAAI,GAAK,OAAOd,EAAKc,CAAI,GAAM,SAAU,CACjD,KAAK,KAAK,wBAAyBA,CAAI,EACvC,OAAOd,EAAKc,CAAI,EAChB,MACF,CACA,OAAO,KAAKd,EAAKc,CAAI,CAAC,EAAE,QAAQ,SAAUZ,EAAG,CAC3C,IAAIa,EAAIf,EAAKc,CAAI,EAAEZ,CAAC,EAChB,OAAOa,GAAM,WACf,KAAK,KAAK,sBAAuBb,EAAG,KAAK,UAAUa,CAAC,CAAC,EACrD,OAAOf,EAAKc,CAAI,EAAEZ,CAAC,GAErB,IAAID,EAASR,GAAc,QAAQO,EAAKc,CAAI,EAAEZ,CAAC,CAAC,EAC5CD,IAAQD,EAAKc,CAAI,EAAEZ,CAAC,EAAID,EAAO,SAAS,EAC9C,EAAG,IAAI,EACT,EAAG,IAAI,CACT,EAEA,gBAAiB,SAAUD,EAAM,CAC3BA,EAAK,UACP,KAAK,KAAK,mBAAmB,EAC7B,OAAOA,EAAK,QAEhB,EAEA,iBAAkB,SAAUA,EAAM,CAC5B,OAAOA,EAAK,UAAa,WAC3BA,EAAK,SAAWA,EAAK,SAAS,MAAM,MAAM,GAExCA,EAAK,UAAY,CAAC,MAAM,QAAQA,EAAK,QAAQ,GAC/C,OAAOA,EAAK,SACZ,KAAK,KAAK,kBAAkB,GACnBA,EAAK,WACdA,EAAK,SAAWA,EAAK,SAAS,OAAO,SAASgB,EAAI,CAChD,OAAI,OAAOA,GAAO,UAAY,CAACA,GAC7B,KAAK,KAAK,kBAAkB,EACrB,IAEA,EAEX,EAAG,IAAI,EAEX,EAEA,gBAAiB,SAAShB,EAAMU,EAAQ,CAGtC,IAAIC,EAAQ,CAACD,EACb,GAAI,CAACV,EAAK,QACR,OAAAA,EAAK,QAAU,GACR,GAET,GAAI,CAACT,GAAO,MAAMS,EAAK,QAASW,CAAK,EACnC,MAAM,IAAI,MAAM,qBAAsBX,EAAK,QAAU,GAAG,EAE1D,OAAAA,EAAK,QAAUT,GAAO,MAAMS,EAAK,QAASW,CAAK,EACxC,EACT,EAEA,UAAW,SAASX,EAAM,CACxBiB,GAAajB,EAAMkB,EAAa,EAChCD,GAAajB,EAAMmB,EAAW,CAChC,EAEA,aAAc,SAASnB,EAAMoB,EAAS,CAChC,OAAOA,GAAY,UAAWA,EAAU,CAAC,OAAQA,CAAO,EACnD,OAAOA,EAAY,MAAaA,EAAU,CAAC,GACpD,IAAIV,EAASU,EAAQ,OACrB,GAAI,CAACpB,EAAK,MAAQ,CAACU,EAAQ,CACzBV,EAAK,KAAO,GACZ,MACF,CACA,GAAI,OAAOA,EAAK,MAAS,SACvB,MAAM,IAAI,MAAM,8BAA8B,EAE3CU,IACHV,EAAK,KAAOA,EAAK,KAAK,KAAK,GAC7BqB,GAAgBrB,EAAK,KAAMU,EAAQU,EAAQ,eAAe,EACtD1B,GAAgBM,EAAK,IAAI,GAC3B,KAAK,KAAK,kBAAmBA,EAAK,IAAI,CAC1C,EAGA,oBAAqB,SAAUA,EAAM,CAC/BA,EAAK,aAAe,OAAOA,EAAK,aAAgB,WAClD,KAAK,KAAK,sBAAsB,EAChC,OAAOA,EAAK,aAEVA,EAAK,QAAU,CAACA,EAAK,cACvBA,EAAK,YAAcJ,GAAmBI,EAAK,MAAM,GAC9CA,EAAK,cAAgB,QAAW,OAAOA,EAAK,YAC5CA,EAAK,aAAa,KAAK,KAAK,oBAAoB,CACvD,EAEA,eAAgB,SAAUA,EAAM,CACzBA,EAAK,SACR,KAAK,KAAK,eAAe,EACzBA,EAAK,OAAS,+BAElB,EAEA,aAAc,SAASA,EAAM,CAC3B,GAAI,CAACA,EAAK,MAAQA,EAAK,YAAcA,EAAK,WAAW,IAAK,CACxD,IAAIC,EAASR,GAAc,QAAQO,EAAK,WAAW,GAAG,EACnDC,GAAUA,EAAO,KAAK,IACvBD,EAAK,KAAO,CAAC,IAAKC,EAAO,KAAK,CAAC,EAEnC,SACQD,EAAK,KAAM,CACjB,IAAIsB,EAAU,cACd,GAAG,OAAOtB,EAAK,MAAQ,SAClBsB,EAAQ,KAAKtB,EAAK,IAAI,EACvBA,EAAK,KAAO,CAAC,MAAMA,EAAK,IAAI,EACtBH,GAAI,MAAMG,EAAK,IAAI,EAAE,SAC3BA,EAAK,KAAO,CAAC,IAAKA,EAAK,IAAI,EAE3B,KAAK,KAAK,uBAAuB,MAEhC,CACHuB,GAAUvB,EAAK,KAAM,KAAK,IAAI,EAC9B,IAAIwB,EAAUxB,EAAK,KACnBA,EAAK,KAAO,CAAC,EACVwB,EAAQ,MACN,OAAOA,EAAQ,KAAQ,UAAY3B,GAAI,MAAM2B,EAAQ,GAAG,EAAE,SAC3DxB,EAAK,KAAK,IAAMwB,EAAQ,IAExB,KAAK,KAAK,oBAAoB,GAE/BA,EAAQ,QACN,OAAOA,EAAQ,OAAU,UAAYF,EAAQ,KAAKE,EAAQ,KAAK,EAChExB,EAAK,KAAK,MAAQwB,EAAQ,MAE1B,KAAK,KAAK,wBAAwB,EAExC,CACG,CAACxB,EAAK,KAAK,OAAS,CAACA,EAAK,KAAK,MAChC,OAAOA,EAAK,KACZ,KAAK,KAAK,qBAAqB,EAEnC,CACF,EAEA,iBAAkB,SAASA,EAAM,CAC/B,GAAI,CAACA,EAAK,UAAYA,EAAK,YAAcA,EAAK,WAAW,IAAK,CAC5D,IAAIC,EAASR,GAAc,QAAQO,EAAK,WAAW,GAAG,EAClDC,GAAUA,EAAO,KAAK,IAAGD,EAAK,SAAWC,EAAO,KAAK,EAC3D,CACA,GAAKD,EAAK,SAEV,IAAG,OAAOA,EAAK,UAAa,SAC1B,YAAK,KAAK,gBAAgB,EACnB,OAAOA,EAAK,SAEjBH,GAAI,MAAMG,EAAK,QAAQ,EAAE,WAC3BA,EAAK,SAAW,UAAYA,EAAK,UAErC,EAEA,gBAAiB,SAASA,EAAM,CAC9B,GAAKA,EAAK,QAIN,OAAOA,EAAK,SAAa,UACzBA,EAAK,QAAQ,OAAS,GACtBA,EAAK,QAAQ,KAAK,IAAM,GAExB,KAAK,KAAK,gBAAgB,EAErBR,GAAgBQ,EAAK,OAAO,EAAE,qBACjC,KAAK,KAAK,gBAAgB,MAV9B,QAAO,KAAK,KAAK,gBAAgB,CAarC,CACF,EAEA,SAASyB,GAAyBC,EAAM,CACtC,GAAIA,EAAK,OAAO,CAAC,IAAM,IAAK,MAAO,GAEnC,IAAIC,EAAOD,EAAK,MAAM,CAAC,EAAE,MAAM,GAAG,EAClC,OAAIC,EAAK,SAAW,EAAU,GAEvBA,EAAK,CAAC,GAAKA,EAAK,CAAC,GACtBA,EAAK,CAAC,IAAM,mBAAmBA,EAAK,CAAC,CAAC,GACtCA,EAAK,CAAC,IAAM,mBAAmBA,EAAK,CAAC,CAAC,CAC1C,CAEA,SAASC,GAAuBF,EAAM,CACpC,MAAO,CAACA,EAAK,MAAM,aAAa,GAC9BA,IAAS,mBAAmBA,CAAI,CACpC,CAEA,SAASL,GAAiBQ,EAAMnB,EAAQoB,EAAiB,CACvD,GAAID,EAAK,OAAO,CAAC,IAAM,KACnB,EAAEJ,GAAyBI,CAAI,GAAKD,GAAuBC,CAAI,IAC9DnB,GAAW,CAACoB,GAAoBD,IAASA,EAAK,YAAY,GAC3DA,EAAK,YAAY,IAAM,gBACvBA,EAAK,YAAY,IAAM,cACrB,MAAM,IAAI,MAAM,iBAAmB,KAAK,UAAUA,CAAI,CAAC,CAE/D,CAEA,SAASZ,GAAcjB,EAAM+B,EAAI,CAC/B,OAAI/B,EAAK,SAAQA,EAAK,OAAS+B,EAAG/B,EAAK,MAAM,GAC5C,CAAC,cAAe,cAAc,EAAE,QAAQ,SAAUgC,EAAK,CACjD,MAAM,QAAQhC,EAAKgC,CAAG,CAAC,IAC5BhC,EAAKgC,CAAG,EAAIhC,EAAKgC,CAAG,EAAE,IAAID,CAAE,EAC9B,CAAC,EACM/B,CACT,CAEA,SAASkB,GAAee,EAAQ,CAC9B,GAAI,OAAOA,GAAW,SAAU,OAAOA,EACvC,IAAIJ,EAAOI,EAAO,MAAQ,GACtBC,EAAID,EAAO,KAAOA,EAAO,IACzBpC,EAAMqC,EAAK,KAAKA,EAAE,IAAO,GACzBC,EAAIF,EAAO,OAASA,EAAO,KAC3BG,EAAQD,EAAK,KAAKA,EAAE,IAAO,GAC/B,OAAON,EAAKO,EAAMvC,CACpB,CAEA,SAASsB,GAAac,EAAQ,CAC5B,GAAI,OAAOA,GAAW,SAAU,OAAOA,EACvC,IAAIJ,EAAOI,EAAO,MAAM,YAAY,EAChCpC,EAAMoC,EAAO,MAAM,cAAc,EACjCG,EAAQH,EAAO,MAAM,WAAW,EAChCI,EAAM,CAAC,EACX,OAAIR,GAAQA,EAAK,CAAC,EAAE,KAAK,IAAGQ,EAAI,KAAOR,EAAK,CAAC,EAAE,KAAK,GAChDO,IAAOC,EAAI,MAAQD,EAAM,CAAC,GAC1BvC,IAAKwC,EAAI,IAAMxC,EAAI,CAAC,GACjBwC,CACT,CAEA,SAASxB,GAAuBb,EAAMsC,EAAM,CAC1C,IAAIC,EAAIvC,EAAK,qBACb,GAAKuC,EACL,KAAIrC,EAAIF,EAAK,cAAgB,CAAC,EAC9B,OAAO,KAAKuC,CAAC,EAAE,QAAQ,SAAUpC,EAAG,CAClCD,EAAEC,CAAC,EAAIoC,EAAEpC,CAAC,CACZ,CAAC,EACDH,EAAK,aAAeE,EACtB,CAEA,SAASsC,GAAc1B,EAAM2B,EAAMH,EAAM,CACvC,GAAI,CAACxB,EAAM,MAAO,CAAC,EAInB,GAHI,OAAOA,GAAS,WAClBA,EAAOA,EAAK,KAAK,EAAE,MAAM,eAAe,GAEtC,CAAC,MAAM,QAAQA,CAAI,EAAG,OAAOA,EACjCwB,EAAK,8BAA+BG,CAAI,EACxC,IAAIF,EAAI,CAAC,EACT,OAAAzB,EAAK,OAAO,SAAUZ,EAAG,CACvB,OAAO,OAAOA,GAAM,QACtB,CAAC,EAAE,QAAQ,SAASA,EAAG,CACrBA,EAAIA,EAAE,KAAK,EAAE,MAAM,cAAc,EACjC,IAAIwC,EAAKxC,EAAE,MAAM,EACbyC,EAAKzC,EAAE,KAAK,EAAE,EAClByC,EAAKA,EAAG,KAAK,EACbA,EAAKA,EAAG,QAAQ,KAAM,EAAE,EACxBJ,EAAEG,CAAE,EAAIC,CACV,CAAC,EACMJ,CACT,CAEA,SAAS3B,GAAeZ,EAAMsC,EAAM,CAClC3C,GAAS,QAAQ,SAAU8C,EAAM,CAC1BzC,EAAKyC,CAAI,IACdzC,EAAKyC,CAAI,EAAID,GAAaxC,EAAKyC,CAAI,EAAGA,EAAMH,CAAI,EAClD,CAAC,CACH,CAEA,SAASf,GAAUqB,EAAMN,EAAM,CACxBM,GACL,OAAO,KAAKA,CAAI,EAAE,QAAQ,SAAUzC,EAAG,CACjCL,GAAM,KAAKK,CAAC,IACdmC,EAAK,OAAQnC,EAAGL,GAAM,KAAKK,CAAC,EAAG,MAAM,EACrCyC,EAAK9C,GAAM,KAAKK,CAAC,CAAC,EAAIyC,EAAKzC,CAAC,EAC5B,OAAOyC,EAAKzC,CAAC,EAEjB,CAAC,CACH,ICjaA,IAAA0C,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAA,SACE,aAAgB,mFACf,kBAAqB,uBACrB,aAAgB,8BAChB,iBAAoB,4BACpB,gBAAmB,wCACnB,cAAiB,yBACjB,gBAAmB,uCACnB,2BAA8B,oEAC9B,0BAA6B,wCAC7B,8BAAiC,2CACjC,sBAAyB,6BACzB,oBAAuB,4BACvB,4BAA+B,uCAC/B,kBAAqB,8BACrB,iBAAoB,yCACpB,iBAAoB,yCACpB,gBAAmB,6CACnB,qBAAwB,yCACxB,mBAAsB,iBACtB,cAAiB,iBACjB,eAAkB,oBAClB,sBAAyB,sDACzB,mBAAsB,gDACtB,uBAA0B,oDAC1B,oBAAuB,8DACvB,eAAkB,gDAClB,eAAkB,oDAClB,KAAQ,2BACX,IC7BA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,KAAIC,GAAO,QAAQ,MAAM,EACrBC,GAAW,KAEfF,GAAO,QAAU,UAAW,CAC1B,IAAIG,EAAO,MAAM,UAAU,MAAM,KAAK,UAAW,CAAC,EAC9CC,EAAcD,EAAK,MAAM,EAC7B,GAAIC,GAAe,OACjB,OAAOC,GAAgB,MAAM,KAAKF,CAAI,EAGtC,IAAIG,EAAcJ,GAASE,CAAW,EAAIF,GAASE,CAAW,EAAIA,EAAc,SAChF,OAAAD,EAAK,QAAQG,CAAW,EACjBL,GAAK,OAAO,MAAM,KAAME,CAAI,CAEvC,EAEA,SAASE,GAAiBE,EAAcC,EAAcC,EAAO,CAC3D,OAAIA,IACFF,EAAeE,EAAQ,KAAOF,EAAe,KAC7CC,EAAeC,EAAQ,KAAOD,EAAe,MAExCP,GAAK,OAAOC,GAAS,KAAMK,EAAcC,CAAY,CAC9D,ICtBA,IAAAE,GAAAC,EAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAO,QAAUC,GAEjB,IAAIC,GAAQ,KACZD,GAAU,MAAQC,GAElB,IAAIC,GAAc,KAEdC,GAAc,CAAC,OAAO,UAAU,cAAc,aAAa,UAAU,UACtD,QAAQ,MAAM,MAAM,OAAO,WAAW,SAAS,WAAW,SAAS,EAClFC,GAAmB,CAAC,eAAe,SAAU,OAAO,EAEpDC,GAAcF,GAAY,IAAI,SAASG,EAAW,CACpD,OAAOC,GAAQD,CAAS,EAAI,OAC9B,CAAC,EAIDD,GAAcA,GAAY,OAAOD,EAAgB,EAEjD,SAASJ,GAAWQ,EAAMC,EAAMC,EAAQ,CACnCD,IAAS,KAAMA,EAAO,KAAMC,EAAS,IACpCA,IAAQA,EAAS,KAClB,CAACD,GAAQD,EAAK,WAASC,EAAO,SAASE,EAAK,CAAa,GAExDH,EAAK,SACLA,EAAK,QAAQ,UAAY,oBACzB,CAACA,EAAK,QAAQ,aAChBA,EAAK,QAAU,IAEjBP,GAAM,KAAO,UAAW,CAAEQ,EAAKP,GAAY,MAAM,KAAM,SAAS,CAAC,CAAE,EACnEG,GAAY,QAAQ,SAASO,EAAW,CACtCX,GAAM,MAAQM,GAAQK,CAAS,CAAC,EAAEJ,EAAME,CAAM,CAChD,CAAC,EACDF,EAAK,IAAMA,EAAK,KAAO,IAAMA,EAAK,OACpC,CAEA,SAASD,GAASM,EAAQ,CACxB,OAAOA,EAAO,OAAO,CAAC,EAAE,YAAY,EAAIA,EAAO,MAAM,CAAC,CACxD,ICtCA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,GAAM,CAAC,UAAAC,EAAS,EAAI,QAAQ,MAAM,EAC5BC,GAAK,QAAQ,IAAI,EACjBC,GAAO,QAAQ,MAAM,EACrBC,GAAY,KAEZC,GAAgBJ,GAAUC,GAAG,QAAQ,EAE3CF,GAAO,QAAU,MAAMM,GAAW,CACjCA,EAAU,CACT,IAAK,QAAQ,IAAI,EACjB,UAAW,GACX,GAAGA,CACJ,EAEA,IAAMC,EAAWJ,GAAK,QAAQG,EAAQ,IAAK,cAAc,EACnDE,EAAOJ,GAAU,MAAMC,GAAcE,EAAU,MAAM,CAAC,EAE5D,OAAID,EAAQ,WACX,KAAkCE,CAAI,EAGhCA,CACR,EAEAR,GAAO,QAAQ,KAAOM,GAAW,CAChCA,EAAU,CACT,IAAK,QAAQ,IAAI,EACjB,UAAW,GACX,GAAGA,CACJ,EAEA,IAAMC,EAAWJ,GAAK,QAAQG,EAAQ,IAAK,cAAc,EACnDE,EAAOJ,GAAUF,GAAG,aAAaK,EAAU,MAAM,CAAC,EAExD,OAAID,EAAQ,WACX,KAAkCE,CAAI,EAGhCA,CACR,ICxCA,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cACA,IAAMC,GAAO,QAAQ,MAAM,EACrBC,GAAS,KACTC,GAAU,KAEhBH,GAAO,QAAU,MAAMI,GAAW,CACjC,IAAMC,EAAW,MAAMH,GAAO,eAAgBE,CAAO,EAErD,GAAKC,EAIL,MAAO,CACN,YAAa,MAAMF,GAAQ,CAAC,GAAGC,EAAS,IAAKH,GAAK,QAAQI,CAAQ,CAAC,CAAC,EACpE,KAAMA,CACP,CACD,EAEAL,GAAO,QAAQ,KAAOI,GAAW,CAChC,IAAMC,EAAWH,GAAO,KAAK,eAAgBE,CAAO,EAEpD,GAAKC,EAIL,MAAO,CACN,YAAaF,GAAQ,KAAK,CAAC,GAAGC,EAAS,IAAKH,GAAK,QAAQI,CAAQ,CAAC,CAAC,EACnE,KAAMA,CACP,CACD,IC7BA,IAAAC,GAAAC,EAAAC,IAAA,cACA,OAAO,eAAeA,GAAS,aAAc,CAAE,MAAO,EAAK,CAAC,EAC5DA,GAAQ,mBAAqBA,GAAQ,sBAAwB,OAC7D,IAAMC,GAAQ,QAAQ,cAAc,EAC9BC,GAAgB,KAChBC,GAAwB,MAAOC,EAASC,OAAeJ,GAAM,eAAe,CAC9E,GAAGG,EACH,QAAS,CACL,GAAGA,EAAQ,QACX,GAAGC,CACP,CACJ,CAAC,EACDL,GAAQ,sBAAwBG,GAChC,IAAMG,GAAqB,MAAOF,EAASG,IAA0B,CACjE,GAAIA,IAA0B,GAC1B,SAAWN,GAAM,eAAeG,CAAO,EAG3C,IAAMI,KAAWN,GAAc,MAAM,CAAE,IAAK,UAAW,UAAW,EAAM,CAAC,EACnEO,EAENR,GAAM,YAAY,mBACbO,GAAM,aAAa,OAASA,GAAM,aAAa,OACpD,GAAI,sBAAuBJ,EAAS,CAChC,IAAMM,EAAe,GAAGN,EAAQ,iBAAiB,IAAIA,EAAQ,aAAa,GAE1E,GAAI,CAACI,GAAM,aAAa,iBAAiB,SAASE,CAAY,EAAG,CAC7D,IAAMC,EAAU,qBAAqBD,CAAY,2DACjD,QAAQ,MAAMC,CAAO,EACrB,MACJ,CACJ,CACA,SAAWV,GAAM,eAAe,CAC5B,GAAGG,EACH,QAAS,CACL,GAAGA,EAAQ,QACX,sBAAuB,CACnB,KAAMH,GAAM,YAAY,YACxB,cAAeA,GAAM,YAAY,cACjC,kBAAAQ,EACA,KAAMR,GAAM,WAAW,cACvB,GAAGM,CACP,CACJ,CACJ,CAAC,CACL,EACAP,GAAQ,mBAAqBM,KC9C7B,IAAAM,GAAA,GAAAC,GAAAD,GAAA,aAAAE,KAAA,eAAAC,GAAAH,ICAA,IAAAI,GAAoF,wBCApF,IAAAC,GAAoE,wBACpEC,GAAkD,SAErC,CAAE,wBAAAC,EAAwB,KAAI,wBAA0C,EAExEC,GAAmB,CAC9B,KAAM,cAAW,WACjB,cAAe,iBACf,kBAAmB,UACrB,EAEA,eAAsBC,GAAOC,EAAkBC,EAA0C,CACvF,GAAKJ,GACL,SAAO,uBACL,CACE,GAAGC,GACH,KAAME,EAAU,KAAO,MACvB,QAAS,CAAE,WAAY,CAACA,CAAQ,CAClC,EACAC,GAAoC,EACtC,EAAE,MAAM,IAAM,CAEd,CAAC,CACH,CDlBA,IAAMC,GAAQ,IAAI,SAEZC,GAA6B,wBAInC,IAAMC,GAAmB,IAAM,KAAK,MAAM,IAAI,KAAK,EAAE,QAAQ,EAAI,GAAI,EAsC9D,SAASC,GAAS,CAAE,MAAAC,CAAM,EAAsB,CACrD,MAAO,CAAC,CAACA,EAAMA,EAAM,OAAS,CAAC,EAAE,QACnC,CA4CO,SAASC,IAAyC,CACvD,IAAIC,EAAWC,GAAmB,EAClC,GAAID,EAAU,CACZ,IAAME,EAAQ,CAAC,GAAGF,EAAS,MAAO,CAAE,UAAWG,GAAiB,CAAE,CAAC,EACnEH,EAAW,CACT,GAAGA,EACH,MAAAE,CACF,EACAE,GAAM,IAAIC,GAA4B,KAAK,UAAUL,CAAQ,CAAC,EAC1DA,EAAS,OAAS,SAASM,GAAO,EAAI,CAC5C,CACA,OAAON,CACT,CAeO,SAASO,IAA2C,CACzD,IAAMC,EAASC,GAAM,IAAIC,EAA0B,EACnD,GAAIF,EACF,OAAO,KAAK,MAAMA,CAAM,CAE5B,CAmEO,IAAMG,MAAc,wBAAiC,EAC/CC,GAAkD,CAC7D,MAAO,WAAWD,GAAY,qBAAqB,EAAI,GACvD,cAAe,WAAWA,GAAY,0BAA0B,EAAI,GACpE,aAAc,WAAWA,GAAY,yBAAyB,EAAI,EACpE,EEvMA,IAAAE,GAA0C,wBAkB1C,eAAsBC,IAAgB,CACpC,IAAMC,EAAWC,GAAiB,EAClC,aAAMC,GAAe,EACdF,CACT,CASA,eAAeG,IAAiB,CAC9B,GAAI,CACF,QAAM,kBAAc,CAClB,KAAM,oBACN,KAAM,cAAW,aACnB,CAAC,CACH,OAASC,EAAO,CACd,QAAQ,MAAMA,CAAK,CACrB,CACF,CHrCA,eAAOC,IAA0B,CAC/B,IAAMC,EAAkBC,GAAmB,EAC3C,OAAKD,EAGAE,GAASF,CAAe,GAG7B,MAAMG,GAAc,EACb,mBAHE,sBAHA,iBAOX", "names": ["require_p_try", "__commonJSMin", "exports", "module", "pTry", "fn", "arguments_", "resolve", "require_p_limit", "__commonJSMin", "exports", "module", "pTry", "pLimit", "concurrency", "queue", "activeCount", "next", "run", "fn", "resolve", "args", "result", "enqueue", "generator", "require_p_locate", "__commonJSMin", "exports", "module", "pLimit", "EndError", "value", "testElement", "element", "tester", "finder", "values", "pLocate", "iterable", "options", "limit", "items", "checkLimit", "error", "require_locate_path", "__commonJSMin", "exports", "module", "path", "fs", "promisify", "pLocate", "fsStat", "fsLStat", "typeMappings", "checkType", "type", "matchType", "stat", "paths", "options", "statFn", "path_", "require_path_exists", "__commonJSMin", "exports", "module", "fs", "promisify", "pAccess", "path", "require_find_up", "__commonJSMin", "exports", "module", "path", "locate<PERSON><PERSON>", "pathExists", "stop", "name", "options", "directory", "root", "paths", "run<PERSON><PERSON><PERSON>", "locateOptions", "<PERSON><PERSON><PERSON>", "require_is_arrayish", "__commonJSMin", "exports", "module", "obj", "require_error_ex", "__commonJSMin", "exports", "module", "util", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errorEx", "name", "properties", "errorExError", "ErrorEXError", "message", "newMessage", "key", "modifier", "v", "overwrittenStack", "stackDescriptor", "stackGetter", "stackValue", "newstack", "stack", "lineCount", "line", "str", "def", "require_json_parse_even_better_errors", "__commonJSMin", "exports", "module", "hexify", "char", "h", "parseError", "txt", "context", "badToken", "errIdx", "msg", "start", "end", "slice", "JSONParseError", "er", "caller", "metadata", "n", "kIndent", "kNewline", "formatRE", "emptyRE", "parseJson", "reviver", "parseText", "stripBOM", "newline", "indent", "result", "e", "isEmptyArray", "require_build", "__commonJSMin", "exports", "LF", "CR", "LinesAndColumns", "string", "offsets", "offset", "index", "line", "column", "location", "nextOffset", "require_js_tokens", "__commonJSMin", "exports", "match", "token", "nonASCIIidentifierStartChars", "nonASCIIidentifierChars", "nonASCIIidentifierStart", "RegExp", "nonASCIIidentifier", "astralIdentifierStartCodes", "astralIdentifierCodes", "isInAstralSet", "code", "set", "pos", "i", "length", "isIdentifierStart", "test", "String", "fromCharCode", "isIdentifierChar", "isIdentifierName", "name", "<PERSON><PERSON><PERSON><PERSON>", "cp", "charCodeAt", "trail", "reservedWords", "keyword", "strict", "strictBind", "keywords", "Set", "reservedWordsStrictSet", "reservedWordsStrictBindSet", "isReservedWord", "word", "inModule", "isStrictReservedWord", "has", "isStrictBindOnlyReservedWord", "isStrictBindReservedWord", "isKeyword", "_identifier", "require", "_keyword", "require_picocolors", "__commonJSMin", "exports", "module", "p", "argv", "env", "isColorSupported", "formatter", "open", "close", "replace", "input", "string", "index", "replaceClose", "result", "cursor", "createColors", "enabled", "f", "require_escape_string_regexp", "__commonJSMin", "exports", "module", "matchOperatorsRe", "str", "require_color_name", "__commonJSMin", "exports", "module", "require_conversions", "__commonJSMin", "exports", "module", "cssKeywords", "reverseKeywords", "key", "convert", "model", "channels", "labels", "rgb", "g", "b", "min", "max", "delta", "h", "s", "l", "rdif", "gdif", "bdif", "r", "v", "diff", "diffc", "c", "w", "m", "y", "k", "comparativeDistance", "x", "reversed", "currentClosestDistance", "currentClosestKeyword", "keyword", "value", "distance", "z", "xyz", "a", "hsl", "t1", "t2", "t3", "val", "i", "smin", "lmin", "sv", "hsv", "hi", "f", "p", "q", "t", "vmin", "sl", "hwb", "wh", "bl", "ratio", "n", "cmyk", "lab", "y2", "x2", "z2", "hr", "lch", "args", "ansi", "color", "mult", "rem", "integer", "string", "match", "colorString", "char", "chroma", "grayscale", "hue", "hcg", "pure", "mg", "apple", "gray", "require_route", "__commonJSMin", "exports", "module", "conversions", "buildGraph", "graph", "models", "len", "i", "deriveBFS", "fromModel", "queue", "current", "adjacents", "adjacent", "node", "link", "from", "to", "args", "wrapConversion", "toModel", "path", "fn", "cur", "conversion", "require_color_convert", "__commonJSMin", "exports", "module", "conversions", "route", "convert", "models", "wrapRaw", "fn", "wrappedFn", "args", "wrapRounded", "result", "len", "i", "fromModel", "routes", "routeModels", "toModel", "require_ansi_styles", "__commonJSMin", "exports", "module", "colorConvert", "wrapAnsi16", "fn", "offset", "wrapAnsi256", "code", "wrapAnsi16m", "rgb", "assembleStyles", "codes", "styles", "groupName", "group", "styleName", "style", "ansi<PERSON><PERSON>i", "n", "rgb2rgb", "r", "g", "b", "key", "suite", "require_has_flag", "__commonJSMin", "exports", "module", "flag", "argv", "prefix", "pos", "terminatorPos", "require_supports_color", "__commonJSMin", "exports", "module", "os", "hasFlag", "env", "forceColor", "translateLevel", "level", "supportsColor", "stream", "min", "osRelease", "sign", "version", "getSupportLevel", "require_templates", "__commonJSMin", "exports", "module", "TEMPLATE_REGEX", "STYLE_REGEX", "STRING_REGEX", "ESCAPE_REGEX", "ESCAPES", "unescape", "c", "parseArguments", "name", "args", "results", "chunks", "matches", "chunk", "m", "escape", "chr", "parseStyle", "style", "buildStyle", "chalk", "styles", "enabled", "layer", "current", "styleName", "tmp", "escapeChar", "inverse", "close", "str", "errMsg", "require_chalk", "__commonJSMin", "exports", "module", "escapeStringRegexp", "ansiStyles", "stdoutColor", "template", "isSimpleWindowsTerm", "levelMapping", "skip<PERSON><PERSON><PERSON>", "styles", "applyOptions", "obj", "options", "scLevel", "Chalk", "chalk", "args", "chalkTag", "key", "codes", "build", "model", "level", "bgModel", "proto", "_styles", "_empty", "builder", "applyStyle", "self", "enabled", "argsLen", "str", "a", "originalDim", "code", "strings", "parts", "_jsTokens", "require", "_helperValidatorIdentifier", "_picocolors", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "colors", "process", "env", "FORCE_COLOR", "createColors", "_colors", "compose", "f", "g", "v", "sometimesKeywords", "Set", "getDefs", "keyword", "cyan", "capitalized", "yellow", "jsxIdentifier", "punctuator", "number", "magenta", "string", "green", "regex", "comment", "gray", "invalid", "white", "bgRed", "bold", "NEWLINE", "BRACKET", "tokenize", "JSX_TAG", "getTokenType", "token", "offset", "text", "type", "isKeyword", "value", "isStrictReservedWord", "test", "slice", "toLowerCase", "match", "jsTokens", "exec", "matchToToken", "index", "highlightTokens", "defs", "highlighted", "colorize", "split", "map", "str", "join", "should<PERSON><PERSON><PERSON>", "options", "isColorSupported", "forceColor", "pcWithForcedColor", "getColors", "_pcWithForcedColor", "highlight", "code", "chalk", "chalkWithForcedColor", "exports", "getChalk", "_chalk", "_chalkWithForcedColor", "constructor", "enabled", "level", "require_lib", "__commonJSMin", "exports", "codeFrameColumns", "_default", "_highlight", "_interopRequireWildcard", "_getRequireWildcardCache", "cache", "obj", "newObj", "hasPropertyDescriptor", "key", "desc", "deprecationWarningShown", "getDefs", "chalk", "NEWLINE", "getMarkerLines", "loc", "source", "opts", "startLoc", "endLoc", "linesAbove", "linesBelow", "startLine", "startColumn", "endLine", "endColumn", "start", "end", "lineDiff", "markerLines", "i", "lineNumber", "sourceLength", "rawLines", "highlighted", "defs", "maybe<PERSON><PERSON><PERSON>", "chalkFn", "string", "lines", "hasColumns", "numberMaxWidth", "frame", "line", "index", "number", "gutter", "<PERSON><PERSON><PERSON><PERSON>", "lastMarkerLine", "markerLine", "markerSpacing", "numberOfMarkers", "colNumber", "message", "deprecationError", "require_parse_json", "__commonJSMin", "exports", "module", "errorEx", "fallback", "LinesAndColumns", "codeFrameColumns", "JSONError", "parseJson", "string", "reviver", "filename", "error", "indexMatch", "jsonError", "lines", "index", "location", "codeFrame", "require_semver", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "debug", "args", "MAX_LENGTH", "MAX_SAFE_INTEGER", "MAX_SAFE_COMPONENT_LENGTH", "MAX_SAFE_BUILD_LENGTH", "re", "safeRe", "src", "R", "LETTERDASHNUMBER", "safeRegexReplacements", "makeSafeRe", "value", "i", "token", "max", "NUMERICIDENTIFIER", "NUMERICIDENTIFIERLOOSE", "NONNUMERICIDENTIFIER", "MAINVERSION", "MAINVERSIONLOOSE", "PRERELEASEIDENTIFIER", "PRERELEASEIDENTIFIERLOOSE", "PRERELEASE", "PRERELEASELOOSE", "BUILDIDENTIFIER", "BUILD", "FULL", "FULLPLAIN", "LOOSEPLAIN", "LOOSE", "GTLT", "XRANGEIDENTIFIERLOOSE", "XRANGEIDENTIFIER", "XRANGEPLAIN", "XRANGEPLAINLOOSE", "XRANGE", "XRANGELOOSE", "COERCE", "LONETILDE", "TILDETRIM", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TILDE", "TILDELOOSE", "LONECARET", "CARETTRIM", "caretTrimReplace", "CARET", "CARETLOOSE", "COMPARATORLOOSE", "COMPARATOR", "COMPARATORTRIM", "comparator<PERSON><PERSON>Replace", "HYPHENRANGE", "HYPHENRANGELOOSE", "STAR", "parse", "version", "options", "r", "valid", "v", "clean", "s", "m", "id", "num", "other", "compareIdentifiers", "a", "b", "release", "identifier", "inc", "loose", "diff", "version1", "version2", "eq", "v1", "v2", "prefix", "defaultResult", "key", "numeric", "anum", "bnum", "rcompareIdentifiers", "major", "minor", "patch", "compare", "compareLoose", "rcompare", "sort", "list", "rsort", "gt", "lt", "neq", "gte", "lte", "cmp", "op", "Comparator", "comp", "ANY", "rangeTmp", "Range", "satisfies", "sameDirectionIncreasing", "sameDirectionDecreasing", "sameSemVer", "differentDirectionsInclusive", "oppositeDirectionsLessThan", "oppositeDirectionsGreaterThan", "range", "c", "comps", "hr", "hyphen<PERSON>eplace", "compRe", "set", "parseComparator", "thisComparators", "thisComparator", "rangeComparators", "rangeComparator", "toComparators", "replaceCarets", "replaceTildes", "replaceXRanges", "replaceStars", "isX", "replaceTilde", "_", "M", "p", "pr", "ret", "replaceCaret", "replaceXRange", "gtlt", "xM", "xm", "xp", "anyX", "$0", "from", "fM", "fm", "fp", "fpr", "fb", "to", "tM", "tm", "tp", "tpr", "tb", "testSet", "allowed", "maxSatisfying", "versions", "maxSV", "rangeObj", "minSatisfying", "min", "minSV", "minVersion", "minver", "comparators", "comparator", "compver", "validRange", "ltr", "outside", "gtr", "hilo", "gtfn", "ltefn", "ltfn", "ecomp", "high", "low", "prerelease", "parsed", "intersects", "r1", "r2", "coerce", "match", "require_spdx_license_ids", "__commonJSMin", "exports", "module", "require_deprecated", "__commonJSMin", "exports", "module", "require_spdx_exceptions", "__commonJSMin", "exports", "module", "require_scan", "__commonJSMin", "exports", "module", "licenses", "exceptions", "source", "index", "hasMore", "read", "value", "chars", "match", "skipWhitespace", "operator", "string", "possibilities", "i", "idstring", "expectIdstring", "documentRef", "licenseRef", "identifier", "begin", "parseToken", "tokens", "token", "require_parse", "__commonJSMin", "exports", "module", "tokens", "index", "hasMore", "token", "next", "parseOperator", "operator", "t", "parseWith", "parseLicenseRef", "begin", "string", "parseLicense", "node", "exception", "parseParenthesizedExpression", "left", "expr", "parseExpression", "parseAtom", "makeBinaryOpParser", "<PERSON><PERSON><PERSON><PERSON>", "parseBinaryOp", "right", "parseAnd", "require_spdx_expression_parse", "__commonJSMin", "exports", "module", "scan", "parse", "source", "require_spdx_correct", "__commonJSMin", "exports", "module", "parse", "spdxLicenseIds", "valid", "string", "sortTranspositions", "a", "b", "length", "transpositions", "TRANSPOSED", "CORRECT", "transforms", "argument", "licensesWithVersions", "id", "match", "objectMap", "item", "key", "licensesWithOneVersion", "lastResorts", "SUBSTRING", "IDENTIFIER", "validTransformation", "identifier", "i", "transformed", "validLastResort", "upperCased", "lastResort", "anyCorrection", "check", "transposition", "transposed", "corrected", "checked", "options", "upgrade", "postprocess", "value", "upgradeGPLs", "validArugment", "noPlus", "require_validate_npm_package_license", "__commonJSMin", "exports", "module", "parse", "correct", "genericWarning", "fileReferenceRE", "startsWith", "prefix", "string", "usesLicenseRef", "ast", "license", "argument", "match", "result", "corrected", "require_git_host_info", "__commonJSMin", "exports", "module", "gitHosts", "fragment", "formatHashFragment", "gitHostDefaults", "name", "key", "protocol", "require_git_host", "__commonJSMin", "exports", "module", "gitHosts", "extend", "target", "source", "keys", "GitHost", "type", "user", "auth", "project", "committish", "defaultRepresentation", "opts", "gitHostInfo", "key", "template", "vars", "self", "rawAuth", "rawcommittish", "rawFragment", "rawPath", "rawProject", "value", "pathComponent", "res", "P", "F", "opts_", "require_hosted_git_info", "__commonJSMin", "exports", "module", "url", "gitHosts", "GitHost", "protocolToRepresentationMap", "protocolToRepresentation", "protocol", "authProtocols", "cache", "giturl", "opts", "key", "fromUrl", "fixupUnqualifiedGist", "isGitHubShorthand", "parsed", "parseGitUrl", "shortcutMatch", "matches", "gitHostName", "gitHostInfo", "auth", "committish", "user", "project", "defaultRepresentation", "pathmatch", "matched", "ex", "arg", "legacy", "authmatch", "whatwg", "require_homedir", "__commonJSMin", "exports", "module", "os", "home", "user", "require_caller", "__commonJSMin", "exports", "module", "origPrepareStackTrace", "_", "stack", "require_path_parse", "__commonJSMin", "exports", "module", "isWindows", "splitWindowsRe", "win32", "win32SplitPath", "filename", "pathString", "allParts", "splitPathRe", "posix", "posixSplitPath", "require_node_modules_paths", "__commonJSMin", "exports", "module", "path", "parse", "getNodeModulesDirs", "absoluteStart", "modules", "prefix", "paths", "parsed", "dirs", "a<PERSON><PERSON>", "moduleDir", "start", "opts", "request", "require_normalize_options", "__commonJSMin", "exports", "module", "x", "opts", "require_implementation", "__commonJSMin", "exports", "module", "ERROR_MESSAGE", "toStr", "max", "funcType", "concatty", "a", "b", "arr", "j", "slicy", "arrLike", "offset", "joiny", "joiner", "str", "i", "that", "target", "args", "bound", "binder", "result", "<PERSON><PERSON><PERSON><PERSON>", "boundArgs", "Empty", "require_function_bind", "__commonJSMin", "exports", "module", "implementation", "require_hasown", "__commonJSMin", "exports", "module", "call", "$hasOwn", "bind", "require_core", "__commonJSMin", "exports", "module", "require_is_core_module", "__commonJSMin", "exports", "module", "hasOwn", "specifierIncluded", "current", "specifier", "nodeParts", "parts", "op", "versionParts", "i", "cur", "ver", "matchesRange", "range", "specifiers", "versionIncluded", "nodeVersion", "specifierValue", "data", "x", "require_async", "__commonJSMin", "exports", "module", "fs", "getHomedir", "path", "caller", "nodeModulesPaths", "normalizeOptions", "isCore", "realpathFS", "homedir", "defaultPaths", "defaultIsFile", "file", "cb", "err", "stat", "defaultIsDir", "dir", "defaultRealpath", "x", "realpathErr", "realPath", "maybeR<PERSON>path", "realpath", "opts", "defaultReadPackage", "readFile", "pkgfile", "readFileErr", "body", "pkg", "getPackageCandidates", "start", "dirs", "i", "options", "callback", "isFile", "isDirectory", "readPackage", "conflictErr", "packageIterator", "extensions", "includeCoreModules", "basedir", "parent", "absoluteStart", "realStart", "init", "res", "loadAsDirectory", "onfile", "loadAsFile", "loadNodeModules", "n", "realN", "moduleError", "m", "d", "realD", "thePackage", "loadAsFilePackage", "exts", "load", "loadPackage", "onpkg", "loadpkg", "pkg_", "rfile", "rel", "r", "onex", "ex", "unwrapErr", "pkgdir", "pkgParam", "loadAsDirectoryPackage", "fpkg", "mainError", "processDirs", "isdir", "ondir", "thunk", "require_core", "__commonJSMin", "exports", "module", "require_core", "__commonJSMin", "exports", "module", "isCoreModule", "data", "core", "mod", "require_is_core", "__commonJSMin", "exports", "module", "isCoreModule", "x", "require_sync", "__commonJSMin", "exports", "module", "isCore", "fs", "path", "getHomedir", "caller", "nodeModulesPaths", "normalizeOptions", "realpathFS", "homedir", "defaultPaths", "defaultIsFile", "file", "stat", "e", "defaultIsDir", "dir", "defaultRealpathSync", "x", "realpathErr", "maybeRealpathSync", "realpathSync", "opts", "defaultReadPackageSync", "readFileSync", "pkgfile", "body", "pkg", "getPackageCandidates", "start", "dirs", "i", "options", "isFile", "isDirectory", "readPackageSync", "packageIterator", "extensions", "includeCoreModules", "basedir", "parent", "absoluteStart", "res", "m", "loadAsFileSync", "loadAsDirectorySync", "n", "loadNodeModulesSync", "err", "loadpkg", "rfile", "r", "mainError", "thunk", "require_resolve", "__commonJSMin", "exports", "module", "async", "require_extract_description", "__commonJSMin", "exports", "module", "extractDescription", "d", "s", "l", "e", "require_typos", "__commonJSMin", "exports", "module", "require_fixer", "__commonJSMin", "exports", "module", "semver", "validateLicense", "hostedGitInfo", "isBuiltinModule", "depTypes", "extractDescription", "url", "typos", "fixer", "data", "hosted", "d", "k", "files", "file", "b", "match", "bdd", "bd", "strict", "loose", "objectifyDeps", "addOptionalDepsToDeps", "deps", "r", "kw", "modifyPeople", "unParse<PERSON>erson", "parse<PERSON><PERSON>", "options", "ensureValidName", "emailRe", "bugsTypos", "oldBugs", "isValidScopedPackageName", "spec", "rest", "isCorrectlyEncodedName", "name", "allowLegacyCase", "fn", "set", "person", "u", "e", "email", "obj", "warn", "o", "depObjectify", "type", "dn", "dv", "bugs", "require_warning_messages", "__commonJSMin", "exports", "module", "require_make_warning", "__commonJSMin", "exports", "module", "util", "messages", "args", "warningName", "makeTypoWarning", "msgTemplate", "providedName", "<PERSON><PERSON><PERSON>", "field", "require_normalize", "__commonJSMin", "exports", "module", "normalize", "fixer", "makeWarning", "fieldsToFix", "otherThingsToFix", "thingsToFix", "fieldName", "ucFirst", "data", "warn", "strict", "msg", "thingName", "string", "require_read_pkg", "__commonJSMin", "exports", "module", "promisify", "fs", "path", "parseJson", "readFileAsync", "options", "filePath", "json", "require_read_pkg_up", "__commonJSMin", "exports", "module", "path", "findUp", "readPkg", "options", "filePath", "require_distribution", "__commonJSMin", "exports", "api_1", "read_pkg_up_1", "callbackLaunchCommand", "options", "result", "crossLaunchCommand", "callbackLaunchOptions", "pack", "ownerOrAuthorName", "targetHandle", "message", "continue_timer_exports", "__export", "continue_timer_default", "__toCommonJS", "import_api", "import_api", "import_raycast_cross_extension", "enableFocusWhileFocused", "dndLaunchOptions", "setDND", "enabled", "callbackOptions", "cache", "CURRENT_INTERVAL_CACHE_KEY", "currentTimestamp", "isPaused", "parts", "continueInterval", "interval", "getCurrentInterval", "parts", "currentTimestamp", "cache", "CURRENT_INTERVAL_CACHE_KEY", "setDND", "getCurrentInterval", "result", "cache", "CURRENT_INTERVAL_CACHE_KEY", "preferences", "intervalDurations", "import_api", "continueTimer", "interval", "continueInterval", "refreshMenuBar", "refreshMenuBar", "error", "continue_timer_default", "currentInterval", "getCurrentInterval", "isPaused", "continueTimer"]}