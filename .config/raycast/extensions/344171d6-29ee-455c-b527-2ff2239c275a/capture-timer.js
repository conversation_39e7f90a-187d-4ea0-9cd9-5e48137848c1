"use strict";var t=Object.defineProperty;var o=Object.getOwnPropertyDescriptor;var c=Object.getOwnPropertyNames;var u=Object.prototype.hasOwnProperty;var p=(r,e)=>{for(var s in e)t(r,s,{get:e[s],enumerable:!0})},f=(r,e,s,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of c(e))!u.call(r,i)&&i!==s&&t(r,i,{get:()=>e[i],enumerable:!(a=o(e,i))||a.enumerable});return r};var g=r=>f(t({},"__esModule",{value:!0}),r);var b={};p(b,{default:()=>l});module.exports=g(b);var m=require("@raycast/api"),n=require("child_process"),l=async r=>{r.arguments.time?(0,n.exec)(`/usr/sbin/screencapture -i -p -T ${r.arguments.time}`):(0,n.exec)("/usr/sbin/screencapture -i -p -T 5"),await(0,m.closeMainWindow)()};
