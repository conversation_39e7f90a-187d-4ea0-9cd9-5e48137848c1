"use strict";var a=Object.defineProperty;var p=Object.getOwnPropertyDescriptor;var r=Object.getOwnPropertyNames;var m=Object.prototype.hasOwnProperty;var l=(e,t)=>{for(var i in t)a(e,i,{get:t[i],enumerable:!0})},S=(e,t,i,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of r(t))!m.call(e,o)&&o!==i&&a(e,o,{get:()=>t[o],enumerable:!(n=p(t,o))||n.enumerable});return e};var f=e=>S(a({},"__esModule",{value:!0}),e);var h={};l(h,{default:()=>d});module.exports=f(h);var s=require("@raycast/api"),c=require("child_process"),d=async()=>{(0,c.exec)("/System/Applications/Utilities/Screenshot.app/Contents/MacOS/Screenshot"),await(0,s.closeMainWindow)()};
