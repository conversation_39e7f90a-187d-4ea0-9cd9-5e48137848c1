{"version": 3, "sources": ["../src/capture-window-to-clipboard.ts"], "sourcesContent": ["import { closeMainWindow } from \"@raycast/api\";\nimport { exec } from \"child_process\";\n\nexport default async () => {\n  exec(\"/usr/sbin/screencapture -w -p -c\");\n  await closeMainWindow();\n};\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAgC,wBAChCC,EAAqB,yBAEdH,EAAQ,SAAY,IACzB,QAAK,kCAAkC,EACvC,QAAM,mBAAgB,CACxB", "names": ["capture_window_to_clipboard_exports", "__export", "capture_window_to_clipboard_default", "__toCommonJS", "import_api", "import_child_process"]}