"use strict";var a=Object.defineProperty;var s=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var p=Object.prototype.hasOwnProperty;var f=(r,e)=>{for(var o in e)a(r,o,{get:e[o],enumerable:!0})},u=(r,e,o,c)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of m(e))!p.call(r,i)&&i!==o&&a(r,i,{get:()=>e[i],enumerable:!(c=s(e,i))||c.enumerable});return r};var d=r=>u(a({},"__esModule",{value:!0}),r);var w={};f(w,{default:()=>l});module.exports=d(w);var t=require("@raycast/api"),n=require("child_process"),l=async()=>{(0,n.exec)("/usr/sbin/screencapture -i -c"),await(0,t.closeMainWindow)()};
