"use strict";var a=Object.defineProperty;var p=Object.getOwnPropertyDescriptor;var s=Object.getOwnPropertyNames;var m=Object.prototype.hasOwnProperty;var f=(r,e)=>{for(var o in e)a(r,o,{get:e[o],enumerable:!0})},u=(r,e,o,t)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of s(e))!m.call(r,i)&&i!==o&&a(r,i,{get:()=>e[i],enumerable:!(t=p(e,i))||t.enumerable});return r};var d=r=>u(a({},"__esModule",{value:!0}),r);var w={};f(w,{default:()=>l});module.exports=d(w);var c=require("@raycast/api"),n=require("child_process"),l=async()=>{(0,n.exec)("/usr/sbin/screencapture -i -p"),await(0,c.closeMainWindow)()};
