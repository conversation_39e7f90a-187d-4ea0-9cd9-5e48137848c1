"use strict";var i=Object.defineProperty;var p=Object.getOwnPropertyDescriptor;var s=Object.getOwnPropertyNames;var m=Object.prototype.hasOwnProperty;var f=(r,e)=>{for(var a in e)i(r,a,{get:e[a],enumerable:!0})},u=(r,e,a,t)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of s(e))!m.call(r,o)&&o!==a&&i(r,o,{get:()=>e[o],enumerable:!(t=p(e,o))||t.enumerable});return r};var d=r=>u(i({},"__esModule",{value:!0}),r);var w={};f(w,{default:()=>l});module.exports=d(w);var c=require("@raycast/api"),n=require("child_process"),l=async()=>{(0,n.exec)("/usr/sbin/screencapture -p"),await(0,c.closeMainWindow)()};
