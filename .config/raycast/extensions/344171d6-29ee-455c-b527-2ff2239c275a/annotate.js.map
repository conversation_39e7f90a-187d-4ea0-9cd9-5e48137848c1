{"version": 3, "sources": ["../src/annotate.ts"], "sourcesContent": ["import { closeMainWindow } from \"@raycast/api\";\nimport { exec } from \"child_process\";\n\nexport default async () => {\n  exec(\"/usr/sbin/screencapture -i -P\");\n  await closeMainWindow();\n};\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAgC,wBAChCC,EAAqB,yBAEdH,EAAQ,SAAY,IACzB,QAAK,+BAA+B,EACpC,QAAM,mBAAgB,CACxB", "names": ["annotate_exports", "__export", "annotate_default", "__toCommonJS", "import_api", "import_child_process"]}