<LocationServiceConfiguration>
  <LastChangeId>3108867</LastChangeId>
  <CacheExpirationDate>2024-11-17T18:11:37.052566Z</CacheExpirationDate>
  <DefaultAccessMappingMoniker>ScaleUnitMapping</DefaultAccessMappingMoniker>
  <VirtualDirectory>
  </VirtualDirectory>
  <AccessMappings>
    <AccessMapping>
      <Moniker>HostGuidAccessMapping</Moniker>
      <AccessPoint>https://pipelines.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</AccessPoint>
      <DisplayName>Host Guid Access Mapping</DisplayName>
    </AccessMapping>
    <AccessMapping>
      <Moniker>PublicAccessMapping</Moniker>
      <AccessPoint>https://pipelines.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</AccessPoint>
      <DisplayName>Public Access Mapping</DisplayName>
      <VirtualDirectory>
      </VirtualDirectory>
    </AccessMapping>
    <AccessMapping>
      <Moniker>AzureInstanceMapping</Moniker>
      <AccessPoint>https://pipelinesghubeus8aks.eastus.cloudapp.azure.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</AccessPoint>
      <DisplayName>Azure Instance Mapping</DisplayName>
    </AccessMapping>
    <AccessMapping>
      <Moniker>CodexAccessMapping</Moniker>
      <AccessPoint>https://pipelines.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</AccessPoint>
      <DisplayName>Codex Access Mapping</DisplayName>
      <VirtualDirectory>
      </VirtualDirectory>
    </AccessMapping>
    <AccessMapping>
      <Moniker>ScaleUnitMapping</Moniker>
      <AccessPoint>https://pipelinesghubeus8.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</AccessPoint>
      <DisplayName>Scale Unit Access Mapping</DisplayName>
      <VirtualDirectory>
      </VirtualDirectory>
    </AccessMapping>
  </AccessMappings>
  <Services>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>5f814983-29e7-4f0f-d4b8-8016a66b281b</Identifier>
      <DisplayName>acghubeus2</DisplayName>
      <Description>
      </Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>VsService</ParentServiceType>
      <ParentIdentifier>0000006d-0000-8888-8000-000000000000</ParentIdentifier>
      <LocationMappings />
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>0000006d-0000-8888-8000-000000000000</Identifier>
      <DisplayName>acghubeus2</DisplayName>
      <Description>
      </Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>LocationService2</ParentServiceType>
      <ParentIdentifier>5f814983-29e7-4f0f-d4b8-8016a66b281b</ParentIdentifier>
      <LocationMappings />
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Identifier>
      <DisplayName>Location Service</DisplayName>
      <Description>Location Service for GitHub Actions Server.</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <LocationMappings>
        <LocationMapping>
          <AccessMapping>PublicAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>HostGuidAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>AzureInstanceMapping</AccessMapping>
          <Location>https://pipelinesghubeus8aks.eastus.cloudapp.azure.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>ScaleUnitMapping</AccessMapping>
          <Location>https://pipelinesghubeus8.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
      </LocationMappings>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>8d299418-9467-402b-a171-9165e2f703e2</Identifier>
      <DisplayName>Location Service</DisplayName>
      <Description>Location Service for GitHub Actions Server.</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <LocationMappings />
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>a85b8835-c1a1-4aac-ae97-1c3d0ba72dbd</Identifier>
      <DisplayName>distributedtask</DisplayName>
      <Description>Resource Area</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>LocationService2</ParentServiceType>
      <ParentIdentifier>0000005a-0000-8888-8000-000000000000</ParentIdentifier>
      <LocationMappings>
        <LocationMapping>
          <AccessMapping>PublicAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>HostGuidAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>ScaleUnitMapping</AccessMapping>
          <Location>https://pipelinesghubeus8.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
      </LocationMappings>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>5d6898bb-45ec-463f-95f9-54d49c71752e</Identifier>
      <DisplayName>build</DisplayName>
      <Description>Resource Area</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>LocationService2</ParentServiceType>
      <ParentIdentifier>0000005a-0000-8888-8000-000000000000</ParentIdentifier>
      <LocationMappings>
        <LocationMapping>
          <AccessMapping>PublicAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>HostGuidAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>ScaleUnitMapping</AccessMapping>
          <Location>https://pipelinesghubeus8.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
      </LocationMappings>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>d5366ebe-2295-4205-984e-62916e51f1eb</Identifier>
      <DisplayName>runtime</DisplayName>
      <Description>Resource Area</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>LocationService2</ParentServiceType>
      <ParentIdentifier>0000005a-0000-8888-8000-000000000000</ParentIdentifier>
      <LocationMappings>
        <LocationMapping>
          <AccessMapping>PublicAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>HostGuidAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>ScaleUnitMapping</AccessMapping>
          <Location>https://pipelinesghubeus8.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
      </LocationMappings>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>4a933897-0488-45af-bd82-6fd3ad33f46a</Identifier>
      <DisplayName>PipelinesChecks</DisplayName>
      <Description>Resource Area</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>LocationService2</ParentServiceType>
      <ParentIdentifier>0000005a-0000-8888-8000-000000000000</ParentIdentifier>
      <LocationMappings>
        <LocationMapping>
          <AccessMapping>PublicAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>HostGuidAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>ScaleUnitMapping</AccessMapping>
          <Location>https://pipelinesghubeus8.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
      </LocationMappings>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>965220d5-5bb9-42cf-8d67-9b146df2a5a4</Identifier>
      <DisplayName>Build</DisplayName>
      <Description>Resource Area</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>LocationService2</ParentServiceType>
      <ParentIdentifier>0000005a-0000-8888-8000-000000000000</ParentIdentifier>
      <LocationMappings>
        <LocationMapping>
          <AccessMapping>PublicAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>HostGuidAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>ScaleUnitMapping</AccessMapping>
          <Location>https://pipelinesghubeus8.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
      </LocationMappings>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>2e0bf237-8973-4ec9-a581-9c3d679d1776</Identifier>
      <DisplayName>pipelines</DisplayName>
      <Description>Resource Area</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>LocationService2</ParentServiceType>
      <ParentIdentifier>0000005a-0000-8888-8000-000000000000</ParentIdentifier>
      <LocationMappings>
        <LocationMapping>
          <AccessMapping>PublicAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>HostGuidAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>ScaleUnitMapping</AccessMapping>
          <Location>https://pipelinesghubeus8.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
      </LocationMappings>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>d1d8c505-0f9b-4bef-89c7-b072449f6def</Identifier>
      <DisplayName>artifactcache</DisplayName>
      <Description>Resource Area</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>LocationService2</ParentServiceType>
      <ParentIdentifier>0000006d-0000-8888-8000-000000000000</ParentIdentifier>
      <LocationMappings>
        <LocationMapping>
          <AccessMapping>PublicAccessMapping</AccessMapping>
          <Location>https://artifactcache.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>HostGuidAccessMapping</AccessMapping>
          <Location>https://artifactcache.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>ScaleUnitMapping</AccessMapping>
          <Location>https://acghubeus2.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
      </LocationMappings>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>73f6b305-6840-4983-b200-d72ccece0013</Identifier>
      <DisplayName>runner</DisplayName>
      <Description>Resource Area</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>LocationService2</ParentServiceType>
      <ParentIdentifier>0000006f-0000-8888-8000-000000000000</ParentIdentifier>
      <LocationMappings>
        <LocationMapping>
          <AccessMapping>PublicAccessMapping</AccessMapping>
          <Location>https://runner.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>HostGuidAccessMapping</AccessMapping>
          <Location>https://runner.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
      </LocationMappings>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>a7b3b527-4f4f-4dac-8e84-f144fa6d554b</Identifier>
      <DisplayName>oauth2</DisplayName>
      <Description>Resource Area</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>LocationService2</ParentServiceType>
      <ParentIdentifier>0000005a-0000-8888-8000-000000000000</ParentIdentifier>
      <LocationMappings>
        <LocationMapping>
          <AccessMapping>PublicAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>HostGuidAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>ScaleUnitMapping</AccessMapping>
          <Location>https://pipelinesghubeus8.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
      </LocationMappings>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>1644b0a3-b109-43d6-a0e7-f20d9dfb7508</Identifier>
      <DisplayName>actions</DisplayName>
      <Description>Resource Area</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>LocationService2</ParentServiceType>
      <ParentIdentifier>0000005a-0000-8888-8000-000000000000</ParentIdentifier>
      <LocationMappings>
        <LocationMapping>
          <AccessMapping>PublicAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>HostGuidAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>ScaleUnitMapping</AccessMapping>
          <Location>https://pipelinesghubeus8.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
      </LocationMappings>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>1814ab31-2f4f-4a9f-8761-f4d77dc5a5d7</Identifier>
      <DisplayName>serviceendpoint</DisplayName>
      <Description>Resource Area</Description>
      <RelativePath relativeTo="FullyQualified">
      </RelativePath>
      <ParentServiceType>LocationService2</ParentServiceType>
      <ParentIdentifier>0000005a-0000-8888-8000-000000000000</ParentIdentifier>
      <LocationMappings>
        <LocationMapping>
          <AccessMapping>PublicAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>HostGuidAccessMapping</AccessMapping>
          <Location>https://pipelines.actions.githubusercontent.com/serviceHosts/c72d5a90-c7cc-4e99-86c4-2cfce9658ae4</Location>
        </LocationMapping>
        <LocationMapping>
          <AccessMapping>ScaleUnitMapping</AccessMapping>
          <Location>https://pipelinesghubeus8.actions.githubusercontent.com/HQoyNMJw9ye3ZkwFtYY9zZa44xpUZsNzOCx07jppgDsYjX8mTy/</Location>
        </LocationMapping>
      </LocationMappings>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>LocationService2</ServiceType>
      <Identifier>464ccb8d-abaf-4793-b927-cfdc107791ee</Identifier>
      <DisplayName>Location Service</DisplayName>
      <Description>Location Service for GitHub Actions Server.</Description>
      <RelativePath relativeTo="Context">/</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>actions</ServiceType>
      <Identifier>beb126ff-77cd-4476-83ff-877b31fab2b0</Identifier>
      <DisplayName>gates</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}/{gateId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>Container</ServiceType>
      <Identifier>e4f5c81e-e250-447b-9fef-bd48471bea5e</Identifier>
      <DisplayName>Containers</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/resources/{resource}/{containerId}/{*itemPath}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>4</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>Container</ServiceType>
      <Identifier>e71a64ac-b2b5-4230-a4c0-dad657cf97e2</Identifier>
      <DisplayName>Containers</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{resource}/{container}/{*itemPath}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>3</ResourceVersion>
      <MinVersion>2.1</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>9f904df8-b9f4-11ed-afa1-0242ac120002</Identifier>
      <DisplayName>enterpriseaccesspolicies</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>557624af-b29e-4c20-8ab0-0399d2204f3f</Identifier>
      <DisplayName>events</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{scopeIdentifier}/_apis/{area}/hubs/{hubName}/plans/{planId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>2.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>e298ef32-5878-4cab-993c-043836571f42</Identifier>
      <DisplayName>agents</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/pools/{poolId}/{resource}/{agentId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>2</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>bd247656-4d13-49af-80c1-1891bb057a93</Identifier>
      <DisplayName>agentCloudRequestMessages</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/agentclouds/{agentCloudId}/requests/{agentCloudRequestId}/messages</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>5.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>3ecd9bbb-1cc8-4817-9e57-20e4a3dbf6a2</Identifier>
      <DisplayName>jitconfig</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/agents/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>134e239e-2df3-4794-a6f6-24f1f19ec8dc</Identifier>
      <DisplayName>sessions</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/pools/{poolId}/{resource}/{sessionId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>83597576-cc2c-453c-bea6-2882ae6a1653</Identifier>
      <DisplayName>timelines</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{scopeIdentifier}/_apis/{area}/hubs/{hubName}/plans/{planId}/{resource}/{timelineId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>2.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>be5e691c-1592-40d4-a039-2fee0e7cc6b8</Identifier>
      <DisplayName>feedstream</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{scopeIdentifier}/_apis/{area}/hubs/{hubName}/plans/{planId}/timelines/{timelineId}/records/{recordId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>af19090b-d86c-4bcf-80fe-30444d639087</Identifier>
      <DisplayName>packagedownload</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}/{packageType}/{platform}/{version}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>eb55e5d6-2f30-4295-b5ed-38da50b1fc52</Identifier>
      <DisplayName>attachments</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{scopeIdentifier}/_apis/{area}/hubs/{hubName}/plans/{planId}/{resource}/{type}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>2.1</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>c3a054f6-7a8a-49c0-944e-3a8e5d7adfd7</Identifier>
      <DisplayName>messages</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/pools/{poolId}/{resource}/{messageId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>5cecd946-d704-471e-a45f-3b4064fcfaba</Identifier>
      <DisplayName>plans</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{scopeIdentifier}/_apis/{area}/hubs/{hubName}/{resource}/{planId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>2</ResourceVersion>
      <MinVersion>2.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>a60d0d28-8e2f-4ce2-bbaf-471bf3bf0bfc</Identifier>
      <DisplayName>accesspolicy</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/pools/{poolId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>ffe38397-3a9d-4ca6-b06d-49303f287ba5</Identifier>
      <DisplayName>timelines</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/plans/{planId}/{resource}/{timelineId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>1.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>8cc1b02b-ae49-4516-b5ad-4f9b29967c30</Identifier>
      <DisplayName>updates</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/pools/{poolId}/agents/{agentId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>3.2</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>858983e4-19bd-4c5e-864c-507b59b58b12</Identifier>
      <DisplayName>feed</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{scopeIdentifier}/_apis/{area}/hubs/{hubName}/plans/{planId}/timelines/{timelineId}/records/{recordId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>2.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>38f00041-0953-4d24-86c3-5432d23e2205</Identifier>
      <DisplayName>brokerlistener</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>8ffcd551-079c-493a-9c02-54346299d144</Identifier>
      <DisplayName>packages</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}/{packageType}/{platform}/{version}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>2</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>68ba3f2c-5f79-4d8b-a48a-57c7b46ed3e0</Identifier>
      <DisplayName>labels</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}/{labelId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>27d7f831-88c1-4719-8ca1-6a061dad90eb</Identifier>
      <DisplayName>actiondownloadinfo</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{scopeIdentifier}/_apis/{area}/hubs/{hubName}/plans/{planId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>7898f959-9cdf-4096-b29e-7f293031629e</Identifier>
      <DisplayName>attachments</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{scopeIdentifier}/_apis/{area}/hubs/{hubName}/plans/{planId}/timelines/{timelineId}/records/{recordId}/{resource}/{type}/{name}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>2.1</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>4eedf63a-38ba-42ea-8525-81f7eb96c0e3</Identifier>
      <DisplayName>requesttypelimits</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}/{poolId}/{requestType}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>15344176-9e77-4cf4-a7c3-8bc4d0a3c4eb</Identifier>
      <DisplayName>logs</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/plans/{planId}/{resource}/{logId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>1.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>fc825784-c92a-4299-9221-998a02d1b54f</Identifier>
      <DisplayName>jobrequests</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/pools/{poolId}/{resource}/{requestId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>8893bc5b-35b2-4be7-83cb-99e683551db4</Identifier>
      <DisplayName>records</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{scopeIdentifier}/_apis/{area}/hubs/{hubName}/plans/{planId}/timelines/{timelineId}/{resource}/{recordId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>2.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>bfa72b3d-0fc6-43fb-932b-a7f6559f93b9</Identifier>
      <DisplayName>agentclouds</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}/{agentCloudId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>5.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>dfed02fb-deee-4039-a04d-aa21d0241995</Identifier>
      <DisplayName>events</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/plans/{planId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>1.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>9ae056f6-d4e4-4d0c-bd26-aee2a22f01f2</Identifier>
      <DisplayName>feed</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/plans/{planId}/timelines/{timelineId}/records/{recordId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>1.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>9236daac-313e-4760-8245-b0a8bfca212a</Identifier>
      <DisplayName>admintoken</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/pools/{pool}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>5.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>4ebade4d-ba5d-43bf-a047-b58cee747c84</Identifier>
      <DisplayName>agentCloudRequest</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/pools/{poolId}/requests/{agentRequestid}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>25adab70-1379-4186-be8e-b643061ebe3a</Identifier>
      <DisplayName>runnermessages</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}/{messageId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>f8d10759-6e90-48bc-96b0-d19440116797</Identifier>
      <DisplayName>plans</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}/{planId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>1.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>a8c47e17-4d56-4a56-92bb-de7ea7dc65be</Identifier>
      <DisplayName>pools</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}/{poolId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>46f5667d-263a-4684-91b1-dff7fdcf64e2</Identifier>
      <DisplayName>logs</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{scopeIdentifier}/_apis/{area}/hubs/{hubName}/plans/{planId}/{resource}/{logId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>2.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>50170d5d-f122-492f-9816-e2ef9f8d1756</Identifier>
      <DisplayName>records</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/plans/{planId}/timelines/{timelineId}/{resource}/{recordId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>1.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>69a319f4-28c1-4bfd-93e6-ea0ff5c6f1a2</Identifier>
      <DisplayName>idtoken</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{scopeIdentifier}/_apis/{area}/hubs/{hubName}/plans/{planId}/jobs/{jobId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>2.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>4a35de01-9369-4f33-af9a-eb94ea60f6d2</Identifier>
      <DisplayName>queuedrequests</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>distributedtask</ServiceType>
      <Identifier>20189bd7-5134-49c2-b8e9-f9e856eea2b2</Identifier>
      <DisplayName>requests</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/agentclouds/{agentCloudId}/{resource}/{agentCloudRequestId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>5.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>Fallback</ServiceType>
      <Identifier>232b00f3-c6b8-48c6-883f-1a8dc6cbef8a</Identifier>
      <DisplayName>NotFound</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{*params}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>FeatureAvailability</ServiceType>
      <Identifier>3e2b80f8-9e6f-441e-8393-005610692d9c</Identifier>
      <DisplayName>FeatureFlags</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{resource}/{name}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>Location</ServiceType>
      <Identifier>00d9565f-ed9c-4a06-9a50-00e7896ccab4</Identifier>
      <DisplayName>ConnectionData</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>Location</ServiceType>
      <Identifier>e81700f7-3be2-46de-8624-2eb35882fcaa</Identifier>
      <DisplayName>ResourceAreas</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{resource}/{areaId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>3.2</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>Location</ServiceType>
      <Identifier>d810a47d-f4f4-4a62-a03f-fa1860585c4c</Identifier>
      <DisplayName>ServiceDefinitions</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{resource}/{serviceType}/{identifier}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>oauth2</ServiceType>
      <Identifier>10d13a60-2758-406c-8ab7-cffccb21fcf4</Identifier>
      <DisplayName>token</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>0.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>operations</ServiceType>
      <Identifier>7f82df6d-7d09-46c1-a015-643b556b3a1e</Identifier>
      <DisplayName>operations</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{resource}/{pluginId}/{operationId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>4.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>operations</ServiceType>
      <Identifier>9a1b74b4-2ca8-4a9f-8470-c2f2e6fdc949</Identifier>
      <DisplayName>operations</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{resource}/{operationId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>2.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>5.1</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>c6f7a235-42d9-4921-b721-0e29f91e15a5</Identifier>
      <DisplayName>planLogs</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/plans/{orchestrationIdentifier}/logs</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>85023071-bd5e-4438-89b0-2a5bf362a19d</Identifier>
      <DisplayName>artifacts</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{project}/_apis/{area}/{pipelineId}/runs/{runId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>5.2</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>6b2ac16f-cd00-4df9-a13b-3a1cc8afb188</Identifier>
      <DisplayName>signedartifactscontent</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{project}/_apis/{area}/{pipelineId}/runs/{runId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>5.2</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>c41b3775-6d50-48bd-b261-42da7f0f1ba0</Identifier>
      <DisplayName>live</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{project}/_apis/{area}/{pipelineId}/runs/{runId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>2</ResourceVersion>
      <MinVersion>5.2</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>6bada0b9-cb9c-435d-9584-4e0e730001bb</Identifier>
      <DisplayName>githubbillingtags</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/orgs/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>ef349595-f66b-4ccb-a216-50c77479cd17</Identifier>
      <DisplayName>signedsummary</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/plans/{planId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>3fccc81a-f469-4633-bd4a-581c11a24de1</Identifier>
      <DisplayName>workflowArtifacts</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/workflows/{workflowRunId}/artifacts</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>99ea91b7-bbe9-4bd3-a924-874f13205b21</Identifier>
      <DisplayName>steps</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/plans/{planId}/jobs/{jobId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>01d75881-6892-4ec6-8dca-91ecfb0dc048</Identifier>
      <DisplayName>summary</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/plans/{planId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>cfe7c963-19d0-4451-9ae8-96009ee26441</Identifier>
      <DisplayName>planArtifacts</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/plans/{orchestrationIdentifier}/artifacts</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>cd70ba1a-d59a-4e0b-9934-97998159ccc8</Identifier>
      <DisplayName>orgs</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>5.1</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>1ffe4916-ac72-4566-add0-9bab31e44fcf</Identifier>
      <DisplayName>signalr</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{project}/_apis/{area}/{pipelineId}/runs/{runId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>5.2</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>366b03b8-6a21-4631-bc9e-9c8e7b5b361a</Identifier>
      <DisplayName>runinfo</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{project}/_apis/{area}/{resource}/{orchestrationIdentifier}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>fb1b6d27-3957-43d5-a14b-a2d70403e545</Identifier>
      <DisplayName>logs</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{project}/_apis/{area}/{pipelineId}/runs/{runId}/{resource}/{logId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>5.1</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>7859261e-d2e9-4a68-b820-a5d84cc5bb3d</Identifier>
      <DisplayName>runs</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{project}/_apis/{area}/{pipelineId}/{resource}/{runId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>2</ResourceVersion>
      <MinVersion>5.2</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>a75c63c4-1ade-40eb-a0ce-a9378d13ed5a</Identifier>
      <DisplayName>adminevents</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{project}/_apis/{area}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>28e1305e-2afe-47bf-abaf-cbb0e6a91988</Identifier>
      <DisplayName>pipelines</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{project}/_apis/{area}/{pipelineId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>5.1</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>4818972d-29fa-4b86-92c1-de5ae7ef33f5</Identifier>
      <DisplayName>jobs</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/plans/{planId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>pipelines</ServiceType>
      <Identifier>74f99e32-e2c4-44f4-93dc-dec0bca530a5</Identifier>
      <DisplayName>signedlogcontent</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">{project}/_apis/{area}/{pipelineId}/runs/{runId}/{resource}/{logId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>5.1</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>runtime</ServiceType>
      <Identifier>35931bc4-ad7b-443a-a004-05e196e6aca3</Identifier>
      <DisplayName>generatejitconfig</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/runnerscalesets/{runnerScaleSetId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>runtime</ServiceType>
      <Identifier>eecccaab-67d0-4c74-ab62-0c420001c513</Identifier>
      <DisplayName>acquirablejobs</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/runnerscalesets/{runnerScaleSetId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>runtime</ServiceType>
      <Identifier>d5d2a677-b1ad-4e16-bba0-2b0275ddc338</Identifier>
      <DisplayName>runnerscalesets</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}/{runnerScaleSetId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>runtime</ServiceType>
      <Identifier>29fbb88c-d23d-4921-b5d4-473639ca6ccf</Identifier>
      <DisplayName>visibility</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/runnergroups/{groupId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>runtime</ServiceType>
      <Identifier>70bd3705-14b4-4c74-8480-67316bd79fe9</Identifier>
      <DisplayName>runnergroups</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/{resource}/{groupId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>runtime</ServiceType>
      <Identifier>9267acd8-c7c6-45d8-b50e-883fe06a2b9a</Identifier>
      <DisplayName>acquirejobs</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/runnerscalesets/{runnerScaleSetId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>runtime</ServiceType>
      <Identifier>057fef20-bfa7-4621-a06d-cf39eede64a7</Identifier>
      <DisplayName>acquire</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/runnerscalesets/{runnerScaleSetId}/jobs/{requestId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>runtime</ServiceType>
      <Identifier>a0facaaa-c2f6-4d34-ae1f-d0cd687d7576</Identifier>
      <DisplayName>acquire</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/runnerscalesets/{runnerScaleSetId}/jobs/{requestId}/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>runtime</ServiceType>
      <Identifier>15bc8dc1-f86c-44e6-b220-d2a1d9d14b2f</Identifier>
      <DisplayName>sessions</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/runnerscalesets/{runnerScaleSetId}/{resource}/{sessionId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>runtime</ServiceType>
      <Identifier>c9b03fd5-6283-460a-90f5-e1adeaeae5ad</Identifier>
      <DisplayName>messages</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{area}/runnerscalesets/{runnerScaleSetId}/{resource}/{messageId}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>6.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
    <ServiceDefinition>
      <ServiceType>Servicing</ServiceType>
      <Identifier>3c4bfe05-aeb6-45f8-93a6-929468401657</Identifier>
      <DisplayName>ServiceLevel</DisplayName>
      <Description>Resource Location</Description>
      <RelativePath relativeTo="Context">_apis/{resource}</RelativePath>
      <ParentServiceType>
      </ParentServiceType>
      <ParentIdentifier>00000000-0000-0000-0000-000000000000</ParentIdentifier>
      <ResourceVersion>1</ResourceVersion>
      <MinVersion>1.0</MinVersion>
      <MaxVersion>6.0</MaxVersion>
      <ReleasedVersion>0.0</ReleasedVersion>
    </ServiceDefinition>
  </Services>
  <CachedMisses />
</LocationServiceConfiguration>